<form name="BuildingColumnVisibilityCtrl"
      data-ng-controller='BuildingColumnVisibilityCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Show/Hide Columns"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <md-dialog-content layout layout-wrap layout-padding>
            <span flex="100">Choose which columns you want to display.</span>
        </md-dialog-content>

        <fieldset id="edit-inputs">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Column</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>
                     <tr ng-repeat="(key, value) in vm.showableColumns">
                        <td>
                            {{vm.showableColumns[key].name}}
                        </td>
                        <td>
                            <!-- .hidden is the opposite of checked  -->
                            <md-checkbox ng-model="vm.showableColumns[key].hidden" ng-change="vm.toggleColumnVisibility(key, value)"
                                ng-true-value="false" ng-false-value="true"></md-checkbox>
                        </td>
                     </tr>
                </tbody>
            </table>

        </fieldset>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

            <div class="clearfix"></div>
        </div>

    </div>

</form>

<style>
    /* Date Picker adjustments */

    .modal-datepicker .md-datepicker-input {
        font-size: 12px !important;
    }

    /* Placeholder color */

    .modal-datepicker .md-datepicker-input::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: rgba(0, 0, 0, 0.87);
        opacity: 1; /* Firefox */
    }

    .modal-datepicker .md-datepicker-input:-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: rgba(0, 0, 0, 0.87);
    }

    .modal-datepicker .md-datepicker-input::-ms-input-placeholder { /* Microsoft Edge */
        color: rgba(0, 0, 0, 0.87);
    }

    /* Triangle icon color */

    .modal-datepicker .md-datepicker-triangle-button .md-datepicker-expand-triangle,
    .modal-datepicker .md-datepicker-triangle-button:hover .md-datepicker-expand-triangle {
        border-top-color: rgba(0, 0, 0, 0.87);
    }

    /* Date Picker full width */

    md-datepicker.hiddenCalendar .md-datepicker-input-container {
        width: 100%;
    }
    .md-datepicker-input {
        max-width: 100%;
    }
</style>