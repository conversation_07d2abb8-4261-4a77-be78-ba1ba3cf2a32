(function () {
    'use strict';

    var app = angular.module('app');

    app.directive('userName', ['security', function (security) {
        return {
            restrict: 'A',
            link: function ($scope, element, attrs) {
                var userName = security.currentUser.displayName;
                $scope.user = security.currentUser;
                $scope.$watch('user', function (user) {
                    userName = user.displayName;
                    updateHtml();
                }, true);

                function updateHtml() {
                    element.html(userName);
                }
            }
        }
    }]);

    app.directive('ccSidebar', ['$window', function ($window) {
        // Repositions the sidebar on window resize 
        // and opens and closes the sidebar menu.
        // Usage:
        //  <div data-cc-sidebar>
        // Creates:
        //  <div data-cc-sidebar class="sidebar">
        var directive = {
            link: link,
            restrict: 'A'
        };
        var $win = $($window);
        return directive;

        function link(scope, element, attrs) {
            var $sidebarInner = element.find('.sidebar-inner');
            var $dropdownElement = element.find('.sidebar-dropdown a');
            element.addClass('sidebar');
            $win.resize(resize);
            $dropdownElement.click(dropdown);

            function resize() {
                $win.width() >= 765 ? $sidebarInner.slideDown(350) : $sidebarInner.slideUp(350);
            }

            function dropdown(e) {
                var dropClass = 'dropy';
                e.preventDefault();
                if (!$dropdownElement.hasClass(dropClass)) {
                    hideAllSidebars();
                    $sidebarInner.slideDown(350);
                    $dropdownElement.addClass(dropClass);
                } else if ($dropdownElement.hasClass(dropClass)) {
                    $dropdownElement.removeClass(dropClass);
                    $sidebarInner.slideUp(350);
                }

                function hideAllSidebars() {
                    $sidebarInner.slideUp(350);
                    $('.sidebar-dropdown a').removeClass(dropClass);
                }
            }
        }
    }]);

    app.directive('ccWidgetClose', function () {
        // Usage:
        // <a data-cc-widget-close></a>
        // Creates:
        // <a data-cc-widget-close="" href="#" class="wclose">
        //     <i class="icon-remove"></i>
        // </a>
        var directive = {
            link: link,
            template: '<i class="icon-remove"></i>',
            restrict: 'A'
        };
        return directive;

        function link(scope, element, attrs) {
            attrs.$set('href', '#');
            attrs.$set('wclose');
            element.click(close);

            function close(e) {
                e.preventDefault();
                element.parent().parent().parent().hide(100);
            }
        }
    });

    app.directive('rdBackButton', ['$rootScope', '$state', function ($rootScope, $state) {
        // Usage:
        // <rd-back-button default-nav="batches"></rd-back-button>  
        var directive = {
            link: link,
            template: '<button class="btn btn-default btn-xs navbar-back" type="button">Back</button>',
            restrict: 'E'
        };
        return directive;

        function link(scope, element, attrs) {
            element.click(back);
            scope.previousRoute = $rootScope.previousState;
            function back(e) {

                if (scope.previousRoute != null) {
                    $state.go(scope.previousRoute);
                }
                else {
                    $state.go(attrs.defaultNav);
                }
            }
        }
    }]);

    app.directive('ccWidgetMinimize', function () {
        // Usage:
        // <a data-cc-widget-minimize></a>
        // Creates:
        // <a data-cc-widget-minimize="" href="#"><i class="icon-chevron-up"></i></a>
        var directive = {
            link: link,
            template: '<i class="icon-chevron-up"></i>',
            restrict: 'A'
        };
        return directive;

        function link(scope, element, attrs) {
            //$('body').on('click', '.widget .wminimize', minimize);
            attrs.$set('href', '#');
            attrs.$set('wminimize');
            element.click(minimize);

            function minimize(e) {
                e.preventDefault();
                var $wcontent = element.parent().parent().next('.widget-content');
                var iElement = element.children('i');
                if ($wcontent.is(':visible')) {
                    iElement.removeClass('icon-chevron-up');
                    iElement.addClass('icon-chevron-down');
                } else {
                    iElement.removeClass('icon-chevron-down');
                    iElement.addClass('icon-chevron-up');
                }
                $wcontent.toggle(500);
            }
        }
    });

    app.directive('ccScrollToTop', ['$window',
        // Usage:
        // <span data-cc-scroll-to-top></span>
        // Creates:
        // <span data-cc-scroll-to-top="" class="totop">
        //      <a href="#"><i class="icon-chevron-up"></i></a>
        // </span>
        function ($window) {
            var directive = {
                link: link,
                template: '<a href="#"><i class="icon-chevron-up"></i></a>',
                restrict: 'A'
            };
            return directive;

            function link(scope, element, attrs) {
                var $win = $($window);
                element.addClass('totop');
                $win.scroll(toggleIcon);

                element.find('a').click(function (e) {
                    e.preventDefault();
                    // Learning Point: $anchorScroll works, but no animation
                    //$anchorScroll();
                    $('body').animate({ scrollTop: 0 }, 500);
                });

                function toggleIcon() {
                    $win.scrollTop() > 300 ? element.slideDown() : element.slideUp();
                }
            }
        }
    ]);

    app.directive('ccSpinner', ['$window', function ($window) {
        // Description:
        //  Creates a new Spinner and sets its options
        // Usage:
        //  <div data-cc-spinner="vm.spinnerOptions"></div>
        var directive = {
            link: link,
            restrict: 'A'
        };
        return directive;

        function link(scope, element, attrs) {
            var showMidPage = false;
            if (scope.isReport && scope.isReport == true) {
                showMidPage = true;
            }
            scope.spinner = null;
            scope.$watch(attrs.ccSpinner, function (options) {
                if (scope.spinner) {
                    scope.spinner.stop();
                }
                options = { radius: 5, width: 3, length: 6 };
                if (showMidPage == true) {
                    options = { radius: 28, width: 15, length: 30 };
                }

                scope.spinner = new $window.Spinner(options);
                scope.spinner.spin(element[0]);
            }, true);
        }
    }]);

    app.directive('ccWidgetHeader', ['$rootScope', '$state', function ($rootScope, $state) {
        //Usage:
        //<div data-cc-widget-header title="vm.map.title"></div>
        var directive = {
            link: link,
            scope: {
                'title': '@',
                'subtitle': '@',
                'rightText': '@',
                'allowCollapse': '@',
                'isModal': '=',
                'cancel': '&',
                'backButton': '@'
            },
            templateUrl: 'app/ui/layout/widgetheader.html',
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-header');
            }
            else {
                attrs.$set('class', 'widget-head');
            }

            scope.previousRoute = $rootScope.previousState;
            scope.goback = function (e) {
                e.preventDefault();
                if (scope.previousRoute != null) {
                    $state.go(scope.previousRoute);
                }
                else {
                    $state.go(attrs.defaultNav);
                }
            }
        }
    }]);

    app.directive('ccWidgetContent', function () {
        //Usage:
        //<div data-cc-widget-content data-is-modal="vm.isModal">content here</div>
        var directive = {
            link: link,
            scope: {
                'isModal': '=',
                'noActionBar': '='
            },
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-body');
            }
            else {
                attrs.$set('class', 'widget-content');
                if (scope.noActionBar == true) {
                    attrs.$set('class', 'widget-content no-action-bar');
                }
            }
        }
    });

    app.directive('ccWidgetButtonBar', function () {
        //Usage:
        //<div data-cc-widget-button-bar data-is-modal="vm.isModal">buttons here</div>
        var directive = {
            link: link,
            scope: {
                'isModal': '='
            },
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-footer');
            }
            else {
                attrs.$set('class', 'widget-button-bar');
            }
        }
    });

    // Directive to display Created By and Modified By line.
    // Requires consistent naming of created and modified values.
    app.directive('rdDisplayCreatedModified', ['$window', function ($window) {
        //Usage:
        //<div data-rd-display-created-modified ng-model="vm.MyModel"></div>
        var directive = {
            link: link,
            restrict: 'AE',
            require: 'ngModel',
            scope: {
                ngModel: '='
            }
        };
        return directive;

        function link(scope, element, attrs, ngModel) {
            scope.$watch('ngModel', function (newModel) {
                if (newModel != undefined) {
                    var wkText = "";
                    var moment = $window.moment;
                    var dateFormat = "DD/MM/YYYY h:mm a";
                    var crDt = newModel.createdDate;
                    var mdDt = newModel.updatedDate;

                    if (newModel.createdBy != undefined) {
                        wkText = "Created by " + newModel.createdBy + " on " + moment(crDt).format(dateFormat) + ". ";
                    }
                    if (newModel.updatedBy != undefined) {
                        wkText = wkText + "Last modified by " + newModel.updatedBy + " on " + moment(mdDt).format(dateFormat);
                    }
                    element.html(wkText);
                    attrs.$set('class', 'text-info text-center');
                }
            });
        }
    }]);

    // Widget Action Bar directive.
    // Supports creation of an action bar for both lists and forms.
    // Includes Quick Find, New Record Buttons, Other Action Buttons, and Refresh (with spinner).
    // Attributes:
    //    quick-find-model: defines the filter field used by a list
    //    refresh-list: function to call to refresh data
    //    spinner-busy: true/false variable that indicates when to display the spinned (normally vm.isBusy)
    //    new-record: function to call to create a new record (via modal or new page)
    //    new-record-text: text to display on the new record button
    //    action-buttons: a list of objects that defines the action buttons to display
    //                    { name: 'Button Name', onclick: functiontoacall, 
    //                      desc: 'More detailed description of the buttons purpose', 
    //                      roles: 'comma separated string of roles. if not specified everyone has access',
    //                      condition: 'a function that evaluates to true or false.  If always true have function just return true.'}
    //    filterOptions: an array of filter options to be displayed.  Format [{code: 'Current', name: 'Current Items'},{...}]
    //    filterChanged: function to call when the filter has changed
    app.directive('ccWidgetActionBar', function ($compile) {
        //Usage:
        //<div cc-widget-action-bar quick-find-model='vm.listfilter' action-buttons='vm.actionList' refresh-list='vm.refreshList()' spinner-busy='vm.isBusy' new-record='vm.newRecord()' new-record-text='New Batch' ></div>
        var directive = {
            link: link,
            restrict: 'AE',
            scope: {
                'selectedRecordList': '&',
                'quickFindModel': '=',
                'copyFromUserId': '=',
                'actionButtons': '=',
                'persistName': '@',
                'refreshList': '&',
                'spinnerBusy': '=',
                'newRecord': '&',
                'newRecordText': '@',
                'otherRecord': '&',
                'otherRecordText': '@',
                'filterChanged': '&',
                'filterOptions': '=',
                'currentFilter': '=',
                'showAddButton': '=',
                'disabledAddButton': '=',
                'showOtherButton': '=',
                'hide': '=',
                'exportList': '&',
                'exportFilename': '@',
                'isReport': '=',
                'importList': '&',
                'selectedCount': '=',
                'recordTitle': '@',
            },
        };
        return directive;

        function link(scope, element, attrs) {

            var wkHtml = "";
            scope.colNames = "";
            if (attrs.hide != undefined && scope.hide == true) {
                return;
            }
            if (attrs.quickFindModel != undefined && attrs.quickFindModel != "") {
                wkHtml = wkHtml + '<input type="text" ng-model="quickFindModel" class="quick-search" data-ng-class="{bgyellow: quickFindModel != \'\' && quickFindModel != null }" placeholder="quick find" data-rd-persist="' + attrs.persistName + '" />';
            }
            if (attrs.copyFromUserId != undefined && attrs.copyFromUserId != "") {
                wkHtml = wkHtml + '<input type="text" ng-model="copyFromUserId" class="user-text" placeholder="Copy Roles From User Id"/>';
            }
            if (attrs.selectedRecordList != undefined && attrs.selectedRecordList != "") {
                wkHtml = wkHtml + '<button class="btn btn-sm btn-primary" type="button" ng-click="selectedRecordList()">Select</button>';
            }
            if (attrs.newRecord != undefined && attrs.newRecord != "") {
                wkHtml = wkHtml + '<button class="btn btn-sm btn-orange" type="button" ng-click="newRecord()" data-ng-show="showAddButton" data-ng-disabled="disabledAddButton" title="create a {{newRecordText}}">{{newRecordText}}</button>';
            }
            if (attrs.otherRecord != undefined && attrs.otherRecord != "") {
                wkHtml = wkHtml + '<button class="btn btn-sm btn-orange" type="button" ng-click="otherRecord()" data-ng-show="showOtherButton" title="create a {{otherRecordText}}">{{otherRecordText}}</button>';
            }
            if (scope.actionButtons != undefined) {
                //TODO add code to deal with the roles string 
                if (attrs.isActionWithConfirm) {
                    var spinClass = "spinner-right";
                    if (scope.isReport == true) {
                        spinClass = "spinner-mid-page";
                    }
                    wkHtml = wkHtml + '<button ng-repeat="rec in actionButtons" ng-disabled="(selectedCount>0?false:true)" class="btn btn-sm btn-default" type="button" ng-confirm-click="rec.onclick()" ng-confirm-condition="true" ng-confirm-message="{{rec.confirmationMessage}} {{selectedCount}} {{recordTitle}}." ng-show="rec.condition() || rec.condition() == undefined" >{{rec.name}}</button>';
                }
                else {
                    wkHtml = wkHtml + '<button ng-repeat="rec in actionButtons" class="btn btn-sm btn-default" type="button" ng-click="rec.onclick()" ng-show="rec.condition() || rec.condition() == undefined" >{{rec.name}}</button>';
                }
            }
            if (attrs.exportList != undefined && attrs.exportList != "") {
                var wkFilename = 'export';
                if (attrs.exportFilename != undefined && attrs.exportFilename != "") {
                    wkFilename = attrs.exportFilename;
                }
                wkHtml = wkHtml + '<button class="btn btn-sm btn-default" type="button" ng-csv="exportList" csv-header="colNames" filename="' + wkFilename + '.csv" title="export list to a csv file">Export</button>';
            }
            if (attrs.refreshList != undefined && attrs.refreshList) {
                var spinClass = "spinner-right";
                if (scope.isReport == true) {
                    spinClass = "spinner-mid-page";
                }
                wkHtml = wkHtml + '<div class="pull-right"><div class="' + spinClass + '" data-ng-show="spinnerBusy" data-cc-spinner=""></div>' +
                            '<button class="btn btn-xs fa fa-refresh refresh" data-ng-show="!spinnerBusy" type="button" ng-click="refreshList({value: currentFilter})" title="Refresh"></button></div>';
            }
            if (attrs.filterOptions != undefined && scope.filterOptions != null && scope.filterOptions.length > 0) {
                if (scope.currentFilter == undefined || scope.currentFilter == null) {
                    scope.currentFilter = scope.filterOptions[0].code;
                }
                wkHtml = wkHtml + "<select class='pull-right datasetdrop col-lg-1' ng-model='currentFilter' ng-change='filterChanged({value: currentFilter})' ng-options='rec.code as rec.name for rec in filterOptions'></select>"
            }
            if (attrs.importList != undefined && attrs.importList != "") {
                wkHtml = wkHtml + '<button class="btn btn-sm btn-default" type="button" ng-click="importList()" title="import list from file.">Import</button>';
            }
            element.html('<div class="widget-actionbar">' + wkHtml + '</div>').show();
            $compile(element.contents())(scope);

            if (attrs.exportList != undefined) {
                scope.$watch(scope.exportList, function () {
                    if (scope.exportList() != undefined && scope.exportList().length > 0 && typeof scope.exportList()[0] === 'object') {
                        var recs = scope.exportList()[0];
                        scope.colNames = Object.keys(recs);
                    }
                });
            }
        }
    });

    app.directive('rdExportData', function ($compile) {
        //Usage:

        var directive = {
            link: link,
            restrict: 'AE',
            scope: {
                'exportList': '&',
                'exportFilename': '@'
            },
        };
        return directive;

        function link(scope, element, attrs) {

            var wkHtml = "";
            scope.colNames = "";

            if (attrs.exportList != undefined && attrs.exportList != "") {
                var wkFilename = 'export';
                if (attrs.exportFilename != undefined && attrs.exportFilename != "") {
                    wkFilename = attrs.exportFilename;
                }
                wkHtml = wkHtml + '<button class="btn btn-sm btn-default" type="button" ng-csv="exportList" csv-header="colNames" filename="' + wkFilename + '.csv" title="export list to a csv file">Export</button>';
            }

            element.html(wkHtml).show();
            $compile(element.contents())(scope);

            if (attrs.exportList != undefined) {
                scope.$watch(scope.exportList, function () {
                    if (scope.exportList() != undefined && scope.exportList().length > 0 && typeof scope.exportList()[0] === 'object') {
                        var recs = scope.exportList()[0];
                        scope.colNames = Object.keys(recs);
                    }
                });
            }
        }
    });

    /**
     * A generic confirmation for risky actions.
     * Usage: Add attributes:
     * * ng-confirm-message="Are you sure?"  - not used if delete-item is present
     * * ng-confirm-delete-item="description of item"
     * * ng-confirm-delete-reason-required   - will force the user to specify why they are deleting the record.
     * * ng-confirm-click="takeAction(yourlocalitemtodelete, deleteReasonId, deleteNote)" function
     * * ng-confirm-condition="mustBeEvaluatedToTrueForTheConfirmBoxBeShown" expression
    */
    app.directive('ngConfirmClick', ['bootstrap.dialog', function (modalDialog) {
        return {
            restrict: 'A',
            scope: {
                'action': '&ngConfirmClick',
                'delItem': '=ngConfirmDeleteItem'
            },
            link: function (scope, element, attrs) {
                element.bind('click', function (ev) {
                    if (ev != null) {
                        ev.preventDefault();
                        ev.stopPropagation();
                    }
                    var condition = scope.$eval(attrs.ngConfirmCondition);
                    if (condition) {
                        var message = attrs.ngConfirmMessage;
                        var result;
                        if (attrs.ngConfirmDeleteItem) {
                            if (attrs.ngConfirmDeleteReasonRequired != undefined) {
                                result = modalDialog.deleteDialogWithReason(scope.delItem);
                            }
                            else {
                                result = modalDialog.deleteDialog(scope.delItem);
                            }
                        }
                        else {
                            result = modalDialog.confirmationDialog("Confirm", message);
                        }
                        result.then(function (data) {
                            scope.action({ deleteReasonId: data.reasonId, deleteNote: data.note });
                        });
                    }
                    else {
                        scope.action();
                    }
                });
            }
        }
    }]);


    //Directive for numeric value
    app.directive('numberOnly', function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            scope: {
                ngModel: '='
            },
            link: function (scope) {
                scope.$watch('ngModel', function (newValue, oldValue) {
                    if (oldValue != undefined && oldValue.length > 0) {
                        if (newValue != undefined) {
                            if (typeof newValue == 'string') {
                                var notNumberCheck = newValue.replace(oldValue, '');
                                if (isNaN(newValue)) {
                                    if (notNumberCheck != '.') {
                                        scope.ngModel = oldValue;
                                        return;
                                    }
                                }
                            }
                        } else {
                            scope.ngModel = "";
                            return;
                        }
                    } else {
                        if (isNaN(newValue) && newValue != '.') {
                            scope.ngModel = "";
                            return;
                        }
                    }
                    var arr = String(newValue).split("");
                    if (arr.length === 0) return;
                    if (arr.length === 1 && (arr[0] == '-' || arr[0] === '.')) return;
                    if (arr.length === 2 && newValue === '-.') return;
                    if (isNaN(newValue)) {
                        scope.ngModel = oldValue;
                    }
                });
            }
        };
    })

    //Directive for enter only number in a textbox
    app.directive('onlyNumeric', function () {
        return {
            require: 'ngModel',
            scope: {
                showCommas: '='
            },
            link: function (scope, element, attrs, modelCtrl) {
                modelCtrl.$parsers.push(function (inputValue) {
                    // this next if is necessary for when using ng-required on your input. 
                    // In such cases, when a letter is typed first, this parser will be called
                    // again, and the 2nd time, the value will be undefined
                    if (inputValue == undefined) return ''
                    var transformedInput = inputValue.replace(/[^0-9]/g, '');
                    if (transformedInput != inputValue) {
                        modelCtrl.$setViewValue(transformedInput);
                        modelCtrl.$render();
                    }

                    return transformedInput;
                });
            }
        };
    });

    //Directive to not allow numerics or special characters to be entered.
    app.directive('noNumerics', function () {
        return {
            require: 'ngModel',
            link: function (scope, element, attrs, modelCtrl) {
                modelCtrl.$parsers.push(function (inputValue) {
                    // this next if is necessary for when using ng-required on your input. 
                    // In such cases, when a letter is typed first, this parser will be called
                    // again, and the 2nd time, the value will be undefined
                    if (inputValue == undefined) return ''
                    var transformedInput = inputValue.replace(/[0-9@#$%^&|\\?*/()-+=~{}![\]<>]/g, '');
                    if (transformedInput != inputValue) {
                        modelCtrl.$setViewValue(transformedInput);
                        modelCtrl.$render();
                    }

                    return transformedInput;
                });
            }
        };
    });

    ////Date validation directive
    app.directive('customDatepicker', function ($compile) {
        return {
            replace: true,
            scope: {
                ngModel: '=',
                dateOptions: '='
            },
            link: function ($scope, $element, $attrs, $controller) {
                var $button = $element.find('button');
                var $input = $element.find('input');
                $button.on('click', function () {
                    if ($input.is(':focus')) {
                        $input.trigger('blur');
                    } else {
                        $input.trigger('focus');
                    }
                });
            }
        };
    });

    //directive for focus
    app.directive('focusMe', function ($timeout) {
        return {
            scope: { trigger: '=focusMe' },
            link: function (scope, element) {
                scope.$watch('trigger', function (value) {
                    if (value === true) {
                        //console.log('trigger',value);
                        //$timeout(function() {
                        element[0].focus();
                        scope.trigger = false;
                        //});
                    }
                });
            }
        };
    });

    app.directive('autofocus', ['$timeout', function ($timeout) {
        return {
            restrict: 'A',
            link: function ($scope, $element) {
                $timeout(function () {
                    $element[0].focus();
                });
            }
        }
    }]);


    // rdPersist - persists the ngModel value and restores it next time the directive is loaded (page refresh)
    // <input ng-model="vm.colFilter" data-rd-persist="thisPage" />
    // Set the rd-persist attribute to a value that identifies the  page/view/location.
    app.directive('rdPersist', function () {
        return {
            require: '^ngModel',
            link: function (scope, element, attr, ctrl) {
                var nameSpace = "rd-" + attr.rdPersist + attr.ngModel;
                //save value every time it changes
                scope.$watch(attr.ngModel, function (newValue, oldValue) {
                    if (newValue !== oldValue) {
                        localStorage.setItem(nameSpace, JSON.stringify(newValue));
                    }
                }, true);

                //fetch the saved state when the directive is loaded
                var test = localStorage.getItem(nameSpace);
                if (test && test != "undefined") {
                    var savedState = JSON.parse(localStorage.getItem(nameSpace));
                    if (ctrl.$viewValue != savedState) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                ctrl.$setViewValue(savedState);
                                ctrl.$render();
                            }, 500)
                        });
                    }
                }

            }
        };
    });

    /**
 * @ngdoc directive
 * @name ng.directive:rcSubmit
 *
 * @description
 * Alternative to ngSubmit that verifies the ngFormController is valid before
 * executing the given expression.  Otherwise it cancels the event. 
 * see http://code.realcrowd.com/on-the-bleeding-edge-advanced-angularjs-form-validation/
 *
 * @element form
 * @param {expression} rcSubmit {@link guide/expression Expression} to eval.
 */
    app.directive(
        'rcSubmit', ['$parse', '$q', '$timeout', function ($parse, $q, $timeout) {
            return {
                restrict: 'A',
                require: ['rcSubmit', '?form'],
                controller: ['$scope', function ($scope) {
                    var formElement = null;
                    var formController = null;
                    var attemptHandlers = [];
                    var submitCompleteHandlers = [];

                    this.attempted = false;
                    this.submitInProgress = false;

                    this.setFormElement = function (element) {
                        formElement = element;
                    }

                    this.submit = function () {
                        if (!formElement) return;

                        jQuery(formElement).submit();
                    }

                    this.onAttempt = function (handler) {
                        attemptHandlers.push(handler);
                    };

                    this.setAttempted = function () {
                        this.attempted = true;

                        angular.forEach(attemptHandlers, function (handler) {
                            handler();
                        });
                    };

                    this.setFormController = function (controller) {
                        formController = controller;
                    };

                    this.needsAttention = function (fieldModelController) {
                        if (!formController) return false;
                        if (fieldModelController) {
                            return fieldModelController.$invalid &&
                                   (fieldModelController.$dirty || this.attempted);
                        } else {
                            return formController && formController.$invalid &&
                                   (formController.$dirty || this.attempted);
                        }
                    };

                    this.onSubmitComplete = function (handler) {

                        submitCompleteHandlers.push(handler);
                    };

                    this.setSubmitComplete = function (success, data) {

                        angular.forEach(submitCompleteHandlers, function (handler) {
                            handler({ 'success': success, 'data': data });
                        });
                    };
                }],
                compile: function (cElement, cAttributes, transclude) {
                    return {
                        pre: function (scope, formElement, attributes, controllers) {

                            var submitController = controllers[0];
                            var formController = (controllers.length > 1) ? controllers[1] : null;

                            submitController.setFormElement(formElement);
                            submitController.setFormController(formController);

                            scope.rc = scope.rc || {};
                            scope.rc[attributes.name] = submitController;
                        },
                        post: function (scope, formElement, attributes, controllers) {

                            var submitController = controllers[0];
                            var formController = (controllers.length > 1) ? controllers[1] : null;
                            var fn = $parse(attributes.rcSubmit);

                            formElement.bind('submit', function (event) {
                                submitController.setAttempted();
                                if (!scope.$$phase) scope.$apply();

                                if (!formController.$valid) return false;

                                var doSubmit = function () {

                                    submitController.submitInProgress = true;
                                    if (!scope.$$phase) scope.$apply();

                                    var returnPromise = $q.when(fn(scope, { $event: event }));

                                    returnPromise.then(function (result) {
                                        submitController.submitInProgress = false;
                                        if (!scope.$$phase) scope.$apply();

                                        // This is a small hack.  We want the submitInProgress
                                        // flag to be applied to the scope before we actually
                                        // raise the submitComplete event. We do that by
                                        // using angular's $timeout service which even without
                                        // a timeout value specified will not fire until after
                                        // the scope is digested.
                                        $timeout(function () {
                                            submitController.setSubmitComplete(true, result);
                                        });

                                    }, function (error) {
                                        submitController.submitInProgress = false;
                                        if (!scope.$$phase) scope.$apply();
                                        $timeout(function () {
                                            submitController.setSubmitComplete(false, error);
                                        });
                                    });
                                };

                                if (!scope.$$phase) {
                                    scope.$apply(doSubmit);
                                } else {
                                    doSubmit();
                                    if (!scope.$$phase) scope.$apply();
                                }
                            });
                        }
                    };
                }
            };
        }]
    );

    //app.directive('thisEarlierThan', function () {
    //    return {
    //        require: 'ngModel',
    //        restrict: 'A',
    //        link: function (scope, elem, attrs, ctrl) {
    //            var startDate,
    //                endDate;

    //            scope.$watch(attrs.ngModel, function (newVal, oldVal, scope) {
    //                startDate = newVal;
    //                check();
    //            });

    //            scope.$watch(attrs.thisEarlierThan, function (newVal, oldVal, scope) {
    //                endDate = newVal;
    //                check();
    //            });

    //            var check = function () {
    //                if (typeof startDate === 'undefined' || typeof endDate === 'undefined') {
    //                    return;
    //                }

    //                if (!validate(startDate)) {
    //                    startDate = new Date(startDate);
    //                    if (!validate(startDate)) {
    //                        return;
    //                    }
    //                }

    //                if (!validate(endDate)) {
    //                    endDate = new Date(endDate);
    //                    if (!validate(endDate)) {
    //                        return;
    //                    }
    //                }

    //                if (startDate < endDate) {
    //                    ctrl.$setValidity('thisEarlierThan', true);
    //                }
    //                else {
    //                    ctrl.$setValidity('thisEarlierThan', false);
    //                }

    //                return;
    //            };

    //            var validate = function (date) {
    //                if (Object.prototype.toString.call(date) === '[object Date]') {
    //                    if (isNaN(date.getTime())) {
    //                        return false;
    //                    }
    //                    else {
    //                        return true;
    //                    }
    //                }
    //                else {
    //                    return false;
    //                }
    //            };
    //        }
    //    };
    //});


    app.directive('rdAttachments', ['attachmentservice', '$timeout', function (attachmentservice, $timeout) {
        //Usage:
        //<div rd-attachments rd-key-name='contactId' rd-key='vm.contact.contactId' rd-upload-allowed='true'></div> 
        var directive = {
            link: link,
            restrict: 'AE',
            transclude: true,
            templateUrl: 'app/components/common-directives/templates/attachments-tpl.html',
            scope: {
                'rdKeyName': '@',
                'rdKey': '=',
                'rdUploadAllowed': '=',
                'rdDataCrop': '=',
                'rdTitle': '@',
                'rdListHeight': '@',
            },
        };
        return directive;

        function link(scope, element, attrs, ctrl) {

            scope.maxHeightStyle = {};
            if (attrs.rdListHeight != undefined && attrs.rdListHeight != null) {
                scope.maxHeightStyle = { 'max-height': attrs.rdListHeight, 'overflow-y': 'auto' };
            }


            scope.refreshList = function () {

                if (scope.rdKey != undefined && scope.rdKeyName != undefined && scope.rdKey != null && scope.rdKeyName != null) {
                    scope.parentKey = scope.rdKey;
                    attachmentservice.getAttachmentList(scope.rdKeyName, scope.rdKey).then(function (data) {
                        scope.attachments = data;
                        scope.uploadModel = null;
                    });
                }
            }


            $timeout(function () {
                scope.$apply(function () {
                    scope.parentKey = scope.rdKey;
                    scope.refreshList();
                })
            }, 900);

            var lastSavedUuid = null;
            scope.uploadComplete = function (info) {

                if (info.count > 1) {
                    var fileArray = info.files();
                    for (var i = 0; i < fileArray.length; i++) {
                        saveFile(fileArray[i]);
                    }
                }
                else {
                    saveFile(info);
                }
                return true;
            }

            function saveFile(info) {
                var attachment = {};
                if (info.uuid == lastSavedUuid) {
                    return; // Already saved.
                }
                lastSavedUuid = info.uuid;
                attachment.parentKeyName = scope.rdKeyName;
                attachment.parentKey = scope.rdKey;
                attachment.name = info.uuid;
                attachment.externalId = info.uuid;
                attachment.displayName = info.name;
                attachment.description = "";
                attachment.isImage = info.isImage;
                attachment.url = info.cdnUrl;
                attachment.baseUrl = info.originalUrl;
                attachment.contentType = "file";
                attachment.size = info.size;
                attachment.fileSystem = "UC"; /* Upload Care */
                attachmentservice.saveAttachment(attachment).then(function (data) {
                    scope.refreshList();
                });
            }

            scope.deleteAttachment = function (attachment) {
                attachmentservice.deleteAttachment(attachment.fileId).then(function () {
                    scope.refreshList();
                });
            }

            scope.$watch('rdKey', function (newValue, oldValue) {
                if (newValue !== oldValue) {
                    scope.refreshList();
                }
            }, false);

            var newwindow = null;
            scope.displayInNewWindow = function (event, url) {
                event.preventDefault();
                newwindow = window.open(url, 'name', 'height=900,width=1000,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=no');
                if (window.focus) { newwindow.focus() }
                return false;
            }
        }
    }]);

    /*!
 * angular-input-match
 * Checks if one input matches another
 * @version v1.0.0
 * @link https://github.com/TheSharpieOne/angular-input-match
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
    app.directive('rdMatch', match);
    function match() {
        return {
            require: '?ngModel',
            restrict: 'A',
            scope: {
                rdMatch: '='
            },
            link: function (scope, elem, attrs, ctrl) {
                if (!ctrl) {
                    console && console.warn('Match validation requires ngModel to be on the element');
                    return;
                }

                scope.$watch(function () {
                    var modelValue = angular.isUndefined(ctrl.$modelValue) ? ctrl.$$invalidModelValue : ctrl.$modelValue;
                    return (ctrl.$pristine && angular.isUndefined(modelValue)) || scope.rdMatch === modelValue;
                }, function (currentValue) {
                    ctrl.$setValidity('rdMatch', currentValue);
                });
            }
        };
    }

    app.directive('sysStrength', ['$templateCache', function ($templateCache) {
        $templateCache.put(
            'template/toolbelt/strength.html',
            '<span class="label label-{{ result.label }}">{{ result.complexity }}</span>'
        );
        var f = 0;
        var requiredComplexity, requiredCharsets;
        var labels = ['success', 'warning', 'danger'];
        var results = [
            { rank: 1, complexity: 'Too short', label: 'danger' },
            { rank: 2, complexity: 'Must have upper,lower, numbers and special symbol', label: 'warning' },
            { rank: 3, complexity: 'Very Weak' },
            { rank: 4, complexity: 'Weak' },
            { rank: 5, complexity: 'Poor' },
            { rank: 6, complexity: 'Good' },
            { rank: 7, complexity: 'Strong' },
            { rank: 8, complexity: 'Very Strong' }
        ];

        function hasLowerCase(string) {
            return /[a-z]+/.test(string);
        }

        function hasUpperCase(string) {
            return /[A-Z]+/.test(string);
        }

        function hasNumeric(string) {
            return /[0-9]+/.test(string);
        }

        function hasSpecial(string) {
            f = 1;
            //   alert(string);
            return /[$-/:-?{-~!"^_`\[\]]/g.test(string);
        }



        function getResult(score) {
            var percentage = (score * 100) / 20;
            var result;
            if (f == 1) {
                result = percentage < 20 ? results[2] :
                       percentage < 35 ? results[3] :
                       percentage < 50 ? results[4] :
                       percentage < 65 ? results[5] :
                       percentage < 90 ? results[6] : results[7];
            }
            var rankDifference = requiredComplexity - result.rank;
            if (rankDifference >= labels.length) {
                rankDifference = labels.length - 1;
            } else if (rankDifference < 0) {
                rankDifference = 0;
            }
            result.label = labels[rankDifference];

            return result;
        }

        return {
            require: 'ngModel',
            scope: {
                model: '=ngModel',
                target: '@'
            },
            replace: true,
            templateUrl: 'template/toolbelt/strength.html',
            link: function (scope, elem, attrs) {
                var minLength = parseInt(attrs.minLength) || 6;
                var formCtrl = elem.inheritedData("$formController");

                requiredComplexity = parseInt(attrs.complexity) > 8 ? 8 : parseInt(attrs.complexity) || 6;
                requiredCharsets = parseInt(attrs.charsets) > 4 ? 4 : parseInt(attrs.charsets) || 1;

                var updateStrength = function (string) {
                    var charsets = 0, score = 0;
                    if (string) {
                        // Gain points based on variation of character types
                        if (hasLowerCase(string)) charsets++;
                        if (hasUpperCase(string)) charsets++;
                        if (hasNumeric(string)) charsets++;
                        if (hasSpecial(string)) charsets++;
                        // Length improves weighting
                        score = charsets * (string.length / 2);
                        // Requires a minimum length
                        scope.result = string.length >= minLength ? (requiredCharsets <= charsets ? getResult(score) : results[1]) : results[0];
                    } else {
                        scope.result = results[0];
                    }
                    formCtrl[scope.target].$setValidity('strength', requiredComplexity <= scope.result.rank && requiredCharsets <= charsets);
                };

                scope.$watch('model', updateStrength);
            }
        };
    }]);

    /*!
    * Show row is new
    * Check if row is new (created today or yesterday).  Add label with New text
    * @version v1.0.0
    * @link https://github.com/TheSharpieOne/angular-input-match
    * @license MIT License, http://www.opensource.org/licenses/MIT
    */
    app.directive('rdShowIsNew', match);
    function match() {
        return {
            restrict: 'A',
            scope: {
                rdShowIsNew: '='
            },
            link: function (scope, elem, attrs) {
                if (scope.rdShowIsNew) {
                    if (moment(scope.rdShowIsNew).isAfter(moment().subtract(7, 'day'), 'd')) {
                        elem.append("<span class='label label-success label-extra-small' title='This record was added today'>NEW</span>");
                    }
                }
            }
        };
    }

    app.directive('rdShowIsModified', match);
    function match() {
        return {
            restrict: 'A',
            scope: {
                rdShowIsNew: '='
            },
            link: function (scope, elem, attrs) {
                if (scope.rdShowIsNew) {
                    if (moment(scope.rdShowIsNew).isAfter(moment().subtract(1, 'day'), 'd')) {
                        elem.append("<span class='label label-success label-extra-small' title='This record was modified today'>Upd</span>");
                    }
                }
            }
        };
    }

    // Keep a bootstrap downdown open when clicking within the dropdown itself.
    // Useful for forms, etc within a dropdown.
    app.directive('dropdownKeepOpen', ['$timeout', function ($timeout) {
        return {
            restrict: 'A',
            link: function (scope, elem, attrs) {
                $timeout(function () {
                    $(elem).on({
                        "shown.bs.dropdown": function () { this.closable = true; },
                        "click": function () { this.closable = true; },
                        "hide.bs.dropdown": function () { return this.closable; }
                    });
                    elem.children('ul').click(function (e) { e.stopPropagation(); })
                }, 1000);
            }
        };
    }]);

    app.directive('passwordConfirm', ['$parse', function ($parse) {
        return {
            restrict: 'A',
            scope: {
                matchTarget: '=',
            },
            require: 'ngModel',
            link: function link(scope, elem, attrs, ctrl) {
                var validator = function (value) {
                    ctrl.$setValidity('match', value === scope.matchTarget);
                    return value;
                }
                ctrl.$parsers.unshift(validator);
                ctrl.$formatters.push(validator);
                // This is to force validator when the original password gets changed
                scope.$watch('matchTarget', function (newval, oldval) {
                    validator(ctrl.$viewValue);
                });
            }
        };
    }]);

    app.directive("phoneFormat", function () {
        return {
            restrict: "A",
            require: "ngModel",
            link: function (scope, element, attr, ngModelCtrl) {
                //Parsing is performed from angular from view to model (e.g. update input box)
                //Sounds like you just want the number without the hyphens, so take them out with replace
                var maxLength = 11;
                var excludePrefixes = [];
                if (attr.isServiceNumber != undefined) {
                    maxLength = 10;
                    excludePrefixes = ['13', '18', '19'];
                }
                var phoneParse = function (value) {
                    if (value != undefined) {
                        var numbers = value && value.replace(/-/g, "");
                        var numbers = numbers && numbers.replace(' ', "");
                        if (numbers.length == 11 && maxLength >= 11) {
                            if (/^\d{11}$/.test(numbers)) {
                                return numbers;
                            }
                        }
                        else if (numbers.length == 10) {
                            if (excludePrefixes.length > 0) {
                                for (var i = 0, len = excludePrefixes.length; i < len; i++) {
                                    if (excludePrefixes[i] == numbers.substr(0, excludePrefixes[i].length)) {
                                        return "";
                                    }
                                }
                            }
                            if (/^\d{10}$/.test(numbers)) {
                                return numbers;
                            }
                        }
                        else if (numbers.length == 0) {
                            return "";
                        }
                    }
                    return "";
                }
                //Formatting is done from view to model (e.g. when you set $scope.telephone)
                //Function to insert hyphens if 10 digits were entered.
                var phoneFormat = function (value) {
                    if (value != null) {
                        var numbers = value && value.replace(/-/g, "");
                        numbers = numbers && numbers.replace(/[\s]/g, "");
                        if (numbers.length == 11 && maxLength >= 11) {
                            var matches = numbers && numbers.substring(0, 11) != "00000000000" && numbers.match(/^(\d{2})(\d{1})(\d{4})(\d{4})$/);
                            if (matches) {
                                return matches[1] + "-" + matches[2] + "-" + matches[3] + "-" + matches[4];
                            }
                        }
                        else if (numbers.length == 10
                                 && numbers.substring(0, 2) != "19"
                                 && numbers.substring(0, 2) != "18"
                                 && numbers.substring(0, 2) != "13"
                                 && numbers.substring(0, 10) != "0000000000") {
                            var matches = numbers && numbers.match(/^(\d{2})(\d{4})(\d{4})$/);
                            if (matches) {
                                return matches[1] + "-" + matches[2] + "-" + matches[3];
                            }
                        }
                        else if (numbers.length == 0) {
                            return numbers;
                        }
                        return undefined;
                    }
                }

                //Add these functions to the formatter and parser pipelines
                ngModelCtrl.$parsers.push(phoneParse);
                ngModelCtrl.$formatters.push(phoneFormat);
                //Since you want to update the error message on blur, call $setValidity on blur
                element.bind("keyup", function () {
                    var value = phoneFormat(element.val());
                    var isValid = !!value;
                    if (isValid) {
                        ngModelCtrl.$setViewValue(value);
                        ngModelCtrl.$render();
                    }
                    if (element.val() == "") {
                        isValid = true;
                    }
                    ngModelCtrl.$setValidity("validatePhoneField", isValid);
                    //call scope.$apply() since blur event happens "outside of angular"
                    scope.$apply();
                });
            }
        };
    });

    //directive for disable backspace acting like back button
    app.directive('backSpaceNotBackButton', [function () {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                // This will stop backspace from acting like the back button
                $(element).keydown(function (e) {
                    var elid = $(document.activeElement)
                    .filter(
                    "input:not([type], [readonly])," +
                    "input[type=text]:not([readonly]), " +
                    "input[type=password]:not([readonly]), " +
                    "input[type=search]:not([readonly]), " +
                    "input[type=number]:not([readonly]), " +
                    "input[type=email]:not([readonly]), " +
                    "input[type=date]:not([readonly]), " +
                    "input[type=datetime]:not([readonly]), " +
                    "input[type=datetime-local]:not([readonly]), " +
                    "input[type=month]:not([readonly]), " +
                    "input[type=tel]:not([readonly]), " +
                    "input[type=time]:not([readonly]), " +
                    "input[type=url]:not([readonly]), " +
                    "input[type=week]:not([readonly]), " +
                    "textarea")[0];
                    if (e.keyCode === 8 && !elid) {
                        return false;
                    }
                });
            }
        }
    }])

    ////Email custom validation directive
    app.directive('advancedEmailValidation', function () {
        var EMAIL_REGEXP = /^[_a-z0-9]+(\.[_a-z0-9]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/;

        return {
            require: 'ngModel',
            restrict: '',
            link: function (scope, elm, attrs, ctrl) {
                // only apply the validator if ngModel is present and Angular has added the email validator
                if (ctrl && ctrl.$validators.email) {

                    // this will overwrite the default Angular email validator
                    ctrl.$validators.email = function (modelValue) {
                        return ctrl.$isEmpty(modelValue) || EMAIL_REGEXP.test(modelValue);
                    };
                }
            }
        };
    });

    app.directive('windowExit', ['$window', 'currentsalemodel', function ($window, currentsalemodel) {
        return {
            restrict: 'AE',
            //performance will be improved in compile
            compile: function (element, attrs) {
                var myEvent = $window.attachEvent || $window.addEventListener,
                chkevent = $window.attachEvent ? 'onbeforeunload' : 'beforeunload'; /// make IE7, IE8 compatable

                myEvent(chkevent, function (e) { // For >=IE7, Chrome, Firefox
                    var confirmationMessage = ' ';  // a space
                    //(e || $window.event).returnValue = "Are you sure that you'd like to close the browser?";
                    //console.log("currentsalemodel.currentSale.saleId:");
                    //console.log(currentsalemodel.currentSale.saleId);
                    if (localStorage.getItem('OpenSale' + currentsalemodel.currentSale.saleId) != null) {
                        localStorage.removeItem('OpenSale' + currentsalemodel.currentSale.saleId);
                    }
                    return confirmationMessage;
                });
            }
        };
    }]);

    // Watch a form and when a field is modified set the isModified flag.
    // If the form is Saved then clear the $Dirty flags on the form so isModified can be set again is something changes.
    app.directive('setFormModified', ['$parse', function ($parse) {
        return {
            restrict: 'A',
            require: '^form',
            link: link

        };

        function link(scope, element, attrs, controller) {
            var setFormModifiedPropertyName = attrs.setFormModified;
            var setFormModifiedGetter = $parse(setFormModifiedPropertyName);
            var setFormModified = setFormModifiedGetter.assign;
            var undoWatch = scope.$watch(function () { return controller.$dirty; },
                                function (newVal, oldVal) {
                                    //check if the form is dirty
                                    if (newVal == true) {
                                        // Set isModified flag on the required object.
                                        var val = setFormModified(scope, true);
                                        resetFormState(val, scope, controller);
                                    }
                                });
            scope.$watch(function () { return setFormModifiedGetter(scope); },
                                function (newVal, oldVal) {
                                    //check if the isModified Flag has been reset (after a save)
                                    if (oldVal == true && newVal == false) {
                                        // Reset the form back to clean and no changes.
                                        controller.$setPristine();
                                        controller.$setUntouched();
                                    }
                                });

        }

        function resetFormState(modField, scope, controller) {
            scope.$watch(function () { return modField },
                                function (newVal, oldVal) {
                                    //check if the form is dirty
                                    if (newVal == false) {
                                        controller.form.$setPristine();
                                        controller.form.$setUntouched();
                                    }
                                });
        }
    }]);

    app.directive('datepickerPopup', function () {
        return {
            restrict: 'EAC',
            require: 'ngModel',
            link: function (scope, element, attr, controller) {
                //remove the default formatter from the input directive to prevent conflict
                controller.$formatters.shift();
            }
        }
    });

    // Force a model value to be a string is its a Number
    // Useful on ngModel values used to on Select elements (as the model must be a string).
    app.directive('forceString', function () {
        return {
            require: 'ngModel',
            link: function (scope, element, attrs, controller) {
                scope.$watch(attrs.ngModel, function (newModel) {
                    if (newModel != undefined) {
                        if (typeof newModel === 'number') {
                            newModel = newModel.toString();
                            controller.$setViewValue(newModel);
                            controller.$render();
                        }
                    }
                });
            } // end of link
        };
    });

    app.directive('myAlert', function ($modal, $log) {
        return {
            restrict: 'E',
            scope: {
                mode: '@',
                boldTextTitle: '@',
                textAlert: '@'
            },
            link: function (scope, elm, attrs) {
                var template = '<div class="modal-body" style="padding:0px">' +
                                '<div class="alert alert-{{data.mode}}" style="margin-bottom:0px">' +
                                    '<strong>{{data.boldTextTitle}}</strong> {{data.textAlert}}' +
                                '</div>' +
                                '<button type="button" class="close" data-ng-click="close()" >' +
                                        '<span class="fa fa-remove">Ok</span>' +
                                    '</button>' +
                            '</div>';
                scope.data = {
                    mode: scope.mode,
                    boldTextTitle: scope.boldTextTitle,
                    textAlert: scope.textAlert
                }

                var ModalInstanceCtrl = function ($scope, $modalInstance, data) {

                    console.log(data);

                    scope.data = {
                        mode: scope.mode || 'info',
                        boldTextTitle: scope.boldTextTitle || 'title',
                        textAlert: scope.textAlert || 'text'
                    }
                };

                elm.parent().bind("click", function (e) {
                    scope.open();
                });

                scope.open = function () {

                    var modalInstance = $modal.open({
                        template: template,
                        controller: ModalInstanceCtrl,
                        backdrop: true,
                        keyboard: true,
                        backdropClick: true,
                        size: 'lg',
                        resolve: {
                            data: function () {
                                return scope.data;
                            }
                        }
                    });


                    modalInstance.result.then(function (selectedItem) {
                        scope.selected = selectedItem;
                    }, function () {
                        $log.info('Modal dismissed at: ' + new Date());
                    });
                }
            }
        };
    });

    app.directive('httpPrefix', function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            link: function (scope, element, attrs, ngModelCtrl) {
                function ensureHttpPrefix(value) {
                    // Need to add prefix if we don't have http:// prefix already AND we don't have part of it
                    if (value && !/^(http):\/\//i.test(value)
                       && 'http://'.indexOf(value) === -1) {
                        ngModelCtrl.$setViewValue('http://' + value);
                        ngModelCtrl.$render();
                        return 'http://' + value;
                    }
                    else
                        return value;
                }
                ngModelCtrl.$parsers.push(ensureHttpPrefix);
                ngModelCtrl.$formatters.push(ensureHttpPrefix);
                
                element.bind("keyup", function () {
                    var value = element.val();
                    var RegExp = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;
                    var isValid = !!value;
                    if (RegExp.test(element.val())) {
                        isValid = true;
                        ngModelCtrl.$setViewValue(value);
                        ngModelCtrl.$render();
                    }
                    else {
                        isValid = false;
                    }
                    if (element.val() == "") {
                        isValid = true;
                    }
                    ngModelCtrl.$setValidity("validateUrlField", isValid);
                    //call scope.$apply() since blur event happens "outside of angular"
                    scope.$apply();
                });
            }
        };
    });

})();