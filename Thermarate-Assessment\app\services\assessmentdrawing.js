// Name: assessmentdrawingservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'assessmentdrawingservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', assessmentdrawingservice]);

    function assessmentdrawingservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'assessmentdrawing/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getAssessmentDrawing: getAssessmentDrawing,
            createAssessmentDrawing: createAssessmentDrawing,
            updateAssessmentDrawing: updateAssessmentDrawing,
            deleteAssessmentDrawing:deleteAssessmentDrawing,
            undoDeleteAssessmentDrawing:undoDeleteAssessmentDrawing,
            getByAssessment: getByAssessment,
            getByComplianceOption: getByComplianceOption,
            updateList: updateList,
            startProcessingPdf,
            getDefaultStampPosition: getDefaultStampPosition,
            zipArchiveForOriginalDocumentsDownloadUrl
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting AssessmentDrawing list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getAssessmentDrawing(assessmentDrawingId) {
            return $http({
                url: baseUrl + 'Get',
                params: { assessmentDrawingId: assessmentDrawingId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAssessmentDrawing(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Drawing Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAssessmentDrawing(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Drawing Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteAssessmentDrawing(assessmentDrawingId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { assessmentDrawingId: assessmentDrawingId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAssessmentDrawing(assessmentDrawingId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { assessmentDrawingId: assessmentDrawingId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getByAssessment(assessmentId, isIncludedInReport, includeArchived) {
            return $http({
                url: baseUrl + 'GetByAssessment',
                params: { assessmentId: assessmentId, isIncludedInReport: isIncludedInReport, includeArchived: includeArchived },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getByComplianceOption(assessmentId, complianceOptionsId) {
            return $http({
                url: baseUrl + 'GetByComplianceOption',
                params: { assessmentId: assessmentId, complianceOptionsId: complianceOptionsId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentDrawing by compliance option: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateList(data) {
            var url = baseUrl + 'UpdateList';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Drawing Stamps Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating stamps AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function startProcessingPdf(assessmentId, convertUrl, convertFileId, fileName, documentId, complianceOptionId) {
            var params = { 
                assessmentId: assessmentId, 
                fileId: convertFileId, 
                fileName: fileName, 
                documentId: documentId,
                complianceOptionId: complianceOptionId 
            };
            
            return $http({
                url: baseUrl + 'StartProcessingPdf',
                params: params,
                data: { url: convertUrl },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp.data;
            }
            function fail(error) {
                var msg = "Error converting AssessmentDrawing: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // get the auto calculated best spot for a stamp.
        function getDefaultStampPosition(drawingUrl, rotation ) {
            return $http({
                url: baseUrl + 'GetDefaultStampPosition',
                params: { fileUrl: drawingUrl, rotation: rotation },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Default Stamp Position: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function zipArchiveForOriginalDocumentsDownloadUrl(assessmentId, complianceOptionId, outputFileName) {
            
            const url = baseUrl + `GetZipArchiveForOriginalDocuments?assessmentId=${assessmentId}&complianceOptionId=${complianceOptionId}&outputFileName=${encodeURIComponent(outputFileName)}`;
            console.log("string is: ", url);

            return url;
        }
    }
})();
