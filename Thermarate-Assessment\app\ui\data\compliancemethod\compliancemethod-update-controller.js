(function () {
    // The CompliancemethodUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'CompliancemethodUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', 'compliancemethodservice', 'security',  compliancemethodUpdateController]);
    function compliancemethodUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, compliancemethodservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Compliance Method';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.complianceMethodCode = null;
        vm.compliancemethod = { };
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Compliance Method";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.complianceMethodCode = $scope.complianceMethodCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.complianceMethodCode = $stateParams.complianceMethodCode;
        }

        // Get data for object to display on page
        var complianceMethodCodePromise = null;
        if (vm.complianceMethodCode != null) {
            complianceMethodCodePromise = compliancemethodservice.getComplianceMethod(vm.complianceMethodCode)
            .then(function (data) {
                if (data != null) {
                    vm.compliancemethod = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("compliancemethod-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                compliancemethodservice.createComplianceMethod(vm.compliancemethod).then(function(data){
                    vm.compliancemethod = data;
                    vm.complianceMethodCode = vm.compliancemethod.complianceMethodCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                compliancemethodservice.updateComplianceMethod(vm.compliancemethod).then(function(data){
                    if (data != null) {
                        vm.compliancemethod = data;
                        vm.complianceMethodCode = vm.compliancemethod.complianceMethodCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            compliancemethodservice.deleteComplianceMethod(vm.complianceMethodCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            compliancemethodservice.undoDeleteComplianceMethod(vm.complianceMethodCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();