﻿
using Microsoft.Data.Sqlite;
using NetTopologySuite.IO;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts certain required data on-the-fly from a GeoPackage (SQLite Database)
    /// to be joined with the main PSA dataset.
    /// </summary>
    public class TenureExtractor
    {
        private const string TITLE_COLUMN = "formatted_title_identifier";
        private const string OWNER_COLUMN = "proprietor_name";
        private const string LGA_COLUMN = "lga_names";

        private string geopackagePath;
        private SqliteConnection connection;
        private SqliteCommand cmd;
        
        public TenureExtractor(string geopackagePath, Logger logger)
        {
            try
            {
                logger.Log("Attempting to initialize TenureExtractor using GeoPackage located at: " + geopackagePath);
                this.geopackagePath = geopackagePath;
                SQLitePCL.Batteries_V2.Init();
                this.connection = new SqliteConnection("Filename=" + this.geopackagePath);
                this.connection.Open();

                CreateLandIdIndex();
            }
            catch(Exception e)
            {
                logger.Log("Exception encountered when trying to initialize TenureExtractor. Error was: " + e.Message);
                throw;
            }
        }

        public void Dispose()
        {
            if (this.connection != null && this.connection.State == ConnectionState.Open)
                this.connection.Close();

            this.connection?.Dispose();
        }

        ~TenureExtractor()
        {
            if (this.connection != null && this.connection.State == ConnectionState.Open)
                this.connection.Close();

            this.connection?.Dispose();
        }

        /// <summary>
        /// Creates
        /// </summary>
        private void CreateLandIdIndex()
        {
            SqliteCommand c = new SqliteCommand("CREATE INDEX IF NOT EXISTS main.idx_land_id ON Land_Tenure_LGate_226(land_id)", this.connection);
            c.ExecuteNonQuery();
        }

        /// <summary>
        /// Returns the title identifier + formatted Project Owner/s for the given LandId
        /// </summary>
        public List<TenureTableData> GetMatchingTenureRecords(string landId)
        {
            // This command can be re-used (minus params) across calls. I wonder if this is actually faster...???
            if (this.cmd == null)
                cmd = new SqliteCommand(
                    $@"SELECT {OWNER_COLUMN}, 
                              {TITLE_COLUMN},
                              {LGA_COLUMN}
                      FROM Land_Tenure_LGATE_226 
                      WHERE land_id = @LandId",
                    this.connection);

            if (cmd.Parameters != null && cmd.Parameters.Count > 0)
                cmd.Parameters.Clear();

            int landIdInt = Convert.ToInt32(landId);
            SqliteParameter param = new SqliteParameter("LandId", landIdInt);
            cmd.Parameters.Add(param);

            var data = new List<TenureTableData>();

            try
            {
                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var titleIdentifier = reader[TITLE_COLUMN] as string;
                        var projectOwner = reader[OWNER_COLUMN] as string;
                        var lgaNames = reader[LGA_COLUMN] as string;

                        data.Add(new TenureTableData()
                        {
                            TitleIdentifier = titleIdentifier,
                            ProjectOwner = FormatProjectOwner(projectOwner),
                            LgaNames = lgaNames
                        });
                    }

                    reader.Close();
                }

                return data;
            }
            catch (Exception e)
            {

                throw;
            }
            
        }

        
        /// <summary>
        /// Format a given 'Project Owner' by capitalising each word and structuring it as "{other name(s)} {last name}", and accounting for multiple owners
        /// </summary>
        public static string FormatProjectOwner(string inProjectOwner)
        {
            // IF is a non-data record, return empty string
            if (NonDataRecord(inProjectOwner))
                return "";

            // IF name includes ',' once, rearrange (go from "Lastname, Firstname" to "Firstname Lastname". 
            string[] tempElements = inProjectOwner.Split(',');
            string tempString = inProjectOwner;
            
            if (tempElements.Length == 2)
                tempString = tempElements[1] + " " + tempElements[0];

            // Then, regardless, split on every space.
            tempElements = tempString.Split(' ');
            StringBuilder outProjectOwner = new StringBuilder();

            for (int j = 0; j < tempElements.Length; j++)
            {
                if (tempElements[j].Length > 0)
                {
                    // IF not any of exceptions, capitalise first letter
                    if (!(tempElements[j].Equals("WA") || tempElements[j].Equals("(WA)")))
                        tempElements[j] = tempElements[j].ToLower();

                    tempElements[j] = CapitaliseLetterInWord(tempElements[j], 0); ;

                    // Format based on apostrophe, eg. "O'Connor" or "'DEL"
                    tempElements[j] = CapitaliseAfterApostrophe(tempElements[j]);

                    // Format based on hyphen, eg. "O'NEILL-GEARY"
                    tempElements[j] = CapitaliseAfterHyphen(tempElements[j]);

                    // Format based on McNol, e.g. "MCNOL" -> "McNol"
                    tempElements[j] = CapitaliseAfterMc(tempElements[j]);


                    // Add to this project owner
                    outProjectOwner.Append(tempElements[j] + " ");
                }
            }
            if (outProjectOwner.Length > 0)
                outProjectOwner = outProjectOwner.Remove(outProjectOwner.Length - 1, 1);

            // RETURN
            return outProjectOwner.ToString();
        }

        private static bool NonDataRecord(string inRecord)
        {
            if (inRecord == null || inRecord == "")
                return true;
            
            if (inRecord.StartsWith("records found but", StringComparison.OrdinalIgnoreCase))
                return true;
            
            return false;
        }

        private static string CapitaliseLetterInWord(string inWord, int inLetterNum)
        {
            char[] tempCharArray = inWord.ToCharArray();
            if (tempCharArray.Length == 0)
                return inWord;
            
            // WHILE inLetterNum'th char is not letter and there are more chars to right, go to next char (like if word
            // is "(<word>)"
            while (!( (tempCharArray[inLetterNum] >= 'A' && tempCharArray[inLetterNum] <= 'Z') ||
                      (tempCharArray[inLetterNum] >= 'a' && tempCharArray[inLetterNum] <= 'z') ) 
                   && inLetterNum < tempCharArray.Length - 1)
                inLetterNum++;
            
            tempCharArray[inLetterNum] = char.ToUpper(tempCharArray[inLetterNum]);
            return new string(tempCharArray);
        }

        private static string CapitaliseAfterApostrophe(string inWord)
        {
            string[] elements = inWord.Split('\'');
            
            if (elements.Length == 1)
                return inWord;
            
            for (int i = 0; i < elements.Length; i++)
            {
                // IF last letter of word and only one char, ignore
                if ( !(i == elements.Length - 1 && elements[i].Length == 1) )
                    elements[i] = CapitaliseLetterInWord(elements[i], 0);
            }
            return string.Join("\'", elements);
        }

        private static string CapitaliseAfterHyphen(string inWord)
        {
            string[] elements = inWord.Split('-');
            if (elements.Length == 1)
                return inWord;
            for (int i = 0; i < elements.Length; i++)
            {
                elements[i] = CapitaliseLetterInWord(elements[i], 0);
            }
            return string.Join("-", elements);
        }

        /// <summary>
        /// MCDONALD -> McDonald
        /// MACDONALD -> MacDonald
        /// 
        /// Will, unfortunately, also transform some names which do NOT require this treatment, however
        /// it appears the overwhelming majority of (at least, English) names require this transform
        /// so it's a good tradeoff.
        /// </summary>
        static Regex mcnol = new Regex("([Mm]a*[Cc])(.*)");
        private static string CapitaliseAfterMc(string inWord)
        {
            var x = mcnol.Match(inWord);

            if (x.Success)
                return Shared.ToTitleCase(x.Groups[1].Value) + Shared.ToTitleCase(x.Groups[2].Value);
            else
                return inWord;
        }
    }

    public class TenureTableData
    {
        public string TitleIdentifier { get; set; }
        public string ProjectOwner { get; set; }
        public string LgaNames { get; set; }
    }
}
