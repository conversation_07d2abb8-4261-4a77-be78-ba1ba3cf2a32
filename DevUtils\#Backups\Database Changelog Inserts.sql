-- 18/03/25
-- Ran this after replacing Test Site 1 db with Test Site 2 db, since all queries were already manually run on Test Site 2 so we needed to manually tell liquibase that these have already run

INSERT INTO [thermarate].[dbo].[DATABASECHANGELOG]
([Id],        [AUTHOR], [FILENAME],                                                                                             [DATEEXECUTED], [ORDEREXECUTED], [EXECTYPE])
VALUES
('001',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/create-tables.sql', GETDATE(),     518,             'EXECUTED'),
('002',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/create-tables.sql', GETDATE(),     519,             'EXECUTED'),
('003',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/create-tables.sql', GETDATE(),     520,             'EXECUTED'),
('180924.1',  'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/create-tables.sql', GETDATE(),     521,             'EXECUTED'),
('050325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/create-tables.sql', GETDATE(),     522,             'EXECUTED'),

('050325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     523,             'EXECUTED'),
('001',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     524,             'EXECUTED'),
('002',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     525,             'EXECUTED'),
('003',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     526,             'EXECUTED'),
('004',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     527,             'EXECUTED'),
('005',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     528,             'EXECUTED'),
('006',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     529,             'EXECUTED'),
('007',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     530,             'EXECUTED'),
('008',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     531,             'EXECUTED'),
('009',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     532,             'EXECUTED'),
('010',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     533,             'EXECUTED'),
('011',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     534,             'EXECUTED'),
('012',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     535,             'EXECUTED'),
('013',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     536,             'EXECUTED'),
('014',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     537,             'EXECUTED'),
('015',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     538,             'EXECUTED'),
('016',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     539,             'EXECUTED'),
('017',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     540,             'EXECUTED'),
('018',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     541,             'EXECUTED'),
('019',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     542,             'EXECUTED'),
('020',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     543,             'EXECUTED'),
('021',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     544,             'EXECUTED'),
('022',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     545,             'EXECUTED'),
('023',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     546,             'EXECUTED'),
('024',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     547,             'EXECUTED'),
('025',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     548,             'EXECUTED'),
('026',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     549,             'EXECUTED'),
('027',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     550,             'EXECUTED'),
('028',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     551,             'EXECUTED'),
('029',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     552,             'EXECUTED'),
('030',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     553,             'EXECUTED'),
('031',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     554,             'EXECUTED'),
('032',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     555,             'EXECUTED'),
('033',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     556,             'EXECUTED'),
('034',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     557,             'EXECUTED'),
('035',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     558,             'EXECUTED'),
('036',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     559,             'EXECUTED'),
('037',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     560,             'EXECUTED'),
('038',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     561,             'EXECUTED'),
('039',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     562,             'EXECUTED'),
('040',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     563,             'EXECUTED'),
('041',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     564,             'EXECUTED'),
('042',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     565,             'EXECUTED'),
('043',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     566,             'EXECUTED'),
('044',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     567,             'EXECUTED'),
('045',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     568,             'EXECUTED'),
('170924',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     569,             'EXECUTED'),
('011024',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     570,             'EXECUTED'),
('261124',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     571,             'EXECUTED'),
('220125',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     572,             'EXECUTED'),
('060225',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     573,             'EXECUTED'),
('110225',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     574,             'EXECUTED'),
('260225',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     575,             'EXECUTED'),
('260225.2',  'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     576,             'EXECUTED'),
('040325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     577,             'EXECUTED'),
('040325_02', 'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     578,             'EXECUTED'),
('050325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     579,             'EXECUTED'),
('130325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     580,             'EXECUTED'),
('170325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/alter-tables.sql',  GETDATE(),     581,             'EXECUTED'),

('001',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     582,             'EXECUTED'),
('002',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     583,             'EXECUTED'),
('003',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     584,             'EXECUTED'),
('004',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     585,             'EXECUTED'),
('005',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     586,             'EXECUTED'),
('006',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     587,             'EXECUTED'),
('007',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     588,             'EXECUTED'),
('008',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     589,             'EXECUTED'),
('041',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     590,             'EXECUTED'),
('042',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     591,             'EXECUTED'),
('043',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     592,             'EXECUTED'),
('044',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     593,             'EXECUTED'),
('045',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     594,             'EXECUTED'),
('046',       'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     595,             'EXECUTED'),
('050325',    'JCC',    'C:/projects/thermarate-assessment/thermarate-assessment/DatabaseScripts//Release1.0/apply-data.sql',    GETDATE(),     596,             'EXECUTED');