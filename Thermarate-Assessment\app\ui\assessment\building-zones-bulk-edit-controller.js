(function () {
    'use strict';
    let controllerId = 'BuildingZonesBulkEditCtrl';
    angular
        .module('app')
        .controller(controllerId, ['common', '$scope', '$mdDialog', buildingZonesBulkEditController])

    function buildingZonesBulkEditController(common, $scope, $mdDialog) {

        let vm = this;

        vm.storeys = $scope.storeys;
        vm.zoneTypeList = $scope.zoneTypeList;
        vm.zoneActivityList = $scope.zoneActivityList;
        vm.nccClassificationList = $scope.nccClassificationList;
        vm.isExterior = $scope.isExterior;
        vm.ventilationList = $scope.ventilationList;

        vm.currentZones = $scope.currentZones;
        vm.code = $scope.code;
        vm.title = $scope.title ?? 'Building Zones Bulk Edit';

        // This gets populated with any data we wish to apply over existing fields.
        // Any null/undefined values are NOT applied (i.e. nothing gets nullified).
        vm.data = {
            bulkEditAction: 'EDIT' // Valid are EDIT, COPY, CLEAR, DELETE
        }; 

        /** Simply returns any selected values to the caller, who must then apply them. */
        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.clearEditValues = function () {
            let action = vm.data.bulkEditAction;
            vm.data = {
                bulkEditAction: action
            }; 
        }

        vm.availableZoneActivityList = function () {
            if (!vm.isExterior)
                return vm.zoneActivityList?.filter(x => x.availableFor != 'exterior');
            else
                return []; // For exterior zones, we do not which the user to be able to change it.
        }

        vm.availableZoneTypeList = function () {
            if (!vm.isExterior)
                return vm.zoneTypeList?.filter(x => x.availableFor != 'exterior');
            else
                return vm.zoneTypeList?.filter(x => x.availableFor == 'exterior' || x.availableFor == 'all');
        }

        /** Filters available selection based on rules from THR-77 (Also see zone-list-controller.js equivalent) */
        vm.availableNccClassificationList = function () {

            let available = [];
            if (!vm.isExterior)
                available = vm.nccClassificationList?.filter(x => x.availableFor != 'exterior');
            else
                return vm.nccClassificationList?.filter(x => x.availableFor == 'exterior' || x.availableFor == 'all');

            // Ok so this only runs if this is NOT an exterior zone.
            let interiorZones = vm.currentZones?.filter(x => x.zoneActivity == null);
            let allClassifications = vm.nccClassificationList;

            if (interiorZones == null || interiorZones.length == 0) {
                return allClassifications;
            }

            let class10A = allClassifications?.find(x => x.nccClassificationCode == 'Class10A');
            if (class10A == null)
                return allClassifications;

            // Find the first interior zone which is NOT AttachedClass10a, check its NCC classification type, and limit based on that.
            let limiting = interiorZones.filter(x => x.nccClassification?.nccClassificationCode != class10A.nccClassificationCode);

            // If we found no limiting zones, return all options
            if (limiting == null || limiting.length == 0) {
                return allClassifications;
            }

            return [limiting[0].nccClassification, class10A];

        }

        vm.showForInteriorAndExterior = function () {
            return vm.code == 'selectedForInteriorBulkEdit' || vm.code == 'selectedForExteriorBulkEdit'
        }

        vm.showForInterior = () => vm.code == 'selectedForInteriorBulkEdit';
        vm.showForRoofSpace = () => vm.code == 'selectedForRoofSpaceBulkEdit';
        vm.showForSubfloorSpace = () => vm.code == 'selectedForSubfloorBulkEdit';
        vm.showForGroundSurface = () => vm.code == 'selectedForGroundZoneBulkEdit';

        vm.roofVentilationList = () => vm.ventilationList?.filter(x => x.type == "Roof Space Ventilation");
        vm.subfloorVentilationList = () => vm.ventilationList?.filter(x => x.type == "Subfloor Ventilation" && x.airCavityCode !== "SubfloorConnectedToGround");

    }
    // END
})();