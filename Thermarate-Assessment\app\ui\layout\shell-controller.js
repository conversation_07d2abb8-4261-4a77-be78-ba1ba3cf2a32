(function () {
    'use strict';
    var controllerId = 'ShellCtrl';

    angular.module('app').controller(
        controllerId,
        ['$rootScope', 'common', 'config', shell]
    );

    function shell($rootScope, common, config) {
        var vm = this;
        var events = config.events;
        vm.busyMessage = 'Please wait ...';
        vm.isBusy = true;
        vm.spinnerOptions = {
            radius: 40,
            lines: 7,
            length: 0,
            width: 30,
            speed: 1.7,
            corners: 1.0,
            trail: 100,
            color: '#F58A00'
        };

        activate();

        vm.showHeader = function () {
            return !window.location.hash.includes('#/login') && !window.location.hash.includes("#/external-login-verify");
        };

        function activate() {
            common.activateController([], controllerId);
        }

        function toggleSpinner(on) { vm.isBusy = on; }

        $rootScope.$on(
            '$routeChangeStart',
            (event, next, current) => { toggleSpinner(true); }
        );

        $rootScope.$on(
            events.controllerActivateSuccess,
            data => { toggleSpinner(false); }
        );

        $rootScope.$on(
            events.spinnerToggle,
            data => { toggleSpinner(data.show); }
        );
    };

})();