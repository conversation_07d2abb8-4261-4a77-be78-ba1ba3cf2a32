(function () {
    'use strict';
    var serviceId = 'glazingcalculatorexportservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', glazingcalculatorexportservice]);

    function glazingcalculatorexportservice(common, config, $http) {
        
        var $q = common.$q;
        var log = common.logger;
        var baseUrl = config.servicesUrlPrefix + 'glazingcalculatorexport/';
        
        var service = {
            generateGlazingCalculator2019,
            generateGlazingCalculator2019Multi,
            generateGlazingCalculator2022,
            generateGlazingCalculator2022Multi,
        };
            
        return service;


        /**
         * Generates 1 glazing calculator per-storey on the server, zips them up and returns it to the caller for
         * download. 
         * 
         * @param storeys Storey data in 'dictionary' format (so should just be an object / K-V Pair). Actual value 
         *                class corresponds with StoreyDto from V1 of AB-Sim.
         * @param exportedFileName Optional override for when you want to specify the default download name.
         * @returns {*} Returns the HTTP response body in case you want to check errors, but not you do NOT have to
         *              throw up the download request yourself - that is handled here.
         */
        function generateGlazingCalculator2019(storeys, exportedFileName = "GlazingCalculator") {

            // TODO: Might be a good idea to ensure that the exportedFileName is valid.

            var url = baseUrl + 'GenerateGlazingCalc2019?exportedFileName=' + common.encodeForUrl(exportedFileName);
            return $http
                .post(
                    url,
                    storeys, {
                        responseType: "blob"
                    })
                .then(
                    success,
                    fail);

            function success(resp) {

                // Create a dummy anchor element with a data uri and simulate a click on it.
                // This will show the download pop-up to the user.
                var a = window.document.createElement('a');
                a.href = window.URL.createObjectURL(resp.data);
                a.download = exportedFileName + ".zip";
                document.body.appendChild(a)
                a.click();

                // Finally, remove our anchor from the dom.
                document.body.removeChild(a);

                log.logSuccess("Glazing Calculator generated successfully. See downloads.");
                return resp;
            }

            function fail(error) {
                var msg = "Error generating Glazing Calculator: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }

        function generateGlazingCalculator2019Multi(storeys, exportedFileName = "GlazingCalculator") {

            var url = baseUrl + 'GenerateGlazingCalc2019Multi?exportedFileName=' + common.encodeForUrl(exportedFileName);
            return $http
                .post(
                    url,
                    storeys, {
                        responseType: "blob"
                    })
                .then(
                    success,
                    fail);

            function success(resp) {

                // Create a dummy anchor element with a data uri and simulate a click on it.
                // This will show the download pop-up to the user.
                var a = window.document.createElement('a');
                a.href = window.URL.createObjectURL(resp.data);
                a.download = exportedFileName + ".zip";
                document.body.appendChild(a)
                a.click();

                // Finally, remove our anchor from the dom.
                document.body.removeChild(a);

                log.logSuccess("Glazing Calculator generated successfully. See downloads.");
                return resp;
            }

            function fail(error) {
                var msg = "Error generating Glazing Calculator: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }


        /**
         * Generates a glazing calculator (in a single file .xlsx format) on the server and returns it to the caller for
         * download. The glazing data supplied should be in the format of the 'GlazingDto' found in V2 (currently 
         * 'master' branch of AB-Sim's GlazingCalculator package).
         * 
         * @param glazingData Glazing information required to export the calculator.
         * @param exportedFileName Optional override for when you want to specify the default download name.
         * 
         * @returns {*} Returns the HTTP response body in case you want to check errors, but not you do NOT have to 
         *              throw up the download request yourself - that is handled here.
         */
        function generateGlazingCalculator2022(glazingData, exportedFileName = "GlazingCalculator") {
            
            // TODO: Might be a good idea to ensure that the exportedFileName is valid.

            var url = baseUrl + 'GenerateGlazingCalc2022?exportedFileName=' + common.encodeForUrl(exportedFileName);
            return $http
                .post(
                    url, 
                    glazingData, {
                        responseType: "blob"
                    })
                .then(
                    success, 
                    fail);
            
            function success(resp) {
                
                // Create a dummy anchor element with a data uri and simulate a click on it.
                // This will show the download pop-up to the user.
                var a = window.document.createElement('a');
                a.href = window.URL.createObjectURL(resp.data);
                a.download = exportedFileName + ".xlsx";
                document.body.appendChild(a)
                a.click();

                // Finally, remove our anchor from the dom.
                document.body.removeChild(a); 
                
                log.logSuccess("Glazing Calculator generated successfully. See downloads.");
                return resp;
            }
            
            function fail(error) {
                var msg = "Error generating Glazing Calculator: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            
        }

        function generateGlazingCalculator2022Multi(glazingData, exportedFileName = "GlazingCalculator") {
            
            // TODO: Might be a good idea to ensure that the exportedFileName is valid.

            var url = baseUrl + 'GenerateGlazingCalc2022Multi?exportedFileName=' + common.encodeForUrl(exportedFileName);
            return $http
                .post(url, glazingData)
                .then(success, fail);
            
            function success(resp) {                
                log.logSuccess("Glazing Calculator generated successfully. See downloads.");
                return resp;
            }
            
            function fail(error) {
                var msg = "Error generating Glazing Calculator: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            
        }

    }
})();
