(function () {
	'use strict';
	angular
		.module('app')
		.component('searchStreetType', {
			bindings: {
				assessment: '<',
				addressChanged: '&',
				condensed: '<',
				required: '<'
			},
			templateUrl: 'app/ui/assessment/search-street-type/search-street-type.html',
			controller: SearchStreetType,
			controllerAs: 'vm'
		});

	SearchStreetType.$inject = ['addressservice'];

	function SearchStreetType(addressservice) {
		var vm = this;

		vm.searchStreetTypeText = '';
		vm.selectedSearchStreetType = '';

		vm.searchStreetType = searchStreetType;
		vm.searchStreetTypeItemChange = searchStreetTypeItemChange;

		vm.$onInit = function () {
			vm.searchStreetTypeText = vm.assessment.assessmentProjectDetail.streetType;
		}

		function searchStreetType(search) {
			if (!search) {
				return Promise.resolve([]);
			}
			return addressservice.queryStreetType(search);
		}
		function searchStreetTypeItemChange(item) {
			if (item) {
				vm.assessment.assessmentProjectDetail.streetType = item;
				vm.addressChanged();
			}
		}
	}
})();