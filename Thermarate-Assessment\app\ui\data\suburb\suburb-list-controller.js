(function () {
    // The SuburbListCtrl supports a list page.
    'use strict';
    var controllerId = 'SuburbListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'suburbservice', 'daterangehelper', '$interval', 'Upload', suburbListController]);
function suburbListController($rootScope, $scope, $mdDialog, suburbservice, daterangehelper, $interval, Upload) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Suburbs';
        vm.suburbList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.activated = false;
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'name',
                    description: 'Name',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'stateCode',
                    description: 'State',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'postcode',
                    description: 'Postcode',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        vm.progressString = "";
        vm.progress = 0;
        vm.progressClass = "progress-bar-warning";
        vm.statusText = "Awaiting file";
        vm.currentlyUploading = false;
        //list of uploads that are being processed or are queued to be processed
        vm.processingList = [];
        //list of uploads that have completed processing or have failed.
        vm.completedList = [];
        vm.uploadCompleteCallbackHandle = null;

        var persistRangeName = "suburbList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createSuburb = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/suburb/suburb-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "name", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            suburbservice.getListCancel();
            suburbservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = suburbservice.currentFilter();
                    vm.suburbList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.suburbList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        vm.uploadFile = function ($file) {
            //The button can fire this event before file is selected with a null value, so ust return if that's the case.
            if ($file == undefined || $file == null) {
                return;
            }
            if (vm.currentlyUploading) {
                alert("Only one file at a time may be uploaded!");
                return;
            }
            vm.currentlyUploading = true;
            vm.progressClass = "progress-bar-warning";
            vm.progress = 0;
            vm.progressString = "";
            vm.statusText = "Uploading file to Database...";
            var promise = Upload.upload({
                url: "../api/UploadSuburbSpreadsheet/Post", // webapi url
                method: "POST",
                file: $file
            });
            promise.progress(function (evt) {
                // get upload percentage
                vm.progress = parseInt(100.0 * evt.loaded / evt.total);
                vm.progressString = parseInt(100.0 * evt.loaded / evt.total) + "%";
            }).success(function (data, status, headers, config) {
                vm.activated = false;
                vm.progressClass = "progress-bar-success";
                vm.progressString = "Complete! (100%)";
                vm.statusText = "Upload Complete.";
                vm.refreshList(null);
                vm.uploadCompleteCallbackHandle = $interval(resetUploadForm, 5000, 1);
                // file is uploaded successfully
            }).error(function (data, status, headers, config) {
                // file failed to upload
                vm.progressString = "Error! Something went wrong.";
                vm.progressClass = "progress-bar-danger";
                vm.statusText = "Upload Failed. Logging errors..";
                vm.activated = false;
                $interval(resetUploadForm, 5000, 1);
            });
        }

        var resetUploadForm = function () {
            vm.progress = 0;
            vm.progressString = "";
            vm.progressClass = "progress-bar-warning";
            vm.statusText = "Awaiting file";
            vm.currentlyUploading = false;
        }

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createSuburb,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();

        //destroy the $interval callbacks on page destroy to avoid memory leaks
        $scope.$on('$destroy', function () {
            if (angular.isDefined(vm.uploadCompleteCallbackHandle)) {
                $interval.cancel(vm.uploadCompleteCallbackHandle);
                vm.uploadCompleteCallbackHandle = undefined;
            }
        });
    }
})();