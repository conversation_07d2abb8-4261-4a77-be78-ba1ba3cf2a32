USE [thermarate];

SELECT TOP(10)
       [SlipAddressDataId]
      ,[LandId]
      ,[SurveyType]
      ,[SurveyNumber]
      ,[LotNumber]
      ,[UnitType]
      ,[UnitNumber]
      ,[RoadNumber]
      ,[RoadName]
      ,[RoadType]
      ,[Locality]
      ,[PostCode]
      ,[FormattedAddress]
      ,[Latitude]
      ,[Longitude]
      ,[LandArea]
      ,[TitleIdentifier]
      ,[ProjectOwner]
      ,[Boundary]
      ,[BoundaryString]
      ,[BoundaryJson]
  FROM [dbo].[RSS_SlipAddressDataB]
  WHERE 1=1
    -- AND [RoadNumber] = '13'
    AND [RoadName] = 'Enchanted'
    -- AND [PostCode] = '6171'