﻿(function () {
    'use strict';
    var app = angular.module('app');
    app.directive('fieldRequired', ['$compile', function ($compile) {
        return {
            restrict: "A",
            scope: {
                fieldRequired: '@' // what needs to be passed to the template
            },
            link: function (scope, element, attrs, ctrl) {
                   if (attrs.fieldRequired == 'true') {
                   element.append($compile('<span style="color:red;"> * </span>')(scope));
                }
            }
        }
    }]);
})();
