﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using TenureExtraction;

namespace ExtractionTests
{
    [TestClass]
    public class StandardHomeModelExtractionTests
    {


        [TestMethod]
        public  void TestStandardHomeModelExtraction()
        {
            string connectionString = "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True";
            string path = Directory.GetCurrentDirectory() + "\\Data\\Standard Home Model (SAMPLE).xlsx"; 
            StandardHomeModelExtractor.Extract(
                connectionString, 
                path, 
                Guid.Parse("4ea4dbca-df69-4712-b4d5-8483616b8ba0"));

            ;
        }
        
        [TestMethod]
        public void TestDistinctCheck()
        {
            string connectionString = "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True";
            StandardHomeModelExtractor.DetermineDistinctVariableOptions(
                    connectionString, 
                    Guid.Parse("4ea4dbca-df69-4712-b4d5-8483616b8ba0"))

            ;
        }







    }
}
