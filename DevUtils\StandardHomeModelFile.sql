USE [thermarate];

SELECT [file].[StandardHomeModelFileId]
      ,[file].[StandardHomeModelId]
	  ,[model].[Title] [__Model]
      ,[file].[FileId]
      ,[file].[MetadataJson]
      ,[file].[Deleted]
      ,[file].[CreatedOn]
      ,[file].[CreatedByName]
      ,[file].[ModifiedOn]
      ,[file].[ModifiedByName]
      ,[SortOrder]
  FROM [dbo].[RSS_StandardHomeModelFile] [file]
  INNER JOIN [dbo].[RSS_StandardHomeModel] [model] ON [file].[StandardHomeModelId] = [model].[StandardHomeModelId]
  ORDER BY [file].[CreatedOn] DESC