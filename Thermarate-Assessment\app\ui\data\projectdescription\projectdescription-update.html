<form name="projectdescriptionform" class="main-content-wrapper" novalidate data-ng-controller='ProjectdescriptionUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex-50">
                    <md-card>
                        <md-card-header>
                            Building Description
                        </md-card-header>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- ******** Project Description Code ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Building Description Code</label>
                                    <input type="text" name="projectDescriptionCode"
                                           ng-model="vm.projectdescription.projectDescriptionCode" md-autofocus
                                           md-maxlength="20"
                                           required
                                           ng-disabled="!vm.newRecord || vm.editPermission == false"/>
                                    <div ng-messages="projectdescriptionform.projectDescriptionCode.$error">
                                        <div ng-message="required">Building Description Code is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Description ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Description</label>
                                    <input type="text" name="description"
                                           ng-model="vm.projectdescription.description"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="projectdescriptionform.description.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Number of Storeys With Glzing -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Number of Storeys</label>
                                    <md-select name="NumberOfStoreysWithGlazing"
                                               ng-model="vm.projectdescription.numberOfStoreysWithGlazing"
                                               ng-change="vm.floorsWithGlazingChanged(vm.projectdescription.numberOfStoreysWithGlazing)">
                                        <md-option ng-repeat="number in ['Not Set', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                                   ng-value="number != 'Not Set' ? number : null">
                                            {{number}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Default Storey Names -->
                                <div ng-if="vm.projectdescription.numberOfStoreysWithGlazing !== null && vm.projectdescription.numberOfStoreysWithGlazing > 0"
                                     flex="100">
                                    <div layout layout-align="start center">
                                   <span style="font-size: 9px; font-weight: 100; color:#999999">
                                       Default Storey Names
                                   </span>
                                    </div>
                                    <div layout="row" ng-repeat="floor in vm.projectdescription.storeys track by $index">
                                        <span style="margin-top: 8px; font-weight: bold;">{{$index + 1}}.</span>

                                        <md-input-container class="md-block vertically-condensed"
                                                            flex="100">
                                            <input ng-model="floor.description"
                                                   class="vertically-condensed">
                                        </md-input-container>
                                    </div>
                                </div>

                                <!-- Default For Number of Storeys -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Default For Number of Storeys</label>
                                    <md-select name="defaultForStoreyCount"
                                               ng-required
                                               ng-model="vm.projectdescription.defaultForStoreyCount">
                                        <md-option ng-value="true">
                                            Yes
                                        </md-option>
                                        <md-option ng-value="false">
                                            No
                                        </md-option>
                                    </md-select>
                                    <div ng-messages="projectdescriptionform.defaultForStoreyCount.$error">
                                        <div ng-message="required">Default is required.</div>
                                    </div>
                                </md-input-container>

                            </fieldset>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.projectdescription"></div>
                            </div>
                        </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           ng-disabled="projectdescriptionform.$invalid || vm.editPermission == false" 
                           ng-show="vm.projectdescription.deleted!=true" 
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised" 
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.projectdescription.projectDescriptionCode!=null && vm.projectdescription.deleted!=true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.projectdescription.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
