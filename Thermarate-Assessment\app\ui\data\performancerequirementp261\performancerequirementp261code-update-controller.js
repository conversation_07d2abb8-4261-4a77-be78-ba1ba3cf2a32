(function () {
    // The Performancerequirementp261codeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'Performancerequirementp261codeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'performancerequirementp261service', performancerequirementp261codeUpdateController]);
function performancerequirementp261codeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  performancerequirementp261service) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Performance Requirement P261';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.performanceRequirementP261Code = null;
        vm.performancerequirementp261code = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Performance Requirement P261";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.performanceRequirementP261Code = $scope.performanceRequirementP261Code;
            }
            vm.hideActionBar = true;
        } else {
            vm.performanceRequirementP261Code = $stateParams.performanceRequirementP261Code;
        }

        // Get data for object to display on page
        var performanceRequirementP261CodePromise = null;
        if (vm.performanceRequirementP261Code != null) {
            performanceRequirementP261CodePromise = performancerequirementp261service.getPerformanceRequirementP261(vm.performanceRequirementP261Code)
            .then(function (data) {
                if (data != null) {
                    vm.performancerequirementp261code = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("performancerequirementp261code-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                performancerequirementp261service.createPerformanceRequirementP261(vm.performancerequirementp261code).then(function(data){
                    vm.performancerequirementp261code = data;
                    vm.performanceRequirementP261Code = vm.performancerequirementp261code.performanceRequirementP261Code;
                    vm.cancel();
                });
            }else{
                performancerequirementp261service.updatePerformanceRequirementP261(vm.performancerequirementp261code).then(function(data){
                    if (data != null) {
                        vm.performancerequirementp261code = data;
                        vm.performanceRequirementP261Code = vm.performancerequirementp261code.performanceRequirementP261Code;
                    }
                });
            }
        }

        vm.delete = function () {
            performancerequirementp261service.deletePerformanceRequirementP261(vm.performanceRequirementP261Code).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            performancerequirementp261service.undodeletePerformanceRequirementP261(vm.performanceRequirementP261Code).then(function () {
                vm.cancel();
            });
        }

    }
})();