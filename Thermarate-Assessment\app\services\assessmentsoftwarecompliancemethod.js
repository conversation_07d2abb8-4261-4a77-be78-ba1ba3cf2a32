// Name: assessmentsoftwarecompliancemethodservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'assessmentsoftwarecompliancemethodservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', assessmentsoftwarecompliancemethodservice]);

    function assessmentsoftwarecompliancemethodservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'assessmentsoftwarecompliancemethod/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getAssessmentSoftwareComplianceMethod: getAssessmentSoftwareComplianceMethod,
            createAssessmentSoftwareComplianceMethod: createAssessmentSoftwareComplianceMethod,
            updateAssessmentSoftwareComplianceMethod: updateAssessmentSoftwareComplianceMethod,
            deleteAssessmentSoftwareComplianceMethod:deleteAssessmentSoftwareComplianceMethod,
            undoDeleteAssessmentSoftwareComplianceMethod: undoDeleteAssessmentSoftwareComplianceMethod,
            getAvailableAssessmentSoftwareForComplianceMethod: getAvailableAssessmentSoftwareForComplianceMethod,
            getAvailableComplianceMethodsForAssessmentSoftware: getAvailableComplianceMethodsForAssessmentSoftware,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting AssessmentSoftwareComplianceMethod list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getAssessmentSoftwareComplianceMethod(assessmentSoftwareComplianceMethodId) {
            return $http({
                url: baseUrl + 'Get',
                params: {assessmentSoftwareComplianceMethodId: assessmentSoftwareComplianceMethodId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentSoftwareComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }


        function getAvailableAssessmentSoftwareForComplianceMethod(complianceMethodCode) {
            return $http({
                url: baseUrl + 'GetAvailableAssessmentSoftwareForComplianceMethod',
                params: { complianceMethodCode: complianceMethodCode },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting available AssessmentSoftware for ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getAvailableComplianceMethodsForAssessmentSoftware(assessmentSoftwareCode) {
            return $http({
                url: baseUrl + 'GetAvailableComplianceMethodsForAssessmentSoftware',
                params: { assessmentSoftwareCode: assessmentSoftwareCode },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting available AssessmentSoftware for ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAssessmentSoftwareComplianceMethod(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Software Compliance Method Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created AssessmentSoftwareComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAssessmentSoftwareComplianceMethod(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Software Compliance Method Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating AssessmentSoftwareComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteAssessmentSoftwareComplianceMethod(assessmentSoftwareComplianceMethodId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { assessmentSoftwareComplianceMethodId: assessmentSoftwareComplianceMethodId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting AssessmentSoftwareComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAssessmentSoftwareComplianceMethod(assessmentSoftwareComplianceMethodId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { assessmentSoftwareComplianceMethodId: assessmentSoftwareComplianceMethodId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for AssessmentSoftwareComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
