// Ensure 'options' value is filled PRIOR to being passed to this component.
(function () {

    'use strict';
    angular
        .module('app')
        .component('clientOptionsForm', {
            bindings: {
                options: '<',       // 'ClientOptions' Object which contains our settings.
                defaults: '<',      // Client Defaults sometimes require updating also... (Dodgy)
                optionData: '<',    // JSON structure with all required lists in here.
                formStyle: '<',      // OPTIONAL. If "CONDENSED", the inputs will be condensed to match other modal windows (e.g. job-update).
                disabled: '<',
            },
            templateUrl: 'app/ui/client/client-options-form.html',
            controller: ClientOptionsForm,
            controllerAs: 'vm'
        });

    ClientOptionsForm.$inject = ['$scope'];

    function ClientOptionsForm($scope) {

        let vm = this;

        /** Retrieve and initialize any required data here, not floating around the controller. */
        function initialize() {
        }

        /** Checks to see if we have enabled the HER compliance type */
        vm.hasCMHouseEnergyRatingComplianceMethod = function () {
            let f = vm.options.availableComplianceMethodCodes
                ?.some(x => x === "CMHouseEnergyRating" || x === "CMPerfSolutionHER" || x === "CMPerfWAProtocolHER");
            return f;
        }

        // Finally, initialize our component.
        initialize();
    }
})();