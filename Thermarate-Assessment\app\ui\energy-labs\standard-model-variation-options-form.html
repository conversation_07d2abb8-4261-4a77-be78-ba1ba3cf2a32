<div style="width: 100%;">

    <div class="options-buttons-container">

        <!-- Groups -->
        <div ng-repeat="fieldGroup in vm.theList track by $index" class="dropdowns-group-{{$index}}">
            <!-- Repeat -->
            <div ng-repeat="fieldName in fieldGroup track by $index" class="dropdown-container">
                <!-- Dropdown -->
                <div class="design-options-dropdown" ng-click="$event.stopPropagation();">
                    <!-- Selected -->
                    <div class="options-dropdown-selected" ng-class="{ 'selectable': vm.theParentModel.dropdownFields[fieldName].options.length > 1, 'small': fieldGroup.length > 1 }" ng-mousedown="vm.expandField(fieldName); $event.stopPropagation();">
                        <div>{{vm.theParentModel.dropdownFields[fieldName].selected ? vm.theParentModel.dropdownFields[fieldName].selected.optionName : "-"}}</div>
                        <img ng-if="vm.theParentModel.dropdownFields[fieldName].options.length > 1"
                             class="options-dropdown-icon"
                             src="../../../content/images/arrow-up-skinny.png"
                             ng-style="{ 'transform' : vm.theParentModel.dropdownFields[fieldName].expanded ? 'none' : 'rotateX(180deg)' }" />
                        <md-tooltip class="dropdown-tooltip" md-direction="top" md-delay="vm.tooltipDelay">
                            {{vm.theParentModel.dropdownFields[fieldName].selected ? vm.theParentModel.dropdownFields[fieldName].selected.optionName : "-"}} <div class="dropdown-tooltip-triangle" />
                        </md-tooltip>
                    </div>
                    <!-- Expanded -->
                    <div class="dropdown-options-container" ng-class="{ 'dropdown-expanded': vm.theParentModel.dropdownFields[fieldName].expanded }">
                        <div ng-repeat="option in vm.theParentModel.dropdownFields[fieldName].options track by $index"
                             class="design-option"
                             ng-class="{ 'design-option-selected': option.standardHomeModelVariationOptionId == vm.theParentModel.dropdownFields[fieldName].selected.standardHomeModelVariationOptionId}"
                             ng-click="vm.selectOption(fieldName, option); $event.stopPropagation();">
                            {{option.optionName}}
                        </div>
                    </div>
                    <!--  -->
                </div>
                <!-- Divider -->
                <div class="options-divider" />
            </div>
        </div>

    </div>

</div>

<style>

    .specification-label {
        font-size: 9px;
        color: rgba(0,0,0,0.54);
    }

    /* Dropdowns */
    .options-buttons-container {
        position: relative;
        margin: 10px 0;
        display: flex;
        width: calc(100% + 200px);
        margin-left: -100px;
        justify-content: center;
        z-index: 11;
    }

        .options-buttons-container > div {
            display: flex;
            align-items: center;
        }
    /* Outer Groups Equal Size */
    .dropdowns-group-0, .dropdowns-group-2 {
        flex: 1;
    }

    .dropdowns-group-0 {
        justify-content: end;
    }

    .dropdowns-group-2 {
        justify-content: start;
    }
    /* Middle Group Max Content */
    .dropdowns-group-1 {
        width: max-content;
    }

    /* Container */
    .dropdown-container {
        width: max-content;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Dropdown */
    .design-options-dropdown {
        margin-left: 14px;
        position: relative;
        width: max-content;
        height: 30px;
        border-bottom: solid 3px transparent;
        user-select: none;
        overflow: initial;
    }

        .design-options-dropdown:hover {
            border-bottom-color: #adc43b;
        }

    /* Selected */
    .options-dropdown-selected {
        position: relative;
        width: 100%;
        max-width: 300px;
        height: 100%;
        padding: 0 2px 7px 2px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        column-gap: 8px;
        cursor: default;
    }
        /* IF has more than 1 option to select */
        .options-dropdown-selected.selectable {
            cursor: pointer;
        }
    /* Tooltip */
    .dropdown-tooltip {
        overflow: visible;
    }
        .dropdown-tooltip > div {
            position: relative;
            width: max-content;
            min-width: 100px;
            padding: 5px 14px 4px 14px;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
            background-color: #262626;
            opacity: 1 !important;
            overflow: visible;
        }
        .dropdown-tooltip-triangle {
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translate(-50%, 100%);
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 5px solid #262626;
        }

    /* Text */
    .options-dropdown-selected > div:first-child {
        margin-top: 6px;
        max-width: 260px;
        text-wrap: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    /* Icon */
    .options-dropdown-icon {
        width: 10px;
        height: 5px;
        margin-top: 5px;
    }

    /* IF there is more than 1 dropdown in this section, make smaller */
    .options-dropdown-selected.small {
        max-width: 150px;
    }

        .options-dropdown-selected.small > div:first-child {
            max-width: 120px;
        }

    /* Expanded */
    .dropdown-options-container {
        position: absolute;
        bottom: -20px;
        z-index: 999;
        width: max-content;
        overflow: hidden;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.12);
        background-color: white;
        opacity: 0;
        transform: scaleY(0) translateY(100%);
        transition: all 0.3s ease-out;
        -webkit-transition: all 0.3s ease-out;
        -moz-transition: all 0.3s ease-out;
    }

        .dropdown-options-container.dropdown-expanded {
            transform: scaleY(1) translateY(100%);
            opacity: 1;
        }

    /* Option */
    .design-option {
        padding: 7px 16px 7px 10px;
        width: 100%;
    }

        .design-option:hover {
            background-color: #f2f2f2;
            text-shadow: 0px 0px 1px black;
        }

    .design-option-selected {
        text-shadow: 0px 0px 1px black;
        color: #8bc34a
    }

    /* Divider */
    .options-divider {
        margin-left: 14px;
        margin-top: -5px;
        width: 0;
        height: 15px;
        border-left: solid 2px #d3d3d3;
    }

    .dropdowns-group-2 > .dropdown-container:last-child > .options-divider {
        display: none;
    }

</style>