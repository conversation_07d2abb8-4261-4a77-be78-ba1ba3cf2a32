(function () {
    'use strict';
    angular
        .module('app')
        .component('genericFileUpload', {
            bindings: {
                fileObject: '<',
                acceptArray: '<',
                propName: '@',
                formName: '@',
                isLocked: '<',
                forceEdit: '<',
                isRequired: '<',
                requiredMessage: '<',
                category: '<',
                classification: '<',
                onChange: '&',  // Will supply a FileDto array.
                label: '@',
                externalClickHandler: '&',
                accept: '<',    // string, use to limit allowed file types
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/file-upload/generic-file-upload.html',
            controller: FileUpload,
            controllerAs: 'vm'
        });

    FileUpload.$inject = ['Upload', 'fileservice'];

    function FileUpload(Upload, fileservice) {

        var vm = this;

        vm.accept = vm.accept ?? "";

        if (vm.formName == null || vm.formName == "")
            vm.formName = "FileUploadForm";

        if (vm.requiredMessage == null)
            vm.requiredMessage = "Field is required.";

        var acceptExtensions = vm.accept.split(',').map(x => x.split('.').pop().toLowerCase());
        if (vm.accept != null && vm.accept != "") {
          if (acceptExtensions.length == 1) {
            vm.uploadFormatErrorMessage = `Only ${acceptExtensions[0]} files are allowed.`;
          } else {
            vm.uploadFormatErrorMessage = `Only these formats are accepted: ${vm.accept.replaceAll('.', ' ')}.`;
          }
        }

        vm.uploadIsInProgress = uploadIsInProgress;
        vm.uploadFile = uploadFile;
        vm.downloadFile = downloadFile;
        vm.downloadFileForceDialog = downloadFileForceDialog;
        vm.setToNull = setToNull;
        vm.fileChanged = function (data) {
            if (vm.onChange) {
                setTimeout(function () {
                    vm.onChange({data});
                }, 50);
            }
        }

        function setToNull(context, field) {
            if (context != undefined && context != null && context[field] != undefined) {
                context[field] = null;
                if (context[field + "Id"] != undefined) {
                    context[field + "Id"] = null;
                }
            }
            vm.fileChanged(null);
        }

        function downloadFile(f) { fileservice.downloadFile(f); }
        function downloadFileForceDialog(f) { fileservice.downloadFileForceDialog(f); }

        function uploadFile($file, target, field) {

            vm.showUploadFormatError = false;
            let fileExtension = $file.name.split('.').pop();

            if (!acceptExtensions.includes(fileExtension.toLowerCase())) {

              vm.showUploadFormatError = true;

            } else {

              console.log("uploadFile, target is: ", target);
              console.log("FileObject is: ", vm.fileObject);
              target[field + "UploadProgress"] = null;

              let url = "../api/File/UploadFile?"; // Generic file upload interface...

              if(vm.category)
                  url += `&category=${vm.category}`

              if(vm.classification)
                  url += `&classification=${vm.classification}`

              var promise = Upload.upload({
                  url: url, // webapi url
                  method: "POST",
                  file: $file
              });

              return promise
                .progress(function (evt) {
                  target[field + "UploadProgress"] = 100 * (evt.loaded / evt.total);
              }).success(function (data, status, headers, config) {

                  // data WILL BE AN ARRAY.
                  if(vm.acceptArray !== true)
                      data = data[0];

                  target[field] = data;
                  target[field + "UploadProgress"] = null;

                  console.log("Calling fileChanged...", data);
                  vm.fileChanged(data);

                  if (vm.externalClickHandler != null)
                      vm.externalClickHandler();

                  return data;

              }).error(function (data, status, headers, config) {
                  target[field + "UploadProgress"] = null;
                  vm.fileChanged();
                  return null;
              });
            }

        }

        function uploadIsInProgress() {
            return vm.fileObject[vm.propName + 'UploadProgress'] != null && 
                   vm.fileObject[vm.propName + 'UploadProgress'] !== 100;
        }
    }
})();