<md-dialog ng-controller="ColourOverrideModalCtrl as vm">
  <ng-form name="openingsoverridemodalform" novalidate ng-submit="vm.save()">
    <md-toolbar>
      <div class="md-toolbar-tools">
        <h2 style="text-transform: capitalize">{{vm.prefix}} Solar Absorptance</h2>
        <span flex></span>
        <md-button class="md-icon-button" ng-click="vm.cancel()">
          <i class="material-icons">clear</i>
        </md-button>
      </div>
    </md-toolbar>

    <md-dialog-content layout="column" layout-margin layout-padding>

      <table class="table" style="margin: 0;">
        <tbody>
          <!-- Colour Row -->
          <tr>

            <td>
              {{vm.prefix}} Colour
            </td>

            <!-- Overridden value with reset button-->
            <td>
              <div style="display: grid; grid-template-columns: 8fr 2fr;
                          align-content: center; align-items: center;">
                <md-autocomplete flex="100"
                                 id="OverrideColour"
                                 md-input-name="template"
                                 md-selected-item="vm[vm.colourProperty]"
                                 md-selected-item-change="vm.applyColour(colour)"
                                 md-search-text="colourSearchText"
                                 md-items="colour in vm.colourListSafe() | filter: { title: colourSearchText }"
                                 md-item-text="colour.title"
                                 md-min-length="0"
                                 ng-required="false"
                                 ng-disabled="vm.disabled"
                                 class="lightweight input-black"
                                 md-select-on-match="true"
                                 md-require-match="true"
                                 placeholder="Search colours...">
                  <md-item-template>
                    <span md-highlight-text="colourSearchText"
                          md-highlight-flags="^i">{{colour.title}}</span>{{colour.manufacturer != null ? ' (' + colour.manufacturer.description + ')' : ""}}
                  </md-item-template>
                </md-autocomplete>
              </div>

            </td>
          </tr>

          <!-- Solar Absorptance Row -->
          <tr>

            <td>
              {{vm.prefix}} Solar Absorptance
            </td>

            <!-- Overridden value with reset button-->
            <td>
              <div style="display: grid; grid-template-columns: 8fr 2fr;
                          align-content: center; align-items: center;">
                <md-input-container class="vertically-condensed kindly-remove-error-spacer">
                  <input formatted-number
                         decimals="2"
                         id="override-solar-absorptance-value-focus-node"
                         class="compliance-row-table-input-m"
                         ng-disabled="vm.disabled"
                         ng-required="true"
                         ng-model="vm[vm.solarAbsorptanceProperty]"/>
                </md-input-container>
              </div>

            </td>
          </tr>

        </tbody>
      </table>

    </md-dialog-content>

    <md-dialog-actions layout="row">
      <md-button class="md-raised md-primary"
                 ng-click="vm.save()"
                 ng-disabled="vm.disabled">
        Save
      </md-button>
      <md-button class="md-raised" ng-click="vm.cancel()"> Cancel </md-button>
    </md-dialog-actions>

  </ng-form>

</md-dialog>

<style>
  md-dialog-content * {
    font-size: 16px;
  }
  md-input-container label:not(.md-container-ignore) {
    left: auto;
  }
</style>
