/*
* Menu Parent Controller
*
* Provide a Page for Parent Menu items.
* Provide a list of child menu options.
*
*/
(function () {
    'use strict';
    var controllerId = 'MenuParentCtrl';
    angular.module('app').controller(controllerId, ['common', '$state', 'routes', menuparent]);

    function menuparent(common, $state, routes) {
        var getLogFn = common.logger.getLogFn;
        var log = getLogFn(controllerId);

        var vm = this;
        vm.title = $state.current.data.title;
        vm.navRoutes = []; // The list of menu options for this parent page.
        activate();

        function activate() {
            var promises = [];
            common.activateController(promises, controllerId)
                .then(function () { getNavRoutes(); });
        }

        // Build the list of navigation routes for the parent menu.
        // (The UI will take care of only displaying items the user has access to)
        function getNavRoutes() {
            vm.navRoutes = routes.filter(function (r) {
                return r.stateConfig.data && r.stateConfig.data.nav && r.stateConfig.data.menuParent == $state.current.name && r.stateConfig.data.excludeFromMenu != true;
            }).sort(function (r1, r2) {
                return Number(r1.stateConfig.data.nav) - Number(r2.stateConfig.data.nav);
            });

        }

    }
})();