<form name="batchjobqueueform" class="main-content-wrapper" novalidate data-ng-controller='BatchjobqueueUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
            <!--Left Side-->
            <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}" >
                <md-card>
                    <md-card-header>
                        Batch Job
                    </md-card-header>
                    <md-card-content>

<!-- ******** Request Name ******** -->
                        <md-input-container class="md-block" flex-gt-sm>
                            <label>Request Name</label>
                            <input type="text" name="requestName" 
                                    ng-model="vm.batchjobqueue.requestName" 
                                    md-autofocus
                                    md-maxlength="60"
                                    required
                                    />
                            <div ng-messages="batchjobqueueform.requestName.$error">
                                <div ng-message="required">Request Name is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 60.</div>
                            </div>
                        </md-input-container>

<!-- ******** Request Data ******** -->
                        <md-input-container class="md-block" flex-gt-sm>
                            <label>Request Data</label>
                            <input type="text" name="requestData" 
                                    ng-model="vm.batchjobqueue.requestData" 

                                    md-maxlength="2000"
                                    required
                                    />
                            <div ng-messages="batchjobqueueform.requestData.$error">
                                <div ng-message="required">Request Data is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 2000.</div>
                            </div>
                        </md-input-container>

<!-- ******** Requested Date Time  ******** -->
                        <md-input-container class="md-block" flex-gt-sm ng-if="vm.newRecord==false">
                            <label>Requested Date Time</label>
                            <md-datepicker ng-model="vm.batchjobqueue.requestedDateTimeUtc" 
                                           name="requestedDateTimeUtc"
                                           required

                                           md-placeholder="Enter date">
                            </md-datepicker>
                            <div class="validation-messages" ng-messages="batchjobqueueform.requestedDateTimeUtc.$error">
                                <div ng-message="valid">The entered value is not a date!</div>
                                <div ng-message="required">This date is required!</div>
                                <div ng-message="mindate">Date is too early!</div>
                                <div ng-message="maxdate">Date is too late!</div>
                                <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                            </div>
                        </md-input-container>

<!-- ******** Submitted ******** -->
                        <md-input-container class="md-block" flex-gt-sm ng-if="1==2">
                            <md-checkbox name="submitted"
                                    ng-model="vm.batchjobqueue.submitted"  >
                                Submitted
                            </md-checkbox>
                            <div ng-messages="batchjobqueueform.submitted.$error">
                                <div ng-message="required">Submitted is required.</div>
                            </div>
                        </md-input-container>

<!-- ******** Due Date Time  ******** -->
                        <md-input-container class="md-block" flex-gt-sm >
                            <label>Due Date Time</label>
                            <md-datepicker ng-model="vm.batchjobqueue.dueDateTimeUtc" 
                                           name="dueDateTimeUtc"
                                           required

                                           md-placeholder="Enter date">
                            </md-datepicker>
                            <div class="validation-messages" ng-messages="batchjobqueueform.dueDateTimeUtc.$error">
                                <div ng-message="valid">The entered value is not a date!</div>
                                <div ng-message="required">This date is required!</div>
                                <div ng-message="mindate">Date is too early!</div>
                                <div ng-message="maxdate">Date is too late!</div>
                                <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                            </div>
                        </md-input-container>

<!-- ******** Run Frequency ******** -->
                        <md-input-container class="md-block" flex-gt-sm>
                            <label>Run Frequency</label>
                            <md-select name="runFrequencyCode"
                                       ng-model="vm.batchjobqueue.runFrequencyCode">
                                <md-option ng-value><small>none</small></md-option>
                                <md-option ng-value="item.frequencyCode" 
                                           ng-repeat="item in vm.frequencyList track by item.frequencyCode">
                                    {{ item.description }}
                                </md-option>
                            </md-select>
                        </md-input-container>

<!-- ******** Run At Time ******** -->
                        <md-input-container class="md-block" flex-gt-sm >
                            <label>Run At Time</label>
                            <input type="text" name="runAtTime" 
                                    ng-model="vm.batchjobqueue.runAtTime"
                                    />
                            <div ng-messages="batchjobqueueform.runAtTime.$error">
                            </div>
                        </md-input-container>

<!-- ******** Last Run Date Time  ******** -->
                        <md-input-container class="md-block" ng-if="vm.newRecord==false">
                            <label>Last Run Date Time </label>
                            <md-datepicker ng-model="vm.batchjobqueue.lastRunDateTimeUtc" 
                                           name="lastRunDateTimeUtc"
                                           ng-disabled="true">
                            </md-datepicker>
                            <div class="validation-messages" ng-messages="batchjobqueueform.lastRunDateTimeUtc.$error">
                                <div ng-message="valid">The entered value is not a date!</div>
                                <div ng-message="required">This date is required!</div>
                                <div ng-message="mindate">Date is too early!</div>
                                <div ng-message="maxdate">Date is too late!</div>
                                <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                            </div>
                        </md-input-container>

                        <div flex="100" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.batchjobqueue"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>

<!-- ******** Right Side ******** -->
            <md-card ng-if="vm.newRecord==false" flex-gt-sm="50">
            </md-card>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="batchjobqueueform.$invalid" ng-show="vm.batchjobqueue.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.batchjobqueue.recId>0 && vm.batchjobqueue.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.batchjobqueue.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
