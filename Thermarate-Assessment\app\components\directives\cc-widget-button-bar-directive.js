﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('ccWidgetButtonBar', function () {
        //Usage:
        //<div data-cc-widget-button-bar data-is-modal="vm.isModal">buttons here</div>
        var directive = {
            link: link,
            scope: {
                'isModal': '='
            },
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-footer');
            }
            else {
                attrs.$set('class', 'widget-button-bar');
            }
        }
    });
})();