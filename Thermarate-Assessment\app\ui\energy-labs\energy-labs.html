<div data-ng-controller="EnergyLabsHomeController as vm"
     style="margin-top: -20px;"
     class="el-poppins">

  <div class="el-heading-banner"
       style="height: 350px; padding-top: 30px;">
    <h1 style="margin: 0;">Thermarate EnergyLab</h1>
    <h3 style="margin: 0;">To get started, please select one of the options below.</h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>
  </div>
  <hr class="el-title-divider"/>

  <div class="navigation-text" ng-show="vm.project!=null">
    <div ng-click="vm.backToProjects()" class="clickable">Projects</div>
    <div>></div>
    <b>{{vm.project.projectName}} [{{vm.project.clientName}}]</b>
  </div>

  <div class="central-grid-container tools-picker-grid" ng-show="vm.project!=null">
    <div class="el-tools-grid"
         style="z-index: 2;">

      <div class="el-card el-card-body clickable el-card-relative"
           style="align-content: space-between;"
           ui-sref="energy-labs-identify({projectId: vm.project.projectId})">
        <img ng-if="vm.project.energyLabsSettings.toolIdentifyEnabled == false" class="lock-icon" src="content/images/energy-labs/el-lock-icon.png" />
        <img class="el-mode-icon" src="content/images/energy-labs/el-identify-icon.svg" alt="Arrow pointing right">
        <h2 class="el-tool-title">Identify</h2>
        <div class="el-tool-description" style="text-align: center;">
          Identify the best performing home designs for a specific block.
        </div>
        <div class="el-tool-launch">
          Launch <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </div>
      </div>

      <div class="el-card el-card-body clickable el-card-relative"
           style="align-content: space-between;"
           ui-sref="energy-labs-configure({projectId: vm.project.projectId})">
        <img ng-if="vm.project.energyLabsSettings.toolCOnfigureEnabled == false" class="lock-icon" src="content/images/energy-labs/el-lock-icon.png" />
        <img class="el-mode-icon" style="opacity:0.6; width:80px;" src="content/images/energy-labs/el-configure-icon.png" alt="Configure Icon (Pencil)">
        <h2 class="el-tool-title">Configure</h2>
        <div class="el-tool-description" style="text-align: center;">
          Configure a specific home design and see how it affects performance.
        </div>
        <div class="el-tool-launch">
          Launch <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </div>
      </div>

      <div class="el-card el-card-body clickable el-card-relative"
           style="align-content: space-between;"
           ui-sref="energy-labs-optimise({projectId: vm.project.projectId})">
        <img ng-if="vm.project.energyLabsSettings.toolOptimiseEnabled == false" class="lock-icon" src="content/images/energy-labs/el-lock-icon.png" />
        <img class="el-mode-icon" style="opacity:0.6; width:80px;" src="content/images/energy-labs/el-optimise-icon.png" alt="Optimise Icon (Pencil)">
        <h2 class="el-tool-title">Optimise</h2>
        <div class="el-tool-description" style="text-align: center;">
          Determine the most cost effective compliance solution.
        </div>
        <div class="el-tool-launch">
          Launch <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </div>
      </div>

      <div class="el-card el-card-body clickable el-card-relative"
           style="align-content: space-between;"
           ui-sref="energy-labs-woh({projectId: vm.project.projectId})">
        <img ng-if="vm.project.energyLabsSettings.toolWholeOfHomeEnabled == false" class="lock-icon" src="content/images/energy-labs/el-lock-icon.png" />
        <img class="el-mode-icon" style="opacity:0.6" src="content/images/energy-labs/el-house-icon.png" alt="Whole-of-Home Icon (House)">
        <h2 class="el-tool-title">Whole-of-Home</h2>
        <div class="el-tool-description" style="text-align: center;">
          Measure the energy usage of the home's services and appliances.
        </div>
        <div class="el-tool-launch">
          Launch <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </div>
      </div>

    </div>
  </div>

</div>

<style>

    .tools-picker-grid {
        margin-top: calc(-200px + 20vh);
    }

    .el-card.el-card-relative {
        position: relative;
    }

    .lock-icon {
        position: absolute;
        top: 15px;
        right: 15px;
        opacity: 0.195;
        width: 35px;
        height: 35px;
        object-fit: contain;
    }

    .el-tool-title {
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        margin: 0;
        color: var(--color-text-dark);
    }

    .el-tool-description {
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        max-width: 370px;
        color: var(--color-text-dark);
    }

    .el-tool-launch {
        width: 100%;
        height: 54px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-content: center;
        align-items: center;
        column-gap: 1rem;
        color: white;
        font-size: 21px;
        margin-top: 5rem;
        background-image: linear-gradient(to left, #C1EB85, #85BA38);
    }

</style>
