<section id="businessunit-list-view" class="main-content-wrapper" data-ng-controller="BusinessunitListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Businessunit"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.businessunitList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="businessunitList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="name" class="can-sort text-left">Name</th>
                        <th st-sort="businessUnitTypeDescription" class="can-sort text-left">Business Unit Type</th>
                        <th st-sort="managerFullName" class="can-sort text-left">Manager</th>
                        <th st-sort="businessUnitPrefix" class="can-sort text-left">Business Unit Prefix</th>
                        <th st-sort="emailAddress" class="can-sort text-left">Email Address</th>
                        <th st-sort="phone" class="can-sort text-left">Phone</th>
                        <th st-sort="mobile" class="can-sort text-left">Mobile</th>
                        <th st-sort="fax" class="can-sort text-left">Fax</th>
                        <th st-sort="addressLine1" class="can-sort text-left">Address Line1</th>
                        <th st-sort="addressLine2" class="can-sort text-left">Address Line2</th>
                        <th st-sort="addressSuburb" class="can-sort text-left">Address Suburb</th>
                        <th st-sort="addressStateCodeName" class="can-sort text-left">Address State</th>
                        <th st-sort="addressPostCode" class="can-sort text-left">Address Post Code</th>
                        <th st-sort="addressFormatted" class="can-sort text-left">Address Formatted</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.businessunitList" class="list-row clickable">
                        <td data-title="Name" ng-click="vm.goToBusinessUnit(row.businessUnitId)">
                            <div class="text-left" style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{row.name}}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Business Unit Type" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.businessUnitTypeDescription }}</td>
                        <td data-title="Manager" class="text-right" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.managerUserId | number }}</td>
                        <td data-title="Business Unit Prefix" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.businessUnitPrefix }}</td>
                        <td data-title="Email Address" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.emailAddress }}</td>
                        <td data-title="Phone" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.phone }}</td>
                        <td data-title="Mobile" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.mobile }}</td>
                        <td data-title="Fax" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.fax }}</td>
                        <td data-title="Address Line1" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressLine1 }}</td>
                        <td data-title="Address Line2" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressLine2 }}</td>
                        <td data-title="Address Suburb" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressSuburb }}</td>
                        <td data-title="Address State" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressStateCodeName }}</td>
                        <td data-title="Address Post Code" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressPostCode }}</td>
                        <td data-title="Address Formatted" class="text-left" ng-click="vm.goToBusinessUnit(row.businessUnitId)">{{::row.addressFormatted }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="15" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>

    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>