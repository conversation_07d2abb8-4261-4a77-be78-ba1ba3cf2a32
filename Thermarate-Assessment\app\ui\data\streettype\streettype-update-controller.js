(function () {
    // The streetTypeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'streetTypeUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', 'streettypeservice', streetTypeUpdateController]);
    function streetTypeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, streettypeservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Street Type';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.streetTypeCode = null;
        vm.streetType = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Street Type";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if (vm.newRecord == false) {
                vm.streetTypeCode = $scope.streetTypeCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.streetTypeCode = $stateParams.streetTypeCode;
        }

        // Get data for object to display on page
        var streetTypeCodePromise = null;
        if (vm.streetTypeCode != null) {
            streetTypeCodePromise = streettypeservice.getStreetType(vm.streetTypeCode)
                .then(function (data) {
                    if (data != null) {
                        vm.streetType = data;
                    }
                    vm.isBusy = false;
                });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for (var i = 0, len = eventListenerList; i < len; i++) {
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    streetTypeCode
                    $state.go("streetType-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if (vm.newRecord == true) {
                streettypeservice.createStreetType(vm.streetType).then(function (data) {
                    vm.streetType = data;
                    vm.streetTypeCode = vm.streetType.streetTypeCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                streettypeservice.updateStreetType(vm.streetType).then(function (data) {
                    if (data != null) {
                        vm.streetType = data;
                        vm.streetTypeCode = vm.streetType.streetTypeCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            streettypeservice.deleteStreetType(vm.streetTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();