<div class="el-poppins el-popup-grid">
  <h4 class="el-popup-heading">Heating Load (MJ/m<sup>2</sup>)</h4>
  <table>
    <tbody>
    <tr>
      <td style="min-width: 100px;">Target (max)</td>
      <td style="font-weight: bold;">{{vm.determineTarget()}}</td>
    </tr>
    <tr>
      <td>Calculated</td>
      <td style="font-weight: bold;"><span ng-style="{ color: vm.determineCalcResultColour() }">{{vm.source.heatingLoad.toFixed(1)}}</span></td>
    </tr>
    <tr>
      <td>Regulations</td>
      <td style="font-weight: bold;">
        {{vm.source.assessmentMethod == "Performance Solution (Energy Load Limits)" ? "NCC" : vm.source.stateCode === "NSW" ? "BASIX" : "NCC"}}
      </td>
    </tr>
    </tbody>
  </table>
</div>
