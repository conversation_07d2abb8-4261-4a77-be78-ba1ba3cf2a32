(function () {
	'use strict';
	angular
		.module('app')
		.component('searchAddressSuburbPostcode', {
			bindings: {
				assessment: '<',
				project: '<',
				address: '<',	// Optionally pass an object to use as the basis for the address arg in the callback
				addressChanged: '&',
				onManualAddressSelected: '&',
				required: '<',
				startingText: '<',
			},
			templateUrl: 'app/ui/assessment/search-address-suburb-postcode/search-address-suburb-postcode.html',
			controller: SearchAddress,
			controllerAs: 'vm'
		});

	SearchAddress.$inject = ['addressservice', 'slipaddress', 'nathersclimatezoneservice', 'nccclimatezonedataservice', 'common'];

	function SearchAddress(addressservice, slipaddress, nathersclimatezoneservice, nccclimatezonedataservice, common) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

		var vm = this;

        vm.showTooltip = false;

		vm.searchAddressText = vm.startingText || '';
		vm.selectedSearchAddress = '';

		vm.searchAddressItemChange = searchAddressItemChange;
		vm.switchToManualInput = switchToManualInput;

		var useAddress = !(typeof vm.address == 'undefined');
		var useAssessment = !(typeof vm.assessment == 'undefined');
		var useProject = !(typeof vm.project == 'undefined');

        var retrievedAsSuburb = false;
        var retrievedAsAddress = false;

        // ----------- //
        // - HANDLES - //
        // ----------- //

        // Search
		vm.attemptSearch = function (search) {
			if (!search) {
				return Promise.resolve([]);
			}
			if (search.length < 3) {
				return Promise.resolve([]);
			}

            retrievedAsSuburb = false;
            retrievedAsAddress = false;

            // IF no numbers, search Suburb
            if (!common.textHasNumbers(search)) {
                retrievedAsSuburb = true;
			    return addressservice.querySuburb(search).then(results => {
                    for (let item of results) {
                        item.address = `${item.name}, ${item.stateCode}, ${item.postcode}`;
                    }
                    return results;
                });
            }
            // ELSE IF is only 3 or 4 numbers, search Postcode
            else if (common.isNumber(search) && search.length == 3 || search.length == 4) {
                retrievedAsSuburb = true;
                return addressservice.querySuburbByPostcode(search).then(results => {
                    for (let item of results) {
                        item.address = `${item.name}, ${item.stateCode}, ${item.postcode}`;
                    }
                    return results;
                });
            }
            // ELSE do normal Address search
            else {
                retrievedAsAddress = true;
			    // If the user has initiated a search by lot, we want to wait
			    // until we get a bit more info before hitting the slip api...
                if (search.substring(0, 3).toLowerCase() == "lot") {
				    // Wait until we have an actual number and at least 3 letters for the street...
				    var split = search.split(' ');
				    if (split.length >= 3) {

					    var number = split[1];
					    var street = split[2];

					    if (street.length > 2) {
						    return slipaddress.queryPredictive(number, street);
					    }
                    }
				    return Promise.resolve([]);
			    } else {
				    // For street/house number searches, first try the local database
				    // Only use Geoscape if no results are found
				    return slipaddress.queryByStreetNumber(search)
					    .then(results => {
						    if (results && results.length > 0) {
							    return results; // Return results from local database
						    } else {
							    // No results found in local database, try Geoscape
							    return addressservice.queryGeoscape(search);
						    }
					    });
                }
            }
		}

        // Item change
		function searchAddressItemChange(item) {

			if (useAddress) {
				vm.address = {};
			}

            // IF suburb
            if (item && retrievedAsSuburb) {
                vm.assessment.assessmentProjectDetail.latitude = item.latitude;
                vm.assessment.assessmentProjectDetail.longitude = item.longitude;
                vm.assessment.assessmentProjectDetail.suburb = item.name;
                vm.assessment.assessmentProjectDetail.stateCode = item.stateCode;
				vm.addressChanged({ providedData: vm.assessment });
            }
            // ELSE IF address
            else if (retrievedAsAddress) {

			    if (item && item.id && useAssessment) {

				    // If our query was obtained via SLIP want to use our PSMA address service to obtain the FULL data payload (Behind the scenes it
				    // grabs additional info not available within the slip dataset).
				    if (item.obtainedVia == "SLIP") {

					    slipaddress.getById(item.id).then(data => {

						    // If we have not specifically passed an address object to populate, it means
						    // we are filling in an assessment
						    if (useAssessment) {
							    addressservice.insertAddressDataIntoObject(data, vm.assessment, "ASSESSMENT");
								vm.addressChanged({ providedData: vm.assessment });
                            }

						    if (useProject) {
							    vm.project.suburbCode = data.suburbCode;
							    vm.project.suburbName = data.suburb;
							    vm.project.stateCode = data.stateCode;
							    vm.project.lga = null;
							    vm.project.natHERSClimateZone = data.natHERSClimateZone;
							    vm.project.latitude = data.latitude;
							    vm.project.longitude = data.longitude;
						    }

						    if (useAddress) {
							    addressservice.insertAddressDataIntoObject(data, vm.address, "ADDRESS");
								vm.addressChanged({ providedData: vm.address });
                            }
					    });

				    } else {

					    // If the current search was obtained via PSMA
					    // then we want to use the addressservice to obtain the data and THEN use the slipaddress service
					    // to obtain the LOT and GEOMETRY data!
					    addressservice.getDetails(item.address).then(function (data) {

						    addressservice.insertAddressDataIntoObject(data, vm.assessment, "ASSESSMENT");

						    slipaddress.getRemainder(data).then((remainder) => {
							    // Searches outside WA will return null here, so we don't want to
							    // use them (or in any other instance where we could not get the remainder).
							    if (remainder != null) {
								    // Combine our data.
								    let fullPayload = common.nullAwareMerge(data, remainder);
								    if (useAssessment) {
									    addressservice.insertAddressDataIntoObject(fullPayload, vm.assessment, "ASSESSMENT");
								    }
								    if (useAddress) {
									    addressservice.insertAddressDataIntoObject(fullPayload, vm.address, "ADDRESS");
                                    }
							    } else {
								    // Lot number would have already been filled or nulled for prior call to insertAddressData...
								    if (useAssessment) {
									    vm.assessment.assessmentProjectDetail.boundaryGeometry = data.boundaryGeometry;
								    }
								    if (useAddress) {
									    vm.address.boundaryGeometry = data.boundaryGeometry;
                                    }
                                }

							    if (useAssessment) {
								    vm.addressChanged({ providedData: vm.assessment });
							    }
							    if (useAddress) {
								    vm.addressChanged({ providedData: vm.address });
							    }
						    });
					    });
                    }

                } else if (item && item.id && useProject) {

				    // If our query was obtained via SLIP want to use our PSMA address service to obtain the FULL data payload (Behind the scenes it
				    // grabs additional info not available within the slip dataset).
                    if (item.obtainedVia == "SLIP") {

                        slipaddress.getById(item.id).then(data => {
                            vm.project.natHERSClimateZoneCode = data.natHERSClimateZoneCode;
                            nathersclimatezoneservice.getNatHERSClimateZone(data.natHERSClimateZoneCode).then(climateZone => vm.project.natHERSClimateZoneDescription = climateZone.description);

                            addressservice.getSuburbFromName(data.suburb).then(suburb => {
                                vm.project.suburbCode = suburb.suburbCode;
                            });

                            vm.project.suburbName = data.suburb;
                            vm.project.stateCode = data.state;
                            vm.project.latitude = data.latitude;
                            vm.project.longitude = data.longitude;

                            if (data.latitude && data.longitude) {
                                addressservice.getLocalGovernment(data.latitude, data.longitude).then(localGov => {
                                    if (localGov) {
                                        vm.project.lga = localGov.name;
                                        vm.project.lga = localGov.nameShort;
                                    }
                                });
                                nccclimatezonedataservice.getClimateZone(data.latitude, data.longitude).then(data => {
                                    vm.project.nccClimateZoneCode = "NCC" + data;
                                });
                            }

                            vm.addressChanged();
                        });

                    } else {

                        addressservice.getDetails(item.address).then(data => {
                            vm.project.natHERSClimateZoneCode = data.natHERSClimateZoneCode;
                            nathersclimatezoneservice.getNatHERSClimateZone(data.natHERSClimateZoneCode).then(climateZone => vm.project.natHERSClimateZoneDescription = climateZone.description);

                            addressservice.getSuburbFromName(data.suburb).then(suburb => {
                                vm.project.suburbCode = suburb.suburbCode;
                            });

                            vm.project.suburbName = data.suburb;
                            vm.project.stateCode = data.state;
                            vm.project.latitude = data.latitude;
                            vm.project.longitude = data.longitude;

                            if (data.latitude && data.longitude) {
                                addressservice.getLocalGovernment(data.latitude, data.longitude).then(localGov => {
                                    if (localGov) {
                                        vm.project.lga = localGov.name;
                                        vm.project.lga = localGov.nameShort;
                                    }
                                });
                                nccclimatezonedataservice.getClimateZone(data.latitude, data.longitude).then(data => {
                                    vm.project.nccClimateZoneCode = "NCC" + data;
                                });
                            }

                            vm.addressChanged();
                        });

                    }

                } else {
				    vm.address = null;
			    }

            }
		}

        // Trigger switch to manual input
		function switchToManualInput() {
			vm.assessment.assessmentProjectDetail.isManual = true;
			vm.onManualAddressSelected();
		}

	}
})();