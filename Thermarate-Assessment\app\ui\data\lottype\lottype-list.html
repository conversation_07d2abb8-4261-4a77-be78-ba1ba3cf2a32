<section id="lottype-list-view" class="main-content-wrapper" data-ng-controller="LottypeListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Lottype"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.lottypeList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="lottypeList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="lotTypeCode" class="can-sort text-center">Code</th><!--planTypeCode-->
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.lottypeList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="lottype-updateform({ lotTypeCode: row.lotTypeCode})">Select</md-button>  </td><!--planTypeCode-->
                        <td data-title="Description" class="text-left">{{::row.description }}</td>
                        <td data-title="Code" class="text-center">{{::row.lotTypeCode }}</td><!--planTypeCode-->
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
