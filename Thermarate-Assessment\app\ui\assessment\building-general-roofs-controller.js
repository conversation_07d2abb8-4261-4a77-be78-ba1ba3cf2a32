(function () {

  'use strict';

  // The name of the component in camelCase.
  // This is what you will use in the widget tree (but converted to <snake-case/>)
  const COMPONENT_NAME = "buildingGeneralRoofs";

  // The URL of the HTML template this controller will control.
  const HTML_TEMPLATE_URL = "app/ui/assessment/building-general-roofs.html";

  angular
    .module('app')
    .component(COMPONENT_NAME, {

      // PARAMETERS THAT CAN BE PASSED TO COMPONENT
      bindings: {
        source: '<', // The building which is the source of this data.
        disabled: '<',
        sourceType: '<',
        complianceOption: '<',
      },

      templateUrl: HTML_TEMPLATE_URL,
      controller: Controller,

      controllerAs: 'vm'
    });

  // Inject all services required here (and make sure to add to the controller params too.)
  Controller.$inject = ['$scope', '$rootScope', 'common','uuid4', '$q', '$mdDialog', 'zonetypeservice'];

  function Controller($scope, $rootScope, common, uuid4, $q, $mdDialog, zonetypeservice) {

    let vm = this;

    initialize();

    vm.zoneTypeList = [];

    vm.sortedTableFunc = sortedTableFunc;
    vm.addRoof = addRoof;
    vm.cloneRoof = cloneRoof;
    vm.clearRoof = clearRoof;
    vm.removeRoof = removeRoof;
    vm.removeRoofs = removeRoofs;
    vm.renumberRoofs = renumberRoofs;
    vm.toggleBulkEditAll = toggleBulkEditAll;
    vm.showBulkEdit = showBulkEdit;
    vm.noneSelected = noneSelected;
    vm.disabledEx = disabledEx;
    vm.sortBy = sortBy;

    function initialize() {

      if(vm.source.roofs == null)
        vm.source.roofs = [];

      zonetypeservice.getList().then((data) => {
        vm.zoneTypeList = data.data.filter(x => x.availableFor === 'all' || x.availableFor === 'roofs')
      });
    }

    function sortedTableFunc() {

      return vm.sortedRoofs != null
        ? vm.sortedRoofs
        : vm.source.roofs;
    }

    /** roofList must match a RoofActivityCode without the preceeding "ZA" */
    function sortBy(roofList, column) {

      if(vm[roofList + "SortInfo"] == null)
        vm[roofList + "SortInfo"]  = new SortInfo();

      const matchingRoofs = vm.source.roofs;

      common.setSort(column, vm[roofList + "SortInfo"]);
      // TODO: If we need to sort multiple roof types (like zones), then
      // this will need to be updated. Refer to zone-list-controller.
      vm.sortedRoofs = common.applySort(matchingRoofs, vm[roofList + "SortInfo"]);

      // console.log("vm.sortedRoofs: ", vm.sortedRoofs);
      // console.log("vm.RoofSortInfo: ", vm.RoofSortInfo);
    }

    vm.calcRoofAreaPitch = function (item) {
        if ((item.roofPitch == null && item.roofPitch != 0) || (item.roofArea == null && item.roofArea != 0)) {
            return "";
        } else {
            let roofPitchRadians = item.roofPitch * (Math.PI/180);
            let result = item.roofArea / Math.cos(roofPitchRadians);
            return result.toFixed(2);
        }
    }

    function renumberRoofs() {

      if (vm.sortCol == null && vm.sortDir == null) {
        for (let i = 0; i < vm.source.roofs.length; i++) {
          vm.source.roofs[i].sortOrder = i;
        }
      }
    }

    function addRoof(roofs, leadChar) {

      if (vm.source.roofs == null) {
        vm.source.roofs = [];
      }

      vm.source.roofs.push({
        createdOn: new Date().toUTCString(),
        zoneId: uuid4.generate(),
        linkId: uuid4.generate(),
        floorArea: null,
        zoneNumber: determineNextFreeRoofNumber(leadChar),
        zoneNumberSource: "AUTO",
      });

      renumberRoofs();

      vm.sortedRoofs = common.applySort(vm.source.roofs, vm.RoofSortInfo);
    }

    function cloneRoof(row, leadChar) {

      if (row == null)
        return;

      let index = vm.source.roofs.findIndex(x => x === row); // Original index

      let n = angular.copy(row);
      n.zoneId = uuid4.generate();
      n.zoneNumber = determineNextFreeRoofNumber(leadChar);
      n.zoneNumberSource = "AUTO";
      n.zoneDescription = (row.zoneDescription ?? "") + " - Copy";

      vm.source.roofs.splice(index + 1, 0, n);
      renumberRoofs();

      vm.sortedRoofs = common.applySort(vm.source.roofs, vm.RoofSortInfo);
    }

    function clearRoof(roof) {

      // NOTE: Doesn't nullify Roof Number on purpose (for now...)
      roof.zoneNumber = null;
      roof.zoneDescription = null;
      roof.zoneType = null;
      roof.floorArea = null;
      roof.perimeter = null;
      roof.roofPitch = null;
      roof.storey = null;
      roof.zoneNumberSource = "MANUAL";

    }

    function removeRoof(row) {

      for (var ii = 0, ilen = vm.source.roofs.length; ii < ilen; ii++) {
        if (vm.source.roofs[ii].zoneId === row.zoneId) {
          vm.source.roofs.splice(ii, 1);
          renumberRoofs();

          vm.sortedRoofs = common.applySort(vm.source.roofs, vm.RoofSortInfo);
          return;
        }
      }

    }

    function removeRoofs(roofs) {
      vm.source.roofs = [];
    }

    /**
     * Toggles bulk edit selection on/off for ALL rows.
     *
     * @param {any} toggleState If true, everything will be selected. False, everything deselected.
     * @param {any} code Exterior and Interior roofs have a different property tracking if they have been selected. This is to select which.
     */
    function toggleBulkEditAll(toggleState, code, roofs) {

      roofs.forEach(row => {
        row[code] = toggleState;
      });
    }

    function showBulkEdit(code, bulkCode, roofList, leadChar) {

      var newScope = $rootScope.$new();
      newScope.zoneTypeList = vm.zoneTypeList;
      newScope.storeys = vm.source.storeys;
      newScope.type = "roofs";

      $mdDialog.show({
        templateUrl: 'app/ui/assessment/floor-plan-data/building-spaces-bulk-edit.html',
        scope: newScope,
      })
        .then(function (response) {

          // Loop over all Roofs and apply the new data from our bulk edit modal where appropriate
          for (let i = 0; i < roofList.length; i++) {

            if (roofList[i][code] == null ||
              roofList[i][code] === false)
              continue;

            roofList[i][code] = false;

            if (response.bulkEditAction === 'EDIT')
              roofList[i] = common.nullAwareMerge(roofList[i], response);
            else if (response.bulkEditAction === 'CLEAR')
              clearRoof(roofList[i]);
            else if (response.bulkEditAction === 'DELETE') {
              removeRoof(roofList[i]);
              i--;
            } else if (response.bulkEditAction === 'COPY') {
              cloneRoof(roofList[i], leadChar);
              i++;
            }
          }

          vm[bulkCode] = false;

        }, function () {
          // Cancelled, do nothing.
        });
    }

    function noneSelected(code) {
      return !vm.source?.roofs?.some(x => x[code]);
    }

    /** Determines which is the next free roof number based on (existing roofs, their number). */
    function determineNextFreeRoofNumber(leadChar) {

      leadChar = leadChar.toUpperCase();

      // Get all existing roofs that match the format Z### (Regardless of SOURCE).
      let pattern = `${leadChar}(\\d{3})`
      let regEx = new RegExp(pattern);

      let matches = [];
      let existingRoofs = vm.source?.roofs;

      existingRoofs.forEach(roof => {
        let matching = regEx.exec(roof.zoneNumber);

        // We add a check for length so that things like "ZZ0044", "Z002z" etc don't match.
        if (matching != null && matching.input.length == 4)
          matches.push(roof.zoneNumber);
      });

      // Convert to actual numbers (I.e. Z001 => 1 etc);
      let converted = [];
      matches.forEach(zn => {
        converted.push(parseInt(zn.slice(1)));
      });

      // Loop over until we find a free slot.
      for (let i = 1; i < 10000; i++) {
        if (!converted.some(c => c === i))
          return leadChar + common.addLeadingZero(i, 3);
      }

      // Failure state.
      return "!###";

    }

    vm.disabledEx = disabledEx;
    function disabledEx () {
      return vm.disabled;
    }

    vm.symbol = function(symbolName) {
      return common.symbol(symbolName);
    }

  }

})();