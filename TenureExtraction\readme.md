﻿# Overview

The 'Downloader' class is responsible for using Puppeteer to log into the LandGate website and retreive an authentication cookie which we can then use to retreive the LGATE-226 dataset. It also handles preparing the filesystem (by cleaning files and directories if needed).

The Extractor class then processes the downloaded .zip file and stores it into he given SQL database.

# Things to watch out for

It was discovered that sometimes the LGATE dataset .zip file returned on a given day is actually SIGNIFICANTLY smaller than on prior days (I.e. missing hundreds of thousands of addresses). No idea why this is. But it means that there is some logic within the Extractor class that checks for a smaller file size over a given % and ignores the new dataset if it thinks it is too small.

Make sure the IIS process/user has all needed file permissions to not only the /SlipDataset folder but if placing a specific archive within it, to that also, as the process will fail if the IIS process cannot clean directors and so on.

# Data Overrides / Initial Setup

When putting onto a server for the first time, you can place a .zip archive named DATASET.zip within the "SlipDataset" folder and the extraction process will see it and attempt to use if before downloading a new dataset. You can do this if for instance you know the current dataset if faulty (see "Things to watch out for").
