<div ngf-drop="vm.uploadFile($file, vm.fileObject, vm.propName)"
     ngf-drag-over-class="'file-dragover'"
     ng-form="{{vm.formName}}">

    <!-- Input + Upload Buttons-->
    <div layout="row"
         flex="100"
         layout-wrap
         ng-if="!vm.fileObject[vm.propName] && !vm.uploadIsInProgress()">

        <md-input-container class="md-block" flex>
            <label>{{vm.label || 'File'}}</label>
            <md-select name="file"
                       style="width: 90%;"
                       ng-model="vm.fileObject[vm.propName]"
                       ng-disabled="vm.forceEdit!=true && vm.isLocked"
                       ng-required="vm.isRequired"
                       ng-change="vm.fileChanged()">
                <md-option ng-value="listItem"
                           ng-repeat="listItem in vm.jobFiles track by listItem.fileId">
                    {{listItem.displayName}}
                </md-option>
            </md-select>
            <div ng-messages="vm.formName.file.$error">
                <div ng-message="required">{{vm.requiredMessage}}</div>
            </div>
        </md-input-container>
        <div style="width: 50px" layout="row" layout-align="center start">
            <md-button class="md-raised md-icon-button small-icon-button"
                       ngf-select="vm.uploadFile($file, vm.fileObject, vm.propName)"
                       ngf-accept="vm.accept"
                       ng-show="!vm.isLocked"
                       ng-disabled="(vm.forceEdit!=true && vm.isLocked)">
                <i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>
            </md-button>
        </div>
    </div>

    <!-- Loaded Filename/Download Button + Remove Button -->
    <div layout="row"
         flex="100"
         layout-wrap
         ng-if="vm.fileObject[vm.propName] && !vm.uploadIsInProgress()"
         layout-align="center start"
         style="display: grid; grid-template-columns: 9fr 1fr; align-items: center;">
        <md-button class="md-primary md-small text-left" 
                   style="width: 100%;"
                   ng-click="vm.downloadFile(vm.fileObject[vm.propName])">
            <span style="margin-left: 10px;">{{vm.fileObject[vm.propName].fileName}}</span>
        </md-button>

        <!-- 'More' button w/ Popup -->
        <md-menu style="display: flex; justify-content: center;">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                 class="clickable"
                 ng-click="$mdOpenMenu()"
                 src="/content/feather/more-horizontal.svg"
                 ng-disabled="vm.disabled"/>
            <md-menu-content>

                <!-- Download Button -->
                <md-menu-item>
                    <md-button ng-click="vm.downloadFileForceDialog(vm.fileObject[vm.propName])">
                        Download
                    </md-button>
                </md-menu-item>

                <md-menu-divider ng-if="!vm.isLocked"></md-menu-divider>

                <!-- Rename parent item -->
                <md-menu-item ng-if="!vm.isLocked">
                    <md-button ng-click="vm.setToNull(vm.fileObject, vm.propName)">
                        <span ng-style="{'color': vm.isLocked ? 'inherit' : 'orangered'}">Delete</span>
                    </md-button>
                </md-menu-item>
            </md-menu-content>
        </md-menu>

    </div>

    <div layout="row" ng-if="!vm.fileObject[vm.propName]&&vm.fileObject[vm.propName + 'UploadProgress']" layout-align="center center">
        <span flex></span>
        <md-progress-circular md-diameter="40" md-mode="determinate" value="{{vm.fileObject[vm.propName + 'UploadProgress']}}"></md-progress-circular>
        <span flex></span>
    </div>

</div>