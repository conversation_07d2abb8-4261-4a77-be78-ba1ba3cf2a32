﻿<div class="custom-popup-wrapper"
     ng-style="{top: position().top+'px', left: position().left+'px'}"
     style="display: block;"
     ng-show="isOpen() && !moveInProgress"
     aria-hidden="{{!isOpen()}}">

    <ul class="dropdown-menu" role="listbox">
        <li class="uib-typeahead-match" ng-repeat="match in matches track by $index" ng-class="{active: isActive($index) }"
            ng-mouseenter="selectActive($index)" ng-click="selectMatch($index)" role="option" id="{{::match.id}}">
            <div uib-typeahead-match index="$index" match="match" query="query" template-url="templateUrl"></div>
        </li>
    </ul>
    <div ng-show="actionButtonPresent" class="message">
        <a class="bold btn btn-sm btn-primary" ng-click="decorateButtonCall()"><i class="fa fa-plus"></i>{{decorateButtonLabel}}</a>
    </div>
</div>