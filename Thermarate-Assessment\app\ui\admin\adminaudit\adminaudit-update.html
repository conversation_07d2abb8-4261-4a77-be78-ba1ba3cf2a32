<form name="adminauditform" class="main-content-wrapper" novalidate data-ng-controller='AdminauditUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div>
                    <md-card>
                        <md-card-header>
                            Admin Audit
                        </md-card-header>
                        <md-card-content>

    <!-- ******** Component Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Component Name</label>
                                <input type="text" name="componentName" 
                                        ng-model="vm.adminaudit.componentName" 
                                        md-autofocus
                                        md-maxlength="100"
                                        required
                                        />
                                <div ng-messages="adminauditform.componentName.$error">
                                    <div ng-message="required">Component Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

    <!-- ******** Item Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Item Name</label>
                                <input type="text" name="itemName" 
                                        ng-model="vm.adminaudit.itemName" 

                                        md-maxlength="100"
                                        required
                                        />
                                <div ng-messages="adminauditform.itemName.$error">
                                    <div ng-message="required">Item Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

    <!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Description</label>
                                <input type="text" name="description" 
                                        ng-model="vm.adminaudit.description" 

                                        required
                                        />
                                <div ng-messages="adminauditform.description.$error">
                                    <div ng-message="required">Description is required.</div>
                                </div>
                            </md-input-container>

    <!-- ******** Change Type ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Change Type</label>
                                <input type="text" name="changeType" 
                                        ng-model="vm.adminaudit.changeType" 

                                        md-maxlength="20"
                                        required
                                        />
                                <div ng-messages="adminauditform.changeType.$error">
                                    <div ng-message="required">Change Type is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.adminaudit"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
