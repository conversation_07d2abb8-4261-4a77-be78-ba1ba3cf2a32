<md-dialog ng-controller="PlanNumberModalCtrl as vm">
    <ng-form name="plannumberform" novalidate ng-submit="vm.search()">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Search for Plan Number</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="column" layout-margin layout-padding>

            <md-input-container class="md-block" flex-gt-sm>
                <label>Deposited Plan Number</label>
                <input type="text" name="planNumber"
                       ng-maxlength="40"
                       required
                       ng-model="vm.planNumber"/>
                <div ng-messages="plannumberform.planNumber.$error">
                    <div ng-message="maxlength">Too many characters entered, max length is 40.</div>
                    <div ng-message="required">Required.</div>
                </div>
            </md-input-container>

            <md-button typeof="submit" ng-disabled="vm.planNumber==''||vm.planNumber==null" ng-click="vm.search()" class="md-raised" md-primary>
                Search
            </md-button>

            <div class="widget-content" >
                <p ng-show="vm.showResults && vm.addresses.length > 0">Select an address</p>
                <p ng-show="vm.showResults && vm.addresses.length == 0" class="red">No Addresses Found</p>
                <div ng-repeat="item in vm.addresses track by $index" layout="row" ng-click="vm.addressSelected(item)" class="padd" style="padding:4px 10px;" >
                    <div flex="15"><span ng-show="item.CustomFields.lot_number!=''&&item.CustomFields.lot_number!=null">Lot&nbsp;{{item.CustomFields.lot_number}},</span></div><div>{{item.CustomFields.formattedaddress}}</div>
                </div>
            </div>

        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
        </md-dialog-actions>
    </ng-form>
</md-dialog>