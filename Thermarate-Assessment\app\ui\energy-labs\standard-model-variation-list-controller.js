(function () {

    'use strict';
    angular.module('app').component('standardModelVariationList', {
        bindings: {
            projectId: '<',
            variationOfId: '<',
            variationOptionsList: '<',
            variationOptionsSettings: '<',
            disabled: '<',
            parentIsActive: '<',
            parent3dModel: '<',
            parentCostEstimate: '<',
            parentDesignInsights: '<',
            onInitialiseComplete: '&',
            onDelete: '&',
            passedUpModels: '=', // So parent can save any changes during it's own save process,
            rowClickCallback: '&'
        },
        templateUrl: 'app/ui/energy-labs/standard-model-variation-list.html',
        controller: standardModelListController,
        controllerAs: 'vm'
    });

    standardModelListController.$inject = ['$rootScope', '$scope', '$mdDialog', 'bootstrap.dialog', 'standardmodelservice', 'projectservice', 'daterangehelper', 'common'];

    function standardModelListController($rootScope, $scope, $mdDialog, modalDialog, standardmodelservice, projectservice, daterangehelper, common) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Home Designs';
        vm.modelVariationList = [];
        vm.listFilter = "";
        vm.actionButtons = [];

        vm.actionButtons = [];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};

        vm.bulkStatus = new MasterBulkStatus();
        vm.bulkStatus.checkboxId = "sm-allCheckbox";
        vm.bulkStatus.isIndeterminate = false;
        vm.bulkStatus.selectAllCheckboxState = false;

        var saveTableState = null;
        var persistRangeName = "standardModelList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);

        // Variation
        vm.variationCategories = StandardModelConstants.variationCategories;
        vm.updateVariationOptionSelection = standardmodelservice.updateVariationOptionSelection;

        // Utils
        vm.wohConstants = WholeOfHomeConstants;
        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.roundUpInt = common.roundUpInt;
        vm.featureName = standardmodelservice.featureName;
        vm.keyToName = standardmodelservice.keyToName;

        // --------- //
        // - UTILS - //
        // --------- //

        vm.getAvailableFloorplanOptions = function (variation) {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != variation.standardHomeModelId), variation, vm.getVariationOptions("Floorplan"),     "variationFloorPlanId",     "standardHomeModelVariationOptionId", ["variationDesignOptionId", "variationFacadeId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableDesignOptionOptions = function (variation) {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != variation.standardHomeModelId), variation, vm.getVariationOptions("DesignOption"),  "variationDesignOptionId",  "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationFacadeId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableFacadeOptions = function (variation) {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != variation.standardHomeModelId), variation, vm.getVariationOptions("Facade"),        "variationFacadeId",        "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableSpecificationOptions = function (variation) {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != variation.standardHomeModelId), variation, vm.getVariationOptions("Specification"), "variationSpecificationId", "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationFacadeId", "variationConfigurationId"]);
        }
        vm.getAvailableConfigurationOptions = function (variation) {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != variation.standardHomeModelId), variation, vm.getVariationOptions("Configuration"), "variationConfigurationId", "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationFacadeId", "variationSpecificationId"]);
        }

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Get Variation Options for Category
        vm.getVariationOptions = function (category) {
            return vm.variationOptionsList.filter(o => o.variationCategoryCode == category);
        }

        // Refresh List
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        // Call Server
        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            vm.isBusy = true;

            if (tableState != null)
                saveTableState = tableState;

            let pagination = saveTableState?.pagination;
            let start = pagination.start || 0;       // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            let pageSize = pagination.number || 100; // Number of entries showed per page.
            let pageIndex = (start / pageSize) + 1;

            let sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }

            // Save sort field to vm so know whether to show "Move Down" and "Move Up"
            vm.sortField = sort.field;

            let filter = null;

            standardmodelservice.getListCancel();
            standardmodelservice.getModelVariationsQuery(
                vm.variationOfId,
                vm.listFilter,
                pageSize,
                pageIndex,
                sort,
                filter
            ).then(
                result => {
                    if (result != null) {
                        vm.currentFilter = standardmodelservice.currentFilter();
                        vm.modelVariationList = result.data;
                        // Make sure no 2 SortOrders are the same
                        let tempList = angular.copy(vm.modelVariationList);
                        tempList.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1);
                        for (let i = 0; i < tempList.length-1; i++) {
                            if (tempList[i].sortOrder == tempList[i+1].sortOrder) {
                                for (let j = i+1; j < tempList.length; j++) {
                                    vm.modelVariationList.find(m => m.standardModelId == vm.modelVariationList[j].standardModelId).sortOrder++;
                                }
                            }
                        }
                        vm.passedUpModels = vm.modelVariationList;
                        vm.totalRecords = result.total;
                        saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); // Set the number of pages so the pagination can updateA
                        vm.showingFromCnt = vm.modelVariationList.length > 0 ? start + 1 : 0;
                        vm.showingToCnt = start + result.data.length;
                        if (vm.onInitialiseComplete != null) {
                            setTimeout(vm.onInitialiseComplete); // In timeout so 'passedUpModels' is set by time the 'onInitialiseComplete()' runs
                        }
                        vm.isBusy = false;
                    }
                },
                error => vm.isBusy = false
            );
        };

        // Updates
        vm.defaultVariationChanged = function (variation) {
            vm.modelVariationList.forEach(v => {
                if (v.standardHomeModelId != variation.standardHomeModelId) {
                    v.isDefaultVariation = false;
                }
            });
            variation.isDefaultVariation = true;
        }

        // Add
        vm.addVariation = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.variationOfId = vm.variationOfId;
            modalScope.projectId = vm.projectId;
            modalScope.newRecord = true;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.setAsDefault = vm.modelVariationList.length == 0;
            var modalOptions = {
                templateUrl: 'app/ui/data/standard-model-variation/standard-model-variation-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: () => 'New'
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(
                data => {
                    // Returned from modal, so refresh list.
                    vm.refreshList(null);
                },
                () => {
                    // Cancelled.
                    vm.refreshList(null);
                }
            )['finally'](() => {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        // Duplicate
        vm.duplicate = function (homeModel) {
            standardmodelservice.copyStandardModel(homeModel.standardHomeModelId).then(id => {
                standardmodelservice.getStandardModel(id).then(standardModel => {
                    // Add returned template to list.
                    vm.modelVariationList.push(standardModel);
                    vm.passedUpModels = vm.modelVariationList;
                });
            });
        }

        // Move Variation Up
        vm.moveVariationUp = function (variation) {
            // Find variation before
            let variationBefore = vm.modelVariationList.sort((a,b) => a.sortOrder < b.sortOrder ? 1 : -1).find(o => o.sortOrder < variation.sortOrder);
            if (variationBefore == null) {
                variation.sortOrder = 1;
            } else {
                // Swap sort orders
                let tempSortOrder = variationBefore.sortOrder;
                variationBefore.sortOrder = variation.sortOrder;
                variation.sortOrder = tempSortOrder;
            }
            // Sort new list
            vm.modelVariationList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
        }

        // Move Variation Down
        vm.moveVariationDown = function (variation) {
            // Find variation after
            let variationAfter = vm.modelVariationList.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1).find(o => o.sortOrder > variation.sortOrder);
            // Swap sort orders
            let tempSortOrder = variationAfter.sortOrder;
            variationAfter.sortOrder = variation.sortOrder;
            variation.sortOrder = tempSortOrder;
            // Sort new list
            vm.modelVariationList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
        }

        // Delete
        vm.delete = async function (homeModel, override) {
            if (!override) {
                // IF this Variation is default, don't delete instead show warning
                if (homeModel.isDefaultVariation) {
                    modalDialog.infoDialog(
                        "Cannot delete this Variation",
                        "You can only dete a Variations that are not default.",
                        "",
                        "Ok",
                        null);
                } else {
                    let modalScope = $rootScope.$new();
                    modalScope.confirmationHeader = "Confirm Delete";
                    modalScope.confirmationText = "Are you sure you want to delete this Home Design?";
                    $mdDialog.show({
                        scope: modalScope,
                        templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                        parent: angular.element(document.body),
                        clickOutsideToClose: false,
                    }).then(confirmed => {
                        if (confirmed) {
                            _delete(homeModel);
                        }
                    });
                }
            } else {
                _delete(homeModel);
            }
            async function _delete(row) {
                await standardmodelservice.deleteStandardModel(row.standardHomeModelId);
                row.deleted = true;
                vm.modelVariationList = vm.modelVariationList.filter(x => x.standardHomeModelId !== row.standardHomeModelId);
                if (vm.onDelete) {
                    vm.onDelete();
                }
            }
        }

        // UI apply
        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        // - ---- - //
        // - BULK - //
        // - ---- - //

        // Checkboxes
        vm.selectAllCheckboxes = (a, b, c) => {
            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                safeApply();
            }, 25);
        }

        // Open Modal
        vm.launchBulkEditModal = async function() {
            let modalScope = $rootScope.$new();
            modalScope.thisModelId = vm.modelId;

            // Get selected variations
            let selectedModels = vm.modelVariationList.filter(x => x.checkboxSelected);

            // Determine default values based on selected variations
            // If all selected variations have false for a variable, default to false; otherwise, default to true
            let defaultIsActive = !selectedModels.every(m => m.isActive === false);
            let defaultView3dFloorPlans = !selectedModels.every(m => m.view3dFloorPlans === false);
            let defaultCostEstimateEnabled = !selectedModels.every(m => m.costEstimateEnabled === false);
            let defaultDesignInsightsEnabled = !selectedModels.every(m => m.variableMetadata?.designInsightsEnabled === false);

            // Pass parent settings and default values to modal
            modalScope.parentIsActive = vm.parentIsActive;
            modalScope.parent3dModel = vm.parent3dModel;
            modalScope.parentCostEstimate = vm.parentCostEstimate;
            modalScope.parentDesignInsights = vm.parentDesignInsights;

            modalScope.defaultIsActive = defaultIsActive;
            modalScope.defaultView3dFloorPlans = defaultView3dFloorPlans;
            modalScope.defaultCostEstimateEnabled = defaultCostEstimateEnabled;
            modalScope.defaultDesignInsightsEnabled = defaultDesignInsightsEnabled;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-standard-model-variation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {

                let selectedModels = vm.modelVariationList.filter(x => x.checkboxSelected);

                if (response.bulkEditAction === "DELETE") {

                    // First check if any selected is default
                    if (selectedModels.some(v => v.isDefaultVariation)) {
                        modalDialog.infoDialog(
                            "Cannot delete Variations",
                            "Your selection includes a default value. Default values cannot be deleted.",
                            "",
                            "Ok",
                            null);
                    } else {
                        let modalScope = $rootScope.$new();
                        modalScope.confirmationHeader = "Confirm Delete";
                        modalScope.confirmationText = "Are you sure you want to delete these Home Designs?";
                        $mdDialog.show({
                            scope: modalScope,
                            templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                            parent: angular.element(document.body),
                            clickOutsideToClose: false,
                        }).then(async function (confirmed) {

                            if (confirmed) {
                                vm.bulkDeleteModels(selectedModels);
                            }

                        });
                    }

                } else if (response.bulkEditAction === "COPY") {

                    vm.bulkDuplicateModels();

                } else {

                    // Apply settings to selected variations
                    selectedModels.forEach(m => {
                        m.isActive = response.isActive;
                        m.view3dFloorPlans = response.view3dFloorPlans;
                        m.costEstimateEnabled = response.costEstimateEnabled;
                        m.variableMetadata.designInsightsEnabled = response.designInsightsEnabled;
                    });

                }

                selectedModels.forEach(m => m.checkboxSelected = false);
                vm.bulkStatus.isIndeterminate = false;
                vm.bulkStatus.selectAllCheckboxState = false;

            });
        }

        vm.updateBulkSelectStatus = updateBulkSelectStatus;

        // Bulk Duplicate
        vm.bulkDuplicateModels = function () {
            const bulkSelected = vm.modelVariationList.filter(x => x.checkboxSelected);
            bulkSelected.forEach(x => { vm.duplicate(x); x.checkboxSelected = false; });
            vm.bulkStatus.isIndeterminate = false;
            vm.bulkStatus.selectAllCheckboxState = false;
        }

        // Bulk Delete
        vm.bulkDeleteModels = function (inList) {
            const bulkSelected = inList ?? vm.modelVariationList.filter(x => x.checkboxSelected);
            bulkSelected.forEach(x => { vm.delete(x, true); x.checkboxSelected = false; });
            vm.bulkStatus.isIndeterminate = false;
            vm.bulkStatus.selectAllCheckboxState = false;
        }

        // Upload Spreadsheet
        vm.uploadSpreadsheetForBulkCreate = function (file) {
            if (file != null) {
                standardmodelservice.processSpreadsheet(file, vm.standardModel.standardHomeModelId).then(() => setTimeout(refreshStandardModel, 250));
            }
        }

    }
  })();