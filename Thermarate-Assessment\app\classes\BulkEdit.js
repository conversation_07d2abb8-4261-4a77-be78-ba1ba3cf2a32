﻿
class MasterBulkStatus {

  /** 
   * The ID of the master md-checkbox element. Required as we manually apply a
   * class to specify the checkbox is in the indeterminate state.
   */
  checkboxId;
  
  /** True if some of the rows are a mix of selected/unselected */
  isIndeterminate;
  
  /** The state of the master 'select all' checkbox state. */
  selectAllCheckboxState;
}

/**
 * 
 * @param {[]} allRows An array of anything. Will have the 'checkboxSeleted' 
 * property applied as needed.
 * @param {Boolean} state NEW incoming state that should be applied to all rows.
 * @param {MasterBulkStatus} bulkStatus Used to keep track of the 'master' 
 * checkbox value, as it both drives and is driven by individual rows.
 */
function selectAllCheckboxes(allRows, state, bulkStatus) {
  
    let wantedResult = false;
    if (bulkStatus.isIndeterminate === true)
      wantedResult = true;
    else if (state !== true)
      wantedResult = true;

    allRows.forEach(x => x.checkboxSelected = wantedResult);

    bulkStatus.selectAllCheckboxState = wantedResult;
    updateBulkSelectStatus(allRows, bulkStatus);
}

function updateBulkSelectStatus(allRows, bulkStatus) {

  const allChecked = allRows.length > 0 && allRows
    .every(x => x.checkboxSelected === true);

  const someChecked = allRows
    .some(x => x.checkboxSelected === true );

  if(allChecked) {
    bulkStatus.isIndeterminate = false;
    bulkStatus.selectAllCheckboxState = true;

    // Have to manually apply this class (Probably due to clashes with the indeterminate value)
    setTimeout(() => {
      let currentClass = document.getElementById(bulkStatus.checkboxId).className;
      if (currentClass.indexOf("ng-empty-add md-checked") === -1) {
        document.getElementById(bulkStatus.checkboxId).className += " ng-empty-add md-checked";
      }
    }, 25);

  } else if(someChecked) {
    bulkStatus.selectAllCheckboxState = false;
    bulkStatus.isIndeterminate = true;
  } else {
    bulkStatus.selectAllCheckboxState = false;
    bulkStatus.isIndeterminate = false;
  }
  
}