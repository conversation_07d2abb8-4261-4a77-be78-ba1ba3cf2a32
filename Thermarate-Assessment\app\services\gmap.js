// Name: mapservice
// Type: Angular Service
// Purpose: call server for generating google map and display poles 
(function () {
    'use strict';
    var serviceId = 'mapjobservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', mapjobservice]);

    function mapjobservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var service = {
            /* These are the operations that are available from this service. */           
            getMapDetails: getMapDetails,
        };
     
        return service;
        
        function getMapDetails(current, fromDate, toDate) {
          
            var wkUrl = '../api/Map/Get';
            var params = { current: current, fromDate: fromDate, toDate: toDate};
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {           
                return resp.data;
            }
            function fail(error) {
                var msg = "Error getting Map Details list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return resp.data;
        }
    }

})();