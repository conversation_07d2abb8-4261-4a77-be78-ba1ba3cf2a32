(function () {
    'use strict';
    var controllerId = 'GroupDrawingUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', groupDrawingUpdateController]);
    function groupDrawingUpdateController($scope, $mdDialog) {
        var vm = this;
        vm.matchingDrawings = $scope.matchingDrawings;
        vm.selected = [];
        for (var ii = 0; ii < vm.matchingDrawings.length; ii++) {
            vm.selected[ii] = true;
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.submitSelection = function () {
            var ids = [];
            for (var ii = 0; ii < vm.selected.length; ii++) {
                if (vm.selected[ii] === true) {
                    ids.push(vm.matchingDrawings[ii].updateIndex);
                }
            }
            $mdDialog.hide(ids);
        };
    }
})();