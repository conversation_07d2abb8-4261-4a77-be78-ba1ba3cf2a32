<form name="constructionform"
      class="main-content-wrapper"
      style="width: 600px;"
      ng-style="{ 'width' : vm.isModal == true ? '600px' : ''}"
      novalidate
      data-ng-controller='ConstructionUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
    </div>

    <!-- General -->
    <md-card>
        <md-card-header>
            <h2 style="margin: 0px;">General</h2>
        </md-card-header>
        <md-card-content>

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Category -->
                <!-- Depending on the Category, different inputs are available below. -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Category</label>
                    <md-select required
                               ng-disabled="vm.newRecord == false"
                               name="constructionCategory"
                               ng-model="vm.construction.category"
                               ng-model-options="{trackBy: '$value.constructionCategoryCode'}"
                               ng-change="vm.constructionCategoryChanged()">
                        <md-option ng-value="category"
                                   ng-repeat="category in vm.constructionCategories">
                            {{category.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionCategory.$error">
                        <div ng-message="required">Category is required.</div>
                    </div>
                </md-input-container>

                <!-- Construction Type (Construction DB Only)-->
                <!-- Depends on the Category field above -->
                <md-input-container ng-if="vm.categoryIsSurface() && !(vm.categoryIsPermanentOpening() || vm.categoryIsDoor())"
                                    class="md-block" flex-gt-sm>
                    <label>Construction Type</label>
                    <md-select required
                               ng-disabled="!vm.construction.category || vm.editPermission == false"
                               name="constructionSubCategory"
                               ng-model="vm.construction.subCategory"
                               ng-model-options="{trackBy: '$value.constructionSubCategoryCode'}">
                        <md-option ng-value="category"
                                   ng-repeat="category in vm.filteredConstructionSubCategories">
                            {{category.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionSubCategory.$error">
                        <div ng-message="required">Construction Type is required.</div>
                    </div>
                </md-input-container>

                <!-- Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Description</label>
                    <input type="text" name="description"
                           ng-model="vm.construction.description"
                           md-maxlength="1000"
                           required />
                    <div ng-messages="constructionform.description.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 1000.</div>
                    </div>
                </md-input-container>

                <!-- Display Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Display Description</label>
                    <input type="text"
                           name="Display Description"
                           ng-model="vm.construction.displayDescription"
                           md-maxlength="1000"
                           required />
                    <div ng-messages="constructionform.description.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                    </div>
                </md-input-container>

                <!-- Show in Report -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>Show in Report</label>
                    <md-select required
                               name="showInReport"
                               ng-model="vm.construction.showInReport">
                        <md-option ng-value="true">
                            Yes
                        </md-option>
                        <md-option ng-value="false">
                            No
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.showInReport.$error">
                        <div ng-message="required">You must select a value.</div>
                    </div>
                </md-input-container>

                <!-- Manufacturer -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>Manufacturer</label>
                    <md-select required
                               name="manufacturer"
                               ng-model="vm.construction.manufacturer"
                               ng-model-options="{trackBy: '$value.manufacturerId'}">
                        <md-option ng-value="manufacturer"
                                   ng-repeat="manufacturer in vm.manufacturers">
                            {{manufacturer.description}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.manufacturer.$error">
                        <div ng-message="required">Manufacturer is required.</div>
                    </div>
                </md-input-container>

                <!-- Construction Id -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>{{(vm.categoryIsGlazing() || vm.categoryIsRoofLighting()) ? "Window ID" : "Construction Id"}}</label>
                    <input type="text"
                           name="constructionId"
                           ng-model="vm.construction.externalConstructionId"
                           md-maxlength="200"
                           ng-disabled="vm.categoryIsPermanentOpening()"
                           required />
                    <div ng-messages="constructionform.constructionId.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 200.</div>
                    </div>
                </md-input-container>

                <!-- Adjacency -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Adjacency</label>
                    <md-select required
                               name="constructionAdjacency"
                               ng-model="vm.construction.adjacency"
                               ng-model-options="{trackBy: '$value.adjacencyCode'}">
                        <md-option ng-value="adjacency"
                                   ng-repeat="adjacency in vm.adjacencyList">
                            {{adjacency.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionAdjacency.$error">
                        <div ng-message="required">Adjacency is required.</div>
                    </div>
                </md-input-container>

                <!-- Unit of Measure -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Unit of Measure</label>
                    <md-select required
                               name="unitOfMeasure"
                               ng-model="vm.construction.unitOfMeasure"
                               ng-model-options="{trackBy: '$value.unitOfMeasureCode'}">
                        <md-option ng-value="unit"
                                   ng-repeat="unit in vm.unitOfMeasureList">
                            {{unit.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.unitOfMeasure.$error">
                        <div ng-message="required">Unit of Measure is required.</div>
                    </div>
                </md-input-container>

                <!-- Favourite -->
                <md-input-container class="md-block" flex-gt-sm>
                    <md-checkbox ng-model="vm.construction.isFavourite"
                                 class="md-primary">
                        Favourite
                    </md-checkbox>
                </md-input-container>

            </fieldset>

            <div class="col-md-12" ng-if="vm.newRecord==false">
                <div rd-display-created-modified ng-model="vm.construction"></div>
            </div>
        </md-card-content>
    </md-card>

    <!-- Properties -->
    <md-card>
        <md-card-header>
            <h2 style="margin: 0px;">Properties</h2>
        </md-card-header>

        <!-- Properties for Surfaces-->
        <md-card-content >

            <fieldset redi-enable-roles="settings__settings__edit">

                <div ng-if="vm.categoryIsPermanentOpening()">
                    <!-- Tilt -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Tilt (&deg;)</label>
                        <input type="text"
                               formatted-number
                               decimals="2"
                               name="tilt"
                               ng-disabled="true"
                               ng-model="vm.construction.tilt"/>
                        <div ng-messages="constructionform.tilt.$error">
                            <div ng-message="md-maxlength">Tilt is required.</div>
                        </div>
                    </md-input-container>
                </div>

                <div ng-if="vm.categoryIsSurface()">
                    <!-- Thickness -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Thickness (mm)</label>
                        <input formatted-number
                               decimals="2"
                               name="thickness"
                               ng-model="vm.construction.thickness"
                               required />
                        <div ng-messages="constructionform.thickness.$error">
                            <div ng-message="md-maxlength">Thickness is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Density-->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Density (kg/{{vm.construction.unitOfMeasure.title}})</label>
                        <input type="number"
                               name="density"
                               ng-model="vm.construction.density"
                               required />
                        <div ng-messages="constructionform.density.$error">
                            <div ng-message="md-maxlength">Density is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Tilt -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Tilt (&deg;)</label>
                        <input type="text"
                               formatted-number
                               decimals="2"
                               name="tilt"
                               ng-model="vm.construction.tilt"/>
                        <div ng-messages="constructionform.tilt.$error">
                            <div ng-message="md-maxlength">Tilt is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Floor Covering -->
                    <md-input-container ng-if="vm.categoryIsFloor()"
                                        class="md-block"
                                        flex-gt-sm>
                        <label>Floor Covering</label>
                        <input name="floorCovering"
                               ng-model="vm.construction.floorCovering"
                               required />
                        <div ng-messages="constructionform.floorCovering.$error">
                            <div ng-message="md-maxlength">Floor Covering is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Exterior Colour -->
                    <md-autocomplete flex="100"
                                     id="frameColour"
                                     md-input-name="template"
                                     md-selected-item="vm.construction.exteriorColour"
                                     md-selected-item-change="vm.applyColour(colour, 'exterior')"
                                     md-search-text="exteriorColourSearchText"
                                     md-items="colour in vm.colourListSafe() | filter: { title: exteriorColourSearchText }"
                                     md-item-text="colour.title"
                                     md-min-length="0"
                                     md-select-on-match="true"
                                     placeholder="Search colours..."
                                     md-floating-label="Exterior Colour">
                        <md-item-template>
                    <span md-highlight-text="exteriorColourSearchText"
                          md-highlight-flags="^i">{{colour.title}}</span>
                        </md-item-template>
                    </md-autocomplete>

                    <!-- Exterior Solar Absorptance -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Exterior Solar Absorptance</label>
                        <input formatted-number
                               decimals="2"
                               name="exteriorSolarAbsorptance"
                               ng-model="vm.construction.exteriorSolarAbsorptance"
                               ng-disabled="vm.construction.exteriorColour.allowEditingSolarAbsorptance === false" />
                        <div ng-messages="constructionform.exteriorSolarAbsorptance.$error">
                            <div ng-message="md-maxlength">Exterior Solar Absorptance is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Interior Colour -->
                    <md-autocomplete flex="100"
                                     id="frameColour"
                                     md-input-name="template"
                                     md-selected-item="vm.construction.interiorColour"
                                     md-selected-item-change="vm.applyColour(colour, 'interior')"
                                     md-search-text="interiorColourSearchText"
                                     md-items="colour in vm.colourListSafe() | filter: { title: interiorColourSearchText }"
                                     md-item-text="colour.title"
                                     md-min-length="0"
                                     md-select-on-match="true"
                                     placeholder="Search colours..."
                                     md-floating-label="Interior Colour">
                        <md-item-template>
                    <span md-highlight-text="interiorColourSearchText"
                          md-highlight-flags="^i">{{colour.title}}</span>
                        </md-item-template>
                    </md-autocomplete>

                    <!-- Interior Solar Absorptance -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Interior Solar Absorptance</label>
                        <input formatted-number
                               decimals="2"
                               name="interiorSolarAbsorptance"
                               ng-model="vm.construction.interiorSolarAbsorptance"
                               ng-disabled="vm.construction.interiorColour.allowEditingSolarAbsorptance === false" />
                        <div ng-messages="constructionform.interiorSolarAbsorptance.$error">
                            <div ng-message="md-maxlength">Interior Solar Absorptance is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Air Cavity (Ventilation) -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <!--
                            For some unknown reason, this label refuses to update after the first selection
                            Taking the binding OUT of a label tag works perfectly fine. FFS!
                        -->
                        <label>{{vm.construction.category.ventilationType}}</label>
                        <md-select required
                                   name="airCavity"
                                   ng-disabled="vm.editPermission == false"
                                   ng-model="vm.construction.airCavity"
                                   ng-model-options="{trackBy: '$value.airCavityCode'}">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="ac"
                                       ng-repeat="ac in vm.airCavityList | filter:subfloorVentilationFilter(vm.construction.category)">
                                {{ac.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.airCavity.$error">
                            <div ng-message="required">Air Cavity is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Thermal Bridge-->
                    <md-input-container ng-if="vm.construction.category.requiresThermalBridge == true"
                                        class="md-block"
                                        flex-gt-sm>
                        <label>Thermal Bridge</label>
                        <md-select required
                                   name="thermalBridge"
                                   ng-disabled="vm.editPermission == false"
                                   ng-model="vm.construction.thermalBridge">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.thermalBridge.$error">
                            <div ng-message="required">Thermal Bridge is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Full Masonry-->
                    <md-input-container ng-if="vm.construction.category.requiresIsFullMasonry == true"
                                        class="md-block"
                                        flex-gt-sm>
                        <label>Full Masonry</label>
                        <md-select required
                                   name="isFullMasonry"
                                   ng-disabled="vm.editPermission == false"
                                   ng-model="vm.construction.isFullMasonry">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.weatherStripped.$error">
                            <div ng-message="required">Full Masonry is required.</div>
                        </div>
                    </md-input-container>

                    <!-- System R-Value -->
                    <md-input-container class="md-block"
                                        flex-gt-sm>
                        <label>System R-Value</label>
                        <input type="number"
                               name="systemRValue"
                               ng-model="vm.construction.systemRValue"
                               required />
                        <div ng-messages="constructionform.systemRValue.$error">
                            <div ng-message="md-maxlength">System R-Value is required.</div>
                        </div>
                    </md-input-container>

                </div>

                <div ng-if="vm.categoryIsDoor() || vm.categoryIsPermanentOpening()">

                    <!-- Opening Style -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Opening Style</label>
                        <md-select required
                                   ng-disabled="vm.categoryIsPermanentOpening()"
                                   name="openingStyle"
                                   ng-model="vm.construction.openingStyle"
                                   ng-model-options="{trackBy: '$value.openingStyleCode'}">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="style"
                                       ng-repeat="style in vm.openingListForCategory('window')"
                                       ng-click="vm.construction.openability = style.defaultOpenability">
                                {{style.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.openingStyle.$error">
                            <div ng-message="required">Opening Style is required.</div>
                        </div>
                    </md-input-container>

                    <!-- NCC Opening Style -->
                    <md-input-container class="md-block"
                                        flex-gt-sm>
                        <label>NCC Opening Style</label>
                        <md-select required
                                   ng-disabled="vm.categoryIsPermanentOpening()"
                                   name="nccOpeningStyle"
                                   ng-model="vm.construction.nccOpeningStyle"
                                   ng-model-options="{trackBy: '$value.nccOpeningStyleCode'}">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="style"
                                       ng-repeat="style in vm.nccOpeningStyleList"
                                       ng-click="vm.setNccOpeningStyle(style);">
                                {{style.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.nccOpeningStyle.$error">
                            <div ng-message="required">Opening Style is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Weather Stripped -->
                    <md-input-container ng-if="vm.categoryIsDoor()"
                                        class="md-block" flex-gt-sm>
                        <label>Weather Stripped</label>
                        <md-select required
                                   name="weatherStripped"
                                   ng-model="vm.construction.hasWeatherStrip">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.weatherStripped.$error">
                            <div ng-message="required">Weather Stripped is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Insect Screen -->
                    <md-input-container ng-if="vm.categoryIsDoor()"
                                        class="md-block" flex-gt-sm>
                        <label>Insect Screen</label>
                        <md-select required
                                   name="insectScreen"
                                   ng-model="vm.construction.hasInsectScreen">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.insectScreen.$error">
                            <div ng-message="required">Insect Screen is required.</div>
                        </div>
                    </md-input-container>

                </div>

            </fieldset>

        </md-card-content>

        <!-- Properties for Openings (External Glazing + Roof Lighting) -->
        <md-card-content ng-if="vm.categoryIsGlazing() || vm.categoryIsRoofLighting()">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Frame -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Frame</label>
                    <md-select required
                               name="frameMaterial"
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.construction.frameMaterial"
                               ng-model-options="{trackBy: '$value.frameMaterialCode'}">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="frame"
                                   ng-repeat="frame in vm.frameMaterialList">
                            {{frame.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.airCavity.$error">
                        <div ng-message="required">Frame is required.</div>
                    </div>
                </md-input-container>

                <!-- Thermal Break -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Thermal Break</label>
                    <md-select required
                               name="thermalBreak"
                               ng-model="vm.construction.isThermalBreak">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="true">
                            Yes
                        </md-option>
                        <md-option ng-value="false">
                            No
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.isThermalBreak.$error">
                        <div ng-message="required">Thermal Break is required.</div>
                    </div>
                </md-input-container>

                <!-- Frame Colour -->
                <md-autocomplete flex="100"
                                 id="frameColour"
                                 md-input-name="template"
                                 md-selected-item="vm.construction.frameColour"
                                 md-selected-item-change="vm.applyColour(colour, 'frame')"
                                 md-search-text="colourSearchText"
                                 md-items="colour in vm.colourListSafe() | filter: { title: colourSearchText }"
                                 md-item-text="colour.title"
                                 md-min-length="0"
                                 md-select-on-match="true"
                                 placeholder="Search colours..."
                                 md-floating-label="Frame Colour">
                    <md-item-template>
                    <span md-highlight-text="colourSearchText"
                          md-highlight-flags="^i">{{colour.title}}</span>
                    </md-item-template>
                </md-autocomplete>

                <!-- Frame Solar Absorptance -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Frame Solar Absorptance</label>
                    <input formatted-number
                           decimals="2"
                           name="shaftReflectance"
                           ng-model="vm.construction.frameSolarAbsorptance"
                           ng-disabled="vm.construction.frameColour.allowEditingSolarAbsorptance === false" />
                    <div ng-messages="constructionform.glassThickness.$error">
                        <div ng-message="md-maxlength">Glass Thickness is required.</div>
                    </div>
                </md-input-container>

                <!-- Glass data -->
                <div>

                    <!-- Glass ID -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Glass Id</label>
                        <input type="text"
                               name="glassId"
                               ng-model="vm.construction.glassData.glassId"
                               md-maxlength="1000"
                               required />
                        <div ng-messages="constructionform.glassDescription.$error">
                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                        </div>
                    </md-input-container>

                    <!-- Glass Description -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Glass Description</label>
                        <input type="text"
                               name="glassDescription"
                               ng-model="vm.construction.glassData.description"
                               md-maxlength="1000"
                               required />
                        <div ng-messages="constructionform.glassDescription.$error">
                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                        </div>
                    </md-input-container>

                    <!-- Glass Type -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Glass Type</label>
                        <md-select required
                                   name="glassType"
                                   ng-disabled="vm.editPermission == false"
                                   ng-model="vm.construction.glassData.type"
                                   ng-model-options="{trackBy: '$value.glassTypeCode'}">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="type"
                                       ng-repeat="type in vm.glassTypeList">
                                {{type.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.airCavity.$error">
                            <div ng-message="required">Glass Type is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Glass Colour -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Glass Colour</label>
                        <md-select required
                                   name="glassColour"
                                   ng-disabled="vm.editPermission == false"
                                   ng-model="vm.construction.glassData.colour"
                                   ng-model-options="{trackBy: '$value.glassColourCode'}">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="colour"
                                       ng-repeat="colour in vm.glassColourList">
                                {{colour.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.airCavity.$error">
                            <div ng-message="required">Glass Color is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Laminated -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Laminated</label>
                        <md-select required
                                   name="laminated"
                                   ng-model="vm.construction.glassData.isLaminated">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.laminated.$error">
                            <div ng-message="required">Selection is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Low-E Coating -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Low-E Coating</label>
                        <md-select required
                                   name="loweCoating"
                                   ng-model="vm.construction.glassData.hasLowECoating">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.loweCoating.$error">
                            <div ng-message="required">Selection is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Glass Thickness (mm) -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Glass Thickness (mm)</label>
                        <input formatted-number
                               decimals="2"
                               name="shaftReflectance"
                               ng-model="vm.construction.glassData.thickness"
                               required />
                        <div ng-messages="constructionform.glassThickness.$error">
                            <div ng-message="md-maxlength">Glass Thickness is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Tilt -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Tilt</label>
                        <input formatted-number
                               decimals="2"
                               name="tilt"
                               ng-model="vm.construction.tilt"
                               required />
                        <div ng-messages="constructionform.tilt.$error">
                            <div ng-message="md-maxlength">Tilt is required.</div>
                        </div>
                    </md-input-container>

                </div>

                <!-- Opening Style -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Opening Style</label>
                    <md-select required
                               name="openingStyle"
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.construction.openingStyle"
                               ng-model-options="{trackBy: '$value.openingStyleCode'}">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="style"
                                   ng-repeat="style in vm.openingListForCategory()"
                                   ng-click="vm.setOpeningStyle(vm.construction, style);">
                            {{style.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.openingStyle.$error">
                        <div ng-message="required">Opening Style is required.</div>
                    </div>
                </md-input-container>

                <!-- NCC Opening Style -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>NCC Opening Style</label>
                    <md-select required
                               name="nccOpeningStyle"
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.construction.nccOpeningStyle"
                               ng-model-options="{trackBy: '$value.nccOpeningStyleCode'}">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="style"
                                   ng-repeat="style in vm.nccOpeningStyleList"
                                   ng-click="vm.setNccOpeningStyle(style);">
                            {{style.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.nccOpeningStyle.$error">
                        <div ng-message="required">Opening Style is required.</div>
                    </div>
                </md-input-container>

                <!-- Weather Stripped -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Weather Stripped</label>
                    <md-select required
                               name="weatherStripped"
                               ng-model="vm.construction.hasWeatherStrip">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="true">
                            Yes
                        </md-option>
                        <md-option ng-value="false">
                            No
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.weatherStripped.$error">
                        <div ng-message="required">Weather Stripped is required.</div>
                    </div>
                </md-input-container>

                <!-- Insect Screen -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Insect Screen</label>
                    <md-select required
                               name="insectScreen"
                               ng-model="vm.construction.hasInsectScreen">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="true">
                            Yes
                        </md-option>
                        <md-option ng-value="false">
                            No
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.insectScreen.$error">
                        <div ng-message="required">Insect Screen is required.</div>
                    </div>
                </md-input-container>

            </fieldset>

        </md-card-content>

    </md-card>

    <!-- Performance Card. Only show if type == Opening -->
    <md-card ng-if="vm.categoryIsGlazing() || vm.categoryIsRoofLighting()">
        <md-card-header>
            <h2>Performance</h2>
        </md-card-header>
        <md-card-content>

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- U-Value -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>U-Value</label>
                    <input formatted-number
                           decimals="2"
                           name="uValue"
                           ng-model="vm.construction.performance.uValue"
                           required />
                    <div ng-messages="constructionform.uValue.$error">
                        <div ng-message="md-maxlength">U-Value is required.</div>
                    </div>
                </md-input-container>

                <!-- SHGC -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>SHGC</label>
                    <input formatted-number
                           decimals="2"
                           name="shgc"
                           ng-model="vm.construction.performance.shgc"
                           required />
                    <div ng-messages="constructionform.shgc.$error">
                        <div ng-message="md-maxlength">SHGC is required.</div>
                    </div>
                </md-input-container>

                <!-- Visible Transmittance -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Visible Transmittance</label>
                    <input formatted-number
                           decimals="2"
                           name="visibleTransmittance"
                           ng-model="vm.construction.performance.visibleTransmittance"
                           required />
                    <div ng-messages="constructionform.visibleTransmittance.$error">
                        <div ng-message="md-maxlength">Visible Transmittance is required.</div>
                    </div>
                </md-input-container>

                <!-- Air Infiltration -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Air Infiltration</label>
                    <input formatted-number
                           decimals="2"
                           name="airInfiltration"
                           ng-model="vm.construction.performance.airInfiltration"
                           required />
                    <div ng-messages="constructionform.airInfiltration.$error">
                        <div ng-message="md-maxlength">Air Infiltration is required.</div>
                    </div>
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Insulation Card. Only show if type == Surface-->
    <md-card ng-if="vm.categoryIsSurface()">
        <md-card-header>
            <h2 style="margin: 0px;">Insulation</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.construction.insulationData.hasData"
                         ng-click="vm.toggle('insulationData', 'hasData');">

            </md-checkbox>
        </md-card-header>
        <md-card-content ng-if="vm.construction.insulationData.hasData == true">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Insulation Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Insulation Description</label>
                    <input type="text"
                           name="insulationDescription"
                           ng-model="vm.construction.insulationData.description"
                           md-maxlength="200"
                           required />
                    <div ng-messages="constructionform.insulationDescription.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 200.</div>
                    </div>
                </md-input-container>

                <!-- Insulation R-Value -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>{{vm.construction.category.canHavePerimeterInsulation ? 'Underfloor Insulation R-Value' : 'Insulation R-Value '}}</label>
                    <input formatted-number
                           decimals="2"
                           name="rValue"
                           ng-model="vm.construction.insulationData.rValue"
                           required />
                    <div ng-messages="constructionform.rValue.$error">
                        <div ng-message="md-maxlength">Insulation R-Value is required.</div>
                    </div>
                </md-input-container>

                <!-- Additional fields required for Ground Floor-->
                <div ng-if="vm.construction.category.canHavePerimeterInsulation">

                    <!-- Perimeter Insulation -->
                    <md-input-container class="md-block"
                                        flex-gt-sm>
                        <label>Perimeter Insulation</label>
                        <md-select required
                                   name="slabEdgeInsulation"
                                   ng-model="vm.construction.insulationData.hasSlabEdgeInsulation">
                            <md-option ng-value="null"></md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false"
                                       ng-click="vm.construction.insulationData.slabEdgeRValue = 0">
                                No
                            </md-option>
                        </md-select>
                        <div ng-messages="constructionform.slabEdgeInsulation.$error">
                            <div ng-message="md-maxlength">Perimeter Insulation is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Perimeter Insulation R-Value -->
                    <md-input-container ng-if="vm.construction.insulationData.hasSlabEdgeInsulation == true"
                                        class="md-block"
                                        flex-gt-sm>
                        <label>Perimeter Insulation R-Value</label>
                        <input formatted-number
                               decimals="2"
                               name="slabEdgeRValue"
                               ng-model="vm.construction.insulationData.slabEdgeInsulationRValue"
                               required />
                        <div ng-messages="constructionform.slabEdgeRValue.$error">
                            <div ng-message="md-maxlength">Perimeter Insulation R-Value is required.</div>
                        </div>
                    </md-input-container>

                </div>

                <!-- Bulk Insulation -->
                <md-input-container class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.construction.insulationData.isBulk">
                        Bulk
                    </md-checkbox>
                </md-input-container>

                <!-- Reflective Insulation -->
                <md-input-container class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.construction.insulationData.isReflective">
                        Reflective
                    </md-checkbox>
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Life Cycle and Cost -->
    <md-card ng-if="vm.categoryIsPermanentOpening() === false">
        <md-card-title>
            <h2 style="margin: 0px;">Life Cycle and Cost</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.construction.lifeCycleData.hasConstructionLifeCycleAndCost"
                         ng-click="vm.toggle('lifeCycleData', 'hasConstructionLifeCycleAndCost');">

            </md-checkbox>
        </md-card-title>
        <md-card-content ng-if="vm.construction.lifeCycleData.hasConstructionLifeCycleAndCost == true">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Cost Per -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Cost ($/{{vm.construction.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.construction.lifeCycleData.costPerM2" />
                </md-input-container>

                <!-- Embodied Energy (MJ/m2) -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied Energy (MJ/{{vm.construction.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.construction.lifeCycleData.embodiedEnergy" />
                </md-input-container>

                <!-- Embodied Water (L/m2) -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied Water (L/{{vm.construction.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.construction.lifeCycleData.embodiedWater" />
                </md-input-container>

                <!-- Embodied GHG Emissions -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied GHG Emission (kgCO<sub>2</sub>e/{{vm.construction.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.construction.lifeCycleData.embodiedGhgEmissions" />
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Chenath Scratch File -->
    <md-card ng-if="vm.categoryIsPermanentOpening() === false">
        <md-card-title>
            <h2 style="margin: 0px;">Chenath Scratch File</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.construction.chenathData.hasData"
                         ng-click="vm.toggle('chenathData', 'hasData');">

            </md-checkbox>
        </md-card-title>
        <md-card-content ng-if="vm.construction.chenathData.hasData == true">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Construction Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Construction Description</label>
                    <input type="text"
                           ng-model="vm.construction.chenathData.constructionDescription"
                           md-maxlength="200" />
                </md-input-container>

                <!-- Chenath Construction Code Range (Actually not sure how this is supposed to work?) -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Chenath Construction Code Range</label>
                    <md-select ng-model="vm.construction.chenathData.constructionTypeCodeRange"
                               ng-model-options="{trackBy: '$value.minimum'}">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-repeat="option in vm.chenathConstructionRanges()"
                                   ng-value="option.range">
                            {{option.title}} ({{option.range.minimum}} - {{option.range.maximum}})
                        </md-option>
                    </md-select>
                </md-input-container>

                <table class="table table-striped table-hover table-condensed">
                    <thead>
                    <tr>
                        <th style="width: 20%;">Assessment Software</th>
                        <th>Construction Code</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="software in vm.softwareUsingScratchFiles()">
                        <td>
                            <span>{{software.description}}</span>
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer" flex-gt-sm>
                                <input type="text"
                                       style="white-space: pre;"
                                       ng-model="vm.construction.chenathData.constructionCodePerSoftware[software.assessmentSoftwareCode.toLowerCase()]"
                                       ng-trim="false"/>
                            </md-input-container>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- EnergyPlus IDF File -->
    <md-card ng-if="vm.categoryIsPermanentOpening() === false">
        <md-card-title>
            <h2 style="margin: 0px;">EnergyPlus IDF File</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.construction.epData.hasData"
                         ng-click="vm.toggle('epData', 'hasData');">

            </md-checkbox>
        </md-card-title>
        <md-card-content ng-if="vm.construction.epData.hasData == true">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Energy Plus Construction Name -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Energy Plus Construction Name</label>
                    <input type="text"
                           ng-model="vm.construction.epData.energyPlusConstructionName"
                           md-maxlength="200" />
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Visualisation -->
    <md-card>
        <md-card-title>
            <h2 style="margin: 0px;">Visualisation</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.construction.visualisationData.hasData"
                         ng-click="vm.toggle('visualisationData', 'hasData');">

            </md-checkbox>
        </md-card-title>
        <md-card-content ng-if="vm.construction.visualisationData.hasData == true">

            < TODO >

        </md-card-content>
    </md-card>

    <div data-cc-widget-button-bar
         data-is-modal="vm.isModal">
        <div data-ng-show="vm.isBusy"
             data-cc-spinner="vm.spinnerOptions"></div>
        <md-button class="md-raised md-primary"
                   ng-disabled="constructionform.$invalid || vm.editPermission == false"
                   ng-show="vm.construction.deleted!=true"
                   ng-click="vm.save()">
            Save
        </md-button>
        <md-button ng-if="vm.categoryIsPermanentOpening() === false"
                   class="md-raised md-warn"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.construction.constructionId !=null && vm.construction.deleted!=true"
                   ng-confirm-click="vm.delete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to delete this record.">
            Delete
        </md-button>
        <md-button ng-if="vm.categoryIsPermanentOpening() === false"
                   class="md-raised"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.construction.deleted == true"
                   ng-confirm-click="vm.undoDelete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to RESTORE this record.">
            Restore
        </md-button>
        <md-button class="md-raised"
                   ng-click="vm.cancel()">
            Cancel
        </md-button>
        <div class="clearfix"></div>
    </div>

</form>
