// At one point in time these were referred to as 'Assessment Artificial Lighting' within
// the UI. Although they are now referred to as 'Building Floor Areas' we have not updated
// our naming within the codebase.
(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelBlock', {
      bindings: {
        theModel: '<',              // The 'StandardHomeModel' used as the basis of the option data
        variableOptions: '<',       // Available options for each required property.
        configuration: '@',         // 'default' | 'woh' - modifies available inputs
        project: '<',               // Project is required to limit available selections.
        onDataChanged: '&',         // Callback fired when any variable is changed.
        copyAcrossEnabled: '<',     // Whether to show Copy Across button
        copyAcrossData: '=?',       // Tell parent which option of which field to set in all buildings
        copyAcrossTrigger: '&',     // Trigger Copy Across in parent
        orientateEnabled: '<',
        required: '<',              // Specify whether all inputs are required or not
        disabled: '<',              // Specify whether inputs should be disabled  
        climateZoneIsInvalid: '=?', // Can set whether error shows from parent
        showNorthOffset: '<'
      },
      templateUrl: 'app/ui/energy-labs/standard-model-block-form.html',
      controller: StandardModelBlockController,
      controllerAs: 'vm'
    });

  StandardModelBlockController.$inject = ['common', '$timeout', 'energylabsservice'];

  function StandardModelBlockController(common, $timeout, energylabsservice) {

    let vm = this;

    // - TEST - //
    // - TEST - //
    // - TEST - //
    // - TEST - //
    // - TEST - //
    // - TEST - //
    // - TEST - //
    //vm.theModel.optionData.northOffset = "Multi-Orientation";

    vm.climateZoneIsInvalid = false;

    var ignoreFieldsJson = {
        "woh": [
            "blockType",
        ],
        "orientate": [
            "northOffset",
        ]
    }

    vm.suburbChanged = function () {
        energylabsservice.setBlockDataFromSuburb(
            vm.theModel,
            vm.theModel.suburbObject,
            vm.variableOptions,
            () => vm.onDataChanged()
        );          
    }

    vm.showAnyInputs = function () {
        let result = vm.showInputFor('blockType') ||
                     vm.showInputFor('northOffset') ||
                     vm.showInputFor('natHERSClimateZone') ||
                     vm.showInputFor('siteExposure') ||
                     vm.showInputFor('floorHeight');
        return result;
    }

    vm.showInputFor = function (key) {
        if (vm.project == null) {
            return vm.variableOptions[key] != null;
        } else {
            return vm.project.energyLabsSettings.properties[key] === true && !ignoreFieldsJson[vm.configuration]?.includes(key);
        }
    }   

    vm.copyOptionAcross = function (event, field, option) {
        // Tell parent the field and option so it can set on every other building
        vm.copyAcrossData.field = field;
        vm.copyAcrossData.option = option;
        $timeout(() => {
            // Now tell parent to copy across
            vm.copyAcrossTrigger();
            // Make sure option still clicked so dropdown collapses
            event.currentTarget.previousElementSibling.click();
        });
    }

    vm.copySuburbAcross = function () {
        // 'field' and 'option' already set in 'search-suburb.js', so just trigger parent
        $timeout(() => vm.copyAcrossTrigger());
    }

  }

})();