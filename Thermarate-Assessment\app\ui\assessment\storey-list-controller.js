// At one point in time these were referred to as 'Assessment Artificial Lighting' within
// the UI. Although they are now referred to as 'Building Floor Areas' we have not updated
// our naming within the codebase.
(function () {

    'use strict';
    angular
        .module('app')
        .component('storeyList', {
            bindings: {
                source: '<',                    // Object which contains the Storeys, NOT the Storeys themself.
                isTemplate: '<',                // If true, certain inputs are hidden.
                disabled: '<'
            },
            templateUrl: 'app/ui/assessment/storey-list.html',
            controller: StoreyList,
            controllerAs: 'vm'
        });

    StoreyList.$inject = ['common', 'assessmentcomplianceoptionservice'];

    function StoreyList(common, assessmentcomplianceoptionservice) {

        var vm = this;

        vm.deleteStorey = function (storey) {

            if(storey == null)
                return;

            vm.source.storeys = vm.source.storeys.filter(x => x.floor !== storey.floor);
            vm.source.numberOfStoreys = vm.source.storeys.length;
        }

        vm.copyStorey = function(storey) {

            let index = vm.source.storeys.indexOf(storey);

            let clone = { 
                name: getNextValidCopyName(storey.name),
                description: storey.description,
                heightOffGround: storey.heightOffGround,
                // floorArea: null,
                // ceilingArea: null,
            };

            // Insert below original item.
            vm.source.storeys.splice(index + 1, 0, clone);

            // Renumber storeys
            for(let i = 0; i < vm.source.storeys.length; i++) {
                vm.source.storeys[i].floor = i;
            }

        }

        function getNextValidCopyName(name) {

            const found = vm.source.storeys.find(x => x.name === name);

            return found == null
                ? name
                : getNextValidCopyName(name + " - Copy");

        }

        vm.addStorey = function () {

            if (vm.source.storeys == null)
                vm.source.storeys = [];

            let newStorey = {
                name: assessmentcomplianceoptionservice.floorNames[vm.source.storeys.length],
                description: null,
                floor: vm.source.storeys.length,
                heightOffGround: null,
            }

            vm.source.storeys.push(newStorey);
            vm.source.numberOfStoreys = vm.source.storeys.length;
        }
    }
})();