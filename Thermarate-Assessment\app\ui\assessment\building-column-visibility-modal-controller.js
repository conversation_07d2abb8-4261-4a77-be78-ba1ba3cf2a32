(function () {
    'use strict';
    var controllerId = 'BuildingColumnVisibilityCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'common', buildingColumnVisibilityController]);
    function buildingColumnVisibilityController($scope, $mdDialog, common) {
        var vm = this;

        // By copying the visibility property directly we can just do a shallow copy to avoid mutating
        // the original parent object (ie. useful for when modal is cancelled).
        vm.hiddenTableColumns = {...$scope.parent.hiddenTableColumns};

        const parent = $scope.parent;

        // ID changes depending on which tab we are coming from
        let categoryCode = '';
        if (parent.hasOwnProperty('category')) {
            // Construction tab or Openings tab
            categoryCode = parent.category.constructionCategoryCode;
        } else if (parent.hasOwnProperty('serviceCategory')) {
            // Services tab
            categoryCode = parent.serviceCategory.serviceCategoryCode;
        } else {
            console.error('Cannot find Category Code');
            vm.cancel();
        }

        // serviceCategory: {serviceCategoryCode

        vm.showableColumns = [];

        function initialise() {

            if (!allParentColumns.hasOwnProperty(categoryCode)) {
                console.error('categoryCode does not exist in \'allParentColumns\'');
                return;
            }

            // Initialise the list of columns to hide.
            vm.showableColumns = allParentColumns[categoryCode];

            // Copy any true values from hiddenTableColumns over to allParentColumns
            for (var key in vm.hiddenTableColumns) {
                // If the key exists in showableColumns and its value is false, set it to false in showableColumns
                if (vm.showableColumns.hasOwnProperty(key) && vm.hiddenTableColumns[key] === true) {
                    vm.showableColumns[key].hidden = true;
                }
            }
        }

        // ---

        vm.toggleColumnVisibility = function(key, value) {
            vm.showableColumns[key].hidden = value.hidden;
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.confirm = function () {
            // Only add columns that are hidden to the response.
            const hiddenColumns = {};

            for (var key in vm.showableColumns) {
                // Only add columns that are hidden to the response.
                if (vm.showableColumns[key].hidden === true)
                    hiddenColumns[key] = true;
            }

            $mdDialog.hide(hiddenColumns);
        };

        // Hardcoded mapping of every column's id and label (can change per construction category).
        // Could maybe convert to having a master list, and then overrides for category that have different names,
            // instead of repeating every field.
        // i.e.
        // <construction category code>: {
        //     <column property>: name: <Display Label>, hidden: <bool>,
        // }
        let allParentColumns = {
            // Construction Tab
            "Roof": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "tilt": { name: `Roof Pitch (${common.symbol("degrees")})`, hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "CeilingRoofAbove": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "tilt": { name: `Ceiling Pitch (${common.symbol("degrees")})`, hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "CeilingNeighbourAbove": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "tilt": { name: `Ceiling Pitch (${common.symbol("degrees")})`, hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "ExteriorWall": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "horizontalShading": { name: "Horizontal Shading", hidden: false },
                "verticalShading": { name: "Vertical Shading", hidden: false },
                "leftWingWall.shading": { name: "Left Wing Shading", hidden: false },
                "rightWingWall.shading": { name: "Right Wing Shading", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "SubfloorWall": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey Above", hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorWall": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey Above", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorWallAdjacentToRoofSpace": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey Above", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorWallAdjacentToSubfloorSpace": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey Above", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorWallAdjacentToNeighbour": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey Above", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "GroundFloor": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "ExteriorFloor": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "ExteriorFloorElevated": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "IntermediateFloor": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false }
            },
            "IntermediateFloorNeighbourBelow": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "grossArea": { name: `Gross Area (m${common.symbol("squared")})`, hidden: false },
                "netArea": { name: `Net Area (m${common.symbol("squared")})`, hidden: false },
            },
            // Openings Tab
            "ExteriorGlazing": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "horizontalShading": { name: "Horizontal Shading", hidden: false },
                "verticalShading": { name: "Vertical Shading", hidden: false },
                "leftWingWall.shading": { name: "Left Wing Shading", hidden: false },
                "rightWingWall.shading": { name: "Right Wing Shading", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorGlazing": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "ExteriorDoor": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "horizontalShading": { name: "Horizontal Shading", hidden: false },
                "verticalShading": { name: "Vertical Shading", hidden: false },
                "leftWingWall.shading": { name: "Left Wing Shading", hidden: false },
                "rightWingWall.shading": { name: "Right Wing Shading", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "InteriorDoor": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "VerticalOpening": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "HorizontalOpening": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "adjacentZoneNumber": { name: "Adjacent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "height": { name: "Height (m)", hidden: false },
                "width": { name: "Width (m)", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "Skylight": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "hasInteriorShades": { name: "Interior Shading", hidden: false },
                "hasExteriorShades": { name: "Exterior Shading", hidden: false },
                "shaftLength": { name: "Shaft Length (m)", hidden: false },
                "shaftArea": { name: `Shaft Area (m${common.symbol("squared")})`, hidden: false },
                "shaftReflectance": { name: "Shaft Reflectance", hidden: false },
                "shaftWallRValue": { name: "Shaft Resistance", hidden: false },
                "hasDiffuser": { name: "Diffuser", hidden: false },
                "tilt": { name: `Pitch (${common.symbol("degrees")})`, hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            "RoofWindow": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "hasInteriorShades": { name: "Interior Shading", hidden: false },
                "hasExteriorShades": { name: "Exterior Shading", hidden: false },
                "shaftLength": { name: "Shaft Length (m)", hidden: false },
                "shaftArea": { name: `Shaft Area (m${common.symbol("squared")})`, hidden: false },
                "shaftReflectance": { name: "Shaft Reflectance", hidden: false },
                "shaftWallRValue": { name: "Shaft Resistance", hidden: false },
                "hasDiffuser": { name: "Diffuser", hidden: false },
                "tilt": { name: `Pitch (${common.symbol("degrees")})`, hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "sector": { name: "Sector", hidden: false },
                "openability": { name: "Openability (%)", hidden: false },
                "grossArea": { name: `Area (m${common.symbol("squared")})`, hidden: false }
            },
            // Services Tab (only categories with children)
            // Most fields are calculated... (they don't need to match the property exactly they are just a lookup)
            "ArtificialLighting": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "serviceControlDevice": { name: "Control Device", hidden: false },
                "adjustmentFactor": { name: "Adjustment Factor", hidden: false },
                "number": { name: "Number", hidden: false },
                "cutoutArea": { name: `Cut Out Area (m${common.symbol("squared")})`, hidden: false }
            },
            "ExhaustFans": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "number": { name: "Number", hidden: false },
                "cutoutArea": { name: `Cut Out Area (m${common.symbol("squared")})`, hidden: false }
            },
            "CeilingVents": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "number": { name: "Number", hidden: false },
                "cutoutArea": { name: `Cut Out Area (m${common.symbol("squared")})`, hidden: false }
            },
            "CeilingFans": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "number": { name: "Number", hidden: false }
            },
            "PhotovoltaicSystem": {
                "elementNumber": { name: "Element", hidden: false },
                "parentZoneId": { name: "Parent Zone", hidden: false },
                "storey": { name: "Storey", hidden: false },
                "tilt": { name: `Panel Pitch (${common.symbol("degrees")})`, hidden: false },
                "azimuth": { name: `Azimuth (${common.symbol("degrees")})`, hidden: false },
                "panelArea": { name: `Panel Area (m${common.symbol("squared")})`, hidden: false }
            },
        }

        initialise();

    }
})();