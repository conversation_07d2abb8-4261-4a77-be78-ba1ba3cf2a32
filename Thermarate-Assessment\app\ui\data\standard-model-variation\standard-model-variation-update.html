<form name="standardModelform"
      class="main-content-wrapper"
      style="min-width:600px;"
      novalidate data-ng-controller='StandardModelVariationUpdateCtrl as vm'>

    <!-- Close button for modal -->
    <md-toolbar ng-if="vm.isModal">
        <div class="md-toolbar-tools">
            <h2>{{vm.title}}</h2>
            <span flex></span>
            <md-button class="md-icon-button" ng-click="vm.cancel()">
                <md-icon>close</md-icon>
            </md-button>
        </div>
    </md-toolbar>

    <div class="widget home-design-variation-body" ng-cloak>

        <!-- Breadcrumbs -->
        <div
            ng-if="!vm.newRecord"
            ng-style="{'visibility': vm.project.clientName == null || vm.project.projectName == null || vm.standardModel.isVariationOfHomeModelTitle == null ? 'hidden' : 'visible'}"
            class="navigation-text"
            style="margin-left:0 !important"
        >
            <div ng-click="vm.navigateAttempt('client-listform', null)" class="clickable">Clients</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId})" class="clickable">{{vm.project.clientName}}</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId, tabIndex: 7})" class="clickable">EnergyLab</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('project-updateform', {projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}}</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('standard-model-updateform', {standardHomeModelId: vm.standardModel.isVariationOfHomeModelId})" class="clickable">{{vm.standardModel.isVariationOfHomeModelTitle}}</div>
            <div>></div>
            <b>{{vm.standardModel.title}}</b>
        </div>

        <!-- The Basics -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header ng-if="!vm.newRecord">
                <span class="md-title">Home Design Variation</span>
            </md-card-header>
            <md-card-content>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  Home Design Variation  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Home Design Variation</label>
                        <input type="text"
                               name="title"
                               ng-model="vm.standardModel.title"
                               md-autofocus
                               md-maxlength="100"
                               ng-required />
                        <div ng-messages="standardModelform.title.$error">
                            <div ng-message="required">Title is required.</div>
                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                        </div>
                    </md-input-container>

                    <!-- Description -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Description</label>
                        <input type="text" name="description"
                               ng-model="vm.standardModel.description"
                               md-maxlength="500"/>
                        <div ng-messages="standardModel.description.$error">
                            <div ng-message="md-maxlength">Too many characters entered, max length is 500.</div>
                        </div>
                    </md-input-container>

                    <!--  Home Design  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Home Design</label>
                        <input type="text"
                               name="title"
                               ng-model="vm.standardModel.isVariationOfHomeModelTitle"
                               ng-disabled="true" />
                    </md-input-container>

                    <!--  Project  -->
                    <md-autocomplete md-input-name="projectId"
                                     required
                                     ng-disabled="true"
                                     md-input-minlength="2"
                                     md-min-length="0"
                                     md-selected-item="vm.standardModel.project"
                                     md-search-text="vm.projectIdSearchText"
                                     md-items="item in vm.getProjects(vm.projectIdSearchText)"
                                     md-item-text="item.projectName"
                                     md-require-match
                                     md-floating-label="Project"
                                     md-selected-item-change="vm.projectChanged();">
                        <md-item-template>
                            <span md-highlight-text="vm.projectIdSearchText">{{item.projectName}}</span>
                        </md-item-template>
                        <div ng-messages="standardModelform.projectId.$error">
                            <div ng-message="required">Project is required.</div>
                        </div>
                    </md-autocomplete>

                    <!-- Active -->
                    <div>
                        <label style="font-size: 9px;">Active</label>
                        <md-switch style="width: fit-content;" ng-model="vm.standardModel.isActive" ng-disabled="!vm.parentModel.isActive">
                                <span ng-show="vm.standardModel.isActive === false">The model and all options will <b>not</b> appear in the EnergyLab.</span>
                                <span ng-show="vm.standardModel.isActive === true">The model and all options will appear in the EnergyLab.</span>
                        </md-switch>
                    </div>

                    <!-- Home Plan Image -->
                    <div style="margin-top: 1rem;">
                        <div class="plan-images-container">

                            <div ng-repeat="homeModelFile in vm.standardModel.standardHomeModelFiles | filter: { deleted: false } track by $index"
                                 lr-drag-src="standardHomeModelFiles"
                                 lr-drop-target="standardHomeModelFiles"
                                 lr-drag-data="vm.standardModel.standardHomeModelFiles"
                                 lr-drop-success="vm.setImageSortOrder(vm.standardModel.standardHomeModelFiles)"
                                 lr-match-property="sortOrder"
                                 lr-match-value="{{homeModelFile.sortOrder}}"
                                 lr-index="vm.standardModel.standardHomeModelFiles.indexOf(homeModelFile)"
                                 class="reorderable-grid-item">
                                <generic-file-upload class="vertically-condensed"
                                                     label="Home Design Plan Image"
                                                     accept-array="false"
                                                     category="'Images'"
                                                     classification="'Home Design Plan'"
                                                     required-message="''"
                                                     force-edit="true"
                                                     file-object="homeModelFile"
                                                     prop-name="file"
                                                     is-required="true"
                                                     accept="'.png,.jpg,.jpeg,.svg'"></generic-file-upload>

                                <div style="display: grid; grid-template-columns: 1fr; justify-items: center; justify-content: center; margin-top: 20px; margin-bottom: 30px;">
                                    <img ng-if="homeModelFile.file.url != null"
                                         style="height: 250px; width: 100%; object-fit: contain;"
                                         class="draggable"
                                         src="{{homeModelFile.file.url}}"
                                         alt="Image of home plan"/>
                                    <div ng-if="homeModelFile.file.url == null"
                                         style="height: 250px; width: 125px; border: 2px solid grey;"
                                         class="draggable">
                                    </div>
                                    <div>
                                        <span style="font-weight: bold;">Floor {{homeModelFile.sortOrder}}</span>
                                    </div>
                                    <md-input-container ng-show="vm.drawingsCount >= 2" class="md-block" style="margin-top: 22px; margin-bottom: -8px" flex-gt-sm>
                                        <label>Label</label>
                                        <input type="text" name="description"
                                               ng-model="homeModelFile.label"
                                               md-maxlength="100"/>
                                        <div ng-messages="homeModelFile.description.$error">
                                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-button class="md-warn"
                                               ng-click="vm.deletePlanImage(homeModelFile)">
                                        Delete
                                    </md-button>
                                </div>

                            </div>

                        </div>

                        <div ng-if="vm.standardModel.standardHomeModelFiles.length > 1"
                             style="text-align: center; color: grey">Drag and Drop to re-order</div>

                        <md-button class="md-primary md-raised"
                                   ng-click="vm.addPlanImage()">
                            Add Plan Image
                        </md-button>

                    </div>

                    <!-- 3d Model -->
                    <div style="margin-top: 18px">
                        <label style="font-size: 9px">3D Model</label>
                        <md-switch ng-model="vm.standardModel.view3dFloorPlans" ng-disabled="!vm.parentModel.view3dFloorPlans" style="width: fit-content; margin-top: 5px;">
                        </md-switch>
                    </div>

                    <!--  Floorplanner Link  -->
                    <md-input-container ng-if="vm.standardModel.view3dFloorPlans" class="md-block" flex="100" style="margin-bottom: 0;">
                        <label>Floorplanner Link</label>
                        <input type="text"
                               name="floorplannerLink"
                               ng-model="vm.standardModel.floorplannerLink"
                               ng-blur="vm.floorplannerLinkBlur()"
                               ng-click="vm.floorplannerLinkEditing = true"
                               md-autofocus
                               ng-required />
                        <div ng-messages="vm.standardModel.view3dFloorPlans && standardModelform.floorplannerLink.$error">
                            <div ng-message="required">Floorplanner Link is required.</div>
                        </div>
                        <div ng-if="vm.floorplannerLinkWrongSite" style="margin-top: 5px;" class="label-red">This link must be a Floorplanner link.</div>
                        <div ng-if="vm.floorplannerLinkInvalid" style="margin-top: 5px;" class="label-red">This link is invalid.</div>
                    </md-input-container>

                    <!-- Cost Estimate Toggle -->
                    <div ng-style="{'margin-top': vm.standardModel.view3dFloorPlans ? '0' : '18px'}">
                        <label style="font-size: 9px">Cost Estimate</label>
                        <md-switch ng-model="vm.standardModel.costEstimateEnabled"
                                   ng-disabled="!vm.parentModel.costEstimateEnabled"
                                   style="width: fit-content;">
                            <span ng-show="vm.standardModel.costEstimateEnabled === false">Cost estimates will not be shown to users, even if it is present.</span>
                            <span ng-show="vm.standardModel.costEstimateEnabled === true">Cost estimates will be shown.</span>
                        </md-switch>
                    </div>

                    <!-- Design Insights Toggle -->
                    <div ng-style="{'margin-top': vm.standardModel.view3dFloorPlans ? '0' : '18px'}">
                        <label style="font-size: 9px">Design Insights</label>
                        <md-switch ng-model="vm.standardModel.variableMetadata.designInsightsEnabled"
                                   ng-disabled="!vm.parentModel.variableMetadata.designInsightsEnabled"
                                   style="width: fit-content;">
                            <span ng-show="vm.standardModel.variableMetadata.designInsightsEnabled === false">Design insights will not be shown to users, even if it is present.</span>
                            <span ng-show="vm.standardModel.variableMetadata.designInsightsEnabled === true">Design insights will be shown.</span>
                        </md-switch>
                    </div>

                    <!-- Upload actual data set + see/set variable options -->
                    <div ng-if="!vm.newRecord"
                         style="text-align: center; color: dimgrey; font-size: 11px; ">
                        <span>Excel file can be uploaded after Home Design is saved.</span>
                    </div>
                </fieldset>

                <div class="col-md-12" ng-if="vm.newRecord==false">
                    <div rd-display-created-modified ng-model="vm.standardModel"></div>
                </div>

            </md-card-content>
        </md-card>

        <!-- Variation Selections -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <span class="md-title">Variation Selections</span>
            </md-card-header>
            <md-card-content style="display:flex; flex-direction:column; row-gap:12px;">

                    <!-- Floorplan -->
                    <md-input-container ng-if="vm.variationOptionsSettings.floorplanIsActive" class="md-block" flex="100">
                        <label>Floor Plan</label>
                        <md-select ng-required="true"
                                   class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                   ng-model="vm.standardModel.variationFloorplanId">
                            <md-option ng-repeat="option in vm.getAvailableFloorplanOptions() track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Design Option -->
                    <md-input-container ng-if="vm.variationOptionsSettings.designOptionIsActive" class="md-block" flex="100">
                        <label>Design Option</label>
                        <md-select ng-required="true"
                                   class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                   ng-model="vm.standardModel.variationDesignOptionId">
                            <md-option ng-repeat="option in vm.getAvailableDesignOptionOptions() track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Facade -->
                    <md-input-container ng-if="vm.variationOptionsSettings.facadeIsActive" class="md-block" flex="100">
                        <label>Facade</label>
                        <md-select ng-required="true"
                                   class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                   ng-model="vm.standardModel.variationFacadeId">
                            <md-option ng-repeat="option in vm.getAvailableFacadeOptions() track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Specification -->
                    <md-input-container ng-if="vm.variationOptionsSettings.specificationIsActive" class="md-block" flex="100">
                        <label>Specification</label>
                        <md-select ng-required="true"
                                   class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                   ng-model="vm.standardModel.variationSpecificationId" >
                            <md-option ng-repeat="option in vm.getAvailableSpecificationOptions() track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Configuration -->
                    <md-input-container ng-if="vm.variationOptionsSettings.configurationIsActive" class="md-block" flex="100">
                        <label>Configuration</label>
                        <md-select ng-required="true"
                                   class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                   ng-model="vm.standardModel.variationConfigurationId">
                            <md-option ng-repeat="option in vm.getAvailableConfigurationOptions() track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                        </md-select>
                    </md-input-container>

            </md-card-content>
        </md-card>

        <!-- Category -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <!-- Title -->
                <span class="md-title">Categories</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyCategoriesToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyCategoriesToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllCategories()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:35px;">
                <div>
                    <md-checkbox ng-repeat="category in vm.categories"
                                 ng-model="vm.standardModel[category]"
                                 class="checkbox-aligner"
                                 style="align-self: end;">
                        {{vm.featureName(category)}}
                    </md-checkbox>
                </div>
            </md-card-content>
        </md-card>

        <!-- Design Metadata -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <!-- Title -->
                <span class="md-title">Design</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyDesignMetadataToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyDesignMetadataToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllDesignMetadata()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr 1fr;">

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  Display Floor Area  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Display Floor Area (m<sup>2</sup>)</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.displayFloorArea"
                                   ng-attr-placeholder="{{vm.standardModel.displayFloorAreaVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.displayFloorAreaVarRefJson != null"
                                   ng-required="vm.standardModel.displayFloorAreaVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.displayFloorAreaVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'displayFloorAreaVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.displayFloorAreaVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'displayFloorAreaVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.displayFloorArea = null; vm.standardModel.displayFloorAreaVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Whole-of-Home Floor Area  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Whole-of-Home Floor Area (m<sup>2</sup>)</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.wohFloorArea"
                                   ng-attr-placeholder="{{vm.standardModel.wohFloorAreaVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.wohFloorAreaVarRefJson != null"
                                   ng-required="vm.standardModel.wohFloorAreaVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.wohFloorAreaVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'wohFloorAreaVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.wohFloorAreaVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'wohFloorAreaVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.wohFloorArea = null; vm.standardModel.wohFloorAreaVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Total Floor Area of Habitable Rooms  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Total Floor Area of Habitable Rooms (m<sup>2</sup>)</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.floorAreaHabitableRooms"
                                   ng-attr-placeholder="{{vm.standardModel.floorAreaHabitableRoomsVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.floorAreaHabitableRoomsVarRefJson != null"
                                   ng-required="vm.standardModel.floorAreaHabitableRoomsVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.floorAreaHabitableRoomsVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'floorAreaHabitableRoomsVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.floorAreaHabitableRoomsVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'floorAreaHabitableRoomsVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.floorAreaHabitableRooms = null; vm.standardModel.floorAreaHabitableRoomsVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Site Cover   -->
                    <md-input-container class="md-block" flex="100">
                        <label>Site Cover (m<sup>2</sup>)</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.siteCover"
                                   ng-attr-placeholder="{{vm.standardModel.siteCoverVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.siteCoverVarRefJson != null"
                                   ng-required="vm.standardModel.siteCoverVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.siteCoverVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'siteCoverVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.siteCoverVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'siteCoverVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.siteCover = null; vm.standardModel.siteCoverVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!-- NCC Building Classification -->
                    <md-input-container class="md-block">
                        <label>NCC Building Classification</label>
                        <md-select ng-model="vm.standardModel.nccBuildingClassification"
                                   ng-required="true">
                            <md-option ng-repeat="v in vm.wohConstants.nccBuildingClassifications"
                                       ng-value="v">
                                {{v}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Lowest Living Floor Type -->
                    <md-input-container class="md-block">
                        <label>Lowest Living Area Floor Type</label>
                        <md-select name="lowestLivingAreaFloorType"
                                   ng-required="true"
                                   ng-model="vm.standardModel.lowestLivingAreaFloorType">
                            <md-option value="CSOG Only">CSOG Only</md-option>
                            <md-option value="Suspended Only">Suspended Only</md-option>
                            <md-option value="CSOG & Suspended">CSOG & Suspended</md-option>
                        </md-select>
                    </md-input-container>

                </fieldset>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  House Width  -->
                    <md-input-container class="md-block" flex="100">
                        <label>House Width (m)</label>
                        <input ng-model="vm.standardModel.houseWidth"
                               ng-required="true" />
                    </md-input-container>

                    <!--  House Length  -->
                    <md-input-container class="md-block" flex="100">
                        <label>House Length (m)</label>
                        <input ng-model="vm.standardModel.houseDepth"
                               ng-required="true" />
                    </md-input-container>

                    <!--  Lot Width  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Lot Width (m)</label>
                        <input ng-model="vm.standardModel.width"
                               ng-required="true" />
                    </md-input-container>

                    <!--  Lot Length  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Lot Length (m)</label>
                        <input ng-model="vm.standardModel.depth"
                               ng-required="true" />
                    </md-input-container>

                </fieldset>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  Storeys  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Storeys</label>
                        <input ng-model="vm.standardModel.storeys"
                               ng-change="vm.storeysChanged()"
                               formatted-number
                               decimals="0"
                               ng-required="true" />
                    </md-input-container>

                    <!--  Number of Bedrooms  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Bedrooms</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.numberOfBedrooms"
                                   ng-attr-placeholder="{{vm.standardModel.numberOfBedroomsVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.numberOfBedroomsVarRefJson != null"
                                   ng-required="vm.standardModel.numberOfBedroomsVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.numberOfBedroomsVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'numberOfBedroomsVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.numberOfBedroomsVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'numberOfBedroomsVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.numberOfBedrooms = null; vm.standardModel.numberOfBedroomsVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Number of Bathrooms  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Bathrooms</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.numberOfBathrooms"
                                   ng-attr-placeholder="{{vm.standardModel.numberOfBathroomsVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.numberOfBathroomsVarRefJson != null"
                                   ng-required="vm.standardModel.numberOfBathroomsVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.numberOfBathroomsVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'numberOfBathroomsVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.numberOfBathroomsVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'numberOfBathroomsVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.numberOfBathrooms = null; vm.standardModel.numberOfBathroomsVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Living Areas  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Living Areas</label>
                        <div class="var-ref-field" style="margin-bottom:24px; min-height:30px;">
                            <!-- Single input with conditional value -->
                            <input ng-model="vm.standardModel.livingAreas"
                                   ng-attr-placeholder="{{vm.standardModel.livingAreasVarRefJson != null ? '-' : ''}}"
                                   number-only
                                   ng-disabled="vm.standardModel.livingAreasVarRefJson != null"
                                   ng-required="vm.standardModel.livingAreasVarRefJson == null" />
                            <!-- Manual: Button -->
                            <div ng-if="vm.standardModel.livingAreasVarRefJson == null" class="var-ref-select-button is-absolute" ng-click="vm.openVariableSelectModal(vm.standardModel, 'livingAreasVarRefJson')">
                                <img src="/content/images/select.png" />
                            </div>
                            <!-- Reference: Menu -->
                            <md-menu ng-if="vm.standardModel.livingAreasVarRefJson != null" class="var-ref-three-dot-menu is-absolute">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Select -->
                                    <md-menu-item><md-button ng-click="vm.openVariableSelectModal(vm.standardModel, 'livingAreasVarRefJson')">
                                        Select
                                    </md-button></md-menu-item>
                                    <!-- Clear -->
                                    <md-menu-item><md-button ng-click="vm.standardModel.livingAreas = null; vm.standardModel.livingAreasVarRefJson = null;">
                                        <span style="color: orangered;">Clear</span>
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>
                    </md-input-container>

                    <!--  Number of Parking Spaces  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Parking Spaces</label>
                        <input ng-model="vm.standardModel.numberOfGarageSpots"
                               formatted-number
                               decimals="0"
                               ng-required="true" />
                    </md-input-container>

                </fieldset>

            </md-card-content>
        </md-card>

        <!-- Drawing Areas -->
        <md-card>
            <md-card-header>
                <!-- Title -->
                <span class="md-title">Drawing Areas</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyDrawingAreasToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyDrawingAreasToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllDrawingAreas()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content>

                <table class="table table-striped table-condensed table-data-centered drawingAreasTable">
                    <colgroup>
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                    </colgroup>
                    <thead>
                        <tr>
                            <th>Storey</th>
                            <th>House (m<sup>2</sup>)</th>
                            <th>Garage (m<sup>2</sup>)</th>
                            <th>House & Garage (m<sup>2</sup>)</th>
                            <th>Alfresco (m<sup>2</sup>)</th>
                            <th>Porch (m<sup>2</sup>)</th>
                            <th>House Perimiter (m)</th>
                            <th>Ceiling Height (m)</th>
                            <th>House Facade (m<sup>2</sup>)</th>
                            <th>Roof Pitch ({{vm.symbol("degrees")}})</th>
                            <th>Roof - Horizontal (m<sup>2</sup>)</th>
                            <th>Roof - Pitched (m<sup>2</sup>)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="row in vm.standardModel.drawingAreas | filter: { willDelete: false } track by $index">
                            <td> {{row.storeyName}} </td>
                            <td> <input ng-model="row.houseArea"      formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalHouse();vm.calcHouseGarage(row);vm.calcTotalHouseGarage();vm.updateCostEstimateQuantities();" /> </td>
                            <td> <input ng-model="row.garageArea"     formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalGarage();vm.calcHouseGarage(row);vm.calcTotalHouseGarage();vm.updateCostEstimateQuantities();" /> </td>
                            <td> {{row.houseGarage}} </td>
                            <td> <input ng-model="row.alfrescoArea"   formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalAlfresco();vm.updateCostEstimateQuantities();" /> </td>
                            <td> <input ng-model="row.porchArea"      formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalPorch();vm.updateCostEstimateQuantities();" /> </td>
                            <td> <input ng-model="row.housePerimeter" formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalHousePerimeter();vm.calcHouseFacade(row);vm.calcTotalHouseFacade();vm.updateCostEstimateQuantities();" /> </td>
                            <td> <input ng-model="row.ceilingHeight"  formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcHouseFacade(row);vm.calcTotalHouseFacade();vm.updateCostEstimateQuantities();"/> </td>
                            <td> {{row.houseFacade}} </td>
                            <td> <input ng-model="row.roofPitch"      formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcRoofPitched(row);vm.calcTotalRoofPitched();vm.updateCostEstimateQuantities();" /> </td>
                            <td> <input ng-model="row.roofHorizontal" formatted-number allow-null="true" ng-disabled="vm.disabled" ng-change="vm.calcTotalRoofHorizontal();vm.calcRoofPitched(row);vm.calcTotalRoofPitched();vm.updateCostEstimateQuantities();" /> </td>
                            <td> {{row.roofPitched}} </td>
                        </tr>
                        <tr>
                            <td>Whole Building</td>
                            <td>{{vm.drawingAreasTotals.house}}</td>
                            <td>{{vm.drawingAreasTotals.garage}}</td>
                            <td>{{vm.drawingAreasTotals.houseGarage != null ? vm.drawingAreasTotals.houseGarage : ""}}</td>
                            <td>{{vm.drawingAreasTotals.alfresco}}</td>
                            <td>{{vm.drawingAreasTotals.porch}}</td>
                            <td>{{vm.drawingAreasTotals.housePerimeter}}</td>
                            <td>-</td>
                            <td>{{vm.drawingAreasTotals.houseFacade}}</td>
                            <td>-</td>
                            <td>{{vm.drawingAreasTotals.roofHorizontal}}</td>
                            <td>{{vm.drawingAreasTotals.roofPitched}}</td>
                        </tr>
                    </tbody>
                </table>

            </md-card-content>
        </md-card>

        <!-- Design Metrics Metadata -->
        <md-card ng-if="!vm.newRecord">
            <md-card-header>
                <!-- Title -->
                <span class="md-title">Design Metrics</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Scratch Import -->
                        <md-menu-item><md-button ng-click="vm.openScratchImportModal()">
                            Scratch Import
                        </md-button></md-menu-item>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyDesignMetricsToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyDesignMetricsToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllDesignMetrics()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content>

                <!-- No Data -->
                <div ng-if="vm.zoneSummaryData.zoneSummary == null" style="margin-top: -18px; font-size: 16px;">
                    No data imported.
                </div>

                <!-- Zone Summary -->
                <span ng-if="vm.zoneSummaryData.zoneSummary != null" style="margin-right:15px; font-size:18px;">
                    Zone Summary
                </span>
                <md-tabs md-dynamic-height ng-if="vm.zoneSummaryData.zoneSummary != null" style="margin-bottom:10px;">
                    <md-tab ng-repeat="summaryGroup in vm.knownBuildingSummaryGroups">
                        <md-tab-label>
                            <span>{{vm.zoneSummaryData.zoneSummary[summaryGroup].groupName}}</span>
                        </md-tab-label>
                        <md-tab-body>
                            <table class="table table-striped table-hover table-condensed">

                                <!-- Grouped Header-->
                                <thead>

                                    <tr>
                                        <th class="text-left"
                                            colspan="{{summaryGroup == 'interiorZones' ? 4 : 3}}">
                                        </th><!-- � -->

                                        <th style="text-align:center"
                                            colspan="2">
                                            Floor Area
                                        </th>

                                        <th colspan="2" style="text-align:center">Volume</th>

                                        <th colspan="2" style="text-align:center">Exterior Wall Area</th>

                                        <th colspan="2" style="text-align:center">Exterior Glazing Area</th>

                                        <th colspan="2" style="text-align:center"></th>

                                    </tr>

                                    <!-- 'Subheader' Row -->
                                    <tr style="font-weight: bold;">
                                        <td class="text-center" style="min-width: 110px; width: 110px;">Storey</td>

                                        <td ng-if="summaryGroup == 'interiorZones'"
                                            class="text-center"
                                            style="min-width: 110px; width: 110px;">Zone Number</td>

                                        <td class="text-center" style="min-width: 180px; width: 180px;">
                                            {{vm.zoneSummaryData.zoneSummary[summaryGroup].descriptionHeading}}
                                        </td>

                                        <td style="text-align: center; min-width: 90px; width: 90px;">Zone Count</td>

                                        <td style="text-align: right;">(m<sup>2</sup>)</td>
                                        <td style="text-align: left;">(%)</td>

                                        <!-- Volume m3 & % -->
                                        <td style="text-align: right;">(m<sup>3</sup>)</td>
                                        <td style="text-align:  left;">(%)</td>

                                        <!-- Exterior wall area m2 & % -->
                                        <td style="text-align: right;">(m<sup>2</sup>)</td>
                                        <td style="text-align: left;">(%)</td>

                                        <!-- Exterior glazing area m2 & % -->
                                        <td style="text-align: right;">(m<sup>2</sup>)</td>
                                        <td style="text-align: left;">(%)</td>

                                        <!-- Exterior glazing area m2 & % -->
                                        <td style="text-align: center; width: 180px;">Glass-Exterior Wall (%)</td>
                                        <td style="text-align: center; width: 180px;">Glass-Floor Area (%)</td>
                                    </tr>

                                </thead>

                                <tbody ng-repeat="storeyGroup in vm.zoneSummaryData.zoneSummary[summaryGroup].rows track by $index" class="border-last">

                                    <!-- Groups -->
                                    <tr ng-repeat="row in storeyGroup.zones track by $index"
                                        class="border-last">

                                        <!-- Group -->
                                        <td data-title="Description"
                                            ng-if="$index == 0"
                                            rowspan="{{storeyGroup.zones.length}}"
                                            class="text-center border-cell">
                                            <span>{{storeyGroup.name}}</span>
                                        </td>

                                        <!-- Zone Number -->
                                        <td ng-if="summaryGroup == 'interiorZones'">
                                            {{row.zoneNumber}}
                                        </td>

                                        <!-- Description (Varies) -->
                                        <td>
                                            {{row.description}}
                                        </td>

                                        <td style="text-align: center;">
                                            {{row.zoneCount}}
                                        </td>

                                        <!-- Floor Area -->
                                        <td data-title="Floor Area"
                                            style="text-align: right;">
                                            {{row.floorArea.toFixed(2);}}
                                        </td>

                                        <!-- Floor Area % -->
                                        <td data-title="Floor Area"
                                            style="text-align: left;">
                                            {{row.floorAreaPercent.toFixed(2);}}
                                        </td>

                                        <!-- Volume m2 & % -->
                                        <td style="text-align: right;">{{row.volume.toFixed(2);}}</td>
                                        <td style="text-align: left;">{{row.volumePercent.toFixed(2);}}</td>

                                        <!-- Exterior wall area m2 & % -->
                                        <td style="text-align: right;">{{row.exteriorWallArea.toFixed(2);}}</td>
                                        <td style="text-align: left;">{{row.exteriorWallAreaPercent.toFixed(2);}}</td>

                                        <!-- Exterior Glazing area m2 & % -->
                                        <td style="text-align: right;">{{row.exteriorGlazingArea.toFixed(2);}}</td>
                                        <td style="text-align: left;">{{row.exteriorGlazingAreaPercent.toFixed(2);}}</td>

                                        <!-- Glass vs X ratio %'s -->
                                        <td style="text-align: center;">{{row.glassExteriorWallAreaPercent.toFixed(2);}}</td>
                                        <td style="text-align:  center;">{{row.glassFloorAreaPercent.toFixed(2);}}</td>

                                    </tr>

                                </tbody>

                            </table>
                        </md-tab-body>
                    </md-tab>
                </md-tabs>

                <!-- Envelope Summary -->
                <span ng-if="vm.zoneSummaryData.zoneSummary != null" style="margin-right:15px; font-size:18px;">
                      Envelope Summary
                </span>
                <div ng-if="vm.zoneSummaryData.zoneSummary != null" style="margin-bottom: 15px;">

                    <!-- Table & Filters -->
                    <div class="filter-grid-item">
                        <div>
                            <!-- Table -->
                            <table class="table-striped table-hover table-condensed" style="margin-bottom: 5rem;">
                                <thead>
                                    <tr style="font-weight: bold; text-align: center;">
                                        <!-- Title -->
                                        <th></th>
                                        <!-- Only show each Sector if at least 1 row exists for column -->
                                        <th ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            style="text-transform: capitalize;  text-align: center;">
                                            {{ vm.sectorFromLabel(sector, vm.sectors).description || 'Total'}}
                                        </th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr>
                                        <td style="min-width: 180px;">Exterior Wall Area (m<sup>2</sup>)</td>
                                        <td style="min-width: 80px; text-align: center;"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)">
                                            {{vm.zoneSummaryData.envelopeSummary.exteriorWallAreaTotalsPerSector[sector].area.toFixed(2)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="min-width: 180px;">Exterior Wall Area (%)</td>
                                        <td style="min-width: 80px; text-align: center;"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)">
                                            {{vm.zoneSummaryData.envelopeSummary.exteriorWallAreaTotalsPerSector[sector].percentage.toFixed(2)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="min-width: 180px;">Exterior Glazing Area (m<sup>2</sup>)</td>
                                        <td style="min-width: 80px; text-align: center;"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)">
                                            {{vm.zoneSummaryData.envelopeSummary.exteriorGlazingAreaTotalsPerSector[sector].area.toFixed(2)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="min-width: 180px;">Exterior Glazing Area (%)</td>
                                        <td style="min-width: 80px; text-align: center;"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)">
                                            {{vm.zoneSummaryData.envelopeSummary.exteriorGlazingAreaTotalsPerSector[sector].percentage.toFixed(2)}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="min-width: 180px;">Glass-Exterior Wall Ratio (%)</td>
                                        <td style="min-width: 80px; text-align: center;"
                                            ng-repeat="sector in vm.zoneSummaryData.envelopeSummary.sectorKeys"
                                            ng-if="vm.isSectorNotEmpty(sector, vm.zoneSummaryData.envelopeSummary)">
                                            {{vm.zoneSummaryData.envelopeSummary.glassExteriorWallRatioPerSector[sector].toFixed(2)}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Filters -->
                            <h3 style="margin-top: 16px;">Filter</h3>
                            <div class="filter-grid" ng-repeat="filter in vm.filters track by filter.id">

                                <!-- Row 1 -->
                                <div>
                                    <label class="graph-option-select-label">Storey</label>
                                    <md-select name="storeyFilter"
                                                class="lightweight vertically-condensed"
                                                ng-model="filter.storey"
                                                ng-required="true">
                                        <md-option ng-value="storey"
                                                   ng-click="vm.applyStoreySelectLogic(vm.standardModel.zoneSummaryBuildingData, filter, vm.calculateEnvelopeSummaryData)"
                                                   ng-repeat="storey in vm.selectedStoreys">
                                            {{storey.description}}
                                        </md-option>
                                        <md-option ng-value="'ALL'"
                                                   ng-click="vm.applyStoreySelectLogic(vm.standardModel.zoneSummaryBuildingData, filter, vm.calculateEnvelopeSummaryData)">
                                            Whole Building
                                        </md-option>
                                    </md-select>
                                </div>
                                <div>
                                    <label class="graph-option-select-label">Horizontal Shading (Exterior Glazing)</label>
                                    <md-select name="glazingHorizontalShadingFilter"
                                               class="lightweight vertically-condensed"
                                               ng-model="filter.glazingHorizontalShading.selection"
                                               multiple>
                                        <md-option ng-value="shading"
                                                   ng-click="vm.applySelectionLogic(filter.glazingHorizontalShading, shading)"
                                                   ng-repeat="shading in filter.glazingHorizontalShading.selectableArray track by $index">
                                            {{shading.title}}
                                        </md-option>
                                    </md-select>
                                </div>
                                <div>
                                    <label class="graph-option-select-label">Horizontal Shading (Exterior Walls)</label>
                                    <md-select name="wallHorizontalShadingFilter"
                                               class="lightweight vertically-condensed"
                                               ng-model="filter.wallHorizontalShading.selection"
                                               multiple>
                                        <md-option ng-value="shading"
                                                   ng-click="vm.applySelectionLogic(filter.wallHorizontalShading, shading)"
                                                   ng-repeat="shading in filter.wallHorizontalShading.selectableArray track by $index">
                                            {{shading.title}}
                                        </md-option>
                                    </md-select>
                                </div>

                                <!-- Row 2 -->
                                <div>
                                    <label class="graph-option-select-label">Group</label>
                                    <md-select name="groupFilter"
                                               class="lightweight vertically-condensed"
                                               ng-model="filter.group"
                                               ng-required="true"
                                               ng-change="filter.selection = []">
                                        <md-option ng-value="group"
                                                   ng-click="vm.applyGroupSelectLogic(vm.standardModel.zoneSummaryBuildingData, filter, group, vm.calculateEnvelopeSummaryData)"
                                                   ng-repeat="group in vm.GROUP_OPTIONS">
                                            {{group}}
                                        </md-option>
                                    </md-select>
                                </div>
                                <div>
                                    <label class="graph-option-select-label">Vertical Shading (Exterior Glazing)</label>
                                    <md-select name="glazingVerticalShadingFilter"
                                                class="lightweight vertically-condensed"
                                                ng-model="filter.glazingVerticalShading.selection"
                                                multiple>
                                        <md-option ng-value="shading"
                                                   ng-click="vm.applySelectionLogic(filter.glazingVerticalShading, shading)"
                                                   ng-repeat="shading in filter.glazingVerticalShading.selectableArray track by $index">
                                            {{shading.title}}
                                        </md-option>
                                    </md-select>
                                </div>
                                <div>
                                    <label class="graph-option-select-label">Vertical Shading (Exterior Walls)</label>
                                    <md-select name="wallVerticalShadingFilter"
                                               class="lightweight vertically-condensed"
                                               ng-model="filter.wallVerticalShading.selection"
                                               multiple>
                                        <md-option ng-value="shading"
                                                   ng-click="vm.applySelectionLogic(filter.wallVerticalShading, shading)"
                                                   ng-repeat="shading in filter.wallVerticalShading.selectableArray track by $index">
                                            {{shading.title}}
                                        </md-option>
                                    </md-select>
                                </div>

                                <!-- Row 3 - how to stop making row expand -->
                                <div>
                                    <label class="graph-option-select-label">Selection</label>
                                    <md-select  name="selectionFilter"
                                                class="lightweight vertically-condensed"
                                                ng-model="filter.selection"
                                                multiple
                                                ng-required="true">
                                        <md-option ng-value="selection"
                                                   ng-repeat="selection in filter.selectableArray track by $index"
                                                   ng-click="vm.applySelectionLogic(filter, selection)">
                                            {{ selection.title || selection }}
                                        </md-option>
                                    </md-select>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

                <!-- Interior Summary -->
                <span ng-if="vm.zoneSummaryData.interiorSummary != null" style="margin-top:15px; font-size:18px;">
                    Interior Summary
                </span>
                <table ng-if="vm.zoneSummaryData.interiorSummary != null" class="table-striped table-hover table-condensed">

                    <colgroup>
                        <col span="1" style="min-width: 120px;">
                        <col span="1" style="min-width: 150px;">
                        <col span="1" style="min-width: 220px;">
                        <col span="1" style="min-width: 220px;">
                    </colgroup>

                    <thead>
                         <tr>
                            <th style="text-align:center"></th>
                            <th style="text-align:center">Adjacency</th>
                            <th style="text-align:center">Gross Internal Wall Area (m<sup>2</sup>)</th>
                            <th style="text-align:center">Net Internal Wall Area (m<sup>2</sup>)</th>
                        </tr>
                    </thead>

                    <!-- Storeys -->
                    <tbody ng-repeat="storey in vm.zoneSummaryData.interiorSummary.storeys track by $index" class="border-last">

                        <!-- Rows -->
                        <tr ng-repeat="row in storey.rows track by $index" class="border-last">

                            <!-- Group -->
                            <td data-title="Description"
                                ng-if="$index == 0"
                                rowspan="{{storey.rows.length}}"
                                class="text-center border-cell">
                                <span>{{storey.name}}</span>
                            </td>

                            <!-- Adjacency -->
                            <td>
                                {{row.adjacencyName}}
                            </td>

                            <!-- Gross Internal Wall Area (m2) -->
                            <td style="text-align: center;">
                                {{row.grossInternalWallArea.toFixed(2);}}
                            </td>

                            <!-- Net Internal Wall Area (m2) -->
                            <td style="text-align: center;">
                                {{row.netInternalWallArea.toFixed(2);}}
                            </td>

                        </tr>

                    </tbody>

                </table>

            </md-card-content>
        </md-card>

        <!-- Features -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <!-- Title -->
                <span class="md-title">Features</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyFeaturesToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyFeaturesToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllFeatures()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:25px;margin-top:-20px;">
                <!-- Column -->
                <div ng-repeat="column in vm.featuresColumns">
                    <!-- Group -->
                    <div ng-repeat="group in column.groups">
                        <!-- Heading -->
                        <div class="featureGroupHeading">{{group.name}}</div>
                        <!-- Checkbox -->
                        <md-checkbox ng-repeat="feature in group.features"
                                     ng-model="vm.standardModel[feature]"
                                     class="checkbox-aligner"
                                     style="align-self: end;">
                            {{vm.featureName(feature)}}
                        </md-checkbox>
                    </div>
                </div>
            </md-card-content>
        </md-card>

        <!-- Home Design Options (inc upload) + Variable Options -->
        <md-card ng-if="!vm.newRecord">

            <md-card-header>
                <!-- Title -->
                <span class="md-title">Home Design Options</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyVariablesToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyVariablesToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Restore Defaults -->
                        <md-menu-item><md-button ng-click="vm.restoreVariableDefaults()">
                            <span>Restore Defaults</span>
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearVariables()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>

            <md-card-content>

                <!-- Upload -->
                <div>
                    <div style="display:flex;">
                        <md-button class="md-primary md-raised"
                                   style="width: -webkit-fill-available"
                                   ngf-select="vm.uploadExcelFile($file)"
                                   redi-allow-roles="['settings__uploaddatasets__view']">
                            Upload Spreadsheet
                        </md-button>
                    </div>

                    <div style="margin-top: 12px;"
                         class="standard-model-info-text">
                        Uploading a home design spreadsheet will erase any existing home design 'options'
                        present for this particular home design.
                    </div>
                </div>

                <!-- Existing Rows -->
                <div style="margin-top: 30px;">
                    <h3>Existing Data Sample</h3>
                    <table class="table table-condensed">
                        <thead>
                        <tr>
                            <th style="text-align: center;">Row</th>
                            <th style="text-align: center;">Heating Load</th>
                            <th style="text-align: center;">Cooling Load</th>
                            <th style="text-align: center;">Total Energy Load</th>
                            <th style="text-align: center;">Energy Rating</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in vm.standardModelOptions track by $index">
                            <th style="text-align: center;">{{row.row - 1}}</th>
                            <td style="text-align: center;">{{row.heatingLoad.toFixed(2)}}</td>
                            <th style="text-align: center;">{{row.coolingLoad.toFixed(2)}}</th>
                            <th style="text-align: center;">{{row.totalEnergyLoad.toFixed(2)}}</th>
                            <th style="text-align: center;">{{row.energyRating.toFixed(2)}}</th>
                        </tr>
                        </tbody>
                    </table>

                    <div class="standard-model-info-text">
                        <span>
                            The above rows are the first 5 rows from the excel file as uploaded and are to be used
                            for identification purposes only.
                        </span>
                    </div>
                </div>

                <!-- Variable Options -->
                <div>
                    <h3>Variable Options</h3>

                    <div class="standard-model-info-text">
                        <span>
                            The following variables and unique options were found for this standard models options.
                            These values can be controlled on a per-client level on the Client page.
                        </span>
                    </div>

                    <div ng-repeat="group in vm.propertyGroups"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;">

                        <div ng-repeat="option in group track by $index">

                            <div ng-if="group === 'DUMMY'"></div>
                            <div ng-repeat="variable in vm.variableOptions track by $index"
                                 ng-if="variable.title === option"
                                 ng-style="{ 'color': vm.project.energyLabsSettings.properties[variable.title] ? 'black' : 'red'}">
                                <h4>{{vm.keyToName(variable.title)}}:</h4>
                                <table class="table table-condensed"
                                       style="border-collapse: collapse">
                                    <tbody>
                                        <tr ng-repeat="option in vm.ordered(variable.title,variable.options)"
                                            lro-drag-src="options_{{variable.title}}"
                                            lro-drop-target="options_{{variable.title}}"
                                            lro-drop-success="vm.reorderOptions(variable.title, variable.options)"
                                            class="variable-option">
                                            <td style="min-width: 215px; max-width: 215px; width: 215px;">{{option}}</td>
                                            <td>
                                                <img class="clickable"
                                                     ng-click="vm.toggleCostEstimate(variable, option)"
                                                     style="width: 18px; margin-left: 1rem;"
                                                     src="{{vm.costEstimateEnabledFor(variable, option) == true
                                                            ? '/content/images/energy-labs/el-cost-enabled.svg'
                                                            : '/content/images/energy-labs/el-cost-disabled.svg' }}"
                                                     ng-disabled="vm.disabled"/>
                                                <img class="clickable"
                                                     ng-click="vm.toggleVariableDefaultOption(variable, option)"
                                                     style="width: 18px; margin-left: 1rem;"
                                                     src="{{vm.isDefaultForVariable(variable, option) == true
                                                            ? '/content/images/energy-labs/el-default-enabled.svg'
                                                            : '/content/images/energy-labs/el-default-disabled.svg' }}"
                                                     ng-disabled="vm.disabled"/>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </md-card-content>

        </md-card>

        <!-- Cost Estimate -->
        <md-card ng-if="!vm.newRecord && vm.client != null" flex-gt-lg="{{vm.isModal ? null : 50}}">

            <md-card-header>
                <!-- Title -->
                <span class="md-title">Cost Estimate</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyCostEstimatesToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyCostEstimatesToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Restore Defaults -->
                        <md-menu-item><md-button ng-click="vm.restoreCostEstimateDefaults()">
                            <span>Restore Defaults</span>
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllCostEstimates()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>

            <md-card-content>

                <!-- Cost Estimate section - toggle moved to top section -->

                <!-- Cost Estimate Data Entry Table -->
                <table class="table table-hover table-condensed">
                    <thead>
                    <tr>
                        <th style="width: 40px;">
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox id="sm-cost-estimate-bulk-checkbox"
                                             style="margin: auto; text-align: center; width: 0;"
                                             ng-model="vm.costEstimateBulkStatus.selectAllCheckboxState"
                                             md-indeterminate="vm.costEstimateBulkStatus.isIndeterminate"
                                             ng-click="vm.selectAllCheckboxes(vm.flattenedCostData, vm.costEstimateBulkStatus.selectAllCheckboxState, vm.costEstimateBulkStatus)">
                                </md-checkbox>
                            </div>
                        </th>
                        <th>Description</th>
                        <th>Category</th>
                        <th class="el-smuc-center-col">Item Code</th>
                        <th class="el-smuc-center-col">Quantity</th>
                        <th class="el-smuc-center-col">UOM</th>
                        <th class="el-smuc-center-col">Rate ($)</th>
                        <th class="el-smuc-center-col">Margin (%)</th>
                        <th class="el-smuc-center-col">Rounding</th>
                        <th class="el-smuc-center-col">Cost ($)</th>
                        <th>Notes</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="option in vm.flattenedCostData track by $index"
                        ng-style="{ 'color': vm.project.energyLabsSettings.properties[option.costEstimateData.category] ? 'black' : 'red'}">

                        <!-- Bulk Select -->
                        <td>
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="option.checkboxSelected"
                                             ng-change="vm.updateBulkSelectStatus(vm.flattenedCostData, vm.costEstimateBulkStatus);">
                                </md-checkbox>
                            </div>
                        </td>
                        <!-- Description -->
                        <td>{{option.costEstimateData.description}}</td>
                        <!-- Category -->
                        <td>{{vm.keyToName(option.costEstimateData.category)}}</td>
                        <!-- Item Code -->
                        <td class="el-smuc-center-col">
                            <div>
                                <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                           ng-model="option.costEstimateData.costItemId"
                                           ng-required="true">
                                    <md-option ng-repeat="item in vm.itemCodesForCategory(option.costEstimateData.category) track by item.clientCostItemId"
                                               ng-value="item.clientCostItemId"
                                               ng-click="vm.costItemChanged(option, item)">
                                        {{item.description}}
                                    </md-option>
                                    <md-option ng-value="null">Custom</md-option>
                                </md-select>
                                <md-tooltip class="solid-popup" md-direction="top" md-delay="0" md-autohide="true">
                                    <strong>{{option.costEstimateData.costItemId != null ? (vm.client.clientCostItems | filter:{clientCostItemId:option.costEstimateData.costItemId})[0].description : 'Custom'}}</strong>
                                </md-tooltip>
                            </div>
                        </td>
                        <!-- Quantity -->
                        <td class="el-smuc-center-col">
                            <div class="var-ref-field">
                                <!-- Manual: Input -->
                                <input ng-if="option.costEstimateData.quantityVarRefJson == null"
                                       ng-model="option.costEstimateData.quantity"
                                       class="lightweight var-ref-manual-input"
                                       style="display: inline-grid;"
                                       ng-required="true" />
                                <!-- Manual: Button -->
                                <div ng-if="option.costEstimateData.quantityVarRefJson == null" class="var-ref-select-button" ng-click="vm.openVariableSelectModal(option.costEstimateData, 'quantityVarRefJson')">
                                    <img src="/content/images/select.png" />
                                </div>
                                <!-- Reference: Value -->
                                <div ng-if="option.costEstimateData.quantityVarRefJson != null"
                                     class="lightweight var-ref-value"
                                     style="display: inline-grid;"
                                     ng-required="true">
                                    {{option.costEstimateData.quantity != null ? option.costEstimateData.quantity : "-"}}
                                </div>
                                <!-- Reference: Menu -->
                                <md-menu ng-if="option.costEstimateData.quantityVarRefJson != null" class="var-ref-three-dot-menu">
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu()"
                                         src="/content/feather/more-horizontal.svg"/>
                                    <md-menu-content>
                                        <!-- Select -->
                                        <md-menu-item><md-button ng-click="vm.openVariableSelectModal(option.costEstimateData, 'quantityVarRefJson')">
                                            Select
                                        </md-button></md-menu-item>
                                        <!-- Clear -->
                                        <md-menu-item><md-button ng-click="option.costEstimateData.quantity = null; option.costEstimateData.quantityVarRefJson = null;">
                                            <span style="color: orangered;">Clear</span>
                                        </md-button></md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>
                        <!-- UOM -->
                        <td class="el-smuc-center-col">
                            <md-select ng-required="true"
                                       ng-disabled="option.costEstimateData.costItemId != null"
                                       class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                       ng-model="option.costEstimateData.unitOfMeasure">
                                <md-option ng-value="'mm'">mm</md-option>
                                <md-option ng-value="'m'">m</md-option>
                                <md-option ng-value="'m2'">m<sup>2</sup></md-option>
                                <md-option ng-value="'m3'">m<sup>3</sup></md-option>
                                <md-option ng-value="'Ea'">Ea</md-option>
                                <md-option ng-value="'kg'">kg</md-option>
                            </md-select>
                        </td>
                        <!-- Rate ($) -->
                        <td class="el-smuc-center-col">
                            <input ng-model="option.costEstimateData.ratePerUnit"
                                   class="lightweight"
                                   type="text"
                                   formatted-number
                                   decimals="2"
                                   style="display: inline;"
                                   ng-required="true"
                                   ng-disabled="option.costEstimateData.costItemId != null" />
                        </td>
                        <!-- Margin (%) -->
                        <td class="el-smuc-center-col">
                            <input class="lightweight"
                                   style="display: inline-grid;"
                                   ng-model="option.costEstimateData.margin"
                                   ng-disabled="option.costEstimateData.costItemId != null" />
                        </td>
                        <!-- Rounding -->
                        <td class="el-smuc-center-col">
                            <md-select ng-required="true"
                                       class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                       ng-model="option.costEstimateData.rounding"
                                       ng-disabled="option.costEstimateData.costItemId != null" >
                                <md-option ng-value="null">None</md-option>
                                <md-option ng-value="1">1</md-option>
                                <md-option ng-value="10">10</md-option>
                                <md-option ng-value="100">100</md-option>
                            </md-select>
                        </td>
                        <!--  Cost  -->
                        <td class="el-smuc-center-col">
                            <span ng-if="option.costEstimateData.rounding == null">{{(option.costEstimateData.quantity * option.costEstimateData.ratePerUnit * (1 + (option.costEstimateData.margin / 100))) | currency : '$' : 2}}</span>
                            <span ng-if="option.costEstimateData.rounding != null">{{vm.roundUpInt((option.costEstimateData.quantity * option.costEstimateData.ratePerUnit * (1 + (option.costEstimateData.margin / 100))), option.costEstimateData.rounding) | currency : '$' : 0}}</span>
                        </td>
                        <!--  Notes  -->
                        <td>
                            <input ng-model="option.costEstimateData.notes"
                                   ng-disabled="option.costEstimateData.costItemId != null"
                                   class="lightweight"
                                   type="text" />
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div style="display: flex; justify-content: space-between; align-items: center;">

                    <!-- Initial bulk edit button, which launches options -->
                    <md-button class="md-raised md-primary"
                               ng-click="vm.launchCostEstimateBulkEdit()"
                               ng-disabled="vm.disabledEx() || !(vm.costEstimateBulkStatus.selectAllCheckboxState || vm.costEstimateBulkStatus.isIndeterminate)">
                        BULK EDIT
                    </md-button>
                </div>

            </md-card-content>

        </md-card>

        <!-- Services Defaults -->
        <md-card ng-if="!vm.newRecord" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <!-- Title -->
                <span class="md-headline">Services Defaults</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                         class="clickable"
                         ng-click="$mdOpenMenu()"
                         src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy To Home Design Variation -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyServiceDefaultsToVariation()">
                            Copy To Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Copy From Home Design Variation -->
                        <md-menu-item><md-button ng-click="vm.copyServiceDefaultsToVariation(true)">
                            Copy From Home Design Variation
                        </md-button></md-menu-item>
                        <!-- Restore Defaults -->
                        <md-menu-item><md-button ng-click="vm.restoreServiceDefaults()">
                            <span>Restore Defaults</span>
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllServiceDefaults()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content>

                <div>
                    <!-- Heating System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Heating System</label>
                            <md-select md-container-class="md-select-show-all"
                                       ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']"
                                           ng-value="v.serviceTypeCode">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.showEnergyRatingForCode(vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode, vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating)"
                                            class="md-block vertically-condensed">
                            <label>{{vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpDucted' || vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Heating System Energy Rating (GEMS 2019)'
                                        : 'Heating System Energy Rating'}}</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.gems2019Rating"
                                   formatted-number
                                   decimals="1"
                                   ng-blur="vm.clearResults()"/>
                        </md-input-container>

                    </div>

                    <!-- Cooling System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Cooling System</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode"
                                       md-container-class="md-select-show-all">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']"
                                           ng-value="v.serviceTypeCode">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.showEnergyRatingForCode(vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode, vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling)"
                                            class="md-block vertically-condensed">
                            <label>{{vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpDucted' || vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Cooling System Energy Rating (GEMS 2019)'
                                        : 'Cooling System Energy Rating'}}</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.gems2019Rating"
                                   formatted-number
                                   decimals="1"
                                   ng-blur="vm.clearResults()"/>
                        </md-input-container>

                    </div>

                    <!-- Water Heater Type -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Water Heater Type</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.waterHeating.serviceTypeCode"
                                       md-container-class="md-select-show-all">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']"
                                           ng-value="v.serviceTypeCode">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                    </div>

                    <!-- Swimming Pool -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Swimming Pool</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.exists">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Swimming Pool Volume (L) -->
                        <md-input-container ng-if="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.exists === true"
                                            class="md-block vertically-condensed">
                            <label>Swimming Pool Volume (L)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.volume"
                                   formatted-number
                                   decimals="0"/>
                        </md-input-container>

                        <!-- Pool Pump Energy Rating -->
                        <md-input-container ng-if="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.exists === true"
                                            class="md-block vertically-condensed">
                            <label>Pool Pump Energy Rating</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.gems2019Rating"
                                   formatted-number
                                   decimals="1"/>
                        </md-input-container>

                    </div>

                    <!-- Spa -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Spa</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.exists">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Spa Volume (L) -->
                        <md-input-container ng-if="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.exists === true"
                                            class="md-block vertically-condensed">
                            <label>Spa Volume (L)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.volume"
                                   formatted-number
                                   decimals="0"/>
                        </md-input-container>

                        <!-- Spa Pump Energy Rating -->
                        <md-input-container ng-if="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.exists === true"
                                            class="md-block vertically-condensed">
                            <label>Spa Pump Energy Rating</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.gems2019Rating"
                                   formatted-number
                                   decimals="1"/>
                        </md-input-container>

                    </div>

                    <!-- Photovoltaic (PV) System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed">
                            <label>Photovoltaic (PV) System</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic.exists">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Photovoltaic (PV) System Capacity (kW) -->
                        <md-input-container ng-if="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic.exists === true"
                                            class="md-block vertically-condensed">
                            <label>Photovoltaic (PV) System Capacity (kW)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic.capacity"
                                   formatted-number
                                   decimals="2"/>
                        </md-input-container>

                    </div>
                </div>

            </md-card-content>
        </md-card>

        <!-- Design Insights -->
        <md-card ng-if="!vm.newRecord" flex-gt-lg="{{vm.isModal ? null : 50}}">

            <md-card-header>
                <!-- Title -->
                <span class="md-title">Design Insights</span>
            </md-card-header>

            <md-card-content>

                <!-- Design Insights section - toggle moved to top section -->

                <!-- Cost Estimate Data Entry Table -->
                <table class="table table-hover table-condensed">
                    <thead>
                    <tr>

                        <th style="width: 40px;">
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox id="sm-design-insights-bulk-checkbox"
                                             style="margin: auto; text-align: center; width: 0;"
                                             ng-model="vm.designInsightsBulkStatus.selectAllCheckboxState"
                                             md-indeterminate="vm.designInsightsBulkStatus.isIndeterminate"
                                             ng-click="vm.selectAllCheckboxes(vm.standardModel.variableMetadata.designInsights, vm.designInsightsBulkStatus.selectAllCheckboxState, vm.designInsightsBulkStatus)">
                                </md-checkbox>
                            </div>
                        </th>

                        <th>NatHERS Climate Zone</th>
                        <th>North Offset</th>
                        <th>Design Insight</th>
                    </tr>
                    </thead>
                    <tbody>

                    <tr ng-repeat="insight in vm.standardModel.variableMetadata.designInsights"
                        lro-drag-src="designInsightsArr"
                        lro-drop-target="designInsightsArr"
                        class="variable-option">

                        <!-- Bulk Select -->
                        <td>
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="insight.checkboxSelected"
                                             ng-change="vm.updateBulkSelectStatus(vm.standardModel.variableMetadata.designInsights, vm.designInsightsBulkStatus);">
                                </md-checkbox>
                            </div>
                        </td>

                        <!-- Climate Zone -->
                        <td>
                            <md-input-container class="md-block el-design-insight kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-if="vm.didntJustReorderSomething === true">
                                <md-select class="kindly-remove-error-spacer"
                                           md-container-class="md-select-show-all"
                                           ng-required="false"
                                           multiple="true"
                                           ng-model="insight.climateZones">
                                    <md-option
                                        ng-repeat="option in vm.standardModel.variableOptions['natHERSClimateZone'] track by $index"
                                        ng-value="option"
                                        ng-click="vm.ensureInsightsAreUnique(insight, option, 'natHERSClimateZones')">
                                        {{option}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- North Offset -->
                        <td>
                            <md-input-container class="md-block el-design-insight kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-if="vm.didntJustReorderSomething === true">
                                <md-select class="kindly-remove-error-spacer"
                                           md-container-class="md-select-show-all"
                                           ng-required="false"
                                           multiple="true"
                                           ng-model="insight.northOffsets">
                                    <md-option
                                        ng-repeat="option in vm.standardModel.variableOptions['northOffset'] track by $index"
                                        ng-value="option"
                                        ng-click="vm.ensureInsightsAreUnique(insight, option, 'northOffsets')">
                                        {{option}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Design Insight -->
                        <td>
                            <textarea ng-model="insight.notes" class="el-design-insights"></textarea>
                        </td>

                        <!-- Actions -->
                        <td>
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <!-- 'More' button w/ Popup -->
                                <md-menu>

                                    <!-- Initial '...' button, which launches options -->
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu()"
                                         src="/content/feather/more-horizontal.svg"
                                         ng-disabled="vm.disabled"/>
                                    <md-menu-content>

                                        <!-- Copy To Home Design -->
                                        <md-menu-item><md-button ng-click="vm.copyDesignInsightsToVariation(insight)">
                                            Copy To Home Design Variation
                                        </md-button></md-menu-item>

                                        <!-- Duplicate -->
                                        <md-menu-item>

                                            <md-button ng-click="vm.cloneDesignInsight(insight)">
                                                Duplicate
                                            </md-button>
                                        </md-menu-item>

                                        <md-menu-divider></md-menu-divider>

                                        <md-menu-item>

                                            <!-- Delete button -->
                                            <md-button ng-click="vm.removeDesignInsight(insight)"
                                                       ng-show="!vm.disabled || !(!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)">
                                                <span style="color: orangered">Delete</span>
                                            </md-button>

                                        </md-menu-item>

                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>

                    </tr>
                    </tbody>
                </table>

                <div style="display: flex; justify-content: space-between; align-items: center;">

                    <!-- Initial bulk edit button, which launches options -->
                    <md-button class="md-raised md-primary"
                               ng-click="vm.launchDesignInsightBulkEdit()"
                               ng-disabled="vm.disabledEx() || !(vm.designInsightsBulkStatus.selectAllCheckboxState || vm.designInsightsBulkStatus.isIndeterminate)">
                        BULK EDIT
                    </md-button>

                    <md-button class="md-raised md-primary"
                               style="margin-left: auto; display: block;"
                               ng-click="vm.addDesignInsight()">
                        Add Design Insight
                    </md-button>
                </div>

            </md-card-content>

        </md-card>

        <div data-cc-widget-button-bar
             data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised md-primary"
                       ng-disabled="standardModelform.$invalid || vm.editPermission == false || vm.floorplannerLinkEditing || vm.floorplannerLinkWrongSite || vm.floorplannerLinkInvalid || (!vm.newRecord && !vm.hasChanges())"
                       ng-show="vm.standardModel.deleted!=true"
                       ng-click="vm.save()">
                Save
            </md-button>
            <md-button ng-if="!vm.newRecord"
                       class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="vm.standardModel.title!=null && vm.standardModel.deleted!=true"
                       ng-confirm-click="vm.delete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to delete this record.">
                Delete
            </md-button>
            <md-button class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="vm.standardModel.deleted==true"
                       ng-confirm-click="vm.undoDelete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to RESTORE this record.">
                Restore
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>
    </div>
</form>

<style>

    .home-design-variation-body md-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .home-design-variation-body .three-dot-menu {
    }

    .plan-images-container {
        display: flex;
        flex-wrap: wrap;
        column-gap: 30px;
        max-width: 95vw;
    }

    @media screen and (max-width: 1300px) {
        .plan-images-container {
            grid-template-columns: 400px;
        }
    }

    .standard-model-info-text {
        text-align: left;
        color: dimgrey;
        font-size: 11px;
    }

    .variable-option {
        transition: 300ms;
    }

    .variable-option:hover {
        background-color: #f1f1f1;
    }

    .featureGroupHeading {
        border-bottom: solid 1px #8d8d8d;
        font-weight: bold;
        padding-bottom: 6px;
        margin-top: 22px;
        margin-bottom: 18px;
    }

    /* --------------------------- */
    /* - Design Metrics Metadata - */
    /* --------------------------- */

    .filter-grid {
        display: grid;
        /* 1fr causes child multi-select to stretch column when it becomes bigger */
        grid-template-columns: repeat(3, 33%);
        gap: 12px;
        justify-content: space-around;
        justify-items: center;
    }

        .filter-grid > div {
            width: 100%;
        }

    .filter-grid-item {
        padding: 16px;
    }

    .chart-view-container {
        width: 33%;
        margin: 0 auto;
    }

    /* ----------------- */
    /* - Drawing Table - */
    /* ----------------- */

    .drawingAreasTable {
        width: 100%;
    }

    .drawingAreasTable td,
    .drawingAreasTable th,
    .drawingAreasTable input {
        width: 60px;
        text-align: center;
    }

    .drawingAreasTable input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        border: none;
        outline: none !important;
        border-radius: 0;
        background-color: transparent;
    }

    /* ------------- */
    /* Cost Estimate */
    /* ------------- */

    /* Quantity */
    .var-ref-field {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
        /* Manual: Input */
        .var-ref-manual-input {
            margin-top: -3px;
            width: 40px;
            text-align: center;
        }
        /* Manual: Button */
        .var-ref-select-button {
            margin-left: 6px;
            margin-right: -6px;
            margin-top: -3px;
            margin-bottom: -4px;
            width: 32px;
            padding: 5px 4px 0 5px;
            box-sizing: border-box;
            display: block;
            border-radius: 5px;
            cursor: pointer;
        }
        .var-ref-select-button.is-absolute {
            position: absolute;
            right: 20px;
            top: 0;
        }
        .var-ref-select-button:hover {
            background-color: #adc43b !important;
        }
        .var-ref-select-button > img {
            width: 100%;
            height: auto;
        }
        /* Reference: Value */
        .var-ref-value {
            width: 40px;
        }
        /* Reference: Menu */
        .var-ref-three-dot-menu {
            margin-left: 6px;
            margin-right: -6px;
            width: 32px;
        }
        .var-ref-three-dot-menu.is-absolute {
            position: absolute;
            right: 20px;
            top: 0;
        }
            .var-ref-three-dot-menu > img {
                margin-top: 2px;
                margin-right: -5px;
            }

                margin-left: 5px;
                transform: rotate(90deg);
            }

</style>