<div class="el-poppins el-popup-grid el-plan-view-container {{vm.showLabel ? 'showLabel' : ''}}">

    <!-- Prev Button -->
    <div ng-click="vm.prevImage($event)"
        class="el-plan-view-container-prev-btn clickable"
        show="{{vm.currentIndex !== 0}}">
    <i class="fa fa-angle-left fa-3x" />
    </div>

    <div class="image-container">

        <img class="el-plan-view-3d-view-icon"
             src="../../../content/images/3d-modeling.png"
             ng-click="vm.open3dViewerModal($event)"
             ng-if="vm.show3dFloorPlans" />

        <img class="el-plan-view-magnify-icon"
             src="../../../content/images/zoom-in.png"
             ng-click="vm.openSwitcherModal($event)"/>

        <div>&nbsp;</div>

        <!-- Image -->
        <img src="{{vm.planViews[vm.currentIndex].file.url}}"
                alt="Building floor plan drawing"
                ng-click="vm.openModal($event)"
                class="el-plan-view-image">

        <span class="image-label" ng-show="vm.showLabel">{{vm.planViews[vm.currentIndex].label}}</span>

    </div>

    <!-- Next Button -->
    <div ng-click="vm.nextImage($event)"
        class="el-plan-view-container-next-btn clickable"
        show="{{vm.currentIndex < (vm.planViews.length - 1)}}">
    <i class="fa fa-angle-right fa-3x" />
    </div>

</div>

<style>

    .el-plan-view-container {
        display: grid;
        grid-template-columns: 50px 400px 50px;
        justify-items: center;
        align-items: center;
        height: max-content;
        width: max-content;
        position: relative;
        padding: 0px;
    }
    .el-plan-view-container.showLabel {
        padding: 0px 15px 30px 15px;
    }

    .image-container {
        width: max-content;
        height: max-content;
    }

    .el-plan-view-3d-view-icon,
    .el-plan-view-magnify-icon {
        width: 40px;
        height: 40px;
        position: absolute;
        z-index: 1;
        right: 16px;
        cursor: pointer;
        box-sizing: border-box;
        opacity: 0.8;
    }
    .el-plan-view-3d-view-icon {
        bottom: 50px;
        padding: 8px;
    }
    .el-plan-view-magnify-icon {
        bottom: 10px;
        padding: 10px;
    }
    .el-plan-view-3d-view-icon:hover,
    .el-plan-view-magnify-icon:hover {
        transition: background-color 200ms, filter 200ms;
        background-color: #585858;
        filter: invert(100%);
    }

    .el-plan-view-image {
        width: 400px;
        height: 600px;
        object-fit: contain;
    }

    .el-plan-view-container-prev-btn,
    .el-plan-view-container-next-btn {
        display: grid;
        grid-template-columns: 1fr;
        align-items: center;
        justify-items: center;
        height: 45px;
        width: 45px;
        border-radius: 50%;
        opacity: 0;
    }

        .el-plan-view-container-prev-btn:hover,
        .el-plan-view-container-next-btn:hover {
            background-color: white;
            transition: 150ms linear;
        }

        .el-plan-view-container-prev-btn[show='true'],
        .el-plan-view-container-next-btn[show='true'] {
            opacity: 100%;
        }

    .el-plan-view-container-prev-btn > i {
        margin-top: -2px;
        margin-left: -3px;
    }

    .el-plan-view-container-next-btn > i {
        margin-top: -2px;
        margin-left: 4px;
    }

    .el-plan-view-container-prev-btn { margin-right: 10px; }
    .el-plan-view-container-next-btn { margin-left:  10px; }

    .image-label {
        width: 100%;
        text-align: center;
        font-size: 16px;
        position: absolute;
        left: 0px;
        bottom: 25px;
    }

</style>