<div ng-form="complianceEleForm">

    <!-- General Info Table-->
    <table flex="100"
           style="margin-bottom: 10px; width: 100%;">
        <thead>
            <tr>
                <th style="width: 10%"></th>
                <th></th>
            </tr>
        </thead>
        <tbody>

            <!-- Certification -->
            <tr ng-if="vm.permission_field_certification_view">
                <td style="font-weight:bold;">Certification</td>
                <td>
                    <md-select name="certification"
                               ng-required="true"
                               class="vertically-condensed"
                               style="width: 530px;"
                               ng-disabled="vm.isLocked || vm.permission_field_certification_edit == false"
                               ng-model="vm.complianceData.certification"
                               ng-model-options="{trackBy: '$value.certificationId'}"
                               ng-change="vm.updateDataLinkedToCertification()">
                        <md-option ng-value="item"
                                   ng-repeat="item in vm.certificationList track by item.certificationId">
                            {{item.title}}
                        </md-option>
                    </md-select></td>
            </tr>

            <!-- Sector Determination -->
            <tr>
                <td style="font-weight:bold;">Sector Determination</td>
                <td>
                    <md-select name="sectorDetermination"
                               class="vertically-condensed"
                               style="width: 530px;"
                               ng-required="true"
                               ng-disabled="vm.isLocked"
                               ng-model="vm.complianceData.sectorDetermination"
                               ng-model-options="{trackBy: '$value.sectorDeterminationCode'}">
                        <md-option ng-value="sector"
                                   ng-repeat="sector in vm.sectorDeterminationList track by sector.sectorDeterminationCode">
                            {{sector.title}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Assessment Method -->
            <tr ng-if="vm.permission_field_assessmentmethod_view">
                <td style="font-weight:bold;">
                    Assessment Method
                </td>
                <td>
                    <md-select name="PreliminaryComplianceMethodCode"
                               class="vertically-condensed"
                               style="width: 530px;"
                               flex="100"
                               ng-disabled="vm.isLocked || vm.permission_field_assessmentmethod_edit == false"
                               ng-model="vm.complianceData.complianceMethod"
                               ng-model-options="{trackBy: '$value.complianceMethodCode'}"
                               placeholder="Select a Method"
                               ng-change="vm.complianceTypeChanged(vm.complianceData); ">
                        <md-option ng-repeat="item in vm.availableComplianceMethods() track by item.complianceMethodCode"
                                   ng-value="item">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Assessment Software -->
            <tr ng-if="vm.permission_field_assessmentsoftware_view">
                <td style="font-weight:bold;">
                    Assessment Software
                </td>
                <td>
                    <md-select name="assessmentSoftwareCode"
                               ng-if="vm.assessmentSoftwareList !== null"
                               class="vertically-condensed"
                               style="width: 530px;"
                               flex="100"
                               ng-model="vm.complianceData.assessmentSoftware"
                               ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}"
                               ng-disabled="vm.isLocked || vm.permission_field_assessmentsoftware_edit == false">
                        <md-option ng-value="item"
                                   ng-disabled="!vm.isSoftwareAvailable(item.assessmentSoftwareCode)"
                                   ng-repeat="item in vm.assessmentSoftwareList track by item.assessmentSoftwareCode">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Description -->
            <tr>
                <td style="font-weight:bold;">
                    Description
                </td>
                <td class="vertically-condensed">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer"
                                        style="min-height: 30px;" flex="100">
                        <textarea maxlength="500"
                                  class="vertically-condensed"
                                  style="width: 530px;"
                                  ng-model="vm.complianceData.description"
                                  ng-required="true && !vm.complianceData.description"
                                  ng-disabled="vm.isLocked"
                                  ng-focus="vm.hidePlaceholder = true;"
                                  ng-blur="vm.hidePlaceholder = false;"
                                  placeholder="{{ ((vm.complianceData.description == null || vm.complianceData.description == '')) && !vm.hidePlaceholder ?  !vm.isOption ? 'Baseline specifications' : 'Option ' + vm.determineUINumber() + ' specifications' : ''}}"
                                  name="ProposedDescription"></textarea>
                        <div ng-if="complianceEleForm.description.$error"
                             ng-messages="complianceEleForm.description.$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>
            </tr>

            <!-- Update Drawings -->
            <tr ng-if="vm.isOption">
                <td style="font-weight:bold;">Drawings</td>
                <td>
                    <md-input-container class="md-block vertically-condensed vertically-condensed-ex">
                        <md-select name="UpdatedDrawingsRequired"
                                   style="margin-top: auto;
                                          margin-bottom: auto;
                                          width: 530px;"
                                   ng-model="vm.complianceData.updatedDrawingsRequired"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true">
                            <md-option ng-value="true">Updated drawings required</md-option>
                            <md-option ng-value="false">Copy from Baseline</md-option>
                        </md-select>
                    </md-input-container>
                </td>
            </tr>

            <!-- New Purchase Order-->
            <tr ng-if="vm.isOption && vm.assessment.job.client.clientDefault.purchaseOrderCode != 'NotRequired'">
                <td style="font-weight:bold;">
                    <span class="full-text">New Purchase Order</span>
                    <span class="short-text">
                        New PO
                        <md-tooltip>
                            New Purchase Order
                        </md-tooltip>
                    </span>
                </td>
                <td>
                    <md-input-container class="md-block vertically-condensed">
                        <md-checkbox ng-model="vm.complianceData.newPurchaseOrderRequired"
                                     style="margin-bottom: 1px; margin-top: 1px;"
                                     ng-disabled="vm.isLocked">
                        </md-checkbox>
                    </md-input-container>
                </td>
            </tr>

            <!-- Markup Required? -->
            <tr ng-if="vm.isOption">
                <td style="font-weight:bold;">Mark-Up</td>
                <td>
                    <file-upload class="vertically-condensed-ex"
                                 is-locked="vm.isLocked"
                                 job-files="vm.jobFiles"
                                 file-object="vm.complianceData.proposed"
                                 prop-name="markupFile"
                                 form-name="ProposedMarkupFile"
                                 is-required="false"
                                 required-message="''"
                                 assessment="vm.assessment"
                                 category="'Building Data'"
                                 classification="'Markup'"
                                 job="vm.assessment.job">
                    </file-upload>
                </td>
            </tr>

        </tbody>
    </table>

    <table class="table-striped table-hover table-condensed" ng-class="{'shadow-z-1': true}"
           style="margin-bottom: 2rem;">
        <thead>
            <tr>
                <th style="white-space: nowrap;  text-align: center;">Insulation</th>
                <th style="white-space: nowrap;  text-align: center;">Roof & Ceiling</th>
                <th style="white-space: nowrap;  text-align: center;">Roof Lights</th>
                <th style="white-space: nowrap;  text-align: center;">External Walls</th>
                <th style="white-space: nowrap;  text-align: center;">External Floors</th>
                <th style="white-space: nowrap;  text-align: center;">
                    <span class="full-text">Class 10a Buildings</span>
                    <span class="short-text">
                        10a Buildings
                        <md-tooltip>
                            Class 10a Buildings
                        </md-tooltip>
                    </span>
                </th>
                <th style="white-space: nowrap;  text-align: center;">External Glazing</th>

                <th style="white-space: nowrap;  text-align: center;">Building Sealing</th>
                <th style="white-space: nowrap;  text-align: center;">Ceiling Fans</th>

                <!-- Valid (Override) -->
                <th style="white-space: nowrap;  text-align: center;">Valid</th>

            </tr>
        </thead>

        <tbody>

            <tr>

                <!-- Insulation? -->
                <td>
                    <md-input-container class="md-block horizontally-condensed" flex>
                        <md-select name="Insulation{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true"
                                   ng-model="vm.complianceData.epComplianceData.insulation"
                                   ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['insulation' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Roof and Ceiling? -->
                <td>
                    <md-input-container class="md-block  horizontally-condensed" flex>
                        <md-select name="RoofAndCeiling{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.roofAndCeiling" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['roofAndCeiling' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Roof Lights?-->
                <td>
                    <md-input-container class="md-block  horizontally-condensed" flex>
                        <md-select name="RoofLights{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.roofLights" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['roofLights' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- External Walls? -->
                <td>
                    <md-input-container class="md-block  horizontally-condensed" flex>
                        <md-select name="ExternalWalls{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.externalWalls" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['externalWalls' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- External Floors? -->
                <td>
                    <md-input-container class="md-block  horizontally-condensed" flex>
                        <md-select name="ExternalFloors{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.externalFloors" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['externalFloors' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Class 10a Buildings -->
                <td>
                    <md-input-container class="md-block horizontally-condensed" flex>
                        <md-select name="Class10a{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.class10a" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['class10a' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- External Glazing -->
                <td>
                    <md-input-container class="md-block horizontally-condensed" flex>
                        <md-select name="ExternalGlazing{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.externalGlazing" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['externalGlazing' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Building Sealing? -->
                <td>
                    <md-input-container class="md-block horizontally-condensed" flex>
                        <md-select name="BuildingSealing{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.buildingSealing" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['buildingSealing' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Ceiling Fans -->
                <td>
                    <md-input-container class="md-block horizontally-condensed" flex>
                        <md-select name="AirMovement{{$index}}"
                                   class="vertically-condensed compliance-row-table-input-m"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true" ng-model="vm.complianceData.epComplianceData.airMovement" ng-change="vm.calculateCompliance()">
                            <md-option value="Yes"><span class="offset-dropdown">Yes</span></md-option>
                            <md-option value="No"><span class="offset-dropdown">No</span></md-option>
                            <md-option value="N/A"><span class="offset-dropdown">N/A</span></md-option>
                        </md-select>
                        <div ng-messages="complianceEleForm['airMovement' + $index].$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>

                <!-- Is Compliance Valid (Override) -->
                <td>
                    <md-switch ng-model="vm.complianceData.isComplianceValid"
                               ng-change="vm.calculateCompliance()"
                               ng-disabled="vm.isLocked"
                               style="margin-left: 8px;"></md-switch>
                </td>

            </tr>
        </tbody>
    </table>

    <!-- File Uploads -->
    <software-file-uploads option="vm.complianceData"
                           disabled="vm.isLocked"
                           assessment="vm.assessment"
                           job-files="vm.jobFiles">
    </software-file-uploads>
</div>
