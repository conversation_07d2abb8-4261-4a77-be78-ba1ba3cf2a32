angular.module('app').controller(
    'TwoFacAuthController', 
    ['$mdDialog', 'common', 'security', 'localizedMessages',
    function($mdDialog, common, security, localizedMessages) {

        var vm = this;
        var log = common.logger;

        // The model for this form 
        vm.user = {};
        vm.user.email = security.currentUser.userId;

        // Any error message from failing to login
        vm.authError = null;
        vm.authenticatorDetails = {};
        vm.verificationCode;

        vm.isBusy = true;
        security.getAuthenticatorDetails().then(authenticatorDetails => {
            vm.authenticatorDetails = authenticatorDetails;
            vm.isBusy = false;
            document.getElementById("authCodeInput").focus();
        });

        vm.authCodeKeyPress = function (event) {
            if (event.keyCode == 13) { vm.verify(); }
        }

        // Attempt to save the user changes specified in the form's model
        vm.verify = function() {
            if (vm.verificationCode != null) {
                // Call the server to change the password
                security.verifyAuthenticator(vm.verificationCode).then(
                    success => {
                        if (!success) {
                            vm.authError = 'Invalid 2FA verification Code';
                        }
                        else {
                            security.currentUser.twoFacAuthEnabled = true;
                            security.currentUser.twoFacAuthVerified = true;
                            security.currentUser.isTwoFacAuthRequire = false;
                            log.logSuccess("Successfully setup 2FA");
                            $mdDialog.hide();
                        }
                    },
                    error => {
                        // If we get here then there was a problem with the password change request to the server
                        vm.authError = '2FA  Code verification Error';
                    });
            }
        };

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.clearForm = function() {
            vm.user = {};
        };

}]);
