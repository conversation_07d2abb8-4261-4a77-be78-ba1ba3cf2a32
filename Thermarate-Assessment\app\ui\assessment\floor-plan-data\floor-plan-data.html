<md-card ng-form="FloorPlanDataForm{{vm.complianceOption.optionIndex}}{{vm.sourceType}}">
    <md-card-content>

        <!-- Floor Plan Spaces -->
        <div class="table-responsive-vertical"
             style="margin-bottom: 15px; padding-top: 1px;">

            <!-- Title + "Not Applicable" checkbox -->
            <div style="display: flex; justify-content: space-between; align-items: center;">

                <h2 style="margin: 15px;">Spaces</h2>

                <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['floorPlanSpaces']"
                             class="not-applicable-checkbox"
                             ng-change="vm.removeSpaces(vm.source.spaces)"
                             ng-disabled="vm.disabledEx()">
                    Not Applicable
                </md-checkbox>
            </div>

            <table ng-if="vm.source.zoneTypesNotApplicable['floorPlanSpaces'] == false || vm.source.zoneTypesNotApplicable['floorPlanSpaces'] == null"
                   class="table table-striped table-hover table-condensed"
                   ng-disabled="vm.source.buildingSpacesTemplateId != null">
                <thead>
                <tr>
                    <th style="width: 20px; text-align: center;">
                        <md-checkbox ng-model="vm.selectedForSpaceBulkEdit"
                                     ng-disabled="vm.disabledEx()"
                                     ng-click="vm.toggleBulkEditAll(!vm.selectedForSpaceBulkEdit, 'selectedForSpaceBulkEdit', vm.source.spaces)"
                                     style="margin: auto;">
                        </md-checkbox>
                    </th>
                    <th class="text-left clickable" ng-click="vm.sortBy('Space', 'zoneNumber');">
                        Space Number
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneNumber' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneNumber' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-left clickable" ng-click="vm.sortBy('Space', 'zoneDescription');">
                        Space Name
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneDescription' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneDescription' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-left clickable" ng-click="vm.sortBy('Space', 'zoneType.description');">
                        Space Type
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneType.description' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'zoneType.description' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-left clickable" ng-click="vm.sortBy('Space', 'location');">
                        Location
                        <i ng-if="vm.SpaceSortInfo.column === 'location' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'location' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-right clickable" ng-click="vm.sortBy('Space', 'floorArea');">
                        Floor Area (m<sup>2</sup>)
                        <i ng-if="vm.SpaceSortInfo.column === 'floorArea' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'floorArea' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-right clickable" ng-click="vm.sortBy('Space', 'perimeter');">
                        Perimeter (m)
                        <i ng-if="vm.SpaceSortInfo.column === 'perimeter' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'perimeter' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>
                    <th class="text-right clickable" ng-click="vm.sortBy('Space', 'ceilingHeight');">
                        Ceiling Height (m)
                        <i ng-if="vm.SpaceSortInfo.column === 'ceilingHeight' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'ceilingHeight' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>

                    <th class="text-right clickable" ng-click="vm.sortBy('Space', 'storey.floor');">
                        Storey
                        <i ng-if="vm.SpaceSortInfo.column === 'storey.floor' && vm.SpaceSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
                        <i ng-if="vm.SpaceSortInfo.column === 'storey.floor' && vm.SpaceSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
                    </th>

                    <!-- Actions -->
                    <th class="text-left" style="width: 32px;"></th>

                </tr>

                </thead>
                <tbody>

                <tr ng-repeat="item in vm.sortedSpacesFunc()"
                    lr-drag-src="spaces"
                    lr-drop-target="spaces"
                    lr-drop-success="vm.renumberSpaces()"
                    lr-match-property="spaceId"
                    lr-drag-data="vm.source.spaces"
                    lr-match-value="{{item.zoneId}}"
                    lr-index="vm.source.spaces.indexOf(item)">

                    <td style="text-align: center;">
                        <md-checkbox ng-model="item.selectedForSpaceBulkEdit"
                                     ng-click="vm.bulkEditAllSpaces = false;"
                                     ng-disabled="vm.disabledEx()"
                                     style="margin: auto;" />
                    </td>

                    <!-- Space Number -->
                    <td data-title="Space Number" ng-class="{ 'draggable': !vm.disabledEx() }">
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx() }">

                            <input type="text"
                                   name="SpaceNumber{{$index}}"
                                   ng-model="item.zoneNumber"
                                   ng-required="vm.isTemplate == false"
                                   ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                                   ng-blur="vm.updateSource(item, item.zoneNumber, 'A');" />
                            <div ng-messages="SpaceListForm['SpaceNumber'+$index].$error">
                            </div>
                        </md-input-container>
                    </td>

                    <!-- Space Name -->
                    <td data-title="Space Name"
                        lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">

                            <input type="text"
                                   name="SpaceName{{$index}}"
                                   ng-model="item.zoneDescription"
                                   ng-required="vm.isTemplate == false"
                                   ng-disabled="vm.disabledEx()" />
                            <div ng-messages="SpaceListForm['SpaceDescription'].$error">
                            </div>
                        </md-input-container>
                    </td>

                    <!-- Space Type -->
                    <td data-title="Space Type" class="text-left" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">

                            <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                       style="margin: 0;"
                                       ng-model="item.zoneType"
                                       ng-model-options="{trackBy: '$value.zoneTypeCode'}"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()">
                                <md-option ng-value="null"
                                           ng-show="false">
                                </md-option>

                                <md-option ng-repeat="zoneType in vm.zoneTypeList track by zoneType.zoneTypeCode"
                                           ng-value="zoneType">
                                    {{zoneType.description}}
                                </md-option>
                            </md-select>
                        </md-input-container>
                    </td>

                    <!-- Location -->
                    <td data-title="Location" class="text-left" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">

                            <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                       style="margin: 0;"
                                       ng-model="item.location"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()">
                                <md-option ng-value="null"
                                           ng-show="false">
                                </md-option>
                                <md-option ng-value="'Back Centre'">Back Centre</md-option>
                                <md-option ng-value="'Back Left'">Back Left</md-option>
                                <md-option ng-value="'Back Right'">Back Right</md-option>
                                <md-option ng-value="'Front Centre'">Front Centre</md-option>
                                <md-option ng-value="'Front Left'">Front Left</md-option>
                                <md-option ng-value="'Front Right'">Front Right</md-option>
                                <md-option ng-value="'Middle Centre'">Middle Centre</md-option>
                                <md-option ng-value="'Middle Left'">Middle Left</md-option>
                                <md-option ng-value="'Middle Right'">Middle Right</md-option>
                                <md-option ng-value="'None'">None</md-option>
                                <md-option ng-value="'Other'">Other</md-option>
                            </md-select>
                        </md-input-container>
                    </td>

                    <!-- Floor Area -->
                    <td data-title="Floor Area" class="text-right" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">
                            <input type="text" 
                                   name="SpaceFloorArea{{$index}}"
                                   ng-model="item.floorArea"
                                   ng-change="vm.calculateAllowance(item);"
                                   ng-blur="vm.matchCeilingAreaToFloorArea(item);"
                                   ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                   formatted-number
                                   decimals="2"
                                   ng-required="vm.isTemplate == false"
                                   ng-disabled="vm.disabledEx()" />
                            <div ng-messages="SpaceListForm['SpaceFloorArea'+$index].$error">
                            </div>
                        </md-input-container>
                    </td>

                    <!-- Perimeter -->
                    <td data-title="Perimeter" class="text-right" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">

                            <input type="text" name="SpacePerimeter{{$index}}"
                                   ng-model="item.perimeter"
                                   ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                   formatted-number
                                   ng-required="false"
                                   decimals="2"
                                   ng-disabled="vm.disabledEx()" />
                            <div ng-messages="SpaceListForm['SpacePerimeter'+$index].$error">
                            </div>
                        </md-input-container>
                    </td>

                    <!-- Ceiling Height -->
                    <td data-title="CeilingHeight" class="text-right" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                            ng-class="{'input-black' : !vm.disabledEx()}">

                            <input type="text" name="SpaceCeilingHeight{{$index}}"
                                   ng-model="item.ceilingHeight"
                                   ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                   formatted-number
                                   ng-required="false"
                                   decimals="2"
                                   ng-disabled="vm.disabledEx()" />
                            <div ng-messages="SpaceListForm['SpaceCeilingHeight'+$index].$error">
                            </div>
                        </md-input-container>
                    </td>

                    <!-- Storey -->
                    <td data-title="Storey" class="text-right" lr-no-drag>
                        <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                            <md-select ng-required="vm.isTemplate == false"
                                       style="margin: 0;"
                                       name="SpaceStorey{{$index}}"
                                       ng-disabled="vm.disabledEx()"
                                       ng-model="item.storey"
                                       ng-change="vm.calculateAllowance(item)">
                                <md-option ng-value="x.floor"
                                           ng-repeat="x in vm.source.storeys">
                                    {{x.name}}
                                </md-option>
                            </md-select>
                        </md-input-container>
                    </td>

                    <!-- Action Buttons -->
                    <td data-title="Clear"
                        lr-no-drag
                        class="text-center">

                        <div ng-include="'space-more-actions-multi-button'" 
                             style="display: flex; justify-content: center; align-content: center;"/>
                    </td>

                </tr>

                </tbody>

            </table>

            <div ng-if="vm.source.zoneTypesNotApplicable['floorPlanSpaces'] == false || vm.source.zoneTypesNotApplicable['floorPlanSpaces'] == null"
                 layout="row"
                 style="padding: 10px 2px;">

                <md-button class="md-raised md-primary"
                           ng-click="vm.addSpace(vm.source.spaces, 'A')"
                           ng-show="!vm.disabledEx()"
                           ng-disabled="vm.disabledEx()">
                    Add
                </md-button>

                <md-button class="md-raised md-primary"
                           ng-click="vm.showBulkEdit('selectedForSpaceBulkEdit', 'bulkEditAllSpaces', vm.source.spaces, 'A')"
                           ng-show="!vm.disabledEx()"
                           ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForSpaceBulkEdit')">
                    Bulk Edit
                </md-button>

            </div>
        </div>

    </md-card-content>
</md-card>

<script type="text/ng-template" id="space-more-actions-multi-button">

    <!-- 'More' button w/ Popup -->
    <md-menu ng-show="!vm.disabledEx()">

        <!-- Initial '...' button, which launches options -->
        <img md-menu-origin
             class="clickable"
             ng-click="$mdOpenMenu()"
             src="/content/feather/more-horizontal.svg"
             ng-disabled="vm.disabled"/>
        <md-menu-content>

            <md-menu-item>

                <!-- Duplicate -->
                <md-button ng-click="vm.cloneSpace(item, 'A')">
                    Duplicate
                </md-button>
            </md-menu-item>

            <md-menu-item>
                <!-- Delete -->
                <md-button ng-click="vm.removeSpace(item)">
                    <span style="color: orangered;">Delete</span>
                </md-button>
            </md-menu-item>

        </md-menu-content>
    </md-menu>

</script>
