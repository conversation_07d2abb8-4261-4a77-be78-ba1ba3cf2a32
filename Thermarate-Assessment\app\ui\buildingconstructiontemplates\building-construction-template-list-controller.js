(function () {
    // The BuildingConstructionTemplateListCtrl supports a list page.
    'use strict';
    var controllerId = 'BuildingConstructionTemplateListCtrl';
    angular.module('app')
        .controller(controllerId, ['$stateParams', '$rootScope', '$mdDialog', '$state', 'buildingconstructiontemplateservice', 'daterangehelper', 'common', buildingConstructionTemplateListController]);
    function buildingConstructionTemplateListController($stateParams, $rootScope, $mdDialog, $state, buildingconstructiontemplateservice, daterangehelper, common) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;

        vm.constructionTemplateList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = {};
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.totalFilteredRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};

        // Multi-filter variables
        vm.searchString = null;
        vm.searchStringOld = null;
        vm.searchFields = ['templateName', 'notes'];
        vm.filters = [
            { field: 'templateName', name: 'templateName', section: 1 },
            { field: 'notes', name: 'notes', section: 1 }
        ];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};
        vm.filtersApplied = false;
        vm.filtersExpanded = false;
        vm.initialiseComplete = false;

        // Multi-filter helper functions
        vm.keyToName = function(key) {
            switch(key) {
                case 'templateName': return 'Template Name';
                case 'notes': return 'Notes';
                default: return common.toSplitTitleCase(key);
            }
        };
        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.anyFiltersApplied = common.anyFiltersApplied;

        // This will determine whether we are creating a construction (surface..) or an opening template...
        vm.type = $stateParams.type;

        vm.title = `Building ${vm.type} Templates`;

        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'assessmentId',
                    description: 'Assessment',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'templateName',
                    description: 'Template Name',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        var persistRangeName = "constructionTemplateList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        vm.goToItem = function (row) {
            $state.go("buildingconstructiontemplate", { templateId: row.buildingConstructionTemplateId, type: vm.determineType(row) });
        }

        // Initialize filters
        vm.filters.forEach(filter => {
            vm.appliedFilters[filter.field] = ['Any'];
        });
        vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

        // Clear filter functions
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = ['Any'];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);
            vm.refreshList();
        }

        vm.clearFilters = function () {
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.filters.forEach(filter => {
                vm.appliedFilters[filter.field] = ['Any'];
            });
            vm.filtersApplied = false;
            vm.refreshList();
        }

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            // Create search filter
            let searchFilter = [];
            if (vm.searchString != null && vm.searchString != '') {
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }
            }

            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "templateName", operator: "startswith", value: val, logic: "or" },
                    { field: "projectDescription", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            buildingconstructiontemplateservice.getListCancel();
            buildingconstructiontemplateservice.getList(vm.type, null, null, null, null, null, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = buildingconstructiontemplateservice.currentFilter();
                    vm.constructionTemplateList = result.data;
                    vm.totalRecords = result.total;
                    vm.totalFilteredRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.constructionTemplateList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;

                    // Check if filters are applied
                    vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);

                    // Initialize filter options and counts (simplified for now)
                    if (!vm.initialiseComplete) {
                        vm.filterOptions = {};
                        vm.filters.forEach(filter => {
                            vm.filterOptions[filter.field] = [];
                            let uniqueValues = [...new Set(vm.constructionTemplateList.map(item => item[filter.field]).filter(val => val != null && val != ''))];
                            uniqueValues.forEach(value => {
                                vm.filterOptions[filter.field].push({ name: value, value: value });
                            });
                        });
                        vm.initialiseComplete = true;
                    }

                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        vm.createConstructionTemplate = function () {
            var modalScope = $rootScope.$new();
            modalScope.type = vm.type.toLowerCase();
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/buildingconstructiontemplates/new-building-construction-template.html',
                clickOutsideToClose: true,
            }).then(function () { }, function () { });
        }

        vm.delete = function(row) {

            let index = vm.constructionTemplateList.indexOf(row);

            buildingconstructiontemplateservice
                .deleteTemplate(row.buildingConstructionTemplateId)
                .then(() => {
                    row.deleted = true;

                    vm.constructionTemplateList.splice(index, 1);
                });
        }

        vm.clone = function(row) {
            buildingconstructiontemplateservice
            .copyTemplate(row.buildingConstructionTemplateId)
            .then((id) => {

                buildingconstructiontemplateservice
                    .getTemplate(id)
                    .then(template => {

                        // Add returned template to list.
                        vm.constructionTemplateList.push(template);

                        // Ideally sort based on what we're actually sorting by, but since
                        // we basically only have the Template Name to go off...
                        vm.constructionTemplateList.sort((a,b) => (a.templateName > b.templateName) ? 1 : ((b.templateName > a.templateName) ? -1 : 0));
                    });

            });
        }

        vm.determineType = function (row) {
            // TODO: Make this return proper value
            // based on what the row is.
            return row.templateType;
        }

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createConstructionTemplate,
                name: 'Add Template',
                desc: 'Add Template',
                roles: ['admin__template__create'],
            });
        }

        setActionButtons();
    }
})();