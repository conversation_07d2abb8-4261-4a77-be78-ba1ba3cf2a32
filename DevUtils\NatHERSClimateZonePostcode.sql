USE [thermarate];

SELECT [climateZonePostcode].[NatHERSClimateZoneCode]
      ,[climateZonePostcode].[Postcode]
	  ,[suburb].[Name] [__Suburb]
	  ,[suburb].[StateCode] [__SuburbState]
      ,[climateZonePostcode].[CreatedOn]
      ,[climateZonePostcode].[CreatedByName]
      ,[climateZonePostcode].[ModifiedOn]
      ,[climateZonePostcode].[ModifiedByName]
      ,[climateZonePostcode].[Deleted]
  FROM [dbo].[RSS_NatHERSClimateZonePostcode] [climateZonePostcode]
  INNER JOIN [dbo].[RSS_Suburb] [suburb] ON [climateZonePostcode].[Postcode] = [suburb].[Postcode]
  WHERE 1=1
	AND [NatHERSClimateZoneCode] IN ('Nat52', 'Nat60')
  ORDER BY [climateZonePostcode].[NatHERSClimateZoneCode], [suburb].[Name]