<md-dialog ng-controller="ChangeDesignTemplateModalCtrl as vm">
    <form name="changetemplateform" novalidate>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Select Template</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="column" layout-margin layout-padding>

            <md-input-container class="md-block" ng-if="vm.buildingDesignTemplates&&vm.buildingDesignTemplates.length>0">
                <label>Template</label>
                <md-select name="template"
                           required
                           ng-model="vm.selectedTemplate">
                    <md-option ng-value="item"
                               ng-repeat="item in vm.buildingDesignTemplates">
                        {{item.templateName}}
                    </md-option>
                </md-select>
                <div ng-messages="changetemplateform.template.$error">
                    <div ng-message="required">Template is required.</div>
                </div>
            </md-input-container>

            <div flex ng-if="!vm.buildingDesignTemplates||vm.buildingDesignTemplates.length==0">
                No Templates Available for this client, description and project type combination.
            </div>
            <div flex ng-if="vm.selectedTemplate" class="red">
                Warning! Applying a template will irreversibly overwrite exisiting data.
            </div>
        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
            <md-button ng-disabled="changetemplateform.$invalid" ng-click="vm.submitSelection()">
                Ok
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>