﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('myAlert', function ($modal, $log) {
        return {
            restrict: 'E',
            scope: {
                mode: '@',
                boldTextTitle: '@',
                textAlert: '@'
            },
            link: function (scope, elm, attrs) {
                var template = '<div class="modal-body" style="padding:0px">' +
                                '<div class="alert alert-{{data.mode}}" style="margin-bottom:0px">' +
                                    '<strong>{{data.boldTextTitle}}</strong> {{data.textAlert}}' +
                                '</div>' +
                                '<button type="button" class="close" data-ng-click="close()" >' +
                                        '<span class="fa fa-remove">Ok</span>' +
                                    '</button>' +
                            '</div>';
                scope.data = {
                    mode: scope.mode,
                    boldTextTitle: scope.boldTextTitle,
                    textAlert: scope.textAlert
                }

                var ModalInstanceCtrl = function ($scope, $modalInstance, data) {

                    console.log(data);

                    scope.data = {
                        mode: scope.mode || 'info',
                        boldTextTitle: scope.boldTextTitle || 'title',
                        textAlert: scope.textAlert || 'text'
                    }
                };

                elm.parent().bind("click", function (e) {
                    scope.open();
                });

                scope.open = function () {

                    var modalInstance = $modal.open({
                        template: template,
                        controller: ModalInstanceCtrl,
                        backdrop: true,
                        keyboard: true,
                        backdropClick: true,
                        size: 'lg',
                        resolve: {
                            data: function () {
                                return scope.data;
                            }
                        }
                    });


                    modalInstance.result.then(function (selectedItem) {
                        scope.selected = selectedItem;
                    }, function () {
                        $log.info('Modal dismissed at: ' + new Date());
                    });
                }
            }
        };
    });
})();