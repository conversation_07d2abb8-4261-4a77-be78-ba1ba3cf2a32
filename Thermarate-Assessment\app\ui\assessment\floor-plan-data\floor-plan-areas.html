<style>
    .border-last:last-child > td {
        border-bottom: 2px solid grey;
    }

    .border-cell {
        border-bottom: 2px solid grey;
    }
</style>

<!-- Drawing Areas -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span style="margin-right: 15px;"
              ng-click="vm.expand('buildingSummary');">
            <span style="margin-right: 15px;" 
                  ng-style="{ 'color' : vm.source.spaces == null || vm.source.spaces.length == 0 ? 'grey' : 'black' }">
                Drawing Areas
            </span>
            <i ng-if="vm.sectionExpansions['buildingSummary']" class="fa fa-caret-up"></i>
            <i ng-if="(!vm.sectionExpansions['buildingSummary']) && vm.source.spaces.length > 0" 
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <!-- Rows in Section -->
    <md-card-content ng-show="vm.sectionExpansions['buildingSummary']"
                        style="margin: 0; padding: 0;">

        <table class="table table-striped table-hover table-condensed">

            <!-- Grouped Header-->
            <thead>

            <!-- Header Groupings (UPPER) -->
            <tr>
                <th class="text-left"
                    colspan="2" />

                <th style="text-align: center"
                    colspan="2">
                    Floor Area
                </th>

                <th style="text-align: center"
                    colspan="2">
                    Perimeter
                </th>

                <th style="text-align: center"
                    colspan="2">
                    Facade Area
                </th>
            </tr>

            <!-- Primary Header (LOWER) -->
            <tr>

                <th>Storey</th>

                <th>Space Type</th>

                <!-- Floor Area m2 / % -->
                <th style="text-align: right;">
                    m<sup>2</sup>
                </th>
                <th style="text-align: left;">
                    %
                </th>

                <!-- Perimeter m / % -->
                <th style="text-align: right;">
                    m
                </th>
                <th style="text-align: left;">
                    %
                </th>

                <!-- Facade Area m / % -->
                <th style="text-align: right;">
                    m<sup>2</sup>
                </th>
                <th style="text-align: left;">
                    %
                </th>
            </tr>

            </thead>

            <tbody ng-repeat="storeyGroup in vm.buildingSummaryGroups['allSpaces'].rows track by $index"
                   class="border-last">

                <!-- Groups -->
                <tr ng-repeat="row in storeyGroup.rows track by $index"
                    class="border-last">

                    <!-- Group -->
                    <td data-title="Description"
                        ng-if="$index == 0"
                        rowspan="{{storeyGroup.rows.length}}"
                        class="text-center border-cell">
                        <span>{{storeyGroup.name}}</span>
                    </td>

                    <!-- Space Type -->
                    <td>{{row.zoneType.description}}</td>

                    <!-- Floor Area -->
                    <td style="text-align: right;">
                        {{row.floorArea.toFixed(2)}}
                    </td>
                    <td style="text-align: left;">
                        {{row.floorAreaPercent.toFixed(2)}}
                    </td>

                    <!-- Perimeter -->
                    <td style="text-align: right;">
                        {{row.perimeter.toFixed(2)}}
                    </td>
                    <td style="text-align: left;">
                        {{row.perimeterPercent.toFixed(2)}}
                    </td>

                    <!-- Facade Area -->
                    <td style="text-align: right;">
                        {{row.perimeterWallArea.toFixed(2)}}
                    </td>
                    <td style="text-align: left;">
                        {{row.perimeterWallAreaPercent.toFixed(2)}}
                    </td>

                </tr>
            </tbody>
        </table>

    </md-card-content>

</md-card>
