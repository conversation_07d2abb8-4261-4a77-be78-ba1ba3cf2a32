// Name: errorservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'errorservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', errorservice]);

    function errorservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var initPromise,
            initFailed;
        var initialised = false;
        var errorData = {};
        var currentFilter = "";

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            currentFilter: function () { return currentFilter },
            error: function () { return errorData.error },
            getError: function (errorId) { return getError(errorId) },
            saveError: function (error) { return saveError(error) },
            deleteError: function (error) { return deleteError(error) },
            undoDeleteError: function (error) { return undoDeleteError(error) },
        };

        return service;


        //Fetch Error List from server
        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            var defer = $q.defer();
            var wkUrl = '../api/Error/Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
                return resp.data; 
            }
            function fail(error) {
                var msg = "Error getting error list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Fetch the requested errorId from the server
        function getError(errorId) {
            //Get error from the Server 
            return $http({
                url: '../api/Error/Get/',
                method: 'GET',
                params: { 'errorId': errorId },
                isArray: true
            }).then(success, fail)
            function success(resp) {
                if (Array.isArray(resp.data) && resp.data.length > 0) {
                    errorData.error = resp.data[0]; //Assign data to error
                }
                else {
                    errorData.error = resp.data;
                }
            }
            function fail(error) {
                var msg = "Error getting error list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Save error Detail on Server Call
        function saveError(data) {
            var saveData = data;

            //Save Error Detail on the Server
            return $http.post(baseUrl, saveData)
                .then(success, fail);

            function success(resp) {
                data = resp.data;
                log.logSuccess('Error Saved.');
                refresh(currentFilter); // Force the list of error to be refreshed.
            }
            function fail(error) {
                var msg = "Error saving error detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Delete error Detail on Server Call
        function deleteError(data) {
            initPromise = null;

            // Delete error Detail from the server
            data.isDeleted = true;
            initPromise = $http.post(baseUrl, data).then(success, fail);
            function success(resp) {
                log.logSuccess('Error Deleted.');
                refresh(currentFilter);  // Force the list of error to be refreshed.
            }
            function fail(error) {
                var msg = "Error deleting error detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }

        //Undo Delete error by calling server.
        function undoDeleteError(data) {
            initPromise = null;
            // Undo Delete by calling server
            data.isDeleted = false;
            data.isModified = true;
            data.ValidTo = null;
            initPromise = $http.post(baseUrl, data).then(success, fail);
            function success(resp) {
                log.logSuccess('Error Restored.');
                refresh(currentFilter);  // Force the list of error to be refreshed.
            }
            function fail(error) {
                var msg = "Error restoring error detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }


    }

})();