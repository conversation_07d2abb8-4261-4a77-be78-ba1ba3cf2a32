<section id="zonetype-list-view" class="main-content-wrapper" data-ng-controller="ZoneTypeListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="ZoneType"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.zonetypeList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="zonetypeList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>

                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="zoneTypeCode" class="can-sort text-center">Code</th>
                        <th st-sort="sortOrder" class="can-sort text-center">Sort Order</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.zonetypeList">
                        <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="zonetype-updateform({ zoneTypeCode: row.zoneTypeCode})">Select</md-button>  </td>
                        <td data-title="Description" class="text-left">{{::row.description }}</td>
                        <td data-title="Code" class="text-center">{{::row.zoneTypeCode }}</td>
                        <td data-title="Sort Order" class="text-center">{{::row.sortOrder | number }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
