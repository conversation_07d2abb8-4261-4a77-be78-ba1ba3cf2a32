(function () {
    'use strict';
    angular
        .module('app')
        .component('assessmentOutcomes', {
            bindings: {
                assessment: '<',
                isLocked: '<',
                allowOptionSelect: '<', // Should be FALSE if e.g. any form data from other tabs is missing.
                setItemReferences: '<',
                complianceMethodList: '<',
                clientId: '<',
                complianceStatusList: '<',
                complianceMethodCode: '<',
                assessmentSoftwareList: '<',
                uploadFile: '<',
                jobFiles: '<',
                setToNull: '<',
                downloadFile: '<',
                setFinalComplianceMethod: '&'
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/assessment-outcomes.html',
            controller: AssessmentOutcomes,
            controllerAs: 'vm'
        });

    AssessmentOutcomes.$inject = ['$scope', 'clientservice', 'uuid4', 'compliancemethodservice'];

    function AssessmentOutcomes($scope, clientservice, uuid4, compliancemethodservice) {
        var vm = this;
        vm.initalComplianceMet = false;

        // Watch for changes to ClientID and retreive client data when necessary.
        // Note: This was needed to prevent values going undefined (and never
        // being filled again...) on navigation.
        var unwatch = $scope.$watch('vm.clientId', initializeClient);
        function initializeClient(newVal, oldVal) {

            if (newVal == null || newVal == "")
                return;

            if (vm.clientId != null && vm.clientId != "") {
                clientservice.getClient(vm.clientId).then((data) => {
                    vm.client = data;
                });
            }
        }

        vm.selectedComplianceOption = function() {

            if (vm.assessment && vm.assessment.allComplianceOptions)
                return vm.assessment.allComplianceOptions.find(x => x.isSelected);
            else
                return null;
        }

        vm.simulationOptionIsSelected = function () {
            return vm.assessment.allComplianceOptions.find(x => x.isSelected && !x.isBaselineSimulation);
        }

        vm.complianceTypeChanged = function (option) {

            // NOTE: In the below, option.complianceMethodCode is still the OLD value.
            // When changing from (HER or DTS-HER) ---> (VURB or DTS-EP) THEN
            //  DO NOT copy reference.heating, reference.cooling, reference.totalEnergyLoad
            //  DO COPY proposed.heating, proposed.cooling, proposed.totalEnergyLoad
            if((option.complianceMethodCode === "CMHouseEnergyRating" ||
                option.complianceMethodCode === "CMPerfSolutionHER" || 
                option.complianceMethodCode === "CMPerfWAProtocolHER" ||
                option.complianceMethodCode === "CMPerfELL")
              &&
              (option.complianceMethod.complianceMethodCode === "CMPerfSolution" ||
                option.complianceMethod.complianceMethodCode === "CMPerfSolutionDTS")) {

                option.reference.heating = null;
                option.reference.cooling = null;
                option.reference.totalEnergyLoad = null;

                // Also copy across baseline data into reference (since HER etc don't feature a reference building)
                option.reference.zones = angular.copy(option.proposed.zones);
                option.reference.storeys = angular.copy(option.proposed?.storeys);
                option.reference.buildingZonesTemplateId = option.proposed.buildingZonesTemplateId;
                option.reference.projectDescription = option.proposed.projectDescription;
                option.reference.projectDescriptionOther = option.proposed.projectDescriptionOther;
                option.reference.projectClassification = option.proposed.projectClassification;
                option.reference.design = option.proposed.design;
                option.reference.buildingOrientation = option.proposed.buildingOrientation;
                option.reference.designFeatures = angular.copy(option.proposed.designFeatures);

                option.reference.zones.forEach(x => x.zoneId = uuid4.generate());

            }

            option.complianceMethodCode = option.complianceMethod.complianceMethodCode;
            option.availableSoftware = compliancemethodservice.determineAvailableSoftware(vm.assessmentSoftwareList, option.complianceMethodCode);

            // When changing from (VURB or DTS-EP) ---> (HER or DTS-HER) THEN
            //  DO NOT copy reference.heating, reference.cooling, reference.totalEnergyLoad <--- This will 'update' these values.
            if(option.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" ||
               option.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfELL") {
                compliancemethodservice.calculateHERCompliance(vm.assessment, option);
            }

        }
    }
})();
