// Name: manufacturerservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'manufacturerservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', manufacturerservice]);

    function manufacturerservice(common, config, $http, Upload) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'manufacturer/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getManufacturer: getManufacturer,
            createManufacturer: createManufacturer,
            updateManufacturer: updateManufacturer,
            deleteManufacturer:deleteManufacturer,
            undoDeleteManufacturer: undoDeleteManufacturer,
            copyManufacturer,
            processSpreadsheet,
            getAllManufacturersList: getAllManufacturersList
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Manufacturer list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getManufacturer(manufacturerId) {
            return $http({
                url: baseUrl + 'Get',
                params: {manufacturerId: manufacturerId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createManufacturer(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Manufacturer Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error creating Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateManufacturer(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Manufacturer Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteManufacturer(manufacturerId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { manufacturerId: manufacturerId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteManufacturer(manufacturerId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { manufacturerId: manufacturerId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyManufacturer(manufacturerId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { manufacturerId },
                method: 'POST',
            }).then(success, fail)

            function success(resp) {
                log.logSuccess("Manufacturer copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Manufacturer: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function processSpreadsheet(file) {

            let url = baseUrl + 'ProcessSpreadsheet';
            var promise = Upload.upload({
                url: url, // webapi url
                method: "POST",
                file: file,
            });

            return promise.progress(function (evt) {

            }).success(function (data, status, headers, config) {

                console.log(data);
                log.logSuccess("Manufacturer Database Updated");
                return data;

            }).error(function (data, status, headers, config) {

                log.logError("Error updating Manufacturer Database. Please check input file.", null, null, true);
            });
        }

        // Get a list of all manufacturers (aka manufacturers)
        function getAllManufacturersList() {
            return $http({
                url: baseUrl + 'GetAllManufacturerList',
                method: 'GET',
                cache: true,
            }).then(success, fail)
            
            function success(resp) {
                return resp.data;
            }
            function fail(error) {
                var msg = "Error getting All Manufacturers List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
