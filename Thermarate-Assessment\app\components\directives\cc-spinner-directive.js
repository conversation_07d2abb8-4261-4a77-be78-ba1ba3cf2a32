﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('ccSpinner', ['$window', function ($window) {
        // Description:
        //  Creates a new Spinner and sets its options
        // Usage:
        //  <div data-cc-spinner="vm.spinnerOptions"></div>
        var directive = {
            link: link,
            restrict: 'A'
        };
        return directive;

        function link(scope, element, attrs) {
            var showMidPage = false;
            if (scope.isReport && scope.isReport == true) {
                showMidPage = true;
            }
            scope.spinner = null;
            scope.$watch(attrs.ccSpinner, function (options) {
                if (scope.spinner) {
                    scope.spinner.stop();
                }
                options = { radius: 5, width: 3, length: 6 };
                if (showMidPage == true) {
                    options = { radius: 28, width: 15, length: 30 };
                }

                scope.spinner = new $window.Spinner(options);
                scope.spinner.spin(element[0]);
            }, true);
        }
    }]);
})();