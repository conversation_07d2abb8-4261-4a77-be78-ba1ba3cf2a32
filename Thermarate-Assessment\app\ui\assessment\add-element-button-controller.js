(function () {

    'use strict';
    angular
        .module('app')
        .component('addElementButton', {
            bindings: {
                parent: '<',  // The parent construction which houses elements.
                building: '<',                
                disabled: '<',
                type: '<', // Specify 'service' to add the item to the services..
                elementsAddedCallback: '&',
                defaultControlDevice: '<', // ...
            },
            templateUrl: 'app/ui/assessment/add-element-button.html',
            controller: AddElementButton,
            controllerAs: 'vm'
        });

    AddElementButton.$inject = ['common', 'constructionservice', 'zoneservice', 'uuid4'];

    function AddElementButton(common, constructionservice, zoneservice, uuid4) {

        let vm = this;

        // Set show flags instead of a brrr function
        vm.showGroupDividers = [false, false, false, false, false];

        vm.openMenu = function ($mdOpenMenu, ev) {
            $mdOpenMenu(ev);
        };

        /**
         * Adds a new element (of the same template) to the given group/parent.
         * 
         * @param {any} parent
         * @param {any} building
         */
        vm.addElement = function(parent, building, linkId = null, storey = null, updateElemCodes = true) {

            const elem = vm.createElement(parent, building, linkId, storey);
            parent.elements.push(elem);

            if(updateElemCodes === true) {
                if(vm.type !== 'services')
                    vm.elementsAddedCallback({categoryCode: vm.parent.category.constructionCategoryCode});
                else
                    vm.elementsAddedCallback({categoryCode: vm.parent.serviceCategory.serviceCategoryCode});
            }
        }

        vm.createElement = function(parent, building, linkId = null, storey = null) {

            const elem = parent;
            const copy = angular.copy(elem);

            if(vm.type !== 'services') {

                // Create a copy of base element and then nullify some data.

                copy.constructionId = uuid4.generate();

                // Alistair has specified that any data which appears in the element row itself (i.e. not the
                // parent row) should be empty when adding an element.
                copy.source = 'manual';
                copy.grossArea = 0;
                copy.netArea = 0;
                copy.width = null;
                copy.height = null;
                copy.parentZoneId = linkId;
                copy.isParentElement = false;
                copy.adjacentZoneNumber = null;
                copy.storey = storey;
                copy.tilt = null;
                copy.azimuth = null;
                copy.shadeProjection = null;
                copy.shadeOffset = null;
                copy.hasInteriorShades = null;
                copy.hasExteriorShades = null;
                copy.shaftLength = null;
                copy.shaftArea = null;
                copy.shaftReflectance = null;
                copy.shaftWallRValue = null;
                copy.hasDiffuser = null;
                copy.sector = null;
                // Shading
                copy.horizontalProjection1 = null;
                copy.horizontalProjection2 = null;
                copy.verticalScreen1 = null;
                copy.verticalScreen2 = null;
                copy.verticalScreen3 = null;
                copy.leftWingWall = null;
                copy.rightWingWall = null;

            } else {

                copy.serviceTemplateId = uuid4.generate();

                // Alistair has specified that any data which appears in the element row itself (i.e. not the
                // parent row) should be empty when adding an element.
                copy.source = 'manual';

                copy.width = null;
                copy.length = null;
                copy.parentZoneId = linkId;
                copy.storey = storey;
                copy.isParentElement = false;
                copy.isPrimary = false;
                copy.number = 1;

                if(vm.defaultControlDevice != null)
                    copy.serviceControlDevice = vm.defaultControlDevice;

                delete copy.adjacentZoneNumber;

                delete copy.hasTimeSwitch;
                delete copy.hasCover;
                delete copy.hasSpeedController;
                delete copy.isPermanentlyInstalled;
                delete copy.isSealed;
                delete copy.isFlued;
                delete copy.isRecessed;
                delete copy.isDucted;
                delete copy.starRating;
                delete copy.bladeDiameter;
                delete copy.cutOutDiameter;
                delete copy.lampPowerRating;
                delete copy.volume;
                delete copy.arrayCapacity;
                delete copy.tilt;
                delete copy.azimuth;
                delete copy.panelArea;
            }

            return copy;
        }

        // This array is looped over within our html template to create our available 'add multiple' types.
        // Dynamical as only want each of them to appear if they exist on the assessment.
        // Need to be in a specific order, so have them all in the array but only show them if flagged.
        // Need group dividers for specific groups, so have each group in their own array.
        vm.addElementTypeGroups = [
            [
                // All Interior Zones
                { id: 'AllZones', title: 'All Zones', callback: () => addElementsForZones(zoneservice.interiorZones(vm.building.zones)), show: true}
            ],
            [
                { id: 'ZAKitchenLiving', title: 'All Kitchen / Living Zones', callback: () => addElementsForZones(zoneservice.kitchenLivingZones(vm.building.zones)), show: false },
                { id: 'ZALiving', title: 'All Living Zones', callback: () => addElementsForZones(zoneservice.livingZones(vm.building.zones)), show: false },
                { id: 'ZADayTime', title: 'All Day Time Zones', callback: () => addElementsForZones(zoneservice.dayTimeZones(vm.building.zones)), show: false },
                { id: 'ZABedroom', title: 'All Bedroom Zones', callback: () => addElementsForZones(zoneservice.bedroomZones(vm.building.zones)), show: false },
                { id: 'ZANightTime', title: 'All Night Time Zones', callback: () => addElementsForZones(zoneservice.nightTimeZones(vm.building.zones)), show: false },
                { id: 'unconditioned', title: 'All Unconditioned Zones', callback: () => addElementsForZones(zoneservice.unconditionedZones(vm.building.zones)), show: false },
                { id: 'garage', title: 'All Garage Zones', callback: () => addElementsForZones(zoneservice.garageZones(vm.building.zones)), show: false },
                { id: 'garageConditioned', title: 'All Garage Conditioned Zones', callback: () => addElementsForZones(zoneservice.garageConditionedZones(vm.building.zones)), show: false }
            ],
            [
                { id: 'ZTHabitableRoom', title: 'All Habitable Zones', callback: () => addElementsForZones(zoneservice.habitableZones(vm.building.zones)), show: false },
                { id: 'ZTNonHabitableRoom', title: 'All Non-Habitable Zones', callback: () => addElementsForZones(zoneservice.nonHabitableZones(vm.building.zones)), show: false },
                { id: 'ZTInterconnecting', title: 'All Interconnecting Space Zones', callback: () => addElementsForZones(zoneservice.interconnectingZones(vm.building.zones)), show: false },
                { id: 'ZTClass10A', title: 'All Class 10a Zones', callback: () => addElementsForZones(zoneservice.class10AZones(vm.building.zones)), show: false }    
            ],
            [
                { id: 'conditioned', title: 'All Conditioned Zones', callback: () => addElementsForZones(zoneservice.conditionedZones(vm.building.zones)), show: false },
                { id: 'unconditioned', title: 'All Unconditioned Zones', callback: () => addElementsForZones(zoneservice.unconditionedZones(vm.building.zones)), show: false },
            ],
            [
                { id: 'Class1A', title: 'All Class 1a Zones', callback: () => addElementsForZones(zoneservice.class1AZones(vm.building.zones)), show: false },
                { id: 'ZTClass10A', title: 'All Class 10a Zones', callback: () => addElementsForZones(zoneservice.class10AZones(vm.building.zones)), show: false }
            ]
        ];

        vm.building.zones?.forEach(zone => {

            // Conditional as the different codes appear in different parts of the assessment object

            // Zone Activity Code
            if (zone?.zoneActivity?.zoneActivityCode) {

                showElementType(zone?.zoneActivity?.zoneActivityCode);

                // Garage Zone
                if (zone.zoneActivity?.zoneActivityCode === 'ZAGarage' ||
                    zone.zoneActivity?.zoneActivityCode === 'ZAGarageConditioned')
                    showElementType('garage');

                // Garage Conditioned Zone
                if ((zone.zoneActivity?.zoneActivityCode === "ZAGarage" ||
                    zone.zoneActivity?.zoneActivityCode === "ZAGarageConditioned") &&
                    zone.conditioned === true)
                    showElementType('garageConditioned');
            }

            // Zone Type Code
            if (zone?.zoneType?.zoneTypeCode)
                showElementType(zone?.zoneType?.zoneTypeCode);

            // Conditioned
            if (zone?.conditioned)
                showElementType('conditioned');
            else
                showElementType('unconditioned');

            // NCC Classification
            if (zone?.nccClassification?.nccClassificationCode === "Class1A")
                showElementType('Class1A');
        });

        function showElementType(zoneCode) {
            // Loop through all element types and set the show flag to true for every zone code that matches
            vm.addElementTypeGroups.forEach(group => {
                group.forEach(group => {
                    // Don't break early as duplicates exist
                    if (zoneCode === group.id)
                        group.show = true;
                });
            });
        }

        // Set dividers show flag based on the below populated array.
        vm.addElementTypeGroups.forEach((elementGroup, i) => {
            vm.showGroupDividers[i] = elementGroup.some(item => item.show === true);
        });

        async function addElementsForZones(zones) {

            // console.log('zones:', zones);

            if(zones !== null && zones !== undefined) {

                const newElements = [];

                for (let i = 0; i < zones.length; i++) {
                    const zone = zones[i];
                    const elem = vm.createElement(vm.parent, vm.building, zone.linkId, zone.storey, false)
                    newElements.push(elem);
                }

                vm.parent.elements.push(...newElements);

                if(vm.type !== 'services')
                    vm.elementsAddedCallback({categoryCode: vm.parent.category.constructionCategoryCode});
                else
                    vm.elementsAddedCallback({categoryCode: vm.parent.serviceCategory.serviceCategoryCode});
            }
            else {
                // This most likely indicates you're adding elements to a template, and thus have no known zones or
                // storeys to insert into...
                vm.addElement(vm.parent, vm.building)
            }

        }

    }
})();