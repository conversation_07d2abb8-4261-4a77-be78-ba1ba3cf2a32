using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using RediSoftware.Dtos;
using AutoMapper;
using RediSoftware.Redi_Utility;
using RediSoftware.Helpers;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using System.Windows.Data;

namespace RediSoftware.BusinessLogic
{
    public enum AssessmentStatus
    {
        /// <summary> Processing (New Job)         </summary>
        ADraft,
        /// <summary> Final Review                 </summary>
        AComplete,
        /// <summary> Preliminary Review           </summary>
        APreliminaryReview,
        /// <summary> Compliance Options Provided  </summary>
        ACompliance,
        /// <summary> Cancelled                    </summary>
        ACancelled,
        /// <summary> Processing (Option Selected) </summary>
        AOptionSelected,
        /// <summary> In Progress                  </summary>
        AInProgress,
        /// <summary> Complete                     </summary>
        AIssued,
        /// <summary> Superseded                   </summary>
        ASuperseded,
        /// <summary> Processing (Recertification) </summary>
        ARecertification
    }

    public class Assessment : BusinessLogicBase
    {
		private Guid _assessmentId;
		private RSS_Assessment _assessmentDbModel;
        private Func<AssessmentPdfGenerator> _assessmentPDFFactory;
        private Func<Job> _jobFactory;
        private Func<ArchivePDF> _archivePDFFactory;
        private Func<AssessmentDrawing> _assessmentDrawingFactory;
        private Func<Construction> _constructionFactory;
        private Func<ServiceTemplate> _serviceFactory;

        private static Random _rand = new Random();

        /// <summary>
        /// Returns the Id in the database model (useful to get an Id after changes have been commited)
        /// </summary>
        public Guid CreatedId
		{
			get
			{
				return _assessmentDbModel.AssessmentId;
			}
		}

        public AssessmentDto Dto
        {
            get
            {
                AssessmentDto dto = _mapper.Map<AssessmentDto>(_assessmentDbModel);

                return dto;
            }
        }

        public ExtendedAssessmentDto ExtendedDto
        {
            get
            {
                return BuildExtendedDto(_assessmentDbModel);
            }
        }

        public static ExtendedAssessmentDto BuildExtendedDto(RSS_Assessment model, IUnitOfWork unitOfWork, IMapper mapper)
        {
            ExtendedAssessmentDto dto = mapper.Map<ExtendedAssessmentDto>(model);

            dto.AllComplianceOptions = unitOfWork.Context.RSS_AssessmentComplianceOption
                .Where(s => s.AssessmentId == model.AssessmentId && s.Deleted == false)
                .OrderBy(s => s.OptionIndex)
                .ProjectTo<AssessmentComplianceOptionDto>(mapper.ConfigurationProvider)
                .ToList();

            if (model.RSS_Job != null)
            {
                dto.Job = unitOfWork.Context.RSS_Job.Where(s => s.JobId == model.JobId).ProjectTo<SimpleJobDto>(mapper.ConfigurationProvider).First();
                dto.Job.JobEvents = unitOfWork.Context.RSS_JobEvent.Where(s => s.JobId == model.JobId && s.Deleted == false).OrderByDescending(s => s.CreatedOn).ProjectTo<JobEventDto>(mapper.ConfigurationProvider).ToList();
            }

            foreach (var option in dto.AllComplianceOptions)
            {
                option.AssessmentDrawings = option.AssessmentDrawings.OrderBy(x => x.DrawingNumber).ToList();

                if (option.Proposed != null)
                    option.Proposed.Zones = unitOfWork.Context.RSS_Zone
                        .Where(s => s.AssessmentComplianceBuildingId == option.ProposedBuildingId && s.Deleted == false)
                        .OrderBy(s => s.SortOrder)
                        .ProjectTo<ZoneDto>(mapper.ConfigurationProvider)
                        .ToList();

                if (option.Reference != null)
                    option.Reference.Zones = unitOfWork.Context.RSS_Zone
                        .Where(s => s.AssessmentComplianceBuildingId == option.ReferenceBuildingId && s.Deleted == false)
                        .OrderBy(s => s.SortOrder)
                        .ProjectTo<ZoneDto>(mapper.ConfigurationProvider)
                        .ToList();
            }

            return dto;
        }

        public ExtendedAssessmentDto BuildExtendedDto(RSS_Assessment _model)
        {
            return BuildExtendedDto(_model, _unitOfWork, _mapper);
        }

        public Assessment(IUnitOfWork unitOfWork,
            IMapper mapper,
            Func<AssessmentPdfGenerator> assessmentPDFFactory,
            Func<Job> jobFactory,
            Func<ArchivePDF> archivePDFFactory,
            Func<AssessmentDrawing> assessmentDrawingFactory,
            Func<Construction> constructionFactory,
            Func<ServiceTemplate> serviceFactory)
        {
            _mapper = mapper;
			_unitOfWork = unitOfWork;
            _assessmentPDFFactory = assessmentPDFFactory;
            _jobFactory = jobFactory;
            _archivePDFFactory = archivePDFFactory;
            _assessmentDrawingFactory = assessmentDrawingFactory;
            _constructionFactory = constructionFactory;
            _serviceFactory = serviceFactory;
        }

        //hacky shit to use teh correct unit of work context when resolving the Assessment class from service locator
        public void SetUnitOfWork(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

		/// <summary>
		/// Get db model for supplied Id
		/// </summary>
        public Assessment Get(Guid assessmentId)
        {
			_assessmentId = assessmentId;
			GetModel();

			if (_assessmentDbModel == null)
			{
				throw new Exception(string.Format("{1} row not found for Id: {0}", _assessmentId, "RSS_Assessment"));
			}

            return this;
        }

        public UserDto GetLastAssignedAssessor()
        {
            var user = _unitOfWork.Context.RSS_Assessment
                .Where(s => s.JobId == _assessmentDbModel.JobId && s.AssessorUserId != null)
                .OrderByDescending(s => s.RSS_AssessmentProjectDetail.AssessmentVersion)
                .Select(s => s.RSS_User_Assessor)
                .FirstOrDefault();

            UserDto dto = null;
            if (user != null)
            {
                dto = _mapper.Map<UserDto>(user);
            }
            return dto;
        }

		private Boolean GetModel()
		{
			_assessmentDbModel = _unitOfWork.Context.RSS_Assessment.Where(s => s.AssessmentId == _assessmentId).FirstOrDefault();
			if (_assessmentDbModel != null)
			{
				return true;
			}
			return false;
		}

        /// <summary>
        /// Using the given ExtendedAssessmentDto as a base, creates and inserts an RSS_Assessment into the
        /// database.
        /// </summary>
        public RSS_Assessment Create(ExtendedAssessmentDto assessmentDto, bool invalid, string forceStatus = null, bool isRecertification = false)
        {
            if (assessmentDto.AssessmentId == null || assessmentDto.AssessmentId == Guid.Empty)
			{
				_assessmentId = SequentialGuid.NewSequentialGuid();
				assessmentDto.AssessmentId = _assessmentId;
			}
			else
			{
				// Guid passed in - see if it already exists - if found update.
				_assessmentId = assessmentDto.AssessmentId;
				GetModel();
				if (GetModel())
				{
					// Already exists so go update instead.
					Update(assessmentDto, invalid);
					return null;
				}
            }

            assessmentDto.CreatedOn = DateTime.Now;
            assessmentDto.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            if (!isRecertification)
            {
                if (string.IsNullOrWhiteSpace(forceStatus))
                {
                    SetStatus(assessmentDto, invalid);
                }
                else
                {
                    assessmentDto.StatusCode = forceStatus;
                }
            }

            SetAssessor(assessmentDto);

            // Update the database model
            _assessmentDbModel = _mapper.Map<RSS_Assessment>(assessmentDto);
            RSS_Job dbJob = _unitOfWork.Context.RSS_Job.Find(_assessmentDbModel.JobId);
            if (dbJob != null) {
                dbJob.CurrentAssessmentId = assessmentDto.AssessmentId;
            }

            _unitOfWork.Context.RSS_Assessment.Add(_assessmentDbModel);


            if (_assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null && !isRecertification)
            {
                _jobFactory().Get(_assessmentDbModel.JobId.Value).UpdateJobStatusWithAssessmentStatus((AssessmentStatus)Enum.Parse(typeof(AssessmentStatus), _assessmentDbModel.StatusCode));

                // NOTE: This did not work before when copying. Not sure why, testing to see if this change works.
                //var rssjob = _unitOfWork.Context.RSS_Job.Where(x => x.JobId == _assessmentDbModel.JobId).First();
                var client = _unitOfWork.Context.RSS_Client.Where(y => y.ClientId == dbJob.ClientId).FirstOrDefault();
                string clientName = client.ClientName;

                LogEvent(string.Format("New Assessment created for {0}", clientName), (Guid)_assessmentDbModel.JobId);
            }

            return _assessmentDbModel;
        }

        private void SetAssessor(ExtendedAssessmentDto assessmentDto)
        {

            //set the assessor
            RSS_Job job = _unitOfWork.Context.RSS_Job.Find(assessmentDto.JobId);
            if (job == null && assessmentDto.Job != null)
            {
                job = _unitOfWork.Context.RSS_Job.Find(assessmentDto.Job.JobId);
            }
            if (job != null)
            {
                assessmentDto.AssessorUserId = job.AssessorUserId;
            }
            if (assessmentDto.AssessorUserId == null)
            {
                string currentUserId = null;
                try
                {
                    currentUserId = UtilityFunctions.UserID;
                }
                catch (Exception ex)
                {
                    ErrorWriter.WriteError("Error Casting utility function user id of current user (is object) to a Guid? . Assessment id = " + assessmentDto.AssessmentId + ". Exception message = " + ex.Message);
                }
                Guid? assessorId = _unitOfWork.Context.RSS_User.Where(s => s.AspNetUserId == currentUserId.ToString()).FirstOrDefault()?.UserId;
                assessmentDto.AssessorUserId = assessorId;
            }
        }

        public void UpdateJobClient(ExtendedAssessmentDto assessment)
        {
            var jobDb = _unitOfWork.Context.RSS_Job.Find(assessment.JobId);
            jobDb.ClientId = assessment.Job.Client.ClientId;
        }

        public void UpdateJobAssessor(ExtendedAssessmentDto assessment)
        {
            var job = _unitOfWork.Context.RSS_Job.Find(assessment.JobId);
            job.AssessorUserId = assessment.AssessorUser.UserId;
        }

        public void SetStatus(ExtendedAssessmentDto assessment, bool invalid, bool newRecord = false)
        {
            string oldStatus = assessment.StatusCode;

            var selectedSim = assessment.SelectedSimulation;
            var proposed = selectedSim.Proposed;

            if (assessment.JobId == null || newRecord)
            {
                assessment.StatusCode = AssessmentStatus.AInProgress.ToString("G");
                return;
            }

            assessment.ComplianceStatus = _unitOfWork.Context.RSS_ComplianceStatus.Where(s => s.ComplianceStatusCode == assessment.ComplianceStatusCode).ProjectTo<ComplianceStatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();
            List<StatusDto> statuses = _unitOfWork.Context.RSS_Status.ProjectTo<StatusDto>(_mapper.ConfigurationProvider).ToList();
            JobStatus? newJobStatus = null;
            Job job = _jobFactory().Get((Guid)assessment.JobId);

            var allConstructionCategories = _constructionFactory().GetConstructionCategoryList();
            var allServiceCategories = _serviceFactory().GetServiceCategories();

            // Loop over all options and check to make sure construction categories are specified
            // for all required.
            foreach(var option in assessment.AllComplianceOptions)
            {
                foreach (var category in allConstructionCategories)
                {
                    bool req = true;

                    if(option.Proposed.CategoriesNotRequired.ContainsKey(category.ConstructionCategoryCode.ToLower()))
                        req = option.Proposed.CategoriesNotRequired[category.ConstructionCategoryCode.ToLower()];

                    // If the proposed building is set to use external data for this category we also skip it.
                    if (category.AllowExternalData &&
                        (option.Proposed.CategoriesWithExternalData.ContainsKey(category.ConstructionCategoryCode.ToLower()) &&
                         option.Proposed.CategoriesWithExternalData[category.ConstructionCategoryCode.ToLower()] == true))
                        continue;


                    // If this is NULL OR FALSE it means we MUST HAVE at least 1 construction item of that category.
                    if (!option.Proposed.CategoriesNotRequired.ContainsKey(category.ConstructionCategoryCode.ToLower()) ||
                        option.Proposed.CategoriesNotRequired[category.ConstructionCategoryCode.ToLower()] == false) {

                        var found = this.CheckBuildingContainsConstructionOfCategory(category, option.Proposed);

                        if (!found)
                        {
                            invalid = true;
                            break;
                        }
                    }
                }

                foreach (var category in allServiceCategories)
                {
                    bool req = true;

                    if(option.Proposed.CategoriesNotRequired.ContainsKey(category.ServiceCategoryCode.ToLower()))
                        req = option.Proposed.CategoriesNotRequired[category.ServiceCategoryCode.ToLower()];

                    // If this is NULL OR FALSE it means we MUST HAVE at least 1 service item of that category.
                    if (!option.Proposed.CategoriesNotRequired.ContainsKey(category.ServiceCategoryCode.ToLower()) ||
                        option.Proposed.CategoriesNotRequired[category.ServiceCategoryCode.ToLower()] == false) {

                        var found = this.CheckBuildingContainsServicesOfCategory(category, option.Proposed);

                        if (!found)
                        {
                            invalid = true;
                            break;
                        }
                    }
                }

                if (invalid == true)
                    break;
            }

            // If the selected drawing is the baseline simulation, then the selected simulation REQUIRES drawings.
            if (selectedSim == assessment.BaselineSimulation)
            {
                if (selectedSim.AssessmentDrawings == null || selectedSim.AssessmentDrawings.Count < 1)
                    invalid = true;
            }
            else
            {
                // If the selected simulation is NOT the baseline, then it only needs drawings
                // in certain instances, otherwise we still wish to check the baseline.
                if (selectedSim.UpdatedDrawingsRequired.HasValue && selectedSim.UpdatedDrawingsRequired.Value)
                {
                    if (selectedSim.AssessmentDrawings == null || selectedSim.AssessmentDrawings.Count < 1)
                        invalid = true;
                }
                else
                {
                    if (assessment.BaselineSimulation.AssessmentDrawings == null ||
                               assessment.BaselineSimulation.AssessmentDrawings.Count < 1)
                        invalid = true;
                }
            }

            if (invalid == false && assessment.ComplianceStatusCode == ComplianceStatusCode.CSAchieved.ToString("G")
                && assessment.StatusCode == AssessmentStatus.AIssued.ToString("G"))
            {
                // No Change - Already Issued.
                assessment.Status = statuses.Where(s => s.StatusCode == assessment.StatusCode).First();
                newJobStatus = null;
            }
            else if (invalid == false && assessment.ComplianceStatusCode == ComplianceStatusCode.CSAchieved.ToString("G"))
            {

                // We've come from the "compliance options provided" state. Check to ensure our selected sim either
                // doesn't require drawings OR it has drawings.
                if (assessment.StatusCode == AssessmentStatus.ACompliance.ToString("G"))
                {
                    var drawingsOk = selectedSim.UpdatedDrawingsRequired == false ||
                                     selectedSim.AssessmentDrawings.Count > 0;

                    if(drawingsOk)
                    {
                        // Can proceed to the 'Option Selected' state. We used to skip straight to "Final Review", but
                        // we now have to hit this state as this will give the frontend one last chance to ask the user
                        // to stamp any unstamped drawings before heading into the 'Final Review' state.
                        assessment.StatusCode = AssessmentStatus.AOptionSelected.ToString("G");
                        assessment.Status = statuses.Where(s => s.StatusCode == "AOptionSelected").First();
                        newJobStatus = JobStatus.JOptionSelected;
                    }
                    else
                    {
                        // Waiting on compliance drawings to be uploaded.
                        assessment.StatusCode = AssessmentStatus.ACompliance.ToString("G");
                        assessment.Status = statuses.Where(s => s.StatusCode == "ACompliance").First();
                        newJobStatus = JobStatus.JCompliance;
                    }
                }
                else
                {
                    // We've NOT come from the "compliance options selected" state, which means we can go straight to
                    // "final review".
                    assessment.StatusCode = AssessmentStatus.AComplete.ToString("G");
                    assessment.Status = statuses.Where(s => s.StatusCode == "AComplete").First();
                    newJobStatus = JobStatus.JComplete;
                    assessment.AssessmentProjectDetail.CompletedDate = DateTime.Now;
                    if (assessment.CertificateDateOverride == null)
                    {
                        assessment.CerficateDate = DateTime.Now;
                    }
                }
            }
            else if (invalid == false && (assessment.ComplianceStatusCode == ComplianceStatusCode.CSNotAchieved.ToString("G") || assessment.ComplianceStatusCode == null) && oldStatus != AssessmentStatus.ACompliance.ToString())
            {
                // NOTE (Luke 23/03/21): Alistair has asked for us to 'completely remove' any trace of the 'Assessment Type' functionality.
                // Previously there was an if clause here with different logic if the assessment was preliminary - this is now redundant.

                // Only go to Compliance Options Provided when we have Compliance Options and at least 1 is compliant (so just need 1 selected).
                if (assessment.ComplianceOptions.Count() > 0 && assessment.ComplianceOptions.Where(x => x.IsCompliant == true).FirstOrDefault() != null)
                {
                    // We now go to the "Preliminary Review" state BEFORE going to the "Compliance Options Available"
                    // state so that the generated reports can be checked.
                    assessment.StatusCode = AssessmentStatus.APreliminaryReview.ToString("G");
                    assessment.Status = statuses.Where(s => s.StatusCode == "APreliminaryReview").First();
                    newJobStatus = JobStatus.JPreliminaryReview;
                }
                else
                {
                    assessment.StatusCode = AssessmentStatus.AInProgress.ToString("G");
                    assessment.Status = statuses.Where(s => s.StatusCode == AssessmentStatus.AInProgress.ToString("G")).First();
                    if (job.Dto.StatusCode == "JDraft" || job.Dto.StatusCode == JobStatus.JCompliance.ToString("G"))
                    {
                        newJobStatus = JobStatus.JInProgress;
                    }
                }

            }
            else if (oldStatus != AssessmentStatus.ACompliance.ToString()) {
                assessment.StatusCode = AssessmentStatus.AInProgress.ToString("G");
                assessment.Status = statuses.Where(s => s.StatusCode == AssessmentStatus.AInProgress.ToString("G")).First();
                newJobStatus = JobStatus.JInProgress;
            }

            if (newJobStatus != null)
            {
                job.UpdateJobStatus((JobStatus)newJobStatus);
            }

            foreach (RSS_Assessment dbAssessment in _unitOfWork.Context.RSS_Assessment.Where(s => s.JobId == assessment.JobId && s.AssessmentId != assessment.AssessmentId && s.Deleted == false)) {
                dbAssessment.StatusCode = AssessmentStatus.ASuperseded.ToString("G");
            }

            // Account for status change
            if (assessment.StatusCode != AssessmentStatus.AIssued.ToString())
            {
                assessment.AssessmentProjectDetail.ComplianceCost = null;
            }
        }

        /// <summary>
        /// Update the Assessment
        /// </summary>
        /// <param name="assessmentDto"></param>
        /// <param name="invalid"></param>
        /// <param name="newRecord"></param>
        /// <param name="templateApplied">If Template Applied is true, then force delete all existing records so as to not double up</param>
        public void Update(ExtendedAssessmentDto assessmentDto, bool invalid, bool newRecord = false, bool templateApplied = false)
        {
            if (assessmentDto.AssessmentProjectDetail == null)
                throw new Exception("Error: 'Assessment Project Detail' cannot be null when updating an assessment.");

            if(assessmentDto.AllComplianceOptions == null || assessmentDto.AllComplianceOptions.Count == 0)
                throw new Exception("Error: Invalid assessment info supplied. Missing all compliance options.");

            string oldStatus = String.Empty;
			if (_assessmentDbModel == null) {throw new Exception(string.Format("_assessmentDbModel not set"));}
            assessmentDto.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            assessmentDto.ModifiedOn = DateTime.Now;
			assessmentDto.AssessmentProjectDetail.ModifiedOn = DateTime.Now;
            assessmentDto.Job.ModifiedOn = DateTime.Now;

            // Stop accidentally setting status back to what was loaded on initial page load
            assessmentDto.Status = null;

            if (_assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null &&
                _assessmentDbModel.StatusCode != assessmentDto.StatusCode)
            {
                oldStatus = assessmentDto.StatusCode;
            }

            // Set the classification code
            SetStatus(assessmentDto, invalid, newRecord);

            if (assessmentDto.AssessorUserId == null) {
                SetAssessor(assessmentDto);
            }

            //Set the Job's assigned assessor Id if it changes
            if (assessmentDto.Job != null && assessmentDto.AssessorUser != null) {
                UpdateJobAssessor(assessmentDto);
            }

            //Must update Job client Id as RSS_Job is ignored in the mapper
            if (assessmentDto.Job != null && assessmentDto.Job.Client != null) {
                UpdateJobClient(assessmentDto);
            }

            if (assessmentDto.AllComplianceOptions != null)
            {
                List<Guid> complianceOptionIds = new List<Guid>();
                foreach (AssessmentComplianceOptionDto opt in assessmentDto.AllComplianceOptions)
                {
                    RSS_AssessmentComplianceOption dbObject = _unitOfWork.Context.RSS_AssessmentComplianceOption.Find(opt.ComplianceOptionsId);
                    if (dbObject != null)
                    {
                        // If the last time this option was saved it was unselected, and now it's selected, we wish
                        // the current user to be considered who modified it.
                        if(!dbObject.IsSelected && opt.IsSelected)
                            opt.ModifiedByName = UtilityFunctions.CurrentUserFullName;
                        else if(dbObject.IsSelected && opt.IsSelected == false)
                            opt.ModifiedByName = null; // Otherwise we nullify it. (NOTE: The modifiedByName is not applied ANYWHERE else atm so shouldn't cause problems..)
                                                       // else: Leave as-is

                        if (opt.CreatedByName == null)
                        {
                            opt.CreatedByName = dbObject.CreatedByName;
                        }
                        if (opt.Proposed.CreatedByName == null)
                        {
                            opt.Proposed.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
                            opt.Proposed.CreatedOn = DateTime.Now;
                            opt.Proposed.AssessmentComplianceOptionId = opt.ComplianceOptionsId;
                        }
                        if (opt.Reference.CreatedByName == null)
                        {
                            opt.Reference.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
                            opt.Reference.CreatedOn = DateTime.Now;
                            opt.Reference.AssessmentComplianceOptionId = opt.ComplianceOptionsId;
                        }

                        // Update any associated drawings and delete if needed.
                        AssessmentComplianceOption.UpdateAssessmentDrawings(opt, assessmentDto.AssessmentId, _unitOfWork, _mapper);

                        // Update associated Zones if needed.
                        Zone.UpdateZones(opt.Proposed, _unitOfWork, _mapper);
                        Zone.UpdateZones(opt.Reference, _unitOfWork, _mapper);

                        _mapper.Map(opt, dbObject);
                        complianceOptionIds.Add(dbObject.ComplianceOptionsId);

                        _unitOfWork.Context.SaveChanges();
                    }
                    else
                    {

                        var saved = AssessmentComplianceOption.InsertIntoDatabase(opt, _mapper, _unitOfWork);

                        // Make sure any ID's are not matching old drawings before you call this or you might have problems.
                        AssessmentComplianceOption.UpdateAssessmentDrawings(opt, assessmentDto.AssessmentId, _unitOfWork, _mapper);

                        complianceOptionIds.Add(saved.ComplianceOptionsId);
                    }
                }

                // Any ids not in the current list have been removed client-side, so set as deleted.
                _unitOfWork.Context.RSS_AssessmentComplianceOption
                .Where(s => s.Deleted == false && s.AssessmentId == assessmentDto.AssessmentId && complianceOptionIds.Contains(s.ComplianceOptionsId) == false)
                .ToList()
                .ForEach(x => x.Deleted = true);

                // ?????????????
                assessmentDto.AllComplianceOptions = null;
            }

            // Compare addresses
            if (assessmentDto.AssessmentProjectDetail != null)
            {
                RecordChangeOfAddressIfNeeded(assessmentDto.AssessmentProjectDetail);

                // Insert our map data if needed.
                if (assessmentDto.AssessmentProjectDetail.MapImageFile != null)
                {
                    // Insert our MapImageFile into the DB if it doesn't already exist
                    // Note: I'm not sure if this is a hack and EF should handle this case automatically or not.
                    var existing = _unitOfWork.Context.RSS_File.Where(s => s.FileId == assessmentDto.AssessmentProjectDetail.MapImageFile.FileId).FirstOrDefault();
                    if (existing == null)
                    {
                        RSS_File file = _mapper.Map<RSS_File>(assessmentDto.AssessmentProjectDetail.MapImageFile);
                        _unitOfWork.Context.RSS_File.Add(file);
                    }
                }
                else
                    assessmentDto.AssessmentProjectDetail.MapImageFileId = null;

                // Insert our site map data if needed.
                if (assessmentDto.AssessmentProjectDetail.SiteMapImageFile != null)
                {
                    // Insert our SiteMapImageFile into the DB if it doesn't already exist
                    // Note: I'm not sure if this is a hack and EF should handle this case automatically or not.
                    var existing = _unitOfWork.Context.RSS_File.Where(s => s.FileId == assessmentDto.AssessmentProjectDetail.SiteMapImageFile.FileId).FirstOrDefault();
                    if (existing == null)
                    {
                        RSS_File file = _mapper.Map<RSS_File>(assessmentDto.AssessmentProjectDetail.SiteMapImageFile);
                        _unitOfWork.Context.RSS_File.Add(file);
                    }
                }
                else
                {
                    assessmentDto.AssessmentProjectDetail.SiteMapImageFileId = null;
                }

            }

            // Removing ?s from HeatmapRadiationChartSVG and its JSON - Was suprising difficult to track down.
            // This only occurs for this chart as it is the only chart that has a <br> in its label html
            // When exporting to an SVG Highcharts converts <br>s to a <tspan> as <br>s do not work in svg
            // <tspans> do not render if they are empty so Highcharts puts in an invisible no gap character \u200B inside it
            // AbcPDF does not recognise this \u200B character and displays a ? instead
            // So the solution to display a newline character without indenting the following line in this case is to
            // remove the \u200B character and wrap the below child: Wh/m^2 in a <tspan>
            // (^2 -> \u00b2)
            // (invisible no gap character -> \u200B)
            var existingHeatmapRadiationChart = assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapRadiationChartSVG;
            assessmentDto.AssessmentProjectDetail.ClimateChartData.HeatmapRadiationChartSVG = existingHeatmapRadiationChart?.Replace(">\u200B</tspan>Wh/m\u00b2", ">Wh/m\u00b2</tspan>");
            var existingJson = assessmentDto.AssessmentProjectDetail.ClimateChartDataJson;
            assessmentDto.AssessmentProjectDetail.ClimateChartDataJson = existingJson?.Replace(">\u200B</tspan>Wh/m\u00b2", ">Wh/m\u00b2</tspan>");

            // We also need to remove the titles from the charts as in the PDF it is impossible to match them
            // exactly due to the two different import methods that need to be used...
            // SVG Charts


            // SVG JSON
            assessmentDto.AssessmentProjectDetail.ClimateChartDataJson = assessmentDto.AssessmentProjectDetail.ClimateChartDataJson?
                                                                            .Replace("Global Horizontal Radiation (by hour)", "")
                                                                            .Replace("Air Temperature (by hour)", "")
                                                                            .Replace("Air Temperature", "")
                                                                            .Replace("Precipitable Water", "")
                                                                            .Replace("Daily Horizontal Radiation", "")
                                                                            .Replace("Wind", "");

            //Update the database model
            _mapper.Map((AssessmentDto)assessmentDto, _assessmentDbModel);

            // Trigger PDF background process
            if (oldStatus != assessmentDto.StatusCode)
            {
                TriggerGenerateReports(assessmentDto.AssessmentId, assessmentDto.StatusCode);
            }

            if (_assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null && _assessmentDbModel.RSS_Job.RSS_Client != null) {
                LogEvent(string.Format("Assessment updated", _assessmentDbModel.RSS_Job.RSS_Client.ClientName), (Guid)_assessmentDbModel.JobId);
                if (!String.IsNullOrEmpty(oldStatus)) {
                    LogEvent(string.Format("Assessment status updated to {0}, was {1}", _assessmentDbModel.StatusCode, oldStatus), (Guid)_assessmentDbModel.JobId);
                }
            }
        }

        public void TriggerGenerateReports(Guid assessmentId, string statusCode)
        {
            if (statusCode == AssessmentStatus.APreliminaryReview.ToString("G") || statusCode == AssessmentStatus.AComplete.ToString("G"))
            {
                Queue.AddNewQueueRecord(Queue.QueueCode.GeneratePreviewPDF, assessmentId.ToString());
            }
            if (statusCode == AssessmentStatus.AComplete.ToString("G") || statusCode == AssessmentStatus.AIssued.ToString("G"))
            {
                Queue.AddNewQueueRecord(Queue.QueueCode.GenerateReportPDF, assessmentId.ToString());
            }
        }

        /// <summary>
        /// Create Queue record for Google Sheets if the Address has changed
        /// </summary>
        /// <param name="assessmentProjectDetailDto"></param>
        private void RecordChangeOfAddressIfNeeded(AssessmentProjectDetailDto assessmentProjectDetailDto)
        {
            var assessmentProjectDetailDb = _mapper.Map<AssessmentProjectDetailDto>(_assessmentDbModel.RSS_AssessmentProjectDetail);
            var streetAddressFromDb = new StreetAddress(assessmentProjectDetailDb);
            var fullAddressFromDb = streetAddressFromDb.FullAddress();
            var streetAddressFromDto = new StreetAddress(assessmentProjectDetailDto);
            var fullAddressFromDto = streetAddressFromDto.FullAddress();
            if (!fullAddressFromDb.Equals(fullAddressFromDto) && _assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null)
            {
                Queue.AddNewQueueRecord(Queue.QueueCode.UpdateGoogleSheets, _assessmentDbModel.JobId.ToString(), _unitOfWork.Context);
            }
        }

        /// <summary>
        /// Delete the current assessment. By default we also delete the entire job.
        /// If you only intend to delete the current version of an assessment, set
        /// deleteEntireJob to false.
        /// </summary>
        /// <returns>
        /// If deleteEntireJob == true, returns the current assessmentId.
        /// If deleteEntireJob == false, returns the next most current assessmentId.
        /// </returns>
		public Guid? Delete(bool deleteEntireJob = true)
        {
			if (_assessmentDbModel == null) { throw new Exception(string.Format("_assessmentDbModel not set")); }

            Guid? currentId = _assessmentDbModel.AssessmentId;

            // If we aren't deleting the entire job, we need to link to the most recent
            // assessment version. If no other assessment versions exist, then we delete the
            // entire job anyway.
            if (!deleteEntireJob)
            {
                var versions = _unitOfWork.Context.RSS_Assessment.Where(
                        s => s.JobId == _assessmentDbModel.JobId &&
                        !s.Deleted && s.AssessmentId
                        != _assessmentDbModel.AssessmentId) // We don't wish to include our current assessment model.
                    .ToList();

                if (versions.Count() == 0)
                    deleteEntireJob = true;
                else
                {
                    // More versions exist, so link the latest one to our Job.
                    // Sort by version number (Relies on client having a coherent order)
                    versions.Sort((f, l) => l.RSS_AssessmentProjectDetail.AssessmentVersion.CompareTo(f.RSS_AssessmentProjectDetail.AssessmentVersion));
                    _assessmentDbModel.RSS_Job.CurrentAssessmentId = versions[0].AssessmentId;
                    _assessmentDbModel.RSS_Job.StatusCode = JobStatus.JIssued.ToString("G");
                    _assessmentDbModel.RSS_Job.ModifiedOn = DateTime.Now;
                    _assessmentDbModel.RSS_Job.ModifiedByName = UtilityFunctions.CurrentUserFullName;

                    currentId = versions[0].AssessmentId;

                    // We know that we can only run the recertification proces AFTER a job has had its status set
                    // to 'AIssued' so we can just set it back to that here.
                    versions[0].StatusCode = "AIssued";
                }
            }

            // Update the database model
            _assessmentDbModel.ModifiedOn = DateTime.Now;
            _assessmentDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            _assessmentDbModel.Deleted = true;

            if(_assessmentDbModel.RSS_AssessmentProjectDetail != null)
            {
                _assessmentDbModel.RSS_AssessmentProjectDetail.Deleted = true;
                _assessmentDbModel.RSS_AssessmentProjectDetail.ModifiedOn = _assessmentDbModel.ModifiedOn;
                _assessmentDbModel.RSS_AssessmentProjectDetail.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            }


            if (_assessmentDbModel.RSS_Job != null) {

                if(deleteEntireJob)
                    _assessmentDbModel.RSS_Job.Deleted = true;

                _assessmentDbModel.RSS_Job.ModifiedOn = _assessmentDbModel.ModifiedOn;
                _assessmentDbModel.RSS_Job.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            }

            string logString = deleteEntireJob
                ? "Assessment deleted."
                : $"Assessment version {_assessmentDbModel.RSS_AssessmentProjectDetail.AssessmentVersion} deleted";

            if (_assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null)
                LogEvent(logString, (Guid)_assessmentDbModel.JobId);

            return currentId;
        }

		public void UndoDelete()
        {
			if (_assessmentDbModel == null) {throw new Exception(string.Format("_assessmentDbModel not set"));}

			// Update the database model
            _assessmentDbModel.ModifiedOn = DateTime.Now;
            _assessmentDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            _assessmentDbModel.Deleted = false;
            if (_assessmentDbModel.RSS_Job != null) {
                _assessmentDbModel.RSS_Job.Deleted = false;
                _assessmentDbModel.RSS_Job.ModifiedOn = _assessmentDbModel.ModifiedOn;
                _assessmentDbModel.RSS_Job.ModifiedByName = UtilityFunctions.CurrentUserFullName;
            }

            if (_assessmentDbModel.JobId != Guid.Empty && _assessmentDbModel.JobId != null) { LogEvent(string.Format("Assessment restored (undeleted)"), (Guid)_assessmentDbModel.JobId); }
        }

        public void SetVersion(decimal? assessmentVersion)
        {
             _assessmentDbModel.RSS_AssessmentProjectDetail.AssessmentVersion = assessmentVersion.HasValue ? assessmentVersion.Value : 1;
        }

        public async Task<string[]> PrepareAndGenerateReportsThenUploadToCloudStorage(bool isIssued)
        {
            if(_assessmentDbModel == null)
                throw new Exception("_assessmentDbModel not set");

            GetNewCertificateNumber(_assessmentDbModel);
            GetNewLightAndVentilationCertificateNumber(_assessmentDbModel);

            var client = _mapper.Map<ClientDto>(_assessmentDbModel.RSS_Job.RSS_Client);

            try
            {
                var pdfReportUrls = await _assessmentPDFFactory()
                    .Initialize(_assessmentId)
                    .GenerateFinalPdfReportsAndUploadToCloudStorage(client);

                if(pdfReportUrls.Length == 0)
                    LogEvent("Failed to generate an Assessment PDF Report, but no exception was thrown?", _assessmentDbModel.JobId.Value);

                LogEvent($"Compliance Reports generated for Assessment with ID '{_assessmentDbModel.AssessmentId}'", _assessmentDbModel.JobId.Value);

                // TESTING: Open the first PDF (Energy Efficiency Compliance Report) automatically
                //if(pdfReportUrls.Length > 0)
                //{
                //    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(pdfReportUrls[0]) { UseShellExecute = true });
                //}

                return pdfReportUrls;
            }
            catch(Exception ex)
            {
                ErrorWriter.WriteError(ex);
                ErrorWriter.WriteInfo("There was an error issuing the assessment.");
                throw new Exception($"Exception encountered while trying to issue a new Assessment PDF Report. | AssessmentId: {_assessmentDbModel.AssessmentId} | Message: {ex.Message} | StackTrace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Get semi-random certificate number that has not already been used.
        /// </summary>
        /// <param name="assessmentDbModel"></param>
        private void GetNewCertificateNumber(RSS_Assessment assessmentDbModel)
        {
            if (string.IsNullOrEmpty(assessmentDbModel.CertificateNumber) || assessmentDbModel.CerficateDate == null)
            {
                DateTime certificateDate = assessmentDbModel.CertificateDateOverride ?? assessmentDbModel.CerficateDate ?? DateTime.Now;
                string certificateNumber;

                bool isGettingNumber = true;
                while (isGettingNumber)
                {
                    certificateNumber = certificateDate.ToString("ddMMyy") + _rand.Next(0, 9999).ToString().PadLeft(4, '0');

                    RSS_Assessment result = _unitOfWork.Context.RSS_Assessment
                        .FirstOrDefault(s => s.CertificateNumber == certificateNumber);

                    if (result == null)
                        isGettingNumber = false;

                    assessmentDbModel.CertificateNumber = certificateNumber;
                }
            }
        }

        private void GetNewLightAndVentilationCertificateNumber(RSS_Assessment assessmentDbModel)
        {
            if (string.IsNullOrEmpty(assessmentDbModel.LightAndVentilationCertificateNumber))
            {
                DateTime certificateDate = assessmentDbModel.CertificateDateOverride ?? assessmentDbModel.CerficateDate ?? DateTime.Now;
                string certificateNumber;
                bool isGettingNumber = true;
                while (isGettingNumber)
                {
                    certificateNumber = certificateDate.ToString("ddMMyy") + _rand.Next(0, 9999).ToString().PadLeft(4, '0');

                    RSS_Assessment result = _unitOfWork.Context.RSS_Assessment
                        .FirstOrDefault(s => s.LightAndVentilationCertificateNumber == certificateNumber);

                    if (result == null)
                        isGettingNumber = false;

                    assessmentDbModel.LightAndVentilationCertificateNumber = certificateNumber;
                }
            }
        }

        public string ArchiveFile(Guid assessmentDrawingId, Guid jobId, Guid assessmentId)
        {
            if (_assessmentDbModel == null) { throw new Exception(string.Format("_assessmentDbModel not set")); }

            AssessmentDrawing assessmentDrawing = _assessmentDrawingFactory().Get(assessmentDrawingId);
            Guid fileId = assessmentDrawing.Dto.Attachment.FileId;
            int pageNumber = assessmentDrawing.Dto.PageNumber ?? 1;

            string wkFileUrl = string.Empty;
            try
            {
                ArchivePDF archivePdf = _archivePDFFactory().Get(fileId, pageNumber);
                wkFileUrl = archivePdf.Archive(assessmentDrawing.Dto.AssessmentId, jobId);
            }
            catch (Exception ex)
            {
                ErrorWriter.WriteError(ex);
                ErrorWriter.WriteInfo("There was an error issuing the assessment.");
                return string.Empty;
            }

            if (wkFileUrl == null | wkFileUrl == "")
            {
                LogEvent(string.Format("Assessment failed to generate."), (Guid)_assessmentDbModel.JobId);
            }

            return wkFileUrl;
        }

        /// <summary>
        /// Given the filepath of an uploaded file, move the file to amazon and create and return a filedto
        /// </summary>
        public FileDto UploadFileToCloudAndAddToAssessment(
            string originalFileName,
            string folder,
            string category,
            string classification,
            Guid assessmentId,
            Guid jobId,
            string wantedFileName = null)
        {
            GeneratedDocStorageUtil docUtil = new GeneratedDocStorageUtil();
            string url = docUtil.StoreDocumentFile(originalFileName, folder, category, classification, jobId, assessmentId, null, wantedFileName);

            RSS_File file = _unitOfWork.Context.RSS_File
                .FirstOrDefault(s => s.URL == url);

            if (file == null)
                file = _unitOfWork.Context.RSS_File
                    .FirstOrDefault(s => string.IsNullOrEmpty(s.URL) && s.OriginalFileUrl == url);

            return _mapper.Map<FileDto>(file);
        }


        private string FindNewFileName(List<string> usedFileNames, List<string> existingFileNames, string originalFileName)
        {
            string originalExt = System.IO.Path.GetExtension(originalFileName);
            string originalName = System.IO.Path.GetFileNameWithoutExtension(originalFileName);

            bool found = false;
            int i = 1;
            do
            {
                string possibleNewName = originalName + "_" + i + originalExt;
                if (usedFileNames.Contains(possibleNewName) == false && existingFileNames.Contains(possibleNewName) == false)
                {
                    return possibleNewName;
                }
                i++;
            } while (!found);

            return "";
        }

        public List<FileDto> ImportDrawingsFromPriorAssessment(List<string> externalUrls, List<string> existingFileNames, Guid assessmentId, Guid jobId)
        {
            if (_assessmentDbModel == null) { throw new Exception("_assessmentDbModel not set"); }

            List<FileDto> importedFiles = new List<FileDto>();
            List<string> localFileNames = new List<string>();
            List<string> usedFileNames = new List<string>();
            string tempName = DateTime.UtcNow.ToString("yyyyMMddTHHmmssZ");
            string folderName = System.Web.Hosting.HostingEnvironment.MapPath("~/Uploads/");

            foreach (var externalUrl in externalUrls)
            {
                Uri uri = new Uri(externalUrl);
                string fileName = System.IO.Path.GetFileName(uri.LocalPath);
                if(usedFileNames.Contains(fileName) || existingFileNames.Contains(fileName))
                {
                    fileName = FindNewFileName(usedFileNames, existingFileNames, fileName);
                }
                string tempFileName = tempName + fileName;

                using (System.Net.WebClient webClient = new System.Net.WebClient())
                {
                    webClient.DownloadFile(uri, folderName + tempFileName);
                    localFileNames.Add(tempFileName);
                }
                usedFileNames.Add(fileName);
            }

            for(int ii = 0; ii < localFileNames.Count; ii++)
            {
                string localFileName = localFileNames[ii];
                string usedFileName = usedFileNames[ii];
                var underlyingRssFile = UploadFileToCloudAndAddToAssessment(localFileName, folderName, "Assessment Drawing",  "Generic", assessmentId, jobId, usedFileName);
                importedFiles.Add(underlyingRssFile);
            }

            return importedFiles;
        }

        public void LogEvent(string description, Guid jobId, string eventTypeCode = null)
        {
            RSS_JobEvent jobEvent = new RSS_JobEvent();
            jobEvent.JobEventId = SequentialGuid.NewSequentialGuid();
            jobEvent.JobId = jobId;
            jobEvent.EventTypeCode = eventTypeCode;
            jobEvent.Description = description;

            jobEvent.CreatedOn = DateTime.Now;
            jobEvent.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            _unitOfWork.Context.RSS_JobEvent.Add(jobEvent);
        }

        /// <summary>
        /// Creates a new Assessment Version for the given job. All details within the passed
        /// CopyJobDto will override details found within the DB.
        /// </summary>
        public Guid Recertify(
            ExtendedJobDto job,
            ExtendedAssessmentDto assessment,
            Guid? assessorId)
        {
            string oldStatus = assessment.StatusCode;

            // NOTE: AssessmentId will already have been re-assigned from the frontend
            // (this is required to work with the 'upload drawings' field)
            assessment = Assessment.ReassignGuids(assessment, job.JobId);

            assessment.StatusCode = AssessmentStatus.ARecertification.ToString("G");
            assessment.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == assessment.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();

            decimal prevVersion = assessment.AssessmentProjectDetail.AssessmentVersion;

            assessment.AssessmentProjectDetail.AssessmentId = assessment.AssessmentId;
            assessment.AssessmentProjectDetail.AssessmentVersion++;
            assessment.AssessmentProjectDetail.IsRecertified = true;
            assessment.AssessmentProjectDetail.OrderDate = DateTime.Now;
            assessment.AssessmentProjectDetail.CreatedOn = DateTime.Now;
            assessment.AssessmentProjectDetail.CreatedByName = assessment.CreatedByName = job.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            assessment.AssessorUserId = assessorId;
            assessment.AssessorUser = null;

            assessment.CertificateNumber = null;
            assessment.CerficateDate = null;
            assessment.LightAndVentilationCertificateNumber = null;

            // Do the actual insertion now that our data is up to date.
            Assessment.InsertIntoDbInOrder(assessment, _mapper, _unitOfWork);

            // Now that our assessment exists in the DB we can copy and assign our drawings to it (if needed). If new
            // drawings have been passed in via the 'files in order' option, these apply only to the baseline assessment
            // and are not being 'copied' from previous assessments, and thus must run through the usual splitting /
            // conversion process.
            if (job.FilesInOrder == null || job.FilesInOrder.Count == 0)
            {
                foreach (var opt in assessment.AllComplianceOptions)
                {
                    AssessmentDrawing.CopyAndInsert(
                        opt.AssessmentDrawings,
                        assessment.AssessmentId,
                        opt.ComplianceOptionsId,
                        job.JobId,
                        _mapper,
                        _unitOfWork);
                }
            }
            else
            {
                // Start processing.
                AssessmentDrawing.StartPdfSplittingProcess(_unitOfWork, assessment.BaselineSimulation, job.FilesInOrder, job.Client.ClientDefault.IncludeDrawingsInReport, job.Client.ClientDefault.StampDrawings);
            }


            // Finally, update the job to point to our new assessment as the current.
            job.CurrentAssessment = assessment;
            job.CurrentAssessmentId = assessment.AssessmentId;
            job.StatusCode = JobStatus.JRecertification.ToString("G");
            job.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == job.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();
            job.ModifiedOn = DateTime.Now;
            job.ModifiedByName = UtilityFunctions.CurrentUserFullName;

            // Account for status change
            if (assessment.StatusCode != AssessmentStatus.AIssued.ToString())
            {
                assessment.AssessmentProjectDetail.ComplianceCost = null;
            }

            var rssJob = _unitOfWork.Context.RSS_Job.Find(job.JobId);
            _mapper.Map(job, rssJob);
            _unitOfWork.Context.SaveChanges();

            // Log event.
            if (assessment.JobId != Guid.Empty && assessment.JobId != null)
                LogEvent(
                    string.Format("Assessment v{0} recertified from v{1}",
                        string.Format("{0:N2}", assessment.AssessmentProjectDetail.AssessmentVersion),
                        string.Format("{0:N2}", prevVersion)),
                    job.JobId);

            return assessment.AssessmentId;
        }

        /// <summary>
        /// Copies a template assessment into a new template assessmemt.
        /// For the love of god don't add arbitrary logic into this function just because you need it, do your logic OUTSIDE of this function.
        /// Nothing to do with jobs. Nothing to do with assessment project details. Nothing to do with the semantics between system templates and client templates.
        /// This function is given a template id, copies it into a new template, then returns that new templates' id.
        /// That's it. Nothing else. Seriously.
        /// </summary>
        /// <param name="templateId">The id of the template assessment to copy.</param>
        /// <returns></returns>
        // TODO: Check everywhere this is used and seee if those functions still work...
        public Guid CopyTemplate(Guid templateId)
        {
            RSS_Assessment dbTemplate = _unitOfWork.Context.RSS_Assessment.Find(templateId);
            if (dbTemplate == null)
            {
                throw new Exception("Error copying assessment for assessment with id = " + templateId);
            }

            ExtendedAssessmentDto assessment = BuildExtendedDto(dbTemplate);

            assessment.AssessmentId = SequentialGuid.NewSequentialGuid();

            // Delinkcompliance opts and so on, THEN SaveChanges() on the context before relinking (EF Circular refs)
            var options = assessment.AllComplianceOptions;
            assessment.AllComplianceOptions = null;

            var rssAssessment = _mapper.Map<RSS_Assessment>(assessment);
            _unitOfWork.Context.RSS_Assessment.Add(rssAssessment);
            _unitOfWork.Context.SaveChanges();

            foreach (AssessmentComplianceOptionDto opt in options)
            {
                // If we're doing a copy, assign new ID's so EF does not overwrite existing.
                opt.Proposed.AssessmentComplianceBuildingId = Guid.NewGuid();
                opt.ProposedBuildingId = opt.Proposed.AssessmentComplianceBuildingId;

                opt.Reference.AssessmentComplianceBuildingId = Guid.NewGuid();
                opt.ReferenceBuildingId = opt.Reference.AssessmentComplianceBuildingId;

                opt.ComplianceOptionsId = Guid.NewGuid();
                opt.AssessmentId = assessment.AssessmentId;

                opt.Proposed.AssessmentComplianceOptionId = opt.ComplianceOptionsId;
                opt.Reference.AssessmentComplianceOptionId = opt.ComplianceOptionsId;


                // Create new Zones for all Proposed & Reference buildings.
                opt.Proposed.Zones = Zone.ReassignGuids(opt.Proposed.Zones, opt.Proposed.AssessmentComplianceBuildingId);
                opt.Reference.Zones = Zone.ReassignGuids(opt.Reference.Zones, opt.Reference.AssessmentComplianceBuildingId);

                AssessmentComplianceOption.InsertIntoDatabase(opt, _mapper, _unitOfWork);
            }

            assessment.AllComplianceOptions = options;
            _unitOfWork.Context.SaveChanges(); // Now save again so compliance options are attached.


            return assessment.AssessmentId;
        }

        public List<AssessmentComplianceOptionDto> GetComplianceOptions(Guid assessmentId)
        {
            return _unitOfWork.Context.RSS_AssessmentComplianceOption.Where(s => s.AssessmentId == assessmentId && s.Deleted == false)
                               .OrderBy(s => s.OptionIndex)
                               .ProjectTo<AssessmentComplianceOptionDto>(_mapper.ConfigurationProvider).ToList();
        }

        /// <summary>
        /// Re-assigns Guid's on the given AssessmentDto in preparation for
        /// re-insertion into the database under new rows (i.e., a copy).
        ///
        /// Does NOT insert any new rows. This just gets things ready.
        /// </summary>
        /// <returns>The modified ExtendedAssessmentDto</returns>
        public static ExtendedAssessmentDto ReassignGuids(
                ExtendedAssessmentDto assessmentDto,
                Guid destinationJobId)
        {
            string oldStatus = assessmentDto.StatusCode;

            // Assign high-level job values.
            // Includes clearing any objects which EF will fill-in based on their GUID.

            // Note: To preserve the ability to use the new file drawing file uploads field, we cannot
            // re-assign the GUID here. The frontend will (must!) have already done this for us.
            //assessmentDto.AssessmentId = Guid.NewGuid();
            assessmentDto.CreatedOn = DateTime.Now;
            assessmentDto.ModifiedOn = null;
            assessmentDto.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            assessmentDto.StatusCode = AssessmentStatus.AInProgress.ToString("G");
            assessmentDto.JobId = destinationJobId;
            assessmentDto.Job = null;

            assessmentDto.AssessmentProjectDetail.AssessmentId = assessmentDto.AssessmentId;
            assessmentDto.AssessmentProjectDetail.CreatedOn = DateTime.Now;
            assessmentDto.AssessmentProjectDetail.CreatedByName = assessmentDto.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            // Must do this outside.
            assessmentDto.AssessorUser = null;

            assessmentDto.CertificateNumber = null;
            assessmentDto.CerficateDate = null;
            assessmentDto.LightAndVentilationCertificateNumber = null;

            // Change all GUID's for all child items
            // and discard anything not required before calling the Create()
            // method

            // NOTE: No need to save map file! It's fine for us to reference the old one at this stage.

            // We need to keep reference to this as it will have been modified from the UI
            // with info we need regardless of if we are technically NOT copying the baseline.
            var originalBaseline = assessmentDto.BaselineSimulation;

            // Re-assign all GUID's for ALL compliance options sent through from front end
            var newComplianceOptions = new List<AssessmentComplianceOptionDto>();
            foreach (var opt in assessmentDto.AllComplianceOptions)
            {
                var n = AssessmentComplianceOption.ReassignGuids(opt, assessmentDto.AssessmentId);
                newComplianceOptions.Add(n);
            }
            assessmentDto.AllComplianceOptions = newComplianceOptions;

            // Account for status change
            if (assessmentDto.StatusCode != AssessmentStatus.AIssued.ToString())
            {
                assessmentDto.AssessmentProjectDetail.ComplianceCost = null;
            }

            return assessmentDto;
        }

        /// <summary>
        /// Inserts an (already constructed) AssessmentDto into the database, ensuring files are saved in order so as
        /// to not cause Entity Framework conflicts.
        ///
        /// NOT MODIFICATIONS TO THE PASSED IN ASSESSMENT SHOULD HAPPEN WITHIN THIS FUNCTION OTHER THAN AS NEEDED
        /// TO SAVE IN ORDER.
        /// </summary>
        public static ExtendedAssessmentDto InsertIntoDbInOrder(ExtendedAssessmentDto assessment, IMapper mapper, IUnitOfWork unitOfWork)
        {
            // Unlink compliance options
            var options = assessment.AllComplianceOptions;
            assessment.AllComplianceOptions = null;

            // Insert our map data if needed.
            if (assessment.AssessmentProjectDetail.MapImageFile != null)
            {
                // Insert our MapImageFile into the DB if it doesn't already exist
                // Note: I'm not sure if this is a hack and EF should handle this case automatically or not.
                var existing = unitOfWork.Context.RSS_File.Where(s => s.FileId == assessment.AssessmentProjectDetail.MapImageFile.FileId).FirstOrDefault();
                if (existing == null)
                {
                    RSS_File file = mapper.Map<RSS_File>(assessment.AssessmentProjectDetail.MapImageFile);
                    unitOfWork.Context.RSS_File.Add(file);
                }
            }
            else
                assessment.AssessmentProjectDetail.MapImageFileId = null;

            // Insert our site map data if needed.
            if (assessment.AssessmentProjectDetail.SiteMapImageFile != null)
            {
                // Insert our SiteMapImageFile into the DB if it doesn't already exist
                // Note: I'm not sure if this is a hack and EF should handle this case automatically or not.
                var existing = unitOfWork.Context.RSS_File.Where(s => s.FileId == assessment.AssessmentProjectDetail.SiteMapImageFile.FileId).FirstOrDefault();
                if (existing == null)
                {
                    RSS_File file = mapper.Map<RSS_File>(assessment.AssessmentProjectDetail.SiteMapImageFile);
                    unitOfWork.Context.RSS_File.Add(file);
                }
            }
            else
                assessment.AssessmentProjectDetail.SiteMapImageFileId = null;


            RSS_Assessment rssAssessment = mapper.Map<RSS_Assessment>(assessment);
            unitOfWork.Context.RSS_Assessment.Add(rssAssessment);
            unitOfWork.Context.SaveChanges(); // Now that Job and Assessment are linked we can save again.

            // SAVE COMPLIANCE OPTIONS AND BUILDINGS.
            // For every compliance option (inc baseline) we want to map it to a database row,
            // remove the reference to the compliance building (since it doesn't exist in the DB
            // yet and would throw an error due to constraints) and then save the row.
            // THEN we save the actual compliance buildings and THEN re-link everything.

            foreach (var opt in options) // If this has already been nullified, nothing will happen
            {
                // If this is a copy option (rather than create) we need to assign
                // new ID's to everything so they get saved into the DB as new rows.
                opt.AssessmentId = assessment.AssessmentId;

                AssessmentComplianceOption.InsertIntoDatabase(opt, mapper, unitOfWork);
            }

            // RE-LINK REMAINDER AND SAVE.
            assessment.AllComplianceOptions = options;

            unitOfWork.Context.SaveChanges();
            return assessment;
        }

        public bool CheckJobNumberIsAvailable(string jobNumber, string customClientName, Guid clientId, Guid? jobId = null)
        {
            var s = from job in _unitOfWork.Context.RSS_Job
                join assessment in _unitOfWork.Context.RSS_Assessment
                    on job.JobId equals assessment.JobId
                join detail in _unitOfWork.Context.RSS_AssessmentProjectDetail
                    on assessment.AssessmentId equals detail.AssessmentId
                where job.JobId != jobId
                   && job.ClientId == clientId
                   && assessment.Deleted == false
                   && detail.ClientJobNumber == jobNumber && (customClientName == null || detail.CustomClientName == customClientName)
                select job; // Throwaway since apparently we need to select something

            var exists = s.Any();

            return !exists;
        }

        #region Helpers

        // Checks to see if the given building contains constructions of the specific category..
        private bool CheckBuildingContainsConstructionOfCategory(ConstructionCategoryDto category, AssessmentComplianceBuildingDto building)
        {

            var found = GetConstructionsForSpecificCategory(category, building);
            return found != null && found.Count > 0;
        }

        // Checks to see if the given building contains constructions of the specific category..
        private bool CheckBuildingContainsServicesOfCategory(ServiceCategoryDto category, AssessmentComplianceBuildingDto building)
        {

            bool found = building.Services.Any(
                x => x.ServiceCategory.ServiceCategoryCode == category.ServiceCategoryCode);
            return found;
        }


        /// <summary>
        /// Returns all constructions of the correct type from the given building.
        /// </summary>
        private List<ConstructionTemplateDto> GetAllConstructionsForCategoryType(ConstructionCategoryDto category, AssessmentComplianceBuildingDto building)
        {
            return category.Type == "surface" || category.Type == "permanentopening"
                ? building.Surfaces.Select(x => (ConstructionTemplateDto)x).ToList()
                : building.Openings.Select(x => (ConstructionTemplateDto)x).ToList();
        }

        /// <summary>
        /// Returns only constructions matching the EXACT category supplied.
        /// </summary>
        private List<ConstructionTemplateDto> GetConstructionsForSpecificCategory(ConstructionCategoryDto category, AssessmentComplianceBuildingDto building)
        {
            var list = GetAllConstructionsForCategoryType(category, building);

            var filtered = list
                .Where(x => x.Category?.ConstructionCategoryCode == category.ConstructionCategoryCode);

            return filtered.ToList();
        }

        #endregion
    }
}