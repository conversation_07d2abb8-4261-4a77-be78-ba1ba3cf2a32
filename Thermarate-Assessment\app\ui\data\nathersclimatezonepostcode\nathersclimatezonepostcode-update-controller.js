(function () {
    // The NathersclimatezoneUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'NathersclimatezonePostcodeUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', 'nathersclimatezoneservice',
            'nathersclimatezonepostcodeservice', 'security', nathersclimatezoneUpdateController]);
    function nathersclimatezoneUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, nathersclimatezoneservice,
            nathersclimatezonepostcodeservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit NatHERS Climate Zone Postcode';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.postcode = null;
        vm.nathersclimatezone = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New NatHERS Climate Zone Postcode";
            // Set any default values required for a new record.
        }

        vm.natHERSClimateZoneList = [];
        nathersclimatezoneservice.getList()
            .then(function (data) {
                vm.natHERSClimateZoneList = data.data;
            });

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.postcode = $scope.postcode;
            }
            vm.hideActionBar = true;
        } else {
            vm.postcode = $stateParams.postcode;
        }

        // Get data for object to display on page
        var natHERSClimateZoneCodePromise = null;
        if (vm.postcode != null) {
            natHERSClimateZoneCodePromise = nathersclimatezonepostcodeservice.getNatHERSClimateZonePostcode(vm.postcode)
            .then(function (data) {
                if (data != null) {
                    vm.nathersclimatezone = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("nathersclimatezone-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                nathersclimatezonepostcodeservice.createNatHERSClimateZonePostcode(vm.nathersclimatezone).then(function(data){
                    vm.nathersclimatezone = data;
                    vm.postcode = vm.nathersclimatezone.postcode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                nathersclimatezonepostcodeservice.updateNatHERSClimateZonePostcode(vm.nathersclimatezone).then(function(data){
                    if (data != null) {
                        vm.nathersclimatezone = data;
                        vm.postcode = vm.nathersclimatezone.postcode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            nathersclimatezonepostcodeservice.deleteNatHERSClimateZonePostcode(vm.postcode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            nathersclimatezonepostcodeservice.undoDeleteNatHERSClimateZonePostcode(vm.postcode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();