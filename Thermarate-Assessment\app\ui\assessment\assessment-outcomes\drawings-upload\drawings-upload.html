<style>
    .drag-over-class {
        border: 1px solid RGB(134, 195, 74) !important;
    }
</style>

<div ng-form="drawingsUploadForm">

    <div ngf-drop="vm.uploadFile($files, vm.target, 'attachment')"
         ngf-multiple="true"
         ngf-drag-over-class="'drag-over-class'"
         style="padding: 0px;">

        <!-- PROMPT WHEN SELECTED DRAWINGS LENGTH == 0 -->
        <div ng-if="vm.option.assessmentDrawings.length == 0"
             layout="row"
             layout-align="center center">
            <div style="color: grey; font-size: 10px; margin-left: 0px;"> Drag &amp; Drop Files or Click the Upload Button</div>

            <!-- UPLOAD BUTTON INLINE WITH PLACEHOLDER -->
            <div style="text-align: center; align-self: center; width: 50px; margin-left: auto; margin-right: 0px;"
                 data-title="Attachment" class="text-left"
                 ngf-multiple="true">
                <md-button class="md-raised md-icon-button small-icon-button"
                           ngf-select="vm.uploadFile($files, vm.target, 'attachment')"
                           ngf-multiple="true"
                           ng-show="!vm.isLocked"
                           ng-disabled="(vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded')">
                    <md-tooltip md-direction="top">Upload drawings.</md-tooltip>
                    <i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>
                </md-button>
            </div>
        </div>

        <!-- LIST OF SELECTED DRAWINGS (WHEN LENGTH > 0) -->
        <div ng-if="vm.option.assessmentDrawings.length > 0">

            <div layout="row"
                 flex="100"
                 layout-wrap
                 ng-repeat="item in vm.option.assessmentDrawings track by $index"
                 layout-align="center start"
                 class="small-compressed"
                 lr-drag-data="vm.option.assessmentDrawings"
                 lr-match-property="id"
                 lr-index="vm.option.assessmentDrawings.indexOf(item)"
                 lr-match-value="{{item.id}}"
                 lr-drop-success="vm.dropSuccess()"
                 lr-drag-src="newFile"
                 lr-drop-target="newFile">

                <!--Download Button (Doubles as display name)-->
                <md-button flex="90"
                           class="md-primary md-small text-left"
                           ng-click="vm.downloadFile(row.referenceFile)">
                    {{item.attachment.fileName}}
                    <md-tooltip md-direction="top">
                        {{vm.fileObject[vm.propName].fileName}}
                    </md-tooltip>
                </md-button>

                <!-- Remove Button -->
                <md-button flex="10"
                           class="md-small small-icon-button"
                           ng-click="vm.deleteFile(item)">
                    <i class="material-icons" style="text-align: center; vertical-align: middle;  margin-right: 14px;">clear</i>
                </md-button>

                <!--Circular Progress Indicator-->
                <md-progress-circular ng-if="item.attachmentUploadProgress != null"
                                      style="display:inline-block;"
                                      md-diameter="20"
                                      md-mode="{{vm.progressMode}}"
                                      value="{{item.attachmentUploadProgress}}"></md-progress-circular>

            </div>

            <!-- UPLOAD BUTTON BELOW TABLE -->
            <div style="text-align: center; align-self: center; width: 50px; margin-left: auto; margin-right: 0px;"
                 data-title="Attachment" class="text-left"
                 ngf-multiple="true">
                <md-button class="md-raised md-icon-button small-icon-button"
                           ngf-select="vm.uploadFile($files, vm.target, 'attachment')"
                           ngf-multiple="true"
                           ng-show="!vm.isLocked"
                           ng-disabled="(vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded')">
                    <md-tooltip md-direction="top">Upload drawings.</md-tooltip>
                    <i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>
                </md-button>
            </div>

        </div>

    </div>
</div>