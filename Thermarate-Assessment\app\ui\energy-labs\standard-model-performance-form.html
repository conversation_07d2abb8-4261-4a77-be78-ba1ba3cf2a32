<!-- Standard Model OPTION Performance / Results -->
<div>

  <div class="el-section-title" style="padding: 2rem 2rem 0 2rem;">
    <img class="el-section-icon"
         src="content/images/energy-labs/el-performance-icon.svg"
         alt="Icon of a home design which is stamped or has awards">
    Performance
  </div>

  <div ng-if="vm.source == null"
       class="el-category" style="text-align: center; color: orangered; margin: 2rem;">
    No matches found for given configuration.
  </div>

  <div ng-if="vm.source != null"
       style="padding: 0 2rem 2rem 2rem; display: flex; justify-content: space-evenly; justify-items: center;">

    <!-- Heating -->
    <div class="el-performance-result">
      <label>Heating (MJ/m<sup>2</sup>)</label>
      <span>

        <span ng-style="{ color: vm.determineHeatCoolResultColour(vm.heatingCoolingLoadLimits, vm.source, 'heatingLoad', 'heatingLoadLimit') }">
          {{vm.source.heatingLoad.toFixed(1)}}
        </span>
        <md-tooltip class="solid-popup"
                    md-direction="bottom">
          <el-heating-tooltip source="vm.source" assessment-method="vm.assessmentMethod" heating-cooling-load-limits="vm.heatingCoolingLoadLimits"></el-heating-tooltip>
        </md-tooltip>
      </span>
    </div>

    <!-- Cooling -->
    <div class="el-performance-result">
      <label>Cooling (MJ/m<sup>2</sup>)</label>
      <span>

        <span ng-style="{ color: vm.determineHeatCoolResultColour(vm.heatingCoolingLoadLimits, vm.source, 'coolingLoad', 'coolingLoadLimit') }">
          {{vm.source.coolingLoad.toFixed(1)}}
        </span>

        <!-- Popup with Additional Data -->
        <md-tooltip class="solid-popup"
                    md-direction="bottom">
          <el-cooling-tooltip source="vm.source" assessment-method="vm.assessmentMethod" heating-cooling-load-limits="vm.heatingCoolingLoadLimits"></el-cooling-tooltip>
        </md-tooltip>

      </span>
    </div>

    <!-- Total -->
    <div class="el-performance-result">
      <label>Total (MJ/m<sup>2</sup>)</label>
      <span>

        <span ng-style="{ color: vm.source.totalEnergyLoad <= vm.source.energyLoadLimits.calculatedMaxEnergy ? 'var(--thermarate-green)' : 'var(--warning)' }">
          {{vm.source.totalEnergyLoad.toFixed(1)}}
        </span>

        <!-- Popup with Additional Data -->
        <md-tooltip class="solid-popup"
                    md-direction="bottom">
          <el-total-tooltip source="vm.source"></el-total-tooltip>
        </md-tooltip>

      </span>
    </div>

    <!-- Rating -->
    <div class="el-performance-result"
         ng-if="vm.source.assessmentMethod !== 'Performance Solution (Energy Load Limits)'">
      <label style="margin-bottom: 16px; margin-top: -1px;">Energy Rating</label>
      <span>

        <span ng-style="{ color: vm.source.energyRating >= 7 ? 'var(--thermarate-green)' : 'var(--warning)' }">
          {{vm.source.energyRating.toFixed(1)}}
        </span>

        <!-- Popup with Additional Data -->
        <md-tooltip class="solid-popup"
                    md-direction="bottom">
          <el-rating-tooltip source="vm.source" target-energy-rating="vm.targetEnergyRating"></el-rating-tooltip>
        </md-tooltip>

      </span>
    </div>

  </div>

</div>

<style>

</style>