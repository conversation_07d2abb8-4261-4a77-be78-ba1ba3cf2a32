(function () {
    'use strict';
    var controllerId = 'BulkEditModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', bulkEditModalController]);
    function bulkEditModalController($scope, $mdDialog) {
        var vm = this;

        vm.isArchiveTable = $scope.isArchiveTable;
        vm.pageSizeList = $scope.pageSizeList;

        vm.pageSize = null;
        vm.revision = null;
        vm.revisionDate = null;
        vm.isIncludedInReport = null;
        vm.isShownToClient = null;
        vm.stampAction = null;
        vm.archive = false;
        vm.delete = false;
        // vm.ignoreSheetSize = false;

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.confirm = function () {
            var response = {};

            response.pageSize = vm.pageSize;
            response.revision = vm.revision;
            response.revisionDate = vm.revisionDate;
            response.isIncludedInReport = vm.isIncludedInReport;
            response.isShownToClient = vm.isShownToClient;
            response.archive = vm.archive;
            response.delete = vm.delete;
            response.stampAction = vm.stampAction;
            // response.ignoreSheetSize = vm.ignoreSheetSize;

            $mdDialog.hide(response);
        };
    }
})();