// Name: projectdescriptionservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'projectdescriptionservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', projectdescriptionservice]);

    function projectdescriptionservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var canceller2 = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'projectdescription/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getProjectDescription: getProjectDescription,
            getProjectRatingModeList: getProjectRatingModeList,
            createProjectDescription: createProjectDescription,
            updateProjectDescription: updateProjectDescription,
            deleteProjectDescription:deleteProjectDescription,
            undoDeleteProjectDescription:undoDeleteProjectDescription,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, clientId, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate, clientId: clientId };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting ProjectDescription list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getProjectDescription(projectDescriptionCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {projectDescriptionCode: projectDescriptionCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting ProjectDescription: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createProjectDescription(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Description Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error creating Building Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateProjectDescription(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Description Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Building Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteProjectDescription(projectDescriptionCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { projectDescriptionCode: projectDescriptionCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting ProjectDescription: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteProjectDescription(projectDescriptionCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { projectDescriptionCode: projectDescriptionCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for ProjectDescription: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getProjectRatingModeList(pageSize, pageIndex, sort, filter, aggregate) {

            canceller2 = $q.defer();
            var wkUrl = baseUrl + 'GetRatingModes';
            var params = {  };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller2.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting ProjectRatingMode list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

    }
})();
