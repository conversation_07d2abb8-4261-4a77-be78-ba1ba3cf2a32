<!-- Specification Summary -->
<md-card>
    <md-card-header>
        <h3 style="margin: 0;">Specification Summary</h3>
    </md-card-header>
    <md-card-content>

        <table class="table-striped table-hover table-condensed shadow-z-1">
            <thead>
            <tr>
                <th>File Name</th>
                <th>Date</th>
                <th>Version</th>
                <th>Show on Client Portal</th>
                <th>Allow Download on Client Portal</th>
            </tr>
            </thead>
            <tbody>

            <tr ng-repeat="report in vm.specificationSummaryReports() track by $index">

                <td><a href="{{report.url}}" target="_blank">{{report.fileName}}</a></td>
                <td>{{report.createdOn | date: 'dd/MM/yyyy hh:mm a' }}</td>
                <td>{{report.versionNo | number }}</td>

                <td style="text-align: center;">
                    <md-checkbox ng-model="report.showOnClientPortal"
                                 ng-disabled="report.fileName.contains('Baseline') || vm.disabled"
                                 ng-click="report.allowDownloadOnClientPortal = false; vm.updateAllowReportDownloadState(report)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="report.allowDownloadOnClientPortal"
                                 ng-click="vm.updateAllowReportDownloadState(report)"
                                 class="vertically-condensed-ex"
                                 ng-disabled="report.showOnClientPortal !== true || vm.disabled"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
            </tr>
            </tbody>
        </table>

    </md-card-content>
</md-card>

<!-- Compliance Report -->
<md-card>
    <md-card-header>
        <h3 style="margin: 0;">Compliance Report</h3>
    </md-card-header>
    <md-card-content>

        <table class="table-striped table-hover table-condensed shadow-z-1">
            <thead>
            <tr>
                <th>File Name</th>
                <th>Date</th>
                <th>Version</th>
                <th>Show on Client Portal</th>
                <th>Allow Download on Client Portal</th>
            </tr>
            </thead>
            <tbody>

            <tr ng-repeat="report in vm.complianceReports() track by $index">

                <td><a href="{{report.url}}" target="_blank">{{report.fileName}}</a></td>
                <td>{{report.createdOn | date: 'dd/MM/yyyy hh:mm a' }}</td>
                <td>{{report.versionNo | number }}</td>

                <!-- ... temporarily disable all of this functionality for software report........ -->
                <td ng-if="report.fileName.contains('Software Report')"
                    style="text-align: center;">
                    <md-checkbox ng-value="false"
                                 ng-disabled="true"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td ng-if="report.fileName.contains('Software Report')"
                    style="text-align: center;">
                    <md-checkbox ng-value="false"
                                 ng-disabled="true"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <!-- End temporary measures -------------------------------------------------------->

                <td ng-if="!report.fileName.contains('Software Report')"
                    style="text-align: center;">
                    <md-checkbox ng-model="report.showOnClientPortal"
                                 ng-click="report.allowDownloadOnClientPortal = false; vm.updateAllowReportDownloadState(report)"
                                 ng-disabled="vm.disabled"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td ng-if="!report.fileName.contains('Software Report')"
                    style="text-align: center;">
                    <md-checkbox ng-model="report.allowDownloadOnClientPortal"
                                 ng-click="vm.updateAllowReportDownloadState(report)"
                                 class="vertically-condensed-ex"
                                 ng-disabled="report.showOnClientPortal !== true || vm.disabled"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
            </tr>
            </tbody>
        </table>

    </md-card-content>
</md-card>

<!-- TODO: -->
<!--ONLY appear when the Assessment is in the Complete state-->
<!--Clear/delete id the Assessment is taken out of the Complete state-->
<!--ONLY applies to the version of the Assessment that is current when the Report/Document was uploaded (i.e. if a document -->
<!--was uploaded for Assessment 1, it would no longer be "available" (via the show/download checkboxes)vfor Assessment 2 etc.)-->
<!-- TODO: ASK Client if he wants the version number on reports to increment or not. Currently they do not, as they are 
  deleted a fter you click edit, which means the 'next version' logic ignores them.... -->

<!-- Manually Uploaded Documents -->
<md-card>
    <md-card-header>
        <h3 style="margin: 0;">Manually Uploaded Documents</h3>
    </md-card-header>
    <md-card-content>

        <table class="table-striped table-hover table-condensed shadow-z-1">
            <thead>
            <tr>
                <th>File Name</th>
                <th>Date</th>
                <th>Version</th>
                <th>Show on Client Portal</th>
                <th>Allow Download on Client Portal</th>
                <th><!-- Actions --></th>
            </tr>
            </thead>
            <tbody>

            <tr ng-repeat="report in vm.customDocuments() track by report.fileId">

                <td><a href="{{report.url}}" target="_blank">{{report.fileName}}</a></td>
                <td>{{report.createdOn | date: 'dd/MM/yyyy hh:mm a' }}</td>
                <td>{{report.versionNo | number }}</td>

                <td style="text-align: center;">
                    <md-checkbox ng-model="report.showOnClientPortal"
                                 ng-disabled="report.fileName.contains('Baseline')"
                                 ng-click="report.allowDownloadOnClientPortal = false; vm.updateAllowReportDownloadState(report)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="report.allowDownloadOnClientPortal"
                                 ng-click="vm.updateAllowReportDownloadState(report)"
                                 class="vertically-condensed-ex"
                                 ng-disabled="report.showOnClientPortal !== true"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td>
                    <!-- 'More' button w/ Popup -->
                    <md-menu style="display: flex; justify-content: center;">

                        <!-- Initial '...' button, which launches options -->
                        <img md-menu-origin
                             class="clickable"
                             ng-click="$mdOpenMenu()"
                             src="/content/feather/more-horizontal.svg"
                             ng-disabled="vm.disabled"/>
                        <md-menu-content>

                            <!-- Download Button -->
                            <md-menu-item>
                                <md-button ng-click="vm.downloadFileForceDialog(report)">
                                    Download
                                </md-button>
                            </md-menu-item>

                            <md-menu-divider></md-menu-divider>

                            <!-- Delete -->
                            <md-menu-item>
                                <md-button ng-click="vm.deleteCustomDocument(report)"
                                           ng-disabled="vm.isLocked">
                                    <span style="color: orangered;">Delete</span>
                                </md-button>
                            </md-menu-item>
                        </md-menu-content>
                    </md-menu>
                </td>
            </tr>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="999">
                    <!-- Custom Upload Button -->
                    <!-- TODO: Only show in 'Complete' state -->
                    <div ng-show="!vm.disabled"
                         class="drawing-upload-area clickable"
                         style="height: 130px;"
                         ngf-drop="vm.uploadFiles($files)"
                         ngf-select="vm.uploadFiles($files)"
                         ngf-multiple="true"
                         ngf-drag-over-class="'drawing-upload-file-over'"
                    >

                        <!-- Notification of upload in progress -->
                        <div ng-if="vm.allUploadingFiles.length > 0"
                             style="display: grid; grid-template-columns: 1fr; align-items: center; justify-items: center;">

                            <!-- Upload + Processing Progress bar -->
                            <h3 style="margin-top: 0;">Uploading...</h3>
                            <div class="loading-bar-background"
                                 style="width: 400px;"
                                 ng-click="vm.stopClickThrough($event)">
                                <div class="loading-bar-processed"
                                     style="height: 100%; width: {{vm.drawingsUploadProgress()}}%;">
                                </div>
                            </div>
                        </div>

                        <!-- Actual upload section -->
                        <div ng-show="vm.allUploadingFiles.length == 0 && !vm.disabled">

                            <div style="text-align: center;">
                                <h3 style="margin-top: 0;">Upload Document</h3>
                                <button class="md-block feather-icon-button"
                                        style="transform: scale(1.2);"
                                        ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                        title="Click to upload file.">
                                    <img src="/content/feather/upload.svg" />
                                </button>
                                <div style="margin-top: 16px; font-size: 11px;">Click or Drag 'n' Drop</div>
                            </div>
                        </div>

                    </div>
                </td>
            </tr>
            </tfoot>
        </table>

    </md-card-content>
</md-card>
