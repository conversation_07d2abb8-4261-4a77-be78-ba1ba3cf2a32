<form name="constructionform"
      class="main-content-wrapper"
      style="width: 600px;"
      ng-style="{ 'width' : vm.isModal == true ? '600px' : ''}"
      novalidate
      data-ng-controller='ServiceTemplateUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-action-bar
             data-quick-find-model=''
             data-action-buttons='vm.actionButtons'
             data-refresh-list=''
             data-spinner-busy='vm.isBusy'
             data-new-record=""
             data-new-record-text=""
             data-is-modal="vm.isModal"
             data-hide="vm.hideActionBar">
        </div>
    </div>

    <!-- General -->
    <md-card>
        <md-card-header>
            <h2 style="margin: 0px;">General</h2>
        </md-card-header>
        <md-card-content>

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Category -->
                <!-- Depending on the Category, different inputs are available below. -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Category</label>
                    <md-select required
                               ng-disabled="vm.newRecord == false"
                               name="serviceCategory"
                               ng-model="vm.serviceTemplate.serviceCategory"
                               ng-model-options="{trackBy: '$value.serviceCategoryCode'}">
                        <md-option ng-value="category"
                                   ng-repeat="category in vm.serviceCategories">
                            {{category.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionCategory.$error">
                        <div ng-message="required">Category is required.</div>
                    </div>
                </md-input-container>

                <!-- Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Description</label>
                    <input type="text" name="description"
                           ng-model="vm.serviceTemplate.description"
                           md-maxlength="1000"
                           required />
                    <div ng-messages="constructionform.description.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 1000.</div>
                    </div>
                </md-input-container>

                <!-- Display Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Display Description</label>
                    <input type="text"
                           name="displayDescription"
                           ng-model="vm.serviceTemplate.displayDescription"
                           md-maxlength="1000"
                           required />
                    <div ng-messages="constructionform.description.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                    </div>
                </md-input-container>

                <!-- Show in Report -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>Show in Report</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               name="showInReport"
                               ng-model="vm.serviceTemplate.showInReport">
                        <md-option ng-value="true">
                            Yes
                        </md-option>
                        <md-option ng-value="false">
                            No
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.showInReport.$error">
                        <div ng-message="required">You must select a value.</div>
                    </div>
                </md-input-container>

                <!-- Manufacturer -->
                <md-input-container class="md-block"
                                    flex-gt-sm>
                    <label>Manufacturer</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               name="manufacturer"
                               ng-model="vm.serviceTemplate.manufacturer"
                               ng-model-options="{trackBy: '$value.manufacturerId'}">
                        <md-option ng-value="manufacturer"
                                   ng-repeat="manufacturer in vm.manufacturers">
                            {{manufacturer.description}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.manufacturer.$error">
                        <div ng-message="required">Manufacturer is required.</div>
                    </div>
                </md-input-container>

                <!-- Unit of Measure -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Unit of Measure</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               name="unitOfMeasure"
                               ng-model="vm.serviceTemplate.unitOfMeasure"
                               ng-model-options="{trackBy: '$value.unitOfMeasureCode'}">
                        <md-option ng-value="unit"
                                   ng-repeat="unit in vm.unitOfMeasureList">
                            {{unit.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.unitOfMeasure.$error">
                        <div ng-message="required">Unit of Measure is required.</div>
                    </div>
                </md-input-container>

                <!-- Favourite -->
                <md-input-container class="md-block" flex-gt-sm>
                    <md-checkbox ng-model="vm.serviceTemplate.isFavourite"
                                 ng-disabled="vm.editPermission == false"
                                 class="md-primary">
                        Favourite
                    </md-checkbox>
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Properties -->
    <md-card>
        <md-card-header>
            <h2 style="margin: 0px;">Properties</h2>
        </md-card-header>
        <md-card-content>

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Service Type (Like a sub-category) -->
                <!-- Depending on the Category above, different inputs are available -->
                <md-input-container class="md-block" flex-gt-sm
                                    ng-if="vm.serviceTemplate.serviceCategory.serviceTypeUIDisplay != null">
                    <label>{{vm.serviceTemplate.serviceCategory.serviceTypeUIDisplay}}</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.serviceTemplate.serviceType"
                               ng-model-options="{trackBy: '$value.serviceTypeCode'}">
                        <md-option ng-value="serviceType"
                                   ng-repeat="serviceType in vm.serviceTemplate.serviceCategory.serviceTypesForThisCategory"
                                   ng-click="vm.setLampPowerIfApplicable(serviceType)">
                            {{serviceType.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionCategory.$error">
                        <div ng-message="required">Service Category is required.</div>
                    </div>
                </md-input-container>

                <!-- Volume (L) -->
                <md-input-container ng-if="vm.showVolume(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Volume (L)</label>
                    <input name="volume"
                           ng-model="vm.serviceTemplate.volume"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
                </md-input-container>

                <!-- Pump Type -->
                <md-input-container ng-if="vm.showPumpType(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Pump Type</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.serviceTemplate.servicePumpType"
                               ng-model-options="{trackBy: '$value.servicePumpTypeCode'}">
                        <md-option ng-value="servicePumpType"
                                   ng-repeat="servicePumpType in vm.servicePumpTypes">
                            {{servicePumpType.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionCategory.$error">
                        <div ng-message="required">Service Category is required.</div>
                    </div>
                </md-input-container>

                <!-- Fuel Type -->
                <md-input-container ng-if="vm.showFuelType(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Fuel Type</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.serviceTemplate.serviceFuelType"
                               ng-model-options="{trackBy: '$value.serviceFuelTypeCode'}">
                        <md-option ng-value="serviceFuelType"
                                   ng-repeat="serviceFuelType in vm.serviceFuelTypes">
                            {{serviceFuelType.title}}
                        </md-option>
                    </md-select>
                    <div ng-messages="constructionform.constructionCategory.$error">
                        <div ng-message="required">Fuel Type is required.</div>
                    </div>
                </md-input-container>

                <!-- Energy Rating 2019 -->
                <md-input-container ng-if="vm.showStarRating(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>
                        {{vm.serviceTemplate.serviceCategory.serviceCategoryCode == 'Spa' || vm.serviceTemplate.serviceCategory.serviceCategoryCode == 'SwimmingPool'
                            ? 'Pump '
                            : ''}}
                        {{(vm.serviceTemplate.serviceCategory.serviceCategoryCode == 'SpaceHeatingSystem' || vm.serviceTemplate.serviceCategory.serviceCategoryCode == 'SpaceCoolingSystem') && (vm.serviceTemplate.serviceType.serviceTypeCode == 'HeatPumpDucted' || vm.serviceTemplate.serviceType.serviceTypeCode == 'HeatPumpNonDucted')
                            ? 'Energy Rating (GEMS 2019)'
                            : 'Energy Rating'}}
                    </label>
                    <input formatted-number
                           decimals="1"
                           ng-model="vm.serviceTemplate.starRating2019"/>
                </md-input-container>

                <!-- Ducted -->
                <md-input-container ng-if="vm.showDucted(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.isDucted">
                        Ducted
                    </md-checkbox>
                </md-input-container>

                <!-- Flued -->
                <md-input-container ng-if="vm.showFlued(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.isFlued">
                        Flued
                    </md-checkbox>
                </md-input-container>

                <!-- Lamp Power Rating -->
                <md-input-container ng-if="vm.showLampPowerRating(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Lamp Power Rating</label>
                    <input name="lampPowerRating"
                           ng-model="vm.serviceTemplate.lampPowerRating"
                           ng-disabled="vm.serviceTemplate.serviceType.serviceTypeCode == 'NotApplicable'"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
                </md-input-container>

                <!-- Control Device -->
                <md-input-container class="md-block" flex-gt-sm
                                    ng-if="vm.showControlDevice(vm.serviceTemplate.serviceCategory.serviceCategoryCode)">
                    <label>Control Device</label>
                    <md-select ng-model="vm.serviceTemplate.serviceControlDevice"
                               ng-model-options="{trackBy: '$value.serviceControlDeviceCode'}"
                               ng-disabled="vm.editPermission == false">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="controlDevice"
                                   ng-repeat="controlDevice in vm.serviceControlDevices">
                            {{controlDevice.title}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- Blade Diameter (mm) -->
                <md-input-container ng-if="vm.showBladeDiameter(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Blade Diameter (mm)</label>
                    <input name="bladeDiameter"
                           ng-model="vm.serviceTemplate.bladeDiameter"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
                </md-input-container>

                <!-- Permanently Installed -->
                <md-input-container ng-if="vm.showPermanentlyInstalled(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.isPermanentlyInstalled">
                        Permanently Installed
                    </md-checkbox>
                </md-input-container>

                <!-- Speed Controller -->
                <md-input-container ng-if="vm.showSpeedController(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.hasSpeedController">
                        Speed Controller
                    </md-checkbox>
                </md-input-container>

                <!-- Recessed -->
                <md-input-container ng-if="vm.showRecessed(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.isRecessed">
                        Recessed
                    </md-checkbox>
                </md-input-container>

                <!-- Sealed -->
                <md-input-container ng-if="vm.showSealed(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block"
                                    style="margin-bottom: 32px;">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.isSealed">
                        Sealed
                    </md-checkbox>
                </md-input-container>

                <!-- IC Rating -->
                <md-input-container ng-if="vm.showICRating(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block"
                                    flex-gt-sm>
                    <label>IC Rating</label>
                    <md-select ng-model="vm.serviceTemplate.icRating"
                               ng-model-options="{trackBy: '$value.icRatingCode'}"
                               ng-disabled="vm.editPermission == false">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="rating"
                                   ng-repeat="rating in vm.icRatings">
                            {{rating.title}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- Cut Out Diameter (mm) -->
                <md-input-container ng-if="vm.showCutOutDiameter(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block"
                                    flex-gt-sm>
                    <label>Cut Out Diameter (mm)</label>
                    <input name="cutOutDiameter"
                           ng-model="vm.serviceTemplate.cutOutDiameter"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
                </md-input-container>

                <!-- Length (mm) -->
                <md-input-container ng-if="vm.showLength(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Length (mm)</label>
                    <input name="length"
                           ng-model="vm.serviceTemplate.length"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57"/>
                </md-input-container>

                <!-- Width (mm) -->
                <md-input-container ng-if="vm.showWidth(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Width (mm)</label>
                    <input name="width"
                           ng-model="vm.serviceTemplate.width"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                </md-input-container>

                <!-- Array Capacity (kW) -->
                <md-input-container ng-if="vm.showArrayCapacity(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Array Capacity (kW)</label>
                    <input formatted-number
                           name="systemCapacity"
                           decimals="2"
                           ng-model="vm.serviceTemplate.systemCapacity" />
                </md-input-container>

                <!-- Area (m2) -->
                <md-input-container ng-if="vm.showArea(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Area (m<sup>2</sup>)</label>
                    <input formatted-number
                           name="area"
                           decimals="2"
                           ng-model="vm.serviceTemplate.area" />
                </md-input-container>

                <!-- Azimuth (o) -->
                <md-input-container ng-if="vm.showAzimuth(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Azimuth (&deg;)</label>
                    <input formatted-number
                           name="azimuth"
                           decimals="2"
                           ng-model="vm.serviceTemplate.azimuth" />
                </md-input-container>

                <!-- Pitch (o) -->
                <md-input-container ng-if="vm.showPitch(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Pitch (&deg;)</label>
                    <input formatted-number
                           name="pitch"
                           decimals="2"
                           ng-model="vm.serviceTemplate.pitch" />
                </md-input-container>

                <!-- Shade Factor (%) -->
                <md-input-container ng-if="vm.showShadeFactor(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Shade Factor (%)</label>
                    <input formatted-number
                           name="shadeFactor"
                           decimals="2"
                           ng-model="vm.serviceTemplate.shadeFactor" />
                </md-input-container>

                <!-- Inverter Capacity (kW) -->
                <md-input-container ng-if="vm.showInverterCapacity(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Inverter Capacity (kW)</label>
                    <input formatted-number
                           name="inverterCapacity"
                           decimals="2"
                           ng-model="vm.serviceTemplate.inverterCapacity" />
                </md-input-container>

                <!-- Battery Type -->
                <md-input-container ng-if="vm.showBatteryType(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Battery Type</label>
                    <md-select required
                               ng-disabled="vm.editPermission == false"
                               ng-model="vm.serviceTemplate.serviceBatteryType"
                               ng-change="vm.batteryTypeChange()"
                               ng-model-options="{trackBy: '$value.serviceBatteryTypeCode'}">
                        <md-option ng-value="batteryType"
                                   ng-repeat="batteryType in vm.serviceBatteryTypes">
                            {{batteryType.title}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- Battery Capacity (kWh) -->
                <md-input-container ng-if="vm.showBatteryCapacity(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block" flex-gt-sm>
                    <label>Battery Capacity (kWh)</label>
                    <input formatted-number
                           name="batteryCapacity"
                           decimals="2"
                           ng-disabled="vm.serviceTemplate.serviceBatteryType.serviceBatteryTypeCode == 'None'"
                           ng-model="vm.serviceTemplate.batteryCapacity" />
                </md-input-container>

                <!-- Heating System -->
                <md-input-container class="md-block" flex-gt-sm
                                    ng-if="vm.serviceTemplate.serviceCategory.heatingSystemTypeUIDisplay != null">
                    <label>{{vm.serviceTemplate.serviceCategory.heatingSystemTypeUIDisplay}}</label>
                    <md-select ng-model="vm.serviceTemplate.heatingSystemType"
                               ng-model-options="{trackBy: '$value.heatingSystemTypeCode'}"
                               ng-disabled="vm.editPermission == false">
                        <md-option ng-value="null"></md-option>
                        <md-option ng-value="heatingSystemType"
                                   ng-repeat="heatingSystemType in vm.heatingSystemTypesForCategoryCode(vm.serviceTemplate.serviceCategory.serviceCategoryCode)">
                            {{heatingSystemType.title}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- Cover -->
                <md-input-container ng-if="vm.showCover(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.hasCover">
                        Cover
                    </md-checkbox>
                </md-input-container>

                <!-- Time Switch -->
                <md-input-container ng-if="vm.showTimeSwitch(vm.serviceTemplate.serviceCategory.serviceCategoryCode)"
                                    class="md-block">
                    <md-checkbox class="checkbox-aligner"
                                 ng-disabled="vm.editPermission == false"
                                 style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                                 ng-model="vm.serviceTemplate.hasTimeSwitch">
                        Time Switch
                    </md-checkbox>
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <!-- Life Cycle and Cost -->
    <md-card>
        <md-card-title>
            <h2 style="margin: 0px;">Life Cycle and Cost</h2>
            <md-checkbox class="checkbox-aligner"
                         ng-disabled="vm.editPermission == false"
                         style="margin-left: auto; margin-top: auto; margin-bottom: auto;"
                         ng-model="vm.serviceTemplate.lifeCycleData.hasConstructionLifeCycleAndCost"
                         ng-click="vm.toggle('lifeCycleData', 'hasConstructionLifeCycleAndCost');">

            </md-checkbox>
        </md-card-title>
        <md-card-content ng-if="vm.serviceTemplate.lifeCycleData.hasConstructionLifeCycleAndCost == true">

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Cost Per -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Cost ($/{{vm.serviceTemplate.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.serviceTemplate.lifeCycleData.costPerM2" />
                </md-input-container>

                <!-- Embodied Energy (MJ/m2) -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied Energy (MJ/{{vm.serviceTemplate.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.serviceTemplate.lifeCycleData.embodiedEnergy" />
                </md-input-container>

                <!-- Embodied Water (L/m2) -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied Water (L/{{vm.serviceTemplate.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.serviceTemplate.lifeCycleData.embodiedWater" />
                </md-input-container>

                <!-- Embodied GHG Emissions -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Embodied GHG Emission (kgCO<sub>2</sub>e/{{vm.serviceTemplate.unitOfMeasure.title}})</label>
                    <input formatted-number
                           decimals="2"
                           ng-model="vm.serviceTemplate.lifeCycleData.embodiedGhgEmissions" />
                </md-input-container>

            </fieldset>

        </md-card-content>
    </md-card>

    <div data-cc-widget-button-bar
         data-is-modal="vm.isModal">
        <div data-ng-show="vm.isBusy"
             data-cc-spinner="vm.spinnerOptions"></div>
        <md-button class="md-raised md-primary"
                   ng-disabled="constructionform.$invalid || vm.editPermission == false"
                   ng-show="vm.serviceTemplate.deleted!=true"
                   ng-click="vm.save()">
            Save
        </md-button>
        <md-button class="md-raised md-warn"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.serviceTemplate.serviceTemplateId !=null && vm.serviceTemplate.deleted!=true"
                   ng-confirm-click="vm.delete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to delete this record.">
            Delete
        </md-button>
        <md-button class="md-raised"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.serviceTemplate.deleted == true"
                   ng-confirm-click="vm.undoDelete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to RESTORE this record.">
            Restore
        </md-button>
        <md-button class="md-raised"
                   ng-click="vm.cancel()">
            Cancel
        </md-button>
        <div class="clearfix"></div>
    </div>

    <div class="col-md-12" ng-if="vm.newRecord==false">
        <div rd-display-created-modified ng-model="vm.serviceTemplate"></div>
    </div>

</form>
