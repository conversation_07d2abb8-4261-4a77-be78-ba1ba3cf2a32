(function () {

  'use strict';
  var controllerId = 'EnergyLabsIdentifyController';
  angular.module('app')
         .controller(
             controllerId,
             ['$scope', '$stateParams', '$state', 'common', 'standardmodelservice', 'projectservice', 'addressservice', 'bootstrap.dialog', '$rootScope', '$mdDialog', energyLabsIdentifyController]
         );

  function energyLabsIdentifyController(
      $scope, $stateParams, $state, common, standardmodelservice, projectservice, addressservice, modalDialog, $rootScope, $mdDialog
  ) {

    let vm = this;

    vm.projectId = $stateParams.projectId;
    vm.project = null;
    vm.isBusy = false;
    vm.availableOptionData = {};
    vm.filterData = {};
    vm.standardModel = {
      optionData: {},
      designData: {},
    };
    vm.matchingOptions = [];

    vm.designVariables = [
      "livingAreas",
      "storeys",
      "numberOfBedrooms",
      "numberOfBathrooms",
      "numberOfGarageSpots",
      "width"
    ];

    vm.existingStandardModels = null;

    vm.determineHeatCoolResultColour = common.determineELHeatCoolResultColour;

    vm.targetEnergyRating = null;

    vm.backToProjects = function () {
      $state.go('energy-labs-parent-menu');
    }

    vm.refreshDesigns = function() {
        standardmodelservice.getForProject(vm.projectId, null).then(data => {
            vm.existingStandardModels = data;
        });
    }
    vm.refreshDesigns();

    async function initialize(projectId) {
        if (projectId != null) {
            projectservice.getProject(projectId).then(data => {
                vm.project = data;
                if (vm.project == null) {
                    vm.showResults = false;
                    return;
                } else {
                    // Apply project defaults.
                    vm.restoreDefaults();
                }
                determineAvailableOptionData(vm.project);
            });
        }
        vm.standardModel = {
            optionData: {},
            designData: {},
        };
    }

    // Return to home page
    vm.cancel = function () {
        $state.go("/");
    }

    function determineAvailableOptionData(project) {

      const allProperties = project.energyLabsSettings.properties;
      var allData = project.energyLabsSettings.allowedVariables;
      const metadata = project.energyLabsSettings.variableOptionDefaults.generalOptionData;
      var allowedData = {};

      for (let key in allData) {

        // Don't include if toggled off.
        if(allProperties[key] === false)
          continue;

        allowedData[key] = allData[key]
          .filter(x => x.isAllowed)
          .map(x => {
            const match = metadata[key]?.find(y => y.optionValue === x.value);

            if(match != null)
              return match;

            return  x.value;
          })
          .sort((a, b) => {

            if(a?.sortOrder != null && b?.sortOrder != null)
              return a.sortOrder - b.sortOrder;

            if(a?.sortOrder != null && b?.sortOrder == null)
              return -1;

            if(a?.sortOrder == null && b?.sortOrder != null)
              return -1;

            // Falllback to string compare when so no sort order set on any.
            if (typeof a == 'string' && b != null) {
              return a.localeCompare(b);
            } else {
              return a - b;
            }

          })
          .map(x => x.optionValue == null && x.optionValue !== 0 ? x : x.optionValue);

      }

      vm.availableOptionData = allowedData;

    }

    vm.expandedHeight = function (option) {
        let height = 60;
        height += option.variationFloorplanName     != null ? 60 : 0;
        height += option.variationDesignOptionName  != null ? 60 : 0;
        height += option.variationFacadeName        != null ? 60 : 0;
        height += option.variationSpecificationName != null ? 60 : 0;
        height += option.variationConfigurationName != null ? 60 : 0;
        return height;
    }

    vm.updateDesignFilters = function ({ filterData }) {
        vm.currentTotal = null;
        vm.project.energyLabsSettings.distinctDesignOptions.features = standardmodelservice.allFeatures;
        vm.project.energyLabsSettings.distinctDesignOptions.categories = standardmodelservice.categories;
        vm.standardModel.designData = angular.copy(filterData);

        // Reset all 'features' data points.
        for(const key in vm.standardModel.designData) {
        if(key.startsWith("feature") && key !== "features")
            delete vm.standardModel.designData[key];
        if(key.startsWith("categories") && key !== "category")
            delete vm.standardModel.designData[key];
        }

        // Set needed ones to true.
        filterData.features?.forEach(x => {
            vm.standardModel.designData[x] = true;
        });
        filterData.categories?.forEach(x => {
            vm.standardModel.designData[x] = true;
        });

        delete vm.standardModel.designData.features;
        delete vm.standardModel.designData.categories;

        standardmodelservice.getForProjectMultiFiltered(
            vm.projectId,
            {
                fields: standardmodelservice.multiFiltersFields,
                distinctDesignOptions: vm.project.energyLabsSettings.distinctDesignOptions,
                appliedFilters: filterData,
                selectedVariationsForModels: {}
            }
        ).then(response => {
            vm.filterCountData = response.filterCountData;
            if (filterData == null) {
                vm.totalWithoutFilters = response.totalItems;
            } else {
                vm.currentTotal = response.totalItems;
            }
            vm.anyFiltersApplied = standardmodelservice.anyFiltersApplied(filterData);
        });
        vm.clearResults();
    }

    vm.restoreDefaults = function () {
        for (const key in vm.filterData) {
            vm.filterData[key] = ['Any'];
        }
        vm.updateDesignFilters({ filterData: { features: [] } });
        standardmodelservice.assignProjectDefaults([vm.standardModel], vm.project.energyLabsSettings);
        // Suburb
        if (vm.project.lockWOHLocation && vm.project.suburbCode != null) {
            vm.standardModel.suburb = vm.project.suburbName;
            addressservice.climateZoneCodeByPostCode(vm.project.suburb.postcode).then(climateZone => {
                vm.standardModel.optionData.natHERSClimateZone = climateZone.slice(3);
            });
        } else {
            vm.standardModel.suburb = null
            vm.standardModel.suburbObject = null
        }
        vm.modelBlockShowError = false;
        // State
        if (vm.project.lockWOHLocation && vm.project.stateCode != null) {
            vm.standardModel.stateCode = vm.project.stateCode;
        } else {
            vm.standardModel.stateCode = null;
        }
        if (vm.energyLabsForm != null) {
            vm.energyLabsForm.$setPristine();
            vm.energyLabsForm.$setUntouched();
        }
        vm.showResults = false;
    }

    vm.clearForm = function () {
        for (const key in vm.filterData) {
            vm.filterData[key] = ['Any'];
        }
        vm.updateDesignFilters({ filterData: { features: [] } });
        vm.standardModel.designData = {};
        // Only clear natHERSCliamteZone, suburb and state if lockWOHLocation toggled off
        if (!vm.project.lockWOHLocation) {
            vm.standardModel.optionData = {};
            vm.standardModel.suburb = null;
            vm.standardModel.suburbObject = null;
            vm.standardModel.stateCode = null;
        } else {
            vm.standardModel.optionData = { natHERSClimateZone: vm.standardModel.optionData?.natHERSClimateZone };
        }
        vm.clearResults();
        vm.modelBlockShowError = false;
        if (vm.energyLabsForm != null) {
            vm.energyLabsForm.$setPristine();
            vm.energyLabsForm.$setUntouched();
        }
        vm.showResults = false;
    }

    vm.clearResults = function() {
      vm.showResults = false;
    }

    // Calculate
    vm.identify = async function () {

      if (vm.isBusy)
        return;

      try {

        vm.isBusy = true;

        vm.targetEnergyRating = vm.project.energyLabsSettings.heatingCoolingLoadLimits ? vm.project.energyLabsSettings.targetEnergyRating : null;
        vm.matchingOptions = await standardmodelservice.identify(vm.project.projectId, vm.standardModel, vm.standardModel.stateCode, vm.targetEnergyRating);
        vm.matchingOptions.forEach(o => o.launchExpanded = false);

        vm.showResults = true;
        vm.isBusy = false;

      } catch (e) {

        throw e;

      } finally {
        vm.isBusy = false;
      }

    }

    vm.goBack = function () {
      $state.go("energy-labs-parent-menu");
    }

    vm.goToOtherTool = async function(toolName, option) {
      let result = await modalDialog.confirmationDialog(
        "Confirm",
        "Continuing will result in the data on this page to be lost. Are you sure you want to proceed?",
        "Proceed",
        "Cancel");

      if (result !== null) {
        $state.go(`energy-labs-${toolName}`, {
          projectId: vm.project.projectId,
          standardHomeModelId: option.isVariationOfHomeModelId,
          variationId: option.standardHomeModelId,
          standardHomeModelOptionId: option.standardHomeModelOptionId,
          suburb: vm.standardModel.suburb,
          stateCode: vm.standardModel.stateCode,
          nccClimateZone: vm.standardModel.optionData.nccClimateZone
        });
      }
    }

    vm.expandResult = function ($event, allOptions, option) {
        $event.stopPropagation();
        let wasExpanded = option.uiExpanded;
        allOptions.forEach(x => {
            x.uiExpanded = false;
            x.launchExpanded = false;
        });
        option.uiExpanded = !wasExpanded;
    }

    vm.anyLaunchDropdownsExpanded = function () {
        return vm.matchingOptions.some(o => o.launchExpanded);
    }

    vm.collapseAllLaunchDropdowns = function () {
        vm.matchingOptions.forEach(o => o.launchExpanded = false);
    }

    // Open 3D Model modal
    vm.open3dViewerModal = function (option, event) {
        if (event) event.stopPropagation();
        // Collapse the LAUNCH menu immediately when modal opens
        vm.collapseAllLaunchDropdowns();
        var modalScope = $rootScope.$new();
        modalScope.floorplannerLink = option.floorplannerLink;
        $mdDialog.show({
            scope: modalScope,
            templateUrl: 'app/ui/energy-labs/modals/home-plan-3d-viewer-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: true,
        });
    }

    // Open Floor Plan modal
    vm.openSwitcherModal = function (option, event) {
        if (event) event.stopPropagation();
        // Collapse the LAUNCH menu immediately when modal opens
        vm.collapseAllLaunchDropdowns();
        var modalScope = $rootScope.$new();
        modalScope.planViews = option.standardHomeModelFiles;
        modalScope.currentIndex = 0;
        $mdDialog.show({
            scope: modalScope,
            templateUrl: 'app/ui/energy-labs/modals/home-plan-switcher-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: true,
        });
    }

    vm.keyToName = standardmodelservice.keyToName;
    vm.toSplitTitleCase = common.toSplitTitleCase;

    initialize($stateParams.projectId);

    $scope.$on('$destroy', function () {
        vm.existingStandardModels = null;
    });
  }
})();