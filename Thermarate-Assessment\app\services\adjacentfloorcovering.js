(function () {
    'use strict';
    var serviceId = 'adjacentfloorcoveringservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', adjacentfloorcoveringservice]);

    function adjacentfloorcoveringservice(common, config, $http, Upload) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        const baseUrl = config.servicesUrlPrefix + 'adjacentfloorcovering/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getAdjacentFloorCovering: getAdjacentFloorCovering,
            createAdjacentFloorCovering: createAdjacentFloorCovering,
            updateAdjacentFloorCovering: updateAdjacentFloorCovering,
            deleteAdjacentFloorCovering: deleteAdjacentFloorCovering,
            undoDeleteAdjacentFloorCovering: undoDeleteAdjacentFloorCovering,
            getAll,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status === 0 || error.status === -1) {
                    return;
                }
                var msg = "Error getting Adjacent Floor Covering list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting Adjacent Floor Covering List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }
        
        function getAdjacentFloorCovering(code) {
            return $http({
                url: baseUrl + 'Get',
                params: { code },
                method: 'GET',
                cache: true, 
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Adjacent Floor Covering: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAdjacentFloorCovering(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Adjacent Floor Covering Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created Adjacent Floor Covering: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAdjacentFloorCovering(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Adjacent Floor Covering Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Adjacent Floor Covering: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteAdjacentFloorCovering(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Adjacent Floor Covering: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAdjacentFloorCovering(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for AdjacentFloorCovering: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
