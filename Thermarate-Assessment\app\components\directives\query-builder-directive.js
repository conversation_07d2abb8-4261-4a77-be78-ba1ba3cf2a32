﻿(function () {
    'use strict';
    // Query Builder Directive.
    // Enables user to build up a database query.
    //------------------------------------------------------------------------

    var app = angular.module('app');
    
    app.directive('queryBuilder', ['$compile', '$http', 'config', 'common', 'constants', '$parse', '$rootScope', '$mdDialog',
        function ($compile, $http, config, common, constants, $parse, $rootScope, $mdDialog) {
        //Usage: 
        //<query-builder query-model='vm.queryModel' run-query='vm.refreshlist' query-name='test-query' current-query='vm.currentQuery' ></query-builder>
        // Where vm.queryModel is an object
        // {
        //    canSave: false,
        //    fields: [
        //              {
        //                  name: 'assetId',
        //                  description: 'Db Asset Id',
        //                  dataType: 'integer', -- Valid dateTypes are integer, string, decimal, date, boolean
        //                  operators : [] -- Not used yet.
        //              }
        //            ],
        // }
            
        return ({
            restrict: "E",
            replace: true,
            transclude: false,
            template: "<div class='query-wrapper' style='display:inline-block;'><md-button ng-click='showQueryModal()' class=''>Adv Search</md-button><div ng-if='showSummary'>{{currentQuery.summary}}</div></div>",
            scope: {
                'runQuery': '&',
                'currentQuery': '=',
                'queryModel': '=',
                'queryName': '=',
                'showSummary': '=',
            },
            controller: ['$scope', '$attrs', '$parse', '$timeout', function (scope, attrs, $parse, $timeout) {

                /*var vm = {};
                vm.queryModelPropertyName = attrs.queryModel;
                vm.queryModel = $parse(vm.queryModelPropertyName)(scope.$parent);
                vm.runQueryPropertyName = attrs.runQuery;
                vm.runQuery = scope.runQuery; //$parse(vm.runQueryPropertyName)(scope.$parent);
                vm.queryNamePropertyName = attrs.queryName;
                vm.queryName = $parse(vm.queryNamePropertyName)(scope.$parent);
                vm.currentQueryPropertyName = attrs.currentQuery;
                vm.currentQuery = $parse(vm.currentQueryPropertyName)(scope.$parent);*/

                if (scope.queryName != null) {
                    // Get saved query if one exists.
                    var test = localStorage.getItem(scope.queryName);
                    if (test && test != "undefined") {
                        scope.currentQuery = JSON.parse(localStorage.getItem(scope.queryName));
                        $timeout(function(){
                            run();
                        });
                    }
                }

                if (scope.currentQuery != null) {
                    scope.currentQuery.queryName = scope.queryName;
                }

                scope.showQueryModal = function (data) {
                    var modalScope = $rootScope.$new();
                    modalScope.viewMode = "Edit";
                    modalScope.queryModel = scope.queryModel;
                    modalScope.currentQuery = scope.currentQuery;
                    modalScope.runQuery = scope.runQuery;
                    modalScope.queryName = scope.queryName;
                    var modalOptions = {
                        templateUrl: 'app/components/directives/templates/query-builder-tpl.html',
                        controller: 'QueryBuilderFormController as vm',
                        scope: modalScope,
                    };
                    modalScope.modalInstance = $mdDialog.show(modalOptions);
                    modalScope.modalInstance.then(function (data) {
                        // Successfully closed, so run query.
                        run();

                    }, function () {
                        // Cancelled.
                    })['finally'](function () {
                        modalScope.modalInstance = undefined  // <--- This fixes
                    });
                };

                function run() {
                    $parse(attrs.runQuery)(scope.$parent);
                }
            }]
        });
        
        }]);

    // Query Summary Directive
    // Display a summary of the current query. Useful to show above lists, so the user is aware of what filters are in place.
    app.directive('querySummary', ['$compile', '$http', 'config', 'common', 'constants', '$parse', '$rootScope', '$sce',
        function ($compile, $http, config, common, constants, $parse, $rootScope, $sce) {
            //Usage: 
            //<query-summary run-query='vm.refreshlist' current-query='vm.currentQuery' ></query-summary>
            return ({
                restrict: "E",
                priority: -1,
                replace: true,
                transclude: true,
                template: "<div class='query-summary' ng-show='currentQuery.lines.length>0'>Filter has been applied. "
                        + "<div class='query-sum-line' ng-repeat='line in currentQuery.lines' ng-click='clearLine($index)' ng-show='line.field!=\"\"' title='Remove filter'>"
                        + "<span class='query-field'>{{line.field.description}}</span> "
                        + " {{line.operator.description.toLowerCase()}} "
                        + "<span ng-if='line.field.dataType!=\"date\"' class='query-val'>{{line.value}}</span>"
                        + "<span ng-if='line.field.dataType==\"date\"' class='query-val'>{{line.value | date : \"dd MMM yyyy\"}}</span>"
                        + "<span ng-if='line.value2!=null && line.field.dataType!=\"date\"'> and <span class='query-val'>{{line.value2}}</span></span>"
                        + "<span ng-if='line.value2!=null && line.field.dataType==\"date\"'> and <span class='query-val'>{{line.value2  | date : \"d MMM yyyy\"}}</span></span>"
                        + "<span ng-if='!$last'> {{line.logic.description.toLowerCase()}}&nbsp;</span>"
                        + "</div></div>",
                scope: {
                    "currentQuery": "=",
                    'runQuery': '&',
                },
                link: function (scope, element, attrs, queryBuilderCtrl) {

                    scope.clearLine = function (lineIndex) {
                        scope.currentQuery.lines.splice(lineIndex, 1);
                        flattenForFilter(scope.currentQuery);
                        scope.runQuery();
                        localStorage.setItem(scope.currentQuery.queryName, JSON.stringify(scope.currentQuery));
                    }
                }
            });

        }]);


    app.controller('QueryBuilderFormController', ['$scope', '$parse', '$filter', 'common', 'constants', '$sce', '$mdDialog',
        function queryBuilderFormController($scope, $parse, $filter, common, constants, $sce, $mdDialog) {
            var $q = common.$q;
            var log = common.logger;
            var vm = this;
            vm.isModal = true;
            vm.isBusy = false;
            vm.title = "Query Builder";

            vm.operators = [
                { name: 'contains', description: 'Contains', valueRequired: true, dataTypes : "string" },
                { name: 'startswith', description: 'Starts with', valueRequired: true, dataTypes: "string" },
                { name: 'endswith', description: 'Ends with', valueRequired: true, dataTypes: "string" },
                { name: 'eq', description: 'Is equal to', valueRequired: true, dataTypes: "string,integer,decimal,boolean,date" },
                { name: 'neq', description: 'Is not equal to', valueRequired: true, dataTypes: "integer,decimal,boolean,date" },
                { name: 'gt', description: 'Greater than', valueRequired: true, dataTypes: "integer,decimal" },
                { name: 'gte', description: 'Greater than or equal (>=)', valueRequired: true, dataTypes: "integer,decimal" },

                { name: 'lt', description: 'Less than', valueRequired: true, dataTypes: "integer,decimal" },
                { name: 'lte', description: 'Less than or equal (<=)', valueRequired: true, dataTypes: "integer,decimal" },
                
                { name: 'in', description: 'In', valueRequired: true, dataTypes: "string,integer,decimal" },
                { name: 'doesnotcontain', description: 'Does not contain', valueRequired: true, dataTypes: "string" },
                { name: 'after', description: 'Is after', valueRequired: true, dataTypes: "date" },
                { name: 'before', description: 'Is before', valueRequired: true, dataTypes: "date" },
                { name: 'between', description: 'Between', valueRequired: true, dataTypes: "integer,decimal,date" },

                { name: 'null', description: 'Is Empty', valueRequired: false, dataTypes: "string,integer,decimal,boolean,date" },
                { name: 'notnull', description: 'Not Empty', valueRequired: false, dataTypes: "string,integer,decimal,boolean,date" },
            ];
            vm.logics = [
                { name: "or", description: "OR" },
                { name: "and", description: "AND" },
            ];

            vm.datapickerOptions = {
                initDate: new Date(),
                showWeeks: true
            };

            vm.currentQuery = $scope.currentQuery;
            vm.queryModel = $scope.queryModel;
            vm.runQuery = $scope.runQuery;
            vm.queryName = $scope.queryName;

            if (vm.queryModel.fields != null) {
                vm.queryModel.fields = _.sortBy(vm.queryModel.fields, 'description');
            }

            if (vm.currentQuery == null || vm.currentQuery == {} || vm.currentQuery.lines == null || vm.currentQuery.lines.length == 0) {
                vm.currentQuery.name = "Default";
                vm.currentQuery.lines = [
                    {
                        field: "",
                        operator: vm.operators[0],
                        value: "",
                        value2: null,
                        logic: vm.logics[0],
                        isValueDateOpen: false,
                        isValue2DateOpen: false,
                    },
                ];

            }
            else {
                for (var i = 0, len = vm.currentQuery.lines.length; i < len; i++) {
                    var chkLine = vm.currentQuery.lines[i];
                    if (chkLine.field.dataType == 'date') {
                        if (typeof chkLine.value === "string") {
                            if (chkLine.value != null && chkLine.value.length > 19
                                && moment(chkLine.value.substr(0, 19), "YYYY-MM-DDTHH:mm:ss", true).isValid()) {
                                chkLine.value = (moment(chkLine.value)).toDate();
                            }
                        }
                        if (typeof chkLine.value2 === "string") {
                            if (chkLine.value2 != null && chkLine.value2.length > 19
                                && moment(chkLine.value2.substr(0, 19), "YYYY-MM-DDTHH:mm:ss", true).isValid()) {
                                chkLine.value2 = (moment(chkLine.value2)).toDate();
                            }
                        }
                    }
                }
            }

            vm.addQueryLine = function () {
                var newLine = {
                    field: "",
                    operator: vm.operators[0],
                    value: "",
                    value2: null,
                    logic: vm.logics[0],
                    isValueDateOpen : false,
                    isValue2DateOpen : false,
                };
                if (vm.currentQuery.lines.length > 0) {
                    newLine.logic = vm.currentQuery.lines[vm.currentQuery.lines.length - 1].logic;
                }
                vm.currentQuery.lines.push(newLine);
            }

            vm.fieldChanged = function(line){
                if (line.field != null && line.operator != null) {
                    if (line.operator.dataTypes.indexOf(line.field.dataType) == -1) {
                        var defOp = _.find(vm.operators, function(val) { return val.dataTypes.indexOf(line.field.dataType) != -1 });
                        line.operator = defOp;
                        line.value = null;
                        line.value2 = null;
                    }
                }
            }

            vm.cancel = function() {
                if ($scope.modalInstance != null) {
                    flattenForFilter(vm.currentQuery, true);
                    $mdDialog.cancel('cancel');
                }
            };

            vm.close = function () {
                if ($scope.modalInstance != null) {
                    flattenForFilter(vm.currentQuery, true);
                    localStorage.setItem(vm.queryName, JSON.stringify(vm.currentQuery));
                    $mdDialog.hide();
                }
            }

            vm.run = function () {
                flattenForFilter(vm.currentQuery);
                vm.runQuery();
                localStorage.setItem(vm.queryName, JSON.stringify(vm.currentQuery));
            }

            vm.removeLine = function (lineIndex) {
                vm.currentQuery.lines.splice(lineIndex,1);
            }
            vm.clear = function() {
                vm.currentQuery.lines = [];
                vm.currentQuery.filter = [];
                vm.currentQuery.summary = "";
                vm.runQuery();
            }

            

            vm.openLineValueDate = function ($event, line) {
                line.isValueDateOpen = true;
            }

            vm.openLineValue2Date = function ($event, line) {
                line.isValue2DateOpen = true;
            }

    }]);

    function flattenForFilter(currentQuery, removeEmpty) {

        currentQuery.filter = [];
        if (currentQuery.lines.length > 0) {
            if (removeEmpty == true) {
                for (var i = currentQuery.lines.length - 1; i >= 0; i--) {
                    if (currentQuery.lines[i].field == "") {
                        currentQuery.lines.splice(i, 1);
                    }
                }
            }

            for (var i = 0, len = currentQuery.lines.length; i < len; i++) {
                var line = currentQuery.lines[i];
                if (line.field != null && line.field != "") {
                    var fil = {
                        field: line.field.name,
                        operator: line.operator.name,
                        value: line.value,
                        value2: line.value2,
                        valueType: line.field.dataType,
                        logic: line.logic.name
                    };
                    if (line.field.dataType == "date") {
                        if (line.value != null && line.value instanceof Date) {
                            fil.value = line.value.toISOString();
                        }
                        if (fil.value2 != null && line.operator.name == "between") {
                            fil.value2 = line.value2.toISOString();
                        }
                    }
                    currentQuery.filter.push(fil);
                }
            }
            if (currentQuery.filter.length > 0) {
                currentQuery.filter[currentQuery.filter.length - 1].logic = "";
            }
        }
    }
	
})();