(function () {
    'use strict';
    const serviceId = 'designchangeservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', designchangeservice]);

    function designchangeservice(common, config, $http, Upload) {
        
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'designchange/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getDesignChange: getDesignChange,
            createDesignChange: createDesignChange,
            updateDesignChange: updateDesignChange,
            deleteDesignChange:deleteDesignChange,
            undoDeleteDesignChange: undoDeleteDesignChange,
            copyDesignChange,
            getAll,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting DesignChange list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting Design Change List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }
        
        function getDesignChange(id) {
            return $http({
                url: baseUrl + 'Get',
                params: { id },
                method: 'GET',
                cache: true, 
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createDesignChange(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Design Change Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateDesignChange(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Design Change Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteDesignChange(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteDesignChange(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyDesignChange(designChangeId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { designChangeId },
                method: 'POST',
            }).then(success, fail)

            function success(resp) {
                log.logSuccess("Design Change copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Design Change: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
    }
})();
