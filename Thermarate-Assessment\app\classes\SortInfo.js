﻿/**
 * Used to keep track of sort direction and on which column a sort routine 
 * should be applied to (generally for 'temporary' sorts).
 * 
 * @property { "asc" | "direction" | null } direction The direction to sort the column.
 * @property { string } column The 'column' (property) of the 'row' (object) to sort on.
 * @property { function(value) } transform Optional callback which can be used to apply transformations to raw column data.
 * 
 */
class SortInfo {
    direction;
    column;
    transform;
}

// Testing...
/**
 * Simple interface for HTTP requests which we decide to cache (i.e. unlikely to change on next call).
 *
 * @property { any | null } data Null if not cached, otherwise contains the data response from our HTTP request.
 * @property { Promise } promise  Null if request is not pending, otherwise contains the in-progress OR resolved
 *                                promise with data.
 */
class Cacheable {
    data;
    promise;
}

const SHOW_CACHE_DEBUG_DATA = true;

/**
 * Checks the given Cacheable to see if it already has available data OR is in the middle of a request. If true,
 * use the returned promise which will resolved with needed data.
 *
 * @param { Promise<Cacheable> }item
 * @param { string } debugTitle If true, debug info will be printed.
 * @return {{cached: boolean, data: Promise<any>} | {data: Promise<any>, cached: boolean}}
 */
function checkCache(item, debugTitle = null) {

    if(item.data != null && item.data.length > 0) {
        
        if(SHOW_CACHE_DEBUG_DATA && debugTitle)
            console.log("Returning data from cache for " + debugTitle);
        
        return { cached: true, data: Promise.resolve(item.data) };
    }

    if(item.promise != null) {

        if(SHOW_CACHE_DEBUG_DATA && debugTitle)
            console.log("Returning promise from cache for " + debugTitle);
        
        return { cached: true, data: item.promise };
    }

    if(SHOW_CACHE_DEBUG_DATA && debugTitle)
        console.log("No cached data for " + debugTitle);
    
    return { cached: false, data: null}
}


/**
 *
 * @param response
 * @param { Cacheable | null } cacheable
 * @return { Promise<null|*> }
 */
function cacheResponse(response, cacheable) {

    if (response != null && response.data !== undefined) {

        if(cacheable != null) {
            cacheable.data = response.data;
        }

        return response.data;
    }
    else {

        // Returning null PROBABLY? indicates a failure...???? So we 
        // don't bother caching this.
        if(cacheable != null)
            cacheable.promise = null;

        return null;
    }
}