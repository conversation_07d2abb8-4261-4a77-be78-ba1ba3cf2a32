(function () {
    'use strict';
    var controllerId = 'BulkEditServiceTemplateModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', bulkEditServiceTemplateModalController]);
    function bulkEditServiceTemplateModalController(common, $scope, $mdDialog) {

        // - VARIABLES - //

        let vm = this;
        vm.isBusy = false;

        vm.data = { 
            bulkEditAction: "COPY"
        };

        // - HANDLES - //

        vm.confirm = function () {
            $mdDialog.hide(vm.data.bulkEditAction);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();
