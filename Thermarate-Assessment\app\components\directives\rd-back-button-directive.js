﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('rdBackButton', ['$rootScope', '$state', function ($rootScope, $state) {
        // Usage:
        // <rd-back-button default-nav="batches"></rd-back-button>  
        var directive = {
            link: link,
            template: '<button class="btn btn-default btn-xs navbar-back" type="button">Back</button>',
            restrict: 'E'
        };
        return directive;

        function link(scope, element, attrs) {
            element.click(back);
            scope.previousRoute = $rootScope.previousState;
            function back(e) {

                if (scope.previousRoute != null) {
                    $state.go(scope.previousRoute);
                }
                else {
                    $state.go(attrs.defaultNav);
                }
            }
        }
    }]);
})();