﻿// Important to note that even though this is called "metromapservice" it is _mostly_ generic
// due to its usage of Leaflet.js. I.e. it works with google maps, open street maps etc.
// There are certain functions specific to MetroMaps however.
(function () {
    'use strict';
    var serviceId = 'metromapservice';
    angular.module('appservices')
        .factory(serviceId, ['common', 'config', '$http', metromapservice]);

    function metromapservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;

        const baseUrl = config.servicesUrlPrefix + 'MapImage/';

        // MetroMaps api keys and useful URLS
        const apiKey = '4gvihy977zg5rp96bmhvw7wduq74b1z50spjnr7cqqkk9a6nphrwxakpcbkxk9ev';
        
        // Old url: `https://api.metromap.com.au/metromapkey/wms?key=${apiKey}&SERVICE=/WMTS`;
        // New url. Note this is the 'GetCapabilities' url but with the ?GetCapabilities flag removed...
        const metroWmsUrl = `https://api.metromap.com.au/ogc/key/${apiKey}/service`;
        
        const CROP_RECT_KEY = "Crop Rectangle";
        const CROP_POLY_KEY = "Crop Polygon";
        const GPS_MARKER_KEY = "Set Latitude & Longitude";

        let MARKER_KEYS = {
            GPS: "GPS_MARKER",
            AUTO: "AUTO_MARKER"
        };
        
        var service = {
            initializeMap: initializeMap,
            initializeGeoman: initializeGeoman,
            initializeBigImage: initializeBigImage,
            addOnCreateCallback: addOnCreateCallback,
            takePartialSnapshot: takePartialSnapshot,
            takeFullscreenSnapshot: takeFullscreenSnapshot,
            clearPolygonLayers: clearPolygonLayers,
            unprojectBoundary: unprojectBoundary,
            drawBoundary: drawBoundary,
            zoomToBoundary: zoomToBoundary,
            drawBoundaryAndZoomIfExists: drawBoundaryAndZoomIfExists,
            uploadMapImageAndStore: uploadMapImageAndStore,
            clearExistingMarkers,
            MARKER_KEYS,
            isDataAvailableAtCoordinates,
            // switchToMetroMapsLayer,
            // switchToGoogleMapsLayer,
        };

        /**
         * private: Uploads the given image to Amazon S3 using our API and returns data about said image
         * including URL etc.
         */
        function storeMapImage(imageDataOrUrl, assessmentId, jobId) {

            if (imageDataOrUrl && imageDataOrUrl.includes("data:image/png;base64,")) {
                return $http.post(baseUrl + "UploadMapImage/" + assessmentId + "/" + jobId, { MapImage: imageDataOrUrl }).then(success, fail)
                function success(resp) {
                    if (resp != null && resp.data != undefined && resp.data != null) {
                        return resp.data;
                    } else {
                        return null;
                    }
                }
                function fail(error) {
                    var msg = "Error in UploadMapImage: " + error;
                    log.logError(msg, error, null, true);
                    throw error; // so caller can see it
                }
            } else {

                return new Promise(function (resolve, reject) {
                    return null;
                });
            }
        }

        /**
         * Uploads the raw buffer to our server / S3 bucket and stores the resulting URL into 
         * assessment[property].
         * 
         * @param {any} rawImageBuffer
         * @param {any} assessment Actual assessment object where returned map image will be stored.
         * @param {any} property The property of the 'assessment' where you wish to save the stored data.
         * @param {any} jobId
         */
        function uploadMapImageAndStore(rawImageBuffer, assessment, property, jobId) {

            var defer = $q.defer();

            if (rawImageBuffer == null) {

                // NOTE: If the user has discarded the image, the relevant projectDetail
                // properties will already be null. If they are NOT null, it means we wish to
                // keep the old image (if any).
                defer.resolve();

            } else {

                // Only store if it is image-data, not just an s3 address
                storeMapImage(rawImageBuffer, assessment.assessmentId, jobId)
                    .then(function (fileDto) {

                        // Image uploaded and returned sucessfully, setting data...
                        assessment.assessmentProjectDetail[property] = fileDto;
                        assessment.assessmentProjectDetail[property + "Id"] = fileDto.fileId;
                        defer.resolve();
                    }, function () {
                        console.log("Warning! Failed to upload image. Continuing anyway...");
                        assessment.assessmentProjectDetail[property] = null;
                        assessment.assessmentProjectDetail[property + "Id"] = null;
                        defer.resolve();
                    });
            }

            return defer.promise;

        }
        
        /**
         * Initializes our map. Should only be called once. Is responsible for creating map layers and so on. 
         * Returns the initialized map.
         * 
         * @param mapElem
         * @param coords
         * @param { 'street' | 'satellite' | 'metro' } mapType
         * @returns {{success: boolean, rOverlay, rMap: *, rMarkerLayer}|{success: boolean}}
         */
        function initializeMap(mapElem, coords, mapType) {

            // Our secondary "MapBox" layer which can be used if there is a problem with
            // the metromap service for some reason.
            const MAP_BOX_CONFIG = L.tileLayer('https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw', {
                maxZoom: 22, // Lower = zoomed out, higher =  zoomed IN
                attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, ' +
                    '<a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, ' +
                    'Imagery � <a href="https://www.mapbox.com/">Mapbox</a>',
                id: 'mapbox/streets-v11',
                tileSize: 512,
                zoomOffset: -1
            });

            // * Our default "MetroMap" layer which should be used for all
            const METRO_MAP_CONFIG = L.tileLayer.wms(metroWmsUrl, {
                layers: 'Australia_latest', // This is derived from the /GetCapabilities URL
                maxZoom: 26, // Lower = zoomed out, higher =  zoomed IN
                attribution: "MetroMap powered by Aerometrex",
                id: 'metro_maps/dev',
                tileSize: 256,
                zoomOffset: -1
            });

            const GOOGLE_STREETS_CONFIG = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}',{
                maxZoom: 21,
                subdomains:['mt0','mt1','mt2','mt3']
            });

            const GOOGLE_SATELLITE_CONFIG = L.tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',{
                maxZoom: 19,
                subdomains:['mt0','mt1','mt2','mt3']
            });
            
            const GOOGLE_SATELLITE_LAYER = { "Google Satellite": GOOGLE_SATELLITE_CONFIG };
            const GOOGLE_STREETS_LAYER = { "Google Maps": GOOGLE_STREETS_CONFIG };
            const METRO_MAPS_LAYER = { "MetroMaps": METRO_MAP_CONFIG };
            const MAP_BOX_LAYER = { "MapBox": MAP_BOX_CONFIG }
            
            // Toggleable overlay where the user can draw polygons and so on
            // ? Not currently implemented.
            var geoOverlay = L.featureGroup();
            let markerLayer = L.featureGroup();
            
            // * Construct our actual map.
            try {

                // Bit of a dodgy hack to handle situations where the given element already
                // contains a map.
                var container = L.DomUtil.get(mapElem);
                if (container != null) {
                    container = {}; // ?? lol
                }

                let initialLayer = [GOOGLE_STREETS_CONFIG];
                
                if(mapType === "metro")
                    initialLayer = [METRO_MAP_CONFIG];
                else if(mapType === "satellite")
                    initialLayer = [GOOGLE_SATELLITE_CONFIG];
                
                var map = L.map(mapElem, {
                    center: [coords.lat, coords.lng],
                    zoom: 18,
                    layers: initialLayer, // Available layers. Does not show a control unless we speciy
                    // crs: L.CRS.EPSG4326, // crs refers to the coordinate system used, and can be found within the GetCapabilities file/query.
                    renderer: L.canvas(),
                    zoomSnap: 1,
                });

                // Available  tile layers which can be selected from the map view. Only one
                // can be active at once.
                let availableTileLayers = {
                    ...METRO_MAPS_LAYER,
                    ...GOOGLE_STREETS_LAYER,
                    ...GOOGLE_SATELLITE_LAYER
                };

                // Available overlays for the map. If there are multiple layers, they can be
                // toggled on and off independantly of each other.
                var availableOverylayLayers = {
                    "Overlay": geoOverlay,
                    "Markers": markerLayer,
                }

                // Add our swappable layers to the map.
                L.control.layers(availableTileLayers, availableOverylayLayers).addTo(map);

                markerLayer.addTo(map); // This toggle the markerLayer on by default.

                console.log("Map initialized successfully.");
                return {
                    success: true,
                    rMap: map,
                    rOverlay: geoOverlay,
                    rMarkerLayer: markerLayer
                };
            }
            catch (e) {

                // Map probably already initialized, return...?
                console.log("! Hiccup initializing map: ", e);
                return { success: false };
            }
            
        }

        /// Initialize the GEOMAN leaflet plugin (For drawing on the map).
        function initializeGeoman(mapObj) {

            let map = mapObj.map;
            let geoOverlay = mapObj.geoOverlay;
            let markerLayer = mapObj.markerLayer;

            L.PM.initialize({ optIn: false });

            map.pm.addControls({
                position: 'topleft',
                drawCircle: false,
            });

            map.pm.setGlobalOptions({ layerGroup: geoOverlay });

            // Add custom crop/print tool to toolbar
            // copy a rectangle and customize its name, block, title and actions
            map.pm.Toolbar.copyDrawControl('Rectangle',
                {
                    name: CROP_RECT_KEY,
                    block: 'custom',
                    title: 'Crops Map to Drawn Rectangle',
                    //actions: actions
                }
            );

            map.pm.Toolbar.copyDrawControl('Polygon',
                {
                    name: CROP_POLY_KEY,
                    block: 'custom',
                    title: 'Crops Map to Drawn Polygon',
                    //actions: actions
                }
            );

            map.pm.Toolbar.copyDrawControl('Marker',
                {
                    name: GPS_MARKER_KEY,
                    block: 'custom',
                    title: 'Sets the Latitude and Longitude',
                    continueDrawing: false,
                    className: 'leaflet-pm-toolbar control-fa-icon fa fa-circle-thin label-green', // This can be a css class name with icon.
                    layer: markerLayer,
                    toggle: false,
                    onClick: (e) => {
                        console.log("onClick!");
                        console.log(e);

                        // LOL.
                        mapObj.inReverseGeocodeMode = true; 
                    },
                }
            );

            //map.pm.Toolbar.createCustomControl({
            //    name: GPS_MARKER_KEY,
            //    block: 'custom',
            //    title: 'Attempt to reverse geocode location',
            //    onClick: (e) => {
            //        console.log("onClick!");
            //        console.log(e);
            //    },
            //    afterClick: (e) => {
            //        console.log("afterClick!");
            //        console.log(e);
            //    },
            //    toggle: false
            //});
        }

        /**
         * Adds a custom callback to a map which will be fired whenever the maps 'create' callback is fired 
         * (e.g. when drawing a shape using Geoman).
         */
        function addOnCreateCallback(map, callback) {
            map.on('pm:create', callback);
        }

        /// Initializes the 'bigImage' plugin (For taking snapshots).
        function initializeBigImage(map) {
            // BigImage func.
            var bigImage = L.control.bigImage({ position: 'topright' }).addTo(map);
            return bigImage;
        }

        // Takes a snapshot of the current position of the map and returns the
        // raw data.
        // ! The snapshot function will ONLY WORK if the display property
        // of the map is not 'none'. I.e. the map must be rendered by the DOM
        // (but not necessarily to the user).
        function takePartialSnapshot(bigImage, scale, bounds) {

            return new Promise(function (resolve, reject) {

                bigImage._print(scale, bounds).then(function (response) {
                    resolve(response);
                });
            });
        }

        // Takes a screenshot of the entire map. Positions the map based on boundary data if present.
        // Map must be present in DOM (but may have visibility set to 'hidden' - display CANNOT be 'none' though)
        function takeFullscreenSnapshot(domMapElem, coords, boundary, mapType, cropToBoundary, zoom) {

            domMapElem.style.display = "block";
            domMapElem.style.visibility = "hidden"; // Turns out this doesn't need to be visible

            const { success, rMap, rOverlay, rMarkerLayer } = initializeMap(domMapElem, {
                lat: coords.lat,
                lng: coords.lng
            }, mapType);
            
            if(success === false) {
                throw "Problem initializing map within takeFullscreenSnapshot...";
            }
                

            var tempMap = rMap;

            let mapObj = {
                map: rMap,
                geoOverlay: rOverlay,
                markerLayer: rMarkerLayer
            };

            var tempBigImage = initializeBigImage(tempMap);

            // Note: We are drawing and zooming on our TEMP map that is discarded after the screenshot.
            drawBoundaryAndZoomIfExists(tempMap, coords, boundary, cropToBoundary, zoom);

            // Place our marker on the temp map.
            L.marker([coords.lat, coords.lng], { fillColor: 'green', })
                .addTo(tempMap);

            tempMap.invalidateSize();

                return new Promise(function (resolve, reject) {

                    setTimeout(function () {
                    tempMap.invalidateSize();

                    // Scale 1, no pixel bounds (fullscreen)
                    tempBigImage._print(1, null, false).then(function (response) {

                        domMapElem.style.display = "none";
                        domMapElem.style.visibility = "hidden";

                        // Remove temp map data so it can be re-created in future if needed without errors.
                        try {
                            tempMap.off();
                            tempMap.remove();
                        }
                        catch (e) {
                            // No ill effects have been noticed by this, so just continue as normal.
                            // console.log("Map element used in multiple places, continuing...")
                        }

                        resolve(response);
                    });

                    }, 500);
                });
        }

        // Attempts to draw the addresses geometry boundary to the given map IF geometry data
        // exists and then zoom to a level fitting that geometry.
        // If the geometry does NOT exist, it doesn't try to render anything an instead goes to a safe level of zoom.
        function drawBoundaryAndZoomIfExists(theMap, coords, boundary, cropToBoundary, zoom) {

            if (boundary == null || boundary.length == 0) {
                // Skip drawing any boundary since we don't have it, and go to a safe level of zoom
                theMap.setView([coords.lat, coords.lng], zoom);
            } else {
                
                var polyBoundary = drawBoundary(theMap, boundary);

                if(cropToBoundary)
                    zoomToBoundary(theMap, boundary);
                else
                    theMap.setView([coords.lat, coords.lng], zoom);
            }
        }

        // Convert from whatever format our stored coords are in to proper lat/lng
        // Here we have to 'unproject' from the EPSG3857 coord system to the EPSG4326 coordinate system.
        // The EPSG4326 coord system is the default used by Leaflet unless you specify otherwise when
        // creating the map.
        // NOTE: The geometry from our Local DB and the SLIP REST API are in different formats (currently)
        // so if we ever switch back to using the REST API we will have to modify this back to how it was (See git changes)
        function unprojectBoundary(boundary) {

            if (boundary == null) {
                return null;
            }

            var unprojectedCoords = [];

            for (var i = 0; i < boundary.length; i++) {

                var e = L.point(boundary[i][0], boundary[i][1])
                var ee = L.CRS.EPSG3857.unproject(e);
                unprojectedCoords.push(ee);
            }

            return unprojectedCoords;
        }

        // Draws the given boundary on the map.
        function drawBoundary(theMap, boundary) {

            if (boundary == null || theMap == null) {
                return null;
            }

            var unprojectedCoords = boundary;
            var polyBoundary = L.polygon(unprojectedCoords, {
                color: '#00cc00',
                fillColor: '#00cc00',
                fillOpacity: '0.1',
                weight: 2,          // Boundary width in pixels
            }).addTo(theMap);

            return polyBoundary;
        }

        function zoomToBoundary(theMap, unprojectedBoundary) {

            if (unprojectBoundary == null || theMap == null) {
                return null;
            }

            var polyBoundary = L.polygon(unprojectedBoundary, {
                color: '#00cc00',
                fillColor: '#00cc00',
                fillOpacity: '0.1',
                weight: 2,          // Boundary width in pixels
            });

            theMap.fitBounds(polyBoundary.getBounds(),{ padding: [375, 125] });
        }

        /** Erases any existing polygon layers on the given map. */
        function clearPolygonLayers(theMap) {

            if (theMap == null) {
                return null;
            }

            theMap.eachLayer((layer) => {

                // Mega dodgy hack - How else do we check for whether the layer is a polygon-only layer?
                if (layer._rings != null)
                    layer.remove();
            });
        }

        function clearExistingMarkers(mapObj) {

            let map = mapObj.map;
            let markerLayer = mapObj.markerLayer;

            console.log(map);

            if (map == null) {
                return null;
            }

            markerLayer.clearLayers();

            map.eachLayer((layer) => {

                if (layer.id == MARKER_KEYS.GPS || layer.alt == MARKER_KEYS.AUTO) {
                    layer.remove();
                }else if (layer._drawnByGeoman == true) {
                    layer.remove();
                }
                    
            });
        }

        /**
         * 
         * @param  { { lat, lng } } coords
         * @return {Promise<Boolean>}
         */
        async function isDataAvailableAtCoordinates(coords) {
            try {

                const response = await $http({
                    url: baseUrl + "IsDataAvailableAtCoordinates",
                    params: { ...coords },
                    method: 'GET',
                });
                
                if (response.status === 200)
                {
                    return response.data;
                }
                log.logError("Status: " + response.status + ". MetroMap data is NOT currently available at the given coordinates!" , "", null, true);
                return false;
                
            } catch (e) {
                
                log.logError("Error checking if MetroMap data is available at the given coordinates!", e, null, true);
                throw e;
                
            }
        }
        
        return service;
    }
})();

