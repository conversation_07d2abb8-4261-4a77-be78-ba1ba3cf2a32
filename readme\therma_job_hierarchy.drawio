<mxfile host="app.diagrams.net" modified="2023-04-26T15:17:45.803Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="-x-FXt_HRo4aw2rUsexM" version="21.2.2" type="device">
  <diagram name="Page-1" id="qMvRRRU4bAu31b4cJCkl">
    <mxGraphModel dx="1207" dy="819" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="htk6H2RphT7sqIMyPjtq-2" value="" style="rounded=1;whiteSpace=wrap;html=1;arcSize=6;" vertex="1" parent="1">
          <mxGeometry x="110" y="360" width="240" height="200" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-3" value="Job" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="325" y="130" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.696;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="htk6H2RphT7sqIMyPjtq-7" target="htk6H2RphT7sqIMyPjtq-11">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="365" y="275" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="htk6H2RphT7sqIMyPjtq-7" target="htk6H2RphT7sqIMyPjtq-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="htk6H2RphT7sqIMyPjtq-7" target="htk6H2RphT7sqIMyPjtq-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-7" value="AssessmentComplianceOption (1...n)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="250" width="230" height="50" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-8" value="AssessmentComplianceBuilding&lt;br style=&quot;font-size: 12px;&quot;&gt;(Proposed Building)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="125" y="380" width="210" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-9" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;Job Hierarchy&lt;/font&gt;" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="49" y="20" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="htk6H2RphT7sqIMyPjtq-11" target="htk6H2RphT7sqIMyPjtq-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-11" value="Assessment (1...n)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="180" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="htk6H2RphT7sqIMyPjtq-13" target="htk6H2RphT7sqIMyPjtq-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-13" value="AssessmentProjectDetail (1-1)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="465" y="180" width="175" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-14" value="" style="rounded=1;whiteSpace=wrap;html=1;arcSize=6;" vertex="1" parent="1">
          <mxGeometry x="380" y="360" width="240" height="200" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-15" value="(stored as json)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="htk6H2RphT7sqIMyPjtq-14">
          <mxGeometry x="120" y="70" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-16" value="Zones&lt;br&gt;Spaces&lt;br&gt;Roofs&lt;br&gt;&lt;br&gt;Surfaces&lt;br&gt;Openings&lt;br&gt;&lt;br&gt;etc" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="430" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-17" value="AssessmentComplianceBuilding&lt;br style=&quot;font-size: 12px;&quot;&gt;(Reference Building)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="395" y="380" width="210" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-18" value="Zones&lt;br&gt;Spaces&lt;br&gt;Roofs&lt;br&gt;&lt;br&gt;Surfaces&lt;br&gt;Openings&lt;br&gt;&lt;br&gt;etc" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="405" y="430" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-19" value="This hierarchy went through a period of high volatility during the creation of V2, with data being moved up and down the hierarchy &lt;br&gt;on a near daily basis at points. Combined with terminology changes just as frequent, the state of the code can be confusing at times." style="text;html=1;align=left;verticalAlign=top;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="49" y="60" width="730" height="40" as="geometry" />
        </mxCell>
        <mxCell id="htk6H2RphT7sqIMyPjtq-20" value="(stored as json)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="230" y="430" width="100" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
