(function () {
    // The errorDetailCtrl provides the behaviour behind a reusable form to allow viewing/updating.
    // This controller and its template (error-detail.html) are used in a normal view and a modal dialog box.
    'use strict';
    var controllerId = 'ErrorDetailCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', 'errorservice', '$stateParams', '$state', '$timeout', '$mdDialog', errorDetailController]);
function errorDetailController($rootScope, $scope, errorservice, $stateParams, $state, $timeout, $mdDialog) {
        // The model for this form 
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = false;
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.error = {
            isNew: true,
            isModified: false,
            isDeleted: false,
        };
        vm.hideActionBar = false;
        vm.isModal = $scope.modalInstance != null;
        vm.title = "Log Details";
        if (vm.viewMode == 'New') {
            vm.title = "New Error";
            vm.hideActionBar = true;
        }

        vm.errorId = $stateParams.errorId;

        // Make sure service has initialised before populating.
        vm.isBusy = true;
         if (vm.errorId != null) {
            // Existing Error so go get it from the server (need to make sure we always have the latest record).
            errorservice.getError(vm.errorId).then(function () {
                vm.error = errorservice.error();
                vm.error.allXml = vm.error.allXml.replace(/&#xD;&#xA;/g, "\n");
                vm.isBusy = false;
            });
        }
        else {
            // New Error - Set any required defaults
        }

        //Save error Information
        vm.saveError = function () {
            vm.isBusy = true;
            vm.error.isModified = true;
            errorservice.saveError(vm.error).then(function () {
                vm.error = errorservice.error();
                vm.isBusy = false;
                if ($scope.modalInstance) {
                    // For a modal we close the modal.
                    $mdDialog.hide(vm.error);
                }
                else {
                    if (vm.previousRoute != null) {
                         $state.go(vm.previousRoute);
                    }
                    else {
                         $state.go("error-list");
                    }
                }
            });

        };

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.cancel();;
            }
            else {
                if (vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("error-list");
                }
            }
        };

        //Delete error Information
        vm.delete = function () {
            vm.isBusy = true;
            var promise = errorservice.deleteError(vm.error);
            promise.then(function () {
                vm.isBusy = false;
                // Now exit the current Page/Modal
                if ($scope.modalInstance) {
                    // Close modal if it was a modal.
                    $mdDialog.hide(vm.error);
                }
                else {
                    $timeout(function () {
                        $state.go(vm.previousRoute);
                        }, 1000);
                }
            })
        };

        //Undo Delete error 
        vm.undoDelete = function () {
            vm.isBusy = true;
            var promise = errorservice.undoDeleteError(vm.error);
            promise.then(function () {
                vm.isBusy = false;
                // Now exit the current Page/Modal
                if ($scope.modalInstance) {
                    // Close modal if it was a modal.
                    $mdDialog.hide(vm.error);
                }
                else {
                    $timeout(function () {
                        $state.go(vm.previousRoute);
                        }, 1000);
                }
            })
        };
        vm.dteOpentimeUtc = false;
        vm.openDatetimeUtc = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();
            vm.dteOpentimeUtc = !vm.dteOpentimeUtc;
        }

    }
})();
