
<div class="widget">

    <div class="table-responsive-vertical shadow-z-1">
      <table class="table table-striped table-hover table-very-condensed table-data-centered"
             st-table="vm.standardModelList"
             st-table-filtered-list="exportList"
             st-global-search="vm.listFilter"
             st-pipe="vm.callServer">

        <!-- Headings -->
        <thead>
            <tr>
                <th style="width: 40px;">
                <div style="display: grid; justify-content: center;">
                <md-checkbox id="sm-allCheckbox"
                                style="margin: auto; text-align: center; width: 0;"
                                ng-model="vm.bulkStatus.selectAllCheckboxState"
                                md-indeterminate="vm.bulkStatus.isIndeterminate"
                                ng-click="vm.selectAllCheckboxes(vm.standardModelList, vm.bulkStatus.selectAllCheckboxState, vm.bulkStatus)">
                </md-checkbox>
                </div>
                </th>
                <th class="clickable" style="user-select:none" st-sort="title">Title</th>
                <th class="clickable" style="user-select:none" st-sort="variationsCount">Design Variations</th>
                <th>Active</th>
                <th>3D Model</th>
                <th>Cost Estimate</th>
                <th>Design Insights</th>
                <th>Floor Plan</th>
                <th>Design Option</th>
                <th>Facade</th>
                <th>Specification</th>
                <th>Configuration</th>
                <th class="clickable" style="user-select:none" st-sort="createdOn">Date Created</th>
                <th class="clickable" style="user-select:none" st-sort="modifiedOn">Date Modified</th>
                <th style="width: 50px;"></th>
            </tr>
        </thead>

        <!-- Data -->
        <tbody>
            <tr ng-repeat="row in vm.standardModelList track by $index" class="list-row">

                <!-- Checkbox -->
                <td>
                    <div style="display: grid; justify-content: center;">
                        <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                     ng-model="row.checkboxSelected"
                                     ng-change="vm.updateBulkSelectStatus(vm.standardModelList, vm.bulkStatus);">
                        </md-checkbox>
                    </div>
                </td>

                <!-- Title -->
                <td data-title="Title" class="text-left clickable" style="position:relative;" ng-click="vm.rowClickCallback({modelId: row.standardHomeModelId})">
                    <div style="width:100%; padding-left:10px; padding-right: 40px; box-sizing:border-box; text-align:left;">
                        {{row.title}}
                        <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                    </div>
                </td>
                <!-- Design Variations -->
                <td ng-click="vm.rowClickCallback({modelId: row.standardHomeModelId})" class="clickable">{{row.variationsCount}}</td>
                <!-- Active -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.isActive" ng-disabled="!vm.parentIsActive">
                        </md-switch>
                    </div>
                </td>
                <!-- 3D Model -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.view3dFloorPlans" ng-disabled="!vm.parent3dModel">
                        </md-switch>
                    </div>
                </td>
                <!-- Cost Estimate -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.costEstimateEnabled" ng-disabled="!vm.parentCostEstimate">
                        </md-switch>
                    </div>
                </td>
                <!-- Design Insights -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variableMetadata.designInsightsEnabled">
                        </md-switch>
                    </div>
                </td>
                <!-- Floor Plan -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variationOptionsSettings.floorplanIsActive" ng-disabled="!vm.projectEnergyLabsSettings.varCategoryFloorplanActive">
                        </md-switch>
                    </div>
                </td>
                <!-- Design Option -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variationOptionsSettings.designOptionIsActive" ng-disabled="!vm.projectEnergyLabsSettings.varCategoryDesignOptionActive">
                        </md-switch>
                    </div>
                </td>
                <!-- Facade -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variationOptionsSettings.facadeIsActive" ng-disabled="!vm.projectEnergyLabsSettings.varCategoryFacadeActive">
                        </md-switch>
                    </div>
                </td>
                <!-- Specification -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variationOptionsSettings.specificationIsActive" ng-disabled="!vm.projectEnergyLabsSettings.varCategorySpecificationActive">
                        </md-switch>
                    </div>
                </td>
                <!-- Configuration -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="row.variationOptionsSettings.configurationIsActive" ng-disabled="!vm.projectEnergyLabsSettings.varCategoryConfigurationActive">
                        </md-switch>
                    </div>
                </td>
                <!-- Date Created -->
                <td ng-click="vm.rowClickCallback({modelId: row.standardHomeModelId})" class="clickable">{{row.createdOn != null ? (row.createdOn | date: 'dd/MM/yyyy') : '-'}}</td>
                <!-- Date Modified -->
                <td ng-click="vm.rowClickCallback({modelId: row.standardHomeModelId})" class="clickable">{{row.modifiedOn != null ? (row.modifiedOn | date: 'dd/MM/yyyy') : '-'}}</td>
                <!-- Menu -->
                <td>
                    <div style="display: flex; justify-content: center; align-items: center;">
                        <md-menu ng-show="!vm.disabled">
                            <!-- Initial '...' button, which launches options -->
                            <img md-menu-origin
                                    class="clickable"
                                    ng-click="$mdOpenMenu()"
                                    src="/content/feather/more-horizontal.svg"/>
                            <md-menu-content>
                                <!-- Copy to Project -->
                                <md-menu-item><md-button ng-click="vm.copyToProject(row)">
                                    Copy to Project
                                </md-button></md-menu-item>
                                <!-- Duplicate -->
                                <md-menu-item><md-button ng-click="vm.clone(row)">
                                    Duplicate
                                </md-button></md-menu-item>
                                <!-- Move Up -->
                                <md-menu-item ng-show="vm.sortField == null && $index > 0"><md-button ng-click="vm.moveModelUp(row)">
                                    Move Up
                                </md-button></md-menu-item>
                                <!-- Move Down -->
                                <md-menu-item ng-show="vm.sortField == null && $index < vm.standardModelList.length-1"><md-button ng-click="vm.moveModelDown(row)">
                                    Move Down
                                </md-button></md-menu-item>
                                <!-- Divider -->
                                <md-menu-divider></md-menu-divider>
                                <!-- Delete -->
                                <md-menu-item><md-button ng-click="vm.delete(row)">
                                    <span style="color: orangered">Delete</span>
                                </md-button></md-menu-item>

                            </md-menu-content>
                        </md-menu>
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="9999" class="text-center">
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; background-color: rgb(250,250,250);">

              <!-- Just empty. -->
              <div></div>

              <!-- Pagination Display -->
              <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

            </div>
          </td>
        </tr>
        </tfoot>
      </table>
      <div class="widget-pager" style="text-align: center;">
        <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
      </div>

      <div style="display: flex; justify-content: space-between; align-items: center;">

        <!-- 'Bulk Edit' button w/ Popup -->
        <md-menu>

          <!-- Initial bulk edit button, which launches options -->
          <md-button class="md-raised md-primary"
                     ng-click="vm.launchBulkEditModal()"
                     ng-disabled="vm.disabledEx() || !(vm.bulkStatus.selectAllCheckboxState || vm.bulkStatus.isIndeterminate)">
            BULK EDIT
          </md-button>

          <md-menu-content>

            <!-- Duplicate Element Button -->
            <md-menu-item>
              <md-button ng-click="vm.bulkCopyModels()">
                Duplicate
              </md-button>
            </md-menu-item>

            <md-menu-divider></md-menu-divider>

            <!-- Delete Element Button -->
            <md-menu-item>
              <md-button ng-click="vm.bulkDeleteModels()">
                <span style="color: orangered;">Delete</span>
              </md-button>
            </md-menu-item>

          </md-menu-content>
        </md-menu>

        <md-button class="md-raised md-primary"
                   redi-enable-roles="settings__settings__create"
                   ng-show="!vm.disabled"
                   style="margin: 2rem 1.5rem;"
                   ng-click="vm.createStandardModel()">
          Add HOME DESIGN
        </md-button>
      </div>

      <div>

      </div>
    </div>
    <div class="widget-foot">
      <div class="clearfix"></div>
    </div>
  </div>

<style>

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>
