﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('userName', ['security', function (security) {
        return {
            restrict: 'A',
            link: function ($scope, element, attrs) {
                var userName = security.currentUser.userFullName;
                $scope.user = security.currentUser;
                $scope.$watch('user', function (user) {
                    userName = user.userFullName;
                    updateHtml();
                }, true);

                function updateHtml() {
                    element.html(userName);
                }
            }
        }
    }]);
})();