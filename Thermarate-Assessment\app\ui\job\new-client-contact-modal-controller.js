// Name is a misnomer. This is the NEW CLIENT modal, not the "New Client 'Contact'" modal (Which does not exist)
(function () {
    'use strict';
    var controllerId = 'NewClientContactCtrl';
    angular.module('app')
        .controller(controllerId, ['$q', '$scope', '$mdDialog', 'nominatedbuildingsurveyorservice', 'userservice', 'common',
            'compliancemethodservice', 'assessmentsoftwareservice', 'certificationservice', 'worksdescriptionservice', 'clientservice', newClientContactModalController]);
    function newClientContactModalController($q, $scope, $mdDialog, nominatedbuildingsurveyorservice, userservice, common,
        compliancemethodservice, assessmentsoftwareservice, certificationservice, worksdescriptionservice, clientservice) {

        var vm = this;
        vm.user = {};

        vm.client = {
            clientOptions: {
                heatingAndCoolingRulesetCode: "Inherit",
                showHERDrawingsInReport: false,
                complianceCostEnabled: false,
                ncc2022ThermPerfEnabled: false,
                ncc2022WohEnabled: false,
                analyticsEnabled: false
            },
            clientDefault: {}
        };
        vm.clientOptionsData = {};

        vm.purchaseOrderList = clientservice.purchaseOrderSettingsList;

        vm.response = {
            clientName: null,
            accountsFirstName: null,
            accountsLastName: null,
            accountsPhone: null,
            accountsEmailAddress: null,
            accountsNote: null,
            accountsSameAsContact: false,
            user: {},
            purchaseOrderCode: 'NotRequired'
        };

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.accountsMatchContactChange = function () {
            if (vm.response.accountsSameAsContact) {
                vm.response.accountsFirstName = vm.response.user.firstName;
                vm.response.accountsLastName = vm.response.user.lastName;
                vm.response.accountsPhone = vm.response.user.phone;
                vm.response.accountsEmailAddress = vm.response.user.userName;
            }
        } 

        vm.submitSelection = async function () {

            mergeSplitValueOptions();

            // Add final clientOptions to response.
            vm.response.clientOptions = vm.client.clientOptions;

            if (
                vm.response.clientName != null &&
                vm.response.user.firstName != null &&
                vm.response.user.lastName != null &&
                (!vm.response.user.enableClientPortalAccess || vm.response.user.userName != null)
            ) {
                let userNameTakenCheck = await userservice.userNameAvailable(vm.response.user.userName);
                if (userNameTakenCheck) {
                    common.logger.logError("This Email Address is taken.", null, null, true);
                } else {
                    $mdDialog.hide(vm.response);
                }
            }
        };

        /** 
         * Takes our split values (which are an array) and converts them into
         * a CSV string. If we don't do this step the data will be malformed for
         * the actual Client.Create controller route.
         */
        function mergeSplitValueOptions() {

            for (var k = 0; k < vm.client.clientOptions.length; k++) {
                var option = vm.client.clientOptions[k];
                option.values = "";
                for (var ii = 0; ii < option?.splitValues?.length; ii++) {
                    if (ii > 0) {
                        option.values += ",";
                    }
                    option.values += option.splitValues[ii];
                }
            }
        }

        // Load Async data required for client settings.
        vm.nominatedBuildingSurveyorList = [];
        var nominatedBuildingSurveyorPromise = nominatedbuildingsurveyorservice.getList()
            .then(function (data) {
                vm.nominatedBuildingSurveyorList = data;
            });

        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
            });

        vm.assessmentSoftwareList = [];
        var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
            .then(function (data) {
                vm.assessmentSoftwareList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.certificationList = data.data;
            });

        // Select Item Changes
        vm.minHouseEnergyRatingSearchText = "";
        vm.minHouseEnergyRatingChoices = [];
        for (var i = 0; i <= 10; i = i + 0.1) {
            vm.minHouseEnergyRatingChoices.push({ val: i.toFixed(1), description: i.toFixed(1).toString() });
        }

        vm.worksDescriptionList = [];
        var worksDescriptionPromise = worksdescriptionservice.getList()
          .then(function (data) {
              vm.worksDescriptionList = data;
              console.log(data);
          });

        //Wait for the required dropdown lists to set so the client options can filter them
        $q.all([complianceMethodPromise, assessmentSoftwarePromise, nominatedBuildingSurveyorPromise,
            certificationPromise, worksDescriptionPromise]).then(function () {

            // We set a small timeout to ensure there is no race condition between this firing and
            // our .then's firing on the individual promises.
            setTimeout(() => {
                vm.clientOptionsData = {
                    nominatedBuildingSurveyorList: vm.nominatedBuildingSurveyorList,
                    complianceMethodList: vm.complianceMethodList,
                    assessmentSoftwareList: vm.assessmentSoftwareList,
                    minHouseEnergyRatingChoices: vm.minHouseEnergyRatingChoices,
                    certificationList: vm.certificationList,
                    worksDescriptionList: vm.worksDescriptionList
                }
            }, 100);
        });
    }
})();