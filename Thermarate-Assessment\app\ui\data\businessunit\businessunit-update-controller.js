(function () {
    // The BusinessunitUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'BusinessunitUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'businessunittypeservice',
        'userservice', 'stateservice', 'businessunitservice', 'security', businessunitUpdateController]);
    function businessunitUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  businessunittypeservice,
                                          userservice, stateservice, businessunitservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = false;
        var eventListenerList = [];
        vm.title = 'Edit Business Unit';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.businessUnitId = null;
        vm.businessUnit = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;

        vm.createPermission = securityservice.immediateCheckRoles('admin__businessunit__create');
        vm.editPermission = securityservice.immediateCheckRoles('admin__businessunit__edit');
        vm.deletePermission = securityservice.immediateCheckRoles('admin__businessunit__delete');

        if (vm.newRecord) {
            vm.title = "New Business Unit";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.businessUnitId = $scope.businessUnitId;
            }
            vm.hideActionBar = true;
        } else {
            vm.businessUnitId = $stateParams.businessUnitId;
        }

        // Get data for object to display on page
        var businessUnitIdPromise = null;
        if (vm.businessUnitId != null) {
            businessUnitIdPromise = businessunitservice.getBusinessUnit(vm.businessUnitId)
            .then(function (data) {
                if (data != null) {
                    vm.businessUnit = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.businessUnitTypeList = [];
        var businessUnitTypePromise = businessunittypeservice.getList()
            .then(function(data){
                vm.businessUnitTypeList = data.data;
            });
        vm.stateList = [];
        var statePromise = stateservice.getList()
            .then(function(data){
                vm.stateList = data.data;
            });

        // Functions to get data for Typeahead
        vm.getemployees = function(searchTerm) {
            var filter = [
                { field: "fullName", operator: "startswith", value: searchTerm, logic: "and" },
                { field: "isExternal", operator: "eq", value: false, valueType: "boolean" }
            ];
            return userservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateEmployee', function(event){
            event.stopPropagation();
            vm.createEmployee() // function to launch add modal;
            }));

        vm.createEmployee = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("businessunit-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                businessunitservice.createBusinessUnit(vm.businessUnit).then(function(data){
                    vm.businessUnit = data;
                    vm.businessUnitId = vm.businessUnit.businessUnitId;
                    vm.cancel();
                });
            }else{
                businessunitservice.updateBusinessUnit(vm.businessUnit).then(function(data){
                    if (data != null) {
                        vm.businessUnit = data;
                        vm.businessUnitId = vm.businessUnit.businessUnitId;
                    }
                });
            }
        }

        vm.delete = function () {
            businessunitservice.deleteBusinessUnit(vm.businessUnitId).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            businessunitservice.undoDeleteBusinessUnit(vm.businessUnitId).then(function () {
                vm.cancel();
            });
        }

    }
})();