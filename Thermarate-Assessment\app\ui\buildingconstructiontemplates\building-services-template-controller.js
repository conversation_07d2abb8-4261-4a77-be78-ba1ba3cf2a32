(function () {
    // The BuildingServicesTemplateCtrl supports a list page.
    'use strict';
    var controllerId = 'BuildingServicesTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',
            'buildingservicestemplateservice', 'servicetemplateservice', 'security', buildingServicesTemplateController]);
    function buildingServicesTemplateController($rootScope, $scope, $mdDialog, $stateParams, $state,
        buildingservicestemplateservice, servicetemplateservice, securityservice) {

        // The model for this form 
        var vm = this;
        vm.isBusy = true; 
        vm.title = '';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.newRecord = $scope.newRecord != undefined && $scope.newRecord == true;

        vm.buildingServicesTemplateId = $stateParams.templateId;

        vm.editPermission = securityservice.immediateCheckRoles('admin__template__edit');
        vm.deletePermission = securityservice.immediateCheckRoles('admin__template__delete');

        function initialize() {

            if (vm.newRecord)
                vm.title = "New Building Services Template";

            // Get data for object to display on page
            var buildingServicesTemplateIdPromise = null;
            if (vm.buildingServicesTemplateId != null) {
                buildingServicesTemplateIdPromise = buildingservicestemplateservice
                    .getTemplate(vm.buildingServicesTemplateId)
                    .then(function (data) {

                        if (data != null) {
                            vm.template = data;
                            vm.title = "Edit Building Services Template";
                            vm.subheading = "Building Services";

                            servicetemplateservice.getServiceCategories()
                                .then((data) => {
                                    vm.serviceCategories = data;

                                });
                        }

                        vm.isBusy = false;
                    })
            }
            else {
                vm.isBusy = false;
            }
        }

        initialize();

        // Cancel - Route back to last page.
        vm.cancel = function () {
            $state.go("buildingservicestemplate-listform");
        }

        vm.save = function () {

            vm.isBusy = true;

            if(vm.newRecord == true) {

                buildingservicestemplateservice.createTemplate(vm.template).then(function(data){
                    vm.buildingServicesTemplateId = vm.template.buildingServicesTemplateId;
                    vm.isBusy = false;
                });

            } else {

                buildingservicestemplateservice.updateTemplate(vm.template).then(function(data){
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            buildingservicestemplateservice.deleteTemplate(vm.buildingServicesTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            buildingservicestemplateservice.undoDeleteTemplate(vm.buildingServicesTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }
    }
})();