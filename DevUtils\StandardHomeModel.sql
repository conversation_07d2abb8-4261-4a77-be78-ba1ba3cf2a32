USE [thermarate];

SELECT [homeModel].[StandardHomeModelId]
      ,[homeModel].[ProjectId]
      ,[project].[ProjectName] [__Project]
      ,[homeModel].[ClientId]
      ,[client].[ClientName] [__Client]
      ,[homeModel].[Title]
      ,[homeModel].[Description]
      ,[homeModel].[Category]
      ,[homeModel].[MinLotWidth]
      ,[homeModel].[Width]
      ,[homeModel].[FloorArea]
      ,[homeModel].[Storeys]
      ,[homeModel].[NumberOfBedrooms]
      ,[homeModel].[NumberOfBathrooms]
      ,[homeModel].[NumberOfGarageSpots]
      ,[homeModel].[NatHERSClimateZone]
      ,[homeModel].[NorthOffset]
      ,[homeModel].[BlockType]
      ,[homeModel].[VariableOptionsJson]
      ,[homeModel].[Deleted]
      ,[homeModel].[CreatedOn]
      ,[homeModel].[CreatedByName]
      ,[homeModel].[ModifiedOn]
      ,[homeModel].[ModifiedByName]
      ,[homeModel].[FeaturesMasterSuiteAtFront]
      ,[homeModel].[FeaturesMasterSuiteAtRear]
      ,[homeModel].[FeaturesMasterSuiteGroundFloor]
      ,[homeModel].[FeaturesMasterSuiteFirstFloor]
      ,[homeModel].[FeaturesHomeTheatre]
      ,[homeModel].[FeaturesActivity]
      ,[homeModel].[FeaturesGames]
      ,[homeModel].[FeaturesHomeOffice]
      ,[homeModel].[FeaturesScullery]
      ,[homeModel].[FeaturesOutdoorLiving]
      ,[homeModel].[FeaturesRHGarage]
      ,[homeModel].[FeaturesLHGarage]
      ,[homeModel].[FeaturesRearCarAccess]
      ,[homeModel].[IsActive]
      ,[homeModel].[NccBuildingClassification]
      ,[homeModel].[LowestLivingAreaFloorType]
      ,[homeModel].[HabitableRoomFloorAreaM2]
      ,[homeModel].[CostEstimateEnabled]
      ,[homeModel].[CostEstimateDataJson]
      ,[homeModel].[VariableMetadataJson]
      ,[homeModel].[LivingAreas]
      ,[homeModel].[Depth]
      ,[homeModel].[HouseArea]
      ,[homeModel].[CarParkingArea]
      ,[homeModel].[OutdoorAlfrescoArea]
      ,[homeModel].[FeaturesKitchenLivingDiningGroundFloor]
      ,[homeModel].[FeaturesKitchenLivingDiningUpperFloor]
      ,[homeModel].[FeaturesKitchenLivingDiningRear]
      ,[homeModel].[FeaturesKitchenLivingDiningFront]
      ,[homeModel].[FeaturesWalkInPantry]
      ,[homeModel].[FeaturesSecondLiving]
      ,[homeModel].[FeaturesRetreat]
      ,[homeModel].[FeaturesCarport]
      ,[homeModel].[FeaturesGarage]
      ,[homeModel].[FeaturesRearAccess]
      ,[homeModel].[HouseWidth]
      ,[homeModel].[HouseDepth]
      ,[homeModel].[GlassToHouseAreaRatio]
      ,[homeModel].[GlassToHousePerimiterRatio]
      ,[homeModel].[ConditionedFloorArea]
      ,[homeModel].[ConditionedNonHabitableFloorAreaRatio]
      ,[homeModel].[GlassToInternalFloorAreaRatio]
      ,[homeModel].[GlassToConditionedFloorAreaRatio]
      ,[homeModel].[GlassToExteriorWallRatio]
      ,[homeModel].[FrontElevationWallRatio]
      ,[homeModel].[RearElevationWallRatio]
      ,[homeModel].[LeftElevationWallRatio]
      ,[homeModel].[RightElevationWallRatio]
      ,[homeModel].[FeaturesRLOutdoorLiving]
      ,[homeModel].[FeaturesRROutdoorLiving]
      ,[homeModel].[FeaturesCourtyard]
      ,[homeModel].[FeaturesRCOutdoorLiving]
      ,[homeModel].[CategoryDisplayHome]
      ,[homeModel].[CategoryFarmhouse]
      ,[homeModel].[CategoryNarrowLot]
      ,[homeModel].[CategoryNewDesign]
      ,[homeModel].[CategorySingleStorey]
      ,[homeModel].[CategoryTwoStorey]
      ,[homeModel].[CategoryThreeStorey]
      ,[homeModel].[SiteCover]
      ,[homeModel].[WohFloorArea]
      ,[homeModel].[View3dFloorPlans]
      ,[homeModel].[FloorplannerLink]
      ,[homeModel].[CategoryGrannyFlat]
      ,[homeModel].[CategoryRearLoaded]
      ,[homeModel].[FeaturesDressingRoom]
      ,[homeModel].[FeaturesComputerNook]
      ,[homeModel].[FeaturesBalcony]
      ,[homeModel].[FeaturesVerandah]
      ,[homeModel].[FeaturesWorkshop]
      ,[homeModel].[FeaturesMultipurposeRoom]
      ,[homeModel].[DisplayFloorArea]
      ,[homeModel].[FloorAreaHabitableRooms]
      ,[homeModel].[HabitableRooms]
      ,[homeModel].[CategoryAcreage]
      ,[homeModel].[CategoryDualOccupancy]
      ,[homeModel].[CategoryDuplex]
      ,[homeModel].[CategorySplitLevel]
      ,[homeModel].[FeaturesKitchenLivingDiningMiddle]
      ,[homeModel].[FeaturesButlersPantry]
      ,[homeModel].[FeaturesMasterSuiteAtMiddle]
      ,[homeModel].[FeaturesHisHerWir]
      ,[homeModel].[FeaturesWalkInRobe]
      ,[homeModel].[FeaturesMedia]
      ,[homeModel].[FeaturesEntertainment]
      ,[homeModel].[FeaturesFamily]
      ,[homeModel].[FeaturesFormalLounge]
      ,[homeModel].[FeaturesLeisure]
      ,[homeModel].[FeaturesLounge]
      ,[homeModel].[FeaturesRumpus]
      ,[homeModel].[FeaturesENook]
      ,[homeModel].[FeaturesStudy]
      ,[homeModel].[FeaturesStudyNook]
      ,[homeModel].[FeaturesCellar]
      ,[homeModel].[FeaturesCloakRoom]
      ,[homeModel].[FeaturesGuestBedroom]
      ,[homeModel].[FeaturesGym]
      ,[homeModel].[FeaturesMudRoom]
      ,[homeModel].[FeaturesNannysQuarters]
      ,[homeModel].[FeaturesPowderRoom]
      ,[homeModel].[FeaturesStoreRoom]
      ,[homeModel].[FeaturesWalkInLinen]
      ,[homeModel].[FeaturesLHCarport]
      ,[homeModel].[FeaturesRHCarport]
      ,[homeModel].[FeaturesRearLoaded]
      ,[homeModel].[FeaturesAlfresco]
      ,[homeModel].[FeaturesMLAlfresco]
      ,[homeModel].[FeaturesMRAlfresco]
      ,[homeModel].[FeaturesRCAlfresco]
      ,[homeModel].[FeaturesRLAlfresco]
      ,[homeModel].[FeaturesRRAlfresco]
      ,[homeModel].[FeaturesFrontBalcony]
      ,[homeModel].[FeaturesRearBalcony]
      ,[homeModel].[FeaturesLHCourtyard]
      ,[homeModel].[FeaturesRHCourtyard]
      ,[homeModel].[FeaturesMLOutdoorLiving]
      ,[homeModel].[FeaturesMROutdoorLiving]
      ,[homeModel].[FeaturesHomeCinema]
      ,[homeModel].[VariationOptionsSettingsJson]
      ,[homeModel].[IsVariationOfHomeModelId]
      ,[homeModel].[VariationFloorplanId]
      ,[homeModel].[VariationDesignOptionId]
      ,[homeModel].[VariationFacadeId]
      ,[homeModel].[VariationSpecificationId]
      ,[homeModel].[VariationConfigurationId]
      ,[homeModel].[IsDefaultVariation]
      ,[homeModel].[SortOrder]
      ,[homeModel].[DrawingAreasJson]
    --   ,[homeModel].[ZoneSummaryDataJson]
      ,[homeModel].[DisplayFloorAreaVarRefJson]
  FROM [dbo].[RSS_StandardHomeModel] [homeModel]
  LEFT JOIN [dbo].[RSS_Client] [client] on [homeModel].[ClientId] = [client].[ClientId]
  LEFT JOIN [dbo].[RSS_Project] [project] on [homeModel].[ProjectId] = [project].[ProjectId]
  WHERE 1=1
    AND [homeModel].[Deleted] = 0
    -- AND [homeModel].[StandardHomeModelId] = 'cb74b569-faf8-477c-9c72-736fff9dda7f'
    AND [homeModel].[IsVariationOfHomeModelId] IS NULL
    -- AND [homeModel].[IsVariationOfHomeModelId] = 'c509071c-16e3-45fe-91f1-8470d933bc68'
    AND [homeModel].[ProjectId] = '4fdc16f0-9e35-416a-aeaa-a715d0efaaa9'
    -- AND [homeModel].[ClientId] = 'B0139FEE-0D56-6D81-4442-3A0B3032EF11'
  ORDER BY [CreatedOn] DESC