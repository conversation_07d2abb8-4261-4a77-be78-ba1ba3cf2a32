(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelPerformance', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
        assessmentMethod: '<',
        heatingCoolingLoadLimits: '<',
        targetEnergyRating: '<',
      },
      templateUrl: 'app/ui/energy-labs/standard-model-performance-form.html',
      controller: StandardModelPerformanceController,
      controllerAs: 'vm'
    });

  StandardModelPerformanceController.$inject = ['common'];

  function StandardModelPerformanceController(common) {

    let vm = this;

    vm.determineHeatCoolResultColour = common.determineELHeatCoolResultColour;

  }

})();