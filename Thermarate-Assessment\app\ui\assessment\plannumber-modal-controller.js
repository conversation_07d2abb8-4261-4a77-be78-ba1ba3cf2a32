(function () {
    'use strict';
    var controllerId = 'PlanNumberModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'mapdataservice', planNumberModalController]);
    function planNumberModalController($scope, $mdDialog, mapdataservice) {
        var vm = this;
        vm.addresses = [];
        vm.showResults = false;

        if ($scope.planNumber != null) {
            vm.planNumber = $scope.planNumber;
        }

        vm.search = function () {
            vm.showResults = false;
            vm.addresses = [];
            mapdataservice.searchStreetsByDp(vm.planNumber)
            .then(function (data) {
                if (data.Results != null && data.Results.length > 0) {
                    for (var i = 0, len = data.Results.length; i < len; i++) {
                        var ret = mapdataservice.getPlanNumberFromField(data.Results[i].Value, data.Results[i].CustomFields.region);
                        if (ret.lot != "") {
                            data.Results[i].CustomFields.lot_number = ret.lot;
                        }
                    }

                    data.Results = _.sortBy(data.Results, function (rec) { return rec.CustomFields.lot_number + rec.CustomFields.route });
                }

                vm.addresses = data.Results;
                vm.showResults = true;
            });
        }

        vm.addressSelected = function (address) {
            close(address);
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        function close (result) {
            $mdDialog.hide(result);
        };

    }
})();