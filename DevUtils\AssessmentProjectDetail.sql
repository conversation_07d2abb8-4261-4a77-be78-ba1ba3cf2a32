USE [thermarate];

SELECT TOP 1000
       [detail].[AssessmentId]
      ,[detail].[Latitude]
      ,[detail].[Longitude]
      ,[detail].[LotTypeCode]
      ,[detail].[Prefix]
      ,[detail].[StrataLotNumber]
      ,[detail].[SurveyStrataLotNumber]
      ,[detail].[OriginalLotNumber]
      ,[detail].[LotNumber]
      ,[detail].[DepositedPlanNumber]
      ,[detail].[Volume]
      ,[detail].[Folio]
      ,[detail].[HouseNumber]
      ,[detail].[StreetName]
      ,[detail].[StreetType]
      ,[detail].[Suburb]
      ,[detail].[StateCode]
      ,[detail].[Postcode]
      ,[detail].[LocalGovernmentAuthority]
      ,[detail].[LocalGovernmentAuthorityShort]
      ,[detail].[CreatedOn]
      ,[detail].[CreatedByName]
      ,[detail].[ModifiedOn]
      ,[detail].[ModifiedByName]
      ,[detail].[Deleted]
      ,[detail].[ClientJobNumber]
      ,[detail].[ProjectOwner]
      ,[detail].[OrderDate]
      ,[detail].[AssessmentVersion]
      ,[detail].[OrderTypeCode]
      ,[detail].[FullAddress]
      ,[detail].[CompletedDate]
      ,[detail].[UseCustomAddress]
      ,[detail].[CustomDisplayAddress]
      ,[detail].[IsRecertified]
      ,[detail].[IsManual]
      ,[detail].[CoordinatesAreConfirmed]
      ,[detail].[MapImageFileId]
      ,[detail].[BoundaryGeometryJson]
      ,[detail].[SiteMapImageFileId]
      ,[detail].[PlanType]
      ,[detail].[CertificateOfTitle]
      ,[detail].[ParcelArea]
      ,[detail].[LotShape]
      ,[detail].[NorthSideFacing]
      ,[detail].[FacingDescription]
      ,[detail].[FacingWithinXMetres]
      ,[detail].[CornerBlock]
      ,[detail].[NominatedBuildingSurveyorId]
      ,[detail].[CreatorUserId]
      ,[detail].[BoundarySides]
      ,[detail].[ClimateChartDataJson]
      ,[detail].[LotDescription]
      ,[detail].[RearLaneway]
      ,[detail].[RuralLot]
      ,[detail].[LotWidth]
      ,[detail].[NarrowLot]
      ,[detail].[NarrowLotIsManuallyOverridden]
      ,[detail].[ClientAssigneeUserId]
      ,[detail].[LotLength]
      ,[detail].[UseCustomClientName]
      ,[detail].[CustomClientName]
      ,[detail].[ComplianceCost]
  FROM [dbo].[RSS_AssessmentProjectDetail] [detail]
  INNER JOIN [dbo].[RSS_Assessment] [assessment] ON [detail].[AssessmentId] = [assessment].[AssessmentId]
  INNER JOIN [dbo].[RSS_Job] [job] ON [assessment].[JobId] = [job].[JobId]
  INNER JOIN [dbo].[RSS_Client] [client] ON [job].[ClientId] = [client].[ClientId]
  WHERE 1=1
    -- AND [detail].[AssessmentId] = '99c4940b-82e4-4338-b8ce-452ca322c5d1'
    --AND [client].[ClientId] = 'A11B5AB6-2E8B-285B-E98C-3A0706E55047'
    -- AND [Volume] IS NOT NULL
  ORDER BY [CreatedOn] DESC

--   UPDATE [dbo].[RSS_AssessmentProjectDetail] SET [LocalGovernmentAuthorityShort] = 'Bayswater' WHERE [AssessmentId] = '41adf56b-f56e-42cb-8c9a-2f55af54fb8b'