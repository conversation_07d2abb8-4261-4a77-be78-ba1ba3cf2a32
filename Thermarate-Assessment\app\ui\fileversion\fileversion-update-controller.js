(function () {
    // The FileversionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'FileversionUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'fileservice', 'fileversionservice', fileversionUpdateController]);
function fileversionUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  fileservice, fileversionservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit File Version';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.versionNo = null;
        vm.fileversion = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New File Version";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.versionNo = $scope.versionNo;
            }
            vm.hideActionBar = true;
        } else {
            vm.versionNo = $stateParams.versionNo;
        }

        // Get data for object to display on page
        var versionNoPromise = null;
        if (vm.versionNo != null) {
            versionNoPromise = fileversionservice.getFileVersion(vm.versionNo)
            .then(function (data) {
                if (data != null) {
                    vm.fileversion = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead
        vm.getfiles = function(searchTerm) {
            var filter = [{ field: "displayName", operator: "startswith", value: searchTerm }];
            return fileservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateFile', function(event){
            event.stopPropagation();
            vm.createFile() // function to launch add modal;
            }));

        vm.createFile = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("fileversion-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                fileversionservice.createFileVersion(vm.fileversion).then(function(data){
                    vm.fileversion = data;
                    vm.versionNo = vm.fileversion.versionNo;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                fileversionservice.updateFileVersion(vm.fileversion).then(function(data){
                    if (data != null) {
                        vm.fileversion = data;
                        vm.versionNo = vm.fileversion.versionNo;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            fileversionservice.deleteFileVersion(vm.versionNo).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            fileversionservice.undoDeleteFileVersion(vm.versionNo).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();