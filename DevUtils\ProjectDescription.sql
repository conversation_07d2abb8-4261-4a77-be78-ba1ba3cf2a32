USE [thermarate];

SELECT TOP (1000) [ProjectDescriptionCode]
      ,[Description]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[Deleted]
      ,[SortOrder]
      ,[ProjectRatingModeId]
      ,[NumberOfStoreysWithGlazing]
      ,[StoreysJson]
      ,[DefaultForStoreyCount]
  FROM [dbo].[RSS_ProjectDescription]
  WHERE 1=1
    AND [Deleted] = 0
  ORDER BY [SortOrder]