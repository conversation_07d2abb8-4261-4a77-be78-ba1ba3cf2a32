<section id="client-list-view" class="main-content-wrapper" data-ng-controller="ClientListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
             data-quick-find-model='vm.listFilter'
             data-quick-find-holder="Search"
             data-action-buttons='vm.actionButtons'
             data-refresh-list='vm.refreshList()'
             data-spinner-busy='vm.isBusy'
             data-filter-options="vm.filterOptions"
             data-filter-changed="vm.refreshList(value)"
             data-current-filter="vm.currentFilter"
             data-query-builder-name="Client"
             data-query-builder-current="vm.currentQuery">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                   st-table="vm.clientList"
                   st-table-filtered-list="exportList"
                   st-global-search="vm.listFilter"
                   st-persist="clientList"
                   st-pipe="vm.callServer"
                   st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="clientName" class="can-sort text-left" style="white-space:nowrap;">Client Name</th>
                        <th st-sort="usersCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Users</th>
                        <th st-sort="jobsCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Jobs (Total)</th>
                        <th st-sort="jobsActiveCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Jobs (Active)</th>
                        <th align="left" class="action-col" style="width:80px; text-align:center; white-space:nowrap;">Client Portal</th>
                        <th class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Energy Lab</th>
                        <th st-sort="projectsActiveCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Projects (Active)</th>
                        <th st-sort="homeDesignsActiveCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Home Designs (Active)</th>
                        <th st-sort="homeDesignVariationsActiveCount" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Home Design Variations (Active)</th>
                        <th class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Favourite</th>
                        <th st-sort="createdOn" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Date Created</th>
                        <th st-sort="modifiedOn" class="can-sort" style="width:60px; text-align:center; white-space:nowrap;">Date Modified</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.clientList" class="list-row clickable">
                        <!-- Client Name -->
                        <td data-title="Client Name" ng-click="vm.goToClient(row.clientId)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{row.clientName}}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <!-- Users -->
                        <td data-title="Users" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.usersCount}}</td>
                        <!-- Jobs (Total) -->
                        <td data-title="Jobs (Total)" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.jobsCount}}</td>
                        <!-- Jobs (Active) -->
                        <td data-title="Jobs (Active)" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.jobsActiveCount}}</td>
                        <!-- Client Portal -->
                        <td data-title="Client Portal" ng-click="vm.goToClient(row.clientId)" style="text-align: center;">
                            <md-checkbox ng-checked="row.hasClientPortalAccess" disabled style="margin:auto;"/>
                        </td>
                        <!-- Energy Labs -->
                        <td data-title="Energy Labs" ng-click="vm.goToClient(row.clientId)" style="text-align: center;">
                            <md-checkbox ng-checked="row.energyLabsIsActive" disabled style="margin:auto;"/>
                        </td>
                        <!-- Projects (Active) -->
                        <td data-title="Projects (Active)" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.projectsActiveCount}}</td>
                        <!-- Home Designs (Active) -->
                        <td data-title="Home Designs (Active)" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.homeDesignsActiveCount}}</td>
                        <!-- Home Design Variations (Active) -->
                        <td data-title="Home Design Variations (Active)" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.homeDesignVariationsActiveCount}}</td>
                        <!-- Favourite -->
                        <td data-title="Favourite" s ng-click="vm.goToClient(row.clientId)"tyle="text-align: center;">
                            <md-checkbox ng-model="row.isFavourite"
                                         redi-enable-roles="admin__client__edit"
                                         style="margin:auto auto auto 25%;"
                                         ng-click="vm.setFavouriteStatus(row.clientId, !row.isFavourite);$event.stopPropagation();"/>
                        </td>
                        <!-- Date Created -->
                        <td data-title="Date Created" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.createdOn | date: 'dd/MM/yyyy'}}</td>
                        <!-- Date Modified -->
                        <td data-title="Date Modified" ng-click="vm.goToClient(row.clientId)" style="text-align:center;">{{::row.modifiedOn | date: 'dd/MM/yyyy'}}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>

    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>