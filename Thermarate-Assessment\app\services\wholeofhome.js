﻿// Service used to retreive weather data for specific NCC Climate Zones 
// (e.g.climate zone 8, climate zone 43, etc)
(function () {
  'use strict';

  const SERVICE_ID = 'wholeofhomeservice';

  angular.module('appservices').factory(SERVICE_ID,
    ['common', 'config', '$http', 'wholeofhomedataservice', 'servicetemplateservice', wholeofhome]);

  function wholeofhome(common, config, $http, wholeofhomedataservice, servicetemplateservice) {

    const log = common.logger;
    const baseUrl = config.servicesUrlPrefix + 'wholeofhome/';

    function calculateAreaFactor(wohFloorArea) {

      const match = WholeOfHomeConstants.areaAdjustmentFactors
        .find(row => wohFloorArea >= row.min && wohFloorArea < row.max);

      if (match == null)
        return 0;

      return match.factor;
    }

    function calculateEnergyFactor(state, climateZone, buildingClassification) {

      const match = WholeOfHomeConstants.energyFactors
        .find(x => x.state === state && x.climateZone === climateZone);

      if (buildingClassification.startsWith("Class 1"))
        return match.class1;
      else
        return match.class2;

    }

    function calculateSwimmingPoolPumpFactor(state, poolPumpEnergyRating) {
      const match = WholeOfHomeConstants.poolPumpFactors.find(x => x.rating === poolPumpEnergyRating);

      if (match == null)
        return 0;

      return match[state];
    }

    function calculateSwimmingPoolPumpEnergyUsage(volume, pumpFactor) {
      // Swimming Pool Pump Energy Usage (EP) is = Swimming Pool Volume (L) * ( FP / 1000 )
      return volume * (pumpFactor / 1000);
    }

    function calculateSpaPumpEnergyUsage(volume, pumpFactor) {
      // Spa Pump Energy Usage (ES) is = Spa Volume (L) * ( FS / 100 )
      return volume * (pumpFactor / 100);
    }

    function calculateSpaPumpFactor(state) {
      const match = WholeOfHomeConstants.spaPumpFactor[state];

      return match != null
        ? match
        : 0;
    }

    /** Note this is NOT the same as wholeofhomeDATAservice.getEfficiencyFactor - confusing */
    async function getEfficiencyFactor(stateCode,
                                       climateZone,
                                       heatingServiceCode,
                                       coolingServiceCode,
                                       gemsYear,
                                       heatingEnergyRating,
                                       coolingEnergyRating,
                                       hotWaterServiceCode) {

      if (hotWaterServiceCode == null)
        return null;

      const fullRow = await wholeofhomedataservice.getEfficiencyFactor(
        stateCode,
        climateZone,
        heatingServiceCode,
        coolingServiceCode,
        gemsYear,
        heatingEnergyRating,
        coolingEnergyRating
      );

      console.log("WoH EEF: ", fullRow);

      const conv = hotWaterServiceCode.charAt(0).toLowerCase() + hotWaterServiceCode.slice(1);
      const forWaterService = fullRow[conv];

      return forWaterService;

    }

    /** Calculates the 'allowed' and 'achieved' data from all incoming data */
    async function calculate({
                         swimmingPool,
                         spa,
                         photovoltaic,
                         spaceHeating,
                         spaceCooling,
                         waterHeating,
                         projectDetails
                       }) {

      if (swimmingPool.exists === false || swimmingPool.exists == null) {
        swimmingPool.volume = null;
        swimmingPool.gems2019Rating = null;
      }

      if (spa.exists === false || spa.exists == null) {
        spa.volume = null;
        spa.gems2019Rating = null;
      }

      if (photovoltaic.exists === false || photovoltaic.exists == null) {
        photovoltaic.capacity = null;
      }

      // If ANY required variable is null, we can't compute
      if (requiredDataIsMissing({
        swimmingPool,
        spa,
        photovoltaic,
        spaceHeating,
        spaceCooling,
        waterHeating,
        projectDetails
      })) {
        return { allowance: null, achieved: null }
      }

      console.log("Running calculations...");

      // Get mapped Service Type if required
      let spaceHeatingTypeCode = servicetemplateservice.getMappedServiceTypeCode(spaceHeating?.serviceType?.serviceTypeCode);
      let spaceCoolingTypeCode = servicetemplateservice.getMappedServiceTypeCode(spaceCooling?.serviceType?.serviceTypeCode);
      let waterHeatingTypeCode = servicetemplateservice.getMappedServiceTypeCode(waterHeating?.serviceType?.serviceTypeCode);

      const woheff = await getEfficiencyFactor(
        projectDetails?.stateCode,
        projectDetails?.nccClimateZone,
        spaceHeatingTypeCode,
        spaceCoolingTypeCode,
        2019,
        spaceHeating?.gems2019Rating || 0,
        spaceCooling?.gems2019Rating || 0,
        waterHeatingTypeCode,
      );

      const areaFactor = calculateAreaFactor(projectDetails.wohFloorArea);
      const adjustedArea = projectDetails.wohFloorArea * areaFactor;
      const energyFactor = calculateEnergyFactor(
        projectDetails.stateCode,
        projectDetails.nccClimateZone,
        projectDetails.nccBuildingClassification);

      let poolPumpEnergyUsage = 0;
      if (swimmingPool.exists && swimmingPool.gems2019Rating != null) {
        const poolPumpFactor = calculateSwimmingPoolPumpFactor(projectDetails.stateCode, swimmingPool.gems2019Rating);
        poolPumpEnergyUsage = calculateSwimmingPoolPumpEnergyUsage(swimmingPool.volume, poolPumpFactor);
      }

      let spaPumpEnergyUsage = 0;
      if (spa.exists && spa.gems2019Rating != null) {
        const spaPumpFactor = calculateSpaPumpFactor(projectDetails.stateCode);
        spaPumpEnergyUsage = calculateSpaPumpEnergyUsage(spa.volume, spaPumpFactor);
      }

      const allowance = calculateAllowance(adjustedArea, energyFactor);
      const achieved = calculateAchieved(
        adjustedArea,
        woheff,
        poolPumpEnergyUsage,
        spaPumpEnergyUsage,
        photovoltaic?.capacity);
      
      return { allowance, achieved };

    }

    function requiredDataIsMissing({
                                     swimmingPool,
                                     spa,
                                     photovoltaic,
                                     spaceHeating,
                                     spaceCooling,
                                     waterHeating,
                                     projectDetails
                                   }) {
      return (projectDetails.stateCode == null ||
        projectDetails.nccClimateZone == null ||
        spaceHeating?.serviceType?.serviceTypeCode == null ||
        spaceCooling?.serviceType?.serviceTypeCode == null ||
        (showEnergyRating(spaceHeating) && spaceHeating?.gems2019Rating == null) ||
        (showEnergyRating(spaceCooling) && spaceCooling?.gems2019Rating == null) ||
        waterHeating?.serviceType?.serviceTypeCode == null ||
        projectDetails.wohFloorArea == null ||
        projectDetails.nccBuildingClassification == null ||
        swimmingPool.exists == null ||
        spa.exists == null ||
        photovoltaic.exists == null ||
        (swimmingPool.exists && (swimmingPool.gems2019Rating == null || swimmingPool.volume == null)) ||
        (spa.exists && (spa.volume == null || spa.gems2019Rating == null)) ||
        (photovoltaic.exists && photovoltaic.capacity == null));
    }

    function showEnergyRating(service) {

      if (service == null || service.serviceType == null || service.serviceType.serviceTypeCode == null)
        return false;

      const found = WholeOfHomeConstants.spaceHeatingTypesNotNeedingGemsRating
        .find(code => code === service.serviceType.serviceTypeCode);

      if (found) {
        service.gems2019Rating = null;
        return false;
      }

      return true;
    }

    function showEnergyRatingForCode(serviceCode, service) {
        if(serviceCode == null) {
            if (service) service.gems2019Rating = null;
            return false;
        }
        const found = WholeOfHomeConstants.spaceHeatingTypesNotNeedingGemsRating.find(code => code === serviceCode);
        if (found) {
            service.gems2019Rating = null;
            return false;
        }
        return !found;
    }

    /**
     * Calculate the achieved 'net equivalent energy usage'
     *
     * @param a Area factor
     * @param e Whole-of-Home efficiency factors
     * @param p Pool pump energy usage
     * @param s Spa pump energy usage
     * @param r Photovoltaics capacity
     *
     * @returns {number} Achieved 'net equivalent energy usage'
     */
    function calculateAchieved(a, e, p, s, r) {

      console.log("calculateAchieved inputs: ", a, e, p, s, r);
      const achieved = (a * e) + p + s - r;
      console.log("achieved: ", achieved);
      return achieved;
    }

    /**
     * Calculate the allowed 'net equivalent energy usage'
     * @param a Area factor
     * @param f Energy factor
     * @returns {number} Allowed 'net equivalent energy usage'
     */
    function calculateAllowance(a, f) {
      console.log("calculateAllowance inputs: ", a, f);
      const allowance = a * f;
      console.log("allowance: ", allowance);
      return allowance;
    }

    /** Generic success handling function. Checks for success and returns data if so. */
    function handleSuccess(response, popupMessage = null) {
      console.log("Got response", response);
      if (response != null && response.data != null) {

        if (popupMessage != null)
          log.logSuccess(popupMessage);

        return response.data;
      } else {
        return null;
      }
    }

    /** Generic failure handling function. Logs a message with small popup. */
    function handleFail(error, message) {
      var msg = `${message}: ${error}`;
      log.logError(msg, error, null, true);
      throw error; // so caller can see it
    }

    return {
      calculateAreaFactor,
      calculateEnergyFactor,
      calculateSwimmingPoolPumpFactor,
      calculateSwimmingPoolPumpEnergyUsage,
      calculateSpaPumpEnergyUsage,
      calculateSpaPumpFactor,
      getEfficiencyFactor,

      showEnergyRating,
      showEnergyRatingForCode,

      calculate,
      calculateAchieved,
      calculateAllowance,
    };


  }
})();
