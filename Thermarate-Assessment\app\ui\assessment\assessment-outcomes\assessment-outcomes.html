<md-card layout-margin
         ng-class="{'compliance-option-selected' : vm.assessment.allComplianceOptions[0].isSelected == true && vm.assessment.allComplianceOptions[0].isCompliant}">

    <md-card-header style="display: block; margin-bottom: 8px; padding-bottom: 0px;">
        <div class="md-title" ng-class="{'card-has-errors' : baselineRunAssessmentOutcomesForm.$invalid}">
            Baseline
        </div>
    </md-card-header>

    <!-- Baseline Assessment Option: Switch depending on Preliminary Compliance Option -->
    <md-card-content ng-form="baselineRunAssessmentOutcomesForm"
                     ng-switch="vm.assessment.allComplianceOptions[0].complianceMethod.complianceMethodCode"
                     style="margin-top: 0px; padding-top: 0px;">
        <compliance-her assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMHouseEnergyRating"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        assessment-software-list="vm.assessmentSoftwareList"
                        on-compliance-changed=""
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-her>
        <compliance-her assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMPerfWAProtocolHER"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        assessment-software-list="vm.assessmentSoftwareList"
                        on-compliance-changed=""
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-her>
        <compliance-her assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMPerfSolutionHER"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        assessment-software-list="vm.assessmentSoftwareList"
                        on-compliance-changed=""
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-her>
        <compliance-her assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMPerfELL"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        assessment-software-list="vm.assessmentSoftwareList"
                        on-compliance-changed=""
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-her>
        <compliance-perf assessment="vm.assessment"
                         compliance-data="vm.assessment.allComplianceOptions[0]"
                         compliance-method-list="vm.complianceMethodList"
                         ng-switch-when="CMPerfSolution"
                         is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                         job-files="vm.jobFiles"
                         purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                         assessment-software-list="vm.assessmentSoftwareList"
                         on-compliance-changed=""
                         compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-perf>
        <!-- Atm the CMPerfSolution and CMPerfSolutionDTS share the same design-->
        <compliance-perf assessment="vm.assessment"
                         compliance-data="vm.assessment.allComplianceOptions[0]"
                         compliance-method-list="vm.complianceMethodList"
                         ng-switch-when="CMPerfSolutionDTS"
                         is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                         job-files="vm.jobFiles"
                         purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                         assessment-software-list="vm.assessmentSoftwareList"
                         on-compliance-changed=""
                         compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-perf>
        <compliance-ele assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMElementalProv"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        on-compliance-changed=""
                        assessment-software-list="vm.assessmentSoftwareList"
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-ele>
        <compliance-ele assessment="vm.assessment"
                        compliance-data="vm.assessment.allComplianceOptions[0]"
                        compliance-method-list="vm.complianceMethodList"
                        ng-switch-when="CMPerfWAProtocolEP"
                        is-locked="vm.isLocked || vm.simulationOptionIsSelected()"
                        job-files="vm.jobFiles"
                        purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                        on-compliance-changed=""
                        assessment-software-list="vm.assessmentSoftwareList"
                        compliance-type-changed="vm.complianceTypeChanged(vm.assessment.allComplianceOptions[0]);">
        </compliance-ele>
    </md-card-content>

</md-card>

<!-- Alternative Compliance Options list for when baseline assessment run does not satisfy energy requirements. -->
<compliance-options ng-if="!vm.assessment.allComplianceOptions[0].isCompliant"
                    compliance-method-list="vm.complianceMethodList"
                    assessment="vm.assessment"
                    on-selected-changed="vm.selectedComplianceOptionCode = code"
                    assessment-software-list="vm.assessmentSoftwareList"
                    purchase-order-default="vm.client.clientDefault.purchaseOrderCode"
                    set-final-compliance-method="vm.setFinalComplianceMethod({saveAssessment})"
                    enable-selection="vm.allowOptionSelect"
                    is-locked="vm.isLocked"
                    job-files="vm.jobFiles"></compliance-options>

<!-- Outcome Summary of the Baseline run OR selected compliance option -->
<outcomes-summary assessment="vm.assessment"
                  client-id="vm.clientId"
                  is-locked="vm.isLocked"
                  compliance-status-list="vm.complianceStatusList"
                  compliance-method-code="vm.complianceMethod.complianceMethodCode"
                  initial-compliance-met="vm.assessment.allComplianceOptions[0].isCompliant"
                  compliance-method-list="vm.complianceMethodList"
                  assessment-software-list="vm.assessmentSoftwareList"
                  set-final-compliance-method="vm.setFinalComplianceMethod({saveAssessment})"
                  selected-compliance-option-code="vm.selectedComplianceOption().complianceMethod.complianceMethodCode"></outcomes-summary>