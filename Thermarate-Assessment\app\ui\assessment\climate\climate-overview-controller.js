(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "climateOverview";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/assessment/climate/climate-overview.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {

            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                assessment: '<',
                disabled: '<',
                natHersChanged: '&'
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: Controller,

            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    Controller.$inject = ['$scope', 'common','uuid4', '$q',
        'energyplusweatherdata', 'nathersclimatezoneservice', 'nccclimatezoneservice'];

    function Controller($scope, common, uuid4, $q,
        energyplusweatherdata, nathersclimatezoneservice, nccclimatezoneservice) {

        let vm = this;

        // These compass directions control how many divisions there are within the windrose.
        // If you modify this, ENSURE THEY ARE STILL IN THE CORRECT ORDER WITH NORTH BEING SLOT 0!
        const COMPASS_DIRECTIONS = [
            'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
            'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
        ];

        vm.natHERSClimateZoneList = [];
        let natHERSClimateZonePromise = nathersclimatezoneservice.getList()
            .then(function(data){
                vm.natHERSClimateZoneList = data.data;
            });

        vm.nccClimateZoneCodeList = [];
        let nccClimateZonePromise = nccclimatezoneservice.getList()
            .then(function(data){
                vm.nccClimateZoneCodeList = data.data;
            });

        function saveChart(chart) {
            // Get the chart's SVG code
            var svg = chart.getSVG({
                exporting: {
                    // Have to set to original value otherwise looks to be changing depending on screen size
                    sourceWidth: 878,
                    sourceHeight: 600
                    // sourceWidth: chart.chartWidth,
                    // sourceHeight: chart.chartHeight
                }
            });
            return svg;
        }

        // Setup a watch to check for changes in the climate zone code.
        let unwatch = $scope.$watch('vm.assessment.natHERSClimateZoneCode', refreshData);
        function refreshData(newVal, oldVal) {

            if (newVal == null || newVal === "")
                return;

            if (newVal === oldVal)
                return;

            console.log("Re-initializing climate data...");
            initializeV2(newVal.substring(3, 5));
        }

        $scope.$on("$destroy", () => {
            if(unwatch != null)
                unwatch();
        });

        // Actual dataset features various years (but no overlapping data) so we group to an imaginary year.
        const YEAR = 2001;

        // As Chart X-Axis Months need to appear inbetween ticks now, we need to add an extra tick
        // at the end for December to have space to display its label.
        // Requires
        function monthTickPositioner() {
            var ticks = [
                Date.UTC(YEAR, 0, 1),
                Date.UTC(YEAR, 1, 1),
                Date.UTC(YEAR, 2, 1),
                Date.UTC(YEAR, 3, 1),
                Date.UTC(YEAR, 4, 1),
                Date.UTC(YEAR, 5, 1),
                Date.UTC(YEAR, 6, 1),
                Date.UTC(YEAR, 7, 1),
                Date.UTC(YEAR, 8, 1),
                Date.UTC(YEAR, 9, 1),
                Date.UTC(YEAR, 10, 1),
                Date.UTC(YEAR, 11, 1),
                Date.UTC(YEAR + 1, 1, 1) // Fake tick for label spacing purposes.
            ];
            //dates.info defines what to show in labels
            //apparently dateTimeLabelFormats is always ignored when specifying tickPosistioner
            ticks.info = {
                unitName: "month", //unitName: "day",
                higherRanks: {} // Omitting this would break things
            };
            return ticks;
        }

        /**
         * Retrieve and initialize any required data here,
         * not just floating around the controller.
         */
        async function initializeV2(climateZone) {

            console.time("weatherData");
            const weatherData = await energyplusweatherdata.get(climateZone);
            console.timeEnd("weatherData");

            // These are our final arrays.
            const dailyMeanTemperature = [];
            const dailyMinMaxTemperature = [];
            const dailyPrecipitableWater = [];
            const dailyGHR = [];    // Global horizontal radiation
            const dailyDifHR = [];  // Diffuse horizontal radiation
            const dailyDirHR = [];  // Direct horizontal radiation

            // Data for heatmaps
            const hourlyTemperatures = [];
            const hourlyRadiation = [];
            let climateChartData = {};

            let monthlyData = [];

            // Even though we give it a date to highcharts per data-point, the graph
            // will be messed up unless we have it all in the correct order as well. Oh well.
            for (let m = 1; m <= 12; m++) {

                const monthRaw = weatherData.filter(x => x.month === m);
                const monthCompiled = { days: [] };

                for (let d = 1; d <= 31; d++) {

                    // This day will be 24 rows (hours). So for average data
                    // we need to group that...
                    const hoursInDay = monthRaw.filter(x => x.day === d);

                    // Skip for months with less than 31 days
                    if (hoursInDay == null || hoursInDay.length === 0) {
                        continue;
                    }

                    const date = new Date(YEAR, m - 1, d + 1);
                    const ticks = date.getTime();

                    // Temp and water data
                    const meanTemp = parseFloat((hoursInDay.map(x => x.dbtc).reduce((a, b) => a + b, 0) / hoursInDay.length).toFixed(2));
                    const sortedTemps = hoursInDay.map(x => x.dbtc).sort((a, b) => Number(a) - Number(b));
                    const meanPrecipitableWater = parseFloat((hoursInDay.map(x => x.pwmm).reduce((a, b) => a + b, 0) / hoursInDay.length).toFixed(2));

                    dailyMeanTemperature.push([ticks, meanTemp]);
                    dailyMinMaxTemperature.push([ticks, sortedTemps[0], sortedTemps[sortedTemps.length - 1]]);
                    dailyPrecipitableWater.push([ticks, meanPrecipitableWater]);

                    // Radiation Chart Data
                    // � create the stacked bar chart where Diffuse Horizontal Radiation + Direct Horizontal Radiation = Global Horizontal Radiation
                    //      o Noting that we do not actually have the value for  Direct Horizontal Radiation,
                    //        and therefore needs to be calculated i.e.Direct Horizontal Radiation = Global Horizontal Radiation(N13) - DIffuse Horizontal Radiation(N15)
                    const sumGlobalHR = parseFloat((hoursInDay.map(x => x.ghr).reduce((a, b) => a + b, 0)).toFixed(2));
                    const sumDiffuseHR = parseFloat((hoursInDay.map(x => x.dhr).reduce((a, b) => a + b, 0)).toFixed(2));
                    const sumDirectHR = parseFloat((sumGlobalHR - sumDiffuseHR).toFixed(2));

                    dailyGHR.push([ticks, sumGlobalHR]);
                    dailyDifHR.push([ticks, sumDiffuseHR]);
                    dailyDirHR.push([ticks, sumDirectHR]);

                    // Determine HOURLY heatmap data
                    hoursInDay.forEach(hour => {
                        // This appears to have the tooltip working BUT the data is not displayed...???
                        hourlyTemperatures.push([ticks, hour.hour, hour.dbtc]);
                        hourlyRadiation.push([ticks, hour.hour, hour.ghr]);
                    });

                    // Used later to determine 'comfort metrics'.
                    const dailyMin = sortedTemps[0];
                    const dailyMax = sortedTemps[sortedTemps.length - 1];
                    const dailyRange = Number((dailyMax - dailyMin).toFixed(1));
                    monthCompiled.days.push({
                        hours: hoursInDay,
                        dailyMeanTemperature: meanTemp,
                        dailyMinTemperature: dailyMin,
                        dailyMaxTemperature: dailyMax,
                        dailyTempRange: dailyRange
                    });

                }

                monthCompiled.meanTemperature = monthCompiled.days
                    .map(x => x.dailyMeanTemperature)
                    .reduce((a, b) => a + b, 0) / monthCompiled.days.length;

                monthlyData.push(monthCompiled);
            }

            let windSpeedData = calculateWindSpeedData2(weatherData);

            // Average monthly temperate chart (Line graph avg + min/max)
            let temperatureChart = Highcharts.chart('monthly-temperature-chart', {

                chart: {
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },
                title: { text: 'Air Temperature' },
                credits: { enabled: false },    // This disables the highcharts watermark.
                legend: { enabled: false },

                yAxis: {
                    title: {
                        text: 'Temperature (' + common.symbol("degrees") + 'C)',
                    }
                },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    dateTimeLabelFormats: {
                        month: '%b'
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                tooltip: {
                    shared: true,
                    formatter: function () {
                        // NOTE: Apparently highcharts does not like it when you have multiple lines using ``'s
                        let date = Highcharts.dateFormat('%A, %B %d', this.x);
                        return `<span style="font-size: 10px">${date}</span><br/>Mean: <span style="font-weight:bold;">${this.y} ${common.symbol("degrees")}C</span></br>Min: <span style="font-weight:bold;">${this.points[1].point.low} ${common.symbol("degrees")}C</span></br>Max: <span style="font-weight:bold;">${this.points[1].point.high} ${common.symbol("degrees")}C</span>`;
                    }
                },

                series: [{
                    name: 'Mean',
                    data: dailyMeanTemperature,
                    zIndex: 1,
                    type: 'line',
                    marker: {
                        fillColor: 'white',
                        lineWidth: 2,
                        lineColor: Highcharts.getOptions().colors[0]
                    },
                }, {
                    name: 'Range',
                    data: dailyMinMaxTemperature,
                    type: 'arearange',
                    lineWidth: 0,
                    borderWidth: 0,
                    linkedTo: ':previous',
                    color: Highcharts.getOptions().colors[0],
                    fillOpacity: 0.3,
                    zIndex: 0,
                    marker: {
                        enabled: false,
                        lineWidth: 0,
                        borderWidth: 0,
                    },
                }]
            });

            climateChartData.monthlyTempChartSVG = saveChart(temperatureChart);

            // Hourly heatmap chart
            let heatmapTemperatureChart = Highcharts.chart('temperature-heatmap', {

                chart: {
                    type: 'heatmap',
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },
                credits: { enabled: false },    // This disables the highcharts watermark.
                boost: { useGPUTranslations: true },
                title: { text: 'Air Temperature (by hour)', align: 'center' },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    labels: {
                        format: '{value:%b}' // short month
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                yAxis: {
                    title: { text: null },
                    labels: { format: '{value}:00' },
                    minPadding: 0,
                    maxPadding: 0,
                    startOnTick: false,
                    endOnTick: false,
                    tickPositions: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
                    tickWidth: 1,
                    min: 0,
                    max: 23,
                    reversed: false
                },

                tooltip: {
                    formatter: function () {
                        let date = Highcharts.dateFormat('%A, %B %d', this.point.x);
                        let hour = common.addLeadingZero(this.point.y);
                        return `<span style="font-size: 10px">${date} @ ${hour}:00</span><br/>Temperature: <b>${this.point.value}${common.symbol("degrees")}C</b></span>`;
                    }
                },

                colorAxis: {
                    stops: [
                        [0, '#3060cf'],
                        [0.5, '#fffbbc'],
                        [0.9, '#c4463a'],
                        [1, '#c4463a']
                    ],
                    min: -5, // -5 deg to +45 should be... enough.
                    max: 45,
                    startOnTick: false,
                    endOnTick: false,
                    labels: {
                        format: '{value}' + common.symbol("degrees") + 'C'
                    },
                    reversed: false,
                },

                legend: {
                    align: 'right',
                    layout: 'vertical',
                    margin: 0,
                    verticalAlign: 'top',
                    y: 100,
                    symbolHeight: 380,
                    reversed: false
                },

                series: [{
                    data: hourlyTemperatures,
                    boostThreshold: 100,
                    borderWidth: 0,
                    nullColor: '#EFEFEF',
                    // This doesn't seem to affect anything... lol
                    colsize: 24 * 36e5, // one day
                    //rowsize: 36e5,
                }]
            });

            climateChartData.heatmapTemperatureChartSVG = saveChart(heatmapTemperatureChart);

            // Stacked column radiation chart.
            let radiationColumnChart = Highcharts.chart('solar-radiation', {

                chart: {
                    type: 'column',
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },
                title: { text: 'Daily Horizontal Radiation' },
                credits: { enabled: false },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    dateTimeLabelFormats: {
                        month: '%b'
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                yAxis: {
                    min: 0,
                    title: {
                        text: 'Wh/m&#178;.day'
                    },
                },

                legend: {
                    itemStyle: {
                        color: '#333333',
                        fontWeight: 'normal'
                    },
                    align: 'center',
                    verticalAlign: 'top',
                    y: 25,
                    floating: true,
                    backgroundColor: Highcharts.defaultOptions.legend.backgroundColor || 'white',
                    shadow: false
                },

                tooltip: {
                    formatter: function () {
                        // NOTE: Apparently highcharts does not like it when you have multiple lines using ``'s
                        let date = Highcharts.dateFormat('%A, %B %d', this.point.x);
                        return `<span style="font-size: 10px">${date}</span><br/>${this.series.name}: <span style="font-weight:bold;">${this.point.y} Wh/m&#178;.day</span></br>Global Horizontal Radiation: <span style="font-weight:bold;">${this.point.stackTotal} Wh/m&#178;.day</span>`;
                    }
                },

                plotOptions: {
                    column: { stacking: 'normal' }
                },

                series: [{
                    name: 'Diffuse Horizontal Radiation',
                    data: dailyDifHR,
                    color: '#C4473B',
                    lineWidth: 0,
                }, {
                    name: 'Direct Horizontal Radiation',
                    data: dailyDirHR,
                    color: '#88ADD7',
                    lineWidth: 0,
                }]
            });

            climateChartData.radiationColumnChartSVG = saveChart(radiationColumnChart);

            // Hourly Radiation
            let heatmapRadiationChart = Highcharts.chart('solar-radiation-variation', {

                chart: {
                    type: 'heatmap',
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },
                credits: { enabled: false },    // This disables the highcharts watermark.
                boost: { useGPUTranslations: true },
                title: { text: 'Global Horizontal Radiation (by hour)', align: 'center' },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    labels: {
                        format: '{value:%b}' // short month
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                yAxis: {
                    title: { text: null },
                    labels: { format: '{value}:00' },
                    minPadding: 0,
                    maxPadding: 0,
                    startOnTick: false,
                    endOnTick: false,
                    tickPositions: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
                    tickWidth: 1,
                    min: 0,
                    max: 23,
                    reversed: false
                },

                tooltip: {
                    formatter: function () {
                        let date = Highcharts.dateFormat('%A, %B %d', this.point.x);
                        let hour = common.addLeadingZero(this.point.y);
                        return `<span style="font-size: 10px">${date} @ ${hour}:00</span><br/>Radiation: <b>${this.point.value} Wh/m&#178;</b></span>`;
                    }
                },

                colorAxis: {
                    stops: [
                        [0, '#3060cf'],
                        [0.5, '#fffbbc'],
                        [0.9, '#c4463a'],
                        [1, '#c4463a']
                    ],
                    min: 0,
                    max: 1250,
                    startOnTick: false,
                    endOnTick: false,
                    labels: {
                        format: '<div style="text-align: center">{value}<br/>Wh/m&#178;<div>'
                    },
                    reversed: false,
                },

                legend: {
                    align: 'right',
                    layout: 'vertical',
                    margin: 0,
                    verticalAlign: 'top',
                    y: 100,
                    x: -0,
                    symbolHeight: 380,
                    reversed: false
                },

                series: [{
                    data: hourlyRadiation,
                    boostThreshold: 100,
                    borderWidth: 0,
                    nullColor: '#EFEFEF',
                    // This doesn't seem to affect anything... lol
                    colsize: 24 * 36e5, // one day
                    //rowsize: 36e5,
                }],
            });

            climateChartData.heatmapRadiationChartSVG = saveChart(heatmapRadiationChart);

            // Average monthly rain chart (line chart)
            let precipitableWaterChart = Highcharts.chart('monthly-average-moisture-chart', {

                chart: {
                    type: "line",
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },

                title: { text: 'Precipitable Water' },
                credits: { enabled: false },
                legend: { enabled: false },

                yAxis: {
                    title: {
                        text: 'Average Precipitable Water (mm)'
                    },
                },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    dateTimeLabelFormats: {
                        month: '%b'
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                tooltip: {
                    formatter: function () {
                        let date = Highcharts.dateFormat('%A, %B %d', this.point.x);
                        return `<span style="font-size: 10px">${date}</span><br/>Average Precipitable Water: <b>${this.point.y}mm</b></span>`;
                    }
                },

                series: [{
                    type: 'line',
                    name: 'Average Precipitable Water',
                    data: dailyPrecipitableWater
                }]
            });

            climateChartData.precipitableWaterChartSVG = saveChart(precipitableWaterChart);

            // Windrose
            let windroseChart = Highcharts.chart('windrose-chart', {

                data: {
                    table: 'freq',
                    startRow: 1,
                    endRow: 17,
                    endColumn: 7
                },

                chart: {
                    polar: true,
                    type: 'column',
                    height: 600,
                },

                credits: { enabled: false },    // This disables the highcharts watermark.

                title: {
                    text: 'Wind',
                    align: 'center'
                },

                legend: {
                    itemStyle: {
                        color: '#333333',
                        fontWeight: 'normal'
                    },
                    align: 'right',
                    verticalAlign: 'bottom',
                    layout: 'vertical',
                    floating: true,
                },

                xAxis: {
                    tickmarkPlacement: 'on',
                    categories: COMPASS_DIRECTIONS,
                },

                yAxis: {
                    min: 0,
                    endOnTick: false,
                    showLastLabel: true,
                    title: {
                        text: 'Frequency (%)'
                    },
                    labels: {
                        formatter: function () {
                            return this.value + '%';
                        }
                    },
                    reversedStacks: false
                },

                tooltip: {
                    formatter: function () {
                        // NOTE: Apparently highcharts does not like it when you have multiple lines using ``'s
                        return `<span style="font-size: 10px">${this.x} (${this.series.name})</span><br/>Hours: <span style="font-weight:bold;">${((Number(this.point.y) / 100) * 8760).toFixed(0)}</span></br>Time %: <span style="font-weight:bold;">${this.point.y}%</span>`;
                    }
                },

                plotOptions: {
                    series: {
                        stacking: 'normal',
                        shadow: false,
                        groupPadding: 0,
                        pointPlacement: 'on'
                    }
                },

                series: windSpeedData
            });

            climateChartData.windroseChartSVG = saveChart(windroseChart);

            vm.assessment.assessmentProjectDetail.climateChartData = climateChartData;
            vm.assessment.assessmentProjectDetail.climateChartDataJson = JSON.stringify(climateChartData);

            // Not sure if these new charts are needed in the pdf yet?
            vm.comfortMetrics = calculateComfortMetrics(monthlyData)
            constructDegreeDaysCharts(vm.comfortMetrics);

            // Save some data for use later elsewhere...
            vm.assessment.comfortMetrics = vm.comfortMetrics;

            // Thermal Comfort temperate chart (Line graph avg + min/max)
            // (Monthly Temperature Chart but with range bands)

            // assessment.comfortMetrics.heatingSetPoint - vm.comfortMetrics.heatingSetPoint
            // assessment.comfortMetrics.coolingSetPoint - vm.comfortMetrics.coolingSetPoint

            // console.log('vm.comfortMetrics:', vm.comfortMetrics);

            let thermalComfortChart = Highcharts.chart('thermal-comfort-chart', {

                chart: {
                    height: 600,
                    events: {
                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    }
                },
                title: { text: 'Thermal Comfort' },
                credits: { enabled: false },    // This disables the highcharts watermark.
                legend: { enabled: false },

                yAxis: {
                    title: {
                        text: 'Temperature (' + common.symbol("degrees") + 'C)',
                    },

                    // Note: Yes, Cooling Set Point is red and at the top, and Heating Set Point is blue and at the bottom.
                    plotBands: [
                        // Cooling (top)
                        {
                            color: 'rgba(255, 0, 0, 0.1)',
                            from: vm.comfortMetrics.coolingSetPoint, // Start
                            to: 99 // End (overcompensate)
                        },
                        // Heating (bottom)
                        {
                            color: 'rgba(0, 0, 255, 0.1)',
                            from: -99, // Start (overcompensate)
                            to: vm.comfortMetrics.heatingSetPoint // End
                        },
                    ],
                    plotLines: [
                        // Cooling (top)
                        {
                            zIndex: 4, // Lowest possible without appearing below the series
                            color: 'black',
                            value: vm.comfortMetrics.coolingSetPoint,
                            width: 2,
                            label: {
                                text: `Cooling Thermostat Set Point = ${vm.comfortMetrics.coolingSetPoint} ${common.symbol("degrees")}C`,
                                style: { fontSize: '13px' },
                                align: 'left',
                            }
                        },
                        // Heating (bottom)
                        {
                            zIndex: 4,
                            color: 'black',
                            value: vm.comfortMetrics.heatingSetPoint,
                            width: 2,
                            label: {
                                text: `Heating Thermostat Set Point = ${vm.comfortMetrics.heatingSetPoint} ${common.symbol("degrees")}C`,
                                style: { fontSize: '13px' },
                                align: 'left',
                                y: 15, // move label below line
                            }
                        },
                    ]
                },

                xAxis: {
                    type: 'datetime',
                    min: Date.UTC(YEAR, 0, 1),
                    max: Date.UTC(YEAR, 11, 31, 23, 59, 59),
                    dateTimeLabelFormats: {
                        month: '%b'
                    },
                    endOnTick: false, // Hide last spacer tick
                    tickPositioner: monthTickPositioner
                },

                tooltip: {
                    shared: true,
                    formatter: function () {
                        // NOTE: Apparently highcharts does not like it when you have multiple lines using ``'s
                        let date = Highcharts.dateFormat('%A, %B %d', this.x);
                        return `<span style="font-size: 10px">${date}</span><br/>Mean: <span style="font-weight:bold;">${this.y} ${common.symbol("degrees")}C</span></br>Min: <span style="font-weight:bold;">${this.points[1].point.low} ${common.symbol("degrees")}C</span></br>Max: <span style="font-weight:bold;">${this.points[1].point.high} ${common.symbol("degrees")}C</span>`;
                    }
                },

                series: [{
                    name: 'Mean',
                    data: dailyMeanTemperature,
                    zIndex: 1,
                    type: 'line',
                    marker: {
                        fillColor: 'white',
                        lineWidth: 2,
                        lineColor: Highcharts.getOptions().colors[0]
                    },
                }, {
                    name: 'Range',
                    data: dailyMinMaxTemperature,
                    type: 'arearange',
                    lineWidth: 0,
                    borderWidth: 0,
                    linkedTo: ':previous',
                    color: Highcharts.getOptions().colors[0],
                    fillOpacity: 0.3,
                    zIndex: 0,
                    marker: {
                        enabled: false,
                        lineWidth: 0,
                        borderWidth: 0,
                    },
                }]
            });
        }

        function calculateComfortMetrics(monthlyData) {

            // PERF: It seems tempting to calculate a lot of this information
            // during the initial extraction process. Due to the constantly
            // changing nature of graphing requirements and the addition of new
            // calculations regularly, this is not being done yet. However keep
            // it in mind for future when/if things stabilize and we are doing
            // a redesign...

            const metrics = { heatingSetPoint: 15.0 };

            metrics.meanJanuaryTemp = monthlyData[0].meanTemperature;

            metrics.coolingSetPoint = Number((17.8 + (0.31 * metrics.meanJanuaryTemp)).toFixed(1));

            let dailyTempAvgTotal = 0;
            let totalHeatingHours = 0;
            let totalHeatingDegreeHours = 0;
            let totalHeatingDegreeDays = 0;
            let totalCoolingHours = 0;
            let totalCoolingDegreeHours = 0;
            let totalCoolingDegreeDays = 0;
            let totalDehumidificationGramHours = 0;

            monthlyData.forEach((month, m) => {

                month.heatingDegreeDays = 0;
                month.coolingDegreeDays = 0;

                month.days.forEach((day, d) => {

                    dailyTempAvgTotal += day.dailyTempRange;

                    if(day.dailyMeanTemperature < metrics.heatingSetPoint) {
                        month.heatingDegreeDays += metrics.heatingSetPoint - day.dailyMeanTemperature;
                        totalHeatingDegreeDays += metrics.heatingSetPoint - day.dailyMeanTemperature;
                    }

                    if(day.dailyMeanTemperature > metrics.coolingSetPoint) {
                        month.coolingDegreeDays += day.dailyMeanTemperature - metrics.coolingSetPoint;
                        totalCoolingDegreeDays += day.dailyMeanTemperature - metrics.coolingSetPoint;
                    }

                    day.hours.forEach((hour, h) => {

                        if(hour.dbtc < metrics.heatingSetPoint) {
                            totalHeatingHours += 1;
                            totalHeatingDegreeHours += (metrics.heatingSetPoint - hour.dbtc);
                        }

                        if(hour.dbtc > metrics.coolingSetPoint) {
                            totalCoolingHours += 1;
                            totalCoolingDegreeHours += (hour.dbtc - metrics.coolingSetPoint);
                        }

                        // Ps = (1.0007+(3.46*(G20/100)*10^-6))*6.1121*EXP((17.502*D20)/(240.97+D20))
                        // Pv = (F20/100)*BK20
                        // AHgm3 = (BL20*100*2.165)/(D20+273.16)
                        // AHgkg = (1/((G20/101300)*(((20+273.15)/(D20+273.15)))*1.2041))*BM20
                        // DGH = IF(BN20 > 15.7, BN20-15.7 , 0)

                        let Ps = (1.0007 + (3.46 * (hour.asp / 100) * 10e-7)) * 6.1121 * Math.exp((17.502 * hour.dbtc) / (240.97 + hour.dbtc));
                        let Pv = (hour.rh / 100) * Ps;
                        let AHgm3 = (Pv * 100 * 2.165) / (hour.dbtc+273.16);
                        let AHgkg = (1 / ((hour.asp / 101300) * (((20 + 273.15) / (hour.dbtc + 273.15))) * 1.2041)) * AHgm3;
                        let DGH = AHgkg > 15.7
                            ? AHgkg - 15.7
                            : 0

                        totalDehumidificationGramHours += DGH;

                    });
                });
            });

            metrics.annualAvgDailyTempRange = dailyTempAvgTotal / 365;
            metrics.heatingHours = totalHeatingHours;
            metrics.heatingDegreeHours = totalHeatingDegreeHours;
            metrics.heatingDegreeDays = totalHeatingDegreeDays;
            metrics.coolingHours = totalCoolingHours;
            metrics.coolingDegreeHours = totalCoolingDegreeHours;
            metrics.coolingDegreeDays = totalCoolingDegreeDays;
            metrics.dehumidificationGramHours = totalDehumidificationGramHours;
            metrics.monthlyData = monthlyData;

            return metrics;
        }

        function constructDegreeDaysCharts(metrics) {

            const MONTHS = [
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec'
            ];

            const COLOUR_HEATING = "#E63845";
            const COLOUR_COOLING = "#3F87C4";
            // const COLOUR_TOTAL   = "#B1B1B1";

            const coolingData = metrics.monthlyData.map(x => Number(x.coolingDegreeDays.toFixed(0)));
            // Heating is to be negative on the chart.
            const heatingData = metrics.monthlyData.map(x => Number(-1 * x.heatingDegreeDays.toFixed(0)));

            Highcharts.chart('heating-and-cooling-degree-days-chart', {

                chart: {
                    type: 'column',
                    height: 600,
                },
                title: { text: 'Heating and Cooling Degree Days' },
                credits: { enabled: false },

                xAxis: {
                    categories: MONTHS,
                    startOnTick: true,
                    endOnTick: false,
                },
                yAxis: [
                    {
                        reversed: false,
                        title: {
                            text: "Degree Days"
                        },
                        labels: {
                            formatter: function () {
                                return Math.abs(this.value);
                            }
                        },
                    }
                ],

                tooltip: {
                    formatter: function () {
                        return `<span style="font-size: 10px">${this.x}</span><br/><span style="font-size: 12px">${this.series.name}: <span style="font-weight:bold;">${Math.abs(this.point.y)}</span></span>`;
                    }
                },

                legend: {
                    itemStyle: { color: '#333333', fontWeight: 'normal' },
                    align: 'center',
                    verticalAlign: 'top',
                    y: 25,
                    floating: true,
                    backgroundColor: Highcharts.defaultOptions.legend.backgroundColor || 'white',
                    shadow: false
                },

                plotOptions: {
                    series: {
                        stacking: 'normal'
                    }
                },

                series: [
                    {
                        name: `Cooling Degree Days`,
                        data: coolingData,
                        color: COLOUR_COOLING,
                        lineWidth: 0,
                    },
                    {
                        name: `Heating Degree Days`,
                        data: heatingData,
                        color: COLOUR_HEATING,
                        lineWidth: 0,
                        reversed: true
                    }
                ]
            });
        }

        /**
         * Groups up wind speed data as required for a windrose graph.
         *
         * @param {any} data All weather data from .epw file.
         *
         * @returns ???
         */
        function calculateWindSpeedData2(data) {

            // Break down our compass ranges based on the given compass directions.
            // NOTE: COMPASS_DIRECTIONS MUST be in order or this will give wrong results.
            let curDivision = 0;
            const divisions = COMPASS_DIRECTIONS.length;
            let sliceSize = 360 / divisions;
            const COMPASS_RANGES = COMPASS_DIRECTIONS.map(x => {

                let range = {};
                if (curDivision === 0) {
                    range.min = 360 - (sliceSize / 2);
                    range.max = 0 + (sliceSize / 2);
                } else {
                    range.min = (sliceSize / 2) + (sliceSize * (curDivision - 1));
                    range.max = range.min + sliceSize;
                }
                curDivision++;

                return { [x]: range };
            }).reduce((a, b) => { return { ...a, ...b } }, {});

            const WIND_SPEED_GROUPS = [
                [0, 0.1],
                [0.1, 3],
                [3, 6],
                [6, 9],
                [9, 12],
                [12, 15],
                [15, 18],
                [18, 21],
                [21, 24],
            ];

            const WIND_SPEED_GROUPS_NAMES = [
                "Calm",
                "0-3 m/s",
                "3-6 m/s",
                "6-9 m/s",
                "9-12 m/s",
                "12-15 m/s",
                "15-18 m/s",
                "18-21 m/s",
                "21-24 m/s",
            ];

            // We use the total data points to determine the % per direction.
            const totalDataPoints = data.length;
            const transform = [];

            // Determine %age time for all windspeed groups.
            for (let x = 0; x < WIND_SPEED_GROUPS.length; x++) {

                const windGroup = WIND_SPEED_GROUPS[x];

                const windPercentagesForDirection = [];

                for (let i = 0; i < COMPASS_DIRECTIONS.length; i++) {

                    const direction = COMPASS_DIRECTIONS[i];

                    // So... e.g. our WIND_SPEED_GROUP is now 0-2
                    // So we want to grab all data points where
                    // the wind speed is between 0-2 and the compass-direction
                    // is between 325 and 22.5 or whatever.
                    const range = COMPASS_RANGES[direction];

                    let matches = null;
                    if (i === 0) {

                        // We have special rules for NORTH as it is where
                        // the compass crosses the 360->0 bridge.
                        matches = data.filter(
                            x => (x.wdd > range.min ||  x.wdd <= range.max) &&
                                (x.wsms >= windGroup[0] && x.wsms < windGroup[1]));

                    } else {

                        matches = data.filter(
                            x => ((x.wdd > range.min && x.wdd <= range.max)) &&
                                (x.wsms >= windGroup[0] && x.wsms < windGroup[1]));

                    }

                    // To get the percentage of time this direction & speed is the case,
                    // we simply divide by the total number of data points.
                    let percentage = 0;
                    if (matches != null && matches.length > 0)
                        percentage = (matches.length / totalDataPoints) * 100;

                    windPercentagesForDirection.push(parseFloat(percentage.toFixed(1)));

                }

                transform.push({ name: WIND_SPEED_GROUPS_NAMES[x], data: windPercentagesForDirection })
            }

            // Determine what %age of time is simply "calm"
            const calmDataPoints = data.filter(x => x.wsms === 0).length;
            const calmDataPercent = Number(((calmDataPoints / totalDataPoints) * 100).toFixed(1));
            vm.calmDataPercent = calmDataPercent;

            const calmPoints = transform.find(x => x.name === "Calm");

            for(let i = 0; i < calmPoints.data.length; i++) {
                calmPoints.data[i] = calmDataPercent;
            }

            return transform;

        }

        vm.updateNatHERSData = function(item) {
            vm.assessment.natHERSClimateZoneCode = item.natHERSClimateZoneCode;
            nathersclimatezoneservice.updateNatHERSData(vm.assessment);
            vm.natHersChanged();
        }

        vm.symbol = function(symbolName) {
            return common.symbol(symbolName);
        }

        // Initialize component once we have required data for climate zones.
        $q.all([natHERSClimateZonePromise, nccClimateZonePromise])
            .then(() => {

                if(vm.assessment.natHERSClimateZone == null) {
                    setTimeout(() => initializeV2(vm.assessment.natHERSClimateZone?.description), 500);
                } else {
                    initializeV2(vm.assessment.natHERSClimateZone?.description);
                }

            });

    }
})();