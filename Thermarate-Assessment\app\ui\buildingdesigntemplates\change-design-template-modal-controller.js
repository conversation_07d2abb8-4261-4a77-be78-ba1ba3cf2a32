(function () {
    'use strict';
    var controllerId = 'ChangeDesignTemplateModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'buildingdesigntemplateservice', changeDesignTemplateModalController]);
    function changeDesignTemplateModalController($scope, $mdDialog, buildingdesigntemplateservice) {

        var vm = this;

        vm.selectedTemplate = null;
        vm.buildingDesignTemplates = [];

        buildingdesigntemplateservice.getAll(vm.clientId, false).then(function (data) {

            if (data == undefined || data == null || data.length == 0) {
                //nope cant have a match
                return;
            }

            vm.buildingDesignTemplates = data;
        });

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.submitSelection = function () {
            $mdDialog.hide(vm.selectedTemplate);
        };
    }
})();