<form name="addOptionForm" 
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='addOptionModalController as vm'>

    <div class="widget" 
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title()}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">
            <div layout="row" layout-sm="column" layout-xs="column" style="min-width:600px;">
                <!--Main Body-->
                <div flex="100">
                    <md-card>
                        <md-card-content>

                            <!-- Certification -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex-gt-sm
                                                style="margin-top: 20px;">
                                <label>Certification</label>
                                <md-select name="certification"
                                           ng-required="true"
                                           ng-model="vm.option.certification"
                                           ng-model-options="{trackBy: '$value.certificationId'}"
                                           ng-change="vm.option.sectorDetermination = vm.option.certification.sectorDetermination">
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.certificationList track by item.certificationId">
                                        {{item.title}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="jobform.certification.$error">
                                    <div ng-message="required">Certification is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Sector Determination ******** -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex="100">
                                <label>Sector Determination</label>
                                <md-select name="sectorDetermination"
                                           ng-required="true"
                                           ng-model="vm.option.sectorDetermination"
                                           ng-model-options="{trackBy: '$value.sectorDeterminationCode'}">
                                    <md-option ng-value="sector"
                                               ng-repeat="sector in vm.sectorDeterminationList track by sector.sectorDeterminationCode">
                                        {{sector.title}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- ******** Assessment Method ******** -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Assessment Method</label>
                                <md-select name="preliminaryComplianceMethodCode"
                                           ng-required
                                           ng-model="vm.option.complianceMethod"
                                           ng-model-options="{trackBy: '$value.complianceMethodCode'}"
                                           ng-change="vm.determineAvailableSoftware(vm.option.complianceMethod.complianceMethodCode)">
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.availableComplianceMethods() track by item.complianceMethodCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="addOptionFormpreliminaryComplianceMethodCode.$error">
                                    <div ng-message="required">Assessment Method is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Assessment Software ******** -->
                            <md-input-container ng-if="vm.option.assessmentSoftwareCode != 'Other'"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Assessment Software</label>
                                <md-select name="assessmentSoftwareCode"
                                           ng-model="vm.option.assessmentSoftware"
                                           ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}"
                                           ng-required>
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.availableAssessmentSoftwareList track by item.assessmentSoftwareCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- Min House Energy Rating -->
                            <md-input-container ng-if="vm.option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                                       vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolutionHER' ||
                                                       vm.option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER'"
                                                class="md-block vertically-condensed vertically-condensed-ex" 
                                                flex="100">
                                <label>Required House Energy Ratings</label>
                                <md-select name="RequiredHouseEnergyRating"
                                           class="vertically-condensed vertically-condensed-ex"
                                           flex="100"
                                           ng-required="true"
                                           ng-disabled="vm.isLocked"
                                           ng-model="vm.tempRequiredHouseEnergyRating"
                                           ng-change="vm.handleReqHERFormat()">
                                    <md-option ng-repeat="value in vm.availableHouseEnergyRatings"
                                               ng-value="value">
                                        <span>{{value}}</span>
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- Maximum Heating and Cooling Limits Toggle -->
                            <div ng-if="vm.option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                        vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolutionHER' ||
                                        vm.option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER'">
                                <span flex="100" style="font-size: 9px; font-weight: 100; color:#999999" 
                                      class="flex-100">
                                    Load Limits
                                </span>
                                <md-switch ng-model="vm.option.heatingAndCoolingRulesetCode"
                                           style="margin: 4px 0 20px 0"
                                           ng-true-value="'Enabled'"
                                           ng-false-value="'Disabled'"
                                           class="md-warn md-block">
                                </md-switch>
                            </div>

                            <!-- Description -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex="100">
                                <label>Description</label>
                                <textarea type="text" name="notes"
                                          ng-model="vm.option.description"
                                          rows="3"
                                          maxlength="500"
                                          ng-required="true" />
                            </md-input-container>

                            <!-- Updated Drawings Required -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex="100">
                                <label>Drawings</label>
                                <md-select name="UpdatedDrawingsRequired"
                                           ng-model="vm.option.updatedDrawingsRequired"
                                           ng-disabled="vm.isLocked"
                                           ng-required="true">
                                    <md-option ng-value="true">Updated drawings required</md-option>
                                    <md-option ng-value="false">Copy from Baseline</md-option>
                                </md-select>
                            </md-input-container>

                            <!-- New Purchase Order -->
                            <md-checkbox ng-if="vm.assessment.job.client.clientDefault.purchaseOrderCode !== 'NotRequired'"
                                         ng-model="vm.option.newPurchaseOrderRequired"
                                         style="display: block;  margin-bottom: 10px; margin-top: 10px;"
                                         flex>
                                New Purchase Order
                            </md-checkbox>

<!--                            &lt;!&ndash; Mark-Up &ndash;&gt;-->
<!--                            <md-checkbox ng-model="vm.option.markupFileRequired"-->
<!--                                         style="display: block;  margin-bottom: 10px; margin-top: 20px;"-->
<!--                                         flex>-->
<!--                                Mark-Up-->
<!--                            </md-checkbox>-->

                            <!-- Proposed Specifications -->
                            <div style="background-color: #FAFAFA; margin-bottom: 10px; padding: 10px; border-radius: 5px 5px;">

                                <!-- Proposed Header -->
                                <div style="margin: 10px 0 20px 0;">
                                    <span style="font-size: 12px; font-weight: bold;">
                                        Proposed Building Specifications
                                    </span>
                                </div>

                                <!-- Proposed Template Selections -->
                                <div style="border: 1px solid #EAEAEA; border-radius: 4px 4px; padding: 7px 4px; margin: 9px 0px;">

                                    <!-- Proposed Design Template Selection -->
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="margin-top: 15px;"
                                                        flex>
                                        <label>Proposed Building Design Template</label>
                                        <div layout="row">
                                            <md-select name="zoneTemplate"
                                                       flex="100"
                                                       ng-model="vm.option.proposed.newBuildingZoneTemplateId">
                                                <md-option ng-value="item.buildingDesignTemplateId"
                                                           ng-repeat="item in vm.buildingDesignTemplates">
                                                    {{item.templateName}}
                                                </md-option>
                                                <md-option ng-value="'COPY'">
                                                    Copy Baseline
                                                </md-option>
                                                <md-option ng-value="'BLANK_TEMPLATE'">
                                                    Blank Design Template
                                                </md-option>
                                            </md-select>
                                        </div>
                                    </md-input-container>

                                    <!-- ******** Proposed Building Construction Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>Proposed Building Construction Template</label>
                                        <md-select name="proposedConstructionTemplate"
                                                   ng-model="vm.option.proposed.newConstructionTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingConstructionTemplateId"
                                                       ng-repeat="item in vm.buildingConstructionTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                Copy Baseline
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Construction Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Proposed Building Opening Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>Proposed Building Opening Template</label>
                                        <md-select name="proposedOpeningTemplate"
                                                   ng-model="vm.option.proposed.newOpeningTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingConstructionTemplateId"
                                                       ng-repeat="item in vm.buildingOpeningTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                Copy Baseline
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Opening Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Proposed Building Services Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>Proposed Building Services Template</label>
                                        <md-select name="proposedServicesTemplate"
                                                   ng-model="vm.option.proposed.newServicesTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingServicesTemplateId"
                                                       ng-repeat="item in vm.buildingServicesTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                Copy Baseline
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Services Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <!-- Proposed Copy Annual Energy Loads -->
                                <md-checkbox ng-if="(vm.baseline.complianceMethod.complianceMethodCode !== 'CMElementalProv' || 
                                                     vm.baseline.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP') &&
                                                    (vm.option.complianceMethod.complianceMethodCode !== 'CMElementalProv' ||
                                                     vm.option.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP')"
                                             ng-model="vm.option.proposed.copyAnnualEnergyLoads"
                                             style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                             flex>
                                    Copy Baseline Annual Energy Loads
                                </md-checkbox>

                                <!-- Proposed Copy Assessment Files -->
                                <md-checkbox ng-model="vm.option.proposed.copyAssessmentFiles"
                                             style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                             flex>
                                    Copy Baseline Assessment Files
                                </md-checkbox>

                            </div>

                            <!-- Reference Specifications -->
                            <div ng-if="(vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || 
                                         vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS')"
                                 style="background-color: #FAFAFA; margin-bottom: 10px; padding: 10px; border-radius: 5px 5px;">

                                <!-- Reference Header -->
                                <div style="margin: 10px 0 20px 0;">
                                        <span style="font-size: 12px; font-weight: bold; padding-top: 10px; padding-bottom: 10px;">
                                                {{vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference' : 'Deemed-to-Satisfy'}} Building Specifications
                                        </span>
                                </div>

                                <!-- Referenced Template Selections -->
                                <div style="border: 1px solid #EAEAEA; border-radius: 4px 4px; padding: 7px 4px; margin: 9px 0px;">

                                    <!-- Reference Zone Template Selection (If Required)-->
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="margin-top: 15px;"
                                                        flex>
                                        <label>{{vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Design Template' : 'Deemed-to-Satisfy Building Design Template'}}</label>
                                        <div layout="row">
                                            <md-select name="zoneTemplate"
                                                       flex="100"
                                                       ng-model="vm.option.reference.newBuildingZoneTemplateId">
                                                <md-option ng-value="item.buildingDesignTemplateId"
                                                           ng-repeat="item in vm.buildingDesignTemplates">
                                                    {{item.templateName}}
                                                </md-option>
                                                <md-option ng-value="'COPY'">
                                                    {{ vm.copyProposedIntoReference() ? 'Copy Proposed' : 'Copy Baseline' }}
                                                </md-option>
                                                <md-option ng-value="'BLANK_TEMPLATE'">
                                                    Blank Design Template
                                                </md-option>
                                            </md-select>
                                        </div>
                                    </md-input-container>

                                    <!-- ******** Reference Building Construction Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>{{vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Construction Template' : 'Deemed-to-Satisfy Building Construction Template'}}</label>
                                        <md-select name="proposedConstructionTemplate"
                                                   ng-model="vm.option.reference.newConstructionTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingConstructionTemplateId"
                                                       ng-repeat="item in vm.buildingConstructionTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                {{ vm.copyProposedIntoReference() ? 'Copy Proposed' : 'Copy Baseline' }}
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Construction Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Reference Building Opening Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>{{vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Opening Template' : 'Deemed-to-Satisfy Building Opening Template'}}</label>
                                        <md-select name="proposedOpeningTemplate"
                                                   ng-model="vm.option.reference.newOpeningTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingConstructionTemplateId"
                                                       ng-repeat="item in vm.buildingOpeningTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                {{ vm.copyProposedIntoReference() ? 'Copy Proposed' : 'Copy Baseline' }}
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Opening Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- ******** Reference Building Services Templates ******** -->
                                    <md-input-container class="md-block vertically-condensed" flex>
                                        <label>{{vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Services Template' : 'Deemed-to-Satisfy Building Services Template'}}</label>
                                        <md-select name="referenceServicesTemplate"
                                                   ng-model="vm.option.reference.newServicesTemplateId"
                                                   required>
                                            <md-option ng-value="item.buildingServicesTemplateId"
                                                       ng-repeat="item in vm.buildingServicesTemplates">
                                                {{item.templateName}}
                                            </md-option>
                                            <md-option ng-value="'COPY'">
                                                {{ vm.copyProposedIntoReference() ? 'Copy Proposed' : 'Copy Baseline' }}
                                            </md-option>
                                            <md-option ng-value="'BLANK_TEMPLATE'">
                                                Blank Services Template
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                </div>

                                <!-- Copy Annual Energy Loads -->
                                <md-checkbox ng-if="(vm.baseline.complianceMethod.complianceMethodCode !== 'CMElementalProv' || 
                                                     vm.baseline.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP') &&
                                                    (vm.option.complianceMethod.complianceMethodCode !== 'CMElementalProv' ||
                                                     vm.option.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP')"
                                             ng-model="vm.option.reference.copyAnnualEnergyLoads"
                                             style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                             flex>
                                    {{ vm.copyProposedIntoReference() ? 'Copy Proposed Annual Energy Loads' : 'Copy Baseline Annual Energy Loads' }}
                                </md-checkbox>

                                <!-- Copy Assessment Files -->
                                <md-checkbox ng-model="vm.option.reference.copyAssessmentFiles"
                                             style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                             flex>
                                    {{ vm.copyProposedIntoReference() ? 'Copy Proposed Assessment Files' : 'Copy Baseline Assessment Files' }}
                                </md-checkbox>

                            </div>

                        </md-card-content>
                    </md-card>
                </div>
            </div>

            <!-- Add + Cancel Buttons -->
            <div data-cc-widget-button-bar
                 data-is-modal="true">

                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_actions__addcompliance']"
                           ng-disabled="addOptionForm.$invalid"
                           ng-show="vm.job.deleted!=true"
                           ng-click="vm.save()">
                    Add
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>