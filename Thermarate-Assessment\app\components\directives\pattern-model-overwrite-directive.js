﻿(function () {
    'use strict';

    var app = angular.module('app');
    //overwrite the validator of the pattern in old angular js directive to match regex properly
    app.directive('patternModelOverwrite', function patternModelOverwriteDirective() {
        return {
            restrict: 'A',
            require: 'ngModel',
            link: function (scope, elem, attrs, ngModelCtrl) {
                var dRegex = new RegExp('^(?:(?:31(\\/|-|\\.)(?:0?[13578]|1[02]|(?:January|March|May|July|August|October|December)))\\1|(?:(?:29|30)(\\/|-|\\.)(?:0?[1,3-9]|1[0-2]|(?:January|March|April|May|June|July|August|September|October|November|December))\\2))(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$|^(?:29(\\/|-|\\.)(?:0?2|(?:February))\\3(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\\d|2[0-8])(\\/|-|\\.)(?:(?:0?[1-9]|(?:January|February|March|April|May|June|July|August|September))|(?:1[0-2]|(?:October|November|December)))\\4(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$');

                ngModelCtrl.$parsers.unshift(function (value) {

                    if (typeof value === 'string') {
                        var isValid = dRegex.test(value);
                        ngModelCtrl.$setValidity('datep', isValid);
                        if (!isValid) {
                            return undefined;
                        }
                    }

                    return value;
                });

            }
        };
    });
})();