(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "reportFiles";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/report-files/report-files.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {
            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                assessmentId: '<',
                jobId: '<', 
                reportFiles: '<', 
                complianceOptions: '<',
                disabled: '<'
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: Controller,

            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    Controller.$inject = ['$scope', 'common','uuid4', '$q', 'assessmentservice', 'fileservice', 'Upload'];

    function Controller($scope, common, uuid4, $q, assessmentservice, fileservice, Upload) {

        let vm = this;

        var categoriesOrder = [
            { classification: 'Energy Efficiency Compliance Report' },
            { classification: 'Energy Efficiency PBDB' },
            { classification: 'Specification 44' },
            { classification: 'Energy Usage Calculation' },
            { classification: 'Software Report', nameContains: 'Proposed Building' },
            { classification: 'Software Report', nameContains: 'DTS Building' },
            { classification: 'Software Report', nameContains: 'Reference Building' },
            { classification: 'Preliminary Report' },
            { classification: 'Light & Ventilation Report' },
        ];

        vm.reportShowSettingChanged = function(reportSetting) {
            if(reportSetting.showOnClientPortal === false) {
                reportSetting.allowDownloadOnClientPortal = false;
            }
        }

        vm.specificationSummaryReports = () => vm.reportFiles
            ?.filter(x => x.category === "Specification Summary" && x.assessmentId === vm.assessmentId)
            .reverse();

        vm.complianceReports = () => vm.reportFiles
            ?.filter(x => x.category === "Compliance Report" && x.assessmentId === vm.assessmentId)
            .sort((a,b) => categoriesOrder.findIndex(x => x.classification == a.classification && (x.nameContains == null || a.fileName.includes(x.nameContains)))
                            > categoriesOrder.findIndex(x => x.classification == b.classification && (x.nameContains == null || b.fileName.includes(x.nameContains)))
                            ? 1 : -1
            );

        vm.customDocuments = () => vm.reportFiles
            ?.filter(x => x.category === "Custom Document" && x.assessmentId === vm.assessmentId)
            .reverse();

        /**
        * Special functionality: This specific checkbox is able to bypass the regular "save" functionality and is
        * instead updated serverside immediately upon being clicked.
        *
        * Note due to angularjs tomfoolery the 'allowReportDownload' param should be the opposite of what you wish to
        * set it as (timing issues i guess).
        */
        vm.updateAllowReportDownloadState = function(report) {
            setTimeout(() => assessmentservice.updateAllowReportDownload(report), 20);
        }

        vm.downloadFile = (f) => fileservice.downloadFile(f);
        vm.downloadFileForceDialog = (f) => fileservice.downloadFileForceDialog(f);

        vm.uploadingData = null;
        vm.allUploadingFiles = [];
        vm.uploadFiles = function ($files) {

            if ($files == null || $files.length == 0 || $files.every(f => f == null))
              return;

            if(vm.disabled)
              return;

            let url = "../api/Assessment/UploadFile?assessmentId=" + vm.assessmentId;
            url += "&jobId=" + vm.jobId;
            url += `&category=Custom Document`;
            url += `&classification=Generic`;

            vm.allUploadingFiles = [];

            $files.forEach(($file, index) => {
                let promise = Upload.upload({
                    url: url, // webapi url
                    method: "POST",
                    file: $file
                });
                let newFileUpload = {
                    index: index,
                    progress: 0
                };
                vm.allUploadingFiles.push(newFileUpload);

                promise.progress(function (evt) {

                    newFileUpload.progress = 100 * (evt.loaded / evt.total);

                }).success(async function (file) {

                    const exists = vm.reportFiles?.some(x => x.fileId === file.fileId);

                    if(!exists) {
                        vm.reportFiles.push(file);
                    }

                    vm.allUploadingFiles = vm.allUploadingFiles.filter(u => u.index != index);

                }).error(function (data, status, headers, config) {

                    vm.allUploadingFiles = vm.allUploadingFiles.filter(u => u.index != index);

                });
            });
        }

        vm.drawingsUploadProgress = function() {
            if (vm.allUploadingFiles.length == 0) {
                return 0;
            } else {
                return vm.allUploadingFiles.map(u => u.progress).reduce((a,b) => a + b, 0) / vm.allUploadingFiles.length;
            }
        }

        vm.deleteCustomDocument = async (f) => {
            await fileservice.deleteFile(f.fileId);
            vm.reportFiles = vm.reportFiles.filter(x => x.fileId !== f.fileId);
        }
    }
})();