/*
 * Security Authorisation Module
 * -----------------------------
 * Ensures the user is authorised for the requested component.
 * If they are NOT, the component is still shown, but will be disabled.
 * 
 * Recursively disables child components.
 * 
 * If you wish to completely hide the control, use <redi-show-by-roles> directive instead (There is also redi-allow-roles?)
 */

var mod = angular.module('security.authorization');
mod.directive('rediEnableRoles', ['security', function (Auth) {
    // Description:
    // Checks if a user has access to the named role.
    // If no access then element is disabled.
    //
    // Usage:
    // <div redi-enableRoles="administrator"></div>
    return {
        restrict: 'A',
        link: function ($scope, element, attrs) {

            let previousState = element.attr('disabled');
            let userRoles = Auth.currentUser.userRoles;
            let allowRoles = attrs.rediEnableRoles;

            // Watch for changes to the users roles.
            $scope.currentUser = Auth.currentUser;
            $scope.$watch('currentUser', function (user) {
                if (user != undefined && user.userRoles)
                    userRoles = user.userRoles;
                updateHTML();
            }, true);

            // watch for changes to the allowed roles.
            attrs.$observe('rediEnableRoles', function (ar) {
                if (ar) allowRoles = ar;
                updateHTML();
            });

            // update the CSS on the element (display or hide)
            function updateHTML() {

                if (allowRoles == null || allowRoles == "" || allowRoles == "[]" || allowRoles.length == 0) {
                    setDisabledState(element, previousState);
                    return;
                }

                if (userRoles && allowRoles) {

                    // Check through all roles and if we find a match, set the state
                    // to whatever it was before (I _think_ this is so that 'disabled' 
                    // states applied by other directives (e.g. ng-disabled) are still applied).
                    for (var i = 0; i < userRoles.length; i++) {
                        if (allowRoles.indexOf(userRoles[i].toLowerCase()) > -1) {
                            setDisabledState(element, previousState);
                            return;
                        }
                    }

                    setDisabledState(element, 'true');
                    
                }
            }

            /**
             * Recursively loops through the element and all children and applies the disabled state.
             * 
             * @param {String} state 'true' for disabled, 'false' for enabled.
             */
            function setDisabledState(element, state) {

                element.attr('disabled', state);

                let children = element.children();

                // Recursively disable children until we have none left to disable.
                if (children == null || children.length === 0)
                    return;
                else
                    setDisabledState(children, state);

            }
        }
    };
}]);