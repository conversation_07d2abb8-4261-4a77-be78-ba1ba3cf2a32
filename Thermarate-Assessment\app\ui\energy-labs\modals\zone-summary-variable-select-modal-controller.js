(function () {
    'use strict';
    var controllerId = 'ZoneSummaryVariableSelectModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'zoneservice', 'zonesummaryservice', 'selectvariablelinkservice', zoneSummaryVariableSelectController]);
    function zoneSummaryVariableSelectController(common, $scope, $mdDialog, zoneservice, zonesummaryservice, selectvariablelinkservice) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;
        vm.varRefObj = $scope.varRefObj;
        vm.knownBuildingSummaryGroups = zonesummaryservice.knownBuildingSummaryGroups;
        vm.sectorFromLabel = zonesummaryservice.sectorFromLabel;
        vm.sectors = zonesummaryservice.defaultSectors;
        vm.isSectorNotEmpty = zonesummaryservice.isSectorNotEmpty;

        // Envelope 
        vm.GROUP_OPTIONS = zonesummaryservice.groupOptions;
        vm.filters = vm.varRefObj?.filters != null ? angular.copy(vm.varRefObj?.filters) : null;
        vm.knownBuildingSummaryGroups = zonesummaryservice.knownBuildingSummaryGroups;
        vm.sectors = zonesummaryservice.defaultSectors;
        vm.sectorFromLabel = zonesummaryservice.sectorFromLabel;
        vm.isSectorNotEmpty = zonesummaryservice.isSectorNotEmpty;
        vm.applyStoreySelectLogic = zonesummaryservice.applyStoreySelectLogic;
        vm.applyGroupSelectLogic = zonesummaryservice.applyGroupSelectLogic;
        vm.selectedStoreys = null;

        // Data
        vm.zoneSummaryBuildingData = $scope.zoneSummaryBuildingData;
        vm.drawingAreasData = $scope.drawingAreasData ?? selectvariablelinkservice.placeholder_drawingAreas;
        if (vm.zoneSummaryBuildingData?.zones != null) {
            vm.zoneSummary = $scope.zoneSummary;
            vm.envelopeSummary = {}; // Let filters component generate data
            vm.interiorSummary = $scope.interiorSummary;
        } else {
            vm.zoneSummary = selectvariablelinkservice.placeholder_zoneSummary;
            vm.envelopeSummary = selectvariablelinkservice.placeholder_envelopeSummary;
            vm.interiorSummary = selectvariablelinkservice.placeholder_interiorSummary;
        }

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        function initialise() {
            if (vm.varRefObj != null) {
                // Highlight html element for variable that field is linked to
                setTimeout(() => {
                    let section = vm.varRefObj.variablePathObj.sectionValue;
                    let child = vm.varRefObj.variablePathObj?.child;
                    if (!child) {
                        return;
                    }
                    let element = null;
                    switch (section) {
                        case "drawingAreas":
                            element = document.getElementsByClassName(`SECTION-drawingAreas`)[0]
                                              .getElementsByClassName(`FLOOR-${child.value}`)[0]
                                              .getElementsByClassName(`VARIABLE-${child.child.value}`)[0];
                            break;
                        case "drawingAreasTotals":
                            element = document.getElementsByClassName(`SECTION-drawingAreasTotals`)[0]
                                              .getElementsByClassName(`VARIABLE-${child.value}`)[0];
                            break;
                        case "zoneSummary":
                            vm.selectedTab = vm.knownBuildingSummaryGroups.indexOf(child.value);
                            let tabName = child.value;
                            let storeyNum = child.child.type == 'pop' ? vm.zoneSummary[tabName].rows.length-1 : child.child.value;
                            let zoneNum = vm.zoneSummary[tabName].rows[storeyNum].zones.findIndex(z => z.description.toLowerCase() == child.child.child.value.toLowerCase());
                            let variableName = child.child.child.child.value;
                            element = document.getElementsByClassName(`SECTION-zoneSummary`)[0]
                                              .getElementsByClassName(`TAB-${tabName}`)[0]
                                              .getElementsByClassName(`STOREY-${storeyNum}`)[0]
                                              .getElementsByClassName(`ZONE-${zoneNum}`)[0]
                                              .getElementsByClassName(`VARIABLE-${variableName}`)[0];
                            break;
                        case "envelopeSummary":
                            element = document.getElementsByClassName(`SECTION-envelopeSummary`)[0]
                                              .getElementsByClassName(`VARIABLE-${child.value}`)[0]
                                              .getElementsByClassName(`SECTOR-${child.child.value}`)[0];
                            element.scrollIntoView();
                            break;
                        case "interiorSummary":
                            let storey = child.type == 'pop' ? vm.interiorSummary.storeys.length-1 : child.value;
                            element = document.getElementsByClassName(`SECTION-interiorSummary`)[0]
                                              .getElementsByClassName(`STOREY-${storey}`)[0]
                                              .getElementsByClassName(`ROW-${child.child.value}`)[0]
                                              .getElementsByClassName(`VARIABLE-${child.child.child.value}`)[0];
                            element.scrollIntoView();
                            break;
                    }
                    angular.element(element).addClass('variable-selected');
                }, 200);
            }
        }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        // Select Drawing Areas
        vm.selectDrawingAreasVariable = function ($event, floorNum, variableString) {
            vm.varRefObj = { 
                variablePathObj: {
                    sectionValue: "drawingAreas",
                    child: {
                        value: floorNum,
                        child: {
                            value: variableString
                        }
                    }
                }
            };
            vm.processSelect($event);
        }

        // Select Drawing Areas Totals
        vm.selectDrawingAreasTotalsVariable = function ($event, variableString) {
            vm.varRefObj = { 
                variablePathObj: {
                    sectionValue: "drawingAreasTotals",
                    child: {
                        value: variableString
                    }
                }
            };
            vm.processSelect($event);
        }

        // Select Zone Summary variable
        vm.selectZoneSummaryVariable = function ($event, tabString, storeyNum, zoneDescription, variableString) {
            vm.varRefObj = {
                variablePathObj: {
                    sectionValue: "zoneSummary",
                    child: {
                        value: tabString,
                        child: {
                            type: storeyNum == vm.zoneSummary[tabString].rows.length-1 ? "pop" : null, // IF last storey, means Whole Building, so always select Whole Building even if there are less storeys that would otherwise throw error as the storey index would be out of bounds
                            value: storeyNum == vm.zoneSummary[tabString].rows.length-1 ? undefined : storeyNum,
                            child: {
                                type: "whereKeyValue",
                                key: "description",
                                value: zoneDescription,
                                child: {
                                    value: variableString
                                },
                            }
                        }
                    }
                }
            };
            vm.processSelect($event);
        }

        // Select Envelope Summary variable
        vm.selectEnvelopeSummaryVariable = function ($event, variableString, sectorString) {
            vm.varRefObj = {
                variablePathString: `envelopeSummary,${variableString},${sectorString},area`
            };
            vm.varRefObj = {
                variablePathObj: {
                    sectionValue: "envelopeSummary",
                    child: {
                        value: variableString,
                        child: {
                            value: sectorString,
                            child: {
                                value: "area"
                            }
                        }
                    }
                },
                filters: vm.filters,
                savedValue: vm.envelopeSummary[variableString][sectorString].area
            };
            vm.processSelect($event);
        }

        // Select Interior Summary variable
        vm.selectInteriorSummaryVariable = function ($event, storeyNum, rowNum, variableString) {
            vm.varRefObj = {
                variablePathObj: {
                    sectionValue: "interiorSummary",
                    child: {
                        type: storeyNum == vm.interiorSummary.storeys.length-1 ? "pop" : null, // IF last storey, means Whole Building, so always select Whole Building even if there are less storeys that would otherwise throw error as the storey index would be out of bounds
                        value: storeyNum == vm.interiorSummary.storeys.length-1 ? undefined : storeyNum,
                        child: {
                            value: rowNum,
                            child: {
                                value: variableString
                            }
                        }
                    }
                }
            };
            vm.processSelect($event);
        }

        // Process Select
        vm.processSelect = function ($event) {
            angular.element(document.getElementsByClassName('variable-selected')).removeClass('variable-selected');
            angular.element($event.target).addClass('variable-selected');
            setTimeout(vm.save, 80);
        }

        // Cancel
        vm.cancel = function() {
            $mdDialog.cancel();
        }

        // Save
        vm.save = function () {
            $mdDialog.hide(vm.varRefObj);
        }

        initialise();

    }
})();