<div id="options-switcher-container-{{vm.theParentModel.standardHomeModelId}}" ng-blur="vm.switcherBlur()" style="outline:none;">

    <!-- Emptry div that fills screen for click logic -->
    <div class="div-fill-screen"
         ng-style="{ 'display' : vm.anyDropdownOpen() ? 'initial' : 'none' }"
         ng-click="vm.switcherBlur(); $event.stopPropagation();"
    />

    <div class="options-buttons-container">

        <!-- Groups -->
        <div ng-repeat="fieldGroup in vm.theList track by $index" class="dropdowns-group-{{$index}}">
            <!-- Repeat -->
            <div ng-repeat="fieldName in fieldGroup track by $index" class="dropdown-container">
                <!-- Dropdown -->
                <div class="design-options-dropdown" ng-click="$event.stopPropagation();">
                    <!-- Selected -->
                    <div class="options-dropdown-selected" ng-class="{ 'selectable': vm.theParentModel.dropdownFields[fieldName].options.length > 1, 'small': fieldGroup.length > 1 }" ng-mousedown="vm.expandField(fieldName); $event.stopPropagation();">
                        <div>{{vm.theParentModel.dropdownFields[fieldName].selected ? vm.theParentModel.dropdownFields[fieldName].selected.optionName : "-"}}</div>
                        <img ng-if="vm.theParentModel.dropdownFields[fieldName].options.length > 1"
                             class="options-dropdown-icon"
                             src="../../../content/images/arrow-up-skinny.png"
                             ng-style="{ 'transform' : vm.theParentModel.dropdownFields[fieldName].expanded ? 'none' : 'rotateX(180deg)' }" />
                        <md-tooltip class="dropdown-tooltip" md-direction="top" md-delay="vm.tooltipDelay">
                            {{vm.theParentModel.dropdownFields[fieldName].selected ? vm.theParentModel.dropdownFields[fieldName].selected.optionName : "-"}} <div class="dropdown-tooltip-triangle" />
                        </md-tooltip>
                    </div>
                    <!-- Expanded -->
                    <div class="dropdown-options-container" ng-class="{ 'dropdown-expanded': vm.theParentModel.dropdownFields[fieldName].expanded }">
                        <div ng-repeat="option in vm.theParentModel.dropdownFields[fieldName].options track by $index"
                             class="design-option"
                             ng-class="{ 'design-option-selected': option.standardHomeModelVariationOptionId == vm.theParentModel.dropdownFields[fieldName].selected.standardHomeModelVariationOptionId}"
                             ng-click="vm.selectOption(fieldName, option); $event.stopPropagation();">
                            {{option.optionName}}
                        </div>
                    </div>
                </div>
                <!-- Divider -->
                <div ng-if="vm.moreThanOneDropdowns()" class="options-divider" />
            </div>
        </div>

    </div>
    <div style="position: relative;">

        <!-- North arrow which appears after north offset has been filled in... -->
        <img ng-if="vm.showNorthOffset"
             src="/content/images/energy-labs/el-north-arrow-icon.svg"
             style="position: absolute; top: 30px; left: 50%; max-height: 60px;"
             ng-style="{
                'opacity': vm.theParentModel.selectedVariation.optionData.northOffset != null || vm.theParentModel.selectedVariation.optionData.northOffset === 0 ? '100%' : '0%',
                'transform': 'translateX(' + (vm.viewingModelsCount === 3 ? '193px' : '273px') + ') rotate(' + (vm.theParentModel.selectedVariation.optionData.northOffset || 0) + 'deg)'
             }"
             alt="North offset arrow" />

        <!-- First Swticher -->
        <div ng-class="{ 'hide-floorplan': vm.theParentModel.useSecondPlanSwitcher }" style="display:inherit; width: 100%; backface-visibility: hidden; transition: all 0.5s ease; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease;">
            <home-plan-switcher plan-views="vm.firstViewingPlans"
                                show-label="vm.showLabel"
                                view-3d-floor-plans-enabled="vm.show3dFloorPlans"
                                view-zoom-enabled="true"
                                floorplanner-link="vm.theParentModel.selectedVariation.floorplannerLink"
                                plan-view-index="vm.firstPlanViewIndexControl">
            </home-plan-switcher>
            <!-- Icons+Values -->
            <standard-model-data-icons class="icons-row" the-model="vm.firstIconsData"></standard-model-data-icons>
        </div>
        <!-- Second Swticher -->
        <div ng-class="{ 'hide-floorplan': !vm.theParentModel.useSecondPlanSwitcher }" style="position:absolute; top:0; left:0; display: inherit; width: 100%; backface-visibility: hidden; transition:all 0.5s ease; -webkit-transition: all 0.5s ease; -moz-transition: all 0.5s ease;">
            <home-plan-switcher plan-views="vm.secondViewingPlans"
                                show-label="vm.showLabel"
                                view-3d-floor-plans-enabled="vm.show3dFloorPlans"
                                view-zoom-enabled="true"
                                floorplanner-link="vm.theParentModel.selectedVariation.floorplannerLink"
                                plan-view-index="vm.secondPlanViewIndexControl">
            </home-plan-switcher>
            <!-- Icons+Values -->
            <standard-model-data-icons class="icons-row" the-model="vm.secondIconsData"></standard-model-data-icons>
        </div>
    </div>
</div>

<style>

    /* Emptry div that fills screen for click logic */
    .div-fill-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10;
        background-color: transparent;
        cursor: default;
    }

    /* Dropdowns */
    .options-buttons-container {
        position: relative;
        margin-top: -10px;
        display: flex;
        width: calc(100% + 200px); margin-left: -100px;
        justify-content: center;
        z-index: 11;
    }
        .options-buttons-container > div {
            display: flex;
            align-items: center;
        }
        /* Outer Groups Equal Size */
        .dropdowns-group-0, .dropdowns-group-2 { flex: 1; }
        .dropdowns-group-0  { justify-content: end; }
        .dropdowns-group-2 { justify-content: start; }
        /* Middle Group Max Content */
        .dropdowns-group-1 {
            width: max-content;
        }

            /* Container */
            .dropdown-container {
                width: max-content;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

                /* Dropdown */
                .design-options-dropdown {
                    margin-left: 14px;
                    position: relative;
                    width: max-content;
                    height: 30px;
                    border-bottom: solid 3px transparent;
                    user-select: none;
                    overflow: initial;
                }
                .design-options-dropdown:hover {
                    border-bottom-color: #adc43b;
                }

                    /* Selected */
                    .options-dropdown-selected {
                        position: relative;
                        width: 100%;
                        max-width: 200px;
                        height: 100%;
                        padding: 0 2px 7px 2px;
                        box-sizing: border-box;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        column-gap: 8px;
                        cursor: default;
                    }
                    /* IF has more than 1 option to select */
                    .options-dropdown-selected.selectable {
                        cursor: pointer;
                    }
                        /* Tooltip */
                        .dropdown-tooltip {
                            overflow: visible;
                        }
                        .dropdown-tooltip > div {
                            position: relative;
                            width: max-content;
                            min-width: 100px;
                            padding: 5px 14px 4px 14px;
                            text-align: center;
                            font-size: 14px;
                            border-radius: 4px;
                            background-color: #262626;
                            opacity: 1 !important;
                            overflow: visible;
                        }
                            .dropdown-tooltip-triangle {
                                position: absolute;
                                left: 50%; bottom: 0; transform: translate(-50%, 100%);
                                width: 0;
                                height: 0;
                                border-left: 7px solid transparent;
                                border-right: 7px solid transparent;
                                border-top: 5px solid #262626;
                            }

                        /* Text */
                        .options-dropdown-selected > div:first-child {
                            margin-top: 6px;
                            max-width: 160px;
                            text-wrap: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }

                        /* Icon */
                        .options-dropdown-icon {
                            width: 10px;
                            height: 5px;
                            margin-top: 5px;
                        }

                    /* IF there is more than 1 dropdown in this section, make smaller */
                    .options-dropdown-selected.small {
                        max-width: 100px;
                    }
                    .options-dropdown-selected.small > div:first-child {
                        max-width: 60px;
                    }

                    /* Expanded */
                    .dropdown-options-container {
                        position: absolute;
                        bottom: -20px;
                        z-index: 999;
                        width: max-content;
                        overflow: hidden;
                        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.12);
                        background-color: white;
                        opacity: 0;
                        transform: scaleY(0) translateY(100%);
                        transition: all 0.3s ease-out;
                        -webkit-transition: all 0.3s ease-out;
                        -moz-transition: all 0.3s ease-out;
                    }
                    .dropdown-options-container.dropdown-expanded {
                        transform: scaleY(1) translateY(100%);
                        opacity: 1;
                    }

                        /* Option */
                        .design-option {
                            padding: 7px 16px 7px 10px;
                            width: 100%;
                        }
                        .design-option:hover {
                            background-color: #f2f2f2;
                            text-shadow:0px 0px 1px black;
                        }
                        .design-option-selected {
                            text-shadow:0px 0px 1px black;
                            color: #8bc34a
                        }

                /* Divider */
                .options-divider {
                    margin-left: 14px;
                    margin-top: -5px;
                    width: 0;
                    height: 15px;
                    border-left: solid 2px #d3d3d3;
                }
                .dropdowns-group-2 > .dropdown-container:last-child > .options-divider {
                    display: none;
                }

    .show-floorplan {
        transform: rotateY(0deg);
        -webkit-transform: rotateY(0deg);
        -moz-transform: rotateY(0deg);
    }
    .hide-floorplan {
        transform: rotateY(180deg);
        -webkit-transform: rotateY(180deg);
        -moz-transform: rotateY(180deg);
    }

    .flip-show-options {
        transform: rotateY(0deg) !important;
        -webkit-transform: rotateY(0deg) !important;
        -moz-transform: rotateY(0deg) !important;
    }

    .el-card-options-container {
        position: absolute;
        top: 25px;
        left: 0;
        width: 100%;
        height: 95%;
        box-sizing: border-box;
        backface-visibility: hidden;
        transition: all 0.4s ease;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transform: rotateY(180deg);
        -webkit-transform: rotateY(180deg);
        -moz-transform: rotateY(180deg);
        text-align: center;
        font-size: 16px;
        background-color: #eaeaea;
        padding: 10px;
        border-radius: 10px;
    }

    .test-select-dropdown > md-select-menu {
        border: solid 2px orange;
    }

</style>