USE [thermarate];

SELECT [template].[NotificationTemplateId]
      ,[template].[NotificationRuleId]
      ,[rule].[Title] [__NotificationRule]
      ,[rule].[InitialStatusCode] [__InitialStatus]
      ,[rule].[NewStatusCode] [__NewStatus]
      ,[template].[NotificationRecipientTypeCode]
      ,[template].[Subject]
      ,[template].[EmailPlainText]
      ,[template].[EmailHtml]
      ,[template].[Deleted]
      ,[template].[CreatedOn]
      ,[template].[CreatedByName]
      ,[template].[ModifiedOn]
      ,[template].[ModifiedByName]
  FROM [dbo].[RSS_NotificationTemplate] [template]
  INNER JOIN [dbo].[RSS_NotificationRule] [rule] ON [template].[NotificationRuleId] = [rule].[NotificationRuleId]
  WHERE 1=1
	AND [template].[NotificationRuleId] = '0FDC83EE-2EF7-457E-BEFD-B2C6A979F0E6'