<div data-ng-controller="ProjectListController as vm"
     style="margin-top: -20px;"
     class="el-poppins">

  <div class="el-heading-banner"
       style="height: 350px;padding-top:30px;padding-right:23px;">
    <h1 style="margin: 0;">Welcome to the Thermarate EnergyLab</h1>
    <h3 style="margin: 0;">Select a project below to get started.</h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>
  </div>
  <hr class="el-title-divider"/>

  <div class="title">Projects</div>

  <div class="main-container main-content-wrapper">

    <div class="filters-header">
        <div class="filters-container">
            <!-- Search -->
            <div class="filter">
                <div class="filter-label">Search</div>
                <div class="search-input-container">
                    <input class="search-input" type="text" ng-model="vm.searchString" placeholder="Quick Filter" ng-change="vm.applyFilters()">
                    <img src="/content/images/cross.png"
                         class="search-clear-button"
                         ng-show="vm.searchString"
                         ng-click="vm.searchString = null; vm.applyFilters(); $event.stopPropagation()">
                </div>
            </div>
            <!-- Filters -->
            <div ng-repeat="filter in vm.filters"
                 class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">
                <!-- Label -->
                <div class="filter-label">{{vm.getFilterLabel(filter.name)}}</div>
                <!-- Clear Button -->
                <img src="/content/images/cross.png"
                     class="filter-clear-button"
                     ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                />
                <!-- Dropdown -->
                <md-select name="status"
                           class="filter-dropdown"
                           multiple="true"
                           md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                           ng-model="vm.appliedFilters[filter.field]"
                           ng-change="vm.applyFilters()">
                    <md-option ng-repeat="item in vm.filterOptions[filter.field] track by item.value"
                               ng-value="item.value"
                               ng-show="vm.filterCountData[filter.field][item.value] > 0">
                        {{item.name}} ({{vm.filterCountData[filter.field][item.value]}})
                    </md-option>
                </md-select>
            </div>
        </div>
    </div>

    <div class="filter-desciption-container">

        <!-- Number of Items -->
        <div class="current-filter-description" ng-show="!vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters)">
            Showing {{vm.projectList.length}} projects
        </div>
        <div class="current-filter-description filter-description-long" ng-show="vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters)">
            {{vm.filteredProjectList.length}} out of {{vm.projectList.length}} projects match your filters
            (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
        </div>

        <!-- Sort -->
        <sort-list-dropdown sort-by-options="vm.sortByOptions"
                            sort-by-selection="vm.sort"
                            default-sort="vm.defaultSort"
                            default-sort-dir="vm.defaultSortDir"
                            apply-sort="vm.applySort();"></sort-list-dropdown>

    </div>

    <!-- Projects -->
    <div class="grid-container" ng-show="vm.showList">
        <div class="project-item el-card"
             ng-repeat="project in vm.filteredProjectList track by project.projectId"
             ui-sref="energy-labs({ projectId: project.projectId})">
            <!-- Header -->
            <div class="el-card-header">
                <div>{{project.projectName}}</div>
            </div>
            <!-- Logo -->
            <div class="project-logo-container">
                <img class="project-logo" src="{{project.logoFileUrl != null ? project.logoFileUrl : '../../../content/images/project-logo-placeholder.png'}}"/>
            </div>
            <!-- Details -->
            <div class="project-details-container">
                <div class="project-address">{{project.addressText}}</div>
                <div class="project-client">{{project.clientName}}</div>
                <div class="project-type">{{project.projectTypeDescription}}</div>
                <div class="project-active" ng-show="{{project.isActive}}">ACTIVE</div>
                <hr  class="project-line"/>
                <div class="extra-details-container">
                    <div class="extra-detail">
                        <img class="designs-icon" src="/content/images/project-design.png" />
                        <div class="extra-detail-text">{{project.designsCount}}</div>
                    </div>
                    <div class="extra-detail">
                        <img class="users-icon" src="/content/images/project-user.png" />
                        <div class="extra-detail-text">{{project.usersCount}}</div>
                    </div>
                    <div class="extra-detail">
                        <img class="tools-icon" src="/content/images/project-tool.png" />
                        <div class="extra-detail-text">{{project.toolsCount}}</div>
                    </div>
                    <div class="project-modified-on">{{project.modifiedOn | date: 'dd MMMM yyyy'}}</div>
                </div>
            </div>
        </div>
    </div>

  </div>

</div>

<style>
    .title {
        width: 2047px;
        margin: 30px auto 0px auto;
        max-width: 93%;
        font-size: 20px;
    }

    .main-container {
        width: calc(100% - 70px);
    }

    .filters-header {
        margin-top: 62px;
        margin-left: 15px;
        width: 100%;
        height: min-content;
        display: flex;
        flex-wrap: wrap;
        gap: 35px;
        justify-content: space-between;
        position: relative;
    }

        .filters-container {
            float: right;
            display: flex;
            column-gap: 25px;
            row-gap: 20px;
        }

            .filter {
                position: relative;
                width: 240px;
            }

                .search-input-container {
                    position: relative;
                    width: 100%;
                }

                .search-input {
                    width: 100%;
                    height: 52px;
                    margin-top: -6px;
                    padding: 7px 10px 6px 10px;
                    box-sizing: border-box;
                    border: none;
                    background-color: #eeeeee;
                    color: black !important;
                }
                .search-input:focus-visible { border: none !important; }
                .search-input::placeholder { color: black !important; }
                .search-input:-ms-input-placeholder { color: black !important; }

                .search-clear-button {
                    position: absolute;
                    right: 6px;
                    top: 46%;
                    transform: translateY(-50%);
                    z-index: 50;
                    width: 9px;
                    height: auto;
                    padding: 4px;
                    border-radius: 50%;
                    cursor: pointer;
                }
                .search-clear-button:hover {
                    background-color: #f5f5f5;
                }

                .filter-label {
                    position: absolute;
                    top: -32px;
                    left: 15px;
                    font-size: 12px;
                }

                .filter-clear-button {
                    display: none;
                    position: absolute;
                    right: 3px;
                    bottom: 17px;
                    z-index: 50;
                    width: 9px;
                    height: auto;
                    padding: 4px;
                    border-radius: 50%;
                    cursor: pointer;
                }

                    .filter-clear-button:hover {
                        background-color: #f5f5f5;
                    }

                .filter.options-selected > .filter-clear-button {
                    display: inherit !important;
                }

                .filter.options-selected .md-select-icon {
                    margin-left: -36px;
                }

                    .filter-dropdown {
                        margin: -6px 0 0 2px;
                        width: 100%;
                        height: 52px;
                        padding: 7px 10px 6px 10px;
                        box-sizing: border-box;
                        background-color: #eeeeee;
                        color: black !important;
                    }

                    .filter-dropdown span {
                        margin-top: -2px;
                    }

    .filter-desciption-container {
        display: flex;
        justify-content: space-between;
    }

        .current-filter-description {
            margin-left: 20px;
            margin-top: 46px;
            color: #7b7b7b;
            font-size: 12px;
        }

            .clear-filters-text {
                margin-left: 4px;
            }

        .sort-container {
            margin-top: 44px;
            margin-right: 20px;
            color: black;
        }

    .grid-container {
        margin-top: 26px;
        width: calc(100% + 15px);
        display: grid;
        grid-template-columns: min-content min-content min-content min-content;
        justify-content: start;
        column-gap: 26px;
        row-gap: 15px;
    }

        .project-item {
            width: 420px;
            height: 535px;
            padding: 18px 15px 15px 15px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            overflow: hidden;
            position: relative;
        }

            .el-card-header {
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                margin-left: -15px;
                margin-right: -15px;
                margin-top: -18px;
                margin-bottom: 20px;
                padding-top: 21px;
            }

            .project-logo-container {
                margin-top: 20px;
                margin-bottom: 20px;
                width: 100%;
                height: 265px;
                display: flex;
                justify-content: center
            }

                .project-logo {
                    object-fit: cover;
                    max-width: 100%;
                }

            .project-details-container {
                margin-top: 20px;
                position: relative;
            }

                .project-name {
                    display: flex;
                    justify-content: space-between;
                    font-size: 22px;
                }

                .project-client {
                    margin-top: 8px;
                }

                .project-type {
                    margin-top: 10px;
                }

                .project-address {
                    height: 24px;
                    margin-top: 7px;
                    display: flex;
                    justify-content: space-between;
                }

                .project-active {
                    position: absolute;
                    bottom: 59px;
                    right: 10px;
                    height: min-content;
                    font-size: 15px;
                    background-color: #adc43b;
                    padding: 4px 8px 4px 8px;
                    border-radius: 4px;
                    color: white;
                    font-weight: bold;
                }

                .project-line {
                    margin-top: 16px;
                    border: none;
                    border-bottom: 1px solid #e3e3e3;
                }

                .extra-details-container {
                    margin-top: 10px;
                    display: flex;
                    justify-content: space-between;
                    padding-right: 15px;
                }

                    .extra-detail {
                        display: flex;
                        gap: 14px;
                        margin-right: 22px;
                    }

                        .designs-icon {
                            margin-top: 4px;
                            width: 30px;
                            height: 30px;
                        }

                        .users-icon {
                            margin-top: 8px;
                            width: 24px;
                            height: 24px;
                        }

                        .tools-icon {
                            margin-top: 8px;
                            width: 24px;
                            height: 24px;
                        }

                        .extra-detail-text {
                            margin-top: 10px;
                        }

                    .project-modified-on {
                        margin-top: 10px;
                    }

    .widget-pager {
        margin-top: 25px;
    }

    /* Medium Screen */

    @media screen and (max-width: 1875px) {

        .main-container {
            max-width: 1320px;
        }

        .grid-container {
            grid-template-columns: min-content min-content min-content;
        }

        .filter {
            width: 236px;
        }

        .sort-container {
            margin-right: 22px;
        }
    }

    /* Small Screen */

    @media screen and (max-width: 1590px) {
        .main-container {
            width: 880px;
        }

        .grid-container {
            grid-template-columns: min-content min-content;
            margin-top: 26px;
            width: calc(100% + 5px);
        }

        .filters-header {
            margin-top: 32px;
            margin-left: 18px;
            gap: 0;
        }

        .filters-main-container {
            display: flex;
            flex-wrap: wrap;
        }

        .filters-container {
            margin-top: 30px;
        }

        .filter {
            width: 146px;
        }

        .sort-container {
            margin-right: 28px;
        }
    }

    /* Tiny Screen */

    @media screen and (max-width: 1110px) {
        .main-container {
            width: 435px;
        }

        .current-filter-description {
            flex-wrap: wrap;
            margin-left: 13px;
        }

        .filter-description-long {
            margin-top: 34px;
        }

        .sort-label {
            display: none;
        }

        .grid-container {
            grid-template-columns: min-content;
            margin-top: 28px;
        }

        .filters-header {
            margin-top: 62px;
            margin-left: 4px;
            display: block;
        }

        .filters-main-container {
            display: block;
        }

        .filters-container {
            float: none;
            margin-top: 24px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            row-gap: 50px;
        }

        .filter {
            width: 180px;
        }

        .filter-desciption-container {
            margin-left: -6px;
        }

        .sort-container {
            margin-right: 18px;
        }

        .sort-dropdown {
            width: 140px;
            height: 40px;
            right: 0;
            margin-left: 16px;
        }

        .el-heading-banner h1 {
            margin-left: 14px !important;
            font-size: 30px;
        }

        .el-heading-banner h3 {
            margin-left: 14px !important;
            font-size: 22px;
        }
    }

</style>
