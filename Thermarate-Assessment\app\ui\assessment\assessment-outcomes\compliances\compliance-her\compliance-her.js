(function () {
    'use strict';
    angular
        .module('app')
        .component('complianceHer', {
            bindings: {
                assessment: '<',
                complianceData: '<',
                isOption: '<',
                jobFiles: '<',
                assessmentSoftwareList: '<',
                purchaseOrderDefault: '<',
                isLocked: '<',
                complianceMethodList: '<',
                complianceTypeChanged: '&',
                onComplianceChanged: '&'
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/compliances/compliance-her/compliance-her.html',
            controller: ComplianceHer,
            controllerAs: 'vm'
        });

    ComplianceHer.$inject = ['energyloadlimitservice', '$rootScope', '$scope', '$timeout', 'security', 'clientservice', 'compliancemethodservice', 'common', '$mdDialog', 'certificationservice'];

    function ComplianceHer(energyloadlimitservice, $rootScope, $scope, $timeout, securityservice, clientservice, compliancemethodservice, common, $mdDialog, certificationservice) {
        var vm = this;
        var _formValid = false;

        vm.complianceData.heatingLoadLimit = null;
        vm.complianceData.coolingLoadLimit = null;
        vm.houseEnergyRating = 0;

        vm.calculateCompliance = calculateCompliance;
        vm.calculateTotal = calculateTotal;
        vm.purchaseOrderList = clientservice.purchaseOrderSettingsList;

        vm.$onInit = onInit;
        vm.$onChanges = onChanges;

        // Permissions
        vm.permission_field_certification_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__view']);
        vm.permission_field_certification_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__edit']);
        vm.permission_field_assessmentmethod_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__view']);
        vm.permission_field_assessmentmethod_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__edit']);
        vm.permission_field_assessmentsoftware_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__view']);
        vm.permission_field_assessmentsoftware_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit']);
        vm.permission_field_herrequired_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__view']);
        vm.permission_field_herrequired_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__edit']);
        vm.permission_action_overrideenergyloads = securityservice.immediateCheckRoles(['assessment_actions__overrideenergyloads']);

        // Get client available values.
        vm.availableHouseEnergyRatings = vm.assessment.job.client.clientOptions.availableHouseEnergyRatings;
        vm.availableHouseEnergyRatings = vm.availableHouseEnergyRatings?.sort((a, b) => a - b);

        // Convert to and from set to remove duplicates if present.
        vm.availableHouseEnergyRatings = [...new Set(vm.availableHouseEnergyRatings)];

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        let initialised = false;
        let unwatchA = $scope.$watch('vm.complianceData.proposed.conditionedFloorArea', calculateCompliance);
        let unwatchB = $scope.$watch('vm.complianceData.proposed.unconditionedFloorArea', calculateCompliance);
        let unwatchC = $scope.$watch('vm.complianceData.proposed.attachedGarageFloorArea', calculateCompliance);
        let unwatchD = $scope.$watch('vm.complianceData.proposed.heatingLoadLimitCorrectionFactor', calculateCompliance);
        let unwatchE = $scope.$watch('vm.complianceData.proposed.coolingLoadLimitCorrectionFactor', calculateCompliance);
        let unwatchF = $scope.$watch('vm.complianceData.proposed.scratchFileProcessedWatchProperty', calculateCompliance);

        $scope.$on("$destroy", () => {
            if(unwatchA != null)
                unwatchA();

            if(unwatchB != null)
                unwatchB();

            if(unwatchC != null)
                unwatchC();

            if(unwatchD != null)
                unwatchD();

            if(unwatchE != null)
                unwatchE();

            if(unwatchF != null)
                unwatchF();
        });

        function onInit() {

            if (vm.complianceData.assessmentSoftwareCode == null && vm.assessmentSoftwareList !== undefined) {
                vm.complianceData.assessmentSoftwareCode = vm.assessment.assessmentSoftwareCode;
            }

            if (vm.assessment != null) {
                calculateCompliance();
            }

            $scope.$watch('vm.complianceData.proposed.lowestLivingAreaFloorType', function () {
                calculateCompliance();
            });
            $scope.$watch('complianceHerForm.$valid', function (newVal) {
                _formValid = newVal;
                calculateCompliance();
            });

            /** Watch for changes to the requiredHouseEnergyRating and format as needed to ensure we always show 1 decimal point & match up with underlying model. */
            $scope.$watch('vm.complianceData.requiredHouseEnergyRating', function (newVal) {

                if (typeof newVal === 'number')
                    vm.tempRequiredHouseEnergyRating = newVal.toFixed(1);
                else
                    vm.tempRequiredHouseEnergyRating = newVal;
            });

            vm.complianceData.availableSoftware = compliancemethodservice.determineAvailableSoftware(vm.assessmentSoftwareList, vm.complianceData.complianceMethod.complianceMethodCode);
            vm.tempRequiredHouseEnergyRating = vm.complianceData?.requiredHouseEnergyRating?.toFixed(1);

            $timeout(() => {
                initialised = true;
            }, 500);

        }
        function onChanges(changes) {
            if (changes.complianceData && changes.complianceData.currentValue) {
                calculateCompliance();
            }
        }

        // Calculates required energy ratings if the needed data is supplied, and then calls a function which actually
        // checks whether compliance was achieved (calculateCompliance2).
        async function calculateCompliance() {

            if (!initialised) {
                return;
            }

            let compliant;

            // If the energy values are overriden, we can skip the actual compliance calc.
            if(vm.complianceData.proposed.overrideEnergyLoads && vm.complianceData.complianceMethod.complianceMethodCode !== "CMPerfELL") {

                let metRating = vm.complianceData.proposed.houseEnergyRatingOverride >= vm.complianceData.requiredHouseEnergyRating;

                let metHeating = vm.complianceData.reference.heating === -1
                    ? true
                    : vm.complianceData.proposed.heating <= vm.complianceData.reference.heating;

                let metCooling = vm.complianceData.reference.cooling === -1
                    ? true
                    : vm.complianceData.proposed.cooling <= vm.complianceData.reference.cooling;

                compliant = metRating && metHeating && metCooling && vm.complianceData.isComplianceValid;

            } else {
                compliant = await compliancemethodservice.calculateHERCompliance(vm.assessment, vm.complianceData, vm.isOption);
            }

            // Form must also be filled. To prevent this from un-selecting already selected option
            // (e.g. when the form is loading on startup) we skip this check if the item is ALREADY selected.
            if (_formValid === false && !vm.complianceData.isSelected)
                compliant = false;

            vm.complianceData.isCompliant = compliant;
            if (!compliant) {
                vm.complianceData.isSelected = false;

            }

            // If we're compliant AND it's NO OPTIONS 
            // OR
            // If this is the baseline and we're compliant
            // Then-select the baseline
            if ((compliant && vm.assessment.allComplianceOptions.length === 1) ||
                compliant && vm.complianceData.isBaselineSimulation === true)
                vm.complianceData.isSelected = true;

            vm.onComplianceChanged({ compliant: compliant });

        }

        function calculateTotal(building) {
            building.totalEnergyLoad = building.heating + building.cooling;

            // force refresh of input to run 'formatted-number' directive...
            common.forceBlurInputWithId("proposedTotalEnergyLoadInput" + vm.complianceData.complianceOptionsId);
        }

        vm.determineUINumber = function () {
            return vm.complianceData.optionIndex;
        }

        vm.classificationChanged = function (value) {
            vm.complianceData.classification = value;
        }

        vm.isSoftwareAvailable = function (code) {
            return vm.complianceData.availableSoftware?.find(x => x.assessmentSoftwareCode === code) != null;
        }

        /** We have to take our temp (string) value and convert it to the value which will be saved (decimal) */
        vm.handleReqHERFormat = function () {
            vm.complianceData.requiredHouseEnergyRating = parseFloat(vm.tempRequiredHouseEnergyRating);
            calculateCompliance();
        }

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.assessment.worksDescription.worksDescriptionCode);
        }

        vm.showLoadsUpdateModal = function(building, buildingType, title) {

            let modalScope = $rootScope.$new();

            modalScope.title = title;
            modalScope.disabled = vm.isLocked;
            modalScope.assessment = vm.assessment;
            modalScope.option = vm.complianceData;
            modalScope.building = building;
            modalScope.buildingType = buildingType;
            modalScope.energyLoadUnits = vm.complianceData.assessmentSoftware.energyLoadUnits;

            // Modal Inputs
            modalScope.heating = building.heating;
            modalScope.cooling = building.cooling;
            modalScope.totalEnergyLoad = building.totalEnergyLoad;

            // We only care about the override, which could be null..?
            // Can mean equivalent OR calculated.
            modalScope.herOverride = building.houseEnergyRatingOverride;

            modalScope.heatingOriginal = building.heatingOriginal;
            modalScope.coolingOriginal = building.coolingOriginal;
            modalScope.totalEnergyLoadOriginal = building.totalEnergyLoadOriginal;

            modalScope.overrideEnergyLoads = building.overrideEnergyLoads || false;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/loads-override-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(data => {

               building.heating = data.heating;
               building.cooling = data.cooling;
               building.totalEnergyLoad = data.totalEnergyLoad;
               building.overrideEnergyLoads = data.overrideEnergyLoads || false;
               building.houseEnergyRatingOverride = data.herOverride;

               calculateCompliance();

            });

        }

        vm.updateDataLinkedToCertification = function() {
            setTimeout(() => {
                certificationservice.updateDataLinkedToCertification(vm.assessment, vm.complianceData);
            }, 100);
        }

    }
})();