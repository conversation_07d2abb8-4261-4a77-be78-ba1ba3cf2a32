// Name: userauditservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'userauditservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', userauditservice]);

function userauditservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var initPromise,
            initFailed;
        var canceller = null;
        var useListCache = false;
        var userauditData = {};
        var currentFilter = "";
        var baseUrl = config.servicesUrlPrefix + 'useraudit/';
        userauditData.userauditList = [];

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            userauditList: function () { return userauditData.userauditList },
            useraudit: function () { return userauditData.useraudit },
            getUserAudit: function (auditRecId) { return getUserAudit(auditRecId) },
            saveUserAudit: function (useraudit) { return saveUserAudit(useraudit) },
            deleteUserAudit: function (auditRecId) { return deleteUserAudit(auditRecId) },
        };

        return service;

        //#region main application operations
        // ----------------------------------

        function initialise() {

            if (initialised == true) {
                return $q.when(); // Already Initialised.
            }

            if (initPromise && !initFailed) {
                return initPromise; // already initialized/ing
            }
            initFailed = false;

            initPromise = null;
            // Get all the data from the server
            initPromise = getData().then(initialised = true);

            return initPromise;

        } // end initialise()


        function refresh(forFilter) {

            initPromise = null;

            // Get all the data from the server
            initPromise = getData(forFilter).then(initialised = true);

            return initPromise;
        }


        function getList(forFilter, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = {};
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Users list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Fetch the requested auditRecId from the server
        function getUserAudit(auditRecId) {
            if (auditRecId == undefined) {
                auditRecId = 0
            }
            //Get useraudit from the Server 
            return $http({
                url: '../api/useraudit/Get/' + auditRecId,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {

                userauditData.useraudit = resp.data; //Assign data to useraudit
            }
            function fail(error) {
                var msg = "Error getting useraudit list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Save useraudit Detail on Server Call
        function saveUserAudit(useraudit) {
            var isNew = useraudit.auditRecId == null ? true : false;

            //Save UserAudit Detail on the Server
            return $http.post('../api/useraudit/Post', useraudit)
                .then(success, fail);

            function success(resp) {
                userauditData.useraudit = resp.data;
                log.logSuccess('UserAudit Saved.');
                refresh(currentFilter); // Force the list of useraudit to be refreshed.
            }
            function fail(error) {
                var msg = "Error saving useraudit detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Delete useraudit Detail on Server Call
        function deleteUserAudit(auditRecId) {
            initPromise = null;
            // Delete useraudit Detail from the server
            initPromise = $http.delete('../api/useraudit/Delete/' + auditRecId).then(success, fail);

            function success(resp) {
                log.logSuccess('UserAudit Deleted.');
                refresh(currentFilter);  // Force the list of useraudit to be refreshed.
            }
            function fail(error) {
                var msg = "Error deleting useraudit detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

    }

})();
