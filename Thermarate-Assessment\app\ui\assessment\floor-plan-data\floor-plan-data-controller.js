(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "floorPlanData";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/assessment/floor-plan-data/floor-plan-data.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {

            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                source: '<', // Building OR Template
                complianceOption: '<',
                sourceType: '<',
                isTemplate: '<',                // If true, certain inputs are hidden.
                disabled: '<'
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: FloorPlanDataController,
            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    FloorPlanDataController.$inject = ['$scope', '$rootScope', 'common','uuid4', '$q', '$mdDialog', 'zonetypeservice'];

    function FloorPlanDataController($scope, $rootScope, common, uuid4, $q, $mdDialog, zonetypeservice) {

        let vm = this;

        initialize();

        vm.zoneTypeList = [];

        vm.sortedSpacesFunc = sortedSpacesFunc;
        vm.addSpace = addSpace;
        vm.cloneSpace = cloneSpace;
        vm.clearSpace = clearSpace;
        vm.removeSpace = removeSpace;
        vm.removeSpaces = removeSpaces;
        vm.renumberSpaces = renumberSpaces;
        vm.toggleBulkEditAll = toggleBulkEditAll;
        vm.showBulkEdit = showBulkEdit;
        vm.noneSelected = noneSelected;
        vm.disabledEx = disabledEx;
        vm.sortBy = sortBy;

        function initialize() {

            if(vm.source.spaces == null)
                vm.source.spaces = [];

            zonetypeservice.getList().then((data) => {
                vm.zoneTypeList = data.data.filter(x => x.availableFor === 'all' || x.availableFor === 'spaces')
            });
        }

        function sortedSpacesFunc() {

            return vm.sortedSpaces != null
                ? vm.sortedSpaces
                : vm.source.spaces;
        }

        /** spaceList must match a SpaceActivityCode without the preceeding "ZA" */
        function sortBy(spaceList, column) {

            if(vm[spaceList + "SortInfo"] == null)
                vm[spaceList + "SortInfo"]  = new SortInfo();

            const matchingSpaces = vm.source.spaces;

            common.setSort(column, vm[spaceList + "SortInfo"]);
            // TODO: If we need to sort multiple space types (like zones), then
            // this will need to be updated. Refer to zone-list-controller.
            vm.sortedSpaces = common.applySort(matchingSpaces, vm[spaceList + "SortInfo"]);

            // console.log("vm.sortedSpaces: ", vm.sortedSpaces);
            // console.log("vm.SpaceSortInfo: ", vm.SpaceSortInfo);
        }

        function renumberSpaces() {

            if (vm.sortCol == null && vm.sortDir == null) {
                for (let i = 0; i < vm.source.spaces.length; i++) {
                    vm.source.spaces[i].sortOrder = i;
                }
            }
        }

        function addSpace(spaces, leadChar) {

            if (vm.source.spaces == null) {
                vm.source.spaces = [];
            }

            vm.source.spaces.push({
                createdOn: new Date().toUTCString(),
                zoneId: uuid4.generate(),
                linkId: uuid4.generate(),
                floorArea: null,
                zoneNumber: determineNextFreeSpaceNumber(leadChar),
                zoneNumberSource: "AUTO",
            });

            renumberSpaces();

            vm.sortedSpaces = common.applySort(vm.source.spaces, vm.SpaceSortInfo);
        }

        function cloneSpace(row, leadChar) {

            if (row == null)
                return;

            let index = vm.source.spaces.findIndex(x => x === row); // Original index

            let n = angular.copy(row);
            n.zoneId = uuid4.generate();
            n.zoneNumber = determineNextFreeSpaceNumber(leadChar);
            n.zoneNumberSource = "AUTO";
            n.zoneDescription = (row.zoneDescription ?? "") + " - Copy";

            vm.source.spaces.splice(index + 1, 0, n);
            renumberSpaces();

            vm.sortedSpaces = common.applySort(vm.source.spaces, vm.SpaceSortInfo);
        }

        function clearSpace(space) {

            // NOTE: Doesn't nullify Space Number on purpose (for now...)
            space.zoneNumber = null;
            space.zoneDescription = null;
            space.zoneType = null;
            space.floorArea = null;
            space.perimeter = null;
            space.ceilingHeight = null;
            space.storey = null;
            space.zoneNumberSource = "MANUAL";

        }

        function removeSpace(row) {

            for (var ii = 0, ilen = vm.source.spaces.length; ii < ilen; ii++) {
                if (vm.source.spaces[ii].zoneId === row.zoneId) {
                    vm.source.spaces.splice(ii, 1);
                    renumberSpaces();

                    vm.sortedSpaces = common.applySort(vm.source.spaces, vm.SpaceSortInfo);
                    return;
                }
            }

        }

        function removeSpaces(spaces) {
            vm.source.spaces = [];
        }

        /**
         * Toggles bulk edit selection on/off for ALL rows.
         *
         * @param {any} toggleState If true, everything will be selected. False, everything deselected.
         * @param {any} code Exterior and Interior spaces have a different property tracking if they have been selected. This is to select which.
         */
        function toggleBulkEditAll(toggleState, code, spaces) {

            spaces.forEach(row => {
                row[code] = toggleState;
            });
        }

        function showBulkEdit(code, bulkCode, spaceList, leadChar) {

            var newScope = $rootScope.$new();
            newScope.zoneTypeList = vm.zoneTypeList;
            newScope.storeys = vm.source.storeys;

            $mdDialog.show({
                templateUrl: 'app/ui/assessment/floor-plan-data/building-spaces-bulk-edit.html',
                scope: newScope,
            })
                .then(function (response) {

                    // Loop over all Spaces and apply the new data from our bulk edit modal where appropriate
                    for (let i = 0; i < spaceList.length; i++) {

                        if (spaceList[i][code] == null ||
                            spaceList[i][code] === false)
                            continue;

                        spaceList[i][code] = false;

                        if (response.bulkEditAction === 'EDIT')
                            spaceList[i] = common.nullAwareMerge(spaceList[i], response);
                        else if (response.bulkEditAction === 'CLEAR')
                            clearSpace(spaceList[i]);
                        else if (response.bulkEditAction === 'DELETE') {
                            removeSpace(spaceList[i]);
                            i--;
                        } else if (response.bulkEditAction === 'COPY') {
                            cloneSpace(spaceList[i], leadChar);
                            i++;
                        }
                    }

                    vm[bulkCode] = false;

                }, function () {
                    // Cancelled, do nothing.
                });
        }

        function noneSelected(code) {
            return !vm.source?.spaces?.some(x => x[code]);
        }

        /** Determines which is the next free space number based on (existing spaces, their number). */
        function determineNextFreeSpaceNumber(leadChar) {

            leadChar = leadChar.toUpperCase();

            // Get all existing spaces that match the format Z### (Regardless of SOURCE).
            let pattern = `${leadChar}(\\d{3})`
            let regEx = new RegExp(pattern);

            let matches = [];
            let existingSpaces = vm.source?.spaces;

            existingSpaces.forEach(space => {
                let matching = regEx.exec(space.zoneNumber);

                // We add a check for length so that things like "ZZ0044", "Z002z" etc don't match.
                if (matching != null && matching.input.length == 4)
                    matches.push(space.zoneNumber);
            });

            // Convert to actual numbers (I.e. Z001 => 1 etc);
            let converted = [];
            matches.forEach(zn => {
                converted.push(parseInt(zn.slice(1)));
            });

            // Loop over until we find a free slot.
            for (let i = 1; i < 10000; i++) {
                if (!converted.some(c => c === i))
                    return leadChar + common.addLeadingZero(i, 3);
            }

            // Failure state.
            return "!###";

        }

        vm.disabledEx = disabledEx;
        function disabledEx () {
            return vm.disabled;
        }

    }

})();