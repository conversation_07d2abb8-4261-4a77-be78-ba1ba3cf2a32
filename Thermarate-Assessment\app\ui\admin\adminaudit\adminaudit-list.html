<section id="adminaudit-list-view" class="main-content-wrapper" data-ng-controller="AdminauditListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Adminaudit"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.adminauditList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="adminauditList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="componentName" class="can-sort text-left">Component Name</th>
                        <th st-sort="itemName" class="can-sort text-left">Item Name</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="changeType" class="can-sort text-left">Change Type</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.adminauditList" class="list-row clickable">
                        <td data-title="Name" ng-click="vm.goToAdminAudit(row.adminAuditId)">
                            <div class="text-left" style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{row.componentName}}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Item Name" class="text-left" ng-click="vm.goToAdminAudit(row.adminAuditId)">{{::row.itemName }}</td>
                        <td data-title="Description" class="text-left" ng-click="vm.goToAdminAudit(row.adminAuditId)">{{::row.description }}</td>
                        <td data-title="Change Type" class="text-left" ng-click="vm.goToAdminAudit(row.adminAuditId)">{{::row.changeType }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>

    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>