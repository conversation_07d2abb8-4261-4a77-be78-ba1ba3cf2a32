(function () {
    // The MaptouserrolesUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'MaptouserrolesUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'businessroleservice', 'rolesservice', 'maptouserrolesservice', maptouserrolesUpdateController]);
function maptouserrolesUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  businessroleservice, rolesservice, maptouserrolesservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Map To User Roles';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.mapId = null;
        vm.maptouserroles = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Map To User Roles";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.mapId = $scope.mapId;
            }
            vm.hideActionBar = true;
        } else {
            vm.mapId = $stateParams.mapId;
        }

        // Get data for object to display on page
        var mapIdPromise = null;
        if (vm.mapId != null) {
            mapIdPromise = maptouserrolesservice.getMapToUserRoles(vm.mapId)
            .then(function (data) {
                if (data != null) {
                    vm.maptouserroles = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.employeeRoleList = [];
        var employeeRolePromise = businessroleservice.getList()
            .then(function(data){
                vm.employeeRoleList = data.data;
            });

        // Functions to get data for Typeahead
        vm.getroless = function(searchTerm) {
            var filter = [{ field: "roleName", operator: "startswith", value: searchTerm }];
            return rolesservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateRoles', function(event){
            event.stopPropagation();
            vm.createRoles() // function to launch add modal;
            }));

        vm.createRoles = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("maptouserroles-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                maptouserrolesservice.createMapToUserRoles(vm.maptouserroles).then(function(data){
                    vm.maptouserroles = data;
                    vm.mapId = vm.maptouserroles.mapId;
                    vm.cancel();
                });
            }else{
                maptouserrolesservice.updateMapToUserRoles(vm.maptouserroles).then(function(data){
                    if (data != null) {
                        vm.maptouserroles = data;
                        vm.mapId = vm.maptouserroles.mapId;
                    }
                });
            }
        }

        vm.delete = function () {
            maptouserrolesservice.deleteMapToUserRoles(vm.mapId).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            maptouserrolesservice.undoDeleteMapToUserRoles(vm.mapId).then(function () {
                vm.cancel();
            });
        }

    }
})();