(function () {
    'use strict';
    var controllerId = 'ToggleSettingModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', toggleSettingModalController]);
    function toggleSettingModalController($scope, $mdDialog) {
        var vm = this;

        vm.settingName = $scope.settingName || 'Toggle Setting';
        vm.applyToAllLevels = true; // Default to apply to all levels

        vm.confirm = function () {
            $mdDialog.hide({
                applyToAllLevels: vm.applyToAllLevels
            });
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }
    }
})();
