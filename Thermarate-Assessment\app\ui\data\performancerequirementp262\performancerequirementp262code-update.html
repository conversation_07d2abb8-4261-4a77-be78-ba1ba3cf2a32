<form name="performancerequirementp262codeform" class="main-content-wrapper" novalidate data-ng-controller='Performancerequirementp262codeUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div>
                    <md-card>
                        <md-card-header>
                            Performance Requirement P262 Code
                        </md-card-header>
                        <md-card-content>

<!-- ******** Performance Requirement P262 Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Performance Requirement P262 Code</label>
                                <input type="text" name="performanceRequirementP262Code" 
                                        ng-model="vm.performancerequirementp262code.performanceRequirementP262Code" md-autofocus 
                                        md-maxlength="20"
                                        required
                                    />
                                <div ng-messages="performancerequirementp262codeform.performanceRequirementP262Code.$error">
                                    <div ng-message="required">Performance Requirement P262 Code is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Description</label>
                                <input type="text" name="description" 
                                        ng-model="vm.performancerequirementp262code.description"  
                                        md-maxlength="100"
                                    />
                                <div ng-messages="performancerequirementp262codeform.description.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.performancerequirementp262code"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="performancerequirementp262codeform.$invalid" ng-show="vm.performancerequirementp262code.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.performancerequirementp262code.performanceRequirementP262Code!=null && vm.performancerequirementp262code.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.performancerequirementp262code.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
