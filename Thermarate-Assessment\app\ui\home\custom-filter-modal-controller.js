(function () {
    'use strict';
    var controllerId = 'CustomFilterCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'clientservice', 'userservice', 'projectdescriptionservice', 'priorityservice', customFilterModalController]);
    function customFilterModalController($scope, $mdDialog, clientservice, userservice, projectdescriptionservice, priorityservice) {
        var vm = this;

        vm.response = {
            clientId: null,
            clientName: null,
            contactId: null,
            creatorFullName: null,
            assessorUserId: null,
            assessorUserFullName: null,
            projectDescriptionCode: null,
            projectDescriptionDescription: null,
            overdueCode: null,
            overdueDescription: null,
            priorityCode: null,
            priorityDescription: null,
        };
        if ($scope.existingFilter != null) {
            vm.response.clientId = $scope.existingFilter.clientId;
            vm.response.contactId = $scope.existingFilter.contactId;
            vm.response.assessorUserId = $scope.existingFilter.assessorUserId;
            vm.response.projectDescriptionCode = $scope.existingFilter.projectDescriptionCode;
            vm.response.overdueCode = $scope.existingFilter.overdueCode;
            vm.response.priorityCode = $scope.existingFilter.priorityCode;
        }

        vm.overdueList = [{ overdueCode: true, description: 'Overdue' }, {overdueCode: false, description: 'Not Overdue'}];

        vm.clientList = [];
        clientservice.getList()
            .then(function (data) {
                vm.clientList = data.data;
            });

        vm.employeeList = [];
        userservice.getList()
            .then(function (data) {
                vm.employeeList = data.data;
            });

        vm.projectDescriptionList = [];
        projectdescriptionservice.getList()
            .then(function (data) {
                vm.projectDescriptionList = data.data;
            });

        vm.priorityList = [];
        priorityservice.getList()
            .then(function (data) {
                vm.priorityList = data.data;
            });

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.setDescriptions = function () {
            var list = [];
            if (vm.response.clientId) {
                for (var ii = 0, ilen = vm.clientList.length; ii < ilen; ii++) {
                    if (vm.clientList[ii].clientId == vm.response.clientId) {
                        vm.response.clientName = vm.clientList[ii].clientName;
                        list.push({
                            primaryKey: "clientId",
                            blackLabel: vm.response.clientName,
                            blueLabel: "Client",
                        });
                        break;
                    }
                }
            }
            if (vm.response.contactId) {
                for (var ii = 0, ilen = vm.contactList.length; ii < ilen; ii++) {
                    if (vm.contactList[ii].contactId == vm.response.contactId) {
                        vm.response.creatorFullName = vm.contactList[ii].fullName;
                        list.push({
                            primaryKey: "contactId",
                            blackLabel: vm.response.creatorFullName,
                            blueLabel: "Contact",
                        });
                        break;
                    }
                }
            }
            if (vm.response.assessorUserId) {
                for (var ii = 0, ilen = vm.employeeList.length; ii < ilen; ii++) {
                    if (vm.employeeList[ii].employeeId == vm.response.assessorUserId) {
                        vm.response.assessorUserFullName = vm.employeeList[ii].fullName;
                        list.push({
                            primaryKey: "assessorUserId",
                            blackLabel: vm.response.assessorUserFullName,
                            blueLabel: "Assessor",
                        });
                        break;
                    }
                }
            }
            if (vm.response.projectDescriptionCode) {
                for (var ii = 0, ilen = vm.projectDescriptionList.length; ii < ilen; ii++) {
                    if (vm.projectDescriptionList[ii].projectDescriptionCode == vm.response.projectDescriptionCode) {
                        vm.response.projectDescriptionDescription = vm.projectDescriptionList[ii].description;
                        list.push({
                            primaryKey: "projectDescriptionCode",
                            blackLabel: vm.response.projectDescriptionDescription,
                            blueLabel: "Building Description",
                        });
                        break;
                    }
                }
            }
            if (vm.response.overdueCode) {
                for (var ii = 0, ilen = vm.overdueList.length; ii < ilen; ii++) {
                    if (vm.overdueList[ii].overdueCode == vm.response.overdueCode) {
                        vm.response.overdueDescription = vm.overdueList[ii].description;
                        list.push({
                            primaryKey: "overdueCode",
                            blackLabel: vm.response.overdueDescription,
                            blueLabel: "Overdue Status",
                        });
                        break;
                    }
                }
            }
            if (vm.response.priorityCode) {
                for (var ii = 0, ilen = vm.priorityList.length; ii < ilen; ii++) {
                    if (vm.priorityList[ii].priorityCode == vm.response.priorityCode) {
                        vm.response.priorityDescription = vm.priorityList[ii].description;
                        list.push({
                            primaryKey: "priorityCode",
                            blackLabel: vm.response.priorityDescription,
                            blueLabel: "Priority",
                        });
                        break;
                    }
                }
            }

            vm.response.sortedList = list;
        }

        vm.submitSelection = function () {
            vm.setDescriptions();

            $mdDialog.hide(vm.response);
        };

        vm.clearAll = function () {
            vm.response = {
                clientId: null,
                clientName: null,
                contactId: null,
                creatorFullName: null,
                assessorUserId: null,
                assessorUserFullName: null,
                projectDescriptionCode: null,
                projectDescriptionDescription: null,
                overdueCode: null,
                overdueDescription: null,
                priorityCode: null,
                priorityDescription: null,
            };
        };

        vm.clearAllShow = function () {
            return vm.response.clientId != null ||
                vm.response.contactId != null ||
                vm.response.assessorUserId != null ||
                vm.response.projectDescriptionCode != null ||
                vm.response.overdueCode != null ||
                vm.response.priorityCode != null;
        }
    }
})();