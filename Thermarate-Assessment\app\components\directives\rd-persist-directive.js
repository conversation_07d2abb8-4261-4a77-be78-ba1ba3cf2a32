﻿(function () {
    'use strict';

    var app = angular.module('app');
    // rdPersist - persists the ngModel value and restores it next time the directive is loaded (page refresh)
    // <input ng-model="vm.colFilter" data-rd-persist="thisPage" />
    // Set the rd-persist attribute to a value that identifies the  page/view/location.
    app.directive('rdPersist', [ '$parse', function ($parse) {
        return {
            require: '^ngModel',
            link: function (scope, element, attr, ctrl) {
                var nameSpace = "rd-" + attr.rdPersist + attr.ngModel;
                //save value every time it changes
                scope.$watch(attr.ngModel, function (newValue, oldValue) {
                    if (newValue !== oldValue) {
                        localStorage.setItem(nameSpace, JSON.stringify(newValue));
                    }
                }, true);

                //fetch the saved state when the directive is loaded
                var test = localStorage.getItem(nameSpace);
                if (test && test != "undefined") {
                    var savedState = JSON.parse(localStorage.getItem(nameSpace));
                    if (ctrl.$viewValue != savedState) {
                        setTimeout(function () {
                            scope.$apply(function () {
                                ctrl.$setViewValue(savedState);
                                ctrl.$commitViewValue();
                                ctrl.$render();
                                var modelGetter = $parse(attr['ngModel']);
                                var modelSetter = modelGetter.assign;
                                modelSetter(scope, savedState);
                            }, 500)
                        });
                    }
                }

            }
        };
    }]);
})();