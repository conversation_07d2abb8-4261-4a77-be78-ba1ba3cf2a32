<!--
*****************************************************************************
DO NOT CONFUSE THIS WITH user-list-controller.js !!!!
***************************************************************************** 
-->
<section id="users-list-view" class="main-content-wrapper" data-ng-controller="UsersListCtrl as vm">
    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div class="row">
            <!--data-filter-columns-changed="vm.columnsChanged"-->

            <div data-cc-widget-action-bar
                 data-quick-find-model='vm.listFilter'
                 data-action-buttons='vm.actionButtons'
                 data-refresh-list='vm.refreshList()'
                 data-spinner-busy='vm.isBusy'
                 data-new-record="vm.newRecord()"
                 data-new-record-text="New User"
                 data-show-add-button="true"

                 data-filter-a-title="Type"
                 data-filter-a-options="vm.typeOptions"

                 data-filter-columns="vm.filterColumns"
                 data-filter-options="vm.filterOptions"
                 data-filter-changed="vm.refreshList(value)"
                 data-current-filter="vm.currentFilter"
                 data-query-builder-model="vm.queryModel"
                 data-query-builder-name="User"
                 data-query-builder-current="vm.currentQuery">
            </div>
            <div class="table-responsive-vertical shadow-z-1">
                <table class="table table-striped table-hover table-condensed"
                       st-table="vm.usersList"
                       st-global-search="vm.listFilter"
                       st-table-filtered-list="exportList"
                       st-persist="usersList"
                       st-pipe="vm.callServer"
                       st-sticky-header>
                    <thead>
                        <tr>
                            <th align="left" class="action-col">Action</th>
                            <th st-sort="userName" class="can-sort text-left">Username</th>
                            <th st-sort="emailAddress" class="can-sort text-left">Email</th>
                            <th st-sort="clientNames" class="can-sort text-left">Client</th>
                            <th st-sort="isLockedOut" class="can-sort text-center">Locked Out</th>
                            <th st-sort="lastActivityDate" class="can-sort text-left">Last Activity</th>
                            <th>Roles</th>
                            <th class="text-left">Comment</th>
                        </tr>
                    </thead>

                    <tbody>
                        <tr ng-repeat="row in vm.usersList">
                            <td data-title="" class="action-col"><md-button class="md-primary list-select" ui-sref="admin-users-detail({ userId: row.userId, description: row.userName })">Select</md-button>  </td>
                            <td data-title="User" class="text-left"><strong>{{row.userName}}</strong></td>
                            <td data-title="Email" class="text-left">{{row.emailAddress}}</td>
                            <td data-title="Email" class="text-left">{{row.clientNames}}</td>
                            <td data-title="Locked Out" class="text-center"><span ng-class="{'list-boolean-attention': row.isLockedOut == true, 'list-boolean-false': row.isLockedOut == false}" style="color:red;"></span></td>
                            <td data-title="Last Activity" class="text-left">{{row.lastActivityDate| date: 'dd/MM/yyyy'}}</td>
                            <td data-title="Roles" class="text-left"><span ng-repeat="role in row.roleCodes">{{role}}{{$last ? '' : ', '}}</span></td>
                            <td data-title="Comment" class="text-left" flex-gt-xs="20">{{row.comment}}</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="7" class="text-center">
                                <div st-pagination="" st-items-by-page="50" st-displayed-pages="7"></div>
                            </td>
                        </tr>
                    </tfoot>
                </table>
                <div class="widget-pager">
                    <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
                </div>
            </div>
            <div class="widget-foot">
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
</section>
