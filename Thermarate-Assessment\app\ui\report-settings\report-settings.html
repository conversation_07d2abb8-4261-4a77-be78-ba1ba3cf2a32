<!-- Specification Summary -->
<md-card>
    <md-card-header>
        Specification Summary
    </md-card-header>
    <md-card-content>

        <table class="table table-striped table-hover table-condensed shadow-z-1">
            <thead>
            <tr>
                <th>File Name</th>
                <th style="text-align: center;">Show on Client Portal</th>
                <th style="text-align: center;">Allow Download on Client Portal</th>
            </tr>
            </thead>
            <tbody>
            <tr>

                <td>Baseline Specification Summary</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.specificationSummaryReports.baseline.showOnClientPortal"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.specificationSummaryReports.baseline)"
                                 class="vertically-condensed-ex"
                                 disabled
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.specificationSummaryReports.baseline.allowDownloadOnClientPortal"
                                 class="vertically-condensed-ex"
                                 ng-disabled="vm.disabled || vm.reportSettings.specificationSummaryReports.baseline.showOnClientPortal !== true"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>

            </tr>

            <tr>

                <td>Compliance Option Specification Summary</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.specificationSummaryReports.complianceOption.showOnClientPortal"
                                 ng-disabled="vm.disabled"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.specificationSummaryReports.complianceOption)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;"></md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.specificationSummaryReports.complianceOption.allowDownloadOnClientPortal"
                                 class="vertically-condensed-ex"
                                 ng-disabled="vm.disabled || vm.reportSettings.specificationSummaryReports.complianceOption.showOnClientPortal !== true"
                                 style="margin: auto;"></md-checkbox>
                </td>

            </tr>

            </tbody>
        </table>

    </md-card-content>
</md-card>

<!-- Compliance Report -->
<md-card>
    <md-card-header>
        Compliance Report
    </md-card-header>
    <md-card-content>

        <table class="table table-striped table-hover table-condensed shadow-z-1">
            <thead>
            <tr>
                <th>File Name</th>
                <th  style="text-align: center;">Show on Client Portal</th>
                <th  style="text-align: center;">Allow Download on Client Portal</th>
            </tr>
            </thead>
            <tbody>

            <tr>

                <td>Energy Efficiency Compliance Report</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyEfficiencyComplianceReport.showOnClientPortal"
                                 ng-disabled="vm.disabled"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.energyEfficiencyComplianceReport)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyEfficiencyComplianceReport.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.energyEfficiencyComplianceReport.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>

            </tr>

            <tr>

                <td>Energy Efficiency PBDB</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyEfficiencyPbdb.showOnClientPortal"
                                 ng-disabled="vm.disabled"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.energyEfficiencyPbdb)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyEfficiencyPbdb.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.energyEfficiencyPbdb.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>

            </tr>

            <tr>
                <td>Specification 44 Report</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.specification44Report.showOnClientPortal"
                                 ng-disabled="true"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.specification44Report)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.specification44Report.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.specification44Report.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
            </tr>

            <tr>
                <td>Energy Usage Calculation</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyUsageCalculation.showOnClientPortal"
                                 ng-disabled="true"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.energyUsageCalculation)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.energyUsageCalculation.allowDownloadOnClientPortal"
                                 ng-disabled="true"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
            </tr>

            <tr>
                <td>Software Report</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.softwareReport.showOnClientPortal"
                                 ng-disabled="true"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.softwareReport)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.softwareReport.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.softwareReport.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
            </tr>

            <tr>

                <td>Preliminary Report</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.preliminaryReport.showOnClientPortal"
                                 ng-disabled="vm.disabled"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.preliminaryReport)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.preliminaryReport.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.preliminaryReport.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>

            </tr>

            <tr>

                <td>Light And Ventilation Report</td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.lightAndVentilationReport.showOnClientPortal"
                                 ng-disabled="vm.disabled"
                                 ng-change="vm.reportShowSettingChanged(vm.reportSettings.complianceReports.lightAndVentilationReport)"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>
                <td style="text-align: center;">
                    <md-checkbox ng-model="vm.reportSettings.complianceReports.lightAndVentilationReport.allowDownloadOnClientPortal"
                                 ng-disabled="vm.disabled || !vm.reportSettings.complianceReports.lightAndVentilationReport.showOnClientPortal"
                                 class="vertically-condensed-ex"
                                 style="margin: auto;">
                    </md-checkbox>
                </td>

            </tr>

            </tbody>
        </table>

    </md-card-content>
</md-card>
