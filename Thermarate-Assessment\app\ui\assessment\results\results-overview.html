
<div style="position: relative;">

    <div ng-if="vm.building.energyUsageSummary == null"
         style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                background-color: rgba(255, 255, 255, 0.8); z-index: 777;
                display: flex; flex-direction: column; justify-content: center; align-items: center;">
        <h1>No Energy Usage Data Present</h1>
        <h3>To obtain energy usage results, you must upload a matching 'output.txt' file and then re-process.</h3>
    </div>

    <div class="energy-results-graph-grid">

        <!-- Thermal Performance Summary -->
        <md-card class="graph-card">
            <md-card-content style="display: grid; align-items: center; justify-items: center;">

                <div ng-attr-id="thermal-performance-summary{{vm.tabIdentifier}}"
                     class="allow-central-donut-text">

                </div>

                <div id="thermal-performance-center{{vm.tabIdentifier}}"
                     class="thermal-performance-center">
                    <div style="font-size: 16px;">Total</div>
                    <h1 style="margin: 0; font-size: 38px;">{{vm.building.totalEnergyLoad.toFixed(1)}}</h1>
                    <div style="font-size: 16px;">{{vm.energyUnits}} / m<sup>2</sup></div>

                    <!-- Popup tooltip (absolute & positioned via js) -->
                    <div id="thermal-performance-center-tooltip{{vm.tabIdentifier}}"
                         class="thermal-performance-tooltip">

                        <span style="font-size: 10px">Total</span>

                        <!-- Total energy usage again for good measure -->
                        <div>
                            Energy Usage: {{vm.building.totalEnergyLoad.toFixed(1)}} <strong>(100%)</strong>
                        </div>

                        <!-- House energy rating -->
                        <div ng-if="vm.option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' || 
                                    vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolutionHER' ||
                                    vm.option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER'">
                            House Energy Rating: <strong>{{vm.building.overrideEnergyLoads === false ? vm.building.houseEnergyRating.toFixed(1) : vm.building.houseEnergyRatingOverride.toFixed(1)}}</strong>
                        </div>
                    </div>

                </div>
            </md-card-content>
        </md-card>

        <!-- Annual Energy Consumption -->
        <md-card class="graph-card">
            <md-card-content>
                <div ng-attr-id="annual-energy-consumption{{vm.tabIdentifier}}"></div>
                <div class="graph-options-grid">
                    <div>
                        <label class="graph-option-select-label">Grouping</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                ng-model="vm.annualEnergyOptions.grouping"
                                ng-change="vm.buildAnnualEnergyConsumptionChart(vm.building.energyUsageSummary, vm.annualEnergyOptions);">
                            <md-option ng-value="'Daily'">
                                Daily
                            </md-option>
                            <md-option ng-value="'Monthly'">
                                Monthly
                            </md-option>
                        </md-select>
                    </div>
                    <div>
                        <label class="graph-option-select-label">View</label>
                        <md-select class="lightweight graph-option-select multi-select-plus-separator vertically-condensed"
                                   ng-model="vm.annualEnergyOptions.view"
                                   ng-change="vm.buildAnnualEnergyConsumptionChart(vm.building.energyUsageSummary, vm.annualEnergyOptions);"
                                   multiple>
                            <md-option ng-value="view"
                                    ng-repeat="view in vm.VIEWS">
                                {{view}}
                            </md-option>
                        </md-select>

                    </div>
                    <div>
                        <label class="graph-option-select-label">Period</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                   ng-model="vm.annualEnergyOptions.period"
                                   ng-change="vm.buildAnnualEnergyConsumptionChart(vm.building.energyUsageSummary, vm.annualEnergyOptions);">
                            <md-option ng-value="period"
                                    ng-repeat="period in vm.PERIODS">
                                {{period}}
                            </md-option>
                        </md-select>
                    </div>
                </div>
            </md-card-content>
        </md-card>

        <!-- Zone Energy Use -->
        <md-card class="graph-card">
            <md-card-content>
                <div ng-attr-id="zone-energy-use{{vm.tabIdentifier}}"></div>
                <div class="graph-options-grid">
                    <div>
                        <label class="graph-option-select-label">Grouping</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                   ng-model="vm.zoneEnergyOptions.zoneGrouping"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zoneEnergyOptions, false);"
                                   ng-change="">
                            <md-option ng-value="zoneGrouping"
                                       ng-repeat="zoneGrouping in vm.ZONE_GROUPINGS">
                                {{zoneGrouping}}
                            </md-option>
                        </md-select>
                    </div>
                    <div>
                        <label class="graph-option-select-label">View</label>
                        <md-select class="lightweight graph-option-select multi-select-plus-separator vertically-condensed"
                                   ng-model="vm.zoneEnergyOptions.view"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zoneEnergyOptions, false);"
                                   multiple>
                            <md-option ng-value="view"
                                       ng-repeat="view in vm.VIEWS">
                                {{view}}
                            </md-option>
                        </md-select>
                    </div>
                    <div>
                        <label class="graph-option-select-label">Period</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                   ng-model="vm.zoneEnergyOptions.period"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zoneEnergyOptions, false);">
                            <md-option ng-value="period"
                                       ng-repeat="period in vm.PERIODS">
                                {{period}}
                            </md-option>
                        </md-select>
                    </div>
                </div>
            </md-card-content>
        </md-card>

        <!-- Zone Energy Use Per Area -->
        <md-card class="graph-card">
            <md-card-content>
                <div ng-attr-id="zone-energy-use-area{{vm.tabIdentifier}}"></div>
                <div class="graph-options-grid">
                    <div>
                        <label class="graph-option-select-label">Grouping</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                   ng-model="vm.zonePerAreaEnergyOptions.zoneGrouping"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zonePerAreaEnergyOptions, true);"
                                   ng-change="">
                            <md-option ng-value="zoneGrouping"
                                       ng-repeat="zoneGrouping in vm.ZONE_GROUPINGS">
                                {{zoneGrouping}}
                            </md-option>
                        </md-select>
                    </div>
                    <div>
                        <label class="graph-option-select-label">View</label>
                        <md-select class="lightweight graph-option-select multi-select-plus-separator vertically-condensed"
                                   ng-model="vm.zonePerAreaEnergyOptions.view"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zonePerAreaEnergyOptions, true);"
                                   multiple>
                            <md-option ng-value="view"
                                       ng-repeat="view in vm.VIEWS">
                                {{view}}
                            </md-option>
                        </md-select>
                    </div>
                    <div>
                        <label class="graph-option-select-label">Period</label>
                        <md-select class="lightweight graph-option-select vertically-condensed"
                                   ng-model="vm.zonePerAreaEnergyOptions.period"
                                   ng-change="vm.buildZoneEnergyUseChart(vm.building.energyUsageSummary, vm.zonePerAreaEnergyOptions, true);">
                            <md-option ng-value="period"
                                       ng-repeat="period in vm.PERIODS">
                                {{period}}
                            </md-option>
                        </md-select>
                    </div>
                </div>
            </md-card-content>
        </md-card>

    </div>

</div>

<style>

    .graph-options-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-column-gap: 12px;
        justify-content: space-around;
        justify-items: center;
    }

    .graph-options-grid > div {
        width: 100%;
    }

    .graph-option-select-label {
        font-size: 11px;
        color: lightgrey;
        padding-left: 3px;
    }
    .graph-option-select {
        font-size: 14px;
        padding: 6px 0;
        text-align: start;
    }

    .allow-central-donut-text > div,
    .allow-central-donut-text > div > span {
        height: 109% !important;
        width: 100% !important;
    }

    /* By default, show 2 cards per row. */
    .energy-results-graph-grid {
        display: grid;
        grid-template-columns: 100%;
    }

    /* On smaller screens, only have 1 graph per row. */
    @media screen and (max-width: 1140px) {
        .energy-results-graph-grid {
            grid-template-columns: 100%;
        }
    }

    .thermal-performance-center {
        position: absolute; 
        text-align: center; 
        margin-top: 20px;
    }

    .thermal-performance-center:hover .thermal-performance-tooltip {
        opacity: 100%;
    }

    .thermal-performance-tooltip {

        display: block;
        opacity: 0;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid red;
        border-radius: 2px;
        box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.2);

        margin-left: 28px;
        padding: 6px;
        position: absolute;
        z-index: 1000;

        width:165px;

        font-size: 12px;
        text-align: left;
        font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif;

        transition: opacity 200ms;
    }

</style>
