(function () {

  'use strict';
  angular
    .module('app')
    .component('elCoolingTooltip', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
        assessmentMethod: '<',
        heatingCoolingLoadLimits: '<',
      },
      templateUrl: 'app/ui/energy-labs/performance-tooltips/cooling-tooltip.html',
      controller: ElCoolingTooltipController,
      controllerAs: 'vm'
    });

  ElCoolingTooltipController.$inject = ['common'];

  function ElCoolingTooltipController(common) {

    let vm = this;

    vm.determineTarget = function () {
        if (vm.assessmentMethod == "House Energy Rating (HER)" && (vm.heatingCoolingLoadLimits == false || vm.source.energyLoadLimits.coolingLoadLimit?.toFixed(1) == null)) {
            return 'N/A';
        } else {
            return vm.source.energyLoadLimits.coolingLoadLimit?.toFixed(1);
        }
    }

    vm.determineCalcResultColour = function () {
        if (vm.assessmentMethod == "House Energy Rating (HER)" && vm.heatingCoolingLoadLimits == false || vm.source.energyLoadLimits.coolingLoadLimit == null) {
            return 'black';
        } else if (common.lessThanOrEqualish(vm.source.coolingLoad, vm.source.energyLoadLimits.coolingLoadLimit, 1)) {
            return 'var(--thermarate-green)';
        } else {
            return 'var(--warning)';
        }
    }

  }

})();