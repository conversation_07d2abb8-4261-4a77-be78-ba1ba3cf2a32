<form class="main-content-wrapper" 
      novalidate 
      data-ng-controller='constructionElementActionModalController as vm'
      style="min-width:700px; max-width: 700px;">

    <div class="widget"
         ng-form="constructionElementActionModalForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

                <md-card>

                    <md-card-content>

                        <div style="margin:  10px 0;">
                            Select a destination
                        </div>

                        <div class="construction-action-modal-search-container">
                            <div class="clickable construction-action-modal-search-item"
                                 ng-repeat="destination in vm.availableDestinations track by $index"
                                 ng-click="vm.destination = destination;"
                                 ng-class="{ 'construction-action-search-selected' : vm.destination === destination }">
                                <span>{{destination.overrideDisplayDescription || destination.displayDescription || destination.description}}</span>
                            </div>
                        </div>

                    </md-card-content>

                </md-card>

            <!-- Action Buttons -->
            <div data-cc-widget-button-bar
                 data-is-modal="true">

                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_page_(tabs/sub-tabs)__construction__edit', 'assessment_page_(tabs/sub-tabs)__openings__edit']"
                           ng-disabled="constructionElementActionModalForm.$invalid || vm.disabled || vm.destination == null"
                           ng-click="vm.save('move')">
                    Move
                </md-button>
                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_page_(tabs/sub-tabs)__construction__edit', 'assessment_page_(tabs/sub-tabs)__openings__edit']"
                           ng-disabled="constructionElementActionModalForm.$invalid || vm.disabled || vm.destination == null"
                           ng-click="vm.save('copy')">
                    Copy
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>