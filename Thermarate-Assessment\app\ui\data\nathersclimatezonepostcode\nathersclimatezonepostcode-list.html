<section id="nathersclimatezone-list-view" class="main-content-wrapper" data-ng-controller="NathersclimatezonePostcodeListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Nathersclimatezone"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.nathersclimatezoneList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="nathersclimatezoneList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>

                        <th st-sort="Postcode" class="can-sort text-left">Postcode</th>
                        <th st-sort="natHERSClimateZoneCode" class="can-sort text-center">Code</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.nathersclimatezoneList">
                        <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="nathersclimatezonepostcode-updateform({ postcode: row.postcode})">Select</md-button>  </td>

                        <td data-title="Postcode" class="text-left">{{::row.postcode }}</td>
                        <td data-title="Code" class="text-center">{{::row.natHERSClimateZoneCode }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
