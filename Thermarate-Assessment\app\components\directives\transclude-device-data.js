﻿(function () {
    'use strict';
    var app = angular.module('app');
    app.directive('transDeviceData', ['$compile', '$parse', '$templateRequest', transDeviceData]);
    //transDeviceData for creating the dynamic rows based on the columns in the case of the device data
    function transDeviceData($compile, $parse, $templateRequest) {
        return {
            restrict: 'EA',
            replace: true,
            transclude: true,
            scope:{
                colLength: '=',
                srow: '='
            },
            link: function (scope, element, attrs) {
                scope.$watch('colLength', function (val) {
                    if (!angular.isDefined(val)) {
                        return;
                    }
                    element.empty();
                    var html = buildFormHTML(parseInt(val));
                    var template = angular.element(html);
                    element.append(template);
                    $compile(template)(scope);
                });
            }
        };
        function buildFormHTML(colLength) {
            var fieldHtml =  "";
            for (var i = 0; i < colLength; i++) {
                if (i === 0) {
                    fieldHtml += "<td>{{srow[" + i + "] | date: 'dd/MM/yyyy HH:mm:ss'}}</td>";
                } else {
                    fieldHtml += "<td>{{srow[" + i + "]}}</td>";
                }
            }
            return fieldHtml;
        }
    }

    app.directive('transDeviceRemoveStyle', ['$compile', '$parse', '$templateRequest', transDeviceRemoveStyle]);

    //transDeviceRemoveStyle for removing the style element from the th each time the column is dynamically changed
    function transDeviceRemoveStyle($compile, $parse, $templateRequest) {
        return {
            restrict: 'EA',
            scope: {
                colLength: '=',
                srow: '='
            },
            link: function (scope, element, attrs) {
                scope.$watch('colLength', function (val) {
                    if (!angular.isDefined(val)) {
                        return;
                    }
                    if (angular.isDefined(element[0].style.width)) {
                        element[0].style.width = "";
                    }
                });
            }
        };
    }
})();