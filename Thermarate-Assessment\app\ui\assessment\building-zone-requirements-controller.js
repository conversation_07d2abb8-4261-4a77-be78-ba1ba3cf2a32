// NOTE THIS CLASS HAS BEEN SUPERSEDED BY zone-summary-controller + zone-summary.html
//
//
// IT IS BEING LEFT HERE AS A REFERENCE AT THE MOMENT AS I FEEL LIKE IT MAY COME IN HANDY AGAIN AT SOME POINT
// TO COPY OLD FUNCTIONALITY BACK INTO THE NEW ZONE-SUMMARY. 
//
//
// WILL DELETE IF THAT FEELING CHANGES!
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
(function () {

    'use strict';
    angular
        .module('app')
        .component('buildingZoneRequirements', {
            bindings: {
                source: '<',                // Actual source object which CONTAINS zones
                storeys: '<',               // Array of Storeys within the Building/Source
                nccClimateZone: '<',
            },
            templateUrl: 'app/ui/assessment/building-zone-requirements.html',
            controller: BuildingZoneRequirements,
            controllerAs: 'vm'
        });

    BuildingZoneRequirements.$inject = ['$scope', '$interval', 'common', 'zonetypeservice', 'zoneservice', 'uuid4'];

    function BuildingZoneRequirements($scope, $interval, common, zonetypeservice, zoneservice, uuid4) {

        var vm = this;
        vm.zoneTypeList = [];
        vm.nccClassificationList = [];
        vm.zoneActivityList = [];
        vm.requirements = [];
        vm.sectionExpansions = {}; // Used to keep track of which sections are expanded.

        /** Retrieve and initialize any required data here, not just floating around the controller. */
        function initialize() {

            // Retreive our list of available ZoneTypes.
            zonetypeservice.getList()
                .then(function (data) {
                    vm.zoneTypeList = data.data;
                });

            zoneservice.getNccClassificationList()
                .then(function (data) {
                    vm.nccClassificationList = data;
                });

            zoneservice.getZoneActivityList()
                .then(function (data) {
                    vm.zoneActivityList = data;
                });

            vm.calculationTimerPromise = setInterval(() => {
                    calculateZoneRequirements(vm.source, vm.nccClimateZone);
                },
                1000);
        }

        $scope.$on("$destroy", function () {
            if (vm.calculationTimerPromise) {
                clearInterval(vm.calculationTimerPromise);
            }
        });

        /**
         * Loops over the supplied zones and breaks them up into sections and rows, with calculations
         * pre-baked into our vm.requirements property to allow for easy looping over within html side.
         * 
         * @param {any} zones
         * @param {any} storeys
         */
        function calculateZoneRequirements(building, nccClimateZone) {

            const zones = building?.zones;
            const storeys = building?.storeys;

            if (zones == null || storeys == null)
                return;

            let newRequirements = [];

            // Step 1. Loop over all storeys (These will be SECTIONS, following steps will be ROWS)
            storeys.forEach(storey => {

                let section = {
                    title: storey.name,
                    rows: []
                };

                // Step 1.1 Add rows for individual Zones with calculations done for the columns that require it.
                let zonesInStorey = zones.filter(z => z.storey === storey.floor);
                zonesInStorey.forEach(zone => {

                    let row = createZoneRow(zone);
                    section.rows.push(row);
                });

                // SUM ROWS.
                //// Step 1.2 Add SUM rows for each NCC Classification (which is present)
                let presentNccClassifications = nccClassificationsPresentIn(storey.floor);

                presentNccClassifications.forEach(ncc => {

                    let row = createSumRowMatching(
                        ncc.description + ' Total',
                        'nccClassification.nccClassificationCode',
                        ncc.nccClassificationCode,
                        storey.floor);

                    section.rows.push(row);
                });

                // Step 1.3 Add a single SUM row for ZoneType = Outdoor Living
                let outdoorRow = createSumRowMatching(
                    'Covered Outdoor Living Total',
                    'zoneType.zoneTypeCode',
                    'ZTOutdoor',
                    storey.floor
                );

                if (outdoorRow != null)
                    section.rows.push(outdoorRow);

                // Step 1.4 Add a single SUM row for Zonetype = Habitable
                let habitablRow = createSumRowMatching(
                    'Habitable Total',
                    'zoneType.zoneTypeCode',
                    'ZTHabitableRoom',
                    storey.floor
                );

                if (habitablRow != null)
                    section.rows.push(habitablRow);

                // Step 1.5 Add a single SUM row for conditioned = true
                let conditionedRow = createSumRowMatching(
                    'Conditioned Total',
                    'conditioned',
                    true,
                    storey.floor,
                    true // Exclude Exterior
                );

                if (conditionedRow != null)
                    section.rows.push(conditionedRow);

                // Step 1.6 Add a single SUM row for conditioned = false
                let unconditionedRow = createSumRowMatching(
                    'Unconditioned Total',
                    'conditioned',
                    false,
                    storey.floor,
                    true // Exclude exterior
                );

                if (unconditionedRow != null)
                    section.rows.push(unconditionedRow);

                // Step 1.7 Add a single SUM row for 'Garage' (ZoneActivity = 'ZAGarage' OR 'ZAGarageConditioned)
                let garageRow = createSumRowMatching(
                    'Garage Total',
                    'zoneActivity.zoneActivityCode',
                    ['ZAGarage', 'ZAGarageConditioned'],
                    storey.floor,
                    false // Exclude exterior
                );

                if (garageRow != null)
                    section.rows.push(garageRow);

                // Add Storey section to our requirements...
                newRequirements.push(section);

            });

            // Step 2. Create a final "Whole Building" SECTION with all the SUM ROWS as per above.
            let wholeBuildingSection = {
                title: "Whole Building Totals",
                rows: []
            };

            // 2.1 All NCC Classifications
            let presentNccClassifications = nccClassificationsPresentIn(null);
            presentNccClassifications.forEach(ncc => {

                let row = createSumRowMatching(
                    ncc.description + ' Total',
                    'nccClassification.nccClassificationCode',
                    ncc.nccClassificationCode,
                    null);

                wholeBuildingSection.rows.push(row);
            });

            // Step 2.2 Add a single SUM row for ZoneType = Outdoor Living
            let outdoorRow = createSumRowMatching(
                'Covered Outdoor Living Total',
                'zoneType.zoneTypeCode',
                'ZTOutdoor',
                null
            );

            if (outdoorRow != null)
                wholeBuildingSection.rows.push(outdoorRow);

            // Step 2.3 Add a single SUM row for Zonetype = Habitable
            let habitablRow = createSumRowMatching(
                'Habitable Total',
                'zoneType.zoneTypeCode',
                'ZTHabitableRoom',
                null
            );

            if (habitablRow != null)
                wholeBuildingSection.rows.push(habitablRow);

            // Step 2.4 Add a single SUM row for conditioned = true
            let conditionedRow = createSumRowMatching(
                'Conditioned Total',
                'conditioned',
                true,
                null,
                true // Exclude Exterior
            );

            if (conditionedRow != null)
                wholeBuildingSection.rows.push(conditionedRow);

            // Step 2.5 Add a single SUM row for conditioned = false
            let unconditionedRow = createSumRowMatching(
                'Unconditioned Total',
                'conditioned',
                false,
                null,
                true // Exclude Exterior
            );

            if (unconditionedRow != null)
                wholeBuildingSection.rows.push(unconditionedRow);

            let garageRow = createSumRowMatching(
                'Garage Total',
                'zoneActivity.zoneActivityCode',
                ['ZAGarage', 'ZAGarageConditioned'],
                null,
                false // Exclude exterior
            );

            if (garageRow != null)
                wholeBuildingSection.rows.push(garageRow);

            newRequirements.push(wholeBuildingSection);

            // We need to smartly merge in new changes with the old vm.requirements object or
            // we get weird UI things happening that affect the entire page (Paul said he has encountered
            // this on other projects due to the way in which we have a custom calculation cycle here).
            // Anyway so this is a bit of a 'slack' way to do things but essentially the main problem
            // we seem to have with just using angular.merge is it won't REMOVE objects from an array if
            // they don't exist in the array you're merging in, which is a problem for us since storeys and
            // zones can be added and changed around and so on.
            // The very simple fix for this in our case is to simply check if the size of our requirements
            // array (AND the ROWS within the requirements array) are identical in length, and if they are BIGGER or EQUAL
            // in the incoming array, that's ok. But if any are SMALLER, then instead we blow the whole object away.
            let sameOrSmaller = true; // By default we assume the requirements array is the same size or smaller (i.e. OK to angular.merge)
            if (vm.requirements.length < newRequirements.length)
                sameOrSmaller = false; // Ok, need to blow away old value...
            else {
                // Ok so the requirements array is the same, what about sub-rows?
                for (let i = 0; i < newRequirements.length; i++) {

                    let oldRows = vm.requirements[i].rows;
                    let newRows = newRequirements[i].rows;

                    if (oldRows.length < newRows.length) {
                        sameOrSmaller = false;
                        break;
                    }
                }
            }

            if (sameOrSmaller)
                angular.merge(vm.requirements, newRequirements);
            else {
                vm.requirements = newRequirements; // Blow entire thing away - will cause UI problem (only noticeable if an element is open)
            }
        }

        /** Creates a row with data linked to a SPECIFIC zone */
        function createZoneRow(zone) {

            let row = {};

            row.zoneNumber = zone.zoneNumber;
            row.description = zone.zoneDescription;
            row.zoneActivity = zone.zoneActivity?.description;
            row.zoneType = zone.zoneType?.description;
            row.conditioned = zone.conditioned ? "Yes" : "No";
            row.nccClassification = zone.nccClassification?.description;
            row.floorArea = zone.floorArea;

            // For individual rows, this data is already pre-calculated and in
            // the zone object itself, so we can use as-is.
            row.naturalLight = {
                requiredPercent: zone.naturalLightRequiredPercent,
                required: zone.naturalLightRequiredM2,
                achieved: "-"
            };

            row.ventilation = {
                requiredPercent: zone.ventilationRequiredPercent,
                required: zone.ventilationRequiredM2,
                achieved: "-",
            };

            row.airMovement = {
                requiredPercent: zone.airMovementRequiredPercent,
                required: zone.airMovementRequiredM2,
                achieved: "-",
            };

            row.lampPower = {
                maximumWM2: zone.lampPowerMaximumWM2,
                maximumW: zone.lampPowerMaximumW,
                achieved: "-",
            };

            return row;

        }

        /**
         *  Creates a row with data being the SUMMED value of all zones that match the
         *  given path/value combination.
         * @param {any} description A description to name the row (assuming any matches)
         * @param {any} pathToMatch A string to the path of the value we wish to match against.
         * @param {any} valueToMatch The value we want our path to equal to be included.
         * @param {any} floor The INT floor we wish to match with.
         * @param {any} excludeExterior If TRUE, excludes an zone where Zone Activity = TRUE from being included in totals and calculations.
         */
        function createSumRowMatching(description, pathToMatch, valueToMatch, floor, excludeExterior) {

            let row = {};

            // Get all zones matching our filter.
            let matchingZones = [];

            // If a single string has been passed in simply match against it
            if (!Array.isArray(valueToMatch)) {
                matchingZones = filterZones(
                    pathToMatch,
                    valueToMatch,
                    floor);
            } else {

                // If an array of possible values has been passed in, 
                // match against all of them and add together.
                for (let i = 0; i < valueToMatch.length; i++) {
                    let temp = filterZones(
                        pathToMatch,
                        valueToMatch[i],
                        floor);

                    matchingZones = matchingZones.concat(temp);
                }

            }

            if (matchingZones == null || matchingZones.length === 0)
                return null;

            row.zoneNumber = ""; // Not an actual zone, so blank.
            row.description = description;
            row.zoneActivity = determineZoneActivitySum(matchingZones);
            row.zoneType = determineZoneTypeSum(matchingZones);
            row.conditioned = determineConditionedSum(matchingZones);
            row.nccClassification = determineNccClassificationSum(matchingZones);
            row.floorArea = matchingZones.map(x => x.floorArea).reduce((a, b) => a + b);

            // For individual rows, this data is already pre-calculated and in
            // the zone object itself, so we can use as-is.
            row.naturalLight = {
                requiredPercent: determineSameOrVaries(matchingZones, 'naturalLightRequiredPercent'),
                required: matchingZones.map(x => x.naturalLightRequiredM2).reduce((a, b) => a + b),
                achieved: "-"
            };

            row.ventilation = {
                requiredPercent: determineSameOrVaries(matchingZones, 'ventilationRequiredPercent'),
                required: matchingZones.map(x => x.ventilationRequiredM2).reduce((a, b) => a + b),
                achieved: "-",
            };

            row.airMovement = {
                requiredPercent: determineSameOrVaries(matchingZones, 'airMovementRequiredPercent'),
                required: 0,
                achieved: "-",
            };

            row.lampPower = {
                maximumWM2: determineSameOrVaries(matchingZones, 'lampPowerMaximumWM2'),
                maximumW: matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b),
                achieved: "-",
            };

            return row;
        }

        /** Determines what the SUM ZoneActivity is for the given zones. Can be 'VARIES'. */
        function determineZoneActivitySum(zones) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < vm.zoneActivityList.length; i++) {
                let za = vm.zoneActivityList[i];

                if (zones.every(x => x.zoneActivity?.zoneActivityCode == za.zoneActivityCode))
                    return za.description;
            }

            return "Varies";
        }

        /** Determines what the SUM ZoneType is for the given zones. Can be 'Varies'. */
        function determineZoneTypeSum(zones) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < vm.zoneTypeList.length; i++) {
                let za = vm.zoneTypeList[i];

                if (zones.every(x => x.zoneType?.zoneTypeCode == za.zoneTypeCode))
                    return za.description;
            }

            return "Varies";
        }

        /** Determines what the SUM 'Conditioned' value is for the given zones. Can be 'Varies'. */
        function determineConditionedSum(zones) {

            if (zones == null || zones.length == 0)
                return "-";

            if (zones.every(x => x.conditioned === true))
                return "Yes";

            if (zones.every(x => x.conditioned === false))
                return "No";

            return "Varies";
        }

        /** Determines what the SUM NccClassification is for the given zones. Can be 'VARIES'. */
        function determineNccClassificationSum(zones) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < vm.nccClassificationList.length; i++) {
                let za = vm.nccClassificationList[i];

                if (zones.every(x => x.nccClassification?.nccClassificationCode == za.nccClassificationCode))
                    return za.description;
            }

            return "Varies";
        }

        /**
        *  Does NOT return the sum. Either returns a single value if all matching
        *  rows are the same, OR returns "Varies".
        */
        function determineSameOrVaries(zones, path) {
            if (zones == null || zones.length == 0)
                return "0.00";
            else {

                // If the first zone % is not the same as ALL, then it naturally "Varies"
                let zone = zones[0];
                if (zones.every(x => common.resolve(path, x) == common.resolve(path, zone)))
                    return common.resolve(path, zone);
                else
                    return "Varies";
            }
        }

        /**
         * Returns the building zones for the given storey.
         * 
         * @param {any} index 0 = ground level, 1 = first floor, etc.
         */
        function getZonesInStorey(index) {
            let f = vm.source?.zones?.filter(s => s.storey == index);
            return f;
        }

        /** Filters zones by the given path/value combination and OPTIONALLY the storey. */
        function filterZones(path, value, storey = null) {

            let filtered = null;

            if (storey != null) {
                filtered = vm.source.zones
                    ?.filter(x => common.resolve(path, x) == value &&
                        x.storey == storey);
            } else {
                filtered = vm.source.zones
                    ?.filter(x => common.resolve(path, x) == value);
            }

            return filtered;
        }

        /**
         * Return a list of NCC Classifications that actually have corresponding
         * zones for the given storey.
         */ 
        function nccClassificationsPresentIn(storey) {

            let zones = [];

            if (storey !== null)
                zones = getZonesInStorey(storey);
            else 
                zones = vm.source.zones;

            let transformed = zones
                ?.map(x => x.nccClassification?.nccClassificationCode);

            // Remove duplicates... (Note: We can't just have a set of NCC Classifications
            // because the underlying objects won't match, thus no duplicates removed)
            let set = new Set(transformed);
            let um = vm.nccClassificationList.filter(x => set.has(x.nccClassificationCode));

            return um;
        }

        // Finally, initialize our component.
        initialize();

    }
})();