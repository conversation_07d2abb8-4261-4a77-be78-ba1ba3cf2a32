<div>
    <h3 style="margin-top: 16px;">Filter</h3>
    <div ng-if="vm.initialised" class="filter-grid">

        <!-- Row 1 -->
        <div>
            <label class="graph-option-select-label">Storey</label>
            <md-select name="storeyFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.storey"
                        ng-required="true">
                <md-option ng-value="storey"
                           ng-click="vm.applyGroupSelectLogic(vm.zoneSummaryBuildingData, vm.filtersObject, vm.filtersObject.group, vm.calculateEnvelopeSummaryData)"
                           ng-repeat="storey in vm.selectedStoreys">
                    {{storey.description}}
                </md-option>
                <md-option ng-value="'ALL'"
                            ng-click="vm.applyGroupSelectLogic(vm.zoneSummaryBuildingData, vm.filtersObject, vm.filtersObject.group, vm.calculateEnvelopeSummaryData)">
                    Whole Building
                </md-option>
            </md-select>
        </div>
        <div>
            <label class="graph-option-select-label">Horizontal Shading (Exterior Glazing)</label>
            <md-select name="glazingHorizontalShadingFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.glazingHorizontalShading.selection"
                        multiple>
                <md-option ng-value="shading"
                           ng-click="vm.applySelectionLogic(vm.filtersObject.glazingHorizontalShading, shading)"
                           ng-repeat="shading in vm.filtersObject.glazingHorizontalShading.selectableArray track by $index">
                    {{shading.title}}
                </md-option>
            </md-select>
        </div>
        <div>
            <label class="graph-option-select-label">Horizontal Shading (Exterior Walls)</label>
            <md-select name="wallHorizontalShadingFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.wallHorizontalShading.selection"
                        multiple>
                <md-option ng-value="shading"
                           ng-click="vm.applySelectionLogic(vm.filtersObject.wallHorizontalShading, shading)"
                           ng-repeat="shading in vm.filtersObject.wallHorizontalShading.selectableArray track by $index">
                    {{shading.title}}
                </md-option>
            </md-select>
        </div>

        <!-- Row 2 -->                            
        <div>
            <label class="graph-option-select-label">Group</label>
            <md-select name="groupFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.group"
                        ng-required="true"
                        ng-change="vm.filtersObject.selection = []">
                <md-option ng-value="group"
                           ng-click="vm.applyGroupSelectLogic(vm.zoneSummaryBuildingData, vm.filtersObject, group, vm.recalculateSimulationData)"
                           ng-repeat="group in vm.GROUP_OPTIONS"
                           ng-disabled="vm.building == null && group == 'Zone Name'">
                    {{group}}
                </md-option>
            </md-select>
        </div>
        <div>
            <label class="graph-option-select-label">Vertical Shading (Exterior Glazing)</label>
            <md-select name="glazingVerticalShadingFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.glazingVerticalShading.selection"
                        multiple>
                <md-option ng-value="shading"
                            ng-click="vm.applySelectionLogic(vm.filtersObject.glazingVerticalShading, shading)"
                            ng-repeat="shading in vm.filtersObject.glazingVerticalShading.selectableArray track by $index">
                    {{shading.title}}
                </md-option>
            </md-select>
        </div>
        <div>
            <label class="graph-option-select-label">Vertical Shading (Exterior Walls)</label>
            <md-select name="wallVerticalShadingFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.wallVerticalShading.selection"
                        multiple>
                <md-option ng-value="shading"
                            ng-click="vm.applySelectionLogic(vm.filtersObject.wallVerticalShading, shading)"
                            ng-repeat="shading in vm.filtersObject.wallVerticalShading.selectableArray track by $index">
                    {{shading.title}}
                </md-option>
            </md-select>
        </div>

        <!-- Row 3 - how to stop making row expand -->
        <div>
            <label class="graph-option-select-label">Selection</label>
            <md-select  name="selectionFilter"
                        class="lightweight vertically-condensed"
                        ng-model="vm.filtersObject.selection"
                        multiple
                        ng-required="true">
                <md-option ng-value="selection"
                           ng-repeat="selection in vm.filtersObject.selectableArray track by $index"
                           ng-click="vm.applySelectionLogic(vm.filtersObject, selection)">
                    {{ selection.title || selection }}
                </md-option>
            </md-select>
        </div>

    </div>
</div>
<style>
    .filter-grid {
        display: grid;
        /* 1fr causes child multi-select to stretch column when it becomes bigger */
        grid-template-columns: repeat(3, 33%);
        gap: 12px;
        justify-content: space-around;
        justify-items: center;
    }

        .filter-grid > div {
            width: 100%;
        }
    .chart-view-container {
        width: 33%;
        margin: 0 auto;
    }
</style>