<!-- Assessment Files -->
<table class="table-condensed file-uploads-table">
    <thead>
        <tr>
            <th style="text-align: left; width: 185px;">
            </th>
            <th style="text-align: left; width: 570px;">
                <div style="display: grid; grid-template-columns: 9fr 1fr; align-items: center;">
                    <div>Proposed Building</div>
                    <md-menu style="display: flex; justify-content: center;">
                        <img md-menu-origin
                             class="clickable"
                             ng-click="$mdOpenMenu()"
                             src="/content/feather/more-horizontal.svg"
                             ng-disabled="vm.disabled"/>
                        <md-menu-content>
                            <md-menu-item>
                                <md-button ng-click="vm.downloadAll('proposed', 'Proposed Building')">
                                    Download All
                                </md-button>
                            </md-menu-item>
                            <md-menu-divider ng-if="!vm.disabled"></md-menu-divider>
                            <md-menu-item ng-if="!vm.disabled">
                                <md-button ng-click="vm.deleteAll(vm.option.proposed, 'Proposed Building')">
                                    <span style="color: orangered;">Delete All</span>
                                </md-button>
                            </md-menu-item>
                        </md-menu-content>
                    </md-menu>
                </div>
            </th>
            <th style="text-align: left; width: 570px;" ng-if="vm.referenceIsRequired()">
                <div style="display: grid; grid-template-columns: 9fr 1fr; align-items: center;">
                    <div>{{vm.referenceName()}} Building</div>
                    <md-menu style="display: flex; justify-content: center;">
                        <img md-menu-origin
                             class="clickable"
                             ng-click="$mdOpenMenu()"
                             src="/content/feather/more-horizontal.svg"
                             ng-disabled="vm.disabled"/>
                        <md-menu-content>
                            <md-menu-item>
                                <md-button ng-click="vm.downloadAll('reference', vm.referenceName(true) + ' Building')">
                                    Download All
                                </md-button>
                            </md-menu-item>
                            <md-menu-divider ng-if="!vm.disabled"></md-menu-divider>
                            <md-menu-item ng-if="!vm.disabled">
                                <md-button ng-click="vm.deleteAll(vm.option.reference, vm.referenceName() + ' Building')">
                                    <span style="color: orangered;">Delete All</span>
                                </md-button>
                            </md-menu-item>
                        </md-menu-content>
                    </md-menu>
                </div>
            </th>
        </tr>
    </thead>
    <tbody>

        <!-- Primary + Secondary + Tertiary files (as required by assessment software) -->
        <tr ng-repeat="fileType in vm.softwareFileTypes track by $index"
            ng-if="vm.softwareFileIsRequired(fileType.prop);">

            <td style="font-weight: bold;">{{vm.softwareFileDisplayName(fileType.prop)}}</td>

            <td>
                <file-upload class="vertically-condensed-ex"
                             style="width: 100%;"
                             is-locked="vm.disabled"
                             job-files="vm.jobFiles"
                             file-object="vm.option.proposed"
                             prop-name="{{fileType.prop}}"
                             form-name="Proposed{{fileType.prop}}"
                             required-message="''"
                             assessment="vm.assessment"
                             job="vm.assessment.job"
                             category="'Simulation Data'"
                             classification="vm.softwareFileDisplayName(fileType.prop)"
                             is-required="true"
                             accept="vm.option.assessmentSoftware[fileType.prop + 'Extension']">
                </file-upload>
            </td>

            <td ng-if="vm.referenceIsRequired()" style="padding-left: 30px">
                <file-upload class="vertically-condensed-ex"
                             style="width: 100%;"
                             is-locked="vm.disabled"
                             job-files="vm.jobFiles"
                             file-object="vm.option.reference"
                             prop-name="{{fileType.prop}}"
                             form-name="Reference{{fileType.prop}}"
                             required-message="''"
                             assessment="vm.assessment"
                             job="vm.assessment.job"
                             category="'Simulation Data'"
                             classification="vm.softwareFileDisplayName(fileType.prop)"
                             is-required="true"
                             accept="vm.option.assessmentSoftware[fileType.prop + 'Extension']">>
                </file-upload>
            </td>

        </tr>

        <!-- Upload button / drag n drop form area -->
        <tr>
            <td/>
            <td>
                <div class="drawing-upload-area clickable {{vm.dragDropDisabled() ? 'drag-drop-disabled' : ''}}"
                     style="height: 130px;"
                     ngf-drop="vm.uploadFiles($files, vm.option.proposed)"
                     ngf-select="vm.uploadFiles($files, vm.option.proposed)"
                     ngf-multiple="true"
                     ngf-drag-over-class="'drawing-upload-file-over'"
                     ng-disabled="vm.dragDropDisabled()"
                     accept=".pdf">

                    <!-- Notification of upload in progress -->
                    <div ng-if="vm.uploadingDrawings != null"
                         style="display: grid; grid-template-columns: 1fr; align-items: center; justify-items: center;">

                        <!-- Upload + Processing Progress bar -->
                        <h3 style="margin-top: 0;">Uploading...</h3>
                        <div class="loading-bar-background"
                             style="width: 400px;"
                             ng-click="vm.stopClickThrough($event)">
                            <div class="loading-bar-processed"
                                 style="height: 100%; width: {{vm.uploadingDrawings.attachmentUploadProgress}}%;">
                            </div>
                        </div>
                    </div>

                    <!-- Actual drawing upload section -->
                    <div>

                        <div style="text-align: center;">
                            <h3 style="margin-top: 0;">Upload Software Files</h3>
                            <button class="md-block feather-icon-button"
                                    style="transform: scale(1.2);"
                                    ng-disabled="vm.disabled"
                                    title="Click to upload file.">
                                <img src="/content/feather/upload.svg" />
                            </button>
                            <div style="margin-top: 16px; font-size: 11px;">Click or Drag 'n' Drop</div>
                        </div>
                    </div>

                </div>
            </td>
            <td ng-if="vm.referenceIsRequired()" style="padding-left: 30px">
                <div class="drawing-upload-area clickable {{vm.dragDropDisabled() ? 'drag-drop-disabled' : ''}}"
                     style="height: 130px;"
                     ngf-drop="vm.uploadFiles($files, vm.option.reference)"
                     ngf-select="vm.uploadFiles($files, vm.option.reference)"
                     ngf-multiple="true"
                     ngf-drag-over-class="'drawing-upload-file-over'"
                     ng-disabled="vm.dragDropDisabled()">

                    <!-- Notification of upload in progress -->
                    <div ng-if="vm.uploadingDrawings != null"
                         style="display: grid; grid-template-columns: 1fr; align-items: center; justify-items: center;">

                        <!-- Upload + Processing Progress bar -->
                        <h3 style="margin-top: 0;">Uploading...</h3>
                        <div class="loading-bar-background"
                             style="width: 400px;">
                            <div class="loading-bar-processed"
                                 style="height: 100%; width: {{vm.uploadingDrawings.attachmentUploadProgress}}%;">
                            </div>
                        </div>
                    </div>

                    <!-- Actual drawing upload section -->
                    <div>

                        <div style="text-align: center;">
                            <h3 style="margin-top: 0;">Upload Software Files</h3>
                            <button class="md-block feather-icon-button"
                                    style="transform: scale(1.2);"
                                    ng-disabled="vm.disabled"
                                    title="Click to upload file.">
                                <img src="/content/feather/upload.svg" />
                            </button>
                            <div style="margin-top: 16px; font-size: 11px;">Click or Drag 'n' Drop</div>
                        </div>
                    </div>

                </div>
            </td>
        </tr>

        <!-- "Process" buttons, to process the scratch file -->
        <!-- If the required software files are uploaded, then we allow processing to commence. -->
        <tr ng-if="vm.option.assessmentSoftware.assessmentSoftwareCode != 'Other'">

            <td style="font-weight: bold;">
                Import Data
            </td>
            <td>
                <md-button class="md-raised md-primary"
                           ng-disabled="vm.disabled || !vm.requiredFilesAreUploaded(vm.option, vm.option.proposed) || vm.option.proposed.processingScratch"
                           ng-click="vm.processFiles(vm.option, false)">
                    Process
                </md-button>
                <md-tooltip ng-if="!vm.requiredFilesAreUploaded(vm.option, vm.option.proposed)"
                            md-direction="left">
                    Missing required files.
                </md-tooltip>
            </td>
            <td ng-if="vm.referenceIsRequired()" style="padding-left: 30px">
                <md-button class="md-raised md-primary"
                           ng-disabled="vm.disabled || !vm.requiredFilesAreUploaded(vm.option, vm.option.reference) || vm.option.reference.processingScratch"
                           ng-click="vm.processFiles(vm.option, true)">
                    Process
                </md-button>
                <md-tooltip ng-if="!vm.requiredFilesAreUploaded(vm.option, vm.option.reference)"
                            md-direction="left">
                    Missing required files.
                </md-tooltip>
            </td>
        </tr>

    </tbody>
</table>

<style>

    .file-uploads-table > thead > tr > th,
    .file-uploads-table > tbody > tr > td {
        padding: 0;
        margin: 0;
    }

    .drag-drop-disabled {
        box-shadow: none !important;
        border: none !important;
        cursor: default !important;
    }
    .drag-drop-disabled > div {
        opacity: 0.6;
        cursor: default !important;
    }

</style>