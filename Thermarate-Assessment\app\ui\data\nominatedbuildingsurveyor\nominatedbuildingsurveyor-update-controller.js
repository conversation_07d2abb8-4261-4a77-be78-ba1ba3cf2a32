(function () {
    // The NominatedBuildingSurveyorUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'NominatedBuildingSurveyorUpdateCtrl';
    angular.module('app')
        .controller(controllerId,
            ['$rootScope', '$scope', '$mdDialog',
                '$stateParams', '$state', 'nominatedbuildingsurveyorservice', 'security',
                nominatedBuildingSurveyorUpdateController]);

    function nominatedBuildingSurveyorUpdateController($rootScope, $scope, $mdDialog, $stateParams,
            $state, nominatedbuildingsurveyorservice, securityservice) {

        // The model for this form
        var vm = this;
        console.log(vm);

        vm.spinnerOptions = {};
        vm.isBusy = true;

        vm.title = 'Edit Building Surveyor';
        vm.previousRoute = $rootScope.previousState;

        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;

        console.log($scope.buildingSurveyor);
        console.log($stateParams.buildingSurveyorId);

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Building Surveyor";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.buildingSurveyorId = $scope.buildingSurveyorId;
            }
            vm.hideActionBar = true;
        } else {
            vm.buildingSurveyorId = $stateParams.buildingSurveyorId;
        }

        // Get data for object to display on page
        var buildingSurveyorIdPromise = null;
        if (vm.buildingSurveyorId != null) {

            buildingSurveyorIdPromise = nominatedbuildingsurveyorservice.getBuildingSurveyor(vm.buildingSurveyorId)
            .then(function (data) {
                if (data != null) {
                    vm.buildingSurveyor = data;

                    console.log(data);
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("nominatedbuildingsurveyor-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                nominatedbuildingsurveyorservice.createBuildingSurveyor(vm.buildingSurveyor).then(function(data){
                    vm.buildingSurveyor = data;
                    vm.buildingSurveyorId = vm.buildingSurveyor.buildingSurveyorId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                nominatedbuildingsurveyorservice.updateBuildingSurveyor(vm.buildingSurveyor).then(function(data){
                    if (data != null) {
                        vm.buildingSurveyor = data;
                        vm.buildingSurveyorId = vm.buildingSurveyor.buildingSurveyorId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            nominatedbuildingsurveyorservice.deleteBuildingSurveyor(vm.buildingSurveyorId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            nominatedbuildingsurveyorservice.undoDeleteBuildingSurveyor(vm.buildingSurveyorId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();