using System;
using System.Linq;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Globalization;

namespace Kendo.DynamicLinq
{
    /// <summary>
    /// Represents a filter expression of Kendo DataSource.
    /// </summary>
    [DataContract]
    public class Filter
    {
        /// <summary>
        /// Gets or sets the name of the sorted field (property). Set to <c>null</c> if the <c>Filters</c> property is set.
        /// </summary>
        [DataMember(Name = "field")]
        public string Field { get; set; }

        /// <summary>
        /// Gets or sets the filtering operator. Set to <c>null</c> if the <c>Filters</c> property is set.
        /// </summary>
        [DataMember(Name = "operator")]
        public string Operator { get; set; }

        /// <summary>
        /// Gets or sets the filtering value. Set to <c>null</c> if the <c>Filters</c> property is set.
        /// </summary>
        [DataMember(Name = "value")]
        public object Value { get; set; }

        /// <summary>
        /// Gets or sets the filtering value.  Used for to on a range or between
        /// </summary>
        [DataMember(Name = "value2")]
        public object Value2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DataMember(Name = "valueType")]
        public string ValueType { get; set; }

        /// <summary>
        /// Gets or sets the filtering logic. Can be set to "or" or "and". Set to <c>null</c> unless <c>Filters</c> is set.
        /// </summary>
        [DataMember(Name = "logic")]
        public string Logic { get; set; }

        /// <summary>
        /// Gets or sets the child filter expressions. Set to <c>null</c> if there are no child expressions.
        /// </summary>
        [DataMember(Name = "filters")]
        public List<Filter> Filters { get; set; }

        /// <summary>
        /// Mapping of Kendo DataSource filtering operators to Dynamic Linq
        /// </summary>
        private static readonly IDictionary<string, string> operators = new Dictionary<string, string>
        {
            {"eq", "=="},
            {"neq", "!="},
            {"lt", "<"},
            {"lte", "<="},
            {"gt", ">"},
            {"gte", ">="},
            {"null", "== null"},
            {"notnull", "!= null"},
            {"in", "In"},
            {"startswith", "StartsWith"},
            {"endswith", "EndsWith"},
            {"contains", "Contains"},
            {"doesnotcontain", "Contains"},
            {"after", ">"},
            {"before", "<"},
        };

        /// <summary>
        /// Get a flattened list of all child filter expressions.
        /// </summary>
        public IList<Filter> All()
        {
            var filters = new List<Filter>();

            Collect(filters);

            return filters;
        }

        private void Collect(IList<Filter> filters)
        {
            if (Filters != null && Filters.Any())
            {
                foreach (Filter filter in Filters)
                {
                    filters.Add(filter);

                    filter.Collect(filters);
                }
            }
            else
            {
                filters.Add(this);
            }
        }

        private int filterIndex = 0;
        public Filter ConvertFilterString(Filter filter, List<string> filterStrings, Boolean isStart = false)
        {
            if (filterStrings != null && filterStrings.Any())
            {
                for (; filterIndex < filterStrings.Count; )
                {
                    Boolean isSetEnd = false;
                    string fls = filterStrings[filterIndex];
                    if (fls.StartsWith("("))
                    {
                        fls.TrimStart('(');
                        if (isStart != true)
                        {
                            if (filter.Filters == null)
                            {
                                filter.Filters = new List<Filter>();
                                Filter emptyFilter = new Filter();
                                filter.Filters.Add(emptyFilter);
                            }
                            ConvertFilterString(filter.Filters.Last(), filterStrings, true);
                            continue;
                        }
                    }
                    if (fls.EndsWith(")"))
                    {
                        isSetEnd = true;
                        fls.TrimEnd(')');
                    }
                    var parts = fls.Split(new string[] { "@@" }, StringSplitOptions.None);
                    Filter filterObj = new Filter() { Field = parts[0],
                                                      Operator = parts[1],
                                                      Value = parts[2],
                                                      ValueType = parts[3],
                                                      Logic = parts.Length > 4 ? parts[4] : "" };
                    if (!string.IsNullOrEmpty(filterObj.ValueType))
                    {
                        switch (filterObj.ValueType)
                        {
                            case "integer":
                                int intValue;
                                if (int.TryParse((string)filterObj.Value, out intValue))
                                {
                                    filterObj.Value = intValue;
                                }
                                break;
                            case "decimal":
                                decimal decValue;
                                if (decimal.TryParse((string)filterObj.Value, out decValue))
                                {
                                    filterObj.Value = decValue;
                                }
                                break;
                            case "date":
                                DateTime dateValue;
                                if (DateTime.TryParse((string)filterObj.Value, out dateValue))
                                {
                                    filterObj.Value = DateTime.Parse((string)filterObj.Value, null, DateTimeStyles.RoundtripKind).ToLocalTime().Date;
                                    DateTime theDate = (DateTime)filterObj.Value;
                                    switch (filterObj.Operator)
                                    {
                                        case "eq":
                                        case "neq":
                                            filterObj.Value2 = theDate.AddHours(23).AddMinutes(59).AddSeconds(59).AddMilliseconds(999);
                                            filterObj.Value = new DateTime(theDate.Year, theDate.Month, theDate.Day, 0, 0, 0);
                                            break;
                                        case "gt":
                                        case "after":
                                            filterObj.Value = theDate.AddHours(23).AddMinutes(59).AddSeconds(59).AddMilliseconds(999);
                                            break;
                                        case "lt":
                                        case "before":
                                            filterObj.Value = new DateTime(theDate.Year, theDate.Month, theDate.Day, 0, 0, 0);
                                            break;
                                        case "lte":
                                            filterObj.Value = theDate.AddHours(23).AddMinutes(59).AddSeconds(59).AddMilliseconds(999);
                                            break;
                                        case "gte":
                                            filterObj.Value = new DateTime(theDate.Year, theDate.Month, theDate.Day, 0, 0, 0);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                break;
                            case "datetime":
                                DateTime dateValue2;
                                if (DateTime.TryParse((string)filterObj.Value, out dateValue2))
                                {
                                    filterObj.Value = DateTime.Parse((string)filterObj.Value, null, DateTimeStyles.RoundtripKind).ToLocalTime();
                                }
                                break;
                            case "boolean":
                                Boolean boolValue;
                                if (Boolean.TryParse((string)filterObj.Value, out boolValue))
                                {
                                    filterObj.Value = boolValue;
                                }
                                break;
                            case "guid":
                            case "guid?":
                                Guid guidValue;
                                if (Guid.TryParse((string)filterObj.Value, out guidValue))
                                {
                                    filterObj.Value = (Guid?)guidValue;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    if (filter.Filters == null)
                    {
                        filter.Filters = new List<Filter>();
                    }
                    filter.Filters.Add(filterObj);

                    filterIndex++;
                    if (isSetEnd)
                    {
                        return filter;
                    }
                }
            }

            return filter;
        }

        /// <summary>
        /// Converts the filter expression to a predicate suitable for Dynamic Linq e.g. "Field1 = @1 and Field2.Contains(@2)"
        /// </summary>
        /// <param name="filters">A list of flattened filters.</param>
        public string ToExpression(IList<Filter> filters)
        {

            int index = filters.IndexOf(this);
            string clause = "";

            if (Operator != null)
            {
                clause = FormatClause(index);
            }


            if (Filters != null && Filters.Any())
            {
                return clause + " (" + String.Join(" ", Filters.Select(filter => filter.ToExpression(filters)).ToArray()) + ")";
            }

            return clause;
        }

        public string FormatClause(int index)
        {
            string comparison = operators[Operator];

            int valPos = index * 2;
            switch (Operator)
            {
                case "doesnotcontain":
                    return String.Format("!{0}.{1}(@{2}) {3}", Field, comparison, valPos, Logic);
                case "startswith":
                case "endswith":
                case "contains":
                    return String.Format("{0}.{1}(@{2}) {3}", Field, comparison, valPos, Logic);
                case "null":
                case "notnull":
                    return String.Format("{0} {1} {3}", Field, comparison, valPos, Logic);
                case "in":
                    return String.Format("{0}.{1}(@{2}) {3}", Field, comparison, valPos, Logic);
                default:
                    if (ValueType == "date" && Value2 != null)
                    {
                        switch (Operator)
                        {
                            case "eq":
                                return String.Format("({0} {1} @{2} and {0} {4} @{5}) {3}", Field, ">=", valPos, Logic, "<=", valPos+1);
                            case "neq":
                                return String.Format("!({0} {1} @{2} and {0} {4} @{5}) {3}", Field, ">=", valPos, Logic, "<=", valPos + 1);
                            default:
                                return String.Format("{0} {1} @{2} {3}", Field, comparison, valPos, Logic);
                        }
                        
                    }
                    else if (ValueType == "guid?")
                    {
                        return String.Format("{0}.Value.Equals(@{2}) {3}", Field, comparison, valPos, Logic);
                    }
                    else if (ValueType == "guid")
                    {
                        return String.Format("{0}.Equals(@{2}) {3}", Field, comparison, valPos, Logic);
                    }
                    else
                    {
                        return String.Format("{0} {1} @{2} {3}", Field, comparison, valPos, Logic);
                    }


            }
        }
    }
}
