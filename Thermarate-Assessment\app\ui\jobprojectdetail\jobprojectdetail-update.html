<form name="jobprojectdetailform" class="main-content-wrapper" novalidate data-ng-controller='JobprojectdetailUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <!--Left Side-->
                <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}" >
                    <md-card>
                        <md-card-header>
                            <span class="md-headline">Job Project Detail</span>
                        </md-card-header>
                        <md-card-content>

<!-- ******** Job ******** -->
                            <md-autocomplete md-input-name="jobId" md-autofocus 
                                         required
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.jobprojectdetail.job"
                                         md-search-text="vm.jobIdSearchText"
                                         md-items="item in vm.getjobs(vm.jobIdSearchText)"
                                         md-item-text="item.clientJobNumber"
                                         md-require-match
                                         md-floating-label="Job">
                                <md-item-template>
                                    <span md-highlight-text="vm.jobIdSearchText">{{item.clientJobNumber}}</span>
                                </md-item-template>
                                <div ng-messages="jobprojectdetailform.jobId.$error">
                                    <div ng-message="required">Job is required.</div>
                                </div>
                            </md-autocomplete>

<!-- ******** Plan Type ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Plan Type</label>
                                <md-select name="lotTypeCode"  
                                        ng-model="vm.jobprojectdetail.lotTypeCode"><!--planTypeCode-->
                                    <md-option ng-value>none</md-option>
                                    <md-option ng-value="item.lotTypeCode" 
                                            ng-repeat="item in vm.lotTypeList track by item.lotTypeCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

<!-- ******** Prefix ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Prefix</label>
                                <input type="text" name="prefix" 
                                        ng-model="vm.jobprojectdetail.prefix"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="jobprojectdetailform.prefix.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Strata Lot Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Strata Lot Number</label>
                                <input type="text" name="strataLotNumber" 
                                        ng-model="vm.jobprojectdetail.strataLotNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="jobprojectdetailform.strataLotNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Survey Strata Lot Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Survey Strata Lot Number</label>
                                <input type="text" name="surveyStrataLotNumber" 
                                        ng-model="vm.jobprojectdetail.surveyStrataLotNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="jobprojectdetailform.surveyStrataLotNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Original Lot Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Original Lot Number</label>
                                <input type="text" name="originalLotNumber" 
                                        ng-model="vm.jobprojectdetail.originalLotNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="jobprojectdetailform.originalLotNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Lot Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Lot Number</label>
                                <input type="text" name="lotNumber" 
                                        ng-model="vm.jobprojectdetail.lotNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="jobprojectdetailform.lotNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Original Deposited Plan Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Deposited Plan Number</label>
                                <input type="text" name="originalDepositedPlanNumber" 
                                        ng-model="vm.jobprojectdetail.originalDepositedPlanNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="jobprojectdetailform.originalDepositedPlanNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Volume ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Volume</label>
                                <input type="text" name="volume" 
                                        ng-model="vm.jobprojectdetail.volume"  
                                        md-maxlength="40"
                                    />
                                <div ng-messages="jobprojectdetailform.volume.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 40.</div>
                                </div>
                            </md-input-container>

<!-- ******** Folio ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Folio</label>
                                <input type="text" name="folio" 
                                        ng-model="vm.jobprojectdetail.folio"  
                                        md-maxlength="40"
                                    />
                                <div ng-messages="jobprojectdetailform.folio.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 40.</div>
                                </div>
                            </md-input-container>

<!-- ******** House Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>House Number</label>
                                <input type="text" name="houseNumber" 
                                        ng-model="vm.jobprojectdetail.houseNumber"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="jobprojectdetailform.houseNumber.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Street Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Street Name</label>
                                <input type="text" name="streetName" 
                                        ng-model="vm.jobprojectdetail.streetName"  
                                        md-maxlength="400"
                                    />
                                <div ng-messages="jobprojectdetailform.streetName.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 400.</div>
                                </div>
                            </md-input-container>

<!-- ******** Street Type ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Street Type</label>
                                <input type="text" name="streetType" 
                                        ng-model="vm.jobprojectdetail.streetType"  
                                        md-maxlength="100"
                                    />
                                <div ng-messages="jobprojectdetailform.streetType.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

<!-- ******** Suburb ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Suburb</label>
                                <input type="text" name="suburb" 
                                        ng-model="vm.jobprojectdetail.suburb"  
                                        md-maxlength="100"
                                    />
                                <div ng-messages="jobprojectdetailform.suburb.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

<!-- ******** State Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>State Code</label>
                                <input type="text" name="stateCode" 
                                        ng-model="vm.jobprojectdetail.stateCode"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="jobprojectdetailform.stateCode.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Postcode ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Postcode</label>
                                <input type="text" name="postcode" 
                                        ng-model="vm.jobprojectdetail.postcode"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="jobprojectdetailform.postcode.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Local Government Authority ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Local Government Authority</label>
                                <input type="text" name="localGovernmentAuthority" 
                                        ng-model="vm.jobprojectdetail.localGovernmentAuthority"  
                                        md-maxlength="100"
                                        required
                                    />
                                <div ng-messages="jobprojectdetailform.localGovernmentAuthority.$error">
                                    <div ng-message="required">Local Government Authority is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

<!-- ******** Project ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Project</label>
                                <md-select name="projectDescriptionCode"  
                                        ng-model="vm.jobprojectdetail.projectDescriptionCode">
                                    <md-option ng-value>none</md-option>
                                    <md-option ng-value="item.projectDescriptionCode" 
                                            ng-repeat="item in vm.projectDescriptionList track by item.projectDescriptionCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.jobprojectdetail"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>

<!-- ******** Right Side ******** -->
            <div ng-if="vm.newRecord==false" flex-gt-sm="50">

<!-- ******** Google Map ******** -->
                <md-card ng-if="vm.newRecord==false" >
                    <md-card-header>
                        Map
                    </md-card-header>
                    <md-card-content>
                        <div id="location-map"></div>
                        <div ng-show="vm.jobprojectdetail.latitude==null && vm.jobprojectdetail.longitude==null">Location coordinates unavailable.</div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="jobprojectdetailform.$invalid" ng-show="vm.jobprojectdetail.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.jobprojectdetail.jobId!=null && vm.jobprojectdetail.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.jobprojectdetail.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
