(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelSpecifications', {
      bindings: {
        theModel: '<',          // The 'StandardHomeModel' used as the basis of the option data
        variableOptions: '<',   // The options (key, []) that the user can select from.
        variableMetadata: '<',  //
        project: '<',           // Project is required to limit available selections.
        onDataChanged: '&',     // Callback fired when any variable is changed.
        copyAcrossEnabled: '<', // Whether to show Copy Across button
        copyAcrossData: '=?',   // Tell parent which option of which field to set in all buildings
        copyAcrossTrigger: '&', // Trigger Copy Across in parent
        required: '<',          // Specify whether all inputs are required or not
        disabled: '<',          // Specify whether inputs should be disabled
        multiSelect: '<',       // Specify whether inputs are multi-select
        initialiseOptions: '<'  // Whether to initialise all multi-select options to 'Any'
      },
      templateUrl: 'app/ui/energy-labs/standard-model-specifications-form.html',
      controller: StandardModelSpecificationsController,
      controllerAs: 'vm'
    });

  StandardModelSpecificationsController.$inject = ['$scope', '$timeout', 'common', 'standardmodelservice'];

  function StandardModelSpecificationsController($scope, $timeout, common, standardmodelservice) {

    let vm = this;

    // Some special variables are shown under a "Block" heading, the rest go under "Specifications".
    // Hopefully this doesn't change too much.
    const NOT_SPECIFICATION_VARIABLES = standardmodelservice.notSpecificationVariables;

    function initialize() {
        // Set all to 'Any' by default
        if (vm.multiSelect && vm.initialiseOptions) {
            for (const key in vm.theModel.optionData) {
                if (!NOT_SPECIFICATION_VARIABLES.includes(key)) {
                    vm.theModel.optionData[key] = ['Any'];
                }
            }
        }
    }
    initialize();

    vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;

    var oldSelections = {};
    vm.dataChanged = function () {
        if (vm.multiSelect) {
            oldSelections = common.applyMultiSelectChangedLogic(vm.theModel.optionData, oldSelections, NOT_SPECIFICATION_VARIABLES);
        }
        vm.onDataChanged();
    }

    vm.multiFieldText = function (selected) {
        return common.getMultiSelectDisplayText(vm.theModel.optionData, selected);
    }

    if(vm.variableMetadata?.generalOptionData != null) {
        orderOptions(vm.variableOptions, vm.variableMetadata.generalOptionData);
    }

    function orderOptions(optionData, metadata) {
      for (const key in optionData) {
        const meta = metadata[key];
        // No specific order set so continue...
        if(meta == null && Array.isArray(optionData[key])) {
            optionData[key].sort(nilFirstThenAlphabetical);
        } else {
            sortBasedOnOrder(optionData[key], meta);
        }
      }
    }

    vm.isSpecificationVariable = function(key) {
      const found = NOT_SPECIFICATION_VARIABLES.find(x => x === key);
      return found == null;
    }

    vm.propertiesInOrder = function() {

      const allProps = vm.project.energyLabsSettings.properties;
      const props = [];

      for(const key in allProps) {
        if(allProps[key] === true && vm.isSpecificationVariable(key)) {
          props.push(key);
        }
      }

      props.sort((a, b) => {

        const ax = PROP_ORDER[a];
        const bx = PROP_ORDER[b];

        if(ax == null)
          throw "Detected property not found in PROP_ORDER. This indicates a new variable has been added to the extraction process without updating the corresponding logic. Property was: " + a;

        if(bx == null)
          throw "Detected property not found in PROP_ORDER. This indicates a new variable has been added to the extraction process without updating the corresponding logic. Property was: " + b;

        return ax - bx;

      });

      return props;

    }

    const PROP_ORDER = standardmodelservice.specPropOrder;

    vm.showAnyInputs = function() {

      let total = 0;
      for(const key in vm.project?.energyLabsSettings?.properties) {
        const value = vm.project.energyLabsSettings.properties[key];

        if(vm.isSpecificationVariable(key) && value === true)
          total++;
      }

      return total > 0;
    }

    vm.keyToName = standardmodelservice.keyToName;

    $scope.nilFirstThenAlphabetical = nilFirstThenAlphabetical;
    function nilFirstThenAlphabetical(ac, bc) {

      if(ac.value === "Nil")
        return -1;

      if (typeof ac.type === 'string' && bc.type === 'string') {
        return ac.value.localeCompare(bc.value);
      } else {
        return (ac.value || 0) - (bc.value || 0);
      }
    }

    function sortBasedOnOrder(options, metadata) {

      options?.sort((ax, bx) => {

        const a = metadata.find(y => y.optionValue === ax);
        const b = metadata.find(y => y.optionValue === bx);

        if(a?.sortOrder != null && b?.sortOrder != null)
          return a.sortOrder - b.sortOrder;

        if(a?.sortOrder != null && b?.sortOrder == null)
          return -1;

        if(a?.sortOrder == null && b?.sortOrder != null)
          return -1;

        // Falllback to string compare when so no sort order set on any.
        if (typeof a == 'string' && b != null) {
          return a.localeCompare(b);
        } else {
          return (a || 9999) - (b || 9999);
        }
      });

    }

    vm.clearFilter = function (key) {
        vm.theModel.optionData[key] = ['Any'];
        vm.onDataChanged();
    }

    vm.copyOptionAcross = function (event, field, option) {
        // Tell parent the field and option so it can set on every other building
        vm.copyAcrossData.field = field;
        vm.copyAcrossData.option = option;
        $timeout(() => {
            // Now tell parent to copy across
            vm.copyAcrossTrigger();
            // Make sure option still clicked so dropdown collapses
            event.currentTarget.previousElementSibling.click();
        });
    }

  }

})();