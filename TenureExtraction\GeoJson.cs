﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TenureExtraction
{
    // Root myDeserializedClass = JsonConvert.DeserializeObject<GeoJsonRoot>(myJsonResponse); 
    public class Properties
    {
        [JsonProperty("OBJECTID")]
        public string ObjectID { get; set; }

        [JsonProperty("ELEVATION")]
        public string Elevation { get; set; }

        [JsonProperty("FEATREL")]
        public string FeatRel { get; set; }

        [JsonProperty("ATTRREL")]
        public string AttrRel { get; set; }

        [JsonProperty("PLANACC")]
        public string PlanAcc { get; set; }

        [JsonProperty("SOURCE")]
        public string Source { get; set; }

        [JsonProperty("LGA_CODE20")]
        public string LgaCode20 { get; set; }

        [JsonProperty("LGA_NAME20")]
        public string LgaName20 { get; set; }

        [JsonProperty("STE_CODE16")]
        public string SteCode16 { get; set; }

        [JsonProperty("STE_NAME16")]
        public string SteName16 { get; set; }

        [JsonProperty("Climate")]
        public string Climate { get; set; }
    }

    public class Crs
    {
        [JsonProperty("type")]
        public string Type;

        [JsonProperty("properties")]
        public Properties Properties;
    }

    public class Geometry
    {
        [JsonProperty("type")]
        public string Type;

        [JsonProperty("coordinates")]
        public List<List<List<double>>> Coordinates;
    }

    public class Feature
    {
        [JsonProperty("geometry")]
        public Geometry Geometry;

        [JsonProperty("type")]
        public string Type;

        [JsonProperty("properties")]
        public Properties Properties;
    }

    public class GeoJsonRoot
    {
        [JsonProperty("type")]
        public string Type;

        [JsonProperty("crs")]
        public Crs Crs;

        [JsonProperty("features")]
        public List<Feature> Features;
    }


}
