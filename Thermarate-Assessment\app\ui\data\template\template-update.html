<form name="templateform" class="main-content-wrapper" novalidate data-ng-controller='TemplateUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex">
                    <md-card>
                        <md-card-header>
                            Template
                        </md-card-header>
                        <md-card-content>

<!-- ******** Template Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Template Name</label>
                                <input type="text" name="templateName" 
                                        ng-model="vm.template.templateName" md-autofocus 
                                        md-maxlength="30"
                                        required
                                    />
                                <div ng-messages="templateform.templateName.$error">
                                    <div ng-message="required">Template Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 30.</div>
                                </div>
                            </md-input-container>

<!-- ******** Email Subject ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Email Subject</label>
                                <input type="email" name="emailSubject" 
                                        ng-model="vm.template.emailSubject"  
                                        md-maxlength="150"
                                        required
                                    />
                                <div ng-messages="templateform.emailSubject.$error">
                                    <div ng-message="required">Email Subject is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 150.</div>
                                </div>
                            </md-input-container>

<!-- ******** Category ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Category</label>
                                <md-select name="categoryCode"  
                                        required
                                        ng-model="vm.template.categoryCode">
                                    <md-option ng-value="item.code" 
                                            ng-repeat="item in vm.templateCategoryList track by item.code">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="templateform.categoryCode.$error">
                                    <div ng-message="required">Category is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Template ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Template</label>
                                <textarea name="template" 
                                        ng-model="vm.template.template"  
                                        required
                                        redactor
                                    ></textarea>
                                <div ng-messages="templateform.template.$error">
                                    <div ng-message="required">Template is required.</div>
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.template"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="templateform.$invalid" ng-show="vm.template.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.template.templateId>0 && vm.template.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.template.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
