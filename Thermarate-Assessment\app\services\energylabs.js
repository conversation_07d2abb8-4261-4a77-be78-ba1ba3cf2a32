﻿(function () {
	'use strict';

	angular
		.module('appservices')
		.factory('energylabsservice', ['nathersclimatezoneservice', 'nccclimatezonedataservice', 'common', EnergyLabsService]);

	function EnergyLabsService(nathersclimatezoneservice, nccclimatezonedataservice, common) {

		var service = {
			setBlockDataFromSuburb: setBlockDataFromSuburb,
			renderDonutChart: renderDonutChart,
		};
		return service;

        // Set block data
		async function setBlockDataFromSuburb(building, suburb, existingOptions, finishedCallback) {

            if (suburb == null)
                return;

            if(building.optionData == null)
                building.optionData = {};

            building.suburb = suburb.name;

            building.stateCode = suburb.stateCode;
          
            let natHERS = await nathersclimatezoneservice.getNatHERSClimateZone(suburb.natHERSClimateZoneCode);
            let natHersNum = Number(natHERS.description);
            const climateZoneIsValid = existingOptions?.natHERSClimateZone.includes(natHersNum);
            if(climateZoneIsValid) {
                building.climateZoneIsInvalid = false;
                building.optionData.natHERSClimateZone = natHersNum;
            } else {
                building.climateZoneIsInvalid = true;
                building.optionData.natHERSClimateZone = null;
            }
            let ncc = await nccclimatezonedataservice.getClimateZone(suburb.latitude, suburb.longitude);
            building.optionData.nccClimateZone = ncc;
            if (finishedCallback != null) {
                finishedCallback();
            }
		}

        // Render Orientate donut chart
        function renderDonutChart(data, targetEnergyRating, heatingCoolingLoadLimitsEnabled) {
            
            let chartData = data?.map(x => { return {
                ...x.optionData,
                modelIndex: x.modelIndex,
                stateCode: x.optionData.stateCode,
                y: 45,
                color: "#FAFAFA",
                results: x.results,
            }});
            
            chartData.forEach(buildingChartData => {

                buildingChartData.results = buildingChartData.results.map(x => ({
                    ...x,
                    y: 45,
                    color: "#FAFAFA",
                }));
                
                let thermalPerformanceSummaryChart = Highcharts.chart("central-north-offset-donut-chart-" + buildingChartData.modelIndex, {

                    chart: {
                        type: 'pie',
                        height: 1050,
                        width: 1050,
                        style: {
                            fontFamily: "Poppins",
                        }
                    },
                    title: {
                        enabled: false,
                        text: '',
                        subtitle: {
                            text: 'Total',
                            verticalAlign: 'middle',
                            align: 'center',
                            y: 10,
                            floating: true,
                        },
                    },
                    credits: { enabled: false },

                    legend: {
                        enabled: false,
                    },

                    tooltip: {
                        pointFormat: 'Energy Usage: {point.y:.1f} <b>({point.percentage:.1f}%)</b>',
                        enabled: false
                    },

                    plotOptions: {
                        pie: {
                            states: {
                                inactive:  {
                                    opacity: 1
                                },
                                hover: {
                                    color: "var(--thermarate-green-light)"
                                },
                            },
                            dataLabels: {
                                enabled: true,
                                useHTML: true,
                                formatter: function () {
                                    
                                    const radians = (this.point.northOffset + 90) * Math.PI / 180;
                                    const distance = 60;
                                    let x = distance * Math.cos(radians);
                                    let y = distance * Math.sin(radians);
                                    
                                    // Adjust top corner boxes
                                    if ([315].includes(this.point.northOffset)) {
                                        x += 9;
                                        y -= 9;
                                    }
                                    if ([45].includes(this.point.northOffset)) {
                                        x -= 9;
                                        y -= 9;
                                    }
                                    // Adjust middle boxes
                                    if ([90, 270].includes(this.point.northOffset)) {
                                        y -= 7;
                                    }
                                    // Adjust bottom boxes
                                    if ([135].includes(this.point.northOffset)) {
                                        x -= 9;
                                        y -= 9;
                                    }
                                    if ([225].includes(this.point.northOffset)) {
                                        x += 9;
                                        y -= 9;
                                    }
                                    if ([180].includes(this.point.northOffset)) {
                                        y -= 18;
                                    }

                                    const heatingColorVar = determineCalcResultColour(this.point.assessmentMethod, this.point.heatingLoad, this.point.energyLoadLimits.heatingLoadLimit, heatingCoolingLoadLimitsEnabled);
                                    const coolingColorVar = determineCalcResultColour(this.point.assessmentMethod, this.point.coolingLoad, this.point.energyLoadLimits.coolingLoadLimit, heatingCoolingLoadLimitsEnabled);
                                    const totalColorVar = common.lessThanOrEqualish(this.point.totalEnergyLoad, this.point.energyLoadLimits.calculatedMaxEnergy, 1) ? 'var(--thermarate-green)' : 'var(--warning)';
                                    const ratingColorVar = this.point.energyRating >= 7 ? 'var(--thermarate-green)' : 'var(--warning)';
                                    const regulation = this.point.assessmentMethod === "Performance Solution (Energy Load Limits)" ? "NCC" : this.point.stateCode === "NSW" ? "BASIX" : "NCC";

                                    let ratingString = this.point.assessmentMethod === 'Performance Solution (Energy Load Limits)'
                                      ? "" // No energy rating shown for certain assessment methods.
                                      : `
  <!-- Energy Rating -->
  <div class="el-orientation-result-data reveal-child popup-container">
    <span class="el-result-heading-text">Energy Rating</span>
    <div class="el-orientate-result-text smaller-text">
      <span style="color: ${ratingColorVar}">
          ${this.point.energyRating.toFixed(1)}
      </span>
    </div>
    
    <!-- Popup with Additional Data -->
    <div class="solid-popup-html show-on-parent-hover"
                style="margin-bottom:125px"
                md-direction="bottom">
      <h4 class="el-popup-heading">Energy Rating</h4>
      <table>
        <tbody>
        <tr>
          <td style="min-width: 100px; font-weight: normal;">Target (min)</td>
          <td style="font-weight: bold;">${targetEnergyRating != null ? targetEnergyRating.toFixed(1) : '7.0'}</td>
        </tr>
        <tr>
          <td style="font-weight: normal;">Calculated</td>
          <td style="font-weight: bold;"><span style="color: ${ratingColorVar}">${this.point.energyRating.toFixed(1)}</span>
        </tr>
        <tr>
          <td style="font-weight: normal;">Regulations</td>
          <td style="font-weight: bold;">${regulation}</td>
        </tr>
        </tbody>
      </table>
    </div>
    
  </div>`;

                                    return `
<div class="el-orientation-result-grid small" style="transform: translate(${x}px, ${y - 75}px">
  
  <!-- Heating -->
  <div class="el-orientation-result-data reveal-child popup-container">
    <span class="el-result-heading-text">Heating (MJ/m<sup>2</sup>)</span>
    <span class="el-orientate-result-text smaller-text">
      <span style="color: ${heatingColorVar}">
          ${this.point.heatingLoad.toFixed(1)}
        </span>

      <!-- Popup with Additional Data -->
        <div class="solid-popup-html show-on-parent-hover"
             style="margin-left:-61%; margin-bottom:-45px"
             md-direction="bottom">
          <h4 class="el-popup-heading">Heating Load (MJ/m<sup>2</sup>)</h4>
          <table>
            <tbody>
            <tr>
              <td style="min-width: 100px; font-weight: normal;">Target (max)</td>
              <td style="font-weight: bold;">${determineTarget(this.point.assessmentMethod, this.point.energyLoadLimits.heatingLoadLimit?.toFixed(1), heatingCoolingLoadLimitsEnabled)}</td>
            </tr>
            <tr>
              <td style="font-weight: normal;">Calculated</td>
              <td style="font-weight: bold;"><span style="color:${heatingColorVar}">${this.point.heatingLoad.toFixed(1)}</span></td>
            </tr>
            <tr>
              <td style="font-weight: normal;">Regulations</td>
              <td style="font-weight: bold;">${regulation}</td>
            </tr>
            </tbody>
          </table>
        </div>
      
    </span>
  </div>

  <!-- Cooling -->
  <div class="el-orientation-result-data reveal-child popup-container">
    <span class="el-result-heading-text">Cooling (MJ/m<sup>2</sup>)</span>
    <span class="el-orientate-result-text smaller-text">
      <span style="color: ${coolingColorVar}">
          ${this.point.coolingLoad.toFixed(1)}
      </span>
    </span>
    
    <!-- Popup with Additional Data -->
    <div class="solid-popup-html show-on-parent-hover"
                style="margin-bottom:-45px"
                md-direction="bottom">
      <h4 class="el-popup-heading">Cooling Load (MJ/m<sup>2</sup>)</h4>
      <table>
        <tbody>
        <tr>
          <td style="min-width: 100px; font-weight: normal;">Target (max)</td>
          <td style="font-weight: bold;">${determineTarget(this.point.assessmentMethod, this.point.energyLoadLimits.coolingLoadLimit?.toFixed(1), heatingCoolingLoadLimitsEnabled)}</td>
        </tr>
        <tr>
          <td style="font-weight: normal;">Calculated</td>
          <td style="font-weight: bold;"><span style="color: ${coolingColorVar}">${this.point.coolingLoad.toFixed(1)}</span></tr>
        <tr>
          <td style="font-weight: normal;">Regulations</td>
          <td style="font-weight: bold;">${regulation}</td>
        </tr>
        </tbody>
      </table>
    </div>
    
  </div>
  
  <!-- Total -->
  <div class="el-orientation-result-data reveal-child popup-container">
    <span class="el-result-heading-text">Total (MJ/m<sup>2</sup>)</span>
    <span class="el-orientate-result-text smaller-text">
      <span style="color: ${totalColorVar}">
          ${this.point.totalEnergyLoad.toFixed(1)}
      </span>

    </span>
    
    <!-- Popup with Additional Data -->
    <div class="solid-popup-html show-on-parent-hover"
                style="margin-bottom:125px">
      <h4 class="el-popup-heading">Total Energy Load (MJ/m<sup>2</sup>)</h4>
      <table>
        <tbody>
        <tr>
          <td style="min-width: 100px; font-weight: normal;">Target (max)</td>
          <td style="font-weight: bold;">${this.point.energyLoadLimits.calculatedMaxEnergy.toFixed(1)}</td>
        </tr>
        <tr>
          <td style="font-weight: normal;">Calculated</td>
          <td style="font-weight: bold;"><span style="color: ${totalColorVar}">${this.point.totalEnergyLoad.toFixed(1)}</span>
        </tr>
        <tr>
          <td style="font-weight: normal;">Regulations</td>
          <td style="font-weight: bold;">${regulation}</td>
        </tr>
        </tbody>
      </table>
    </div>
    
  </div>
  
  ${ratingString}
  
</div>`;

                                },
                                distance: -50,
                                style: {
                                    fontWeight: 'bold',
                                    fontSize: '22px',
                                    color: 'white'
                                }
                            },
                            size: '85%', // or will cut into legend
                            showInLegend: true,
                            startAngle: -22.5,
                            // endAngle: 347.75,
                        },
                        series: { enableMouseTracking: true },
                    },

                    series: [{
                        type: 'pie',
                        name: 'x / m²',
                        innerSize: '45%',
                        data: buildingChartData.results,
                    }]
                });
            });
        }


        // ----------- //
        // - Private - //
        // ----------- //

        // Determine target
        function determineTarget(assessmentMethod, limit, heatingCoolingLoadLimitsEnabled) {
            if (assessmentMethod == "House Energy Rating (HER)" && (heatingCoolingLoadLimitsEnabled == false || limit == null)) {
                return 'N/A';
            } else {
                return limit;
            }
        }

        // Determine calc result colout
        function determineCalcResultColour(assessmentMethod, value, limit, heatingCoolingLoadLimitsEnabled) {
            if (assessmentMethod == "House Energy Rating (HER)" && heatingCoolingLoadLimitsEnabled == false || limit == null) {
                return 'black';
            } else if (common.lessThanOrEqualish(value, limit, 1)) {
                return 'var(--thermarate-green)';
            } else {
                return 'var(--warning)';
            }
        }

	}

})();