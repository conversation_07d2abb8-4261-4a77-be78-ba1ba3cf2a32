(function () {
    // The ConstructionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ConstructionUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'constructionservice', 'colourservice', 'manufacturerservice','assessmentsoftwareservice', 'security', constructionUpdateController]);
    function constructionUpdateController($rootScope, $scope, $mdDialog, $stateParams,
            $state, constructionservice, colourservice, manufacturerservice, assessmentsoftwareservce, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = false;
        var eventListenerList = [];

        vm.templateType = $scope.type ?? $stateParams.type;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.title = 'Edit ' + vm.templateType;
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.constructionId = null;
        vm.hasInsulations = false;

        vm.construction = {
            insulationData: {
                hasData: false
            },
            lifeCycleData: {},
            glassData: {},
            chenathData: {},
            fr5Data: {},
            heroData: {},
            epData: {},
            visualisationData: {}
        };

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New " + vm.templateType;
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false) {
                vm.constructionId = $scope.constructionId;
            }
            vm.hideActionBar = true;
        } else {
            vm.constructionId = $stateParams.constructionId;
        }

        if (vm.constructionId != null) {
            constructionservice.getConstruction(vm.constructionId, $stateParams.type)
                .then(data => {
                    vm.construction = data;

                    // Edit existing (filter sub category list based on constructionCategory)
                    constructionservice.getConstructionSubCategoryList()
                    .then((data) => {
                        // Need to save the master list and have a filtered list that changes.
                        vm.constructionSubCategories = data;
                        vm.filteredConstructionSubCategories = vm.filterSubCategories(vm.constructionSubCategories);
                    });
                });
        }
        else {
            // Add New (no existing constructionId)
            constructionservice.getConstructionSubCategoryList()
            .then((data) => {
                // Need to save the master list and have a filtered list that changes.
                vm.constructionSubCategories = data;
                vm.filteredConstructionSubCategories = [];
            });
        }

        // Get a whole bunch of data.
        assessmentsoftwareservce.getList()
            .then((data) => { vm.assessmentSoftwareList = data.data; });
        constructionservice.getConstructionCategoryList()
            .then((data) => {

                vm.constructionCategories = data;

                // Restrict selection by type...
                if (vm.templateType.toLowerCase() === "construction")
                    vm.constructionCategories = constructionservice.constructionCategories();
                else if (vm.templateType.toLowerCase() === "opening")
                    vm.constructionCategories = constructionservice.openingCategories();

                if(vm.newRecord) {
                    vm.constructionCategories = vm.constructionCategories.filter(x => x.type !== "permanentopening");
                }

            });
        constructionservice.getAdjacencyList()
            .then(data => { vm.adjacencyList = data; });
        constructionservice.getUnitOfMeasureList()
             .then(data => { vm.unitOfMeasureList = data; });
        constructionservice.getAirCavityList()
            .then(data => { vm.airCavityList = data; });
        constructionservice.getFrameMaterialList()
            .then(data => { vm.frameMaterialList = data; });
        constructionservice.getGlassTypeList()
            .then(data => { vm.glassTypeList = data; });
        constructionservice.getGlassColourList()
            .then(data => { vm.glassColourList = data; });
        constructionservice.getOpeningStyleList()
            .then(data => { vm.openingStyleList = data; });
        constructionservice.getNccOpeningStyleList()
            .then(data => { vm.nccOpeningStyleList = data; });
        colourservice.getAll()
            .then(data => { vm.colourList = data; });

        // md-autocomplete throws a critical error if it is ever supplied with a
        // null value (as can happen before vm.colourList is loaded).
        vm.colourListSafe = () => vm.colourList ?? [];

        vm.manufacturers = [];
        var manufacturerPromise = manufacturerservice.getList()
            .then(data => { vm.manufacturers = data.data; });

        // Functions to get data for Typeahead
        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("construction-list");
                }
            }
        }

        vm.toggle = function (type, prop) {

            // If it's CURRENTLY null or false, we just create it and set the initial
            // 'has' value to true, all other fields blank
            if (vm.construction[type] == null || vm.construction[type][prop] == false) {
                //vm.construction[type] = {};
                vm.construction[type][prop] = vm.construction[type][prop];
            } else {
                // Otherwise if it's currently TRUE we destroy it and re-set
                // the 'has' value to false.
                let backup = vm.construction[type][prop];
                vm.construction[type] = {};
                vm.construction[type][prop] = backup;
            }
        }

        vm.constructionCategoryChanged = function () {
            // TODO: Nullify stuff not required etc...
            // (rofl)

            // Clear the current Sub Category as it might not be valid for the new Category.
            vm.construction.subCategory = null;

            // Refilter the sub category list based on the new selected constructionCategory
            vm.filteredConstructionSubCategories = vm.filterSubCategories(vm.constructionSubCategories);
        }

        vm.filterSubCategories = function filterSubCategories(subCategories) {

            if (!vm.construction?.category?.constructionCategoryCode || !subCategories)
                return [];

            // Filter the Construction Sub Category list
            if (vm.construction.category.constructionCategoryCode.toLowerCase().includes('wall'))
                subCategories = subCategories.filter(x => x.type === 'wall');
            else if (vm.construction.category.constructionCategoryCode.toLowerCase().includes('floor'))
                subCategories = subCategories.filter(x => x.type === 'floor');
            else if (vm.construction.category.constructionCategoryCode.toLowerCase() === 'roof')
                subCategories = subCategories.filter(x => x.type === 'roof');
            else if (vm.construction.category.constructionCategoryCode.toLowerCase().includes('ceiling'))
                subCategories = subCategories.filter(x => x.type === 'ceiling');

            return subCategories;
        }

        vm.save = function () {

            vm.isBusy = true;
            if (vm.newRecord == true) {

                if (vm.categoryIsSurface() || vm.categoryIsPermanentOpening()) {
                    constructionservice.createSurface(vm.construction).then(function (data) {
                        navToNewConstruction(data, 'surface');});
                } else if (vm.categoryIsGlazing() || vm.categoryIsRoofLighting()) {

                    constructionservice.createOpening(vm.construction).then(function (data) {
                        navToNewConstruction(data, 'opening');
                   });
                }

            } else {

                if (vm.categoryIsSurface() || vm.categoryIsPermanentOpening()) {
                    constructionservice.updateSurface(vm.construction).then(function (data) {
                        vm.isBusy = false;
                    });
                } else if (vm.categoryIsGlazing() || vm.categoryIsRoofLighting()) {

                    constructionservice.updateOpening(vm.construction).then(function (data) {
                        vm.isBusy = false;
                    });
                }
            }

            vm.isBusy = false;
        }

        /**
         * Navigate to construction update page of given id.
         * @param {any} id
         */
        function navToNewConstruction(id, type) {
            vm.isBusy = false;
            vm.cancel();
            $state.go("construction-updateform", { constructionId: id, type: type });
        }

        vm.categoryIsSurface = () => vm.construction?.category?.type === "surface";
        vm.categoryIsGlazing = () => vm.construction?.category?.type === "glazing";
        vm.categoryIsRoofLighting = () => vm.construction?.category?.type === "rooflight";
        vm.categoryIsPermanentOpening = () => vm.construction?.category?.type === "permanentopening"
        vm.categoryIsFloor = () => vm.construction?.category?.title?.toLowerCase().includes("floor") && !vm.construction?.category?.title?.toLowerCase().includes("subfloor");
        vm.categoryIsDoor = () => vm.construction?.category?.title?.toLowerCase().includes("door");

        vm.openingListForCategory = function (exclusionFilter) {

            let list = [];

            if (vm.construction.category.constructionCategoryCode === "RoofWindow") 
                list = vm.openingStyleList?.filter(x => x.type === 'roof' || x.type === 'all')
            else if(vm.construction.category.constructionCategoryCode === 'Skylight')
                list = vm.openingStyleList?.filter(x => x.openingStyleCode === "Fixed");
            else if(vm.construction.category.constructionCategoryCode === "HorizontalOpening" ||
                    vm.construction.category.constructionCategoryCode === "VerticalOpening")
                list = vm.openingStyleList?.filter(x => x.openingStyleCode === "PermanentOpening")
            else {
                list = vm.openingStyleList?.filter(x => x.type === 'wall' || x.type === 'all')
            }

            // Instead of filter by INCLUSION, we filter by EXCLUSION so that, for instance,
            // things like 'Fixed' and 'Other' are still included
            if (exclusionFilter != null) {
                exclusionFilter = exclusionFilter.toLowerCase()
                list = list?.filter(x => !x.openingStyleCode.toLowerCase().includes(exclusionFilter));
            }

            return list;
        }

        vm.delete = function () {
            vm.isBusy = true;
            constructionservice.deleteConstruction(vm.constructionId, vm.construction.category.type).then(function () {
                vm.construction.deleted = true;
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            constructionservice.undoDeleteConstruction(vm.constructionId, vm.construction.category.type).then(function () {
                vm.construction.deleted = false;
                vm.isBusy = false;
            });
        }

        vm.applyColour = function (colour, solarProperty) {

            if (colour == null)
                return;

            vm.construction[solarProperty + "SolarAbsorptance"] = colour.solarAbsorptance;
        }

        vm.setOpeningStyle = function (construction, style) {
            construction.openability = style.defaultOpenability;
            construction.nccOpeningStyle = style.nccOpeningStyle;
        }

        vm.setNccOpeningStyle = function (nccStyle) {

            if (nccStyle.defaultOpenability == null) {
                // Do nothing...
            } else {
                vm.construction.openability = nccStyle.defaultOpenability;
            }
        }

        vm.chenathConstructionRanges = () => constructionservice.chenathConstructionRanges;

        vm.softwareUsingScratchFiles = function () {
            return vm.assessmentSoftwareList?.filter(x => x.fileBName == "Scratch File");
        }

        // AngularJS Filter
        // must be $scope and not vm
        // ---
        // Some Subfloor Ventilation types need to be filtered beyond their type.
        // Exterior Floor (Connected to Ground) - Subfloor Ventilation
        //     Connected to Ground
        // Exterior Floor (Suspended) - Subfloor Ventilation
        //     Enclosed
        //     Enclosed Disconnected
        //     Open
        //     Very Open
        // Exterior Floor (Elevated) - Subfloor Ventilation
        //     Elevated
        $scope.subfloorVentilationFilter = function(category) {
            return function(item) {

                let result = false;

                switch (category.constructionCategoryCode) {
                    case 'GroundFloor':
                        result = item.airCavityCode === 'SubfloorConnectedToGround'
                        break;
                    case 'ExteriorFloor':
                        result = item.type === category.ventilationType && 
                            item.airCavityCode != 'SubfloorConnectedToGround' &&
                            item.airCavityCode != 'SubfloorElevated';
                        break;
                    case 'ExteriorFloorElevated':
                        result = item.airCavityCode === 'SubfloorElevated'
                        break;
                    default:
                        result = item.type === category.ventilationType;
                        break;
                }

                return result;
            }
        }
    }
})();