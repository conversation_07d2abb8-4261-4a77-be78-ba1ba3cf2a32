(function () {
    'use strict';
    var controllerId = 'ImportDrawingsModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'jobservice', 'assessmentservice', 'assessmentdrawingservice', importDrawingsModalController]);
    function importDrawingsModalController($scope, $mdDialog, jobservice, assessmentservice, assessmentdrawingservice) {
        var vm = this;

        vm.jobId = $scope.jobId;
        vm.clientId = $scope.clientId;
        vm.clientName = $scope.clientName;
        vm.assessmentId = $scope.assessmentId;

        vm.selectedFiles = [];
        vm.selectedCount = 0;
        vm.isFiltered = false;

        // Loads up any drawings from the current or previous assessments which were INCLUDED IN A REPORT
        // (Fairly certain this means issued...). So don't be surprised if a bunch of test assessments show no drawings.
        if (vm.clientId != null && vm.assessmentId != null) {
            vm.currentJob = null;
            vm.jobList = [];

            jobservice.getBasicClientList(vm.clientId).then(function (data) {
                for (var ii = 0, len = data.length; ii < len; ii++) {
                    data.isOpen = false;
                    if (data[ii].jobId == vm.jobId) {
                        vm.currentJob = data[ii];
                        vm.currentJob.isOpen = true;
                    } else {
                        vm.jobList.push(data[ii]);
                    }
                }

                assessmentservice.getBasicAssessmentList(vm.jobId).then(function (data) {
                    vm.currentJob.assessments = [];
                    for (var ii = 0, len = data.length; ii < len; ii++) {
                        data[ii].isOpen = true;
                        if (data[ii].assessmentId != vm.assessmentId) {
                            vm.currentJob.assessments.push(data[ii]);
                        } 
                    }

                    for (var ii = 0, len = vm.currentJob.assessments.length; ii < len; ii++) {
                        (function (ii) {
                            assessmentdrawingservice.getByAssessment(vm.currentJob.assessments[ii].assessmentId, false, true).then(function (data) {
                                for (var jj = 0, len = data.length; jj < len; jj++) {
                                    data[jj].isOpen = true;
                                    data[jj].isSelected = false;
                                }
                                vm.currentJob.assessments[ii].assessmentDrawings = data;
                            });
                        })(ii);
                    }
                });

                for (var ii = 0, len = vm.jobList.length; ii < len; ii++) {
                    vm.jobList[ii].assessments = [];
                    (function (ii) {
                        assessmentservice.getBasicAssessmentList(vm.jobList[ii].jobId).then(function (data) {
                            for (var jj = 0, len = data.length; jj < len; jj++) {
                                data[jj].isOpen = false;
                            }

                            vm.jobList[ii].assessments = data;

                            for (var jj = 0, len = vm.jobList[ii].assessments.length; jj < len; jj++) {
                                vm.jobList[ii].assessments[jj].assessmentDrawings = [];

                                (function (ii, jj) {
                                    assessmentdrawingservice.getByAssessment(vm.jobList[ii].assessments[jj].assessmentId, false, true).then(function (data) {
                                        for (var kk = 0, len = data.length; kk < len; kk++) {
                                            data[kk].isOpen = false;
                                            data[kk].isSelected = false;
                                        }

                                        vm.jobList[ii].assessments[jj].assessmentDrawings = data;
                                    });
                                })(ii, jj);
                            }
                        });
                    })(ii);
                }
            });
        }

        vm.open = function (item) {
            if (item.isOpen) {
                item.isOpen = false;
            } else {
                item.isOpen = true;
            }
        }

        vm.select = function (item) {
            if (item.isSelected) {
                vm.selectedCount += 1;
            } else {
                vm.selectedCount -= 1;
            }
        }

        vm.filterBySearch = function () {
            if (vm.searchText == null || vm.searchText == "") {
                vm.resetFilter();
                return;
            }

            vm.isFiltered = true;

            for (var jj = 0, jlen = vm.currentJob.assessments.length; jj < jlen; jj++) {
                for (var kk = 0, klen = vm.currentJob.assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                    if (vm.currentJob.assessments[jj].assessmentDrawings[kk].attachmentDisplayName) {
                        if (vm.currentJob.assessments[jj].assessmentDrawings[kk].attachmentDisplayName.toLowerCase().search(vm.searchText.toLowerCase()) != -1) {
                            vm.currentJob.filterFound = true;
                            vm.currentJob.isOpen = true;
                            vm.currentJob.assessments[jj].filterFound = true;
                            vm.currentJob.assessments[jj].isOpen = true;
                            vm.currentJob.assessments[jj].assessmentDrawings[kk].filterFound = true;
                        }
                    }
                }
            }

            for (var ii = 0, ilen = vm.jobList.length; ii < ilen; ii++) {
                for (var jj = 0, jlen = vm.jobList[ii].assessments.length; jj < jlen; jj++) {
                    for (var kk = 0, klen = vm.jobList[ii].assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                        if (vm.jobList[ii].assessments[jj].assessmentDrawings[kk].attachmentDisplayName) {
                            if (vm.jobList[ii].assessments[jj].assessmentDrawings[kk].attachmentDisplayName.toLowerCase().search(vm.searchText.toLowerCase()) != -1) {
                                vm.jobList[ii].filterFound = true;
                                vm.jobList[ii].isOpen = true;
                                vm.jobList[ii].assessments[jj].filterFound = true;
                                vm.jobList[ii].assessments[jj].isOpen = true;
                                vm.jobList[ii].assessments[jj].assessmentDrawings[kk].filterFound = true;
                            }
                        }
                    }
                }
            }
        }

        vm.resetFilter = function () {
            vm.searchText = null;
            vm.isFiltered = false;

            for (var ii = 0, ilen = vm.jobList.length; ii < ilen; ii++) {
                vm.jobList[ii].filterFound = false;
                for (var jj = 0, jlen = vm.jobList[ii].assessments.length; jj < jlen; jj++) {
                    vm.jobList[ii].assessments[jj].filterFound = false;
                    for (var kk = 0, klen = vm.jobList[ii].assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                        vm.jobList[ii].assessments[jj].assessmentDrawings[kk].filterFound = false;
                    }
                }
            }
        }

        vm.toggleArchive = function () {
            if (vm.allowArchiveDrawings == true) {
                for (var jj = 0, jlen = vm.currentJob.assessments.length; jj < jlen; jj++) {
                    for (var kk = 0, klen = vm.currentJob.assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                        if (vm.currentJob.assessments[jj].assessmentDrawings[kk].archived) {
                            vm.currentJob.assessments[jj].assessmentDrawings[kk].isSelected = false;
                            vm.selectedCount -= 1;
                        }
                    }
                }

                for (var ii = 0, ilen = vm.jobList.length; ii < ilen; ii++) {
                    for (var jj = 0, jlen = vm.jobList[ii].assessments.length; jj < jlen; jj++) {
                        for (var kk = 0, klen = vm.jobList[ii].assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                            if (vm.jobList[ii].assessments[jj].assessmentDrawings[kk].archived) {
                                if (vm.jobList[ii].assessments[jj].assessmentDrawings[kk].isSelected) {
                                    vm.jobList[ii].assessments[jj].assessmentDrawings[kk].isSelected = false;
                                    vm.selectedCount -= 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        $scope.$watch('vm.searchText', function (newVal, oldVal) {
            if (newVal != oldVal) {
                if (newVal == null || newVal == "") {
                    vm.resetFilter();
                }
            }
        });

        vm.populateSelectedList = function () {
            vm.selectedFiles = [];
            for (var jj = 0, jlen = vm.currentJob.assessments.length; jj < jlen; jj++) {
                for (var kk = 0, klen = vm.currentJob.assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                    if (vm.currentJob.assessments[jj].assessmentDrawings[kk].isSelected) {
                        vm.selectedFiles.push(vm.currentJob.assessments[jj].assessmentDrawings[kk]);
                    }
                }
            }

            for (var ii = 0, ilen = vm.jobList.length; ii < ilen; ii++) {
                for (var jj = 0, jlen = vm.jobList[ii].assessments.length; jj < jlen; jj++) {
                    for (var kk = 0, klen = vm.jobList[ii].assessments[jj].assessmentDrawings.length; kk < klen; kk++) {
                        if (vm.jobList[ii].assessments[jj].assessmentDrawings[kk].isSelected) {
                            vm.selectedFiles.push(vm.jobList[ii].assessments[jj].assessmentDrawings[kk]);
                        }
                    }
                }
            }
        }

        vm.submitSelection = function () {
            vm.populateSelectedList();
            $mdDialog.hide(vm.selectedFiles);
        };
    }
})();