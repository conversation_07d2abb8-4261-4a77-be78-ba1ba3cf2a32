angular.module('security.login.form', ['services.localizedMessages'])

// The LoginFormController provides the behaviour behind a reusable form to allow users to authenticate.
// This controller and its template (login/form.tpl.html) are used in a modal dialog box by the security service.
.controller('LoginFormController', ['$scope', 'security', 'localizedMessages', function($scope, security, localizedMessages) {
    // The model for this form 
    var vm = this;
    vm.user = {};
    vm.spinnerOptions = {};
    vm.isBusy = false;

    // Any error message from failing to login
    vm.authError = null;

    // The reason that we are being asked to login - for instance because we tried to access something to which we are not authorized
    // We could do something diffent for each reason here but to keep it simple...
    vm.authReason = null;
    if ( security.getLoginReason() ) {
    vm.authReason = ( security.isAuthenticated() ) ?
        localizedMessages.get('login.reason.notAuthorized') :
        localizedMessages.get('login.reason.notAuthenticated');
    }

    vm.enterLogin = function () {
        if(vm.user.email == undefined || vm.user.email == null || vm.user.email == "" ||
            vm.user.password == undefined || vm.user.password == null || vm.user.password == "") {
            return;
        }

        vm.login();
    }

    // Attempt to authenticate the user specified in the form's model
    vm.login = function() {

        var twofacexpDate = localStorage.getItem("2fa_expire_" + vm.user.email);
        var expire = new Date(parseInt(twofacexpDate, 10));

        // Clear any previous security errors
        vm.authError = null;
        vm.isBusy = true;

        security.isTwoFacAuthEnabled(vm.user.email).then(function (enabled) {

            var twofaRequire = false;

            if (enabled) {
                var now = new Date();
                twofaRequire = !twofacexpDate || expire === null || expire === undefined || now > expire;
            }

            if (twofaRequire) {
                vm.user.verificationCode = prompt("Two Factor Code", "");
            }
            // Try to login
            security.login(vm.user.email, vm.user.password, vm.user.verificationCode, twofaRequire).then(function (result) {
                vm.isBusy = false;
                var loggedIn = result === true;
                if (!loggedIn) {
                    // If we get here then the login failed due to bad credentials
                    if (result) {
                        vm.authError = result;
                    }
                    else {
                        vm.authError = localizedMessages.get('login.error.invalidCredentials');
                    }
                } else {
                    security.currentUser.twoFacAuthVerified = security.currentUser.twoFacAuthEnabled;
                }
            }, function (x) {
                // If we get here then there was a problem with the login request to the server
                vm.authError = localizedMessages.get('login.error.serverError', { exception: x });
                vm.isBusy = false;
            });
        });
    };

    vm.clearForm = function() {
        vm.user = {};
    };

    vm.cancelLogin = function () {
        vm.isBusy = false;
        security.cancelLogin();
    };

    vm.forgotLogin = function () {
        // Do stuff to send a new password.
        vm.isBusy = true;
        security.forgotPassword(vm.user.email).then(function (success) {
            console.log("forgot login func success = " + success);
            vm.isBusy = false;
            if (success == false) {
                vm.authError = localizedMessages.get('password.reset.error');
            }
        }, function (x) {
            // If we get here then there was a problem with the password reset request to the server
            vm.authError = localizedMessages.get('login.error.serverError', { exception: x });
            vm.isBusy = false;
        });
    }

}]);
