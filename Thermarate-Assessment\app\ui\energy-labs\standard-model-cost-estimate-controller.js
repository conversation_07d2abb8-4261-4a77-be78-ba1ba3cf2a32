(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelCostEstimate', {
      bindings: {
        source: '<', // Standard model with option data attached (i.e. post-result gathering)
        project: '<',
      },
      templateUrl: 'app/ui/energy-labs/standard-model-cost-estimate.html',
      controller: StandardModelCostEstimate,
      controllerAs: 'vm'
    });

  StandardModelCostEstimate.$inject = ['common', 'standardmodelservice'];

  function StandardModelCostEstimate(common, standardmodelservice) {

    const vm = this;

    vm.source = this.source;

    vm.optionEstimates = [];
    vm.totalCost = null;

    vm.linkOptionsToEstimates = function() {

      if(vm.project == null)
        return;

      const linked = standardmodelservice.linkOptionsToEstimates(vm.source, vm.project.energyLabsSettings.properties);

      if(linked.length === 0) {
        vm.totalCost = 0;
        return [];
      }

      vm.totalCost = linked?.map(x => Number(common.roundUpInt((x.quantity * x.ratePerUnit * (1 + (x.margin / 100))), x.rounding)))?.reduce((a,b) => a + b) || 0;
      return linked;
    }

    vm.zeroOrTwo = function() {

      if(vm.project == null)
        return;

      const linked = standardmodelservice.linkOptionsToEstimates(vm.source, vm.project.energyLabsSettings.properties);

      const anyWithNoRounding = linked.filter(x => x.rounding == null);

      return anyWithNoRounding?.length > 0 
        ? 2 
        : 0;
    }

    vm.roundUpInt = common.roundUpInt;

    vm.toSplitTitleCase = common.toSplitTitleCase;
    vm.keyToName = standardmodelservice.keyToName;

  }

})();