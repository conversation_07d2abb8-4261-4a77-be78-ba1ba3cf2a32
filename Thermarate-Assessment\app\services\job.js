// Name: jobservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'jobservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', jobservice]);

    function jobservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var clientcanceller = null;
        var useListCache = false;
        const baseUrl = config.servicesUrlPrefix + 'job/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListMultiFiltered: getListMultiFiltered,
            getListCancel: getListCancel,
            getActiveJobs: getActiveJobs,
            getActiveJobsMultiFiltered: getActiveJobsMultiFiltered,
            getActiveJobsForUser: getActiveJobsForUser,
            currentFilter: function () { return currentFilter },
            getJob: getJob,
            createJob: createJob,
            updateJob: updateJob,
            deleteJob: deleteJob,
            undoDeleteJob: undoDeleteJob,
            printPDF: printPDF,
            cancelJob: cancelJob,
            reinstateJob: reinstateJob,
            getClientList: getClientList,
            getClientListCancel: getClientListCancel,
            getBasicClientList: getBasicClientList,
            copyAssessment: copyAssessment,
            getAvailableCodesForFilter,
            getAllAssessors,
            getAllVersions,
            getMultiFilterOptions,
            getFilterCountData,
            getActiveJobsFilterCountData,
        };

        return service;

        function getList(fromDate, toDate, pageSize, pageIndex, sort, filter) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            var params = { fromDate, toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter);

            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Job list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListMultiFiltered(pageSize, pageIndex, sort, fields, filterOptions, appliedFilters, searchFilter) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetMultiFiltered';

            let filterData = { fields, filterOptions, appliedFilters };
            let paging = common.buildqueryparameters.build(null, pageSize, pageIndex, sort?.field != null ? [sort] : null, searchFilter);
            filterData.paging = paging;

            return $http.post(wkUrl, filterData).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Job list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getAvailableCodesForFilter(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, statusCode, assessorCode, clientCode ) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetAvailableCodesForFilter';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate, toDate, statusCode, assessorCode, clientCode };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }

            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Job list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getClientList(clientId, forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, statusCode, aggregate) {

            clientcanceller = $q.defer();
            var wkUrl = baseUrl + 'GetByClient';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate, statusCode: statusCode, clientId: clientId };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: false,
                timeout: clientcanceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Job list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getClientListCancel() {
            if (clientcanceller != null) {
                clientcanceller.resolve();
            }
        }

        function getBasicClientList(clientId) {
            clientcanceller = $q.defer();
            var wkUrl = baseUrl + 'GetBasicByClient';
            var params = { clientId: clientId };
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: false,
                timeout: clientcanceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Job list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getActiveJobs(pageSize = 100, pageIndex = 1) {
            return $http({
                url: baseUrl + 'GetActiveJobs',
                params: { pageSize: pageSize, pageIndex: pageIndex },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Active Jobs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getActiveJobsMultiFiltered(fields, filterOptions, appliedFilters, searchFilter, sort, pageSize = 100, pageIndex = 1) {
            var url = baseUrl + 'GetActiveJobsMultiFiltered';
            let filterData = { fields, filterOptions, appliedFilters, searchFilter };
            filterData.paging = common.buildqueryparameters.build(null, pageSize, pageIndex, sort?.field != null ? [sort] : null, searchFilter);
            return $http.post(url, filterData).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Active Jobs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getAllAssessors() {
            return $http({
                url: baseUrl + 'GetAllAssessors',
                params: {},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Active Jobs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getAllVersions() {
            return $http({
                url: baseUrl + 'GetAllVersions',
                params: {},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Active Jobs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getActiveJobsForUser(userId) {
            return $http({
                url: baseUrl + 'GetActiveJobsForUser',
                params: {userName: userId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Active Jobs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getJob(jobId) {
            return $http({
                url: baseUrl + 'Get',
                params: {jobId: jobId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getMultiFilterOptions(activeJobs, fieldsList) {
            var url = baseUrl + 'GetMultiFilterOptions?activeJobs=' + activeJobs;
            return $http.post(url, fieldsList).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getFilterCountData(fields, filterOptions, appliedFilters, searchFilter) {
            var url = baseUrl + 'GetFilterCountData?activeJobs=' + false;
            let filterData = { fields, filterOptions, appliedFilters, searchFilter };
            filterData.paging = common.buildqueryparameters.build(null, 10000, 1, null, searchFilter);
            return $http.post(url, filterData).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting filter count data: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getActiveJobsFilterCountData(fields, filterOptions, appliedFilters, searchFilter) {
            var url = baseUrl + 'GetFilterCountData?activeJobs=' + true;
            let filterData = { fields, filterOptions, appliedFilters, searchFilter };
            filterData.paging = common.buildqueryparameters.build(null, 10000, 1, null, searchFilter);
            return $http.post(url, filterData).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting filter count data: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createJob(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Job Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateJob(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Job Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteJob(jobId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { jobId: jobId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function cancelJob(jobId) {
            return $http({
                url: baseUrl + 'Cancel',
                params: { jobId: jobId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error cancelling Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // Reinstate a cancelled job
        function reinstateJob(jobId) {
            return $http({
                url: baseUrl + 'Reinstate',
                params: { jobId: jobId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error reinstating Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteJob(jobId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { jobId: jobId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function printPDF() {
            return $http({
                url: baseUrl + 'PrintPDF',
                params: { },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error printing PDF for Job: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyAssessment(data) {
            var url = baseUrl + 'Copy';
            return $http({
                url: url,
                method: 'POST',
                data: data,
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Assessment Copied to new Job");
                return resp;
            }
            function fail(error) {
                var msg = "Error copying assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
