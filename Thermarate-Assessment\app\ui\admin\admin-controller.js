(function () {
    'use strict';
    var controllerId = 'AdminCtrl';
    var adminModule = angular.module('admin', ['appservices']);
    adminModule.controller(controllerId, ['common', admin]); 

    function admin(common) {
        var getLogFn = common.logger.getLogFn;
        var log = getLogFn(controllerId);

        var vm = this;
        vm.title = 'Admin';

        activate();

        function activate() {
            common.activateController([], controllerId)
                .then(function () { log('Activated Admin View'); });
        }
    }
})();