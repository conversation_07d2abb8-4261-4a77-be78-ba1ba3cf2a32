(function () {
    // The EmployeeroleUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'EmployeeroleUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', 'businessroleservice',
            'aspnetusersservice', 'userservice','security', employeeroleUpdateController]);
    function employeeroleUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, businessroleservice, 
                                          aspnetusersservice, userservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Business Role';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.roleCode = null;
        vm.employeerole = {
            aspnetRoles: []
        };
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Business Role";
            // Set any default values required for a new record.
        }
        vm.allRoles = [];
        vm.groupedRoles = [];
        vm.editPermission = securityservice.immediateCheckRoles("settings__settings__edit");

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.roleCode = $scope.roleCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.roleCode = $stateParams.roleCode;
        }

        // Get data for object to display on page
        var roleCodePromise = null;
        if (vm.roleCode != null) {
            roleCodePromise = businessroleservice.getEmployeeRole(vm.roleCode)
            .then(function (data) {
                if (data != null) {
                    vm.employeerole = data;
                }
                vm.isBusy = false;
                });
        }
        else {
            vm.isBusy = false;
        }

        function getUserRoles() {
            aspnetusersservice.getRoles().then(function () {
                vm.allRoles = aspnetusersservice.roles();
                groupRolesByName(vm.allRoles);
                console.log("all roles: ", vm.allRoles)
                vm.isBusy = false;
            });
        }
        getUserRoles();

        $scope.toggleSelection = function toggleSelection(roleCode) {
            var idx = vm.employeerole.aspnetRoles.indexOf(roleCode);
            // is currently selected
            if (idx > -1) {
                vm.employeerole.aspnetRoles.splice(idx, 1);
            }
            // is newly selected
            else {
                vm.employeerole.aspnetRoles.push(roleCode);
            }
        };

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("employeerole-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord === true){
                businessroleservice.createEmployeeRole(vm.employeerole).then(function(data){
                    vm.employeerole = data;
                    vm.roleCode = vm.employeerole.roleCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                businessroleservice.updateEmployeeRole(vm.employeerole).then(function(data){
                    if (data != null) {
                        vm.employeerole = data;
                        vm.roleCode = vm.employeerole.roleCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            businessroleservice.deleteEmployeeRole(vm.roleCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            businessroleservice.undoDeleteEmployeeRole(vm.roleCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        function groupRolesByName(roles) { vm.groupedRoles = userservice.groupRolesByName(roles); }

    }
})();