(function () {
    // The BusinessunittypeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'BusinessunittypeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'businessunittypeservice', 'security', businessunittypeUpdateController]);
function businessunittypeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  businessunittypeservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Business Unit Type';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.type = null;
        vm.businessunittype = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Business Unit Type";
            // Set any default values required for a new record.
        }

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.type = $scope.type;
            }
            vm.hideActionBar = true;
        } else {
            vm.type = $stateParams.type;
        }

        // Get data for object to display on page
        var typePromise = null;
        if (vm.type != null) {
            typePromise = businessunittypeservice.getBusinessUnitType(vm.type)
            .then(function (data) {
                if (data != null) {
                    vm.businessunittype = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("businessunittype-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                businessunittypeservice.createBusinessUnitType(vm.businessunittype).then(function(data){
                    vm.businessunittype = data;
                    vm.type = vm.businessunittype.type;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                businessunittypeservice.updateBusinessUnitType(vm.businessunittype).then(function(data){
                    if (data != null) {
                        vm.businessunittype = data;
                        vm.type = vm.businessunittype.type;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            businessunittypeservice.deleteBusinessUnitType(vm.type).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            businessunittypeservice.undoDeleteBusinessUnitType(vm.type).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();