// Name: colourservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'colourservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', colourservice]);

    function colourservice(common, config, $http, Upload) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'colour/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getColour: getColour,
            createColour: createColour,
            updateColour: updateColour,
            deleteColour:deleteColour,
            undoDeleteColour: undoDeleteColour,
            copyColour,
            getAll,
            processSpreadsheet
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Colour list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting Colour List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }
        
        function getColour(id) {
            return $http({
                url: baseUrl + 'Get',
                params: { id },
                method: 'GET',
                cache: true, 
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createColour(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Colour Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateColour(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Colour Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteColour(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteColour(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyColour(colourId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { colourId },
                method: 'POST',
            }).then(success, fail)

            function success(resp) {
                log.logSuccess("Colour copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Colour: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function processSpreadsheet(file) {

            let url = baseUrl + 'ProcessSpreadsheet';
            var promise = Upload.upload({
                url: url, // webapi url
                method: "POST",
                file: file,
            });

            return promise.progress(function (evt) {
                
            }).success(function (data, status, headers, config) {

                // console.log(data);
                log.logSuccess("Colour Database Updated");
                return data;

            }).error(function (data, status, headers, config) {

                log.logError("Error updating Colour Database. Please check input file.", null, null, true);
            });
        }
    }
})();
