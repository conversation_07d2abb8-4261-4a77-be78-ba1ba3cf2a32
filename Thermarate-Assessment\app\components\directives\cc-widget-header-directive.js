﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('ccWidgetHeader', ['$rootScope', '$state', function ($rootScope, $state) {
        //Usage:
        //<div data-cc-widget-header title="vm.map.title"></div>
        var directive = {
            link: link,
            scope: {
                'title': '@',
                'subtitle': '@',
                'rightText': '@',
                'allowCollapse': '@',
                'isModal': '=',
                'cancel': '&',
                'backButton': '@'
            },
            templateUrl: 'app/ui/layout/widgetheader.html',
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-header');
            }
            else {
                attrs.$set('class', 'widget-head');
            }

            scope.previousRoute = $rootScope.previousState;
            scope.previousStateParams = $rootScope.previousStateParams;
            scope.goback = function (e) {
                e.preventDefault();
                if (scope.previousRoute != null) {
                    $state.go(scope.previousRoute, scope.previousStateParams);
                }
                else {
                    if (attrs.defaultNav != null) {
                        $state.go(attrs.defaultNav);
                    }
                    else {
                        // Go Home of nothing else set.
                        $state.go("home");
                    }
                }
            }
        }
    }]);
})();