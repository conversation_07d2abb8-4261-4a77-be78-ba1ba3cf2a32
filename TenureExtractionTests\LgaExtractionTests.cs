﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using TenureExtraction;

namespace ExtractionTests
{
    [TestClass]
    public class LgaExtractionTests
    {
        [TestMethod]
        public async Task TestLgaExtraction()
        {
            string sqlConnectionString = "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True";
            string geopackagePath = Directory.GetCurrentDirectory() + "/Data";

            var x = new LocalGovernmentAreaExtractor(geopackagePath, sqlConnectionString, 
                new Logger((s) => Console.WriteLine(s)));

            await x.UpdateDataset();

            ;
        }
    }
}
