﻿// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'sequentialguidservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', sequentialguidservice]);

    function sequentialguidservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var baseUrl = config.servicesUrlPrefix + 'sequentialguid/';

        var service = {
            /* These are the operations that are available from this service. */
            getGuid: getGuid,
            getList: getList,
        };

        return service;

        function getGuid() {
            return $http({
                url: baseUrl + 'Get',
                params: { },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Guid: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }


        function getList(numOfGuids) {
            return $http({
                url: baseUrl + 'GetList',
                params: { numOfGuids: numOfGuids},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Guid: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
