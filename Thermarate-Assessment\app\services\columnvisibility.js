// Name: userservice
// Type: Angular Service
// Purpose: To provide all table column visibility functions across seperate files
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'columnvisibilityservice';
    angular.module('appservices').factory(serviceId,
        ['$rootScope', '$mdDialog', columnvisibilityservice]);

    function columnvisibilityservice($rootScope, $mdDialog) {

        var service = {
        /* These are the operations that are available from this service. */
            openVisibilityModal : openVisibilityModal,
            isTableColumnVisible : isTableColumnVisible
        };
            
        return service;

        function openVisibilityModal(parent, successCallback) {
            // Load modal
            const modalScope = $rootScope.$new();
            modalScope.parent = parent;

            $mdDialog.show({
                templateUrl: 'app/ui/assessment/building-column-visibility-modal.html',
                scope: modalScope,
            }).then(function (response) {
                // Set result.
                successCallback(response);
            }, function () {
                // Cancelled
            });
        }

        function isTableColumnVisible(columnProperty, parent) {

            let isShown = true;

            // If the column is in the object and set to true, then it is hidden.
            if (parent?.hiddenTableColumns && 
                parent.hiddenTableColumns.hasOwnProperty(columnProperty) && 
                parent.hiddenTableColumns[columnProperty] === true)
                isShown = false;

            return isShown;
        }

    }
})();
