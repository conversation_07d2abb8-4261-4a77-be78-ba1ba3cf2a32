(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelList', {
      bindings: {
        clientId: '<',
        projectId: '<',
        projectEnergyLabsSettings: '<', // So can rerender when make changes in parent
        disabled: '<',
        parentIsActive: '<',
        parent3dModel: '<',
        parentCostEstimate: '<',
        onInitialiseComplete: '&',
        onDelete: '&',
        passedUpModels: '=', // So parent can save SortOrder's during it's own save process
        rowClickCallback: '&'
      },
      templateUrl: 'app/ui/energy-labs/standard-model-list.html',
      controller: standardModelListController,
      controllerAs: 'vm'
    });

  standardModelListController.$inject = ['$rootScope', '$scope', '$mdDialog', 'standardmodelservice', 'projectservice', 'daterangehelper'];

  function standardModelListController($rootScope, $scope, $mdDialog, standardmodelservice, projectservice, daterangehelper) {

    var vm = this;

    var modalInstance = {};
    vm.spinnerOptions = {};
    vm.isBusy = true;
    vm.title = 'Home Designs';
    vm.standardModelList = [];  ``

    vm.actionButtons = [];
    vm.currentFilter = "All";
    vm.totalRecords = 0;
    vm.showingFromCnt = 0;
    vm.showingToCnt = 0;
    vm.currentQuery = {};

    vm.bulkStatus = new MasterBulkStatus();
    vm.bulkStatus.checkboxId = "sm-allCheckbox";
    vm.bulkStatus.isIndeterminate = false;
    vm.bulkStatus.selectAllCheckboxState = false;

    var persistRangeName = "standardModelList-DtRange";
    vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);

    //Repopulate the List after Refresh Page
    vm.refreshList = function (filter) {
      vm.callServer(null);
      localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
    };

    vm.createStandardModel = function () {
      var modalScope = $rootScope.$new();
      modalScope.viewMode = "New";
      modalScope.projectId = vm.projectId;
      modalScope.newRecord = true;
      var modalOptions = {
        templateUrl: 'app/ui/data/standard-model/standard-model-update.html',
        scope: modalScope,
        resolve: {
          viewMode: function () {
            return 'New';
          }
        }
      };
      modalScope.modalInstance = $mdDialog.show(modalOptions);
      modalScope.modalInstance.then(function (data) {
        // Returned from modal, so refresh list.
        vm.refreshList(null);
      }, function () {
        vm.refreshList(null);
        // Cancelled.
      })['finally'](function () {
        modalScope.modalInstance = undefined  // <--- This fixes
      });
    }

    var saveTableState = null;
    vm.callServer = function callServer(tableState) {
        vm.isBusy = true;

        if (tableState != null)
            saveTableState = tableState;

        var pagination = saveTableState?.pagination;
        var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
        var pageSize = pagination.number || 100;  // Number of entries showed per page.
        var pageIndex = (start / pageSize) + 1;

        var sort = {};
        if (saveTableState.sort != null) {
            sort.field = saveTableState.sort.predicate;
            sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
        }

        // Save sort field to vm so know whether to show "Move Down" and "Move Up"
        vm.sortField = sort.field;

        var filter = null;

        if(vm.projectId != null)
            filter = [{ field: "projectId", operator: "eq", value: vm.projectId, valueType: "guid?" }];

        standardmodelservice.getListCancel();
        standardmodelservice.getList(
            vm.listFilter,
            vm.rptDateRange.startDate.toISOString(),
            vm.rptDateRange.endDate.toISOString(),
            pageSize,
            pageIndex,
            sort,
            filter
        ).then(
            result => {
                if (result != null) {
                    vm.currentFilter = standardmodelservice.currentFilter();
                    vm.standardModelList = result.data;
                    // Make sure no 2 SortOrders are the same
                    let tempList = angular.copy(vm.standardModelList);
                    tempList.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1);
                    for (let i = 0; i < tempList.length-1; i++) {
                        if (tempList[i].sortOrder == tempList[i+1].sortOrder) {
                            for (let j = i+1; j < tempList.length; j++) {
                                vm.standardModelList.find(m => m.standardModelId == vm.standardModelList[j].standardModelId).sortOrder++;
                            }
                        }
                    }
                    vm.passedUpModels = vm.standardModelList;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.standardModelList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    if (vm.onInitialiseComplete != null) {
                        setTimeout(vm.onInitialiseComplete); // In timeout so 'passedUpModels' is set by time the 'onInitialiseComplete()' runs
                    }
                    vm.isBusy = false;
                }
            },
            error => vm.isBusy = false
        );
    };

    function setActionButtons() {
      vm.actionButtons = [];
      vm.actionButtons.push({
        onclick: vm.createStandardModel,
        name: 'Add New',
        desc: 'Add New',
        roles: ['settings__settings__create'],
      });
    }

    setActionButtons();

    vm.uploadFile = function(file)
    {
      if (file == null)
        return;

      standardmodelservice.processSpreadsheet(file)
        .then((data) => {
          vm.standardModelList = data.data;
        });
    }

    // Move Up
    vm.moveModelUp = function (model) {
        // Find model before
        let modelBefore = vm.standardModelList.sort((a,b) => a.sortOrder < b.sortOrder ? 1 : -1).find(p => p.sortOrder < model.sortOrder);
        if (modelBefore == null) {
            model.sortOrder = 1;
        } else {
            // Swap sort orders
            let tempSortOrder = modelBefore.sortOrder;
            modelBefore.sortOrder = model.sortOrder;
            model.sortOrder = tempSortOrder;
        }
        // Sort new list
        vm.standardModelList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
    }

    // Move Down
    vm.moveModelDown = function (model) {
        // Find option after
        let modelAfter = vm.standardModelList.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1).find(p => p.sortOrder > model.sortOrder);
        if (modelAfter == null) {
            model.sortOrder = 1;
        } else {
            // Swap sort orders
            let tempSortOrder = modelAfter.sortOrder;
            modelAfter.sortOrder = model.sortOrder;
            model.sortOrder = tempSortOrder;
        }
        // Sort new list
        vm.standardModelList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
    }

    vm.delete = async function (row, override) {
        if (!override) {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Delete";
            modalScope.confirmationText = "Are you sure you want to delete this Home Design?";
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (confirmed) {

                if (confirmed) {
                    _delete(row);
                }

            });
        } else {
            _delete(row);
        }

        async function _delete(row) {
            await standardmodelservice.deleteStandardModel(row.standardHomeModelId);
            row.deleted = true;
            vm.standardModelList = vm.standardModelList.filter(x => x.standardHomeModelId !== row.standardHomeModelId);
            if (vm.onDelete) {
                vm.onDelete();
            }
        }
    }

    vm.bulkSelect = function (state) {
      vm.standardModelList.forEach(x => x.isBulkSelected = state);
    }

    vm.bulkSelectionsExist = function () {
      return vm.standardModelList.some(x => x.isBulkSelected);
    }

    vm.launchBulkEditModal = async function() {

        let modalScope = $rootScope.$new();
        modalScope.thisProjectId = vm.projectId;
        modalScope.clientId = vm.clientId;
        modalScope.projectEnergyLabsSettings = vm.projectEnergyLabsSettings;

        // Get selected models
        let selectedModels = vm.standardModelList.filter(x => x.checkboxSelected);

        // Determine default values based on selected models
        // If all selected models have false for a variable, default to false; otherwise, default to true
        let defaultIsActive = !selectedModels.every(m => m.isActive === false);
        let defaultView3dFloorPlans = !selectedModels.every(m => m.view3dFloorPlans === false);
        let defaultCostEstimateEnabled = !selectedModels.every(m => m.costEstimateEnabled === false);
        let defaultDesignInsightsEnabled = !selectedModels.every(m => m.variableMetadata?.designInsightsEnabled === false);

        // Pass parent settings and default values to modal
        modalScope.parentIsActive = vm.parentIsActive;
        modalScope.parent3dModel = vm.parent3dModel;
        modalScope.parentCostEstimate = vm.parentCostEstimate;
        modalScope.parentDesignInsights = vm.parentDesignInsights;

        modalScope.defaultIsActive = defaultIsActive;
        modalScope.defaultView3dFloorPlans = defaultView3dFloorPlans;
        modalScope.defaultCostEstimateEnabled = defaultCostEstimateEnabled;
        modalScope.defaultDesignInsightsEnabled = defaultDesignInsightsEnabled;

        $mdDialog.show({
            scope: modalScope,
            templateUrl: 'app/ui/energy-labs/modals/bulk-edit-standard-model-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: false,
        }).then(async function (response) {

            let selectedModels = vm.standardModelList.filter(x => x.checkboxSelected);

            if (response.bulkEditAction === "DELETE") {

                let modalScope = $rootScope.$new();
                modalScope.confirmationHeader = "Confirm Delete";
                modalScope.confirmationText = "Are you sure you want to delete these Home Designs?";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(async function (confirmed) {

                    if (confirmed) {
                        vm.bulkDeleteModels(selectedModels);
                    }

                });

            } else if (response.bulkEditAction === "COPYTOPROJECT") {

                vm.bulkCopyModelsToProject(response.selectedProjectId);

            } else if (response.bulkEditAction === "COPY") {

                vm.bulkCopyModels();

            } else {

                // Add toggle settings to the response object
                response.toggleSettings = {
                    isActive: { applyToAllLevels: response.isActiveApplyToAll || false },
                    view3dFloorPlans: { applyToAllLevels: response.view3dFloorPlansApplyToAll || false },
                    costEstimateEnabled: { applyToAllLevels: response.costEstimateEnabledApplyToAll || false },
                    designInsightsEnabled: { applyToAllLevels: response.designInsightsEnabledApplyToAll || false }
                };

                selectedModels.forEach(m => {
                    // Apply settings to this level
                    m.isActive = response.isActive;
                    m.view3dFloorPlans = response.view3dFloorPlans;
                    m.costEstimateEnabled = response.costEstimateEnabled;
                    m.variableMetadata.designInsightsEnabled = response.designInsightsEnabled;
                    m.variationOptionsSettings.floorplanIsActive = response.floorplanIsActive;
                    m.variationOptionsSettings.designOptionIsActive = response.designOptionIsActive;
                    m.variationOptionsSettings.facadeIsActive = response.facadeIsActive;
                    m.variationOptionsSettings.specificationIsActive = response.specificationIsActive;
                    m.variationOptionsSettings.configurationIsActive = response.configurationIsActive;

                    // Add toggle settings to the model
                    // Backend will handle applying to child levels if needed
                    m.toggleSettings = response.toggleSettings;
                });

            }

            selectedModels.forEach(m => m.checkboxSelected = false);
            vm.bulkStatus.isIndeterminate = false;
            vm.bulkStatus.selectAllCheckboxState = false;

        });

    }

    vm.bulkDelete = async function () {

      let toDelete = vm.standardModelList.filter(x => x.isBulkSelected);
      for (let i = 0; i < toDelete.length; i++) {
        await vm.delete(toDelete[i]);
      }
    }

    vm.copyToProject = function (row) {

        let modalScope = $rootScope.$new();
        modalScope.thisProjectId = vm.projectId;
        modalScope.clientId = vm.clientId;
        $mdDialog.show({
            scope: modalScope,
            templateUrl: 'app/ui/energy-labs/modals/copy-design-to-project-modal.html',
            parent: angular.element(document.body),
            clickOutsideToClose: false,
        }).then(async function (selectedProjectId) {

            if (selectedProjectId != null) {
                standardmodelservice.copyStandardModel(row.standardHomeModelId, selectedProjectId);
            }

        });
    }

    vm.clone = function (row) {
        standardmodelservice.copyStandardModel(row.standardHomeModelId).then(id => {
            standardmodelservice.getStandardModel(id).then(standardModel => {
                // Add returned template to list.
                vm.standardModelList.push(standardModel);
            });
        });
    }

    vm.selectAllCheckboxes = (a, b, c) => {

      setTimeout(() => {
        selectAllCheckboxes(a, b, c);
        safeApply();
      }, 25);
    }

    function safeApply() {
      const phase = $rootScope.$$phase;
      if (!phase) {
        $rootScope.$apply();
      }
    }

    vm.updateBulkSelectStatus = updateBulkSelectStatus;

    vm.bulkCopyModelsToProject = function (projectId) {
      const bulkSelected = vm.standardModelList.filter(x => x.checkboxSelected);
      bulkSelected.forEach(x => { standardmodelservice.copyStandardModel(x.standardHomeModelId, projectId); x.checkboxSelected = false; });
      vm.bulkStatus.isIndeterminate = false;
      vm.bulkStatus.selectAllCheckboxState = false;
    }

    vm.bulkCopyModels = function () {
      const bulkSelected = vm.standardModelList.filter(x => x.checkboxSelected);
      bulkSelected.forEach(x => { vm.clone(x); x.checkboxSelected = false; });
      vm.bulkStatus.isIndeterminate = false;
      vm.bulkStatus.selectAllCheckboxState = false;
    }

    vm.bulkDeleteModels = function (inList) {
      const bulkSelected = inList ?? vm.standardModelList.filter(x => x.checkboxSelected);
      bulkSelected.forEach(x => { vm.delete(x, true); x.checkboxSelected = false; });
      vm.bulkStatus.isIndeterminate = false;
      vm.bulkStatus.selectAllCheckboxState = false;
    }

  }
})();