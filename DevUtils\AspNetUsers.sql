USE [thermarate];

SELECT TOP (1000) [Id]
      ,[UserName]
      ,[PasswordHash]
      ,[SecurityStamp]
      ,[EmailConfirmed]
      ,[PhoneNumber]
      ,[PhoneNumberConfirmed]
      ,[TwoFactorEnabled]
    --   ,[LockoutEndDateUtc]
      ,FORMAT(DATEADD(HOUR, 8, [LockoutEndDateUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__LockoutEndDateFormatted]
      ,[LockoutEnabled]
      ,[AccessFailedCount]
      ,[ApplicationId]
      ,[LegacyPasswordHash]
      ,[LoweredUserName]
      ,[MobileAlias]
      ,[IsAnonymous]
      ,[LastActivityDate]
      ,[MobilePIN]
      ,[Email]
      ,[LoweredEmail]
      ,[PasswordQuestion]
      ,[PasswordAnswer]
      ,[IsApproved]
      ,[IsLockedOut]
      ,[CreateDate]
      ,[LastLoginDate]
      ,[LastPasswordChangedDate]
      ,[LastLockoutDate]
      ,[FailedPasswordAttemptCount]
      ,[FailedPasswordAttemptWindowStart]
      ,[FailedPasswordAnswerAttemptCount]
      ,[FailedPasswordAnswerAttemptWindowStart]
      ,[Comment]
      ,[LastTwoFacAuthDate]
      ,[TwoFacAuthSecret]
  FROM [dbo].[AspNetUsers]
  WHERE 1=1
	AND [Id] = 'A368DD06-FFB9-4986-AE21-479904571E4E'
    -- AND [UserName] = 'admin'
    -- AND [UserName] = '<EMAIL>'
    -- AND [UserName] = '<EMAIL>'
    -- AND [UserName] LIKE '%gmail%'
    -- AND [UserName] LIKE '%gmail%'
  ORDER BY [CreateDate] DESC

-- -------------
-- Delete a user
-- -------------
-- UPDATE [dbo].[RSS_User] SET [Deleted] = 1 WHERE [EmailAddress] = '<EMAIL>'
-- UPDATE [dbo].[RSS_User] SET [EmailAddress] = 'DELETED' WHERE [EmailAddress] = '<EMAIL>'
-- UPDATE [dbo].[AspNetUsers] SET [Email] = 'DELETED' WHERE [Email] = '<EMAIL>'
-- UPDATE [dbo].[AspNetUsers] SET [UserName] = 'DELETED' WHERE [UserName] = '<EMAIL>'
-- -------------


-- Unset TwoFactorEnabled on all User
-- UPDATE[dbo].[AspNetUsers]
-- SET [TwoFactorEnabled] = 0