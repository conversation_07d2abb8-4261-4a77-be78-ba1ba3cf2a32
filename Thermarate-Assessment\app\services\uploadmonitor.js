﻿// Name: uploadmonitorservice
// Type: Angular Service
(function () {
    'use strict';
    var serviceId = 'uploadmonitorservice';
    angular.module('appservices').factory(serviceId,
        ['common', uploadmonitorservice]);

    function uploadmonitorservice(common) {
        var log = common.logger;

        var service = {
            monitor: monitor
        };

        return service;

        function monitor(uploadPromise) {
            uploadPromise.progress(function (evt) {
                var percentLoaded = parseInt(100.0 * evt.loaded / evt.total) + "%";
                //toastr.info(percentLoaded, 'Upload Progress');
            }).success(function (data, status, headers, config) {
                toastr.success('success', 'Upload Complete');
            }).error(function (data, status, headers, config) {
                toastr.error("Upload Failed", null, null, true);
            });
        }
    }

})();