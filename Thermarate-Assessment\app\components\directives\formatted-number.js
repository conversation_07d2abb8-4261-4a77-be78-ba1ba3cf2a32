﻿// Used on input fields to format numbers to '123,456,789.00' format to the user
// while retaining the underlying value of the input as an actual number.
// Taken from the below with not insignificant changes to better handle rounding to a given number of decimal places.
// (Really be careful if you need to change anything in here, so many timing, first-click, etc related things to look for)
// See: https://stackoverflow.com/questions/24001895/angularjs-number-input-formatted-view/48511292#48511292

// Example usage:
// <input formatted-number decimals="2"/>

(function () {
    'use strict';

    var app = angular.module('app');

    // adds commas to numbers as they are entered into an input 
    // allows negative numbers and a decimal. Good idea to use this with an input type="tel"
    // in cases where the number does not need to be negative or a decimal
    // so that smartphone browsers bring up number keyboard, but also will allow for commas.
    // otherwise must use input type="text". accepts data-integer and data-positive attributes
    app.directive('formattedNumber', ['common', function (common) {
        return {
            require: 'ngModel',
            link: function (scope, elem, attrs, ngModelCtrl) {

                ngModelCtrl.$formatters.push(function (modelValue) {
                    return modelValue?.length > 0 ? common.getDisplayNumber(modelValue, attrs.decimals, attrs.showZeros, attrs.allowNull) : modelValue;
                });

                // it's best to change the displayed text using elem.val() rather than
                // ngModelCtrl.$setViewValue because the latter will re-trigger the parser
                // and not necessarily in the correct order with the changed value last.
                // see http://radify.io/blog/understanding-ngmodelcontroller-by-example-part-1/
                // for an explanation of how ngModelCtrl works.
                ngModelCtrl.$parsers.push(function (newInput) {

                    let displayValue = common.getDisplayNumber(newInput, attrs.decimals, attrs.showZeros);

                    // Get where cursor is now then set back after set value (if input was char, that char will be removed so move cursor back 1 space)
                    let originalCursorPos = /[0-9|./]/.test(newInput[elem[0].selectionStart-1])
                                          ? elem[0].selectionStart
                                          : elem[0].selectionStart - 1;

                    // Set display vlaue
                    ngModelCtrl.$setViewValue(displayValue); // (there was an issue where if the same symbol (eg. ';') or number entered entered past allowed digits is entered twice and the second time it does not get removed because the parser does not recognise a change, "ngModelCtrl.$setViewValue()" properly sets new value so change is always recognised)
                    elem.val(displayValue);

                    // Set back original cursor position
                    elem[0].selectionStart = originalCursorPos;
                    elem[0].selectionEnd = originalCursorPos;
                    // Account for new comma added
                    if (newInput.split('.')[0].length != displayValue.split('.')[0].length) {
                        elem[0].selectionStart++;
                    }
                    
                    // Set the ngModel value
                    return getNumAsValue(displayValue);

                });

                function getNumAsValue(val, numDecimals) {
                    
                    // Split whole num and decimal
                    let elements = val.split('.');
                    
                    // Remove symbols
                    elements[0] = elements[0].replace(/[^0-9]/g, '');
                    if (elements.length > 1) {
                        elements[1] = elements[1].replace(/[^0-9]/g, '');
                    }
                    
                    // IF has decimal and want to round decimals, round
                    if (elements.length > 1 && numDecimals && elements[1].length > numDecimals) {
                        elements[1] = Number(elements[1]).toFixed(numDecimals);
                    }
                    
                    // Put back together
                    let value = elements.length > 1
                              ? elements[0] + '.' + elements[1]
                              : elements[0]

                    return (attrs.allowNull && value == "") ? null : Number(value);
                }
                
                // Ideally this would be called in an onLoad or onInit or something
                // but I couldn't figure it out atm and this is already taking too much time.
                // Basically sets the initial rounding to match what is desired.
                setTimeout(() => {
                    let val = elem[0].value;
                    let displayValue = common.getDisplayNumber(val, attrs.decimals);
                    elem.val(displayValue);
                }, 200);

                //function determineUnderlyingValue(val) {
                //    var modelNum = val.toString().replace(/,/g, '').replace(/[A-Za-z]/g, '');
                //    modelNum = parseFloat(modelNum);
                //    modelNum = (!isNaN(modelNum)) ? modelNum : 0;
                    
                //    if (modelNum.toString().indexOf('.') !== -1) {
                //        modelNum = Number(modelNum.toFixed(attrs.decimals));
                //    }
                    
                //    if (attrs.positive) {
                //        modelNum = Math.abs(modelNum);
                //    }
                    
                //    return modelNum;
                //}
            }
        };
    }]);
})();