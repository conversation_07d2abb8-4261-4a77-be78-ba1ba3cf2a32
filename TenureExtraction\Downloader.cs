﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PuppeteerSharp;

namespace TenureExtraction
{


    /// <summary>
    /// Handles downloading and unzipping LGATE datasets before extraction.
    /// </summary>
    public class Downloader
    {
        private Logger logger;

        private string datasetDirectory;

        private string oauthToken;

        private const string OUTPUT_ARCHIVE = "DATASET.zip";

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataDirectory">Absolute route to where you want downloaded datasets to be saved to.</param>
        /// <param name="chromeDirectory">Absolute route to where you wish the chromium bundle downloaded by puppeteer to be saved.</param>
        /// <param name="logCallback">Optional callback to handle logging.</param>
        public Downloader(string dataDirectory, LogCallback logCallback = null)
        {
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

            this.datasetDirectory = dataDirectory;

            logger = new Logger(logCallback);
        }

        /// <summary>
        /// Runs the entire dataset download process in order. First attempts to use any existing archives
        /// within the filesystem before downloading a new dataset. Set forceDownload = true if you wish
        /// to ignore any exists archives.
        /// 
        /// NOTE: Does NOT clean up the filesystem afterwards - You caller must call this directly or risk
        /// subsequent calls using the existing archive.
        /// </summary>
        public async Task<bool> RunFullProcess(bool updateAddress = false, bool updateLocalities = false, bool updateLga = false)
        {
            try
            {
                this.oauthToken = await this.GetOAuthToken();

                if (updateAddress)
                {
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/LGATE_Subscription/Property_Street_Address_LGATE_251_WA_{Shared.GDA_TARGET}_Subscription_Geopackage.zip");
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/LGATE_Subscription/Cadastre_Polygon_LGATE_217_WA_{Shared.GDA_TARGET}_Subscription_Geopackage.zip");
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/LGATE_Subscription/Lodged_Cadastre_Polygon_LGATE_222_WA_{Shared.GDA_TARGET}_Subscription_Geopackage.zip");
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/LGATE_Subscription/Land_Tenure_LGATE_226_WA_{Shared.GDA_TARGET}_Subscription_Geopackage.zip");
                }
                
                if(updateLocalities)
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/Boundaries/Localities_LGATE_234_WA_{Shared.GDA_TARGET}_Public_Geopackage.zip");
                
                if(updateLga)
                    await this.DownloadDataset(this.oauthToken,
                        $"https://direct-download.slip.wa.gov.au/datadownload/Boundaries/LGA_Boundaries_LGATE_233_WA_{Shared.GDA_TARGET}_Public_Geopackage.zip");
                
                CleanDirectory(datasetDirectory, new string[] { ".zip" }); // Clean everything that isn't a zip file or the 'donotedelete' file.

                this.Unzip();
                this.DeleteUnnecessaryFiles(); // Also deletes any .zip files. If the extraction runs into a problem it may have been due to shonky zip files.

                return true;
            }
            catch (Exception e)
            {
                // If any exceptions are encountered during this process, clean the directory.
                // This will force the re-downloading of any ZipFiles (Since they might be the source of the problem).
                CleanDirectory(datasetDirectory);
                throw e;
            }
        }

        /// <summary>
        /// Returns true is [OUTPUT_ARCHIVE] exists.
        /// </summary>
        private bool ExistingArchivesExist()
        {
            var files = Directory.GetFiles(datasetDirectory);

            // Due to some differences in how local and test/prod builds wanted to handle filepaths 
            // this is the safest method to check.
            foreach (string file in files)
            {
                if (file.EndsWith(OUTPUT_ARCHIVE))
                    return true;
            }
            return false;
        }

        public async Task<string> GetOAuthToken()
        {
            try
            {
                const string tokenRequestUrl = "https://sso.slip.wa.gov.au/as/token.oauth2?grant_type=password&username=<EMAIL>&password=Letsgo.123";

                var httpClient = new HttpClient();
                var request = new HttpRequestMessage()
                {
                    RequestUri = new Uri(tokenRequestUrl),
                    Method = HttpMethod.Post,
                };

                request.Headers.Add("Authorization", "Basic ZGlyZWN0LWRvd25sb2Fk");

                System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                var response = await httpClient.SendAsync(request);

                if(response.StatusCode == HttpStatusCode.OK)
                {
                    JObject json = JsonConvert.DeserializeObject<JObject>(await response.Content.ReadAsStringAsync());
                    string token = json["access_token"].ToString();

                    httpClient.Dispose();
                    request.Dispose();

                    return token;
                }

                return null;
            }
            catch (Exception e)
            {
                throw new OAuthTokenRetreivalException("Unable to retreive OAuth token. Details: " + e.Message);
            }
        }

        /// <summary>
        /// Downloads the given LGATE Dataset (.zip).
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<bool> DownloadDataset(string oauthToken, string url)
        {
            logger.Log($"Starting to download dataset at {url}");

            using (var client = new HttpClient())
            {
                byte[] bytes = null;

                try
                {
                    // Set an infinite timeout so it doesn't complain after the default 100 seconds.
                    client.Timeout = System.Threading.Timeout.InfiniteTimeSpan;                                     
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {oauthToken}");
                    var response = client.GetAsync(url).Result; // Cannot await or an error is thrown *shrug*

                    bytes = await response.Content.ReadAsByteArrayAsync();
                    if (bytes == null)
                        throw new Exception("Error: bytes == null");

                    if (bytes.Count() < 100000)
                    {
                        var html = await response.Content.ReadAsStringAsync();
                        throw new Exception($"Error while trying to download dataset. Could not retrieve data from {url}. Token was {this.oauthToken}. Returned payload was: {html}.");
                    }

                    // Write all bytes from our response to the dataset zip.
                    string[] split = url.Split('/', '\\');
                    string final = datasetDirectory + "\\" + split.Last();
                    File.WriteAllBytes(final, bytes);
                    return true;
                }
                catch (Exception e)
                {
                    throw new Exception("Error while trying to download dataset: " + e.Message);
                }
            }
        }

        /// <summary>
        /// Unzips every zip archive within the given folder
        /// </summary>
        /// <param name="directory"></param>
        /// <returns>TRUE if all files successfully unzipped, otherwise FALSE.</returns>
        public bool Unzip()
        {
            logger.Log("Starting unzip process");
            string[] files = Directory.GetFiles(datasetDirectory);

            foreach (string curFile in files)
            {
                if (curFile.EndsWith(".zip"))
                {
                    try
                    {
                        // Open our archive, and check against all files to make sure their isn't an existing
                        // file with the same name (causes an exception) before extracting.
                        ZipArchive archive = ZipFile.Open(curFile, ZipArchiveMode.Read);
                        foreach(ZipArchiveEntry zippedFile in archive.Entries)
                        {
                            string completeFileName = Path.Combine(datasetDirectory, zippedFile.FullName);
                            string directory = Path.GetDirectoryName(completeFileName);

                            if (!Directory.Exists(directory))
                                Directory.CreateDirectory(directory);

                            if (zippedFile.Name != "")
                                zippedFile.ExtractToFile(completeFileName, true);
                        }

                        archive.Dispose();
                    }
                    catch (System.Threading.ThreadAbortException tae)
                    {
                        // Recursivley call this function and hope next time it works properly.
                        logger.Log($"Thread abort exception occured while unzipping {curFile}, trying again...");
                        return Unzip();
                    }
                    catch(System.IO.IOException ioe)
                    {
                        ;
                    }
                    catch (Exception e)
                    {
                        throw new Exception("Error while trying to Unzip downloaded data: " + e.Message);
                    }
                }
            }

            //Thread.Sleep(500); // Dodgy way to try and make sure file access has been
            return true;
        }

        /// <summary>
        /// Deletes any file that doesn't end in .dbf/.shp/.shx
        /// </summary>
        public bool DeleteUnnecessaryFiles()
        {
            logger.Log($"Deleting un-necessary files.");
            Thread.Sleep(10000); // Put a sleep on this as the filesystem sometimes had not released resources in time...

            // Deletes any file that doesn't end in .dbf/.shp/.shx
            DirectoryInfo di = new DirectoryInfo(datasetDirectory);

            foreach (FileInfo file in di.GetFiles())
            {
                try
                {
                    if (file.Extension != ".gpkg" && file.Extension != ".dbf" && file.Extension != ".shp" && file.Extension != ".shx" && file.Name != "donotdelete")
                    {
                        file.Delete();
                    }

                }
                catch (Exception e)
                {
                    throw new Exception($"Error while trying to delete unnecesary file {file.Name}: " + e.Message);
                }
            }

            return true;
        }

        /// <summary>
        /// Deletes all files and subdirectories within the given directory (NOT including the given directory)
        /// in preparation for download and unzipping of the new dataset.
        /// </summary>
        /// <param name="directory">The directory to clean.</param>
        /// <returns>TRUE is all files were successfully deleted, otherwise FALSE.</returns>
        public bool FinalCleanup()
        {
            Thread.Sleep(5000); // Put a sleep on this as the filesystem sometimes had not released resources in time...
            logger.Log($"Starting to clean files");
            var r = CleanDirectory(datasetDirectory);
            logger.Log($"Finished cleaning files");
            return r;
        }

        static public bool CleanDirectory(string dir, string[] extensionExceptions = null)
        {
            DirectoryInfo di = new DirectoryInfo(dir);

            foreach (FileInfo file in di.GetFiles())
            {
                try
                {
                    // Skip any files that are off the given extension type
                    if (extensionExceptions != null && extensionExceptions.Contains(file.Extension))
                        continue;
                    else if (file.Name != "donotdelete") // heh
                        file.Delete();
                }
                catch (Exception e)
                {
                    // throw new Exception("Error while trying to clean files: " + e.Message);
                }
            }

            foreach (DirectoryInfo subDirectory in di.GetDirectories())
            {
                try
                {
                    subDirectory.Delete(true);
                }
                catch (Exception e)
                {
                   // throw new Exception("Error while trying to clean subdirectories: " + e.Message);
                }
            }

            return true;
        }

    }

    public class OAuthTokenRetreivalException : Exception
    {
        public new string Message { get; private set; }

        public OAuthTokenRetreivalException(string message)
        {
            this.Message = message;
        }
    }
}
