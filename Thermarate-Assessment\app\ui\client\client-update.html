<form name="clientform" class="main-content-wrapper" novalidate data-ng-controller='ClientUpdateCtrl as vm'>

        <!-- Breadcrumbs -->
        <div
            ng-style="{'visibility': vm.client.clientName == null ? 'hidden' : 'visible'}"
            class="navigation-text"
            style="margin-left:0 !important"
        >
            <div ng-click="vm.navigateAttempt('client-listform', null)" class="clickable">Clients</div>
            <div>></div>
            <!-- IF 'Details' selected -->
            <b ng-if="vm.selectedTab == 0">{{vm.client.clientName}}</b>
            <!-- IF any other tab selected -->
            <div ng-if="vm.selectedTab > 0" class="clickable" ng-click="vm.selectedTab = 0">{{vm.client.clientName}}</div>
            <div ng-if="vm.selectedTab > 0">></div>
            <b   ng-if="vm.selectedTab > 0">{{vm.getCurrentTabName()}}</b>
        </div>

        <!-- Tabs -->
        <div data-cc-widget-content data-is-modal="vm.isModal">
            <md-tabs md-dynamic-height class="hide-tabs-in-modal" md-selected="vm.selectedTab">

                <!-- TAB: Details -->
                <md-tab label="Details">
                    <div layout="row" layout-sm="column" layout-xs="column">

                        <div flex-gt-sm="50">
                            <md-card>
                                <md-card-header>
                                    Client
                                </md-card-header>
                                <md-card-content>
                                    <fieldset ng-disabled="vm.editPermission == false">

                                        <!-- ******** Client Name ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Client Name</label>
                                            <input type="text" name="clientName"
                                                   ng-model="vm.client.clientName" md-autofocus
                                                   md-maxlength="100"
                                                   required />
                                            <div ng-messages="clientform.clientName.$error">
                                                <div ng-message="required">Client Name is required.</div>
                                                <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                            </div>
                                        </md-input-container>

                                    </fieldset>

                                    <div class="col-md-12">
                                        <div rd-display-created-modified ng-model="vm.client"></div>
                                    </div>
                                </md-card-content>
                            </md-card>
                        </div>
                    </div>
                </md-tab>

                <!-- TAB: Reports -->
                <md-tab label="Reports">
                    <div flex-gt-sm="50">
                        <report-settings report-settings="vm.client.reportSettings" disabled="vm.editPermission == false"></report-settings>
                    </div>
                </md-tab>

                <!-- TAB: Users -->
                <md-tab label="Users">
                    <div layout="row" layout-sm="column" layout-xs="column">

                        <div flex-gt-sm="50">
                            <md-card>
                                <md-card-header>
                                    Users
                                </md-card-header>
                                <md-card-content>
                                    <div class="table-responsive-vertical shadow-z-1">
                                        <table class="table table-striped table-hover table-condensed">
                                            <thead>
                                                <tr>
                                                    <th class="text-left">Name</th>
                                                    <th class="text-left">Phone</th>
                                                    <th class="text-left">Email Address</th>
                                                    <th class="text-left">Default Assignee</th>
                                                    <th class="text-left">Client Portal Access</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="item in vm.clientUsers track by $index">
                                                    <td data-title="Full Name" class="text-left">{{item.fullName }}</td>
                                                    <td data-title="Phone" class="text-left">{{item.phone }}</td>
                                                    <td data-title="Email Address" class="text-left"><a href="mailto:{{item.emailAddress }}">{{item.emailAddress }}</a></td>
                                                    <td data-title="Default"
                                                        style="text-align: center;">
                                                        <md-checkbox ng-disabled="vm.editPermission == false"
                                                                     ng-checked="item.checked"
                                                                     ng-click="vm.selectDefault($event, item.userId)">
                                                        </md-checkbox>
                                                    </td>
                                                    <td data-title="ClientPortalAccess"
                                                        style="text-align: center;">
                                                        <md-checkbox ng-checked="item.loginEnabled"
                                                                     disabled>
                                                        </md-checkbox>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </md-card-content>
                            </md-card>
                        </div>
                    </div>
                </md-tab>

                <!-- TAB: Accounts -->
                <md-tab label="Accounts">
                    <div layout="row" layout-sm="column" layout-xs="column">
                        <!-- ******** Right Side ******** -->
                        <div flex-gt-sm="50">
                            <md-card>
                                <md-card-header class="md-headline">
                                    Account Contact Details
                                </md-card-header>
                                <md-card-content>

                                    <fieldset ng-disabled="vm.editPermission == false">

                                        <!-- ******** Accounts First Name ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>First Name</label>
                                            <input type="text" name="accountsFirstName"
                                                   ng-model="vm.client.accountsFirstName"
                                                   md-maxlength="400" />
                                            <div ng-messages="clientform.accountsFirstName.$error">
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Accounts Last Name ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Last Name</label>
                                            <input type="text" name="accountsLastName"
                                                   ng-model="vm.client.accountsLastName"
                                                   md-maxlength="400" />
                                            <div ng-messages="clientform.accountsLastName.$error">
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Accounts Phone ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Phone</label>
                                            <input type="text" name="accountsPhone"
                                                   ng-model="vm.client.accountsPhone"
                                                   md-maxlength="20"
                                                   ui-mask="(99) 9999 9999"
                                                   placeholder="(00) 0000 0000"
                                                   model-view-value="true" />
                                            <div ng-messages="clientform.accountsPhone.$error">
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Accounts Email ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Email</label>
                                            <input type="text" name="accountsEmail"
                                                   ng-model="vm.client.accountsEmail"
                                                   md-maxlength="400" />
                                            <div ng-messages="clientform.accountsEmail.$error">
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Accounts Note ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Note</label>
                                            <textarea name="accountsNote"
                                                      ng-model="vm.client.accountsNote"
                                                      md-maxlength="2000"
                                                      rows="5" />
                                            <div ng-messages="clientform.accountsNote.$error">
                                            </div>
                                        </md-input-container>
                                    </fieldset>

                                </md-card-content>
                            </md-card>

                            <!-- Purchase Order Settings -->
                            <md-card>
                                <md-card-header>
                                    <span class="md-headline">Purchase Order Settings</span>
                                </md-card-header>
                                <md-card-content>

                                    <fieldset ng-disabled="vm.editPermission == false">

                                        <!-- ******** Purchase Order ******** -->
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Purchase Order</label>
                                            <md-select name="purchaseorder"
                                                       ng-disabled="vm.editPermission == false"
                                                       ng-model="vm.client.clientDefault.purchaseOrderCode">
                                                <md-option ng-repeat="item in vm.purchaseOrderList track by item.purchaseOrderCode"
                                                           ng-value="item.purchaseOrderCode">
                                                    {{item.description}}
                                                </md-option>
                                            </md-select>
                                        </md-input-container>

                                    </fieldset>

                                </md-card-content>
                            </md-card>
                        </div>
                    </div>
                </md-tab>

                <!-- TAB: Jobs -->
                <md-tab label="Jobs">
                    <div class="flex-100">
                        <md-card>
                            <md-card-content>
                                <div data-cc-widget-action-bar
                                     data-quick-find-model='vm.listFilter'
                                     data-quick-find-holder="Search"
                                     data-action-buttons='vm.actionButtons'
                                     data-refresh-list='vm.refreshList()'
                                     data-spinner-busy='vm.isBusy'
                                     data-filter-a-options="vm.statusOptions"
                                     data-filter-a-title="Status"
                                     data-filter-c-options="vm.assessorOptions"
                                     data-filter-c-title="Assessor"
                                     data-current-filter="vm.currentFilter"
                                     data-query-builder-name="Client"
                                     data-query-builder-current="vm.currentQuery"
                                     data-default-start="vm.rptDateRange"
                                     data-date-range-label="Created"
                                     data-date-ranges="vm.ranges"
                                     filtercolumns="true"
                                     data-column-options="vm.columnOptions"
                                     data-filter-columns="vm.filterColumns"
                                     data-filter-columns-changed="vm.filterColumnsChanged">
                                </div>
                                <div class="table-responsive-vertical shadow-z-1">
                                    <table class="table table-striped table-hover table-condensed"
                                           st-table="vm.clientJobsList"
                                           st-table-filtered-list="exportList"
                                           st-global-search="vm.listFilter"
                                           st-persist="clientJobsList"
                                           st-pipe="vm.callServer">
                                        <thead>
                                            <tr>
                                                <th class="text-left" ng-if="vm.columnOptions['jobReference']">Ref</th>
                                                <th class="text-left" redi-allow-roles="['admin__client__edit']" ng-if="vm.columnOptions['contact']">Creator</th>
                                                <th class="text-left" ng-if="vm.columnOptions['clientRef']">Client Ref</th>
                                                <th class="text-left" ng-if="vm.columnOptions['owner']">Owner</th>
                                                <th class="text-left" ng-if="vm.columnOptions['orderDate']">Created</th>
                                                <th class="text-right" ng-if="vm.columnOptions['assessmentVersion']">Version</th>
                                                <th class="text-left" ng-if="vm.columnOptions['assessor']">Assessor</th>
                                                <th class="text-left" ng-if="vm.columnOptions['priority']">Priority</th>
                                                <th class="text-left" ng-if="vm.columnOptions['status']">Status</th>
                                                <th class="text-left" ng-if="vm.columnOptions['projectAddress']">Project Address</th>
                                                <th class="text-left" ng-if="vm.columnOptions['projectDescription']">Building Description</th>
                                            </tr>

                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="row in vm.clientJobsList" ui-sref="assessment-updateform({ assessmentId: row.currentAssessmentId, jobId: row.jobId })">
                                                <td data-title="Job Reference" class="text-left" ng-if="vm.columnOptions['jobReference']">{{::row.jobReference }}</td>
                                                <td data-title="Creator" class="text-left" redi-allow-roles="['admin__client__edit']" ng-if="vm.columnOptions['contact']">{{::row.creatorFullName }}</td>

                                                <td data-title="Client Job Ref" class="text-left" ng-if="vm.columnOptions['clientRef']">{{::row.clientJobNumber }}</td>
                                                <td data-title="Project Owner" class="text-left" ng-if="vm.columnOptions['owner']">{{::row.projectOwner }}</td>
                                                <td data-title="Created" class="text-left" ng-if="vm.columnOptions['orderDate']">{{::row.orderDate | date: 'dd/MM/yyyy' }}</td>
                                                <td data-title="Version" class="text-right" ng-if="vm.columnOptions['assessmentVersion']">{{::row.assessmentVersion | number }}</td>

                                                <td data-title="Assessor" class="text-right" ng-if="vm.columnOptions['assessor']">{{::row.assessorFullName }}</td>
                                                <td data-title="Priority"
                                                    class="text-left"
                                                    ng-class="{'label-red label-bold' : row.assessmentPriorityCode=='URGENT', 'label-orange label-bold' : row.assessmentPriorityCode=='HIGH'}"
                                                    style="text-transform: lowercase;"
                                                    ng-if="vm.columnOptions['priority']">
                                                    {{::row.assessmentPriorityCode }}
                                                </td>
                                                <td data-title="Status" class="text-left"
                                                    ng-class="{'label-red' : row.statusCode=='JCancelled', 'label-green' : row.statusCode=='JIssued'||row.statusCode=='JComplete', 'label-orange' : row.statusCode=='JDraft'||row.statusCode=='JInProgress'||row.statusCode=='JCompliance'}"
                                                    ng-if="vm.columnOptions['status']">
                                                    {{::row.jobStatusDescription }}
                                                </td>
                                                <td data-title="Project Address" class="text-left" ng-if="vm.columnOptions['projectAddress']">{{::row.address }}</td>
                                                <td data-title="Building Description" class="text-left" ng-if="vm.columnOptions['projectDescription']">{{::row.projectDescriptionDescription }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class="widget-pager">
                                        <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
                                    </div>
                                </div>
                            </md-card-content>
                        </md-card>
                    </div>
                </md-tab>

                <!-- TAB: Settings (Allowed Selection Values)-->
                <md-tab label="Settings">
                    <div layout="row" layout-sm="column" layout-xs="column">
                        <!-- ******** Right Side ******** -->
                        <div flex-gt-sm="50">
                            <md-card>
                                <md-card-header>
                                    <span class="md-headline">Assessment</span>
                                </md-card-header>
                                <md-card-content>

                                    <client-options-form options="vm.client.clientOptions"
                                                         defaults="vm.client.clientDefault"
                                                         option-data="vm.clientOptionsData"
                                                         disabled="vm.editPermission == false">
                                    </client-options-form>

                                </md-card-content>
                            </md-card>
                            <md-card>
                                <md-card-header>
                                    <span class="md-headline">Client Portal</span>
                                </md-card-header>
                                <md-card-content>

                                    <client-portal-options-form options="vm.client.clientOptions"
                                                         defaults="vm.client.clientDefault"
                                                         option-data="vm.clientOptionsData"
                                                         disabled="vm.editPermission == false">
                                    </client-portal-options-form>

                                </md-card-content>
                            </md-card>
                        </div>
                    </div>
                </md-tab>

                <!-- TAB: Client Defaults -->
                <md-tab label="Client Defaults">

                    <!-- Assessment -->
                    <md-card flex-gt-sm="50">
                        <md-card-header>
                            <span class="md-headline">Assessment</span>
                        </md-card-header>
                        <md-card-content>

                            <fieldset ng-disabled="vm.editPermission == false">

                                <!-- ******** Works Descriptions ******** -->
                                <md-input-container class="md-block"
                                                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                                                    flex="100">
                                    <label>Works Description</label>
                                    <md-select name="WorksDescription"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.worksDescription"
                                               ng-model-options="{trackBy: '$value.worksDescriptionCode'}">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item"
                                                   ng-repeat="item in vm.availableWorksDescriptionList()">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Certification ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Certification</label>
                                    <md-select name="defaultCertification"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.certificationId">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item.certificationId"
                                                   ng-repeat="item in vm.availableCertifications()">
                                            {{item.title}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Baseline Assessment Method ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Baseline Assessment Method</label>
                                    <md-select name="complianceMethodCode"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.preliminaryComplianceMethodCode"
                                               ng-change="vm.validatePreliminaryComplianceMethodChange()">
                                        <md-option ng-value="null">No Default</md-option>
                                        <md-option ng-value="item.complianceMethodCode"
                                                   ng-repeat="item in vm.availableComplianceMethods()">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Required House Energy Rating -->
                                <md-autocomplete md-input-name="minHouseEnergyRating"
                                                 class="vertically-condensed"
                                                 md-input-minlength="1"
                                                 md-min-length="0"
                                                 md-select-on-match="true"
                                                 md-selected-item="vm.tempMinHouseEnergyRating"
                                                 md-selected-item-change="vm.formatActualMinHouseEnergyRating()"
                                                 md-search-text="vm.minHouseEnergyRatingSearchText"
                                                 md-items="value in vm.client.clientOptions.availableHouseEnergyRatings | filter: vm.minHouseEnergyRatingSearchText"
                                                 md-item-text="value"
                                                 md-require-match
                                                 md-no-cache="true"
                                                 md-floating-label="Required House Energy Rating"
                                                 ng-if="vm.client.clientDefault.preliminaryComplianceMethodCode=='CMHouseEnergyRating' ||
                                                    vm.client.clientDefault.preliminaryComplianceMethodCode=='CMPerfSolutionHER' ||
                                                    vm.client.clientDefault.preliminaryComplianceMethodCode=='CMPerfWAProtocolHER'">
                                    <md-item-template>
                                        <span md-highlight-text="vm.minHouseEnergyRatingSearchText">{{value}}</span>
                                    </md-item-template>
                                </md-autocomplete>

                                <!-- ******** Assessment Software ******** -->
                                <md-input-container class="md-block" flex="100" ng-if="vm.assessment.assessmentSoftwareCode!= 'Other'">
                                    <label>Assessment Software</label>
                                    <md-select name="assessmentSoftwareCode"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.assessmentSoftwareCode"
                                               ng-change="vm.assessmentSoftwareChanged()">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item.assessmentSoftwareCode"
                                                   ng-disabled="!item.isAvailable"
                                                   ng-repeat="item in vm.availableAssessmentSoftware()">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Nominated Building Surveyor ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Nominated Building Surveyor</label>
                                    <md-select name="nominatedBuildingSurveyorDefault"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.nominatedBuildingSurveyorId">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item.buildingSurveyorId"
                                                   ng-repeat="item in vm.availableBuildingSurveyors() track by item.buildingSurveyorId">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Priority ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Priority</label>
                                    <md-select name="priority"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.priorityCode">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item.priorityCode"
                                                   ng-repeat="item in vm.priorityList track by item.priorityCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Assigned To Assessor Employee ******** -->
                                <md-autocomplete ng-if="vm.client.clientId"
                                                 md-input-name="assignedToAssessorUserId"
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.client.clientDefault.assessorUser"
                                                 md-search-text="vm.assignedToAssessorUserIdSearchText"
                                                 md-selected-item-change="vm.assessorUserChanged(item)"
                                                 md-items="item in vm.getEmployees(vm.assignedToAssessorUserIdSearchText)"
                                                 md-item-text="item.fullName"
                                                 md-floating-label="Assigned Assessor"
                                                 id="assignedAssessorAutoComplete">
                                    <md-item-template>
                                    <span md-highlight-text="vm.assignedToAssessorUserIdSearchText">
                                        {{item.fullName}}
                                    </span>
                                    </md-item-template>
                                </md-autocomplete>

                                <!-- ******** Include Drawings in Report ******** -->
                                <md-input-container class="md-block md-input-has-value"
                                                    flex="100"
                                                    style="margin-bottom: 36px;">
                                    <label>Include Drawings in Report</label>
                                    <div layout="row" layout-wrap class="spacing-above">
                                        <md-radio-group ng-model="vm.client.clientDefault.includeDrawingsInReport"
                                                        layout="row"
                                                        ng-disabled="vm.disabled === true || !vm.client.clientOptions.includeDrawingsInReport"
                                                        class="checkbox-radio-padding">
                                            <md-radio-button ng-value="true">
                                                Yes
                                            </md-radio-button>
                                            <md-radio-button ng-value="false" ng-click="vm.client.clientDefault.stampDrawings = false">
                                                No
                                            </md-radio-button>
                                        </md-radio-group>
                                    </div>
                                </md-input-container>

                                <!-- ******** Stamp ******** -->
                                <md-input-container class="md-block md-input-has-value"
                                                    flex="100"
                                                    style="margin-bottom: 36px;">
                                    <label>Stamp</label>
                                    <div layout="row" layout-wrap class="spacing-above">
                                        <md-radio-group ng-model="vm.client.clientDefault.stampDrawings"
                                                        layout="row"
                                                        ng-disabled="vm.disabled === true || !vm.client.clientDefault.includeDrawingsInReport || !vm.client.clientOptions.stampDrawings"
                                                        class="checkbox-radio-padding">
                                            <md-radio-button ng-value="true">Yes</md-radio-button>
                                            <md-radio-button ng-value="false">No</md-radio-button>
                                        </md-radio-group>
                                    </div>
                                </md-input-container>

                                <!-- ******** Show QR Code ******** -->
                                <md-input-container class="md-block md-input-has-value" flex="100">
                                    <label>Show QR Code</label>
                                    <md-radio-group ng-model="vm.client.clientDefault.qrCodeEnabled"
                                                    layout="row"
                                                    name="qrCodeEnabled"
                                                    class="checkbox-radio-padding spacing-above"
                                                    ng-disabled="vm.client.clientOptions.isQRCodeAvailable == false">

                                        <md-radio-button ng-value="true"
                                                         ng-disabled="vm.client.clientOptions.isQRCodeAvailable == false">
                                            Yes
                                        </md-radio-button>
                                        <md-radio-button ng-value="false">
                                            No
                                        </md-radio-button>
                                    </md-radio-group>
                                </md-input-container>

                            </fieldset>

                        </md-card-content>
                    </md-card>

                    <!-- Site  -->
                    <md-card flex-gt-sm="50">
                        <md-card-header>
                            <span class="md-headline">Site</span>
                        </md-card-header>
                        <md-card-content>

                            <fieldset ng-disabled="vm.editPermission == false">

                                <!-- ******** Building Exposure ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Building Exposure</label>
                                    <md-select name="buildingExposureCode"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.buildingExposureCode">
                                        <md-option ng-value>No Default</md-option>
                                        <md-option ng-value="item.buildingExposureCode"
                                                   ng-repeat="item in vm.buildingExposureList track by item.buildingExposureCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                            </fieldset>

                        </md-card-content>
                    </md-card>

                    <!-- Assessor notes -->
                    <md-card flex-gt-sm="50">

                        <md-card-header style="display: block; margin-bottom: 8px; padding-bottom: 0px; display: grid; grid-template-columns: auto 1fr; align-content: center;">
                            <div class="md-title">
                                Assessment Notes
                            </div>
                            <md-checkbox style="justify-self: end;"
                                         ng-model="vm.client.clientDefault.assessorNotesNotApplicable"
                                         ng-disabled="vm.editPermission == false">
                                Not Applicable
                            </md-checkbox>
                        </md-card-header>
                        <md-card-body ng-if="!vm.client.clientDefault.assessorNotesNotApplicable"
                                      style="padding: 10px;">

                            <fieldset ng-disabled="vm.editPermission == false">
                                <textarea class="assessment-notes-input"
                                          ng-model="vm.client.clientDefault.assessorNotes">

                                </textarea>
                            </fieldset>
                        </md-card-body>

                    </md-card>

                    <!-- Templates -->
                    <md-card flex-gt-sm="50">
                        <md-card-header>
                            <span class="md-headline">Templates</span>
                        </md-card-header>
                        <md-card-content>

                            <fieldset ng-disabled="vm.editPermission == false">

                                <!-- ******** Proposed Design Templates (was Zones Template) ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Proposed Building Design Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.buildingZonesTemplateId">
                                        <md-option ng-value="item.buildingDesignTemplateId"
                                                   ng-repeat="item in vm.buildingDesignTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Design Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Building Construction Templates ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Proposed Building Construction Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.proposedConstructionTemplateId">
                                        <md-option ng-value="item.buildingConstructionTemplateId"
                                                   ng-repeat="item in vm.buildingConstructionTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Construction Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Building Opening Templates ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Proposed Building Opening Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.proposedOpeningTemplateId">
                                        <md-option ng-value="item.buildingConstructionTemplateId"
                                                   ng-repeat="item in vm.buildingOpeningTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Opening Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Building Services Templates ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Proposed Building Services Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.proposedServicesTemplateId">
                                        <md-option ng-value="item.buildingServicesTemplateId"
                                                   ng-repeat="item in vm.buildingServicesTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Services Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Reference Templates ---------------------------------------------------------->
                                <div style="height: 40px;"></div>

                                <!-- ******** Reference Design Templates (was Zones Template) ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Reference Building Design Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.referenceBuildingZonesTemplateId">
                                        <md-option ng-value="item.buildingDesignTemplateId"
                                                   ng-repeat="item in vm.buildingDesignTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Design Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <md-input-container class="md-block vertically-condensed"
                                                    flex-gt-sm>
                                    <label>Reference Building Construction Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.referenceConstructionTemplateId">
                                        <md-option ng-value="item.buildingConstructionTemplateId"
                                                   ng-repeat="item in vm.buildingConstructionTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Construction Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Reference Openings Template Selector -->
                                <md-input-container class="md-block vertically-condensed"
                                                    flex-gt-sm>
                                    <label>Reference Building Opening Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.referenceOpeningTemplateId">
                                        <md-option ng-value="item.buildingConstructionTemplateId"
                                                   ng-repeat="item in vm.buildingOpeningTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Opening Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Reference Building Services Templates ******** -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                    <label>Reference Building Services Template</label>
                                    <md-select name="template"
                                               ng-disabled="vm.editPermission == false"
                                               ng-model="vm.client.clientDefault.referenceServicesTemplateId">
                                        <md-option ng-value="item.buildingServicesTemplateId"
                                                   ng-repeat="item in vm.buildingServicesTemplates">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                            Blank Services Template
                                        </md-option>
                                        <md-option ng-value="null">
                                            No Default
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                            </fieldset>

                        </md-card-content>
                    </md-card>

                    <!-- Whole-of-Home Calculator -->
                    <md-card flex-gt-sm="50">
                        <md-card-header>
                            <span class="md-headline">Whole-of-Home Calculator</span>
                        </md-card-header>
                        <md-card-content>

                            <fieldset ng-disabled="vm.editPermission == false">

                                <div>

                                    <!--  Project Description  -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Project Description</label>
                                        <input type="text" name="projectDescription"
                                               ng-model="vm.client.clientDefault.projectDescription"
                                               ng-maxlength="100"
                                        />
                                    </md-input-container>

                                    <!-- NCC Building Classification -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>NCC Building Classification</label>
                                        <md-select ng-model="vm.client.clientDefault.nccBuildingClassification">
                                            <md-option ng-repeat="v in vm.constants.nccBuildingClassifications"
                                                       ng-value="v"
                                                       ng-click="vm.recalculateEnergyUsageData()">
                                                {{v}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Heating System -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Heating System</label>
                                        <md-select md-container-class="md-select-show-all"
                                                   ng-model="vm.client.clientDefault.spaceHeatingServiceTypeCode">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']"
                                                       ng-value="v.serviceTypeCode">
                                                {{v.title}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Energy Rating (GEMS 2019) -->
                                    <md-input-container ng-if="vm.showEnergyRating({ serviceType: { serviceTypeCode: vm.client.clientDefault.spaceHeatingServiceTypeCode }})"
                                                        class="md-block vertically-condensed">
                                        <label>
                                            {{vm.client.clientDefault.spaceHeatingServiceTypeCode == 'HeatPumpDucted' || vm.client.clientDefault.spaceHeatingServiceTypeCode == 'HeatPumpNonDucted'
                                                ? 'Heating System Energy Rating (GEMS 2019)'
                                                : 'Heating System Energy Rating'}}
                                        </label>
                                        <input ng-model="vm.client.clientDefault.spaceHeatingGems2019Rating"
                                               formatted-number
                                               decimals="1"/>
                                    </md-input-container>

                                </div>

                                <div>

                                    <!-- Cooling System -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Cooling System</label>
                                        <md-select ng-model="vm.client.clientDefault.spaceCoolingServiceTypeCode"
                                                   md-container-class="md-select-show-all">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']"
                                                       ng-value="v.serviceTypeCode">
                                                {{v.title}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Energy Rating (GEMS 2019) -->
                                    <md-input-container ng-if="vm.showEnergyRating({ serviceType: { serviceTypeCode: vm.client.clientDefault.spaceCoolingServiceTypeCode }})"
                                                        class="md-block vertically-condensed">
                                        <label>
                                            {{vm.client.clientDefault.spaceCoolingServiceTypeCode == 'HeatPumpDucted' || vm.client.clientDefault.spaceCoolingServiceTypeCode == 'HeatPumpNonDucted'
                                                ? 'Cooling System Energy Rating (GEMS 2019)'
                                                : 'Cooling System Energy Rating'}}
                                        </label>
                                        <input ng-model="vm.client.clientDefault.spaceCoolingGems2019Rating"
                                               formatted-number
                                               decimals="1"/>
                                    </md-input-container>

                                </div>

                                <div>

                                    <!-- Water Heater Type -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Water Heater Type</label>
                                        <md-select ng-model="vm.client.clientDefault.waterHeatingServiceTypeCode"
                                                   md-container-class="md-select-show-all">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']"
                                                       ng-value="v.serviceTypeCode">
                                                {{v.title}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                </div>

                                <div>

                                    <!-- Swimming Pool -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Swimming Pool</label>
                                        <md-select ng-model="vm.client.clientDefault.swimmingPoolExists">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-value="true">
                                                Yes
                                            </md-option>
                                            <md-option ng-value="false">
                                                No
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Swimming Pool Volume (L) -->
                                    <md-input-container ng-if="vm.client.clientDefault.swimmingPoolExists === true"
                                                        class="md-block vertically-condensed">
                                        <label>Swimming Pool Volume (L)</label>
                                        <input ng-model="vm.client.clientDefault.swimmingPoolVolume"
                                               formatted-number
                                               decimals="0"/>
                                    </md-input-container>

                                    <!-- Pool Pump Energy Rating -->
                                    <md-input-container ng-if="vm.client.clientDefault.swimmingPoolExists === true"
                                                        class="md-block vertically-condensed">
                                        <label>Pool Pump Energy Rating</label>
                                        <input ng-model="vm.client.clientDefault.swimmingPoolGems2019Rating"
                                               formatted-number
                                               decimals="1"/>
                                    </md-input-container>

                                </div>

                                <div>

                                    <!-- Spa -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Spa</label>
                                        <md-select ng-model="vm.client.clientDefault.spaExists">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-value="true">
                                                Yes
                                            </md-option>
                                            <md-option ng-value="false">
                                                No
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Spa Volume (L) -->
                                    <md-input-container ng-if="vm.client.clientDefault.spaExists === true"
                                                        class="md-block vertically-condensed">
                                        <label>Spa Volume (L)</label>
                                        <input ng-model="vm.client.clientDefault.spaVolume"
                                               formatted-number
                                               decimals="0"/>
                                    </md-input-container>

                                    <!-- Spa Pump Energy Rating -->
                                    <md-input-container ng-if="vm.client.clientDefault.spaExists === true"
                                                        class="md-block vertically-condensed">
                                        <label>Spa Pump Energy Rating</label>
                                        <input ng-model="vm.client.clientDefault.spaGems2019Rating"
                                               formatted-number
                                               decimals="1"/>
                                    </md-input-container>

                                </div>

                                <div>

                                    <!-- Spa -->
                                    <md-input-container class="md-block vertically-condensed">
                                        <label>Photovoltaic (PV) System</label>
                                        <md-select ng-model="vm.client.clientDefault.photovoltaicExists">
                                            <md-option ng-value="null">No Default</md-option>
                                            <md-option ng-value="true">
                                                Yes
                                            </md-option>
                                            <md-option ng-value="false">
                                                No
                                            </md-option>
                                        </md-select>
                                    </md-input-container>

                                    <!-- Photovoltaic (PV) System Capacity (kW) -->
                                    <md-input-container ng-if="vm.client.clientDefault.photovoltaicExists === true"
                                                        class="md-block vertically-condensed">
                                        <label>Photovoltaic (PV) System Capacity (kW)</label>
                                        <input ng-model="vm.client.clientDefault.photovoltaicCapacity"
                                               formatted-number
                                               decimals="2"/>
                                    </md-input-container>

                                </div>

                            </fieldset>

                        </md-card-content>
                    </md-card>

                </md-tab>

                <!-- TAB: Energy Lab -->
                <md-tab label="EnergyLab">
                    <!-- Projects List -->
                    <md-card flex="100">
                        <md-card-header>
                            <span class="md-headline">Projects</span>
                        </md-card-header>
                        <md-card-content>
                            <div class="widget">
                                <div class="table-responsive-vertical shadow-z-1">
                                    <table class="table table-striped table-hover table-very-condensed table-data-centered"
                                           st-table="vm.projectList"
                                           st-table-filtered-list="exportList"
                                           st-global-search="vm.projectListFilter"
                                           st-pipe="vm.projectCallServer"
                                           st-sticky-header>
                                        <!-- Headings -->
                                        <thead>
                                            <tr>
                                              <th style="width: 40px;">
                                                <div style="display: grid; justify-content: center;">
                                                <md-checkbox id="sm-allCheckbox"
                                                             style="margin: auto; text-align: center; width: 0;"
                                                             ng-model="vm.projectBulkStatus.selectAllCheckboxState"
                                                             md-indeterminate="vm.projectBulkStatus.isIndeterminate"
                                                             ng-click="vm.projectSelectAllCheckboxes(vm.projectList, vm.projectBulkStatus.selectAllCheckboxState, vm.projectBulkStatus)">
                                                </md-checkbox>
                                                </div>
                                              </th>
                                              <th class="clickable" style="user-select:none" st-sort="projectName">Name</th>
                                              <th class="clickable" style="user-select:none" st-sort="projectTypeDescription">Type</th>
                                              <th class="clickable" style="user-select:none" st-sort="suburbName">Suburb</th>
                                              <th class="clickable" style="user-select:none" st-sort="lga">LGA</th>
                                              <th class="clickable" style="user-select:none" st-sort="stateCode">State</th>
                                              <th class="clickable" style="user-select:none" st-sort="natHERSClimateZoneDescription">NatHERS Climate Zone</th>
                                              <th class="clickable" style="user-select:none" st-sort="nccClimateZoneDescription">NCC Climate Zone</th>
                                              <th class="clickable" style="user-select:none" st-sort=" ">Home Designs</th>
                                              <th class="clickable" style="user-select:none" st-sort="lots">Lots</th>
                                              <th class="clickable" style="user-select:none" st-sort="lotArea">Lot Area (m<sup>2</sup>)</th>
                                              <th class="clickable" style="user-select:none" st-sort="createdOn">Date Created</th>
                                              <th class="clickable" style="user-select:none" st-sort="modifiedOn">Date Modified</th>
                                              <th>Active</th>
                                              <th style="width: 50px;"></th>
                                            </tr>
                                        </thead>
                                        <!-- Data -->
                                        <tbody>
                                            <tr ng-repeat="row in vm.projectList track by $index" class="list-row">
                                                <!-- Checkbox -->
                                                <td>
                                                    <div style="display: grid; justify-content: center;">
                                                        <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                                                    ng-model="row.checkboxSelected"
                                                                    ng-change="vm.projectUpdateBulkSelectStatus(vm.projectList, vm.projectBulkStatus);">
                                                        </md-checkbox>
                                                    </div>
                                                </td>
                                                <!-- Name -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">
                                                    <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                                        {{row.projectName}}
                                                        <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                                                    </div>
                                                </td>
                                                <!-- Type  -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.projectTypeDescription}}</td>
                                                <!-- Suburb -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.suburbName != null ? row.suburbName : '-'}}</td>
                                                <!-- LGA -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.lga != null ? row.lga : '-'}}</td>
                                                <!-- State -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.stateCode != null ? row.stateCode : '-'}}</td>
                                                <!-- NatHERS Climate Zone -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.natHERSClimateZoneDescription != null ? row.natHERSClimateZoneDescription : '-'}}</td>
                                                <!-- NCC Climate Zone -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.nccClimateZoneDescription != null ? row.nccClimateZoneDescription : '-'}}</td>
                                                <!-- Home Designs -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.designsCount}}</td>
                                                <!-- Lots -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.lots != null && row.lots != 0 ? row.lots : '-'}}</td>
                                                <!-- Lot Area -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.lotArea != null && row.lotArea != 0 ? row.lotArea : '-'}}</td>
                                                <!-- Date Created -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.createdOn | date: 'dd/MM/yyyy'}}</td>
                                                <!-- Date Modified -->
                                                <td ng-click="vm.variationRowClick(row.projectId)" class="clickable">{{row.modifiedOn != null ? (row.modifiedOn | date: 'dd/MM/yyyy') : '-'}}</td>
                                                <!-- Active -->
                                                <td>
                                                    <div style="display: grid; justify-items: center;">
                                                        <md-switch ng-model="row.isActive" ng-click="vm.projectHandleIsActiveClick($event, row)" ng-click-stop="$event.stopPropagation()">
                                                        </md-switch>
                                                    </div>
                                                </td>
                                                <!-- Menu -->
                                                <td>
                                                    <div style="display: flex; justify-content: center; align-items: center;">
                                                        <md-menu>
                                                        <img md-menu-origin
                                                                class="clickable"
                                                                ng-click="$mdOpenMenu()"
                                                                src="/content/feather/more-horizontal.svg"/>
                                                        <md-menu-content>
                                                            <!-- Duplicate -->
                                                            <md-menu-item><md-button ng-click="vm.projectClone(row)">
                                                                Duplicate
                                                            </md-button></md-menu-item>
                                                            <!-- Move Up -->
                                                            <md-menu-item ng-show="vm.projectSortField == null && $index > 0"><md-button ng-click="vm.projectMoveUp(row)">
                                                                Move Up
                                                            </md-button></md-menu-item>
                                                            <!-- Move Down -->
                                                            <md-menu-item ng-show="vm.projectSortField == null && $index < vm.projectList.length-1"><md-button ng-click="vm.projectMoveDown(row)">
                                                                Move Down
                                                            </md-button></md-menu-item>
                                                            <!-- Divider -->
                                                            <md-menu-divider></md-menu-divider>
                                                            <!-- Delete -->
                                                            <md-menu-item><md-button ng-click="vm.projectDelete(row)">
                                                                <span style="color: orangered">Delete</span>
                                                            </md-button></md-menu-item>
                                                        </md-menu-content>
                                                        </md-menu>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <!-- Buttons -->
                                        <tfoot>
                                            <tr>
                                              <td colspan="9999" class="text-center">
                                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; background-color: rgb(250,250,250);">
                                                  <!-- Just empty. -->
                                                  <div></div>
                                                  <!-- Pagination Display -->
                                                  <div st-pagination="" st-items-by-page="1000" st-displayed-pages="10"></div>
                                                </div>
                                              </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                    <div class="widget-pager" style="text-align: center;">
                                        <span>Showing {{vm.projectShowingFromCnt}} - {{vm.projectShowingToCnt}} of {{vm.projectTotalRecords}}</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <!-- 'Bulk Edit' button w/ Popup -->
                                        <md-menu>
                                          <!-- Initial bulk edit button, which launches options -->
                                          <md-button class="md-raised md-primary"
                                                     ng-click="vm.projectLaunchBulkEditModal()"
                                                     ng-disabled="!(vm.projectBulkStatus.selectAllCheckboxState || vm.projectBulkStatus.isIndeterminate)">
                                            BULK EDIT
                                          </md-button>
                                          <md-menu-content>
                                            <!-- Duplicate Element Button -->
                                            <md-menu-item>
                                              <md-button ng-click="vm.projectBulkCopyProjects()">
                                                Duplicate
                                              </md-button>
                                            </md-menu-item>
                                            <md-menu-divider></md-menu-divider>
                                            <!-- Delete Element Button -->
                                            <md-menu-item>
                                              <md-button ng-click="vm.projectBulkDeleteProjects()">
                                                <span style="color: orangered;">Delete</span>
                                              </md-button>
                                            </md-menu-item>
                                          </md-menu-content>
                                        </md-menu>
                                        <md-button class="md-raised md-primary"
                                                   redi-enable-roles="settings__settings__create"
                                                   style="margin: 2rem 1.5rem;"
                                                   ng-click="vm.createProject()">
                                          Add Project
                                        </md-button>
                                    </div>
                                </div>
                                <div class="widget-foot">
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </md-card-content>
                    </md-card>

                    <style>
                        .list-row:hover .go-to-variation-button {
                            visibility: visible;
                        }

                        .go-to-variation-button {
                            visibility: hidden;
                            position: absolute;
                            top: 50%; transform: translateY(-50%);
                            right: 7%;
                            width: 25px;
                            height: 25px;
                            min-width: 25px;
                            min-height: 25px;
                            border-radius: 4px;
                            cursor: pointer;
                        }

                        .go-to-variation-button:hover {
                            background-color: #d1d1d1;
                        }

                        .go-to-variation-button > img {
                            position: absolute;
                            top: 50%;
                            left: 54%;
                            transform: translate(-50%, -50%);
                            width: 60%;
                            height: auto;
                        }
                    </style>
                    <!-- Cost Items -->
                    <md-card flex="100">

                        <md-card-header>
                            <!-- Title -->
                            <span class="md-title">Cost Items</span>
                        </md-card-header>

                        <md-card-content>
                            <table class="table table-striped table-hover table-condensed">
                                <colgroup>
                                    <col span="1" style="width: 50px">   <!-- Bulk Edit Checkbox -->
                                    <col span="1" style="width: auto; min-width:200px;">  <!-- Item Code Description -->
                                    <col span="1" style="width: 200px;"> <!-- Category -->
                                    <col span="1" style="width: 100px;"> <!-- UOM -->
                                    <col span="1" style="width: 70px;">  <!-- Rate ($) -->
                                    <col span="1" style="width: 70px;">  <!-- Margin (%) -->
                                    <col span="1" style="width: 70px;">  <!-- Rounding -->
                                    <col span="1" style="width: auto; min-width:250px;">  <!--  Notes  -->
                                    <col span="1" style="width: 50px;">  <!-- Client Portal -->
                                    <col span="1" style="width: 50px;">  <!-- Menu -->
                                </colgroup>
                                <thead>
                                    <tr>
                                        <th style="width: 40px;">
                                            <div style="display: grid; justify-content: center;">
                                                <md-checkbox id="sm-cost-items-bulk-checkbox"
                                                             style="margin: auto; text-align: center; width: 0;"
                                                             ng-model="vm.costItemsBulkStatus.selectAllCheckboxState"
                                                             md-indeterminate="vm.costItemsBulkStatus.isIndeterminate"
                                                             ng-click="vm.selectAllCheckboxes(vm.client.clientCostItems, vm.costItemsBulkStatus.selectAllCheckboxState, vm.costItemsBulkStatus)">
                                                </md-checkbox>
                                            </div>
                                        </th>
                                        <th>Item Code Description</th>
                                        <th>Category</th>
                                        <th class="el-smuc-center-col">UOM</th>
                                        <th class="el-smuc-center-col">Rate ($)</th>
                                        <th class="el-smuc-center-col">Margin (%)</th>
                                        <th class="el-smuc-center-col">Rounding</th>
                                        <th>Notes</th>
                                        <th class="el-smuc-center-col">Client Portal</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="item in vm.client.clientCostItems track by $index">

                                        <!-- Bulk Select -->
                                        <td>
                                            <div style="display: grid; justify-content: center;">
                                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                                             ng-model="item.checkboxSelected"
                                                             ng-change="vm.updateBulkSelectStatus(vm.client.clientCostItems, vm.costItemsBulkStatus);">
                                                </md-checkbox>
                                            </div>
                                        </td>
                                        <!-- Item Code Description -->
                                        <td>
                                            <input class="lightweight"
                                                   style="width:100%; display:inline-grid;"
                                                   ng-model="item.description"
                                                   ng-required="true" />
                                        </td>
                                        <!-- Category -->
                                        <td class="el-smuc-center-col">
                                            <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                                       ng-model="item.category"
                                                       ng-required="true">
                                                <md-option ng-repeat="category in vm.allCostItemCategories track by category"
                                                           ng-value="category">
                                                    {{vm.keyToName(category)}}
                                                </md-option>
                                            </md-select>
                                        </td>
                                        <!-- UOM -->
                                        <td class="el-smuc-center-col">
                                            <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                                       ng-model="item.unitOfMeasure"
                                                       ng-required="true">
                                                <md-option ng-value="'mm'">mm</md-option>
                                                <md-option ng-value="'m'">m</md-option>
                                                <md-option ng-value="'m2'">m<sup>2</sup></md-option>
                                                <md-option ng-value="'m3'">m<sup>3</sup></md-option>
                                                <md-option ng-value="'Ea'">Ea</md-option>
                                                <md-option ng-value="'kg'">kg</md-option>
                                            </md-select>
                                        </td>
                                        <!-- Rate ($) -->
                                        <td class="el-smuc-center-col">
                                            <input ng-model="item.ratePerUnit"
                                                   type="number"
                                                   class="lightweight"
                                                   style="display: inline-grid;"
                                                   ng-required="true" />
                                        </td>
                                        <!-- Margin (%) -->
                                        <td class="el-smuc-center-col">
                                            <input class="lightweight"
                                                   style="display: inline-grid;"
                                                   ng-model="item.margin"
                                                   ng-required="true" />
                                        </td>
                                        <!-- Rounding -->
                                        <td class="el-smuc-center-col">
                                            <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                                       ng-model="item.rounding"
                                                       ng-required="true" >
                                                <md-option ng-value="null">None</md-option>
                                                <md-option ng-value="1">1</md-option>
                                                <md-option ng-value="10">10</md-option>
                                                <md-option ng-value="100">100</md-option>
                                            </md-select>
                                        </td>
                                        <!--  Notes  -->
                                        <td>
                                            <input class="lightweight"
                                                   style="width:100%;"
                                                   type="text"
                                                   ng-model="item.notes" />
                                        </td>
                                        <!-- Client Portal -->
                                        <td>
                                            <div style="display: grid; justify-content: center;">
                                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                                             ng-model="item.clientPortalEnabled">
                                                </md-checkbox>
                                            </div>
                                        </td>
                                        <!-- Menu -->
                                        <td>
                                            <div style="display: flex; justify-content: center; align-items: center;">
                                                <md-menu ng-show="!vm.disabled">
                                                <img md-menu-origin
                                                        class="clickable"
                                                        ng-click="$mdOpenMenu()"
                                                        src="/content/feather/more-horizontal.svg"/>
                                                <md-menu-content>
                                                    <!-- Duplicate -->
                                                    <md-menu-item><md-button ng-click="vm.cloneCostItem(item)">
                                                        Duplicate
                                                    </md-button></md-menu-item>
                                                    <!-- Divider -->
                                                    <md-menu-divider></md-menu-divider>
                                                    <!-- Delete -->
                                                    <md-menu-item><md-button ng-click="vm.deleteCostItem(item)">
                                                        <span style="color: orangered">Delete</span>
                                                    </md-button></md-menu-item>
                                                </md-menu-content>
                                                </md-menu>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div style="display: flex; justify-content: start; align-items: center;">
                                <md-button class="md-raised md-primary"
                                           ng-click="vm.launchCostItemsBulkEdit()"
                                           ng-disabled="vm.disabledEx() || !(vm.costItemsBulkStatus.selectAllCheckboxState || vm.costItemsBulkStatus.isIndeterminate)">
                                    BULK EDIT
                                </md-button>
                                <div style="flex-grow:1"/>
                                <md-button class="md-raised md-primary"
                                            redi-enable-roles="settings__settings__create"
                                            style="float:right; margin: 2rem 1.5rem;"
                                            ng-click="vm.createCostItem()">
                                    Add Cost Item
                                </md-button>
                            </div>
                        </md-card-content>

                    </md-card>
                </md-tab>

            </md-tabs>
            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="clientform.$invalid || vm.client.clientName=='' || vm.editPermission == false || !vm.hasChanges()"
                           ng-show="vm.client.deleted!=true"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised md-warn"
                           ng-disabled="vm.deletePermission == false"
                           ng-show="vm.client.clientId!=null && vm.client.deleted!=true"
                           ng-confirm-click="vm.delete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           ng-disabled="vm.deletePermission == false"
                           ng-show="vm.client.deleted==true"
                           ng-confirm-click="vm.undoDelete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
</form>

<style>
    .variable-option {
        transition: 300ms;
        border-top: 2px solid transparent;
        border-bottom: 2px solid transparent;
    }

    .variable-option:hover {
        background-color: #f1f1f1;
    }

    .variable-option.lro-drop-target-before {
        border-top: 2px solid grey;
    }

    .variable-option.lro-drop-target-after {
        border-bottom: 2px solid grey;
    }
</style>
