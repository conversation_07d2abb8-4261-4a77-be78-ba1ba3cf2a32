<nav data-ng-controller="NavbarCtrl as vm" class="navbar-fixed">
    <div layout="row" layout-align="none" flex="100" layout-fill class="bg-dark-blue nav-dark" >
        <div layout="row">
            <md-button ng-click="vm.isCollapsed = !vm.isCollapsed"
                       ng-show="false"
                       ng-animate="'slide'" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </md-button>
            <div class="nav-add-button hamburger-mode" ng-if="false" ng-hide="!vm.isAuthenticated()">
                <md-button ng-click="vm.createJob()">New Job</md-button>
            </div>
        </div>

        <div flex layout="row" layout-align="none" class="nav-menu-section">
            <div class="logo">
                <img src="content/images/logo.png" style="width:200px;"/>
            </div>
            <!--Loop through all top level navs. Add the dropdown class if the top level nav has children-->
            <div data-ng-repeat="r in vm.navRoutes"
                 data-ng-class="vm.isCurrent(r)"
                 ng-animate="'slide'"
                 redi-allow-roles="{{r.stateConfig.data.roles}}">

                <!--If no children add this link-->
                <md-button ng-if="r.stateConfig.data.noChildren == true"
                           data-ng-bind-html="r.stateConfig.data.content"
                           class="nav-button md-primary"
                           ng-click="vm.jumpTo(r.name)">
                </md-button>

                <!-- Else if has children use this link and add another nested list-->
                <md-menu ng-if="r.stateConfig.data.noChildren != true" redi-allow-roles="{{r.stateConfig.data.roles}}">
                    <md-button md-menu-origin
                               data-ng-bind-html="r.stateConfig.data.content"
                               class="nav-button md-primary"
                               ng-click="$mdOpenMenu()">
                    </md-button>
                    <md-menu-content width="3" class="nav-menu-content">
                        <!--Loop through all second level navs. Add teh dropdown class if second level nav has children-->
                        <md-menu-item data-ng-repeat="s in r.subMenu" redi-allow-roles="{{s.stateConfig.data.roles}}">
                            <!--If no children add this link-->
                            <md-button ng-if="s.stateConfig.data.noChildren == true"
                                       data-ng-bind-html="s.stateConfig.data.content"
                                       ng-click="vm.jumpTo(s.name)">
                            </md-button>

                            <md-menu ng-if="s.stateConfig.data.noChildren != true" redi-allow-roles="{{s.stateConfig.data.roles}}">
                                <md-button md-menu-origin
                                           data-ng-bind-html="s.stateConfig.data.content"
                                           ng-click="$mdOpenMenu()">
                                </md-button>
                                <md-menu-content width="3" class="nav-menu-content">
                                    <!--Loop through all second level navs. Add teh dropdown class if second level nav has children-->
                                    <md-menu-item data-ng-repeat="t in s.subMenu1" redi-allow-roles="{{t.stateConfig.data.roles}}">
                                        <!--If no children add this link-->
                                        <md-button data-ng-bind-html="t.stateConfig.data.content"
                                                   ng-click="vm.jumpTo(t.name)">
                                        </md-button>

                                    </md-menu-item>
                                </md-menu-content>
                            </md-menu>

                        </md-menu-item>
                    </md-menu-content>
                </md-menu>
            </div>
            <div redi-allow-roles="['naviation_menu__newjob']" class="" ng-hide="!vm.isAuthenticated()">
                <md-button class="nav-button md-primary new-job-nav" ng-click="vm.createJob()">
                    <div class="nav-image-div"><i class="fa fa-plus-circle fa-2x"></i></div><div class="nav-text">
                        <span>New Job</span>
                    </div>
                </md-button>
            </div>
            <span flex></span>
            <!--Right side navbar-->
            <div layout="row">
                <div ng-hide="!vm.isAuthenticated()">

                    <!-- Tools Button w/ Popup -->
                    <md-menu redi-allow-roles="['naviation_menu__tools']"
                                ng-show="vm.immediateCheckRoles(['naviation_menu__glazingcalculator']) || vm.immediateCheckRoles(['naviation_menu__wholeofhome'])">
                        <md-button md-menu-origin class="nav-button md-primary" ng-click="$mdOpenMenu()">
                                <div class="nav-image-div"><i class="fa fa-wrench fa-2x" ></i></div>
                                <div>Tools</div>
                        </md-button>
                        <md-menu-content>
                            <md-menu-item redi-allow-roles="['naviation_menu__glazingcalculator']">
                                <!-- Glazing calculator export button -->
                                <md-button ng-click="vm.showGlazingExportModal()">
                                    Glazing Calculator
                                </md-button>
                            </md-menu-item>

                            <md-menu-item redi-allow-roles="['naviation_menu__wholeofhome']">
                                <!-- Whole-of-home calculation button -->
                                <md-button ng-click="vm.showWoHModal()">
                                    Whole-of-Home Calculator
                                </md-button>
                            </md-menu-item>
                        </md-menu-content>
                    </md-menu>

                    <md-menu>
                        <md-button md-menu-origin class="nav-button md-primary user-button" ng-click="$mdOpenMenu()">
                            <md-icon aria-label="Close dialog"
                                     style="font-size:32px; width: 32px; margin-top: 10px;">
                                account_circle
                            </md-icon>
                            <div user-name></div>
                        </md-button>
                        <md-menu-content width="2">
                            <md-menu-item>
                                <md-button ng-click="vm.account()">My Account</md-button>
                            </md-menu-item>
                            <md-menu-item>
                                <md-button ng-click="vm.logout()">Log Out</md-button>
                            </md-menu-item>
                        </md-menu-content>
                    </md-menu>
                </div>
                <div ng-hide="vm.isAuthenticated()">
                    <redi-login-toolbar></redi-login-toolbar>
                </div>
            </div>

        </div>
    </div>

    <!-- You might be wondering, why is there a remote map container in the navbar? -->
    <!--
        Well any modal which needs to capture a map image (e.g. the New Job / Copy / Recert windows) also needs a (usually hidden) dom element to
        act as the map container. We can have a hidden map element on the actual modal itself (and do have, as a fallback), but this
        causes jarring scrollbars to appear when the element display is set to 'block' (even though it is still 'hidden').

        Since the nav-bar is present on all pages, this is a safe place to put it. From my testing, the div never actually appears
        even when the image is being taken (no scroll bars either) so it should be ok.
    -->
    <div id="remote_map" style="width: 100%; height: 1200px; display: none; visibility: hidden; position: absolute; left: 0; top: 0;"></div>
    <div id="remote_site_map" style="width: 100%; height: 1200px; display: none; visibility: hidden; overflow: hidden; position: absolute; left: 0; top: 0; margin: 0; padding: 0;"></div>

    <style>

        .logo {
            width: 200px;
            margin: 7px 20px 0 20px;
        }

        @media screen and (max-width: 1150px) {
            .logo { display: none; }
        }

        @media screen and (max-width: 910px) {
            .user-button {
                min-width: 0px;
                width: 35px;
            }

            .user-button > md-icon {
                margin-bottom: -15px;
            }

            .user-button > [user-name] {
                visibility: hidden !important;
            }
        }

    </style>

</nav>