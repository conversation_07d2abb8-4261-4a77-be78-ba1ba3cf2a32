﻿(function () {
    'use strict';

    var app = angular.module('app');
    //Directive for enter only number in a textbox
    app.directive('onlyNumeric', function () {
        return {
            require: 'ngModel',
            scope: {
                ngModel: '<'
            },
            link: function (scope, element, attrs, modelCtrl) {
                modelCtrl.$valid = false;
                modelCtrl.$setViewValue(null);
                modelCtrl.$parsers.push(function (inputValue) {
                    // this next if is necessary for when using ng-required on your input. 
                    // In such cases, when a letter is typed first, this parser will be called
                    // again, and the 2nd time, the value will be undefined
                    if (inputValue == undefined) return ''
                    var transformedInput = inputValue.replace(/[^0-9]/g, '');
                    if (transformedInput != inputValue) {
                        modelCtrl.$setViewValue(transformedInput);
                        modelCtrl.$render();
                    }

                    return transformedInput.replace(',', '');
                });
                attrs.$observe('ngModel', function(value) {
                    angular.forEach(modelCtrl.$parsers, function (parser) {
                        parser(value);
                    });
                });

            }
        };
    });
})();