<md-dialog ng-controller="ToggleSettingModalCtrl as vm" class="smallModal">

    <form>

        <div data-cc-widget-header
             data-title="{{vm.settingName}}"
             data-is-modal="true"
             data-cancel="vm.cancel()">
        </div>

        <md-dialog-content layout="column" layout-padding layout-wrap>
            <span style="font-weight: bold; margin-bottom: 20px;">How would you like to apply this change?</span>
            
            <md-radio-group ng-model="vm.applyToAllLevels">
                <md-radio-button ng-value="false" class="md-primary">
                    Apply to this level only
                </md-radio-button>
                <md-radio-button ng-value="true" class="md-primary">
                    Apply to all levels
                </md-radio-button>
            </md-radio-group>
        </md-dialog-content>

        <md-dialog-actions layout="row" style="margin:5px">
            <md-button class="md-raised md-primary"
                       style="margin-left: auto;margin-right:5px;"
                       ng-click="vm.confirm()">
                Apply
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
        </md-dialog-actions>

    </form>

</md-dialog>
