﻿// Service used to obtain data from SLIP/Tenure data set.
(function () {
    'use strict';
    var serviceId = 'slipaddress';
    angular.module('appservices')
        .factory(serviceId, ['common', 'config', '$http', slipaddress]);

    function slipaddress(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;

        const baseUrl = config.servicesUrlPrefix + 'SlipAddress/';

        var service = {
            queryPredictive: queryPredictive,
            queryLot: queryLot,
            queryByStreetNumber: queryByStreetNumber,
            getById: getById,
            getRemainder: getRemainder,
            getCurrentProcessingState,
            getLastProcessDate,
            processNewDataset,

        };

        // Returns a minimal dto with the formatted address only as well as an objectid which can be
        // used to return the FULL dto upon selection.
        function queryPredictive(number, street) {

            return $http.get(baseUrl + `Get/Predictive?lotnumber=${number}&roadname=${street}`)
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in queryPredictive: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // Used when we want the full address dto from a fuzzy search
        // (Not currently used...)
        function queryLot(number, street) {

            return $http.get(baseUrl + `Get/Lot?lotnumber=${number}&roadname=${street}`)
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in queryLot: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // Searches for addresses by street/house number in the local database
        function queryByStreetNumber(query) {
            return $http.get(baseUrl + `Get/QueryByStreetNumber?query=${encodeURIComponent(query)}`)
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in queryByStreetNumber: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // Returns the full address dto given the specific object id.
        function getById(id) {

            return $http.get(baseUrl + `Get/ById?id=${id}`)
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in getById: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        // Returns the remaining info that searches VIA PSMA do not cover.
        function getRemainder(partial) {

            if (partial == null || partial == "" || (typeof partial == 'undefined')) {
                log.logWarning("Unable to reverse geocode. Information not available.", "", null, true);
                return;
            }

            var query = `Get/Remainder?number=${partial.streetNumber}&street=${partial.street}&streetType=${partial.streetType}&postcode=${partial.postCode}`;

            if (partial.unitNumber != null) {
                query += `&unitNumber=${partial.unitNumber}`;
            }


            return $http.get(baseUrl + query)
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in getRemainder: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getCurrentProcessingState() {

            return $http.get(baseUrl + "Get/GetCurrentProcessingState")
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in getCurrentProcessingState: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getLastProcessDate() {

            return $http.get(baseUrl + "Get/LastProcessDate")
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in getLastProcessDate: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /**
         * Processes an entirely new dataset. At the moment support for "progress" is not
         * great, so we just tell the user to manually refresh the page every so often.
         */
        function processNewDataset(updateAddress = true, updateSuburbs = true, updateLga = true) {

            return $http({
                url: baseUrl + "Get/ProcessNewDataset",
                params: { updateAddress, updateSuburbs, updateLga },
                method: 'GET',
            }).then(success, fail)

            return $http.get(baseUrl + "Get/ProcessNewDataset")
                .then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                } else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error in getLastProcessDate: " + error;
                console.log(error);
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }


        return service;
    }
})();

