(function () {
    // The JobprojectdetailListCtrl supports a list page.
    'use strict';
    var controllerId = 'JobprojectdetailListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'jobprojectdetailservice', 'daterangehelper', jobprojectdetailListController]);
function jobprojectdetailListController($rootScope, $scope, $mdDialog, jobprojectdetailservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Job Project Details';
        vm.jobprojectdetailList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'jobId',
                    description: 'Job',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'lotTypeCode', //planTypeCode
                    description: 'Plan Type',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'prefix',
                    description: 'Prefix',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'strataLotNumber',
                    description: 'Strata Lot Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'surveyStrataLotNumber',
                    description: 'Survey Strata Lot Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'originalLotNumber',
                    description: 'Original Lot Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'lotNumber',
                    description: 'Lot Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'depositedPlanNumber',
                    description: 'Deposited Plan Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'originalDepositedPlanNumber',
                    description: 'Original Deposited Plan Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'volume',
                    description: 'Volume',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'folio',
                    description: 'Folio',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'houseNumber',
                    description: 'House Number',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'streetName',
                    description: 'Street Name',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'streetType',
                    description: 'Street Type',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'suburb',
                    description: 'Suburb',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'stateCode',
                    description: 'State Code',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'postcode',
                    description: 'Postcode',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'localGovernmentAuthority',
                    description: 'Local Government Authority',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'projectDescriptionCode',
                    description: 'Project',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        var persistRangeName = "jobprojectdetailList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('This Month', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createJobprojectdetail = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/jobprojectdetail/jobprojectdetail-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "prefix", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            jobprojectdetailservice.getListCancel();
            jobprojectdetailservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = jobprojectdetailservice.currentFilter();
                    vm.jobprojectdetailList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.jobprojectdetailList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createJobprojectdetail,
                name: 'Add Job Project Detail',
                desc: 'Add Job Project Detail',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();
    }
})();