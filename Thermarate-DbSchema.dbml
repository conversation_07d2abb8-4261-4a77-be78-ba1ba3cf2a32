// - User - //

Table RSS_User {
  UserId            UUID         [not null, PK]
  AspNetUserId      varchar(128) [not null, ref: > AspNetUsers.Id]
  FirstName         varchar(100) [not null]
  LastName          varchar(100) [not null]
  Email             varchar(100) [not null]
  Phone             varchar(20)  [null]
  IsActive          boolean      [not null, default: TRUE]
  CreatedOn         timestamp    [not null, default: 'now()']
  CreatedByName     varchar(50)  [null]
  ModifiedOn        timestamp    [null]
  ModifiedByName    varchar(50)  [null]
  Deleted           boolean      [not null, default: FALSE]
  IsGoogleLinked    boolean      [not null, default: FALSE]
  IsMicrosoftLinked boolean      [not null, default: FALSE]
}

Table AspNetUsers {
  Id                   varchar(128) [not null, PK]
  Email                varchar(256) [null]
  EmailConfirmed       boolean      [not null]
  PasswordHash         varchar      [null]
  SecurityStamp        varchar      [null]
  PhoneNumber          varchar      [null]
  PhoneNumberConfirmed boolean      [not null]
  TwoFactorEnabled     boolean      [not null]
  LockoutEndDateUtc    timestamp    [null]
  LockoutEnabled       boolean      [not null]
  AccessFailedCount    int          [not null]
  UserName             varchar(256) [not null]
}

Table AspNetRoles {
  Id           varchar(128) [not null, PK]
  Name         varchar(256) [not null]
  SortOrder    int          [not null, default: 999]
  ParentRoleId UUID         [null]
  Type         varchar(50)  [not null, default: 'Checkbox']
}

// - Client - //

Table RSS_Client {
  ClientId               UUID         [not null, PK]
  ClientName             varchar(100) [not null]
  CreatedOn              timestamp    [not null, default: 'now()']
  CreatedByName          varchar(50)  [null]
  ModifiedOn             timestamp    [null]
  ModifiedByName         varchar(50)  [null]
  Deleted                boolean      [not null, default: FALSE]
  ClientCostItemsJson    varchar      [null]
  EnergyLabsSettingsJson varchar      [null]
}

Table RSS_UsersClient {
  UserClientId   UUID         [not null, PK]
  UserId         varchar(128) [not null, ref: > AspNetUsers.Id]
  ClientId       UUID         [not null, ref: > RSS_Client.ClientId]
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(100) [not null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(100) [null]
  Deleted        boolean      [not null]
}

Table RSS_ClientDefault {
  ClientDefaultId             UUID         [not null, PK]
  ClientId                    UUID         [not null, ref: > RSS_Client.ClientId]
  ProjectTypeCode             varchar(20)  [null, ref: > RSS_ProjectType.ProjectTypeCode]
  AssessmentSoftwareCode      varchar(20)  [null, ref: > RSS_AssessmentSoftware.AssessmentSoftwareCode]
  BuildingExposureCode        varchar(20)  [null, ref: > RSS_BuildingExposure.BuildingExposureCode]
  ComplianceMethodCode        varchar(20)  [null]
  CertificationCode           varchar(20)  [null]
  PriorityCode                varchar(20)  [null]
  WorksDescriptionCode        varchar(20)  [null]
  CreatedOn                   timestamp    [not null, default: 'now()']
  CreatedByName               varchar(50)  [null]
  ModifiedOn                  timestamp    [null]
  ModifiedByName              varchar(50)  [null]
  Deleted                     boolean      [not null, default: FALSE]
  SpaceHeatingServiceTypeCode varchar(100) [null]
  SpaceHeatingGems2019Rating  decimal(4,1) [null]
  SpaceCoolingServiceTypeCode varchar(100) [null]
  SpaceCoolingGems2019Rating  decimal(4,1) [null]
  WaterHeatingServiceTypeCode varchar(100) [null]
  WaterHeatingGems2019Rating  decimal(4,1) [null]
  SwimmingPoolExists          boolean      [null]
  SwimmingPoolVolume          decimal(4,1) [null]
  SwimmingPoolGems2019Rating  decimal(4,1) [null]
  SpaExists                   boolean      [null]
  SpaVolume                   decimal(4,1) [null]
  SpaGems2019Rating           decimal(4,1) [null]
  PhotovoltaicExists          boolean      [null]
  PhotovoltaicCapacity        decimal(4,1) [null]
  IncludeDrawingsInReport     boolean      [not null, default: TRUE]
  StampDrawings               boolean      [not null, default: TRUE]
}

// - Project - //

Table RSS_Project {
  ProjectId              UUID          [not null, PK]
  ClientId               UUID          [null, ref: > RSS_Client.ClientId]
  ProjectName            varchar(100)  [not null]
  Description            varchar(500)  [null]
  ProjectTypeCode        varchar(50)   [null, ref: > RSS_ProjectType.ProjectTypeCode]
  IsActive               boolean       [not null, default: TRUE]
  Lots                   int           [null]
  LotArea                decimal(16,6) [null]
  LogoFileId             UUID          [null, ref: > RSS_File.FileId]
  SuburbCode             UUID          [null]
  StateCode              varchar(20)   [null, ref: > RSS_State.StateCode]
  LGA                    varchar(100)  [null]
  LGAShort               varchar(100)  [null]
  NatHERSClimateZoneCode varchar(20)   [null, ref: > RSS_NatHERSClimateZone.NatHERSClimateZoneCode]
  NCCClimateZoneCode     varchar(20)   [null, ref: > RSS_NCCClimateZone.NCCClimateZoneCode]
  Latitude               decimal(13,9) [null]
  Longitude              decimal(13,9) [null]
  LockWOHLocation        boolean       [not null, default: FALSE]
  EnergyLabsSettingsJson varchar       [null]
  VariationOptionsJson   varchar       [null, note: 'Stores project variation options as JSON']
  CreatedOn              timestamp     [not null, default: 'now()']
  CreatedByName          varchar(50)   [null, default: 'System']
  ModifiedOn             timestamp     [null]
  ModifiedByName         varchar(50)   [null]
  Deleted                boolean       [not null, default: FALSE]
  SortOrder              int           [not null, default: 999]
}

Table RSS_ProjectType {
  ProjectTypeCode varchar(50)  [not null, PK]
  Description     varchar(200) [not null]
  SortOrder       smallint     [not null, default: 999]
  Deleted         boolean      [not null, default: FALSE]
}

Table RSS_UserProject {
  UserProjectId  UUID         [not null, PK]
  UserId         UUID         [not null, ref: > RSS_User.UserId]
  ProjectId      UUID         [not null, ref: > RSS_Project.ProjectId]
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(50)  [null, default: 'System']
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}

// - Home Design - //

Table RSS_StandardHomeModel {
  StandardHomeModelId          UUID          [not null, PK]
  ProjectId                    UUID          [not null, ref: > RSS_Project.ProjectId]
  Title                        varchar(100)  [not null]
  Description                  varchar(500)  [null]
  Category                     varchar(200)  [null]
  MinLotWidth                  smallint      [null]
  Width                        decimal(12,2) [null]
  Depth                        decimal(12,2) [null]
  FloorArea                    smallint      [null]
  HouseArea                    decimal(12,2) [null]
  CarParkingArea               decimal(12,2) [null]
  OutdoorAlfrescoArea          decimal(12,2) [null]
  Storeys                      smallint      [null]
  NumberOfBedrooms             smallint      [null]
  NumberOfBathrooms            smallint      [null]
  NumberOfGarageSpots          smallint      [null]
  NatHERSClimateZone           smallint      [null]
  NorthOffset                  smallint      [null]
  BlockType                    varchar(200)  [null]
  VariableOptionsJson          varchar       [null]
  HomePlanImageId              UUID          [null]
  CreatedOn                    timestamp     [not null, default: 'now()']
  CreatedByName                varchar(50)   [null]
  ModifiedOn                   timestamp     [null]
  ModifiedByName               varchar(50)   [null]
  Deleted                      boolean       [not null, default: FALSE]
  View3dFloorPlans             boolean       [null]
  FloorplannerLink             varchar(200)  [null]
  VariationOptionsSettingsJson varchar       [null]
  IsVariationOfHomeModelId     UUID          [null, ref: > RSS_StandardHomeModel.StandardHomeModelId]
  DrawingAreasJson             varchar       [null]
  ZoneSummaryBuildingDataJson  varchar       [null]
}

Table RSS_StandardHomeModelOption {
  StandardHomeModelOptionId    UUID          [not null, PK]
  StandardHomeModelId          UUID          [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId]
  NatHERSClimateZone           tinyint       [not null]
  SiteExposure                 varchar(100)  [not null]
  FloorHeight                  decimal(12,6) [null]
  InteriorWallSolarAbsorptance decimal(15,5) [not null]
  FloorSolarAbsorptance        decimal(15,5) [not null]
  NorthOffset                  smallint      [not null]
  BlockType                    varchar(100)  [not null]
  RoofConstruction             varchar(100)  [not null]
  RoofInsulation               varchar(100)  [not null]
  RoofSolarAbsorptance         decimal(15,5) [not null]
  CeilingConstruction          varchar(100)  [not null]
  CeilingInsulation            varchar(100)  [not null]
  Comments                     varchar       [null]
  Active                       boolean       [not null]
  AssessmentMethod             varchar(100)  [null]
  CeilingFans                  varchar(100)  [not null, default: 'Nil']
  RecessedLightFittings        varchar(100)  [not null, default: 'Nil']
  ExteriorDoorSolarAbsorptance decimal(15,5) [not null]
  GarageDoorSolarAbsorptance   decimal(15,5) [not null]
}

Table RSS_StandardHomeModelFile {
  StandardHomeModelFileId UUID         [not null, PK]
  StandardHomeModelId     UUID         [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId]
  FileId                  UUID         [null, ref: > RSS_File.FileId]
  MetadataJson            varchar      [null]
  Deleted                 boolean      [not null]
  CreatedOn               timestamp    [not null]
  CreatedByName           varchar(100) [null]
  ModifiedOn              timestamp    [null]
  ModifiedByName          varchar(100) [null]
  SortOrder               smallint     [null]
  Label                   varchar(100) [null]
}

Table RSS_StandardHomeModelVariationOption {
  StandardHomeModelVariationOptionId UUID         [not null, PK]
  StandardHomeModelId                UUID         [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId]
  VariationCategoryCode              varchar(100) [not null]
  OptionName                         varchar(200) [not null]
  SortOrder                          smallint     [not null]
  Deleted                            boolean      [not null, default: FALSE]
}

// - Assessment - //

Table RSS_Assessment {
  AssessmentId            UUID           [not null, PK]
  JobId                   UUID           [null]
  StatusCode              varchar(20)    [not null]
  IsTemplate              boolean        [not null, default: FALSE]
  AssessmentSoftwareCode  varchar(20)    [null, ref: > RSS_AssessmentSoftware.AssessmentSoftwareCode]
  NCCClimateZoneCode      varchar(20)    [not null, ref: > RSS_NCCClimateZone.NCCClimateZoneCode]
  NatHERSClimateZoneCode  varchar(20)    [not null, ref: > RSS_NatHERSClimateZone.NatHERSClimateZoneCode]
  BuildingOrientation     decimal        [null]
  BuildingExposureCode    varchar(20)    [null, ref: > RSS_BuildingExposure.BuildingExposureCode]
  ConditionedFloorArea    decimal(15,5)  [not null]
  UnconditionedFloorArea  decimal(15,5)  [not null]
  AttachedGarageFloorArea decimal(15,5)  [not null]
  SoftwareFileId          UUID           [null, ref: > RSS_File.FileId]
  CertificateNumber       varchar(60)    [null]
  CerficateDate           timestamp      [null]
  CertificateDateOverride timestamp      [null]
  AssessorEmployeeId      int            [not null]
  AssessorSignatureFile   UUID           [null, ref: > RSS_File.FileId]
  CreatedOn               timestamp      [not null, default: 'now()']
  CreatedByName           varchar(50)    [null]
  ModifiedOn              timestamp      [null]
  ModifiedByName          varchar(50)    [null]
  Deleted                 boolean        [not null, default: FALSE]
}

// - Reference Tables - //

Table RSS_File {
  FileId         UUID         [not null, PK]
  FileName       varchar(200) [not null]
  FileExtension  varchar(10)  [not null]
  FileSize       int          [not null]
  MimeType       varchar(100) [not null]
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}

Table RSS_State {
  StateCode      varchar(20)  [not null, PK]
  Description    varchar(100) [null]
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}

Table RSS_NatHERSClimateZone {
  NatHERSClimateZoneCode varchar(20) [not null, PK]
  Description            varchar(20) [null]
  CreatedOn              timestamp   [not null, default: 'now()']
  CreatedByName          varchar(50) [null]
  ModifiedOn             timestamp   [null]
  ModifiedByName         varchar(50) [null]
  Deleted                boolean     [not null, default: FALSE]
}

Table RSS_NCCClimateZone {
  NCCClimateZoneCode varchar(20) [not null, PK]
  Description        varchar(20) [null]
  CreatedOn          timestamp   [not null, default: 'now()']
  CreatedByName      varchar(50) [null]
  ModifiedOn         timestamp   [null]
  ModifiedByName     varchar(50) [null]
  Deleted            boolean     [not null, default: FALSE]
}

Table RSS_BuildingExposure {
  BuildingExposureCode varchar(20)  [not null, PK]
  Description          varchar(100) [null]
  CreatedOn            timestamp    [not null, default: 'now()']
  CreatedByName        varchar(50)  [null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  Deleted              boolean      [not null, default: FALSE]
}

Table RSS_AssessmentSoftware {
  AssessmentSoftwareCode varchar(20)  [not null, PK]
  Description            varchar(100) [null]
  CreatedOn              timestamp    [not null, default: 'now()']
  CreatedByName          varchar(50)  [null]
  ModifiedOn             timestamp    [null]
  ModifiedByName         varchar(50)  [null]
  Deleted                boolean      [not null, default: FALSE]
}