// Name: assessmentservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'assessmentservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', assessmentservice]);

    function assessmentservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'assessment/';

        var service = {
            /* These are the operations that are available from this service. */
            copyAssessment: copyAssessment,
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getAssessment: getAssessment,
            createAssessment: createAssessment,
            updateAssessment: updateAssessment,
            triggerGenerateReports: triggerGenerateReports,
            deleteAssessment:deleteAssessment,
            undoDeleteAssessment:undoDeleteAssessment,
            approvePreliminaryReview,
            issueAssessment: issueAssessment,
            archiveAssessmentDrawing: archiveAssessmentDrawing,
            reinstateAssessmentDrawing: reinstateAssessmentDrawing,
            getCurrent: getCurrent,
            checkVersionNumber: checkVersionNumber,
            getNavigationAssessments: getNavigationAssessments,
            getFullAddress: getFullAddress,
            hasPriorityExpired: hasPriorityExpired,
            getBasicAssessmentList: getBasicAssessmentList,
            getAssessmentOptionDrawingsStillProcessing,
            uploadFilesFromList: uploadFilesFromList,
            recertify: recertify,
            makeEditable: makeEditable,
            checkJobNumberIsAvailable,
            updateAllowReportDownload,
        };
            
        return service;

        function getAssessmentOptionDrawingsStillProcessing(assessmentId, complianceOptionId) {
            var wkUrl = baseUrl + 'GetAssessmentOptionDrawingsStillProcessing';
            var params = { assessmentId, complianceOptionId };
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: false
            }).then(success, fail);
            function success(resp) {
                if (resp !== null && resp.data !== undefined && resp.data !== null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status === 0 || error.status === -1) {
                    return;
                }
                var msg = "Error getting File list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Assessment list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getBasicAssessmentList(jobId) {
            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetBasicByJob';
            var params = { jobId: jobId };
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: false,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Assessment list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getAssessment(assessmentId) {
            return $http({
                url: baseUrl + 'Get',
                params: {assessmentId: assessmentId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //gets a very simple dto that contains all info needed to display and navgate between different assessments for the same job.
        function getNavigationAssessments(jobId) {
            return $http({
                url: baseUrl + 'GetNavigationAssessments',
                params: { jobId: jobId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting navigation Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAssessment(data, invalid) {
            var url = baseUrl + 'Create?invalid='+invalid;
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                //default message
                var message = "Assessment created";
                //add status if its not a template
                if (resp.data != null) {
                    message = message + " with status " + resp.data.statusDescription;
                }
                log.logSuccess(message);
                return resp.data;
            }
            function fail(error) {
                var msg = "Error created Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //copies the given assesment id and returns the id of the copied assessment
        function copyAssessment(assessmentId, copyDrawings, jobId, assessorId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { assessmentId: assessmentId, copyDrawings: copyDrawings, jobId: jobId, assessorId: assessorId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Copied");
                return resp;
            }
            function fail(error) {
                var msg = "Error copying assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAssessment(data, invalid) {
            var url = baseUrl + 'Update?invalid='+invalid;
            return $http.post(url, data).then(resp => {
                //default message
                var message = "Assessment changes saved";
                //add status if its not a template
                if (resp.data != null && resp.data.isTemplate != true) {
                    message = message + " with status " + resp.data.statusDescription;
                }
                log.logSuccess(message);
                return resp.data;
            }, error => {
                var msg = "Error updating Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            });
        }

        function triggerGenerateReports(assessmentId, statusCode) {
            var url = `${baseUrl}TriggerGenerateReports?assessmentId=${assessmentId}&statusCode=${statusCode}`;
            return $http.post(url).then(resp => {
                var message = "Regenerating of reports has been triggered";
                log.logSuccess(message);
                return resp.data;
            }, error => {
                var msg = "Error regenerating Assessment reports: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            });
        }

        function deleteAssessment(assessmentId, deleteEntireJob) {

            // Existing logic is to delete the entire job on assessment delete *shrug*
            // So to keep existing code intact, default to true.
            if (deleteEntireJob == null)
                deleteEntireJob = true;


            return $http({
                url: baseUrl + 'Delete',
                params: { assessmentId: assessmentId, deleteEntireJob: deleteEntireJob },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAssessment(assessmentId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { assessmentId: assessmentId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function approvePreliminaryReview(assessmentId) {
            return $http({
                url: baseUrl + 'ApprovePreliminaryReview',
                params: { assessmentId },
                method: 'GET',
            }).then(success, fail)

            function success(resp) {
                return resp.data;
            }

            function fail(error) {
                var msg = "Error approving preliminary review. Error was: " + error;

                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function issueAssessment(assessmentId, assessmentVersion) {
            return $http({
                url: baseUrl + 'Issue',
                params: { assessmentId, assessmentVersion },
                method: 'GET',
            }).then(success, fail)
            
            function success(resp) {
                return resp.data;
            }
            
            function fail(error) {
                var msg = "Error Generating PDF for Assessment. Unable to proceed to Complete state. Error was: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function archiveAssessmentDrawing(assessmentId, assessmentDrawingId) {
            return $http({
                url: baseUrl + 'Archive',
                params: { assessmentId: assessmentId, assessmentDrawingId: assessmentDrawingId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                if (resp.data == undefined || resp.data == null || resp.data == "") {
                    var msg = "Error archiving assessment drawing.";
                    log.logError(msg, null, null, true);
                }
                return resp;
            }
            function fail(error) {
                var msg = "Error achiving PDF for Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function reinstateAssessmentDrawing(assessmentDrawingId) {
            return $http({
                url: baseUrl + 'Reinstate',
                params: { assessmentDrawingId: assessmentDrawingId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error achiving PDF for Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function makeEditable(assessmentId) {
            return $http({
                url: baseUrl + 'MakeEditable',
                params: { assessmentId: assessmentId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                if (resp.data == undefined || resp.data == null || resp.data == "") {
                    var msg = "Error making Assessment Editable";
                    log.logError(msg, null, null, true);
                }
                return resp;
            }
            function fail(error) {
                var msg = "Error making Assessment Editable: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getCurrent(jobId) {
            return $http({
                url: baseUrl + 'GetCurrent',
                params: { jobId: jobId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (!resp || resp.data == null || resp.data == "") {
                    log.logWarning("You do not currently have an Assessment ready to Issue", null, null, true);
                }

                return resp;
            }
            function fail(error) {
                var msg = "Error getting current Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function checkVersionNumber(jobId, versionNumber) {
            return $http({
                url: baseUrl + 'CheckVersionNumber',
                params: { jobId: jobId, versionNumber: versionNumber },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error getting current Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function recertify(jobDto) {
            return $http({
                url: baseUrl + 'Recertify',
                data: jobDto,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error recertifying Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getFullAddress(assessmentProjectDetail) {
            return BuildFullAddress(assessmentProjectDetail);
        }

        function BuildFirstPart(assessmentProjectDetail) {
            var firstPart = "",
                lotTypeCode = assessmentProjectDetail.lotType ? assessmentProjectDetail.lotType.lotTypeCode : null, //planTypeCode
                streetNumber = assessmentProjectDetail.houseNumber ? assessmentProjectDetail.houseNumber : null,
                strataLotNumber = assessmentProjectDetail.strataLotNumber ? assessmentProjectDetail.strataLotNumber : null,
                surveyStrataLotNumber = assessmentProjectDetail.surveyStrataLotNumber ? assessmentProjectDetail.surveyStrataLotNumber : null,
                lotNumber = assessmentProjectDetail.lotNumber ? assessmentProjectDetail.lotNumber : null,
                originalLotNumber = assessmentProjectDetail.originalLotNumber ? assessmentProjectDetail.originalLotNumber : null,
                prefix = assessmentProjectDetail.prefix ? assessmentProjectDetail.prefix : null;
            
            if ((lotNumber && lotNumber != null && lotNumber != "") && lotTypeCode != "PTStrata" && lotTypeCode != "PTSurvey" && false) {
                firstPart = streetNumber;
            } else {
                var streetNumString = " ";
                if (streetNumber && streetNumber != null && streetNumber != "") {
                    streetNumString = " (#" + streetNumber + ")";
                }

                var appendLotNumber = surveyStrataLotNumber || strataLotNumber;
                switch (lotTypeCode) {
                    case "SurveyStrataLot":
                        firstPart = "Survey Strata Lot";
                        break;
                    default:
                        firstPart = "Lot";
                        break;
                }

                if (appendLotNumber && originalLotNumber) {
                    firstPart = firstPart + " " + appendLotNumber + " of Original Lot " + originalLotNumber + streetNumString;
                } else if (appendLotNumber) {
                    firstPart = firstPart + " " + appendLotNumber + streetNumString;
                } else if (originalLotNumber) {
                    firstPart = "Original Lot " + originalLotNumber + streetNumString;
                } else if (lotNumber) {
                    firstPart = firstPart + " " + lotNumber + streetNumString;
                } else if (streetNumString) {
                    firstPart = streetNumber;
                }
            }

            if (prefix && prefix != null && prefix != "") {
                firstPart = prefix + " " + firstPart;
            }

            if (firstPart == null) {
                firstPart = '';
            }

            return firstPart;
        }

        function BuildFullAddress(assessmentProjectDetail) {
            if (assessmentProjectDetail == undefined || assessmentProjectDetail == null) {
                return "";
            }
            var pre = "",
                post = "",
                second = false;

            var streetName = assessmentProjectDetail.streetName ? assessmentProjectDetail.streetName : null,
                suburb = assessmentProjectDetail.suburb ? assessmentProjectDetail.suburb : null,
                state = assessmentProjectDetail.stateCode ? assessmentProjectDetail.stateCode : null,
                postcode = assessmentProjectDetail.postcode ? assessmentProjectDetail.postcode : null;
          
           
            pre = BuildFirstPart(assessmentProjectDetail);
            if (streetName && streetName != null && streetName != "") {
                pre += " " + assessmentProjectDetail.streetName;
                if (assessmentProjectDetail.streetType && assessmentProjectDetail.streetType != '') {
                    pre += ' ' + assessmentProjectDetail.streetType;
                }
            }
            
            if(suburb && suburb != null && suburb != "") {
                if (pre != "") {
                    post += ", ";
                }
                post += (angular.isObject(suburb) ? common.toTitleCase(suburb.CustomFields.locality.toLowerCase()) : suburb);
                second = true;
            }

            if (state && state != null && suburb != "") {
                if (second == false) {
                    if (pre != "") {
                        post += ", ";
                    }
                } else {
                    post += " ";
                }

                post += state;

                if (postcode && postcode != null && postcode != "") {
                    if (post != "") {
                        post += " ";
                    }
                    post += postcode;
                }
            } else {
                if (postcode && postcode != null && postcode != "") {
                    post += " ";
                    post += postcode;
                }
            }

            return pre + post;
        }

        function hasPriorityExpired(assessmentId) {
            return $http({
                url: baseUrl + 'HasPriorityExpired',
                params: { assessmentId: assessmentId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error getting Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function uploadFilesFromList(assessmentId, jobId, existingFileNames, externalUrls) {
            return $http({
                url: baseUrl + 'UploadFilesFromList',
                params: { assessmentId: assessmentId, jobId: jobId },
                data: { existingFileNames: existingFileNames, externalUrls: externalUrls },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error uploading files from Assessment: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        function checkJobNumberIsAvailable(jobNumber, customClientName, clientId, jobId = null) {
            return $http({
                url: baseUrl + 'CheckJobNumberIsAvailable',
                params: { jobNumber, customClientName, clientId, jobId },
                method: 'GET',
            }).then(response => response.data)
        }

        // TODO: This will need updating.
        function updateAllowReportDownload(report) {
            return $http({
                url: baseUrl + 'UpdateAllowReportDownload',
                params: { 
                    fileId: report.fileId, 
                    show: report.showOnClientPortal,
                    download: report.allowDownloadOnClientPortal },
                method: 'POST',
            }).then(response => response.data)
        }

    }
})();
