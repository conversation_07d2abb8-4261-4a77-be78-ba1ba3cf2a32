// Name: zoneservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'zoneservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'uuid4', 'constructionservice', 'projectdescriptionservice', zoneservice]);

    function zoneservice(common, config, $http, uuid4, constructionservice, projectdescriptionservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'zone/';

        const isInterior = (zone) => (zone.zoneActivity == null ||
            (zone.zoneActivity.zoneActivityCode !== "ZARoofSpace" &&
             zone.zoneActivity.zoneActivityCode !== "ZASubfloorSpace" &&
             zone.zoneActivity.zoneActivityCode !== "ZAGroundSurface"));

        // Create Interior and Exterior Zone group summaries
        const interiorZones = (zones) => zones?.filter(x => isInterior(x));

        const habitableZones = (zones) => interiorZones(zones)?.filter(z => z.zoneType?.zoneTypeCode === "ZTHabitableRoom");
        const nonHabitableZones = (zones) => interiorZones(zones)?.filter(z => z.zoneType?.zoneTypeCode === "ZTNonHabitableRoom")
        const interconnectingZones = (zones) => interiorZones(zones)?.filter(z => z.zoneType?.zoneTypeCode === "ZTInterconnecting");
        const class1AZones = (zones) => interiorZones(zones)?.filter(z => z.nccClassification?.nccClassificationCode === "Class1A");
        const class10AZones = (zones) => interiorZones(zones)?.filter(z => z.zoneType?.zoneTypeCode === "ZTClass10A");
        const conditionedZones = (zones) => interiorZones(zones).filter(z => z.conditioned === true);
        const unconditionedZones = (zones) => interiorZones(zones)?.filter(z => z.conditioned === false);
        const garageZones = (zones) => interiorZones(zones)?.filter(
            z => z.zoneActivity?.zoneActivityCode === "ZAGarage" ||
                z.zoneActivity?.zoneActivityCode === "ZAGarageConditioned");

        const roofSpaceZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZARoofSpace");
        const subfloorSpaceZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZASubfloorSpace");
        const groundSurfaceZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZAGroundSurface");

        const kitchenLivingZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZAKitchenLiving");
        const livingZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZALiving");
        const dayTimeZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZADayTime");
        const bedroomZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZABedroom");
        const nightTimeZones = (zones) => zones?.filter(z => z.zoneActivity?.zoneActivityCode === "ZANightTime");
        const garageConditionedZones = (zones) => interiorZones(zones)?.filter(
            z => (z.zoneActivity?.zoneActivityCode === "ZAGarage" ||
                z.zoneActivity?.zoneActivityCode === "ZAGarageConditioned") &&
                z.conditioned === true);

        const defaultSectorsData = {
            "n": 0,
            "ne": 0,
            "e": 0,
            "se": 0,
            "s": 0,
            "sw": 0,
            "w": 0,
            "nw": 0,
            "total": 0
        };

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getZone: getZone,
            createZone: createZone,
            updateZone: updateZone,
            deleteZone:deleteZone,
            undoDeleteZone: undoDeleteZone,
            getZoneActivityList,
            getNccClassificationList,

            // Zone type grouping convenience functions
            isInterior,
            interiorZones,
            habitableZones,
            nonHabitableZones,
            interconnectingZones,
            class1AZones,
            class10AZones,
            conditionedZones,
            unconditionedZones,
            garageZones,
            roofSpaceZones,
            subfloorSpaceZones,
            groundSurfaceZones,
            kitchenLivingZones,
            livingZones,
            dayTimeZones,
            bedroomZones,
            nightTimeZones,
            garageConditionedZones,
            defaultSectorsData,

            calculateAirMovementOutcomeForZones,
            airMovementOutcome,

            calcPropertyTotalPerSector,
            calcGlassExteriorWallRatioPerSector,
            calcAverageGlazingUValuePerSector,
            calcAverageGlazingSHGCPerSector,
            generateConstructionRowsFromElements,
            azimuthToNeswString,
            passesZoneFilter,
            passesShadingFilter,
            defaultSpacesRoofsForBuilding,
            determineStoreysWithGlazing
        };

        return service;

    function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Zone list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getZone(zoneId) {
            return $http({
                url: baseUrl + 'Get',
                params: {zoneId: zoneId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Zone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createZone(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Building Floor Areas Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created Zone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateZone(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Building Floor Areas Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Zone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteZone(zoneId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { zoneId: zoneId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Zone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteZone(zoneId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { zoneId: zoneId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Zone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getZoneActivityList() {
            return $http({
                url: baseUrl + 'GetZoneActivityList',
                method: 'GET',
                cache: true,
            }).then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null)
                    return resp.data;
                else
                    return null;
            }
            function fail(error) {
                var msg = "Error getting Zone Activity List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getNccClassificationList() {
            return $http({
                url: baseUrl + 'GetNccClassificationList',
                method: 'GET',
                cache: true,
            }).then(success, fail);

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null)
                    return resp.data;
                else
                    return null;
            }
            function fail(error) {
                var msg = "Error getting NCC Classification List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }




        /**
         * If a single row fails, then return "FAIL". Otherwise we use the average of the required and achieved
         * values to calculate additional info (as per the singular Air movement outcomes).
         *
         * @param {any} zones The Zones you wish to calculate. Will generally be all zones on a given storey.
         * @returns "Fail" | "N/A" | "Standard" | "High" | "n.nn x Std"
         */
        function calculateAirMovementOutcomeForZones(zones) {

            if (zones == null || zones.length === 0)
                return "Insufficient Data";
            else {

                // If the first zone % is not the same as ALL, then it naturally "Varies"
                if (zones.every(x => x.airMovementAchievedM2 >= parseFloat(x.airMovementRequiredM2?.toFixed(2)))) {

                    let requiredAvg = zones.map(x => x.airMovementRequiredM2).reduce((a, b) => a + b, 0);
                    let achievedAvg = zones.map(x => x.airMovementAchievedM2).reduce((a, b) => a + b, 0);

                    return airMovementOutcome(requiredAvg, achievedAvg);
                }
                else
                    return "Fail";
            }
        }

        /**
         * Air movement doesn't just return "Pass" or "Fail" but instead goes into more detail.
         *
         * @param {Number} required The value required to achieve a non-failure rating.
         * @param {Number} achieved The achieved value
         *
         * @returns "Fail" | "N/A" | "Standard" | "High" | "n.nn x Std"
         */
        function airMovementOutcome(required, achieved) {

            if (required == null || achieved == null)
                return "N/A";


            required = parseFloat(required?.toFixed(2));
            achieved = parseFloat(achieved?.toFixed(2));

            let ratio = (achieved / required);

            if (required === 0)
                return "N/A";
            else if (common.equalish(ratio, 1))
                return "Standard";
            else if (achieved > (2 * required))
                return "High"
            else if (achieved < required)
                return "Fail";
            else
                return (achieved / required).toFixed(2) + " x Std";

        }


        /**
         * Loop over the child surfaces of a Category and return the total
         * of a specified field per Sector (and overall total)
         *
         * @param {string} type The Category's Type ('surface' | 'opening').
         * @param {string} categoryCode The Category who's children we want to sum.
         * @returns {
         *      {n:     {area, percentage},
         *      {ne:    {area, percentage},
         *      {e:     {area, percentage},
         *      {se:    {area, percentage},
         *      {s:     {area, percentage},
         *      {sw:    {area, percentage},
         *      {w:     {area, percentage},
         *      {nw:    {area, percentage},
         *      {total: {area, percentage}
         * } Sector object
        */
        function calcPropertyTotalPerSector(building, type, categoryCode, sectorDetermination, filters) {
            if (!sectorDetermination || !sectorDetermination.sectors)
                return;

            // Build dynamically based on the current selected sector determination (total will be added manually).
            let totalPerSector = { total: {area: 0, percentage: 0} };
            sectorDetermination.sectors.forEach(s => {
                totalPerSector[s.label.toLowerCase()] = {area: 0, percentage: 0};
            });

            let parents = [];
            if (type === 'surface')
                parents = building.surfaces;
            else if (type === 'opening')
                parents = building.openings;
            else
                throw('Error - Unknown type: ' + type);

            parents.forEach(parent => {

                if (parent.category.constructionCategoryCode !== categoryCode)
                    return;

                parent.elements.forEach(child => {
                    // Get the window zone
                    const windowZones = building.zones.filter(x => x.linkId === child.parentZoneId);
                    if(windowZones == null || windowZones.length === 0)
                        return;

                    const windowZone = windowZones[0];

                    // Check if passes the filter
                    const zoneFilter = passesZoneFilter(filters, windowZone);
                    const shadingFilter = passesShadingFilter(filters, child, parent.category.constructionCategoryCode);
                    const isPass = zoneFilter && shadingFilter;

                    if (isPass) {
                        const sector = child.sector?.toLowerCase(); // match key

                        // Now dynamic based on the selected sector determination.
                        Object.keys(totalPerSector).forEach(key => {
                            if (key === sector) {
                                totalPerSector[key].area += child.grossArea;
                                totalPerSector.total.area += child.grossArea;
                            }
                        });
                    }
                });
            });

            // Calc Total Area, now that the Total Area for each Aector has been calculated.
            // totalPerSector.total.area = Object.keys(totalPerSector).reduce((a, b) => a + totalPerSector[b].area, 0);

            // Calc Percentage Area for each Sector now that the Total Overall Area has been calculated.
            for (const sector in totalPerSector) {
                if (sector === 'total')
                    continue; // Skip and do after others have been calculated.

                let percentage = 0;
                if (totalPerSector.total.area) // Dont divide by 0
                    percentage = totalPerSector[sector].area / totalPerSector.total.area * 100;

                totalPerSector[sector].percentage = percentage;
            }

            // Calc Total Area Percentage for each Sector (Should always be 100% as reverse calculated).
            totalPerSector.total.percentage = Object.keys(totalPerSector).reduce((a, b) => a + totalPerSector[b].percentage, 0);

            return totalPerSector;
        }

        /**
         * Calculate the Glass Exterior Wall Ratio per Sector
         * from the Exterior Wall Area Totals per Sector and the Exterior Glazing Area Totals per Sector
         *
         * @param   {{n, ne, e, se, s, sw, w, nw, total}} exteriorWallSectorTotals    Sector object (output from calcPropertyTotalPerSector())
         * @param   {{n, ne, e, se, s, sw, w, nw, total}} exteriorGlazingSectorTotals Sector object (output from calcPropertyTotalPerSector())
         * @returns {{n, ne, e, se, s, sw, w, nw, total}} Sector object
        */
        function calcGlassExteriorWallRatioPerSector(exteriorWallSectorTotals, exteriorGlazingSectorTotals, sectorDetermination) {

            if (!exteriorWallSectorTotals || !exteriorGlazingSectorTotals)
                return;

            let glassExteriorWallRatioPerSector = { total: 0 };
            sectorDetermination.sectors.forEach(sector => {
                glassExteriorWallRatioPerSector[sector.label.toLowerCase()] = 0;
            });

            // Both parameters and output are the same structure.
            for (const key in glassExteriorWallRatioPerSector) {

                // Uses the area field not the percentage field.
                const exteriorGlazing = exteriorGlazingSectorTotals.hasOwnProperty(key) ? exteriorGlazingSectorTotals[key].area : 0;
                const exteriorWall = exteriorWallSectorTotals.hasOwnProperty(key) ? exteriorWallSectorTotals[key].area : 0;

                let sectorGlassExteriorWallRatio = 0;
                if (exteriorWall !== 0) // Don't divide by 0.
                    sectorGlassExteriorWallRatio = exteriorGlazing / exteriorWall * 100;

                glassExteriorWallRatioPerSector[key] = sectorGlassExteriorWallRatio;
            }

            return glassExteriorWallRatioPerSector;
        }

        function calcAverageGlazingUValuePerSector(rows, sectorDetermination) {
            if (sectorDetermination?.sectors == null)
                return;

            let averageGlazingUValuePerSector = {};

            for (let sector of sectorDetermination.sectors) {
                let rowsForSector = rows.filter(r => r.direction == sector.label.toLowerCase());
                if (rowsForSector.length > 0) {
                    let totalArea = rowsForSector.map(r => r.grossArea).reduce((a, b) => a + b);
                    averageGlazingUValuePerSector[sector.label.toLowerCase()] = rowsForSector.map(r => r.systemUValue * r.grossArea)
                                                                                             .reduce((a, b) => a + b)
                                                                                             / totalArea;
                } else {
                    averageGlazingUValuePerSector[sector.label.toLowerCase()] = 0;
                }
            }

            let totalArea = rows.map(r => r.grossArea).reduce((a, b) => a + b);
            averageGlazingUValuePerSector.total = rows.map(r => r.systemUValue * r.grossArea)
                                                      .reduce((a, b) => a + b)
                                                      / totalArea;

            return averageGlazingUValuePerSector;
        }

        function calcAverageGlazingSHGCPerSector(rows, sectorDetermination) {
            if (sectorDetermination?.sectors == null)
                return;

            let averageGlazingSHGCPerSector = {};

            for (let sector of sectorDetermination.sectors) {
                let rowsForSector = rows.filter(r => r.direction == sector.label.toLowerCase());
                if (rowsForSector.length > 0) {
                    let totalArea = rowsForSector.map(r => r.grossArea).reduce((a, b) => a + b);
                    averageGlazingSHGCPerSector[sector.label.toLowerCase()] = rowsForSector.map(r => r.shgc * r.grossArea)
                                                                                           .reduce((a, b) => a + b)
                                                                                           / totalArea;
                } else {
                    averageGlazingSHGCPerSector[sector.label.toLowerCase()] = 0;
                }
            }


            let totalArea = rows.map(r => r.grossArea).reduce((a, b) => a + b);
            averageGlazingSHGCPerSector.total = rows.map(r => r.shgc * r.grossArea)
                                                    .reduce((a, b) => a + b)
                                                    / totalArea;

            return averageGlazingSHGCPerSector;
        }

        function generateConstructionRowsFromElements(building, parents, filters) {
            let rows = [];
            for (let parent of parents) {
                for (let child of parent.elements) {
                    const windowZones = building.zones.filter(x => x.linkId === child.parentZoneId);
                    if(windowZones == null || windowZones.length === 0)
                        return;
                    const windowZone = windowZones[0];
                    const zoneFilter = passesZoneFilter(filters, windowZone);
                    const shadingFilter = passesShadingFilter(filters, child, parent.category.constructionCategoryCode);
                    const isPass = zoneFilter && shadingFilter;
                    if (isPass) {
                        let row = {
                            systemUValue: parent.performance.overrideUValue || parent.performance.uValue,
                            shgc: parent.performance.overrideSHGC || parent.performance.shgc,
                            grossArea: child.grossArea,
                            direction: child.sector.toLowerCase()
                        }
                        rows.push(row);
                    }
                }
            }
            return rows;
        }

        function azimuthToNeswString(azimuth) {
            if (azimuth == null) {
                return "";
            }

            // Explicitly handle the case when azimuth is exactly 0
            if (azimuth === 0) {
                return "N";
            }

            if ((azimuth >= 315 && azimuth <= 360 || azimuth > 0 && azimuth < 45)) {
                return "N";
            }
            if (azimuth >= 45  && azimuth < 135) return "E";
            if (azimuth >= 135  && azimuth < 225)  return "S";
            if (azimuth >= 225 && azimuth < 315) return "W";

            return "????";
        }

        // Below have been lifted from: export-glazing-calc (minor changes)
        // -----
        function passesZoneFilter(filters, zone) {

            // Loop over all filters and if this row matches any of the criteria, apply it.
            // Note that it's (theoretically) possible for a row to match multiple filters.
            // So in that case we apply them in order (so last takes precedence).

            const isPass = filters.every(o => {

                let passes = false;

                // Being set to null on clear for some reason. Should always be array...
                if (!o.selection)
                    o.selection = [];

                // Not on matching storey so just continue.
                if (o.storey !== "ALL" && o.storey?.floor !== zone.storey)
                    return;

                // If these 2 conditions are met, ALL windows within the storey meet this criteria.
                if (o.group === "All Zones" && o.selection.includes("All Zones"))
                    passes = true;

                if (o.group === "Zone Name") {
                    if (o.selection.some(x => x.code === "ALL" || x.code === zone.zoneDescription))
                        passes = true;
                }

                if (o.group === "Zone Activity") {
                    const code = zone.zoneActivity.zoneActivityCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        passes = true;
                }

                if (o.group === "Zone Type") {
                    const code = zone.zoneType.zoneTypeCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        passes = true;
                }

                if (o.group === "Conditioning") {
                    if(o.selection.includes("All Conditioning Types"))
                        passes = true;
                    if (o.selection.includes("All Conditioned Zones") && zone.conditioned === true)
                        passes = true;
                    if (o.selection.includes("All Unconditioned Zones") && zone.conditioned === false)
                        passes = true;
                }

                if (o.group === "NCC Classification") {
                    const code = zone.nccClassification.nccClassificationCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        passes = true;
                }

                return passes;
            });

            return isPass;

        }

        function passesShadingFilter(filters, child, constructionCategoryCode) {
            // Seperate filter fields for Wall and Glazing.

            // None is a valid filter option but does is not returned as a string, either null or blank.

            let highestHorizontalShading =
                constructionservice.greaterShading(child.horizontalProjection1?.shading, child.horizontalProjection2?.shading);
            if (!highestHorizontalShading)
                highestHorizontalShading = 'No Shading';

            let highestVerticalShading =
                constructionservice.greaterShading(child.verticalScreen1?.shading, child.verticalScreen2?.shading, child.verticalScreen3?.shading);
            if (!highestVerticalShading)
                highestVerticalShading = 'No Shading';

            const isPass = filters.every(o => {
                // If filter is empty, it passes (same as selecting all possibilities).
                let horizontalMatch = true;
                let verticalMatch = true;

                if (constructionCategoryCode === 'ExteriorWall') {
                    if (o.wallHorizontalShading.selection.length > 0)
                        horizontalMatch = o.wallHorizontalShading.selection.map(o => o.code).includes(highestHorizontalShading);
                    if (o.wallVerticalShading.selection.length > 0)
                        verticalMatch = o.wallVerticalShading.selection.map(o => o.code).includes(highestVerticalShading);
                } else if (constructionCategoryCode === 'ExteriorGlazing') {
                    if (o.glazingHorizontalShading.selection.length > 0)
                        horizontalMatch = o.glazingHorizontalShading.selection.map(o => o.code).includes(highestHorizontalShading);
                    if (o.glazingVerticalShading.selection.length > 0)
                        verticalMatch = o.glazingVerticalShading.selection.map(o => o.code).includes(highestVerticalShading);
                } else
                    throw ('Invalid constructionCategoryCode: ' + constructionCategoryCode);

                return horizontalMatch && verticalMatch;
            });

            return isPass;
        }

        function defaultSpaceRoof() {
            return {
                createdOn: new Date().toUTCString(),
                zoneId: uuid4.generate(),
                linkId: uuid4.generate(),
                floorArea: null,
                zoneNumberSource: "AUTO"
            };
        }

        // [THR-498] Based on file "Spaces and Roof Defaults (mapped to Building Description)" in
        async function defaultSpacesRoofsForBuilding(buildingDescription, assessmentStories) {
            let spaces = [];
            let roofs = [];

            // Spaces
            let houseSpace = {
                zoneDescription: "House",
                location: "Other",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "House",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTHouse",
                }
            };
            let undercroftHouseSpace = {
                zoneDescription: "Undercroft House",
                location: "Other",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "House",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTHouse",
                }
            };
            let groundFloorHouseSpace = {
                zoneDescription: "Ground Floor House",
                location: "Other",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "House",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTHouse",
                }
            };
            let firstFloorHouseSpace = {
                zoneDescription: "First Floor House",
                location: "Other",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "House",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTHouse",
                }
            };
            let secondFloorHouseSpace = {
                zoneDescription: "Second Floor House",
                location: "Other",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "House",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTHouse",
                }
            };
            let garageSpace = {
                zoneDescription: "Garage",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Garage",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTGarage",
                }
            };
            let undercroftGarageSpace = {
                zoneDescription: "Undercroft Garage",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Garage",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTGarage",
                }
            };
            let alfrescoSpace = {
                zoneDescription: "Alfresco",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Covered Outdoor Living",
                    lampPowerMaximumWM2: 4,
                    zoneTypeCode: "ZTOutdoor",
                }
            };
            let porchSpace = {
                zoneDescription: "Porch",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Porch",
                    lampPowerMaximumWM2: 5,
                    zoneTypeCode: "ZTPorch",
                }
            };
            let balconySpace = {
                zoneDescription: "Balcony",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Covered Outdoor Living",
                    lampPowerMaximumWM2: 4,
                    zoneTypeCode: "ZTOutdoor",
                }
            };
            let firstFloorBalconySpace = {
                zoneDescription: "First Floor Balcony",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Covered Outdoor Living",
                    lampPowerMaximumWM2: 4,
                    zoneTypeCode: "ZTOutdoor",
                }
            };
            let secondFloorBalconySpace = {
                zoneDescription: "Second Floor Balcony",
                zoneType: {
                    availableFor: "spaces",
                    defaultNccClassificationCode: "Class10A",
                    description: "Covered Outdoor Living",
                    lampPowerMaximumWM2: 4,
                    zoneTypeCode: "ZTOutdoor",
                }
            };

            // Roofs
            let roofRoof        = { zoneDescription: "Roof"              };
            let groundFloorRoof = { zoneDescription: "Ground Floor Roof" };
            let firstFloorRoof  = { zoneDescription: "First Floor Roof"  };
            let secondFloorRoof = { zoneDescription: "Second Floor Roof" };

            // Get default Storeys for Building Description
            let buildingDescDefaultStoreys = (await projectdescriptionservice.getList()).data.find(x => x.projectDescriptionCode == buildingDescription).storeys;

            let defaultFirstStoreyNum  = null;
            let defaultSecondStoreyNum = null;
            let defaultThirdStoreyNum  = null;
            let defaultFourthStoreyNum = null;

            // Set Spaces and Roofs
            switch (buildingDescription) {
                case "BDSingle":
                    defaultFirstStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    spaces = [
                        { ...houseSpace,    storey: defaultFirstStoreyNum },
                        { ...garageSpace,   storey: defaultFirstStoreyNum },
                        { ...alfrescoSpace, storey: defaultFirstStoreyNum },
                        { ...porchSpace,    storey: defaultFirstStoreyNum }
                    ];
                    roofs = [
                        { ...roofRoof,      storey: defaultFirstStoreyNum }
                    ];
                    break;
                case "BDSingleUC":
                    defaultFirstStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    defaultSecondStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 1).description == x.name).floor;
                    spaces = [
                        { ...undercroftHouseSpace,  storey: defaultFirstStoreyNum  },
                        { ...undercroftGarageSpace, storey: defaultFirstStoreyNum  },
                        { ...groundFloorHouseSpace, storey: defaultSecondStoreyNum },
                        { ...alfrescoSpace,         storey: defaultSecondStoreyNum },
                        { ...porchSpace,            storey: defaultSecondStoreyNum }
                    ];
                    roofs = [
                        { ...roofRoof, storey: defaultSecondStoreyNum }
                    ];
                    break;
                case "BDTwo":
                    defaultFirstStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    defaultSecondStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 1).description == x.name).floor;
                    spaces = [
                        { ...groundFloorHouseSpace,  storey: defaultFirstStoreyNum  },
                        { ...garageSpace,            storey: defaultFirstStoreyNum  },
                        { ...alfrescoSpace,          storey: defaultFirstStoreyNum  },
                        { ...porchSpace,             storey: defaultFirstStoreyNum  },
                        { ...firstFloorHouseSpace,   storey: defaultSecondStoreyNum },
                        { ...firstFloorBalconySpace, storey: defaultSecondStoreyNum }
                    ];
                    roofs = [
                        { ...groundFloorRoof, storey: defaultFirstStoreyNum  },
                        { ...firstFloorRoof,  storey: defaultSecondStoreyNum }
                    ];
                    break;
                case "BDTwoUC":
                    defaultFirstStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    defaultSecondStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 1).description == x.name).floor;
                    defaultThirdStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 2).description == x.name).floor;
                    spaces = [
                        { ...undercroftHouseSpace,  storey: defaultFirstStoreyNum  },
                        { ...undercroftGarageSpace, storey: defaultFirstStoreyNum  },
                        { ...groundFloorHouseSpace, storey: defaultSecondStoreyNum },
                        { ...alfrescoSpace,         storey: defaultSecondStoreyNum },
                        { ...porchSpace,            storey: defaultSecondStoreyNum },
                        { ...firstFloorHouseSpace,  storey: defaultThirdStoreyNum  },
                        { ...balconySpace,          storey: defaultThirdStoreyNum  }
                    ];
                    roofs = [
                        { ...groundFloorRoof, storey: defaultFirstStoreyNum  },
                        { ...firstFloorRoof,  storey: defaultSecondStoreyNum }
                    ];
                    break;
                case "BDThree":
                    defaultFirstStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    defaultSecondStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 1).description == x.name).floor;
                    defaultThirdStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 2).description == x.name).floor;
                    spaces = [
                        { ...groundFloorHouseSpace,   storey: defaultFirstStoreyNum  },
                        { ...garageSpace,             storey: defaultFirstStoreyNum  },
                        { ...alfrescoSpace,           storey: defaultFirstStoreyNum  },
                        { ...porchSpace,              storey: defaultFirstStoreyNum  },
                        { ...firstFloorHouseSpace,    storey: defaultSecondStoreyNum },
                        { ...firstFloorBalconySpace,  storey: defaultSecondStoreyNum },
                        { ...secondFloorHouseSpace,   storey: defaultThirdStoreyNum  },
                        { ...secondFloorBalconySpace, storey: defaultThirdStoreyNum  }
                    ];
                    roofs = [
                        { ...groundFloorRoof, storey: defaultFirstStoreyNum  },
                        { ...firstFloorRoof,  storey: defaultSecondStoreyNum },
                        { ...secondFloorRoof, storey: defaultThirdStoreyNum  }
                    ];
                    break;
                case "BDThreeUC":
                    defaultFirstStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 0).description == x.name).floor;
                    defaultSecondStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 1).description == x.name).floor;
                    defaultThirdStoreyNum  = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 2).description == x.name).floor;
                    defaultFourthStoreyNum = assessmentStories.find(x => buildingDescDefaultStoreys.find(y => y.floor == 3).description == x.name).floor;
                    spaces = [
                        { ...undercroftHouseSpace,    storey: defaultFirstStoreyNum  },
                        { ...undercroftGarageSpace,   storey: defaultFirstStoreyNum  },
                        { ...groundFloorHouseSpace,   storey: defaultSecondStoreyNum },
                        { ...alfrescoSpace,           storey: defaultSecondStoreyNum },
                        { ...porchSpace,              storey: defaultSecondStoreyNum },
                        { ...firstFloorHouseSpace,    storey: defaultThirdStoreyNum  },
                        { ...balconySpace,            storey: defaultThirdStoreyNum  },
                        { ...secondFloorHouseSpace,   storey: defaultFourthStoreyNum },
                        { ...secondFloorBalconySpace, storey: defaultFourthStoreyNum }
                    ];
                    roofs = [
                        { ...groundFloorRoof, storey: defaultFirstStoreyNum  },
                        { ...firstFloorRoof,  storey: defaultSecondStoreyNum },
                        { ...secondFloorRoof, storey: defaultThirdStoreyNum  }
                    ];
                    break;
            }

            // Add default fields to each Space and Roof
            for (let i = 0; i < spaces.length; i++) { spaces[i] = { ...defaultSpaceRoof(), zoneNumber: `A00${i+1}`, ...spaces[i] }; }
            for (let i = 0; i < roofs .length; i++) { roofs[i]  = { ...defaultSpaceRoof(), zoneNumber: `R00${i+1}`, ...roofs[i]  }; }

            // Determine "Not Applicable" and return
            return {
                spaces: spaces,
                roofs: roofs,
                zoneTypesNotApplicable: {
                    floorPlanSpaces: buildingDescription == "PDOther",
                    generalroofs: buildingDescription == "PDOther"
                },
            };
        }

        function determineStoreysWithGlazing(parentOpenings) {
            if (!parentOpenings || parentOpenings.length === 0)
                return [];

            var all = parentOpenings
                .map(x => x.elements)
                .reduce((a, b) => [...a, ...b])
                .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing" ||
                    x.category.constructionCategoryCode === "InteriorGlazing")
                .map(x => x.storey);

            const unique =  [...new Set(all)];
            return unique;
        }

    }
})();
