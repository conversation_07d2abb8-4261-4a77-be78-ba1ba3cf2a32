﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using TenureExtraction;

namespace ExtractionTests
{
    [TestClass]
    public class Tests
    {
        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestFullDownload()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";
            var dl = new TenureExtraction.Downloader(directory);
            bool passed = await dl.RunFullProcess();

            Assert.IsTrue(passed, "Failed to download all datasets!");
            ;
        }

        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestFullExtraction()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";
            var ex = new PsaExtractor(directory, "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True");
            string table = await ex.RunFullProcess(cleanOnFailure: false);

            // Assert.IsTrue(string != null, "Failed to download all datasets!");
            ;
        }

        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public void TestTenureExtraction()
        {
            string testId = "30142409";

            var t = new TenureExtraction.TenureExtractor(
                Directory.GetCurrentDirectory() + "/Data/Land_Tenure_LGATE_226_WA_GDA2020_Subscription.gpkg",
                new TenureExtraction.Logger((s) => { }));

            var x = t.GetMatchingTenureRecords(testId);

            // NOTE: Either of these could fail if this land_id is removed from the dataset or the value
            // of the title identifier is changed, so check those things if this test is not successful.
            Assert.IsTrue(x.Count > 0, "Failed to successfuly extract data from GeoPackage.");
            Assert.AreEqual(x[0].TitleIdentifier, "LR3173/633", "Failed to successfuly extract data from GeoPackage.");
        }

        /// <summary>
        /// Use this for 
        /// </summary>
        /// <returns></returns>
        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestTargetedDownload()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";
            var dl = new TenureExtraction.Downloader(directory);

            string oauthToken = await dl.GetOAuthToken();

            bool r;
            // r = dl.FinalCleanup();
            //Assert.IsTrue(r, "Failed to clean output directory.");

            r = await dl.DownloadDataset(oauthToken,
                "https://direct-download.slip.wa.gov.au/datadownload/LGATE_Subscription/Cadastre_Polygon_LGATE_217_WA_GDA2020_Subscription_Geopackage.zip");
            Assert.IsTrue(r, "Failed to download Property Street Address (LGATE-251) Dataset correctly.");

            //r = await dl.DownloadDataset(
            //    "https://maps.slip.wa.gov.au/datadownloads/Landgate_v2_Subscription_Services/Cadastral/CadastrePolygonLGATE_217/CadastrePolygonLGATE_217.zip");
            //Assert.IsTrue(r, "Failed to download Cadastre (Polygon) (LGATE-217) Dataset correctly.");

            //r = await dl.DownloadDataset(
            //    "https://maps.slip.wa.gov.au/datadownloads/Landgate_v2_Subscription_Services/Cadastral/LodgedCadastrePolygonLGATE_222/LodgedCadastrePolygonLGATE_222.zip");
            //Assert.IsTrue(r, "Failed to download Lodged Cadastre (Polygon) (LGATE-222) Dataset correctly.");

            r = dl.Unzip();
            Assert.IsTrue(r, "Failed to Unzip dataset.");

            r = dl.DeleteUnnecessaryFiles();
            Assert.IsTrue(r, "Failed to delete extraneous files.");
        }

        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestPolygonExtraction()
        {
            // This is required for working with certain SQL types (Geography in this instance).
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";

            // Process the Dataset into our DB.
            TenureExtraction.PsaExtractor ex = new TenureExtraction.PsaExtractor(
                directory,
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            await ex.RunFullProcess();
            ex.Dispose();
        }


        [TestMethod]
        public void TestProjectOwnerFormatting()
        {
            
            string res = TenureExtractor.FormatProjectOwner("MCNICHOLAS");
            Assert.AreEqual(res, "McNicholas");
            
            res = TenureExtractor.FormatProjectOwner("O'DONALD");
            Assert.AreEqual(res, "O'Donald");
            
            res = TenureExtractor.FormatProjectOwner("SMITH-JONES");
            Assert.AreEqual(res, "Smith-Jones");

            // Note: Since all incoming names are ALLCAPS, we have no way
            // to determine whether names starting MAC* should be "Mac^*" or not.
            res = TenureExtractor.FormatProjectOwner("MACDONALD");
            Assert.AreEqual(res, "MacDonald");

        }
    }

    [TestClass]
    public class Debugging
    {

        

        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestExtraction()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";

            // Process the Dataset into our DB.
            TenureExtraction.PsaExtractor ex = new TenureExtraction.PsaExtractor(
                directory,
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            await ex.RunFullProcess();
            ex.Dispose();
        }

    }
}
