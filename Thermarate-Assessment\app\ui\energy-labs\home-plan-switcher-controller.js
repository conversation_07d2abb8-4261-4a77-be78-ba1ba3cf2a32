(function () {

    'use strict';
    angular.module('app').component('homePlanSwitcher', {
        bindings: {
            planViews: '<',
            showLabel: '<',
            view3dFloorPlansEnabled: '<',
            viewZoomEnabled: '<',
            floorplannerLink: '<',
            planViewIndex: '=?',
            small: '<'
        },
        templateUrl: 'app/ui/energy-labs/home-plan-switcher.html',
        controller: HomePlanSwitcherController,
        controllerAs: 'vm'
    });

    HomePlanSwitcherController.$inject = ['$rootScope', '$mdDialog'];

    function HomePlanSwitcherController($rootScope, $mdDialog) {

        let vm = this;

        vm.planViewIndex = 0;

        vm.prevImage = function($event) {
            stopPropagation($event);
            vm.runAnimationLeft = false;
            vm.runAnimationRight = false;
            setTimeout(() => {
                if (vm.planViewIndex !== 0) {
                    vm.planViewIndex--;
                }
                vm.runAnimationRight = true;
                $rootScope.$apply();
            }, 0);
        }

        vm.nextImage = function($event) {
            stopPropagation($event);
            vm.runAnimationLeft = false;
            vm.runAnimationRight = false;
            setTimeout(() => {
                if (vm.planViewIndex < vm.planViews.length - 1) {
                    vm.planViewIndex++;
                }
                vm.runAnimationLeft = true;
                $rootScope.$apply();
            }, 0);
        }

        function stopPropagation(event) {
            if(event && event.stopPropagation)
            event.stopPropagation();
        }

        vm.open3dViewerModal = function (event) {
            event.stopPropagation();
            var modalScope = $rootScope.$new();
            modalScope.floorplannerLink = vm.floorplannerLink;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-3d-viewer-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
        }

        vm.openSwitcherModal = function (event) {
            event.stopPropagation();
            var modalScope = $rootScope.$new();
            modalScope.planViews = vm.planViews;
            modalScope.currentIndex = vm.planViewIndex;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-switcher-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
        }

    }

})();