﻿
using NetTopologySuite.IO;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.SqlServer.Types;
using System.Data.SqlTypes;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Data.Sqlite;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts data from a geopackage URL and stores in the given SQL table.
    /// The Bush Fire Prone datasets are NOT updated frequently (As in, at this point, it has not been updated
    /// in nearly 2 years). Therefore we do not go to the effort of keeping 2 tables or running this as a 
    /// background process like we do with some of the other datasets (e.g. Tenure).
    /// </summary>
    public class BushFireProneExtractor
    {
        private SqlConnection connection;
        private string directory;
        private string datasetUrl;
        private Logger logger;
        private SqliteConnection sqliteConnection;

        private const string BUSHFIRE_TABLE = "RSS_BushFireProneData";
        private const int SPATIAL_REFERENCE_IDENTIFIER = 4326;
        
        private const int CHUNK_SIZE = 5000;

        public BushFireProneExtractor(string directory, string datasetUrl, string connectionString, LogCallback logCallback = null)
        {
            this.directory = directory;
            this.datasetUrl = datasetUrl;

            connection = new SqlConnection(connectionString);
            connection.Open();

            logger = new Logger(logCallback);

            // This is required to work with the SqlGeography types.
            SqlServerTypes.Utilities.LoadNativeAssemblies(AppDomain.CurrentDomain.BaseDirectory);
            SQLitePCL.Batteries_V2.Init();
        }

        public void Dispose()
        {
            if(connection != null)
            {
                connection.Close();
                connection.Dispose();
            }
        }

        /// <summary>
        /// Run the entire dbf -> SQL extraction process. Handles dropping and creating tables, and deleting duplicate entries.
        /// 
        /// Caller must still dispose of the BushfireExtractor.
        /// </summary>
        public async Task<string> RunFullProcess()
        {
            try
            {
    
                var dl = new Downloader(
                    directory,
                    (s) => {
                        logger.Log(s);
                    }
                );

                var token = await dl.GetOAuthToken();
                
                dl.FinalCleanup();
                await dl.DownloadDataset(token, this.datasetUrl);
                Downloader.CleanDirectory(this.directory, new string[] { ".zip" });
                dl.Unzip();
                dl.DeleteUnnecessaryFiles();
                
                this.DropExtractionTable();
                this.CreateExtractionTable();
                this.Extract();
                logger.Log("Full extraction of new Bush Fire Prone dataset completed without errors.");
                return "Dataset Processed Successfully.";

                
            }
            catch (Exception e)
            {
                throw e;
            }      
        }

        /// <summary>
        /// Drops the extraction table if it already exists. Otherwise does nothing.
        /// </summary>
        public void DropExtractionTable()
        {
            // Note: For now I am just dropping the entire table - this makes it easier in future if Alistair asks for more 
            // info to be extracted or w/e from the DB we don't have to worry about schema updates and so on - we simply modify
            // the creation method and voila.
            try
            {
                SqlCommand cmd = new SqlCommand($"DROP TABLE dbo.{BUSHFIRE_TABLE};", connection);
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                logger.Log($"'DROP TABLE dbo.{BUSHFIRE_TABLE}' failed with {ex}");
            }
        }

        /// <summary>
        /// Creates the initial table within the database.
        /// </summary>
        public void CreateExtractionTable()
        {
            // Create address table (Derived from .dbf file)
            SqlCommand cmd = new SqlCommand($@"
                CREATE TABLE dbo.{BUSHFIRE_TABLE}(

                    -- Main data inc geometry
	                BushFireProneDataId int IDENTITY(1, 1) PRIMARY KEY NOT NULL,	-- This is our internal ID
	                Boundary	        geography,                                  -- Special geography column
    
                    -- Incase we ever want the raw data in human readable format.
                    -- NOTE: Since this is a computed column we can remove this here
                    -- without updating our DataTable code elsewhere (But beware other areas in th codebase...)
                    BoundaryString      AS Boundary.STAsText(),                     

                    -- Misc. data that might be useful at some point
                    NumberOfGeometries int,
                    LGA                nvarchar(100),
                    Designation        nvarchar(200),
                    DesignationDate    datetime NOT NULL
                    
                );", connection);

            cmd.ExecuteNonQuery();
        }
        
        private void CreateLandIdIndex(string table, SqliteConnection connection)
        {
            SqliteCommand c = new SqliteCommand($"CREATE INDEX IF NOT EXISTS main.idx_id ON {table}(id)", connection);
            c.ExecuteNonQuery();
        }

        /// <summary>
        /// Extract and parse the given dbfFiles. Can optionally only parse a given number (Used for testing only);
        /// </summary>
        /// <param name="totalCount">OPTIONAL: Limit total rows to count for testing. Mainly for debugging.</param>
        public void Extract()
        {

            logger.Log($"BushFireProne Extraction started to table: {BUSHFIRE_TABLE}");
            
            // Find all related .dbf and .shp files within the given folder.
            string[] files = Directory.GetFiles(directory);

            var geopackagePath = files.FirstOrDefault(x => x.EndsWith(".gpkg"));
            
            if (string.IsNullOrEmpty(geopackagePath))
            {
                logger.Log("No localities dataset found. Continuing to next process without updating suburbs...");
                return;
            }

            // Define our bulk insert data
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("BushFireProneDataId", typeof(int));
            dataTable.Columns.Add("Boundary", typeof(SqlGeography));
            dataTable.Columns.Add("NumberOfGeometries", typeof(int));
            dataTable.Columns.Add("LGA", typeof(string));
            dataTable.Columns.Add("Designation", typeof(string));
            dataTable.Columns.Add("DesignationDate", typeof(DateTime));

            // Create object of SqlBulkCopy which help to insert  
            SqlBulkCopy bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.BulkCopyTimeout = 180; // 3 Minutes

            // Map our data table to our DB table
            bulkCopy.DestinationTableName = $"dbo.{BUSHFIRE_TABLE}";
            bulkCopy.ColumnMappings.Add("BushFireProneDataId", "BushFireProneDataId");
            bulkCopy.ColumnMappings.Add("Boundary", "Boundary");
            bulkCopy.ColumnMappings.Add("NumberOfGeometries", "NumberOfGeometries");
            bulkCopy.ColumnMappings.Add("LGA", "LGA");
            bulkCopy.ColumnMappings.Add("Designation", "Designation");
            bulkCopy.ColumnMappings.Add("DesignationDate", "DesignationDate");

            
            dataTable.Clear(); // Clear previous geojson rows in table.
            var tempIndex = 0;
            
            this.sqliteConnection = new SqliteConnection("Filename=" + geopackagePath);
            this.sqliteConnection.Open();

            // Programmatically select table name in the hope this stays constant enough in future to support updates.
            var tableSelectCmd =
                new SqliteCommand(
                    "SELECT tbl_name FROM SQLITE_SCHEMA WHERE type = 'table' AND name LIKE 'Bush_Fire_Prone%' Limit 1;",
                    sqliteConnection);

            var tableName = tableSelectCmd.ExecuteScalar().ToString();
            
            CreateLandIdIndex(tableName, this.sqliteConnection);
            
            for(int r = 0; r < int.MaxValue; r++)
            {
                var selectRowCmd = new SqliteCommand(
                    $"SELECT * FROM {tableName} ORDER BY id LIMIT {CHUNK_SIZE} OFFSET {r * CHUNK_SIZE}",
                    sqliteConnection);
                
                var reader = selectRowCmd.ExecuteReader();

                logger.Log($"Processing bushfire prone dataset rows {r * CHUNK_SIZE}->{ r * CHUNK_SIZE + CHUNK_SIZE} for Bush_Fire_Prone_Areas_2021_OBRM_019");

                if (reader.HasRows == false)
                    break; // Break out of outer loop.

                while (reader.Read())
                {
                    SqlGeography boundary = null;
                        
                    try
                    {
                        boundary     = Shared.GetSqliteGeometryAsSqlBoundary(reader, true, true);
                    }
                    catch (Exception e)
                    {
                        continue; // Invalid data for this row.
                    }
                 
                    DataRow dr = dataTable.NewRow();
                    dr["BushFireProneDataId"] = reader["id"];
                    dr["Boundary"] = boundary;
                    dr["NumberOfGeometries"] = boundary.NumRings().IsNull ? 1 : boundary.NumRings().Value;
                    dr["LGA"] = reader["lga"];
                    dr["Designation"] = reader["designation"];
                    dr["DesignationDate"] = reader["designationdate"];

                    dataTable.Rows.Add(dr);
                }
            }


            // Bulk write data to sql server.
            try
            {
                bulkCopy.WriteToServer(dataTable);
            } 
            catch (Exception e)
            {
                logger.Log($"Exception occured while writing to SQL table. Exception: {e.Message}. Stack: {e.StackTrace}");                 
            }
            
            logger.Log($"Finished row extraction and insertion. Creating indexes.");

            // Create custom spatial index. I think it uses a quadtree behind the scenes.
            var createIndexCmd = new SqlCommand(
                $@" CREATE SPATIAL INDEX si_RSS_BushDireProneData_Boundary
                    ON dbo.{BUSHFIRE_TABLE}(Boundary);",
                connection);

            createIndexCmd.CommandTimeout = 500; // seconds
            createIndexCmd.ExecuteNonQuery();
        }

    }

}
