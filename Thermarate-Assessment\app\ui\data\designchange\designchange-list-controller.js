(function () {
    'use strict';
    const controllerId = 'DesignChangeListCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog',
            'designchangeservice', 'daterangehelper', designChangeListController]);
    function designChangeListController($rootScope, $scope, $mdDialog,
        designchangeservice, daterangehelper) {

        // The model for this form
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Design Changes';
        vm.designChangeList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'title',
                    description: 'Title',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        var persistRangeName = "designChangeList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createDesignChange = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/designChange/designChange-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "title", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            designchangeservice.getListCancel();
            designchangeservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = designchangeservice.currentFilter();
                    vm.designChangeList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.designChangeList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createDesignChange,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();

        vm.delete = async function (row) {
            await designchangeservice
                .deleteDesignChange(row.designChangeId);

            row.deleted = true;
            vm.designChangeList = vm.designChangeList.filter(x => x.designChangeId != row.designChangeId);
        }

        vm.bulkSelect = function (state) {
            vm.designChangeList.forEach(x => x.isBulkSelected = state);
        }

        vm.bulkSelectionsExist = function () {
            return vm.designChangeList.some(x => x.isBulkSelected);
        }

        vm.bulkDelete = async function () {

            let toDelete = vm.designChangeList.filter(x => x.isBulkSelected);
            for (let i = 0; i < toDelete.length; i++) {
                await vm.delete(toDelete[i]);
            }
        }

        vm.clone = function (row) {

            designchangeservice
                .copyDesignChange(row.designChangeId)
                .then((id) => {

                    designchangeservice
                        .getDesignChange(id)
                        .then(designChange => {

                            // Add returned template to list.
                            vm.designChangeList.push(designChange);

                            // Ideally sort based on what we're actually sorting by, but since
                            // we basically only have the Template Name to go off...
                            vm.designChangeList = vm.designChangeList.sort((a, b) => (a.title > b.title) ? 1 : -1);
                        });

                });
        }

    }
})();