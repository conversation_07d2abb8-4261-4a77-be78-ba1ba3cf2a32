(function () {
    'use strict';
    angular
        .module('app')
        .component('sortListDropdown', {
            bindings: {
                sortByOptions: '<',
                sortBySelection: '=',
                defaultSort: '<',
                defaultSortDir: '<',
                applySort: '&'
            },
            templateUrl: 'app/ui/energy-labs/sort-list-dropdown.html',
            controller: SortListDropdownController,
            controllerAs: 'vm'
        });

    SortListDropdownController.$inject = ['$scope'];

    function SortListDropdownController($scope) {
        var vm = this;

        if (vm.defaultSort != null && vm.defaultSort != '') {
            vm.currentSort = vm.sortByOptions.filter(x => x.code == vm.defaultSort)[0];
        } else {
            vm.currentSort = vm.sortByOptions[0];
        }
        vm.sortByDir = vm.defaultSortDir != null && vm.defaultSortDir.length > 0 ? vm.defaultSortDir : "asc";

        vm.setAscending = function () {
            vm.sortByDir = 'asc';
            vm.triggerApplySort();
        }

        vm.setDescending = function () {
            vm.sortByDir = 'desc';
            vm.triggerApplySort();
        }

        vm.triggerApplySort = function () {
            vm.sortBySelection['sortBy'] = [{ field: vm.currentSort.code, dir: vm.sortByDir }];
            vm.applySort();
        }
    }
})();
