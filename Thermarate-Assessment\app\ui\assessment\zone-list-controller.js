(function () {

    'use strict';
    angular
        .module('app')
        .component('zoneList', {
            bindings: {
                source: '<',                    // Object which contains the 'Zones', NOT the Zones themself.
                sourceType: '<',                // 'proposed' or 'reference'
                complianceOption: '<',          // Must be present
                isTemplate: '<',                // If true, certain inputs are hidden.
                disabled: '<',                  // Disable all inputs?
                storeyFilter: '<',              // Optional number. If present, only zones on the given storey will be shown.
                constructionFilter: '<',        // Optional GUID. If present, only zones CONTAINING the given construction item will be shown.
                enableTemplateSelection: '<',   // If true, shows a template selector. (Note: Unsure atm if modificaitons to zones should be disabled if a template is selected...)
                preventUpdateOnLoad: '<',       // If TRUE, prevents our source form being updated with the latest building zone data (applies in Assesment page, for instance)
                baselineOption: '<',            // For copying.
                complianceOptions: '<',      // ugh
            },
            templateUrl: 'app/ui/assessment/zone-list.html',
            controller: ZoneList,
            controllerAs: 'vm'
        });

    ZoneList.$inject = ['common', '$q', 'zonetypeservice', 'uuid4',
        'zoneservice', '$mdDialog', '$rootScope', '$scope', 'coreLoop',
        'buildingdesigntemplateservice', 'constructionservice','projectdescriptionservice',
        'assessmentcomplianceoptionservice'];

    function ZoneList(common, $q, zonetypeservice, uuid4, zoneservice, $mdDialog, $rootScope, $scope, coreLoop,
        buildingdesigntemplateservice,
        constructionservice, projectdescriptionservice, assessmentcomplianceoptionservice) {

        var vm = this;

        vm.zoneTypeList = [];
        vm.buildingZoneTemplates = [];

        // Cached variables for functions that were previously called directly in HTML
        vm.cachedInteriorZones = [];
        vm.cachedRoofSpaceZones = [];
        vm.cachedSubfloorSpaceZones = [];
        vm.cachedGroundSurfaceZones = [];
        vm.cachedInteriorZoneActivityList = [];
        vm.cachedRoofZoneActivityList = [];
        vm.cachedSubfloorZoneActivityList = [];
        vm.cachedInteriorZoneTypeList = [];
        vm.cachedExteriorZoneTypeList = [];
        vm.cachedInteriorNccClassificationList = [];
        vm.cachedExteriorNccClassificationList = [];
        vm.cachedRoofAirCavityList = [];
        vm.cachedSubfloorAirCavityList = [];
        vm.cachedAvailableInteriorNccClassificationList = [];

        // List of functions we want to expose.
        vm.clearAllZonesIfRequired = clearAllZonesIfRequired;
        vm.addZone = addZone;
        vm.removeZone = removeZone;
        vm.removeZones = removeZones;
        vm.clearZone = clearZone;
        vm.cloneZone = cloneZone;
        vm.zoneActivityChanged = zoneActivityChanged
        vm.zoneTypeChanged = zoneTypeChanged;
        vm.determineClassification = determineClassification;
        vm.calculateAllowance = calculateAllowance;
        vm.toggleBulkEditAll = toggleBulkEditAll;
        vm.showBulkEdit = showBulkEdit;
        vm.sortBy = sortBy;
        vm.formHasErrors = formHasErrors;
        vm.matchCeilingAreaToFloorArea = matchCeilingAreaToFloorArea;

        vm.sortCol = null;  // Column name of object to sort by (used for indexing)
        vm.sortDir = null;  // Direction (ASC or DESC).

        /**
         * Updates all cached variables that were previously function calls in the HTML.
         * This should be called on initialization and whenever relevant data changes.
         */
        function updateCachedVariables() {
            // Update zone lists
            vm.cachedInteriorZones = vm.interiorZones();
            vm.cachedRoofSpaceZones = vm.roofSpaceZones();
            vm.cachedSubfloorSpaceZones = vm.subfloorSpaceZones();
            vm.cachedGroundSurfaceZones = vm.groundSurfaceZones();

            // Update activity lists
            vm.cachedInteriorZoneActivityList = vm.interiorZoneActivityList();
            vm.cachedRoofZoneActivityList = vm.roofZoneActivityList();
            vm.cachedSubfloorZoneActivityList = vm.subfloorZoneActivityList();

            // Update zone type lists
            vm.cachedInteriorZoneTypeList = vm.interiorZoneTypeList();
            vm.cachedExteriorZoneTypeList = vm.exteriorZoneTypeList();

            // Update classification lists
            vm.cachedInteriorNccClassificationList = vm.interiorNccClassificationList();
            vm.cachedExteriorNccClassificationList = vm.exteriorNccClassificationList();
            vm.cachedAvailableInteriorNccClassificationList = vm.availableInteriorNccClassificationList();

            // Update air cavity lists
            vm.cachedRoofAirCavityList = vm.roofAirCavityList();
            vm.cachedSubfloorAirCavityList = vm.subfloorAirCavityList();
        }

        /** Retrieve and initialize any required data here, not just floating around the controller. */
        function initialize() {

            // Retreive our list of available ZoneTypes.
            zonetypeservice.getList()
                .then(function (data) {
                    vm.zoneTypeList = data.data;

                    // Add a default row if none exist
                    if (vm.source.zones == undefined) {
                        vm.source.zones = [];
                    }

                    // Update cached variables after zone types are loaded
                    updateCachedVariables();
                });

            zoneservice.getZoneActivityList()
                .then((data) => {
                    vm.zoneActivityList = data;

                    // Update cached variables after zone activities are loaded
                    updateCachedVariables();
                });

            zoneservice.getNccClassificationList()
                .then((data) => {
                    vm.nccClassificationList = data;

                    // Update cached variables after NCC classifications are loaded
                    updateCachedVariables();
                });

            constructionservice.getAirCavityList()
                .then(data => {
                    vm.airCavityList = data;

                    // Update cached variables after air cavities are loaded
                    updateCachedVariables();
                });

            // Load required data for inputs ----------------------------------------------------------------------

            // Load all available design templates.
            let zoneTemplatesPromise = buildingdesigntemplateservice
                .getAll()
                .then((list) => {

                    vm.buildingZoneTemplates = list;

                    // If we have a current building zone  template, apply it. This way, updates to the template are applied
                    // retroactively.
                    if (vm.source.buildingZonesTemplateId != null &&
                        vm.source.buildingZonesTemplateId != "BLANK_TEMPLATE") {

                        let filtered = vm.buildingZoneTemplates.filter(x => x.buildingDesignTemplateId == vm.source.buildingZonesTemplateId);

                        if (filtered != null && filtered.length > 0 && vm.preventUpdateOnLoad == false) {
                            vm.applyZonesTemplate(filtered[0]);
                        }
                    }

                    // Update cached variables after templates are loaded
                    updateCachedVariables();
                });

            // Brrrrr
            setInterval(() => {
                coreLoop.computeFloorTotals(vm.source.storeys, vm.cachedInteriorZones);
                coreLoop.computeFloorAreas(vm.source);
                coreLoop.computeGlassToExteriorWallRatios(vm.source);

                // Update cached variables periodically
                updateCachedVariables();
            }, 1000);
        }

        vm.interiorZones = () => {
            const sortOn = vm.sortedInteriorZones != null
                            ? vm.sortedInteriorZones
                            : vm.source.zones;
            return zoneservice.interiorZones(sortOn);
        }

        vm.roofSpaceZones = () => vm.sortedRoofSpaceZones != null
            ? vm.sortedRoofSpaceZones
            : vm.source?.zones?.filter(x => x.zoneActivity?.zoneActivityCode === "ZARoofSpace");

        vm.subfloorSpaceZones = () => vm.sortedSubfloorSpaceZones != null
            ? vm.sortedSubfloorSpaceZones
            : vm.source?.zones?.filter(x => x.zoneActivity?.zoneActivityCode === "ZASubfloorSpace");

        vm.groundSurfaceZones = () => vm.sortedGroundSurfaceZones != null
            ? vm.sortedGroundSurfaceZones
            : vm.source?.zones?.filter(x => x.zoneActivity?.zoneActivityCode === "ZAGroundSurface");

        vm.interiorZoneActivityList = () => vm.zoneActivityList?.filter(x => x.availableFor === 'interior');
        vm.roofZoneActivityList = () => vm.zoneActivityList?.filter(x => x.availableFor === 'roof');
        vm.subfloorZoneActivityList = () => vm.zoneActivityList?.filter(x => x.availableFor === 'subfloor');

        vm.interiorZoneTypeList = () => vm.zoneTypeList?.filter(x => x.availableFor === 'interior' || x.availableFor === 'all');
        vm.exteriorZoneTypeList = () => vm.zoneTypeList?.filter(x => x.availableFor === 'exterior' || x.availableFor === 'all');
        vm.interiorNccClassificationList = () => vm.nccClassificationList?.filter(x => x.availableFor !== 'exterior');
        vm.exteriorNccClassificationList = () => vm.nccClassificationList?.filter(x => x.availableFor === 'exterior' || x.availableFor === 'all');

        vm.roofAirCavityList = () => vm.airCavityList?.filter(x => x.type === "Roof Space Ventilation");
        vm.subfloorAirCavityList = () => vm.airCavityList?.filter(x => x.type === "Subfloor Ventilation" && x.airCavityCode !== "SubfloorConnectedToGround");

        /**
         * Narrows down our selectable NCC Classification zones so that the first 4
         * (i.e. Class 1a, Class 1b, Class 2 (SOU) and Class 4) are mutually exclusive
         * i.e. only one of these 4 values can be selected by the user.
         *
         * For example, if the user sets the first row as NCC Classification = Class 1a,
         * when they go to select the value in the second row the only options are Class 1a
         * or Attached Class 10a i.e. Class 1b, Class 2 (SOU) and Class 4 are unavailable.
         * The only way to select a different value would be if the user deleted all rows with NCC Classification = Class 1a.
         *
         * Note that �Attached Class 10a� can be selected regardless of the other values i.e. this value can always be �bolted on�
         */
        vm.availableInteriorNccClassificationList = function () {

            let interiorZones = vm.interiorZones();
            let allClassifications = vm.interiorNccClassificationList();

            if (interiorZones == null || interiorZones.length === 0) {
                return allClassifications;
            }

            let class10A = allClassifications?.find(x => x.nccClassificationCode === 'Class10A');
            if (class10A == null)
                return allClassifications;

            // Find the first interior zone which is NOT Class10a, check its NCC classification type, and limit based on that.
            let limiting = interiorZones.filter(x => x.nccClassification?.nccClassificationCode !== class10A.nccClassificationCode);

            // If we found no limiting zones, return all options
            if (limiting == null || limiting.length === 0) {
                return allClassifications;
            }

            return [limiting[0].nccClassification, class10A];

        }

        /** zoneList must match a ZoneActivityCode without the preceeding "ZA" */
        function sortBy(zoneList, column) {

            if(vm[zoneList + "SortInfo"] == null)
                vm[zoneList + "SortInfo"]  = new SortInfo();

            const matchingZones = zoneList === 'Interior'
                ? zoneservice.interiorZones(vm.source.zones)
                : vm.source.zones.filter(x => x.zoneActivity != null &&
                                              x.zoneActivity.zoneActivityCode === `ZA${zoneList}`);

            common.setSort(column, vm[zoneList + "SortInfo"]);
            vm['sorted' + zoneList + 'Zones'] = common.applySort(matchingZones, vm[zoneList + "SortInfo"]);

            // Update cached variables
            updateCachedVariables();
        }

        /**
         * Clears all rows if required (otherwise doesn't touch them).
         * Generally fired when de/selecting the "Not Required" checkbox.
         *
         * @param {Boolean} required Indicates whether we should be clearing everything or not.
         */
        function clearAllZonesIfRequired(required) {

            if (!required) {
                return; // 'Not required' refers to 'not required to clear array' NOT 'array is not required' (good thing this is unambiguous)
            } else {
                vm.source.zones = [];
                vm.source.buildingZonesTemplateId = null; // Nullify template.
            }

            // Re-determine classification.
            vm.source.projectClassification = zonetypeservice
                .determineProjectClassificationBasedOnZones(vm.source.zones);

            // Update cached variables
            updateCachedVariables();
        }

        function renumberZones() {
            if (vm.sortCol == null && vm.sortDir == null) {

                for (let i = 0; i < vm.source.zones.length; i++) {
                    vm.source.zones[i].sortOrder = i;
                }
            }
        }

        vm.renumberZones = renumberZones;

        function addZone(zones, activityCode, leadChar) {

            if (vm.source.zones == null) {
                vm.source.zones = [];
            }

            let zx = vm.zoneActivityList?.filter(x => x.zoneActivityCode === activityCode)[0];

            vm.source.zones.push({
                zoneId: uuid4.generate(),
                linkId: uuid4.generate(),
                floorArea: null,
                zoneNumber: determineNextFreeZoneNumber(leadChar),
                zoneNumberSource: "AUTO",
                zoneActivity: zx,
                naturallyVentilated: null
            });

            renumberZones();

            vm.sortedInteriorZones      = null;
            vm.sortedRoofSpaceZones     = null;
            vm.sortedSubfloorSpaceZones = null;
            vm.sortedGroundSurfaceZones = null;

            vm.sortedInteriorZones = common.applySort(vm.interiorZones(), vm.InteriorSortInfo);
            vm.sortedRoofSpaceZones = common.applySort(vm.roofSpaceZones(), vm.RoofSpaceSortInfo);
            vm.sortedSubfloorSpaceZones = common.applySort(vm.subfloorSpaceZones(), vm.SubfloorSpaceSortInfo);
            vm.sortedGroundSurfaceZones = common.applySort(vm.groundSurfaceZones(), vm.GroundSurfaceSortInfo);

            // Update cached variables
            updateCachedVariables();
        }

        function determineLeadCharFromZone(z) {

            if(zoneservice.isInterior(z))
                return "Z";
            if(z.zoneActivity?.zoneActivityCode === "ZARoofSpace")
                return "R";
            if(z.zoneActivity?.zoneActivityCode === "ZASubfloorSpace")
                return "S";
            if(z.zoneActivity?.zoneActivityCode === "ZAGroundSurface")
                return "G"

            return "Z";
        }

        /** Clones the given building floor row and inserts it directly below the given row. */
        function cloneZone(row) {

            if (row == null)
                return;

            const leadChar = determineLeadCharFromZone(row);

            let index = vm.source.zones.findIndex(x => x === row); // Original index

            let n = angular.copy(row);
            n.zoneId = uuid4.generate();
            n.zoneNumber = determineNextFreeZoneNumber(leadChar);
            n.zoneNumberSource = "AUTO";
            n.zoneDescription = (row.zoneDescription ?? "") + " - Copy";

            vm.source.zones.splice(index + 1, 0, n);
            renumberZones();

            vm.sortedInteriorZones      = null;
            vm.sortedRoofSpaceZones     = null;
            vm.sortedSubfloorSpaceZones = null;
            vm.sortedGroundSurfaceZones = null;

            vm.sortedInteriorZones = common.applySort(vm.interiorZones(), vm.InteriorSortInfo);
            vm.sortedRoofSpaceZones = common.applySort(vm.roofSpaceZones(), vm.RoofSpaceSortInfo);
            vm.sortedSubfloorSpaceZones = common.applySort(vm.subfloorSpaceZones(), vm.SubfloorSpaceSortInfo);
            vm.sortedGroundSurfaceZones = common.applySort(vm.groundSurfaceZones(), vm.GroundSurfaceSortInfo);

            // Update cached variables
            updateCachedVariables();

        }

        /** Nullifies (but does not delete) the given Zone/Row. */
        function clearZone(zone, keepZoneActivity) {

            // NOTE: Doesn't nullify Zone Number on purpose (for now...)
            zone.zoneNumber = null;
            zone.zoneDescription = null;
            zone.zoneType = null;
            zone.floorArea = null;
            zone.volume = null;
            zone.storey = null;
            zone.zoneNumberSource = "MANUAL";
            zone.nccClassification = null

            if (!keepZoneActivity)
                zone.zoneActivity = null;

            zone.conditioned = null;
            zone.roofLight = null;
            zone.ceilingFan = null;
            zone.evaporativeCooler = null;

            vm.calculateAllowance(zone);

            // Re-determine classification.
            vm.source.projectClassification = zonetypeservice
                .determineProjectClassificationBasedOnZones(vm.source.zones);

            // Update cached variables
            updateCachedVariables();
        }

        /** Removes the given row completely and re-names other rows. */
        function removeZone(row) {

            for (var ii = 0, ilen = vm.source.zones.length; ii < ilen; ii++) {
                if (vm.source.zones[ii].zoneId == row.zoneId) {
                    vm.source.zones.splice(ii, 1);
                    renumberZones();

                    // Re-determine classification.
                    vm.source.projectClassification = zonetypeservice
                        .determineProjectClassificationBasedOnZones(vm.source.zones);

                    vm.sortedInteriorZones      = null;
                    vm.sortedRoofSpaceZones     = null;
                    vm.sortedSubfloorSpaceZones = null;
                    vm.sortedGroundSurfaceZones = null;

                    vm.sortedInteriorZones = common.applySort(vm.interiorZones(), vm.InteriorSortInfo);
                    vm.sortedRoofSpaceZones = common.applySort(vm.roofSpaceZones(), vm.RoofSpaceSortInfo);
                    vm.sortedSubfloorSpaceZones = common.applySort(vm.subfloorSpaceZones(), vm.SubfloorSpaceSortInfo);
                    vm.sortedGroundSurfaceZones = common.applySort(vm.groundSurfaceZones(), vm.GroundSurfaceSortInfo);

                    // Update cached variables
                    updateCachedVariables();

                    return;
                }
            }

        }

        function removeZones(zones) {
            zones.forEach(zone => {
                removeZone(zone);
            });
        }

        function formHasErrors() {
            return !vm.isTemplate && ( $scope['ZoneListForm' + vm.complianceOption.optionIndex + vm.sourceType]?.$invalid == true ||
                (vm.complianceOption != null && vm.source.zones.length == 0))
        }

        function zoneActivityChanged(zoneActivity, zone) {

            if (zoneActivity == null || zone == null)
                return;

            let foundZones = vm.zoneTypeList?.filter(s => s.zoneTypeCode == zoneActivity.defaultZoneTypeCode);
            if (foundZones != null && foundZones.length > 0) {

                let foundZoneType = foundZones[0];

                zone.zoneType = foundZoneType;
                let foundNccClassifications = vm.nccClassificationList?.filter(s => s.nccClassificationCode == foundZoneType.defaultNccClassificationCode);

                if (foundNccClassifications != null && foundNccClassifications.length > 0)
                    zone.nccClassification = foundNccClassifications[0];
            }

            zone.conditioned = zoneActivity.defaultConditioned;

            zoneTypeChanged(zone.zoneType, zone);
        }

        /**
         * Called when space type of the zone is changed, assigns values to the row
         * depending on what type is is.
         */
        function zoneTypeChanged(zoneType, zone, autoselectNccAndConditioned) {

            if (zoneType) {

                // Assigned zonetype to actual lighting row.
                zone.zoneType = zoneType;
                zone.zoneTypeCode = zoneType.zoneTypeCode;

                zone.lampPowerMaximumWM2 = zoneType.lampPowerMaximumWM2;

                if (autoselectNccAndConditioned === true) {
                    let foundNccClassifications = vm.nccClassificationList?.filter(s => s.nccClassificationCode == zoneType.defaultNccClassificationCode);

                    if (foundNccClassifications != null && foundNccClassifications.length > 0)
                        zone.nccClassification = foundNccClassifications[0];

                    zone.conditioned = false;
                }

            } else {

                zone.lampPowerMaximumWM2 = null;

                // Nullify zonetype.
                zone.zoneType = null;
                zone.zoneTypeCode = null;
            }

            vm.calculateAllowance(zone);

            // Re-determine classification.
            setTimeout(() => {
                vm.source.projectClassification = zonetypeservice
                    .determineProjectClassificationBasedOnZones(vm.source.zones);

                // Update cached variables
                updateCachedVariables();
            }, 100);
        }

        vm.onNccClassificationUpdate = function (item) {

            setTimeout(() => {
                vm.calculateAllowance(item);
                vm.source.projectClassification = zonetypeservice
                    .determineProjectClassificationBasedOnZones(vm.source.zones);

                // Update cached variables
                updateCachedVariables();
            }, 100);

        }

        /** Fired in case the user enters a name exactly without clicking. */
        function determineClassification(item) {

            vm.calculateAllowance(item);

            // Re-determine classification.
            vm.source.projectClassification = zonetypeservice
                .determineProjectClassificationBasedOnZones(vm.source.zones);

            // Update cached variables
            updateCachedVariables();
        }

        /**
         *  Calculate Lamp Power Load Allowance for given row and also batch calculates
         *  building zone requirements for... everything.
         */
        function calculateAllowance(item) {

            if (item.floorArea == undefined || item.floorArea == null || item.lampPowerMaximumWM2 == undefined || item.lampPowerMaximumWM2 == null) {
                // We could enter this state if items were loaded via scratch file. So in this case, we want to
                // actually determine the lampPowerMaximumWM2 if possible (it's based on the zoneType)
                if (item.zoneType == null || item.zoneType.lampPowerMaximumWM2 == null)
                    return;
                else
                    item.lampPowerMaximumWM2 = item.zoneType.lampPowerMaximumWM2;
            }

            item.lampPowerMaximumW = item.floorArea * item.lampPowerMaximumWM2;
            // Truncate to 1 decimal place
            var maxAllowance = String(item.lampPowerMaximumW);
            var split = maxAllowance.split('.');
            if (split.length == 2) {
                var decimal = split[1];
                if (decimal.length > 1) {
                    decimal = decimal.substring(0, 1);
                    maxAllowance = split[0] + "." + decimal;
                    item.lampPowerMaximumW = Number(maxAllowance);
                }
            }
        }

        function matchCeilingAreaToFloorArea(item) {
            item.ceilingArea = item.floorArea;
            common.forceBlurInputWithId("CeilingAreaInput" + item.zoneId)
        }
        /**
         * Toggles bulk edit selection on/off for ALL rows.
         *
         * @param {any} toggleState If true, everything will be selected. False, everything deselected.
         * @param {any} code Exterior and Interior zones have a different property tracking if they have been selected. This is to select which.
         */
        function toggleBulkEditAll(toggleState, code, zones) {

            zones.forEach(row => {
                row[code] = toggleState;
            });
        }

        function showBulkEdit(code, bulkCode, zoneList, leadChar, title) {

            var newScope = $rootScope.$new();
            newScope.zoneTypeList = vm.zoneTypeList;
            newScope.zoneActivityList = vm.zoneActivityList;
            newScope.nccClassificationList = vm.nccClassificationList;
            newScope.ventilationList = vm.airCavityList;
            newScope.storeys = vm.source.storeys;
            newScope.isExterior = (code == 'selectedForExteriorBulkEdit');
            newScope.currentZones = vm.source?.zones;
            newScope.code = code;
            newScope.title = title + ' Bulk Edit';

            $mdDialog.show({
                templateUrl: 'app/ui/assessment/building-zones-bulk-edit.html',
                scope: newScope,
            })
                .then(function (response) {

                    // Loop over all Zones and apply the new data from our bulk edit modal where appropriate
                    for (let i = 0; i < vm.source.zones.length; i++) {

                        if (vm.source.zones[i][code] == null ||
                            vm.source.zones[i][code] == false)
                            continue;

                        vm.source.zones[i][code] = false;

                        if (response.bulkEditAction == 'EDIT')
                            vm.source.zones[i] = common.nullAwareMerge(vm.source.zones[i], response);
                        else if (response.bulkEditAction == 'CLEAR')
                            clearZone(vm.source.zones[i]);
                        else if (response.bulkEditAction == 'DELETE') {
                            removeZone(vm.source.zones[i]);
                            i--;
                        } else if (response.bulkEditAction == 'COPY') {
                            cloneZone(vm.source.zones[i], leadChar);
                            i++;
                        }
                    }

                    vm[bulkCode] = false;

                }, function () {
                    // Cancelled, do nothing.
                });
        }

        vm.noneSelected = function (code) {
            return !vm.source?.zones?.some(x => x[code]);
        }

        /** Determines which is the next free zone number based on (existing zones, their number). */
        function determineNextFreeZoneNumber(leadChar) {

            leadChar = leadChar.toUpperCase();

            // Get all existing zones that match the format Z### (Regardless of SOURCE).
            let pattern = `${leadChar}(\\d{3})`
            let regEx = new RegExp(pattern);

            let matches = [];
            let existingZones = vm.source?.zones;

            existingZones.forEach(zone => {
                let matching = regEx.exec(zone.zoneNumber);

                // We add a check for length so that things like "ZZ0044", "Z002z" etc don't match.
                if (matching != null && matching.input.length == 4)
                    matches.push(zone.zoneNumber);
            });

            // Convert to actual numbers (I.e. Z001 => 1 etc);
            let converted = [];
            matches.forEach(zn => {
                converted.push(parseInt(zn.slice(1)));
            });

            // Loop over until we find a free slot.
            for (let i = 1; i < 10000; i++) {
                if (converted.some(c => c == i))
                    continue;
                else {
                    return leadChar + common.addLeadingZero(i, 3);
                }
            }

            // Failure state.
            return "!###";
        }

        vm.updateSource = function (item, value, leadChar) {

            // If the value has been nullified, we wish to set the source back to 'AUTO'
            // and find the next available name for it.
            if (value == null || value == "" && item.zoneNumberSource != "AUTO") {

                item.zoneNumberSource = "AUTO";
                item.zoneNumber = determineNextFreeZoneNumber(leadChar);

            } else if (item.zoneNumberSource == "AUTO") {

                // If the value is NOT null, we set the source as 'MANUAL' which ensures
                // it will not be re-calculated.
                item.zoneNumberSource = "MANUAL";

                // I don't think it's appropriate to rename other zones here (even ones which have not
                // been set manually...)

            }
        }

        /**
         * Apply the given zone template to our source.
         * @param {any} template The template to apply.
         */
        vm.applyZonesTemplate = function (template) {

            // We now apply all "Design" data here as well.

            // TODO: Which of these are still required (if any?)
            // vm.source.projectClassification = template.projectClassification;
            // vm.source.projectDescription = template.projectDescription;
            // vm.source.projectDescriptionOther = template.projectDescriptionOther;
            // vm.source.design = template.design;
            // vm.source.buildingOrientation = template.buildingOrientation ?? vm.source.buildingOrientation; // Only nullify if template has orientation data, otherwise retain.

            vm.source.designFeatures = template.designFeatures;

            vm.source.designWasBlankFromTemplate = template.design == null;

            vm.source.storeys = template.storeys;

            vm.source.buildingZonesTemplateId = template.buildingDesignTemplateId ?? template.buildingZonesTemplateId;
            vm.source.zones = angular.copy(template.zones);
            vm.source.spaces = angular.copy(template.spaces);
            vm.source.zoneTypesNotApplicable = angular.copy(template.zoneTypesNotApplicable);

            // Assign new GUIDs and set created on date.
            vm.source.zones.forEach(z => {
                z.zoneId = uuid4.generate();
                z.createdOn = new Date().toUTCString();
                z.assessmentComplianceBuildingId = vm.source.assessmentComplianceBuildingId
            });

            vm.source.projectClassification = zonetypeservice
                .determineProjectClassificationBasedOnZones(vm.source.zones);

            // Update cached variables
            updateCachedVariables();
        }

        /** Nullify Zone data, generally used when selecting the "BLANK_TEMPLATE" */
        vm.nullifyZoneDefaults = function (id) {

            setTimeout(() => {

                // TODO: Which of these are still required (if any?)
                // vm.source.projectClassification = null;
                // vm.source.projectDescription = null;
                // vm.source.projectDescriptionOther = null;
                // vm.source.buildingOrientation = null;
                // vm.source.design = null;

                vm.source.designFeatures = {};
                vm.source.storeys = null;

                vm.source.zones = [];
                vm.source.buildingZonesTemplateId = id;

                vm.source.projectClassification = zonetypeservice
                    .determineProjectClassificationBasedOnZones(vm.source.zones);

                // Update cached variables
                updateCachedVariables();
            }, 151);

        }

        /**
         * Extended 'disabled' condition for inputs, which also ensures data isn't editable if
         * a template is selected (except for the special BLANK_TEMPLATE)
         */
        vm.disabledEx = function () {
            return vm.disabled;
        }

        vm.optionsNotThisOrBaseline = function (option) {
            return vm.complianceOptions
                ?.filter(x => x.optionIndex != 0 && x.optionIndex != option.optionIndex);
        }

        /**
         * We only show the 'copy baseline' button for options if we are currently viewing
         * the 'proposed' building OR we are viewing the 'reference building' AND their is
         * a reference building available to copy from in the baseline option.
         *
         * We determine if a reference building is 'available' based upon its compliance method.
         */
        vm.showCopyBaselineForOption = function (option) {
            return vm.sourceType == 'proposed' ||
                (vm.sourceType == 'reference' && hasReferenceBuilding(option.complianceMethod.complianceMethodCode));
        }

        function hasReferenceBuilding(complianceMethodCode) {
            return complianceMethodCode == "CMPerfSolutionDTS" ||
                complianceMethodCode == "CMPerfSolution";
        }

        /** Copies the given data to the given option.building from the given building */
        vm.copyZonesToFrom = function (toOption, toBuilding, fromBuilding, fromOption) {

            // USE TIMEOUTS TO AVOID WEIRD UI DISPLAY BUGS.
            setTimeout(() => {

                const zones = angular.copy(fromBuilding.zones);
                const storeys = angular.copy(fromBuilding.storeys);

                zones.forEach(z => {
                    z.zoneId = uuid4.generate();
                    z.createdOn = new Date().toUTCString();
                    z.assessmentComplianceBuildingId = toBuilding.assessmentComplianceBuildingId
                });

                toBuilding.zones = zones;
                toBuilding.storeys = storeys;
                toBuilding.buildingZonesTemplateId = fromBuilding.assessmentComplianceBuildingId;

                // TODO: Which of these are still required, if any...?
                // toBuilding.spaces = spaces;
                // toBuilding.projectDescription = fromBuilding.projectDescription;
                // toBuilding.projectDescriptionOther = fromBuilding.projectDescriptionOther;
                // toBuilding.projectClassification = fromBuilding.projectClassification;
                // toBuilding.design = fromBuilding.design;
                // toBuilding.buildingOrientation = fromBuilding.buildingOrientation;

                toBuilding.designFeatures = fromBuilding.designFeatures;

                // Update cached variables
                updateCachedVariables();

            }, 100);

        }

        // Finally, initialize our component.
        initialize();
    }
})();