<div class="table-responsive-vertical shadow-z-1">
    <table class="table table-striped table-data-centered"
           style="margin-bottom: 10px;"
           st-table="vm.modelVariationList"
           st-table-filtered-list="exportList"
           st-global-search="vm.listFilter"
           st-pipe="vm.callServer">

        <!-- Headings -->
        <thead>
            <tr>
                <th st-sort="title" class="clickable" style="user-select:none">Description</th>
                <th ng-if="vm.variationOptionsSettings.floorplanIsActive" st-sort="variationFloorplanName" class="clickable" style="user-select:none">Floorplan</th>
                <th ng-if="vm.variationOptionsSettings.designOptionIsActive" st-sort="variationDesignOptionName" class="clickable" style="user-select:none">Design Option</th>
                <th ng-if="vm.variationOptionsSettings.facadeIsActive" st-sort="variationFacadeName" class="clickable" style="user-select:none">Facade</th>
                <th ng-if="vm.variationOptionsSettings.specificationIsActive" st-sort="variationSpecificationName" class="clickable" style="user-select:none">Specification</th>
                <th ng-if="vm.variationOptionsSettings.configurationIsActive" st-sort="variationConfigurationName" class="clickable" style="user-select:none">Configuration</th>
            </tr>
        </thead>

        <!-- Data -->
        <tbody class="selection-container">
            <tr ng-repeat="modelVariation in vm.modelVariationList track by $index"
                class="model-item {{modelVariation.selected ? 'variation-selected' : ''}}"
                ng-click="vm.selectHomeModel(modelVariation)">

                <!-- Description -->
                <td>
                    {{modelVariation.title}}
                </td>
                <!-- Floorplan -->
                <td ng-if="vm.variationOptionsSettings.floorplanIsActive">
                    {{modelVariation.variationFloorplanName}}
                </td>
                <!-- Design Option -->
                <td ng-if="vm.variationOptionsSettings.designOptionIsActive">
                    {{modelVariation.variationDesignOptionName}}
                </td>
                <!-- Facade -->
                <td ng-if="vm.variationOptionsSettings.facadeIsActive">
                    {{modelVariation.variationFacadeName}}
                </td>
                <!-- Specification -->
                <td ng-if="vm.variationOptionsSettings.specificationIsActive" style="width:max-content;">
                    {{modelVariation.variationSpecificationName}}
                </td>
                <!-- Configuration -->
                <td ng-if="vm.variationOptionsSettings.configurationIsActive">
                    {{modelVariation.variationConfigurationName}}
                </td>
            </tr>
        </tbody>

        <!-- Buttons -->
        <tfoot>
            <tr>
                <td colspan="9999" class="text-center" style="padding: 0;">
                    <div style="background-color: rgb(250,250,250);">

                        <!-- Pagination Display -->
                        <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                    </div>
                </td>
            </tr>
        </tfoot>
    </table>
    <div class="widget-pager" style="text-align: center; margin-bottom: 10px;">
        <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
    </div>
</div>

<style>

    .model-item:hover {
        cursor: pointer;
        user-select: none;
    }

    .model-item.variation-selected td {
        background-color: #bfdba1 !important;
    }

</style>