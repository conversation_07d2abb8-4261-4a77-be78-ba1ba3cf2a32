<md-dialog ng-controller="GroupDrawingUpdateCtrl as vm">
    <form name="groupdrawingupdateform" novalidate>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Apply Changes</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="column" layout-margin layout-padding>
            <p>
                Apply changes to entries from the same document?
            </p>
            <div ng-repeat="item in vm.matchingDrawings track by $index">
                <md-checkbox ng-model="vm.selected[$index]">
                    {{item.drawingDescription}} - Page {{item.pageNumber}}
                </md-checkbox>
            </div>
        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
            <md-button class="md-raised md-primary" ng-disabled="groupdrawingupdateform.$invalid" ng-click="vm.submitSelection()">
                Ok
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>