﻿(function () {
    'use strict';

    var app = angular.module('app');
    // Force a model value to be a string is its a Number
    // Useful on ngModel values used to on Select elements (as the model must be a string).
    app.directive('forceString', function () {
        return {
            require: 'ngModel',
            link: function (scope, element, attrs, controller) {
                scope.$watch(attrs.ngModel, function (newModel) {
                    if (newModel != undefined) {
                        if (typeof newModel === 'number') {
                            newModel = newModel.toString();
                            controller.$setViewValue(newModel);
                            controller.$render();
                        }
                    }
                });
            } // end of link
        };
    });
})();