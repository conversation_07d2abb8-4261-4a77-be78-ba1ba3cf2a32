using System;
using Newtonsoft.Json;

namespace RediSoftware.Dtos
{
    public class Range<T>
    {
        public T Min { get; set; }
        public T Max { get; set; }

        [JsonProperty("minimum")]
        private T MinimumForJs
        {
            get { return Min; }
            set { Min = value; }
        }

        [JsonProperty("maximum")]
        private T MaximumForJs
        {
            get { return Max; }
            set { Max = value; }
        }

        public Range()
        {
        }

        public Range(T min, T max)
        {
            Min = min;
            Max = max;
        }
    }
}
