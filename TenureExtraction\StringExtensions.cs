﻿using Newtonsoft.Json.Serialization;

namespace TenureExtraction
{
    internal static class StringExtensions
    {
        private class CamelCasingHelper : CamelCaseNamingStrategy
        {
            private CamelCasingHelper(){}
            private static CamelCasingHelper helper =new CamelCasingHelper();
            internal static string ToCamelCase(string stringToBeConverted)
            {
                return helper.ResolvePropertyName(stringToBeConverted);     
            }
        
        }
        internal static string ToCamelCase(this string str)
        {
            return CamelCasingHelper.ToCamelCase(str);
        }
    }
}