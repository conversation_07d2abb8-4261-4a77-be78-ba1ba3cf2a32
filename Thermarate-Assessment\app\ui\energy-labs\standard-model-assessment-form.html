<div ng-if="vm.showAssessmentMethod || vm.showTargetEnergyRating || vm.showCostEstimate">

  <div class="el-section-title" style="padding: 2rem 2rem 10px 0;">
    <img class="el-section-icon"
         src="content/images/energy-labs/el-assessment-icon.svg"
         alt="Icon of an arrow which splits into 2 distinct directions">
    Assessment
  </div>

  <div>

    <!-- Assessment Method -->
    <md-input-container ng-if="vm.showAssessmentMethod"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                        style="margin-bottom: -10px;">
      <label>Assessment Method</label>
      <md-select name="assessmentMethod"
                 ng-required="true"
                 ng-model="vm.theModel.optionData.assessmentMethod"
                 ng-change="vm.dataChanged()">
        <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions.assessmentMethod track by $index">
            <md-option ng-value="option">
                {{option}} 
            </md-option>
            <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, 'assessmentMethod', option);"><img src="/content/images/share.png"/></div>
        </div>
      </md-select>
    </md-input-container>

    <!--  Target Energy Rating  -->
    <md-input-container ng-if="vm.showTargetEnergyRating" class="md-block" flex="100" style="margin-bottom: -5px;">
        <label>Target Energy Rating</label>
        <md-select ng-model="vm.theModel.optionData.targetEnergyRating"
                   ng-required="true"
                   ng-change="vm.onDataChanged()">
            <md-option ng-repeat="option in vm.targetEnergyRatingOptions"
                        ng-value="option">
                {{option.toFixed(1)}}
            </md-option>
        </md-select>
    </md-input-container>

    <!-- Cost Estimate -->
    <div ng-if="vm.onLoadCostEstimateEnabled">
        <label style="font-size: 9px;">Include Cost Estimate</label>
        <md-switch ng-model="vm.theModel.optionData.costEstimateEnabledDefault"
                   ng-change="vm.onDataChanged()"
                   style="width: fit-content; margin-top: 5px;">
        </md-switch>
    </div>

  </div>

</div>

<style>

</style>