<form name="designChangeform" 
      class="main-content-wrapper" 
      novalidate data-ng-controller='DesignChangeUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="column">
                <div flex="100">
                    <md-card>
                        <md-card-header>
                            Design Change
                        </md-card-header>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- ******** Description ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Description</label>
                                    <input type="text"
                                           name="description"
                                           ng-model="vm.designChange.description" md-autofocus
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="designChangeform.designChangeCode.$error">
                                        <div ng-message="required">Title is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Sort Order -->
                                <md-input-container class="md-block"
                                                    flex-gt-sm>
                                    <label>Sort Order</label>
                                    <input type="number"
                                           name="sortOrder"
                                           ng-model="vm.designChange.sortOrder"/>
                                </md-input-container>

                            </fieldset>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.designChange"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>

            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="designChangeform.$invalid || vm.editPermission == false"
                           ng-show="vm.designChange.deleted != true"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.designChange.description!=null && vm.designChange.deleted!=true"
                           ng-confirm-click="vm.delete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.designChange.deleted==true"
                           ng-confirm-click="vm.undoDelete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
