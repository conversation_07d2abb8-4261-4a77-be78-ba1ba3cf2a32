// Name: buildingconstructiontemplateservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'buildingconstructiontemplateservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', buildingconstructiontemplateservice]);

    function buildingconstructiontemplateservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'buildingconstructiontemplate/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            getAll: getAll,
            copyTemplate: copyTemplate,
            createEmpty: createEmpty,
            currentFilter: function () { return currentFilter },
            getTemplate: getTemplate,
            createTemplate: createTemplate,
            updateTemplate: updateTemplate,
            deleteTemplate:deleteTemplate,
            undoDeleteTemplate: undoDeleteTemplate,
        };
            
        return service;

        function getList(forType, forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetPaged';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { forType, fromDate, toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Construction Template list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getTemplate(id) {
            return $http({
                url: baseUrl + 'Get',
                params: {id: id},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getAll(type) {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                params: { type },
                cache: true,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Templates: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyTemplate(id) {
            return $http({
                url: baseUrl + 'CopyTemplate',
                params: { id: id },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error copying Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createEmpty(type) {
            return $http({
                url: baseUrl + 'CreateEmpty',
                method: 'GET',
                params: { type }
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error creating empty template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createTemplate(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                //log.logSuccess("System Template Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateTemplate(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Template Updated");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteTemplate(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id: id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteTemplate(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id: id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Template: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
    }
})();
