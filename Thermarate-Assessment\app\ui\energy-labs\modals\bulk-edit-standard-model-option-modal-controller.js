(function () {
    'use strict';
    var controllerId = 'BulkEditStandardModelOptionModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'standardmodelservice', bulkEditStandardModelOptionModalController]);
    function bulkEditStandardModelOptionModalController(common, $scope, $mdDialog, standardmodelservice) {

        // - VARIABLES - //

        let vm = this;

        var thisModelId = $scope.thisModelId;
        var projectId = $scope.projectId;
        vm.categoryName = $scope.categoryName;

        vm.data = { 
            bulkEditAction: "COPYTOMODEL",
            isActive: true,
            costEstimateEnabled: true,
            designInsightsEnabled: true,
            selectedModelId: null
        };

        // - INITIALISE - //

        standardmodelservice.getForProject(projectId, null, true).then(
            result => {
                if (result == null)
                    return;
                vm.modelList = result;
                vm.modelList = vm.modelList.filter(x => x.standardHomeModelId != thisModelId);
                vm.isBusy = false;
            },
            error => {
                console.log(error);
                vm.isBusy = false
            }
        );

        // - HANDLES - //

        vm.selectModel = function (model) {
            vm.modelList.forEach(x => x.selected = false);
            model.selected = true;
            vm.data.selectedModelId = model.standardHomeModelId;
        }

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();