﻿using Microsoft.SqlServer.Types;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts multiple LGATE datasets and combines data as required to obtain as much address coverage as possible.
    /// </summary>
    public class PsaExtractor
    {
        private SqlConnection connection;
        private string directory;
        private Logger logger;

        // To prevent data lose in the case of extraction failure, we switch between table A and B
        //
        private const string SYSTEM_PARAM = "CurrentSlipAddressTable";
        private const string SYSTEM_PARAM_DESC = "Current SlipAddressData table to use for all queries.";
        private const string SYSTEM_DATE_PARAM = "CurrentSlipAddressProcessDate";
        private const string SYSTEM_DATE_PARAM_DESC = "Datetime UTC that the latest accepted dataset was processed.";
        private const string TABLE_A = "RSS_SlipAddressDataA";
        private const string TABLE_B = "RSS_SlipAddressDataB";

        private const string PSA_ID = "251";
        private const string POLYGON_ID = "217";
        private const string LODGED_POLYGON_ID = "222";
        private const string TEMP_POLYGON_TABLE = "TemporaryPolygonTable";

        private const int CHUNK_SIZE = 50000; // Number of rows to return in each sqlite call.

        private string processTable;

        private TenureExtractor tenureExtractor;

        public PsaExtractor(string directory, string connectionString, LogCallback logCallback = null)
        {
            this.directory = directory;

            connection = new SqlConnection(connectionString);
            connection.Open();

            logger = new Logger(logCallback);

            SqlServerTypes.Utilities.LoadNativeAssemblies(AppDomain.CurrentDomain.BaseDirectory);

            // Tenure extractor will retreive data on the fly later.
            tenureExtractor = new TenureExtractor($"{directory}/Land_Tenure_LGATE_226_WA_{Shared.GDA_TARGET}_Subscription.gpkg",
                logger);
            
            SQLitePCL.Batteries_V2.Init();
        }

        public void Dispose()
        {
            if(connection != null)
            {
                connection.Close();
                connection.Dispose();
            }

            tenureExtractor?.Dispose();
        }

        /// <summary>
        /// Run the entire Geopackage -> SQL extraction process. Handles dropping and creating tables, and deleting duplicate entries.
        /// 
        /// Caller must still dispose of the Extractor.
        /// </summary>
        public async Task<string> RunFullProcess(bool cleanOnFailure = true)
        {
            try
            {
                this.Initialize();

                // 1. Drop, create, and extract TEMPORARY polygon tables.
                this.DropTempPolygonTable();
                this.CreateTempPolygonTable();
                this.ExtractCadastrePolygonDatasets();

                // 2. Drop, create and extract from data from the PropertyStreetAddress dataset,
                // combining with data in our other temporary tables as needed, to create our
                // FINAL combined master address table.
                this.DropExtractionTable();
                this.CreateExtractionTable();
                await this.ExtractPsaDataset();

                this.DeleteDuplicates();

                // Update our config within the DB to point to our newly created table (if 
                var result =  this.UpdatePrimaryTable();

                // Drop table to save space.
                this.DropTempPolygonTable();
                return result;
            }
            catch(Exception e)
            {
                logger.Log("msg: " + e.Message + ". stackTrace: " + e.StackTrace);
                // If any exceptions are encountered during this process, clean the directory.
                // This will force the re-downloading of any ZipFiles (Since they might be the source of the problem).
                if(cleanOnFailure)
                    Downloader.CleanDirectory(directory);

                throw e;
            }
        }

        /// <summary>
        /// Initializes & sets System parameters which are needed for switching between our 2 populated tables
        /// </summary>
        public void Initialize()
        {
            SqlCommand cmd = new SqlCommand($"SELECT ParmString FROM dbo.RSS_SystemParameters WHERE ParmCode = '{SYSTEM_PARAM}'", connection);
            processTable = cmd.ExecuteScalar() as string;

            // If the response is null this indicates this is probably the first time the process has happened, so create the given key
            // and for the first process, populate table A
            if(processTable == null)
            {
                processTable = TABLE_A;
                cmd = new SqlCommand($@"INSERT INTO dbo.RSS_SystemParameters (ParmCode, ParmString, ParmDesc) VALUES ('{SYSTEM_PARAM}', '{TABLE_A}', '{SYSTEM_PARAM_DESC}');", connection);
                cmd.ExecuteNonQuery();

                cmd = new SqlCommand($@"INSERT INTO dbo.RSS_SystemParameters (ParmCode, ParmDateTime, ParmDesc) VALUES ('{SYSTEM_DATE_PARAM}', '{DateTime.MinValue}', '{SYSTEM_DATE_PARAM_DESC}');", connection);
                cmd.ExecuteNonQuery();
            } 
            else
            {
                // Otherwise if we have a current table, we want to populate the NON-CURRENT table.
                // At the END of the process we switch the current table to the new table
                processTable = processTable == TABLE_A ? TABLE_B : TABLE_A;
            }
        }

        /// <summary>
        /// Drops our temporary polygon table if it already exists.
        /// </summary>
        public void DropTempPolygonTable()
        {
            // Note: For now I am just dropping the entire table - this makes it easier in future if Alistair asks for more 
            // info to be extracted or w/e from the DB we don't have to worry about schema updates and so on - we simply modify
            // the creation method and voila.
            try
            {
                SqlCommand cmd = new SqlCommand($"DROP TABLE dbo.{TEMP_POLYGON_TABLE};", connection);
                //SqlCommand cmd = new SqlCommand($"IF OBJECT_ID('dbo.{TEMP_POLYGON_TABLE}') IS NOT NULL TRUNCATE TABLE dbo.{TEMP_POLYGON_TABLE};", connection);
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                logger.Log($"DROP TABLE dbo.{TEMP_POLYGON_TABLE} failed with {ex}");
            }
        }

        /// <summary>
        /// Creates temporary polygon table
        /// </summary>
        public void CreateTempPolygonTable()
        {
            // Create address table (Derived from .dbf file)
            SqlCommand cmd = new SqlCommand($@"
                CREATE TABLE dbo.{TEMP_POLYGON_TABLE}(
                    TempDatasetId       int                     IDENTITY (1,1)	NOT NULL,	-- This is our internal ID
                    LandId				int		                NOT NULL,                   -- Apparently there are overlaps (sigh)
	                PolygonNumber		int				        NOT NULL,		            -- This is the polygon number that resides in one of our shapefiles.

                    LotPrefix           nvarchar(30)            NULL,
	                LotNumber			nvarchar(150)			NULL,

	                Latitude			decimal(13, 9)          NULL,
	                Longitude			decimal(13, 9)          NULL,

                    SurveyType          nvarchar(4)             NULL,
                    SurveyNumber        nvarchar(150)           NULL,

                    CreatedDate         nvarchar(100)           NULL,
                    ModifiedDate        nvarchar(100)           NULL,

                    Area                decimal(22, 3)          NULL,                -- Kept failing, bumped mega high
                    Boundary	        geography               NULL,                -- Special geography column
                    BoundaryString      AS Boundary.STAsText()                       -- Incase we ever want the raw data in human readable format.
                );", connection);
            cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Extracts our Polygon and LodgedPolygon datasets into a temporary table which is destroyed after
        /// the full process has run.
        /// </summary>
        /// <param name="limitToCount">OPTIONAL: Limit total rows to count for testing. Mainly for debugging.</param>
        public void ExtractCadastrePolygonDatasets(int? limitToCount = null)
        {
            logger.Log($"Extraction started to table: {processTable}");

            // Grab all files relating to the polygon + lodged polygon datasets.
            string[] files = Directory.GetFiles(directory);
            files = files.Where(s => s.Contains(POLYGON_ID) || s.Contains(LODGED_POLYGON_ID)).ToArray();

            if (files.Length == 0)
                throw new Exception("Warning: No Polygon datasets found to process.");


            // Define our bulk insert data
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add(new DataColumn("LandId", typeof(int)));
            dataTable.Columns.Add(new DataColumn("PolygonNumber", typeof(int)));
            dataTable.Columns.Add(new DataColumn("LotPrefix", typeof(string)));
            dataTable.Columns.Add(new DataColumn("LotNumber", typeof(string)));
            dataTable.Columns.Add(new DataColumn("Latitude", typeof(double)));
            dataTable.Columns.Add(new DataColumn("Longitude", typeof(double)));
            dataTable.Columns.Add(new DataColumn("SurveyType", typeof(string)));
            dataTable.Columns.Add(new DataColumn("SurveyNumber", typeof(string)));
            dataTable.Columns.Add(new DataColumn("CreatedDate", typeof(string)));
            dataTable.Columns.Add(new DataColumn("ModifiedDate", typeof(string)));
            dataTable.Columns.Add(new DataColumn("Area", typeof(double)));
            dataTable.Columns.Add(new DataColumn("Boundary", typeof(SqlGeography)));

            // Create object of SqlBulkCopy which help to insert  
            SqlBulkCopy bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.BulkCopyTimeout = 180; // 3 Minutes

            // Assign and map our data table to our DB table
            bulkCopy.DestinationTableName = $"dbo.{TEMP_POLYGON_TABLE}";
            bulkCopy.ColumnMappings.Add("LandId", "LandId");
            bulkCopy.ColumnMappings.Add("PolygonNumber", "PolygonNumber");
            bulkCopy.ColumnMappings.Add("LotPrefix", "LotPrefix");
            bulkCopy.ColumnMappings.Add("LotNumber", "LotNumber");
            bulkCopy.ColumnMappings.Add("Latitude", "Latitude");
            bulkCopy.ColumnMappings.Add("Longitude", "Longitude");
            bulkCopy.ColumnMappings.Add("SurveyType", "SurveyType");
            bulkCopy.ColumnMappings.Add("SurveyNumber", "SurveyNumber");
            bulkCopy.ColumnMappings.Add("CreatedDate", "CreatedDate");
            bulkCopy.ColumnMappings.Add("ModifiedDate", "ModifiedDate");
            bulkCopy.ColumnMappings.Add("Area", "Area");
            bulkCopy.ColumnMappings.Add("Boundary", "Boundary");


            var geoPackageFiles = new List<string>();

            foreach(string f in files)
            {
                if (f.EndsWith(".gpkg"))
                    geoPackageFiles.Add(f);
            }

            // Loop through each geopackage and insert into full DB.
            foreach (string file in geoPackageFiles)
            {
                logger.Log($"Extraction started for geopackage file: {file}");
                
                var debugCount = 0;
                
                SqliteConnection liteConnection = new SqliteConnection("Filename=" + file);
                liteConnection.Open();

                var table = file.Contains(POLYGON_ID)
                    ? "Cadastre_Polygon_LGATE_217"
                    : "Lodged_Cadastre_Polygon_LGATE_222";
                
                dataTable.Clear(); // Clear previous shapefile/dbf files data.
                
                for(int i = 0; i < int.MaxValue; i++)
                {
                    // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                    if (limitToCount.HasValue && debugCount >= limitToCount)
                        break;
                    
                    var selectRowCmd = new SqliteCommand(
                        $"SELECT * FROM {table} ORDER BY land_id LIMIT {CHUNK_SIZE} OFFSET {i * CHUNK_SIZE}",
                        liteConnection);
                    
                    Console.WriteLine($"Processing Cadastre set {i * CHUNK_SIZE}->{ i * CHUNK_SIZE + CHUNK_SIZE} for {table}");
                    
                    var reader = selectRowCmd.ExecuteReader();

                    if (reader.HasRows == false)
                        break; // Break out of outer loop.
                    
                    while(reader.Read())
                    {
       
                        // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                        if (limitToCount.HasValue && debugCount >= limitToCount)
                            break;

                        debugCount++;
                        
                        // Try to find our polygon within the linked shapefile.
                        SqlGeography geomData = null;

                        try
                        {
                            geomData = Shared.GetSqliteGeometryAsSqlBoundary(reader);
                        }
                        catch (Exception e)
                        {
                            /* Just continue... it's all gooood 8-) */
                        }

                        DataRow dr = dataTable.NewRow();

                        dr["LandId"] = reader["land_id"];
                        dr["PolygonNumber"] = reader["polygon_number"];
                        dr["LotPrefix"] = reader["lot_prefix"];
                        dr["Latitude"] = reader["centroid_latitude"];
                        dr["Longitude"] = reader["centroid_longitude"];
                        dr["SurveyType"] = reader["survey_type"];

                        dr["LotNumber"] = reader["lot_number"];
                        dr["SurveyNumber"] = reader["survey_number"];

                        dr["CreatedDate"] = reader["created_date"]?.ToString();
                        dr["ModifiedDate"] = reader["last_modified_date"]?.ToString();

                        dr["Area"] = reader["calculated_area"];
                        dr["Boundary"] = geomData;

                        dataTable.Rows.Add(dr);
                        if(dataTable.Rows.Count %100 == 0)
                        {
                            bulkCopy.WriteToServer(dataTable);
                            dataTable.Clear();
                        }                        
                    }
                }

                // Bulk write data to sql server.
                if (dataTable.Rows.Count > 0)
                {
                    bulkCopy.WriteToServer(dataTable);
                }
                liteConnection.Close();
            }

           
            // Apply custom indexes to address database.
            // Insert the given row into our DB.
            var createIndexCmd = new SqlCommand(
                $@" CREATE CLUSTERED INDEX PK_RSS_TEMP_LandId ON dbo.{TEMP_POLYGON_TABLE} (LandId);
                    CREATE INDEX PK_RSS_TEMP_PolygonNumber ON dbo.{TEMP_POLYGON_TABLE} (PolygonNumber);", connection);

            createIndexCmd.CommandTimeout = 600; // seconds
            createIndexCmd.ExecuteNonQuery();

            logger.Log($"Finished creating indexes.");
        }
        
        /// <summary>
        /// Drops the primary extraction table if it already exists. Otherwise does nothing.
        /// </summary>
        public void DropExtractionTable()
        {
            // Note: For now I am just dropping the entire table - this makes it easier in future if Alistair asks for more 
            // info to be extracted or w/e from the DB we don't have to worry about schema updates and so on - we simply modify
            // the creation method and voila.
            try
            {
                SqlCommand cmd = new SqlCommand($"DROP TABLE dbo.{processTable};", connection);
                //SqlCommand cmd = new SqlCommand($"IF OBJECT_ID('dbo.{processTable}') IS NOT NULL TRUNCATE TABLE dbo.{processTable};", connection);
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                logger.Log($"DROP TABLE dbo.{processTable} failed with {ex}");
            }
        }

        /// <summary>
        /// Creates the initial table within the database.
        /// </summary>
        public void CreateExtractionTable()
        {
            // Create address table (Derived from .dbf file)
            SqlCommand cmd = new SqlCommand($@"
                CREATE TABLE dbo.{processTable}(

	                SlipAddressDataId   int IDENTITY (1,1) PRIMARY KEY NOT NULL,	-- This is our internal ID

	                LandId				int				NULL,
                    SurveyType          nvarchar(4)     NULL,
                    SurveyNumber        int             NULL,
	                LotNumber			int				NULL,
                    UnitType            nvarchar(64)    NULL,
                    UnitNumber          nvarchar(10)    NULL,
	                RoadNumber			nvarchar(10)	NULL,
	                RoadName			nvarchar(100)	NULL,
	                RoadType			nvarchar(10)	NULL,
	                Locality			nvarchar(100)	NULL,
	                PostCode			nvarchar(12)	NULL,
	                FormattedAddress	nvarchar(200)	NULL,
	                Latitude			decimal(13, 9)  NULL,
	                Longitude			decimal(13, 9)  NULL,
                    LandArea			decimal(22, 3)  NULL,               -- Kept failing, bumped it mega high
	                TitleIdentifier		nvarchar(200)	NULL,
                    ProjectOwner        nvarchar(MAX)	NULL,
                    LgaNames            nvarchar(400)   NULL,

	                Boundary	        geography       NULL,              -- Special geography column (For querying against)
                    BoundaryString      AS Boundary.STAsText(),            -- Incase we ever want the raw data in human readable format.
                    BoundaryJson        nvarchar(MAX)   NULL,              -- Simpler json format for parsing.
                );", connection);
            cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Extract and parse the given dbfFiles. Can optionally only parse a given number (Used for testing only);
        /// </summary>
        /// <param name="limitToCount">OPTIONAL: Limit total rows to count for testing. Mainly for debugging.</param>
        public async Task ExtractPsaDataset(int? limitToCount = null)
        {
            logger.Log($"Extraction started to table: {processTable}");

            // Grab the PSA data set file/s
            string[] files = Directory.GetFiles(directory);
            files = files.Where(s => s.Contains(PSA_ID)).ToArray();
            
            if (files.Length == 0)
                throw new Exception("Warning: No PSA dataset found to process.");

            // Define our bulk insert data
            DataTable dataTable = new DataTable();
            DataColumn[] primaryKeyColumns = new DataColumn[] { new DataColumn("LandId", typeof(int)) }; // Set 'LandId' as primary key
            dataTable.Columns.Add(primaryKeyColumns[0]);
            dataTable.PrimaryKey = primaryKeyColumns;
            dataTable.Columns.Add(new DataColumn("SurveyType", typeof(string)));
            dataTable.Columns.Add(new DataColumn("SurveyNumber", typeof(int)));
            dataTable.Columns.Add(new DataColumn("LotNumber", typeof(int)));
            dataTable.Columns.Add(new DataColumn("UnitType", typeof(string)));
            dataTable.Columns.Add(new DataColumn("UnitNumber", typeof(string)));
            dataTable.Columns.Add(new DataColumn("RoadNumber", typeof(string)));
            dataTable.Columns.Add(new DataColumn("RoadName", typeof(string)));
            dataTable.Columns.Add(new DataColumn("RoadType", typeof(string)));
            dataTable.Columns.Add(new DataColumn("Locality", typeof(string)));
            dataTable.Columns.Add(new DataColumn("PostCode", typeof(string)));
            dataTable.Columns.Add(new DataColumn("FormattedAddress", typeof(string)));
            dataTable.Columns.Add(new DataColumn("Latitude", typeof(double)));
            dataTable.Columns.Add(new DataColumn("Longitude", typeof(double)));
            dataTable.Columns.Add(new DataColumn("TitleIdentifier", typeof(string)));
            dataTable.Columns.Add(new DataColumn("LandArea", typeof(double)));
            dataTable.Columns.Add(new DataColumn("Boundary", typeof(SqlGeography)));
            dataTable.Columns.Add(new DataColumn("BoundaryJson", typeof(string)));
            dataTable.Columns.Add(new DataColumn("ProjectOwner", typeof(string)));
            dataTable.Columns.Add(new DataColumn("LgaNames", typeof(string)));

            // Create object of SqlBulkCopy which help to insert  
            SqlBulkCopy bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.BulkCopyTimeout = 600; // Long

            // Assign and map our data table to our DB table
            bulkCopy.DestinationTableName = $"dbo.{processTable}";
            bulkCopy.ColumnMappings.Add("LandId", "LandId");
            bulkCopy.ColumnMappings.Add("SurveyType", "SurveyType");
            bulkCopy.ColumnMappings.Add("SurveyNumber", "SurveyNumber");
            bulkCopy.ColumnMappings.Add("LotNumber", "LotNumber");
            bulkCopy.ColumnMappings.Add("UnitType", "UnitType");
            bulkCopy.ColumnMappings.Add("UnitNumber", "UnitNumber");
            bulkCopy.ColumnMappings.Add("RoadNumber", "RoadNumber");
            bulkCopy.ColumnMappings.Add("RoadName", "RoadName");
            bulkCopy.ColumnMappings.Add("RoadType", "RoadType");
            bulkCopy.ColumnMappings.Add("Locality", "Locality");
            bulkCopy.ColumnMappings.Add("PostCode", "PostCode");
            bulkCopy.ColumnMappings.Add("FormattedAddress", "FormattedAddress");
            bulkCopy.ColumnMappings.Add("Latitude", "Latitude");
            bulkCopy.ColumnMappings.Add("Longitude", "Longitude");
            bulkCopy.ColumnMappings.Add("TitleIdentifier", "TitleIdentifier");
            bulkCopy.ColumnMappings.Add("LandArea", "LandArea");
            bulkCopy.ColumnMappings.Add("Boundary", "Boundary");
            bulkCopy.ColumnMappings.Add("BoundaryJson", "BoundaryJson");
            bulkCopy.ColumnMappings.Add("ProjectOwner", "ProjectOwner");
            bulkCopy.ColumnMappings.Add("LgaNames", "LgaNames");


            var geoPackageFiles = new List<string>();

            foreach (string f in files)
            {
                if (f.EndsWith(".gpkg"))
                    geoPackageFiles.Add(f);
            }

            // Should only be ONE geopackage file for PSA.
            foreach (string file in geoPackageFiles)
            {
                logger.Log($"Extraction started for geopackage file: {file}");

                var debugCount = 0;

                SqliteConnection liteConnection = new SqliteConnection("Filename=" + file);
                liteConnection.Open();

                
                
                // Loop over all rows in table and add.
                for(int r = 0; r < int.MaxValue; r++)
                {
                    dataTable.Clear(); 
                    
                    // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                    if (limitToCount.HasValue && debugCount >= limitToCount)
                        break;
                    
                    var selectRowCmd = new SqliteCommand(
                        $"SELECT * FROM Property_Street_Address_LGATE_251 ORDER BY land_id LIMIT {CHUNK_SIZE} OFFSET {r * CHUNK_SIZE}",
                        liteConnection);
                    
                    var reader = selectRowCmd.ExecuteReader();

                    Console.WriteLine($"Processing PSA dataset rows {r * CHUNK_SIZE}->{ r * CHUNK_SIZE + CHUNK_SIZE} for Property_Street_Address_LGATE_251");

                    if (reader.HasRows == false)
                        break; // Break out of outer loop.

                    while (reader.Read())
                    {
                        // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                        if (limitToCount.HasValue && debugCount >= limitToCount)
                            break;
                        
                        debugCount++;
                        
                        string landId = reader["land_id"].ToString();
                    
                        var matches = await GetMatchingCadastreRecords(landId);
                        var tenureMatches = tenureExtractor.GetMatchingTenureRecords(landId);

                        PolygonTableData cadastre = null;
                        
                        if(matches.Count > 1)
                        {
                            // use which one has the latest date.
                            cadastre = matches
                                .OrderBy(x => DateTime.Parse(x.CreatedDate, CultureInfo.InvariantCulture))
                                .First();
                        }
                        else
                            cadastre = matches.FirstOrDefault();

                        DataRow dr = dataTable.NewRow();

                        string roadName = Shared.ToTitleCase(reader[PsaField.RoadName] as string);
                        string roadType = Shared.ToTitleCase(reader[PsaField.RoadType] as string);
                        string locality = Shared.ToTitleCase(reader[PsaField.Locality] as string);

                        string formattedAddress = "";

                        string unitNumber = reader[PsaField.UnitNumber] as string;
                        if (unitNumber != null & unitNumber != "")
                            formattedAddress = $"{unitNumber}/{reader[PsaField.RoadNumber]} {roadName} {roadType}, {locality} {reader[PsaField.PostCode]}";
                        else
                            formattedAddress = $"{reader[PsaField.RoadNumber]} {roadName} {roadType}, {locality} {reader[PsaField.PostCode]}";

                        // IF this entry is a duplicate of one already processed (given multiple project owners have already
                        // been processed), skip this entry
                        if (!dataTable.Rows.Contains(landId))
                        {
                            dr["LandId"] = landId;
                            dr["UnitType"] = reader[PsaField.UnitType] as string;

                            dr["UnitNumber"] = unitNumber;
                            dr["RoadNumber"] = reader[PsaField.RoadNumber] as string;
                            dr["PostCode"] = reader[PsaField.PostCode];
                            dr["RoadName"] = roadName;
                            dr["RoadType"] = roadType;
                            dr["Locality"] = locality;
                            dr["FormattedAddress"] = formattedAddress;

                            if (cadastre == null)
                            {
                                dr["Latitude"] = reader[PsaField.Latitude] as double?;
                                dr["Longitude"] = reader[PsaField.Longitude] as double?;

                                int? lotNumber = reader[PsaField.LotNumber] as int?;
                                if (lotNumber.HasValue)
                                    dr["LotNumber"] = lotNumber.Value;
                            }
                            else
                            {
                                SqlChars chars = new SqlChars(new SqlString(cadastre.BoundaryString));
                                var boundary = SqlGeography.STGeomFromText(chars, 4326);
                                boundary = boundary.MakeValid(); // Close any open polygons - No need to RE-reorient.

                                dr["SurveyType"] = cadastre.SurveyType;

                                if (int.TryParse(cadastre.SurveyNumber, out int resultA))
                                    dr["SurveyNumber"] = resultA;

                                if(int.TryParse(cadastre.LotNumber, out int resultB))
                                    dr["LotNumber"] = resultB;

                                dr["Latitude"] = cadastre.Latitude;
                                dr["Longitude"] = cadastre.Longitude;
                                dr["LandArea"] = cadastre.Area;
                                dr["Boundary"] = boundary;
                                dr["BoundaryJson"] = Shared.ConvertSqlGeographyToJson(boundary);
                            }

                            if (tenureMatches?.Count > 0)
                            {
                                dr["TitleIdentifier"] = tenureMatches[0].TitleIdentifier;
                                dr["ProjectOwner"] = tenureMatches[0].ProjectOwner;
                                dr["LgaNames"] = tenureMatches[0].LgaNames;

                                // IF multiple records found
                                if (tenureMatches.Count > 1)
                                {
                                    // Filter out duplicate tenure matches
                                    List<string> filteredProjectOwners = new List<string>();
                                    for (var i = 0; i < tenureMatches.Count; i++)
                                    {
                                        if (!string.IsNullOrEmpty(tenureMatches[i].ProjectOwner) && !filteredProjectOwners.Contains(tenureMatches[i].ProjectOwner))
                                            filteredProjectOwners.Add(tenureMatches[i].ProjectOwner);
                                    }
                                    // After filtering, IF multiple num of records AND num of found records <= 10, add others to current record
                                    if ( filteredProjectOwners.Count > 1 && filteredProjectOwners.Count <= 10)
                                    {
                                        // Add these project owners onto this record.
                                        for (var po = 1; po < filteredProjectOwners.Count - 2; po++)
                                        {
                                            dr["ProjectOwner"] = dr["ProjectOwner"] + ", " + filteredProjectOwners[po];
                                        }
                                        if (filteredProjectOwners.Count > 1 && !string.IsNullOrEmpty(filteredProjectOwners[filteredProjectOwners.Count - 1]))
                                            dr["ProjectOwner"] = dr["ProjectOwner"] + " & " + filteredProjectOwners[filteredProjectOwners.Count - 1];
                                    }
                                    // ELSE IF more than 10, just use simple string
                                    else if (filteredProjectOwners.Count > 10)
                                    {
                                        dr["ProjectOwner"] = "More than 10 project owners.";
                                    }
                                }
                            }

                            // This as new row
                            dataTable.Rows.Add(dr);
                            if (dataTable.Rows.Count % 100 == 0)
                            {
                                try
                                {
                                    bulkCopy.WriteToServer(dataTable);
                                    dataTable.Clear();
                                }
                                catch (Exception e)
                                {
                                    logger.Log($"Exception encountered while writing PSA chunk to SQL Server. " +
                                               $"Range: {r * CHUNK_SIZE} -> {r * CHUNK_SIZE + CHUNK_SIZE}. " +
                                               $"Error: " + e.Message);
                                    return;
                                }
                            }

                        }
                    }

                    if (dataTable.Rows.Count > 0)
                    {
                        // Bulk write chunk data to sql server.
                        try
                        {
                            bulkCopy.WriteToServer(dataTable);
                        }
                        catch (Exception e)
                        {
                            logger.Log($"Exception encountered while writing PSA chunk to SQL Server. " +
                                       $"Range: {r * CHUNK_SIZE} -> {r * CHUNK_SIZE + CHUNK_SIZE}. " +
                                       $"Error: " + e.Message);
                        }
                    }
                }

                liteConnection.Close();
            }

            logger.Log($"Finished row extraction and insertion. Creating indexes.");

            // Apply custom indexes to address database.
            // Insert the given row into our DB.
            var createIndexCmd = new SqlCommand(
                $@" CREATE INDEX PK_RSS_SAD_LotNo ON dbo.{processTable} (LotNumber);
                    CREATE INDEX PK_RSS_SAD_SlipAddressDataId ON dbo.{processTable} (SlipAddressDataId);
                    CREATE INDEX PK_RSS_SAD_RoadName ON dbo.{processTable} (RoadName);", connection);
            createIndexCmd.CommandTimeout = 600; // 10 Minute timeout
            createIndexCmd.ExecuteNonQuery();
            
            logger.Log($"Finished creating indexes.");
        }

        private async Task<List<PolygonTableData>> GetMatchingCadastreRecords(string landId)
        {
            var cmd = new SqlCommand(
                $@"SELECT * FROM dbo.{TEMP_POLYGON_TABLE} WHERE LandId = '{landId}'", connection);

            var responses = new List<PolygonTableData>();

            using (var sqlReader = await cmd.ExecuteReaderAsync())
            {
                while (sqlReader.Read())
                {

                    var lotNo = sqlReader["LotNumber"] as string;
                    var surveyNo = sqlReader["SurveyNumber"] as string;

                    var dto = new PolygonTableData()
                    {

                        TempDatasetId = sqlReader["TempDatasetId"] as int?,
                        LandId = sqlReader["LandId"] as int?,
                        PolygonNumber = sqlReader["PolygonNumber"] as int?,
                        
                        CreatedDate = sqlReader["CreatedDate"] as string,
                        ModifiedDate = sqlReader["ModifiedDate"] as string,

                        LotPrefix = sqlReader["LotPrefix"] as string,
                        LotNumber = lotNo,

                        Latitude = sqlReader["Latitude"] as decimal?,
                        Longitude = sqlReader["Longitude"] as decimal?,

                        SurveyType = sqlReader["SurveyType"] as string,
                        SurveyNumber = surveyNo,

                        Area = sqlReader["Area"] as decimal?,
                        BoundaryString = sqlReader["BoundaryString"] as string
                    };

                    responses.Add(dto);
                }

                sqlReader.Close();

                return responses;
            }

        }

        /// <summary>
        /// Delete any rows that are duplicates in LandId, RoadNumber and RoadName.
        /// </summary>
        public void DeleteDuplicates()
        {
            SqlCommand cmd = new SqlCommand($@"
                WITH cte AS (
                    SELECT
                        LandId,
                        ROW_NUMBER() OVER (
                            PARTITION BY
                                LandId
                            ORDER BY
                                LandId,
                                RoadNumber,
                                RoadName
                        ) row_num
                     FROM
                        dbo.{processTable}
                )
                DELETE FROM cte
                WHERE row_num > 1; ",
                connection);

            cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Update what we consider to be the current up-to-date table IF certain conditions are met.
        /// 
        /// NOTE: This was implemented as LandGate's zip archive was sometimes smaller and missing data found
        /// in previous datasets. Whether this happens often or only in isolated cases remains unknown.
        /// </summary>
        private string UpdatePrimaryTable()
        {
            // On first run, the comparison table will not exist.
            string compareTable = processTable == TABLE_A ? TABLE_B : TABLE_A;

            // If their is no table to compare against, then save out the new table regardless of its data.
            if(!Shared.TableExists(connection, compareTable))
            {
                // Switch our system parameter to say the newly created DB is our current DB to query against.
                var switchParamCmd = new SqlCommand($"UPDATE dbo.RSS_SystemParameters SET ParmString = '{processTable}' WHERE ParmCode = '{SYSTEM_PARAM}'", connection);
                switchParamCmd.ExecuteNonQuery();

                // Update our last processed date
                var dateParamCmd = new SqlCommand($"UPDATE dbo.RSS_SystemParameters SET ParmDateTime = @NewDate WHERE ParmCode = '{SYSTEM_DATE_PARAM}'", connection);
                dateParamCmd.Parameters.AddWithValue("@NewDate", DateTime.Now);
                dateParamCmd.ExecuteNonQuery();

                logger.Log($"New dataset was acceptable because no old dataset exists.");

                return $"New CurrentSlipAddress table confirmed. There was no prior dataset to compare against.";
            }

            // Before we switch what we consider to be the primary table, compare the size (count) of each table
            var tableACmd = new SqlCommand($"SELECT COUNT(*) as total FROM dbo.{TABLE_A};", connection);
            var tableBCmd = new SqlCommand($"SELECT COUNT(*) as total FROM dbo.{TABLE_B};", connection);

            var aCount = tableACmd.ExecuteScalar() as int?;
            var bCount = tableBCmd.ExecuteScalar() as int?;

            var newCount = processTable == TABLE_A ? aCount : bCount;
            var oldCount = processTable == TABLE_A ? bCount : aCount;

            // Since we are unsure if address ever get removed from the dataset, we leave some room for error. As long as the
            // new dataset is at least larger than 97% of the old dataset, we will use it.
            if(oldCount.HasValue == false || newCount.Value >= (oldCount.Value * 0.97))
            {
                // Switch our system parameter to say the newly created DB is our current DB to query against.
                var switchParamCmd = new SqlCommand($"UPDATE dbo.RSS_SystemParameters SET ParmString = '{processTable}' WHERE ParmCode = '{SYSTEM_PARAM}'", connection);
                switchParamCmd.ExecuteNonQuery();

                // Update our last processed date
                var dateParamCmd = new SqlCommand($"UPDATE dbo.RSS_SystemParameters SET ParmDateTime = @NewDate WHERE ParmCode = '{SYSTEM_DATE_PARAM}'", connection);
                dateParamCmd.Parameters.AddWithValue("@NewDate", DateTime.UtcNow);
                dateParamCmd.ExecuteNonQuery();

                logger.Log($"New dataset was acceptable. New:Old =  {newCount.Value}:{oldCount.Value}");

                return $"New CurrentSlipAddress table {processTable} confirmed. There was {newCount.Value} rows in the new dataset and {oldCount.Value} rows in the old dataset.";
            } 
            else 
            {
                // Dataset is less than 97% of the size of the old one, so don't switch.
                logger.Log($"New dataset was NOT acceptable. New:Old =  {newCount.Value}:{oldCount.Value}");
                return $"Old dataset retained. There was {newCount.Value} rows in the new dataset and {oldCount.Value} rows in the old dataset.";
            }
        }
        
        static class PsaField
        {
            internal static string Latitude = "land_centroid_latitude";
            internal static string Longitude = "land_centroid_longitude";

            internal static string RoadNumber = "road_number_1";
            internal static string RoadNumber2 = "road_number_3";
            internal static string RoadName = "road_name";
            internal static string RoadType = "road_type";
            internal static string Locality = "locality";
            internal static string PostCode = "postcode";

            internal static string UnitType = "unit_type_code";
            internal static string UnitNumber = "unit_number";

            internal static string AddressId = "address_id";
            internal static string LandId = "land_id";
            internal static string LotNumber = "lot_number";

            // internal static int TitleIdentifierMachine = 40;    // Eg. "001158000586"
            // internal static int TitleIdentifierHuman = 41;      // Eg. "1158/586"

        }

        private class PolygonTableData
        {
            public int? TempDatasetId;
            public int? LandId;
            public int? PolygonNumber;

            public string LotPrefix;
            public string LotNumber;

            public decimal? Latitude;
            public decimal? Longitude;

            public string SurveyType;
            public string SurveyNumber;

            public string CreatedDate;
            public string ModifiedDate;

            public decimal? Area;
            public SqlGeography Boundary;
            public string BoundaryString;
        }
    }
}
