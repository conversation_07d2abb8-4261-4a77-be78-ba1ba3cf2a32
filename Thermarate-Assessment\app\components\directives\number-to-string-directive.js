﻿(function () {
    'use strict';

    var app = angular.module('app');
    // Currently used on bare <select> elements since by default they don't handle conversions from values where value 
    // is anything other than a string.
    app.directive('numberToString', function () {
        return {
            require: 'ngModel',
            link: function (scope, element, attrs, ngModel) {
                ngModel.$parsers.push(function (val) {
                    return parseInt(val, 10);
                });
                ngModel.$formatters.push(function (val) {
                    return '' + val;
                });
            }
        };
    });;
})();