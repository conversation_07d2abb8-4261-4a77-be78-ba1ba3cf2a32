<form name="BulkEditCostEstimateModal"
      data-ng-controller='BulkEditCostEstimateModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <fieldset id="edit-inputs">
            <table class="bulk-edit-table-consistent-heights table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Item Code -->
                    <tr>
                        <td>
                            Item Code
                        </td>
                        <td>
                            <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                       ng-model="vm.data.costItemId">
                                <md-option ng-value="" ng-click="vm.data.costItemCustomSelected = false"></md-option>
                                <md-option ng-repeat="item in vm.clientCostItems track by item.clientCostItemId"
                                           ng-value="item.clientCostItemId"
                                           ng-click="vm.costItemChanged(item)">
                                    {{item.description}}
                                </md-option>
                                <md-option ng-value="null" ng-click="vm.data.costItemCustomSelected = true">Custom</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Quantity -->
                    <tr>
                        <td>
                            Quantity
                        </td>
                        <td class="el-smuc-center-col">
                            <div class="var-ref-field">
                                <!-- Manual: Input -->
                                <input ng-if="vm.data.quantityVarRefJson == null"
                                       ng-model="vm.data.quantity"
                                       class="lightweight var-ref-input"
                                       style="display: inline-grid;"
                                       ng-required="true" />
                                <!-- Manual: Button -->
                                <div ng-if="vm.data.quantityVarRefJson == null" class="var-ref-select-button" ng-click="vm.openVariableSelectModal(option)">
                                    <img src="/content/images/select.png" />
                                </div>
                                <!-- Reference: Value -->
                                <div ng-if="vm.data.quantityVarRefJson != null"
                                     class="lightweight var-ref-value"
                                     style="display: inline-grid;"
                                     ng-required="true">
                                    {{vm.data.quantity != null ? vm.data.quantity : "-"}}
                                </div>
                                <!-- Reference: Menu -->
                                <md-menu ng-if="vm.data.quantityVarRefJson != null" class="var-ref-three-dot-menu">
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu()"
                                         src="/content/feather/more-horizontal.svg"/>
                                    <md-menu-content>
                                        <!-- Select -->
                                        <md-menu-item><md-button ng-click="vm.openVariableSelectModal(option)">
                                            Select
                                        </md-button></md-menu-item>
                                        <!-- Clear -->
                                        <md-menu-item><md-button ng-click="vm.data.quantity = null; vm.data.quantityVarRefJson = null;">
                                            <span style="color: orangered;">Clear</span>
                                        </md-button></md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>
                    </tr>

                    <!-- UOM -->
                    <tr >
                        <td>
                            UOM
                        </td>
                        <td>
                            <md-select ng-required="false"
                                       class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                       ng-model="vm.data.unitOfMeasure"
                                       ng-disabled="vm.data.costItemId != null">
                                <md-option ng-value="null">No Change</md-option>
                                <md-option ng-value="'mm'">mm</md-option>
                                <md-option ng-value="'m'">m</md-option>
                                <md-option ng-value="'m2'">m<sup>2</sup></md-option>
                                <md-option ng-value="'m3'">m<sup>3</sup></md-option>
                                <md-option ng-value="'Ea'">Ea</md-option>
                                <md-option ng-value="'kg'">kg</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Rate ($) -->
                    <tr>
                        <td>
                            Rate ($)
                        </td>
                        <td>
                            <input ng-model="vm.data.ratePerUnit"
                                   class="lightweight"
                                   type="text"
                                   formatted-number
                                   decimals="2"
                                   style="display: inline;"
                                   ng-required="false"
                                   ng-disabled="vm.data.costItemId != null" />
                        </td>
                    </tr>

                    <!-- Margin (%) -->
                    <tr>
                        <td>
                            Margin (%)
                        </td>
                        <td>
                            <input class="lightweight"
                                   style="display: inline-grid;"
                                   ng-model="vm.data.margin"
                                   ng-disabled="vm.data.costItemId != null" />
                        </td>
                    </tr>

                    <!-- Rounding -->
                    <tr>
                        <td>
                            Rounding
                        </td>
                        <td>
                            <md-select ng-required="false"
                                       class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                       ng-model="vm.data.rounding"
                                       ng-disabled="vm.data.costItemId != null">
                                <md-option ng-value="null">None</md-option>
                                <md-option ng-value="1">1</md-option>
                                <md-option ng-value="10">10</md-option>
                                <md-option ng-value="100">100</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Cost ($) -->
                    <tr ng-if="vm.type === 'model'">
                        <td>
                            Cost ($)
                        </td>
                        <td>
                            <span ng-if="vm.data.rounding == null">{{(vm.data.quantity * vm.data.ratePerUnit * (1 + (vm.data.margin / 100))) | currency : '$' : 2}}</span>
                            <span ng-if="vm.data.rounding != null">{{vm.roundUpInt((vm.data.quantity * vm.data.ratePerUnit * (1 + (vm.data.margin / 100))), vm.data.rounding) | currency : '$' : 0}}</span>

                        </td>
                    </tr>

                    <!-- Notes -->
                    <tr>
                        <td>
                            Notes
                        </td>
                        <td>
                            <input ng-model="vm.data.notes"
                                   class="lightweight"
                                   type="text"
                                   ng-disabled="vm.data.costItemId != null" />
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>
    /* Quantity */
    .var-ref-field {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    /* Manual: Input */
    .var-ref-input {
        margin-top: -3px;
        width: 40px;
        text-align: center;
    }
    /* Manual: Button */
    .var-ref-select-button {
        margin-left: 6px;
        margin-right: -6px;
        margin-top: -3px;
        margin-bottom: -4px;
        width: 32px;
        padding: 5px 4px 0 5px;
        box-sizing: border-box;
        display: block;
        border-radius: 5px;
        cursor: pointer;
    }

        .var-ref-select-button:hover {
            background-color: #adc43b !important;
        }

        .var-ref-select-button > img {
            width: 100%;
            height: auto;
        }
    /* Reference: Value */
    .var-ref-value {
        width: 40px;
    }
    /* Reference: Menu */
    .var-ref-three-dot-menu {
        margin-left: 6px;
        margin-right: -6px;
        width: 32px;
    }

        .var-ref-three-dot-menu > img {
            margin-top: 2px;
            margin-right: -5px;
            margin-left: 5px;
            transform: rotate(90deg);
        }
</style>