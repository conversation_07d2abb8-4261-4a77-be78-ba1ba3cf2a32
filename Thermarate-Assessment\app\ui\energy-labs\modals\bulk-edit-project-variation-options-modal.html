<form name="BulkEditProjectVariationOptionsModal"
      data-ng-controller='BulkEditProjectVariationOptionsModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit ({{vm.categoryName}})"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row" ng-model="vm.data.bulkEditAction">
            <md-radio-button ng-value="'COPYTOPROJECT'">
                Copy To Project
            </md-radio-button>
            <md-radio-button ng-value="'COPY'">
                Duplicate
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <div ng-if="vm.data.bulkEditAction == 'COPYTOPROJECT'"
             style="text-align: center;">

            <div class="content-container">
                <div class="select-text">
                    Select a destination project
                </div>
                <div class="selection-container">
                    <div class="project-item {{project.selected ? 'project-selected' : ''}}" ng-repeat="project in vm.projectList track by project.projectId" ng-click="vm.selectProject(project)">
                        {{project.projectName}}
                    </div>
                </div>
            </div>

        </div>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected variation options will be duplicated.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected variation options will be deleted.</span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()"
                       ng-disabled="vm.data.bulkEditAction == 'COPYTOPROJECT' && vm.data.selectedProjectId == null">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>

    .content-container {
        padding: 20px;
        width: 600px;
        box-sizing: border-box;
    }

        .select-text {
            font-size: 14px;
            text-align: left;
        }

        .selection-container {
            margin-top: 15px;
            width: 100%;
            height: 200px;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 8px;
            background-color: #eeeeee;
            font-size: 16px;
            overflow-y: auto;
        }

            .project-item {
                height: 20px;
                padding: 7px 11px;
                border-radius: 5px;
                cursor: pointer;
                text-align: left;
            }
            .project-item:hover {
                background-color: #bcbcbc;
            }
            .project-item.project-selected,
            .project-item-selected:hover {
                background-color: #bfdba1;
            }

    .buttons-container {
        margin-top: 30px;
    }

</style>
