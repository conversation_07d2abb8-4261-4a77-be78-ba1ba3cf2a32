(function () {
    'use strict';
    var controllerId = 'BulkEditOptionsModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', bulkEditOptionsModalController]);
    function bulkEditOptionsModalController(common, $scope, $mdDialog) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;
        vm.title = $scope.title || "Bulk Edit Options";
        vm.options = $scope.options || [];

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        vm.deleteOptions = function() {
            $mdDialog.hide('DELETE');
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }
    }
})();
