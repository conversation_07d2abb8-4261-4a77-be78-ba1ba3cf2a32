USE [thermarate];

SELECT TOP (1000) [ServiceTemplateId]
      ,[Deleted]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[ServiceCategoryCode]
      ,[ServiceTypeCode]
      ,[Description]
      ,[DisplayDescription]
      ,[ManufacturerId]
      ,[UnitOfMeasureCode]
      ,[ExternalConstructionId]
      ,[SystemCapacity]
      ,[Volume]
      ,[LampPowerRating]
      ,[CutOutDiameter]
      ,[Length]
      ,[Width]
      ,[BladeDiameter]
      ,[StarRating]
      ,[IsDucted]
      ,[IsRecessed]
      ,[IsFlued]
      ,[IsSealed]
      ,[IsPermanentlyInstalled]
      ,[HasSpeedController]
      ,[HasCover]
      ,[HasTimeSwitch]
      ,[ICRatingCode]
      ,[HeatingSystemTypeCode]
      ,[Comments]
      ,[LifeCycleDataJson]
      ,[ServiceControlDeviceCode]
      ,[ShowInReport]
      ,[StarRating2019]
      ,[ServicePumpTypeCode]
      ,[ServiceFuelTypeCode]
      ,[Area]
      ,[Azimuth]
      ,[Pitch]
      ,[ShadeFactor]
      ,[InverterCapacity]
      ,[ServiceBatteryTypeCode]
      ,[BatteryCapacity]
      ,[IsFavourite]
  FROM [dbo].[RSS_ServiceTemplate]
  WHERE [Deleted] = 0
  ORDER BY [ServiceCategoryCode],
           [CreatedOn] DESC