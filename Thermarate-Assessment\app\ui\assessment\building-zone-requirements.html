<!--// NOTE THIS CLASS HAS BEEN SUPERSEDED BY zone-summary-controller + zone-summary.html
//
//
// IT IS BEING LEFT HERE AS A REFERENCE AT THE MOMENT AS I FEEL LIKE IT MAY COME IN HANDY AGAIN AT SOME POINT
// TO COPY OLD FUNCTIONALITY BACK INTO THE NEW ZONE-SUMMARY.
//
//
// WILL DELETE IF THAT FEELING CHANGES!
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//-->
<style>
    .totals-row {
        color: rgba(0, 0, 0, 0.43);
        font-weight: 400;
    }
</style>
<!-- Building Zone Summary -->
<md-card layout-margin>
    <md-card-header>
        <span class="md-title">Zone Summary</span>
    </md-card-header>
    <md-card-content>

        <!-- Sections -->
        <div ng-repeat="section in vm.requirements">

            <md-card ng-if="section.rows.length > 0"
                     style="margin: 0px; padding: 0px; margin-bottom: 10px;">

                <md-card-header style="font-size: 18px; margin-bottom: 10px; margin-right: auto; width: 100%"
                                ng-click="vm.sectionExpansions[section.title] = !vm.sectionExpansions[section.title]">
                    {{section.title}}&nbsp;
                    <i ng-if="vm.sectionExpansions[section.title]" class="fa fa-caret-up" />
                    <i ng-if="!vm.sectionExpansions[section.title]" class="fa fa-caret-down" />
                </md-card-header>

                <!-- Rows in Section -->
                <md-card-content ng-show="vm.sectionExpansions[section.title]"
                                 style="margin: 0px; padding: 0px;">
                    <table class="table table-striped table-hover table-condensed">

                        <!-- Grouped Headers-->
                        <thead>
                            <tr>
                                <th class="text-left"
                                    colspan="7" />
                                <th style="text-align:center"
                                    colspan="3">
                                    Natural Light
                                </th>
                                <th style="text-align:center"
                                    colspan="3">
                                    Ventilation
                                </th>
                                <th style="text-align:center"
                                    colspan="3">
                                    Air Movement
                                </th>
                                <th style="text-align:center"
                                    colspan="3">
                                    Lamp Power
                                </th>
                            </tr>
                        </thead>

                        <!-- 'Subheader' Row -->
                        <tr style="font-weight: bold;">

                            <td class="text-center" style="min-width: 80px; width: 80px;">Zone Number</td>
                            <td class="text-center" style="min-width: 110px; width: 110px;">Description</td>
                            <td class="text-center" style="min-width: 110px; width: 110px;">Zone Activity</td>
                            <td class="text-center" style="width: 133px;">Zone Type</td>
                            <td class="text-center" style="width: 133px;">Conditioned</td>
                            <td class="text-center" style="width: 50px;">NCC Classification</td>
                            <td class="text-right" style="width: 90px;">Floor Area m<sup>2</sup></td>

                            <!-- Natural Light -->
                            <td class="text-center"
                                style="width: 80px;">
                                Required (%)
                            </td>
                            <td class="text-center"
                                style="width: 80px;">
                                Required (m<sup>2</sup>)
                            </td>
                            <td class="text-center"
                                style="width: 85px;">
                                Achieved (m<sup>2</sup>)
                            </td>

                            <!-- Ventilation -->
                            <td class="text-center"
                                style="width: 80px;">
                                Required (%)
                            </td>
                            <td class="text-center"
                                style="width: 80px;">
                                Required (m<sup>2</sup>)
                            </td>
                            <td class="text-center"
                                style="width: 85px;">
                                Achieved (m<sup>2</sup>)
                            </td>

                            <!-- Air Movement -->
                            <td class="text-center"
                                style="width: 80px;">
                                Required (%)
                            </td>
                            <td class="text-center"
                                style="width: 80px;">
                                Required (m<sup>2</sup>)
                            </td>
                            <td class="text-center"
                                style="width: 85px;">
                                Achieved (m<sup>2</sup>)
                            </td>

                            <!-- Lamp Power -->
                            <td class="text-center"
                                style="width: 95px;">
                                Maximum (W/m<sup>2</sup>)
                            </td>
                            <td class="text-center"
                                style="width: 80px;">
                                Maximum (W)
                            </td>
                            <td class="text-center"
                                style="width: 85px;">
                                Achieved (W)
                            </td>
                        </tr>

                        <tbody>

                            <!-- Repeated Rows -->
                            <tr ng-repeat="row in section.rows">

                                <!-- Zone Number -->
                                <td data-title="Zone Number">
                                    <md-input-container class="md-block vertically-condensed">
                                        <textarea type="text"
                                                  ng-model="row.zoneNumber"
                                                  ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Description -->
                                <td data-title="Description">
                                    <md-input-container class="md-block vertically-condensed">
                                        <textarea type="text"
                                                  ng-model="row.description"
                                                  ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Zone Activity -->
                                <td data-title="Zone Activity"
                                    class="text-left">
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-model="row.zoneActivity"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Zone Type -->
                                <td data-title="Zone Type"
                                    class="text-left">
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-model="row.zoneType"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Conditioned -->
                                <td data-title="Conditioned"
                                    class="text-right">
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-model="row.conditioned"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- NCC Classification -->
                                <td data-title="NCC Classification"
                                    class="text-left">
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-model="row.nccClassification"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Floor Area -->
                                <td data-title="Floor Area"
                                    class="text-right">
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.floorArea"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Natural Light Required % -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.naturalLight.requiredPercent"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Natural Light Required M2 -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.naturalLight.required"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Natural Light Achieved -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.naturalLight.achieved"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Ventilation Required % -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.ventilation.requiredPercent"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Ventilation Required M2 -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.ventilation.required"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Ventilation Achieved -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.ventilation.achieved"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Air Movement Required % -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.airMovement.requiredPercent"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Air Movement Required M2 -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.airMovement.required"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Air Movement Achieved -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.airMovement.achieved"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Lamp Power Maximum W/m2 -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-disabled="true"
                                               decimals="2"
                                               ng-value="row.lampPower.maximumWM2" />
                                    </md-input-container>
                                </td>

                                <!-- Lamp Power Maximum W -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.lampPower.maximumW"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Lamp Power Achieved -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed">
                                        <input type="text"
                                               ng-value="row.lampPower.achieved"
                                               decimals="2"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                            </tr>

                        </tbody>
                    </table>
                </md-card-content>
            </md-card>
        </div>
    </md-card-content>
</md-card>
