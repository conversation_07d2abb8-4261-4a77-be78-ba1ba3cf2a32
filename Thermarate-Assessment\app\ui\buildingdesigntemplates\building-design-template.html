<form name="BuildingDesignTemplateForm" 
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='BuildingDesignTemplateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title()}}"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>

        <!-- Template information -->
        <md-card layout-margin ng-form="InfoForm">
            <md-card-header>
                <span class="md-title">Template Information</span>
            </md-card-header>
            <md-card-content>

                <fieldset ng-disabled="vm.editPermission == false">

                    <!-- ******** Template name ******** -->
                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                        <label>Template Name</label>
                        <input type="text" name="templatename"
                               ng-model="vm.template.templateName"
                               maxlength="1000"
                               required />
                        <div ng-messages="templateinfoform.templatename.$error">
                        </div>
                    </md-input-container>

                    <!-- ******** Template Notes ******** -->
                    <md-input-container class="md-block" flex="100" flex-gt-sm="50">
                        <label>Template Notes</label>
                        <textarea type="text" name="templatenotes"
                                  ng-model="vm.template.notes"
                                  rows="4" />
                        <div ng-messages="assessmentform.templatenotes.$error">
                        </div>
                    </md-input-container>

                </fieldset>

            </md-card-content>
        </md-card>

        <!-- TODO: Rename this to something that makes more sense now, like 'zone-data' or something..? -->
        <zone-list ng-form="buildingZonesForm"
                   source="vm.template"
                   on-classification-update="vm.onClassificationUpdate(projectClassification);"
                   disabled="vm.isLocked || vm.editPermission == false"
                   is-template="true">
        </zone-list>

<!--        <floor-plan-data ng-if="vm.loaded === true"-->
<!--                         ng-form="buildingSpacesForm"-->
<!--                         source="vm.template"-->
<!--                         disabled="vm.isLocked || vm.editPermission == false"-->
<!--                         is-template="true">-->
<!--        </floor-plan-data>-->

        <div class="fixed-action-bar">
            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="vm.isBusy || vm.editPermission == false"
                           type="button" 
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           ng-disabled="vm.isBusy || vm.deletePermission == false"
                           type="button" 
                           ng-click="vm.delete()">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>
        </div>

    </div>
</form>       
