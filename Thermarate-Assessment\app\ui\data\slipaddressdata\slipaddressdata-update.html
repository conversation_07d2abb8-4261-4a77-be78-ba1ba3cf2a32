<form id="address-dataset-form"
         class="main-content-wrapper"
         data-ng-controller="slipAddressDataUpdateController as vm">

    <md-card style="padding: 10px 10px; margin-top: 25px;" flex="50">
        <md-card-header>
            <div class="md-title">
                Thermarate Address Dataset
                <div style="font-size: 12px;">Updated on {{vm.lastProcessDate | date: 'dd/MM/yyyy HH:mm:ss'}}</div>
            </div>

        </md-card-header>

        <md-card-content>
            <div>
                The Thermarate Address Dataset is created by compiling data from multiple Landgate datasets. It is updated nightly.
                In the event that the dataset has failed to update overnight, you may manually update the dataset by clicking the button below. This process
                usually takes around 1 hour.
            </div>

            <div>

                <md-checkbox style="margin: 20px 10px 20px 0;"
                             class="md-block" 
                             ng-model="vm.updateStreetAddress">
                    Update Address Dataset
                </md-checkbox>
                <md-checkbox class="md-block" 
                             ng-model="vm.updateSuburbBoundaries">
                    Update Suburb Boundaries
                </md-checkbox>

                <md-checkbox class="md-block"
                             style="margin: 20px 10px;"
                             ng-model="vm.updateLga">
                    Update Local Government Areas
                </md-checkbox>

                <div>
                    <md-button ng-click="vm.processNewDataset()"
                               class="md-raised md-warn"
                               style="margin: 20px 10px;"
                               ng-disabled="vm.processing">
                        Process New Dataset
                    </md-button>
                </div>
                <div>
                    <span style="font-weight: bold;"
                          ng-if="vm.processing">
                        {{vm.state}}
                    </span>
                </div>

            </div>
        </md-card-content>

    </md-card>

</form>
