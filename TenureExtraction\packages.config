﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EPPlus" version="7.2.1" targetFramework="net461" />
  <package id="EPPlus.Interfaces" version="6.1.1" targetFramework="net461" />
  <package id="EPPlus.System.Drawing" version="6.1.1" targetFramework="net461" />
  <package id="GeoAPI.CoordinateSystems" version="1.7.5" targetFramework="net461" />
  <package id="GeoAPI.Core" version="1.7.5" targetFramework="net461" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" targetFramework="net461" />
  <package id="Microsoft.Data.Sqlite" version="5.0.10" targetFramework="net461" />
  <package id="Microsoft.Data.Sqlite.Core" version="5.0.10" targetFramework="net461" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Options" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.Extensions.Primitives" version="2.0.0" targetFramework="net461" />
  <package id="Microsoft.Net.Http.Headers" version="2.0.2" targetFramework="net461" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net461" />
  <package id="NetTopologySuite" version="2.1.0" targetFramework="net461" />
  <package id="NetTopologySuite.Features" version="2.1.0" targetFramework="net461" />
  <package id="NetTopologySuite.IO" version="1.14.0.1" targetFramework="net461" />
  <package id="NetTopologySuite.IO.GeoPackage" version="2.0.0" targetFramework="net461" />
  <package id="NetTopologySuite.IO.SpatiaLite" version="2.0.0" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net461" />
  <package id="ProjNET" version="2.0.0" targetFramework="net461" />
  <package id="PuppeteerSharp" version="2.0.4" targetFramework="net461" />
  <package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.0.6" targetFramework="net461" />
  <package id="SQLitePCLRaw.core" version="2.0.6" targetFramework="net461" />
  <package id="SQLitePCLRaw.lib.e_sqlite3" version="2.0.6" targetFramework="net461" />
  <package id="SQLitePCLRaw.provider.dynamic_cdecl" version="2.0.6" targetFramework="net461" />
  <package id="SQLitePCLRaw.provider.e_sqlite3" version="2.0.6" targetFramework="net461" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net461" />
  <package id="System.Data.Common" version="4.3.0" targetFramework="net461" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net461" />
  <package id="System.Memory" version="4.5.3" targetFramework="net461" />
  <package id="System.Net.Http" version="4.3.3" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net461" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.Encoding.CodePages" version="4.5.1" targetFramework="net461" />
  <package id="System.Text.Encodings.Web" version="4.4.0" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net461" />
</packages>