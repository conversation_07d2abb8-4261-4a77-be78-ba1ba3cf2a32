﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('rdExportData', function ($compile) {
        //Usage:

        var directive = {
            link: link,
            restrict: 'AE',
            scope: {
                'exportList': '&',
                'exportFilename': '@'
            },
        };
        return directive;

        function link(scope, element, attrs) {

            var wkHtml = "";
            scope.colNames = "";

            if (attrs.exportList != undefined && attrs.exportList != "") {
                var wkFilename = 'export';
                if (attrs.exportFilename != undefined && attrs.exportFilename != "") {
                    wkFilename = attrs.exportFilename;
                }
                wkHtml = wkHtml + '<button class="btn btn-sm btn-default" type="button" ng-csv="exportList" csv-header="colNames" filename="' + wkFilename + '.csv" title="export list to a csv file">Export</button>';
            }

            element.html(wkHtml).show();
            $compile(element.contents())(scope);

            if (attrs.exportList != undefined) {
                scope.$watch(scope.exportList, function () {
                    if (scope.exportList() != undefined && scope.exportList().length > 0 && typeof scope.exportList()[0] === 'object') {
                        var recs = scope.exportList()[0];
                        scope.colNames = Object.keys(recs);
                    }
                });
            }
        }
    });
})();