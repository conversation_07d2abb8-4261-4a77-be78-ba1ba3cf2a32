(function () {
    // The StandardModelVariationUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'StandardModelVariationUpdateCtrl';
    angular.module('app').controller(
        controllerId,
        ['$rootScope', 'common', '$scope', '$mdDialog', '$stateParams', '$state', '$http', 'bootstrap.dialog', 'zoneservice', 'zonetypeservice', 'zonesummaryservice',
        'standardmodelservice', 'uuid4', 'projectservice', 'assessmentcomplianceoptionservice', 'security', 'servicetemplateservice', 'clientservice',
        'wadesigncodeservice', 'wholeofhomeservice', 'selectvariablelinkservice', standardModelUpdateController]
    );
    function standardModelUpdateController(
        $rootScope, common, $scope, $mdDialog, $stateParams, $state, $http, modalDialog, zoneservice, zonetypeservice, zonesummaryservice,
        standardmodelservice, uuid4, projectservice, assessmentcomplianceoptionservice, securityservice, servicetemplateservice, clientservice,
        wadesigncodeservice, wholeofhomeservice, selectvariablelinkservice
    ) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;

        // General
        vm.isBusy = true;
        vm.spinnerOptions = {};
        vm.isModal = $scope.modalInstance != null;
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.title = vm.newRecord ? "New Home Design Variation" : 'Edit Home Design Variation';
        vm.viewMode = $scope.viewMode;
        vm.variationOfId = $scope.variationOfId;
        vm.hideActionBar = false;
        vm.floorNames = assessmentcomplianceoptionservice.floorNames;
        vm.symbol = common.symbol;

        // Permissions
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        // Main
        vm.projectId = $scope.projectId;
        vm.project = null;
        vm.standardModel = {
            standardHomeModelId: uuid4.generate(),
            isVariationOfHomeModelId: vm.variationOfId,
            title: $scope.newVariationDefaultName,
            description: "",
            isActive: false, // Will be set from parent model
            projectId: vm.projectId,
            standardHomeModelFiles: [],
            variableOptions: {},
            variationFloorplanId: null,
            variationDesignOptionId: null,
            variationFacadeId: null,
            variationSpecificationId: null,
            variationConfigurationId: null,
            isDefaultVariation: $scope.setAsDefault ?? false,
            drawingAreas: [],
            view3dFloorPlans: false, // Will be set from parent model
            // Initialize variableMetadata with designInsightsEnabled set to false by default (will be set from parent model)
            variableMetadata: {
                generalOptionData: {},
                wholeOfHomeDefaultData: {},
                designInsightsEnabled: false,
                designInsights: []
            },
            // Default NCC Building Classification
            nccBuildingClassification: "Class 1 (Dwelling)",
            // Default Display Floor Area variable path
            displayFloorAreaVarRefJson: JSON.stringify({
                variablePathObj: {
                    sectionValue: "zoneSummary",
                    child: {
                        value: "nccClassification",
                        child: {
                            type: "pop",
                            child: {
                                type: "whereKeyValue",
                                key: "description",
                                value: "Class 1a",
                                child: {
                                    value: "floorArea"
                                }
                            }
                        }
                    }
                }
            }),
            // Default Total Floor Area of Habitable Room variable path
            floorAreaHabitableRoomsVarRefJson: JSON.stringify({
                variablePathObj: {
                    sectionValue: "zoneSummary",
                    child: {
                        value: "habitable",
                        child: {
                            type: "pop",
                            child: {
                                type: "whereKeyValue",
                                key: "description",
                                value: "Habitable Room",
                                child: {
                                    value: "floorArea"
                                }
                            }
                        }
                    }
                }
            }),

            // Default Whole-of-Home Floor Area variable path
            wohFloorAreaVarRefJson: JSON.stringify({
                variablePathObj: {
                    sectionValue: "zoneSummary",
                    child: {
                        value: "nccClassification",
                        child: {
                            type: "pop",
                            child: {
                                type: "whereKeyValue",
                                key: "description",
                                value: "Class 1a",
                                child: {
                                    value: "floorArea"
                                }
                            }
                        }
                    }
                }
            }),
            // Default Bedrooms variable path
            numberOfBedroomsVarRefJson: JSON.stringify({
                variablePathObj: {
                    sectionValue: "zoneSummary",
                    child: {
                        value: "zoneActivity",
                        child: {
                            type: "pop",
                            child: {
                                type: "whereKeyValue",
                                key: "description",
                                value: "Bedroom",
                                child: {
                                    value: "zoneCount"
                                }
                            }
                        }
                    }
                }
            })
        };
        vm.standardModelOriginal = vm.standardModel;
        vm.variationOptionsSettings = {};
        vm.variationOptionsList = [];
        vm.modelVariationList = [];
        vm.drawingAreasTotals = {};

        // TESTING: Dummy Standard Model
        //vm.standardModel = {
        //    ...vm.standardModel,
        //    ...TestDummyData.NewModelVariation,
        //};

        vm.resetChangeDetection = function () {
            vm.standardModelOriginal = angular.copy(vm.standardModel);
        }

        vm.hasChanges = function () {
            return angular.toJson(vm.standardModel) != angular.toJson(vm.standardModelOriginal); // angular.toJson() strips out any "$$..." properties, eg. "$$hashKey"
        }

        // Categories
        vm.categories = standardmodelservice.categories;

        // Design Metrics
        vm.GROUP_OPTIONS = zonesummaryservice.groupOptions;
        vm.filters = angular.copy(zonesummaryservice.defaultFilters);
        zonesummaryservice.applySelectionLogic(vm.filters[0].wallHorizontalShading,    vm.filters[0].wallHorizontalShading.selectableArray[0]);
        zonesummaryservice.applySelectionLogic(vm.filters[0].glazingHorizontalShading, vm.filters[0].glazingHorizontalShading.selectableArray[0]);
        zonesummaryservice.applySelectionLogic(vm.filters[0].wallVerticalShading,      vm.filters[0].wallVerticalShading.selectableArray[0]);
        zonesummaryservice.applySelectionLogic(vm.filters[0].glazingVerticalShading,   vm.filters[0].glazingVerticalShading.selectableArray[0]);
        vm.knownBuildingSummaryGroups = zonesummaryservice.knownBuildingSummaryGroups;
        vm.sectors = zonesummaryservice.defaultSectors;
        vm.sectorFromLabel = zonesummaryservice.sectorFromLabel;
        vm.isSectorNotEmpty = zonesummaryservice.isSectorNotEmpty;
        vm.applyStoreySelectLogic = zonesummaryservice.applyStoreySelectLogic;
        vm.applyGroupSelectLogic = zonesummaryservice.applyGroupSelectLogic;
        vm.selectedStoreys = null;

        // Features
        vm.featuresColumns = standardmodelservice.buildFeaturesColumns();

        // Options
        vm.standardModelOptions = [];

        // Cost Estimate
        vm.costEstimateBulkStatus = new MasterBulkStatus();
        vm.costEstimateBulkStatus.checkboxId = "sm-cost-estimate-bulk-checkbox";
        vm.costEstimateBulkStatus.isIndeterminate = false;
        vm.costEstimateBulkStatus.selectAllCheckboxState = false;
        vm.flattenedCostData = [];

        // Services Defaults
        servicetemplateservice.getServiceTypes().then(data => {
            vm.serviceTypes = data;
            vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                'title',
                vm.serviceTypes
            );
        });

        // Design Insights
        vm.designInsightsBulkStatus = new MasterBulkStatus();
        vm.designInsightsBulkStatus.checkboxId = "sm-design-insights-bulk-checkbox";
        vm.designInsightsBulkStatus.isIndeterminate = false;
        vm.designInsightsBulkStatus.selectAllCheckboxState = false;

        // Utils
        vm.wohConstants = WholeOfHomeConstants;
        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.roundUpInt = common.roundUpInt;
        vm.featureName = standardmodelservice.featureName;
        vm.keyToName = standardmodelservice.keyToName;

        // - ---------- - //
        // - INITIALISE - //
        // - ---------- - //

        function initialise() {
            // IF modal
            if (vm.isModal) {
                if (vm.newRecord === false) {
                    vm.standardModel.standardHomeModelId = $scope.standardHomeModelId;
                }
                vm.hideActionBar = true;
            // ELSE
            } else {
                vm.standardModel.standardHomeModelId = $stateParams.standardHomeModelId;
            }

            // IF new record
            if (vm.newRecord) {
                // Get project
                projectservice.getProject(vm.projectId).then(data => {
                    vm.project = data;
                    vm.standardModel.project = vm.project;
                    vm.standardModel.clientId = vm.project.clientId;

                    // Initialize zoneSummaryDataUnfiltered with placeholder data for new records
                    vm.zoneSummaryDataUnfiltered = {
                        zoneSummary: selectvariablelinkservice.placeholder_zoneSummary,
                        envelopeSummary: selectvariablelinkservice.placeholder_envelopeSummary,
                        interiorSummary: selectvariablelinkservice.placeholder_interiorSummary
                    };

                    // Initialize the linked variable values
                    vm.updateLinkedVarValues(true);
                    vm.isBusy = false;
                });
                // Get Variation settings and options
                standardmodelservice.getStandardModel(vm.variationOfId).then(modelResult => {
                    vm.parentModel = modelResult;
                    vm.variationOptionsSettings = modelResult.variationOptionsSettings;
                    vm.variationOptionsList = modelResult.variationOptionsList;
                    vm.standardModel.isVariationOfHomeModelTitle = modelResult.title;

                    // Set default values based on parent Home Design values
                    vm.standardModel.isActive = modelResult.isActive;
                    vm.standardModel.view3dFloorPlans = modelResult.view3dFloorPlans;
                    vm.standardModel.costEstimateEnabled = modelResult.costEstimateEnabled;
                    vm.standardModel.variableMetadata.designInsightsEnabled = modelResult.variableMetadata?.designInsightsEnabled || false;

                    // Get other existing Variations so can determine what Variation options are available
                    standardmodelservice.getModelVariations(vm.variationOfId).then(
                        variationsResult => {
                            if (variationsResult != null) {
                                vm.modelVariationList = variationsResult;
                                // Set default title based on parent's title
                                vm.standardModel.title = `${modelResult.title} Variation ${variationsResult.length+1}`;
                            }
                        },
                        error => vm.isBusy = false
                    );
                });
            // ELSE
            } else {
                // Lists
                zoneservice.getZoneActivityList().then(data => vm.zoneActivityList = data);
                zonetypeservice.getList().then(data => vm.zoneTypeList = data.data);
                zoneservice.getNccClassificationList().then(data => vm.nccClassificationList = data);
                // Get model
                standardmodelservice.getStandardModel(vm.standardModel.standardHomeModelId).then(result => {
                    vm.standardModel = result;
                    // All other data
                    let p_getClient = clientservice.getClient(vm.standardModel.clientId);
                    let p_getProject = projectservice.getProject(vm.standardModel.projectId);
                    let p_getStandardModelParent = standardmodelservice.getStandardModel(vm.standardModel.isVariationOfHomeModelId);
                    let p_getStandardModelVariations = standardmodelservice.getModelVariations(vm.standardModel.isVariationOfHomeModelId);
                    let p_getStandardModelOptions = standardmodelservice.getStandardModelOptions(vm.standardModel.standardHomeModelId);
                    Promise.all([
                        p_getClient,
                        p_getProject,
                        p_getStandardModelParent,
                        p_getStandardModelVariations,
                        p_getStandardModelOptions
                    ]).then(results => {
                        vm.client = results[0];
                        vm.project = results[1];
                        vm.parentModel = results[2];
                        // Get other existing Variations so can determine what Variation options are available
                        if (result[3] != null) {
                            vm.modelVariationList = result[3];
                        }
                        vm.standardModelOptions = results[4];
                        vm.variationOptionsSettings = vm.standardModel.variationOptionsSettings;
                        vm.variationOptionsList = vm.standardModel.variationOptionsList;
                        vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
                        vm.storeysChanged();
                        vm.standardModel.project = vm.project;
                        vm.client.clientCostItems.sort((a,b) => a.description > b.description ? 1 : -1);
                        vm.setImageSortOrder(vm.standardModel.standardHomeModelFiles);
                        vm.clearVariableOptions = false;
                        // Drawing Areas
                        vm.calcAllDrawingAreas();
                        // Build Variable Options
                        vm.buildVariableOptions();
                        // Envelope Summary Filters
                        if (vm.standardModel.zoneSummaryBuildingData?.zones != null) {
                            vm.applyFilters();
                        } else {
                            flattenCostData();
                        }
                        vm.resetChangeDetection();
                        vm.isBusy = false;
                    });
                });
            }
        }

        // --------- //
        // - UTILS - //
        // --------- //

        vm.getAvailableFloorplanOptions = function () {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != vm.standardModel.standardHomeModelId), vm.standardModel, vm.getVariationOptions("Floorplan"),     "variationFloorPlanId",     "standardHomeModelVariationOptionId", ["variationDesignOptionId", "variationFacadeId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableDesignOptionOptions = function () {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != vm.standardModel.standardHomeModelId), vm.standardModel, vm.getVariationOptions("DesignOption"),  "variationDesignOptionId",  "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationFacadeId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableFacadeOptions = function () {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != vm.standardModel.standardHomeModelId), vm.standardModel, vm.getVariationOptions("Facade"),        "variationFacadeId",        "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationSpecificationId", "variationConfigurationId"]);
        }
        vm.getAvailableSpecificationOptions = function () {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != vm.standardModel.standardHomeModelId), vm.standardModel, vm.getVariationOptions("Specification"), "variationSpecificationId", "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationFacadeId", "variationConfigurationId"]);
        }
        vm.getAvailableConfigurationOptions = function () {
            return common.availableOptionsForItem(vm.modelVariationList.filter(item => item.standardHomeModelId != vm.standardModel.standardHomeModelId), vm.standardModel, vm.getVariationOptions("Configuration"), "variationConfigurationId", "standardHomeModelVariationOptionId", ["variationFloorplanId", "variationDesignOptionId", "variationFacadeId", "variationSpecificationId"]);
        }

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Get Variation Options for Category
        vm.getVariationOptions = function (category) {
            return vm.variationOptionsList.filter(o => o.variationCategoryCode == category);
        }

        // URL is valid
        async function urlIsValid(url) {
            try {
                return fetch(url)
                      .then(response => response.ok)
                      .catch(error => false);
            } catch (error) {
                return false;
            }
        }

        // Floorplanner
        vm.floorplannerLinkBlur = async function () {
            if (!vm.standardModel.floorplannerLink.startsWith("https://floorplanner.com")) {
                vm.floorplannerLinkWrongSite = true;
                vm.floorplannerLinkInvalid = false;
            } else if (!(await urlIsValid(vm.standardModel.floorplannerLink))) {
                vm.floorplannerLinkWrongSite = false;
                vm.floorplannerLinkInvalid = true;
                $scope.$apply();
            } else {
                vm.floorplannerLinkEditing = false;
                vm.floorplannerLinkWrongSite = false;
                vm.floorplannerLinkInvalid = false;
                $scope.$apply();
            }
        }

        // Project Search
        vm.getProjects = function(searchTerm) {
            var filter = [{ field: "projectName", operator: "startswith", value: searchTerm }];
            var sort = [{ field: "projectName", dir: "asc" }];
            return projectservice.getList(null, null, null, null, null, sort, filter).then(data => {
                var list = data.data;
                return list;
            });
        }

        // After Project Selected
        vm.projectChanged = function() {
            if (vm.standardModel.project != null) {
                vm.standardModel.projectId = vm.standardModel.project.projectId;
            }
        }

        // BUild Variable Options
        vm.buildVariableOptions = function () {
            vm.variableOptions = [];
            for (const key in vm.standardModel.variableOptions) {
                vm.variableOptions.push({
                    title: key,
                    options: vm.standardModel.variableOptions[key]
                });
            }
            vm.variableOptions.forEach(o => {
                if (vm.standardModel.variableMetadata.generalOptionData[o] == null) {
                    vm.standardModel.variableMetadata.generalOptionData[o] = [];
                }
            });
        }

        // Storey changed, make changes to Drawing Areas
        vm.storeysChanged = function () {
            // Add any new Storeys if didn't exist before
            for (let i = vm.standardModel.drawingAreas.length; i < vm.standardModel.storeys; i++) {
                vm.standardModel.drawingAreas.push({ storeyName: i < vm.floorNames.length ? vm.floorNames[i] : `Floor ${i+1}`, willDelete: false });
            }
            // Delete any Drawing Areas that are no longer applicable (will actually delete when run 'save'), and vice versa
            for (let i = 0; i < vm.standardModel.storeys; i++) {
                vm.standardModel.drawingAreas[i].willDelete = false;
            }
            for (let i = vm.standardModel.storeys; i < vm.standardModel.drawingAreas.length; i++) {
                vm.standardModel.drawingAreas[i].willDelete = true;
            }
        }

        // Calc all Drawing Areas totlas
        vm.calcAllDrawingAreas = function () {
            vm.drawingAreasTotals = {};
            vm.standardModel.drawingAreas.forEach(row => {
                vm.calcHouseGarage(row);
                vm.calcHouseFacade(row);
                vm.calcRoofPitched(row);
            });
            vm.calcTotalHouse();
            vm.calcTotalGarage();
            vm.calcTotalHouseGarage();
            vm.calcTotalAlfresco();
            vm.calcTotalPorch();
            vm.calcTotalHousePerimeter();
            vm.calcTotalHouseFacade();
            vm.calcTotalRoofHorizontal();
            vm.calcTotalRoofPitched();
        }
        // Drawing Areas Calculations
        vm.calcHouseGarage = function (row) {
            if (row.houseArea == null || row.garageArea == null) {
                row.houseGarage = "";
            } else {
                row.houseGarage = (row.houseArea + row.garageArea).toFixed(2);
            }
        }
        vm.calcHouseFacade = function (row) {
            if (common.stringNullOrEmpty(row.housePerimeter) || common.stringNullOrEmpty(row.ceilingHeight)) { row.houseFacade = ""; }
            else { row.houseFacade = (row.housePerimeter * row.ceilingHeight).toFixed(2); }
        }
        vm.calcRoofPitched = function (row) {
            if (row.roofHorizontal == null || row.roofPitch == null) {
                row.roofPitched = "";
            }
            else {
                row.roofPitched = (row.roofHorizontal / Math.cos(row.roofPitch*(Math.PI/180))).toFixed(2);
            }
        }
        // Drawing Areas Calculations - Totals
        vm.calcTotalHouse = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.houseArea))) { vm.drawingAreasTotals.house = ""; }
            else { vm.drawingAreasTotals.house = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.houseArea??0), 0).toFixed(2) };
        }
        vm.calcTotalGarage = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.garageArea))) { vm.drawingAreasTotals.garage = ""; }
            else { vm.drawingAreasTotals.garage = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.garageArea??0), 0).toFixed(2); }
        }
        vm.calcTotalHouseGarage = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.houseArea)) || vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.garageArea))
            ) {
                vm.drawingAreasTotals.houseGarage = "";
            }
            else vm.drawingAreasTotals.houseGarage = (Number(vm.drawingAreasTotals.house) + Number(vm.drawingAreasTotals.garage)).toFixed(2);
        }
        vm.calcTotalAlfresco = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.alfrescoArea))) { vm.drawingAreasTotals.alfresco = ""; }
            else { vm.drawingAreasTotals.alfresco = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.alfrescoArea??0), 0).toFixed(2); }
        }
        vm.calcTotalPorch = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.porchArea))) { vm.drawingAreasTotals.porch = ""; }
            else { vm.drawingAreasTotals.porch = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.porchArea??0), 0).toFixed(2); }
        }
        vm.calcTotalHousePerimeter = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.housePerimeter))) { vm.drawingAreasTotals.housePerimeter = ""; }
            else { vm.drawingAreasTotals.housePerimeter = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.housePerimeter??0), 0).toFixed(2); }
        }
        vm.calcTotalHouseFacade = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => a.houseFacade == "")) { vm.drawingAreasTotals.houseFacade = ""; }
            else { vm.drawingAreasTotals.houseFacade = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + Number(cur.houseFacade), 0).toFixed(2); }
        }
        vm.calcTotalRoofHorizontal = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => common.stringNullOrEmpty(a.roofHorizontal))) { vm.drawingAreasTotals.roofHorizontal = ""; }
            else { vm.drawingAreasTotals.roofHorizontal = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + (cur.roofHorizontal??0), 0).toFixed(2); }
        }
        vm.calcTotalRoofPitched = function () {
            if (vm.standardModel.drawingAreas?.filter(a => !a.willDelete).some(a => a.roofPitched == "")) { vm.drawingAreasTotals.roofPitched = ""; }
            else { vm.drawingAreasTotals.roofPitched = vm.standardModel.drawingAreas?.filter(a => !a.willDelete).reduce((prev, cur) => prev + Number(cur.roofPitched), 0).toFixed(2); }
        }

        vm.clearAllDrawingAreas = function () {
            for (let i = 0; i < vm.standardModel.drawingAreas.length; i++) {
                vm.standardModel.drawingAreas[i] = { storeyName: i < vm.floorNames.length ? vm.floorNames[i] : `Floor ${i+1}`, willDelete: false };
            }
            vm.calcAllDrawingAreas();
        }

        // Add Plan Image
        vm.addPlanImage = function() {
            if (vm.standardModel.standardHomeModelFiles == null) {
                vm.standardModel.standardhomeModelFiles = [];
            }
            vm.standardModel.standardHomeModelFiles.push({
                standardHomeModelFileId: uuid4.generate(),
                standardHomeModelId: vm.standardModel.standardHomeModelId,
                deleted: false,
                createdOn: new Date().toUTCString(),
                sortOrder: vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length
            });
            vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
        }

        // Delete Plan Image
        vm.deletePlanImage = function(homeModelFile) {
            homeModelFile.deleted = true;
            vm.setImageSortOrder(vm.standardModel.standardHomeModelFiles);
            vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
        }

        /**
         * Sets sort order of standard home model files when no 'sort order' is already supplied. This is only
         * required to fix up old data which was created WITHOUT the sort order in the first place.
         * */
        vm.setImageSortOrder = function (standardHomeModelFiles) {
            let order = 0;
            standardHomeModelFiles.forEach(x => {
                if (x.deleted)
                    return;
                x.sortOrder = order;
                order++;
            });
        }

        // Categories
        vm.copyCategoriesToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Categories ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyCategoriesToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.categoryAcreage = sourceModel.categoryAcreage;
                        vm.standardModel.categoryDualOccupancy = sourceModel.categoryDualOccupancy;
                        vm.standardModel.categoryDuplex = sourceModel.categoryDuplex;
                        vm.standardModel.categorySplitLevel = sourceModel.categorySplitLevel;
                        vm.standardModel.categoryDisplayHome = sourceModel.categoryDisplayHome;
                        vm.standardModel.categoryFarmhouse = sourceModel.categoryFarmhouse;
                        vm.standardModel.categoryGrannyFlat = sourceModel.categoryGrannyFlat;
                        vm.standardModel.categoryNarrowLot = sourceModel.categoryNarrowLot;
                        vm.standardModel.categoryNewDesign = sourceModel.categoryNewDesign;
                        vm.standardModel.categoryRearLoaded = sourceModel.categoryRearLoaded;
                        vm.standardModel.categorySingleStorey = sourceModel.categorySingleStorey;
                        vm.standardModel.categoryTwoStorey = sourceModel.categoryTwoStorey;
                        vm.standardModel.categoryThreeStorey = sourceModel.categoryThreeStorey;
                    }
                }
                else {
                    await standardmodelservice.copyCategoriesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.selectAllCategories = function () {
            vm.categories.forEach(c => vm.standardModel[c] = true);
        }
        vm.clearAllCategories = function () {
            vm.categories.forEach(c => vm.standardModel[c] = false);
        }

        // Design
        vm.copyDesignMetadataToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Design Metadata ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyDesignMetadataToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.wohFloorArea = sourceModel.wohFloorArea;
                        vm.standardModel.wohFloorAreaVarRefJson = sourceModel.wohFloorAreaVarRefJson;
                        vm.standardModel.siteCover = sourceModel.siteCover;
                        vm.standardModel.siteCoverVarRefJson = sourceModel.siteCoverVarRefJson;
                        vm.standardModel.nccBuildingClassification = sourceModel.nccBuildingClassification;
                        vm.standardModel.lowestLivingAreaFloorType = sourceModel.lowestLivingAreaFloorType;
                        vm.standardModel.displayFloorArea = sourceModel.displayFloorArea;
                        vm.standardModel.displayFloorAreaVarRefJson = sourceModel.displayFloorAreaVarRefJson;
                        vm.standardModel.floorAreaHabitableRooms = sourceModel.floorAreaHabitableRooms;
                        vm.standardModel.floorAreaHabitableRoomsVarRefJson = sourceModel.floorAreaHabitableRoomsVarRefJson;
                        vm.standardModel.houseWidth = sourceModel.houseWidth;
                        vm.standardModel.houseDepth = sourceModel.houseDepth;
                        vm.standardModel.width = sourceModel.width;
                        vm.standardModel.depth = sourceModel.depth;
                        vm.standardModel.storeys = sourceModel.storeys;
                        vm.standardModel.numberOfBedrooms = sourceModel.numberOfBedrooms;
                        vm.standardModel.numberOfBedroomsVarRefJson = sourceModel.numberOfBedroomsVarRefJson;
                        vm.standardModel.numberOfBathrooms = sourceModel.numberOfBathrooms;
                        vm.standardModel.numberOfBathroomsVarRefJson = sourceModel.numberOfBathroomsVarRefJson;
                        vm.standardModel.livingAreas = sourceModel.livingAreas;
                        vm.standardModel.livingAreasVarRefJson = sourceModel.livingAreasVarRefJson;
                        vm.standardModel.numberOfGarageSpots = sourceModel.numberOfGarageSpots;
                        vm.updateLinkedVarValues();
                        vm.storeysChanged();
                    }
                }
                else {
                    await standardmodelservice.copyDesignMetadataToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.clearAllDesignMetadata = function () {
            vm.standardModel.displayFloorArea = null;
            vm.standardModel.displayFloorAreaVarRefJson = null;
            vm.standardModel.wohFloorArea = null;
            vm.standardModel.wohFloorAreaVarRefJson = null;
            vm.standardModel.floorAreaHabitableRooms = null;
            vm.standardModel.floorAreaHabitableRoomsVarRefJson = null;
            vm.standardModel.siteCover = null;
            vm.standardModel.siteCoverVarRefJson = null;
            vm.standardModel.nccBuildingClassification = null;
            vm.standardModel.lowestLivingAreaFloorType = null;
            vm.standardModel.houseWidth = null;
            vm.standardModel.houseDepth = null;
            vm.standardModel.width = null;
            vm.standardModel.depth = null;
            vm.standardModel.storeys = null;
            vm.standardModel.numberOfBedrooms = null;
            vm.standardModel.numberOfBedroomsVarRefJson = null;
            vm.standardModel.numberOfBathrooms = null;
            vm.standardModel.numberOfBathroomsVarRefJson = null;
            vm.standardModel.livingAreas = null;
            vm.standardModel.livingAreasVarRefJson = null;
            vm.standardModel.numberOfGarageSpots = null;
        }

        // Design Metrics
        vm.openScratchImportModal = function () {
            let modalScope = $rootScope.$new();
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/variation-scratch-import-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(newBuildingSummary => {
                try {
                    // Save to Variation
                    standardmodelservice.updateZoneSummaryBuildingData(vm.standardModel.standardHomeModelId, newBuildingSummary).then(() => {
                        common.logger.logSuccess("Successfully saved Zone Summary Data to Variation");
                        vm.standardModel.zoneSummaryBuildingData = newBuildingSummary;
                        vm.applyFilters(() => vm.updateLinkedVarValues(true));
                    });
                } catch (e) {
                    common.logger.logError("Error saving Zone Summary Data to Variation.", e);
                }
            });
        }
        vm.copyDesignMetricsToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Design Metrics ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyDesignMetricsToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.zoneSummaryBuildingData = sourceModel.zoneSummaryBuildingData;
                        vm.applyFilters(() => vm.updateLinkedVarValues(true));
                    }
                }
                else {
                    await standardmodelservice.copyDesignMetricsToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.generateZoneSummaryData = function (setUnfilteredData) {
            let genTotalGross = list => list == null || list.length == 0 ? 0 : list.map(i => i.grossArea).reduce((a,b) => a+b, 0);
            let genTotalNet   = list => list == null || list.length == 0 ? 0 : list.map(i => i.netArea  ).reduce((a,b) => a+b, 0);
            // Generate
            let summary = {
                zoneSummary: zonesummaryservice.constructBuildingSummaryGroups(
                    vm.standardModel.zoneSummaryBuildingData,
                    vm.standardModel.zoneSummaryBuildingData.storeys,
                    vm.zoneActivityList,
                    vm.zoneTypeList,
                    vm.nccClassificationList
                ),
                envelopeSummary: zonesummaryservice.calculateEnvelopeSummaryData(
                    vm.standardModel.zoneSummaryBuildingData,
                    { sectors: vm.sectors },
                    vm.filters
                ),
                interiorSummary: {
                    storeys: vm.standardModel.zoneSummaryBuildingData.storeys.sort((a,b) => a.floor > b.floor ? 1 : -1).map(storey => ({
                        name: storey.name,
                        rows: [{
                            adjacencyName: "Interior Zones",
                            grossInternalWallArea: genTotalGross(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                         .find(s => s.category.constructionCategoryCode == "InteriorWall")
                                                                                                         ?.elements
                                                                                                         .filter(e => e.adjacentZoneDescription.toLowerCase() != "garage")),
                            netInternalWallArea: genTotalNet(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                     .find(s => s.category.constructionCategoryCode == "InteriorWall")
                                                                                                     ?.elements
                                                                                                     .filter(e => e.adjacentZoneDescription.toLowerCase() != "garage"))
                        },{
                            adjacencyName: "Garage",
                            grossInternalWallArea: genTotalGross(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                         .find(s => s.category.constructionCategoryCode == "InteriorWall")
                                                                                                         ?.elements
                                                                                                         .filter(e => e.adjacentZoneDescription.toLowerCase() == "garage")),
                            netInternalWallArea: genTotalNet(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                     .find(s => s.category.constructionCategoryCode == "InteriorWall")
                                                                                                     ?.elements
                                                                                                     .filter(e => e.adjacentZoneDescription.toLowerCase() == "garage"))
                        },{
                            adjacencyName: "Roof Space",
                            grossInternalWallArea: genTotalGross(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                         .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToRoofSpace")
                                                                                                         ?.elements),
                            netInternalWallArea: genTotalNet(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                     .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToRoofSpace")
                                                                                                     ?.elements)
                        },{
                            adjacencyName: "Subfloor Space",
                            grossInternalWallArea: genTotalGross(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                         .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToSubfloorSpace")
                                                                                                         ?.elements),
                            netInternalWallArea: genTotalNet(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                     .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToSubfloorSpace")
                                                                                                     ?.elements)
                        },{
                            adjacencyName: "Neighbour",
                            grossInternalWallArea: genTotalGross(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                         .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToNeighbour")
                                                                                                         ?.elements),
                            netInternalWallArea: genTotalNet(vm.standardModel.zoneSummaryBuildingData.surfaces
                                                                                                     .find(s => s.category.constructionCategoryCode == "InteriorWallAdjacentToNeighbour")
                                                                                                     ?.elements)
                        }]
                    }))
                }
            };
            summary.interiorSummary.storeys.forEach(s => {
                s.rows.push({
                    adjacencyName: "All Adjacencies",
                    grossInternalWallArea: s.rows.map(r => r.grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: s.rows.map(r => r.netInternalWallArea).reduce((a,b) => a+b, 0)
                });
            });
            summary.interiorSummary.storeys.push({
                name: "Whole Building",
                rows: [{
                    adjacencyName: "Interior Zones",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[0].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[0].netInternalWallArea).reduce((a,b) => a+b, 0)
                },{
                    adjacencyName: "Garage",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[1].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[1].netInternalWallArea).reduce((a,b) => a+b, 0)
                },{
                    adjacencyName: "Roof Space",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[2].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[2].netInternalWallArea).reduce((a,b) => a+b, 0)
                },{
                    adjacencyName: "Subfloor Space",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[3].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[3].netInternalWallArea).reduce((a,b) => a+b, 0)
                },{
                    adjacencyName: "Neighbour",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[4].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[4].netInternalWallArea).reduce((a,b) => a+b, 0)
                },{
                    adjacencyName: "All Adjacencies",
                    grossInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[5].grossInternalWallArea).reduce((a,b) => a+b, 0),
                    netInternalWallArea: summary.interiorSummary.storeys.map(s => s.rows[5].netInternalWallArea).reduce((a,b) => a+b, 0)
                }]
            });
            vm.zoneSummaryData = summary;
            if (setUnfilteredData) {
                vm.zoneSummaryDataUnfiltered = angular.copy(vm.zoneSummaryData);
                vm.zoneSummaryDataUnfiltered.envelopeSummary = zonesummaryservice.calculateEnvelopeSummaryData(
                    vm.standardModel.zoneSummaryBuildingData,
                    { sectors: vm.sectors },
                    zonesummaryservice.defaultFilters // Default filters
                );
            }
        }
        vm.applySelectionLogic = function (filter, shading) {
            zonesummaryservice.applySelectionLogic(filter, shading, () => vm.generateZoneSummaryData(false));
            // Force refresh (Was taking multiple seconds to reflect change otherwise...?)
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }
        vm.calculateEnvelopeSummaryData = function () {
            // Timeout stops angular being 1 update behind...
            setTimeout(() => {
                vm.zoneSummaryData.envelopeSummary = zonesummaryservice.calculateEnvelopeSummaryData(
                    vm.standardModel.zoneSummaryBuildingData,
                    { sectors: vm.sectors },
                    vm.filters
                );
            }, 30);
        }
        vm.applyFilters = function (finishedCallback) {
            setTimeout(() => {
                // Only take storeys that have glazing when determining this value.
                const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.standardModel.zoneSummaryBuildingData.openings);
                vm.selectedStoreys = vm.standardModel.zoneSummaryBuildingData.storeys?.filter(x => storeysWithGlazing.some(y => y === x.floor));
                // Run dynamic dropdown rules for their initial values. (also refreshes Envelope Summary Data)
                vm.applyStoreySelectLogic(vm.standardModel.zoneSummaryBuildingData, vm.filters[0], () => vm.generateZoneSummaryData(true));
                setTimeout(() => {
                    vm.filters[0].wallHorizontalShading.selection = angular.copy(vm.filters[0].wallHorizontalShading.selectableArray);
                    vm.filters[0].glazingHorizontalShading.selection = angular.copy(vm.filters[0].glazingHorizontalShading.selectableArray);
                    vm.filters[0].wallVerticalShading.selection = angular.copy(vm.filters[0].wallVerticalShading.selectableArray);
                    vm.filters[0].glazingVerticalShading.selection = angular.copy(vm.filters[0].glazingVerticalShading.selectableArray);
                    flattenCostData();
                    if (finishedCallback) {
                        finishedCallback();
                    }
                    vm.resetChangeDetection();
                }, 500);
            }, 200);
        }
        vm.copyDrawingAreasToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Drawing Areas ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyDrawingAreasToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.drawingAreasJson = sourceModel.drawingAreasJson;
                        vm.standardModel.drawingAreas = sourceModel.drawingAreas;
                        vm.storeysChanged();
                        vm.calcAllDrawingAreas();
                    }
                }
                else {
                    await standardmodelservice.copyDrawingAreasToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.clearAllDesignMetrics = function () {
            vm.standardModel.zoneSummaryBuildingData = null;
            vm.zoneSummaryData = null;
            vm.zoneSummaryDataUnfiltered = null;
            vm.updateLinkedVarValues();
        }

        // Features
        vm.copyFeaturesToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Features ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyFeaturesToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.featuresKitchenLivingDiningGroundFloor = sourceModel.featuresKitchenLivingDiningGroundFloor;
                        vm.standardModel.featuresKitchenLivingDiningUpperFloor = sourceModel.featuresKitchenLivingDiningUpperFloor;
                        vm.standardModel.featuresKitchenLivingDiningRear = sourceModel.featuresKitchenLivingDiningRear;
                        vm.standardModel.featuresKitchenLivingDiningMiddle = sourceModel.featuresKitchenLivingDiningMiddle;
                        vm.standardModel.featuresKitchenLivingDiningFront = sourceModel.featuresKitchenLivingDiningFront;
                        vm.standardModel.featuresButlersPantry = sourceModel.featuresButlersPantry;
                        vm.standardModel.featuresScullery = sourceModel.featuresScullery;
                        vm.standardModel.featuresWalkInPantry = sourceModel.featuresWalkInPantry;
                        vm.standardModel.featuresMasterSuiteGroundFloor = sourceModel.featuresMasterSuiteGroundFloor;
                        vm.standardModel.featuresMasterSuiteFirstFloor = sourceModel.featuresMasterSuiteFirstFloor;
                        vm.standardModel.featuresMasterSuiteAtFront = sourceModel.featuresMasterSuiteAtFront;
                        vm.standardModel.featuresMasterSuiteAtMiddle = sourceModel.featuresMasterSuiteAtMiddle;
                        vm.standardModel.featuresMasterSuiteAtRear = sourceModel.featuresMasterSuiteAtRear;
                        vm.standardModel.featuresDressingRoom = sourceModel.featuresDressingRoom;
                        vm.standardModel.featuresHisHerWir = sourceModel.featuresHisHerWir;
                        vm.standardModel.featuresWalkInRobe = sourceModel.featuresWalkInRobe;
                        vm.standardModel.featuresAudioVisual = sourceModel.featuresAudioVisual;
                        vm.standardModel.featuresSecondLivingLounge = sourceModel.featuresSecondLivingLounge;
                        vm.standardModel.featuresHomeCinema = sourceModel.featuresHomeCinema;
                        vm.standardModel.featuresHomeTheatre = sourceModel.featuresHomeTheatre;
                        vm.standardModel.featuresMedia = sourceModel.featuresMedia;
                        vm.standardModel.featuresActivity = sourceModel.featuresActivity;
                        vm.standardModel.featuresEntertainment = sourceModel.featuresEntertainment;
                        vm.standardModel.featuresFamily = sourceModel.featuresFamily;
                        vm.standardModel.featuresFormalLounge = sourceModel.featuresFormalLounge;
                        vm.standardModel.featuresGames = sourceModel.featuresGames;
                        vm.standardModel.featuresLeisure = sourceModel.featuresLeisure;
                        vm.standardModel.featuresLounge = sourceModel.featuresLounge;
                        vm.standardModel.featuresRumpus = sourceModel.featuresRumpus;
                        vm.standardModel.featuresSecondLiving = sourceModel.featuresSecondLiving;
                        vm.standardModel.featuresMultipurposeRoom = sourceModel.featuresMultipurposeRoom;
                        vm.standardModel.featuresRetreat = sourceModel.featuresRetreat;
                        vm.standardModel.featuresComputerNook = sourceModel.featuresComputerNook;
                        vm.standardModel.featuresENook = sourceModel.featuresENook;
                        vm.standardModel.featuresHomeOffice = sourceModel.featuresHomeOffice;
                        vm.standardModel.featuresStudy = sourceModel.featuresStudy;
                        vm.standardModel.featuresStudyNook = sourceModel.featuresStudyNook;
                        vm.standardModel.featuresCellar = sourceModel.featuresCellar;
                        vm.standardModel.featuresCloakRoom = sourceModel.featuresCloakRoom;
                        vm.standardModel.featuresGuestBedroom = sourceModel.featuresGuestBedroom;
                        vm.standardModel.featuresGym = sourceModel.featuresGym;
                        vm.standardModel.featuresMudRoom = sourceModel.featuresMudRoom;
                        vm.standardModel.featuresNannysQuarters = sourceModel.featuresNannysQuarters;
                        vm.standardModel.featuresPowderRoom = sourceModel.featuresPowderRoom;
                        vm.standardModel.featuresStoreRoom = sourceModel.featuresStoreRoom;
                        vm.standardModel.featuresWalkInLinen = sourceModel.featuresWalkInLinen;
                        vm.standardModel.featuresCarport = sourceModel.featuresCarport;
                        vm.standardModel.featuresLHCarport = sourceModel.featuresLHCarport;
                        vm.standardModel.featuresRHCarport = sourceModel.featuresRHCarport;
                        vm.standardModel.featuresGarage = sourceModel.featuresGarage;
                        vm.standardModel.featuresLHGarage = sourceModel.featuresLHGarage;
                        vm.standardModel.featuresRHGarage = sourceModel.featuresRHGarage;
                        vm.standardModel.featuresRearAccess = sourceModel.featuresRearAccess;
                        vm.standardModel.featuresRearLoaded = sourceModel.featuresRearLoaded;
                        vm.standardModel.featuresWorkshop = sourceModel.featuresWorkshop;
                        vm.standardModel.featuresAlfresco = sourceModel.featuresAlfresco;
                        vm.standardModel.featuresMLAlfresco = sourceModel.featuresMLAlfresco;
                        vm.standardModel.featuresMRAlfresco = sourceModel.featuresMRAlfresco;
                        vm.standardModel.featuresRCAlfresco = sourceModel.featuresRCAlfresco;
                        vm.standardModel.featuresRLAlfresco = sourceModel.featuresRLAlfresco;
                        vm.standardModel.featuresRRAlfresco = sourceModel.featuresRRAlfresco;
                        vm.standardModel.featuresBalcony = sourceModel.featuresBalcony;
                        vm.standardModel.featuresFrontBalcony = sourceModel.featuresFrontBalcony;
                        vm.standardModel.featuresRearBalcony = sourceModel.featuresRearBalcony;
                        vm.standardModel.featuresCourtyard = sourceModel.featuresCourtyard;
                        vm.standardModel.featuresLHCourtyard = sourceModel.featuresLHCourtyard;
                        vm.standardModel.featuresRHCourtyard = sourceModel.featuresRHCourtyard;
                        vm.standardModel.featuresOutdoorLiving = sourceModel.featuresOutdoorLiving;
                        vm.standardModel.featuresMLOutdoorLiving = sourceModel.featuresMLOutdoorLiving;
                        vm.standardModel.featuresMROutdoorLiving = sourceModel.featuresMROutdoorLiving;
                        vm.standardModel.featuresRCOutdoorLiving = sourceModel.featuresRCOutdoorLiving;
                        vm.standardModel.featuresRLOutdoorLiving = sourceModel.featuresRLOutdoorLiving;
                        vm.standardModel.featuresRROutdoorLiving = sourceModel.featuresRROutdoorLiving;
                        vm.standardModel.featuresVerandah = sourceModel.featuresVerandah;
                    }
                }
                else {
                    await standardmodelservice.copyFeaturesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.selectAllFeatures = function () {
            vm.featuresColumns.forEach(column => {
                column.groups.forEach(group => {
                    group.features.forEach(feature => {
                        vm.standardModel[feature] = true;
                    });
                });
            });
        }
        vm.clearAllFeatures = function () {
            vm.featuresColumns.forEach(column => {
                column.groups.forEach(group => {
                    group.features.forEach(feature => {
                        vm.standardModel[feature] = false;
                    });
                });
            });
        }

        // Variable Options
        vm.ordered = function(variable, options) {
            let allOptions = vm.standardModel.variableMetadata.generalOptionData[variable];
            // Options will just be strings
            const huh = options.sort((a, b) => {
                const aX = allOptions?.find(x => x.optionValue === a);
                const bX = allOptions?.find(x => x.optionValue === b);
                if(aX?.sortOrder != null && bX?.sortOrder != null)
                    return aX.sortOrder - bX.sortOrder;
                if(aX?.sortOrder != null && bX?.sortOrder == null)
                    return -1;
                if(aX?.sortOrder == null && bX?.sortOrder != null)
                    return -1;
                // Falllback to string compare when so no sort order set on any.
                if (typeof a == 'string' && b != null) {
                    return a.localeCompare(b);
                } else {
                    return a - b;
                }
            });
            return huh;
        }
        vm.uploadExcelFile = function (file) {
            if (file != null) {
                standardmodelservice.processSpreadsheet(file, vm.standardModel.standardHomeModelId).then(data => {
                    setTimeout(initialise, 250);
                });
            }
        }
        vm.propertyGroups = standardmodelservice.specGridOrder;
        vm.toggleVariableDefaultOption = function (variable, option) {
            let options = vm.standardModel.variableMetadata.generalOptionData[variable.title];
            if (options == null) {
                vm.standardModel.variableMetadata.generalOptionData[variable.title] = [];
                options = vm.standardModel.variableMetadata.generalOptionData[variable.title];
            }
            let match = options?.find(x => x.optionValue === option);
            if (match == null) {
                options.push({
                    optionValue: option,
                    costEstimateData: { description: option, category: variable.title, enabled: false }
                });
                match = options?.find(x => x.optionValue === option);
            }
            if (match.costEstimateData == null) {
                match.costEstimateData = { description: option, category: variable.title, enabled: false };
            }
            match.costEstimateData.category = variable.title;
            if (match.isDefaultForVariable) {
                match.isDefaultForVariable = false;
            }
            else {
                options.forEach(option => option.isDefaultForVariable = false);
                match.isDefaultForVariable = true;
            }
        }
        vm.isDefaultForVariable = function (variable, option) {
            return vm.standardModel.variableMetadata
                                   .generalOptionData[variable.title]
                                   ?.filter(x => x.optionValue === option)[0]
                                   ?.isDefaultForVariable;
        }
        vm.copyVariablesToVariation = function () {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = "Copy Variables To Variation";
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (selectedVariationId) {
                await standardmodelservice.copyVariablesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
            });
        }
        vm.copyVariablesToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Variables ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyVariablesToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.variableOptions = sourceModel.variableOptions;
                        vm.standardModel.variableMetadata.generalOptionData = sourceModel.variableMetadata.generalOptionData;

                        // If not a new record, we need to get the standardModelOptions from the backend
                        if (!vm.newRecord) {
                            vm.standardModelOptions = await standardmodelservice.getStandardModelOptions(vm.standardModel.standardHomeModelId);
                        }

                        vm.buildVariableOptions();
                        vm.resetChangeDetection();
                        flattenCostData();
                    }
                }
                else {
                    await standardmodelservice.copyVariablesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.restoreVariableDefaults = function () {
            const model = vm.standardModel;
            const defaults = vm.project.energyLabsSettings.variableOptionDefaults;
            for (const key in vm.project.energyLabsSettings.properties) {

                const values = defaults.generalOptionData[key];

                // If there are no values present for this key, it means that
                // NO DEFAULTS have been set and therefore they should rever to blank.
                if (values == null || values.length === 0) {
                    model.variableMetadata.generalOptionData[key] = [];
                }

                defaults.generalOptionData[key]?.forEach(def => {
                    // Ensure this option value is even valid for this specific home design.
                    const valid = model.variableOptions[key].includes(def.optionValue);
                    if (!valid)
                        return;

                    const option = model.variableMetadata.generalOptionData[key]?.find(x => x.optionValue === def.optionValue);

                    if (option != null) {
                        option.isDefaultForVariable = def.isDefaultForVariable;
                        option.costEstimateEnabled = def.costEstimateEnabled;
                        option.sortOrder = def.sortOrder;
                    } else {
                        if (model.variableMetadata.generalOptionData[key] == null)
                            model.variableMetadata.generalOptionData[key] = [];
                        model.variableMetadata.generalOptionData[key].push(angular.copy(def));
                    }
                });

                // ... we also need to loop the _other_ way. We are looking for values which exist on the model
                // but NOT in the defaults. This implies the value in the model needs to be 'nullified'.
                model.variableMetadata.generalOptionData[key]?.forEach(optX => {
                    const def = defaults.generalOptionData[key]?.find(x => x.optionValue === optX.optionValue);
                    if (def == null) {
                        optX.isDefaultForVariable = null;
                        optX.costEstimateEnabled = null;
                        optX.sortOrder = null;
                    }
                });
            }

            flattenCostData();
        }
        vm.clearVariables = function () {
            for (const key in vm.project.energyLabsSettings.properties) {
                vm.standardModel.variableMetadata.generalOptionData[key] = [];
            }
            vm.variableOptions.forEach(o => o.options = []);
            for (const key in vm.standardModel.variableOptions) {
                vm.standardModel.variableOptions[key] = [];
            }
            vm.standardModelOptions = [];
            vm.clearVariableOptions = true; // So clears in db when click 'Save'
            flattenCostData();
        }
        vm.didntJustReorderSomething = true; // ffs
        vm.reorderOptions = function(variable, optionsInOrder) {
            // This is the hax of all hax. Basically, even though there are never duplicate options in this array AND I
            // even applied a 'unique' filter to the md-option ng-repeat loop, when you reorder the north offset or
            // climate zone variable options, the md-select->md-option's in the design insights card throw up an error
            // saying 'cannot have duplicate md-option values blah blah'. I guess it's got something to do with how
            // lr-drag-n-drop works *shrug*. Anyway this is used to momentarily hide the md-select inputs in the design
            // input card, thus removing the error.
            if (variable === 'northOffset' || variable === 'climateZone') {
                vm.didntJustReorderSomething = false;
                setTimeout(() => vm.didntJustReorderSomething = true, 5);
            }

            // options will be in the order we want so simply loop over and apply
            let allOptions = vm.standardModel.variableMetadata.generalOptionData[variable];

            optionsInOrder.forEach((opt, i) => {
                let match = allOptions?.find(x => x.optionValue === opt);

                if (match == null && match !== 0) {
                    allOptions.push({ optionValue: opt });
                    match = allOptions?.find(x => x.optionValue === opt);
                }

                match.sortOrder = i;
            });

            flattenCostData();
            safeApply();
        }

        // Flatten cost data for UI & sort on category and option desc.
        function flattenCostData() {
            vm.flattenedCostData = [];
            for (var categoryKey in vm.standardModel.variableMetadata.generalOptionData) {
                if (!vm.standardModel.variableMetadata.generalOptionData.hasOwnProperty(categoryKey))
                    continue;
                const ordered = vm.standardModel.variableMetadata.generalOptionData[categoryKey].sort((a, b) => a.sortOrder - b.sortOrder);
                ordered?.forEach(option => {
                    if (option.costEstimateData != null) {
                        // Cost Item
                        if (option.costEstimateEnabled && option.costEstimateData != null) {
                            vm.costItemChanged(option, vm.client.clientCostItems.find(i => i.clientCostItemId == option.costEstimateData.costItemId));
                            vm.flattenedCostData.push(option);
                        }
                    }
                });
            }
            vm.flattenedCostData.sort((a, b) => {
                let ac = a.costEstimateData?.category;
                let bc = b.costEstimateData?.category;
                if (ac === bc) {
                    ac = a.sortOrder;
                    bc = b.sortOrder;
                }
                if (typeof ac == 'string' && bc != null) {
                    return ac.localeCompare(bc);
                } else {
                    return (ac || 0) - (bc || 0);
                }
            });
            vm.updateLinkedVarValues();
        }

        // Update Quantity values from variable references
        vm.updateLinkedVarValues = function (newData) {
            let updatedVariableLinkData = false;
            // Design section
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'displayFloorAreaVarRefJson',        'displayFloorArea');
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'wohFloorAreaVarRefJson',            'wohFloorArea');
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'floorAreaHabitableRoomsVarRefJson', 'floorAreaHabitableRooms');
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'siteCoverVarRefJson',               'siteCover');
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'numberOfBedroomsVarRefJson',        'numberOfBedrooms',        0);
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'numberOfBathroomsVarRefJson',       'numberOfBathrooms',       0);
            updatedVariableLinkData = vm.updateLinkedVar(newData, vm.standardModel, 'livingAreasVarRefJson',             'livingAreas',             0);
            // Quantities in Cost Estimate records
            vm.flattenedCostData.forEach(option => {
                updatedVariableLinkData = vm.updateLinkedVar(newData, option.costEstimateData, 'quantityVarRefJson', 'quantity');
            });
            if (updatedVariableLinkData) {
                standardmodelservice.updateStandardModel(vm.standardModel, false).then(() => vm.resetChangeDetection());
            }
        }

        vm.updateLinkedVar = function (newData, theObject, theRefObjKey, theValueKey, toDp) {
            if (theObject[theRefObjKey] == null || theObject[theRefObjKey] == "") {
                return null;
            }
            let updatedVariableLinkData = false;
            try {
                let updatedJson = selectvariablelinkservice.setValueFromLink(
                    newData,
                    vm.standardModel.zoneSummaryBuildingData,
                    {
                        drawingAreasData: {
                            drawingAreas: vm.standardModel.drawingAreas,
                            drawingAreasTotals: vm.drawingAreasTotals
                        },
                        zoneSummary: vm.zoneSummaryDataUnfiltered?.zoneSummary,
                        envelopeSummary: vm.zoneSummaryDataUnfiltered?.envelopeSummary,
                        interiorSummary: vm.zoneSummaryDataUnfiltered?.interiorSummary
                    },
                    theObject[theRefObjKey],
                    theObject,
                    theValueKey,
                    toDp
                );
                if (updatedJson != null) {
                    theObject[theRefObjKey] = updatedJson;
                    updatedVariableLinkData = true;
                }
            } catch (e) {
                // When a variable path doesn't return a value, keep the reference but set the value to null
                // This allows the UI to display a dash "-" instead of reverting to manual input
                theObject[theValueKey] = null;
                console.log(e);
                // Don't clear the variable reference (theRefObjKey) so the UI stays in reference mode
            }
            return updatedVariableLinkData;
        }

        // Services Defaults
        vm.copyServiceDefaultsToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Services Defaults ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyServiceDefaultsToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        vm.standardModel.variableMetadata.wholeOfHomeDefaultData = sourceModel.variableMetadata.wholeOfHomeDefaultData;
                    }
                }
                else {
                    await standardmodelservice.copyServiceDefaultsToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.restoreServiceDefaults = function () {
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData = angular.copy(vm.parentModel.variableMetadata.wholeOfHomeDefaultData);
        }
        vm.clearAllServiceDefaults = function () {
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.waterHeating = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic = null;
        }
        vm.showEnergyRatingForCode = wholeofhomeservice.showEnergyRatingForCode;

        // Cost Estimates
        vm.toggleCostEstimate = function (variable, option) {
            let options = vm.standardModel.variableMetadata.generalOptionData[variable.title];
            let match = options?.find(x => x.optionValue === option);
            if (match == null) {
                options.push({ optionValue: option, costEstimateData: { description: option, category: variable.title } });
                match = options?.find(x => x.optionValue === option);
            }
            if (match.costEstimateData == null) {
                match.costEstimateData = { description: option, category: variable.title, enabled: false };
            }
            match.costEstimateEnabled = !match.costEstimateEnabled;
            match.costEstimateData.category = variable.title;
            flattenCostData();
        }
        vm.costEstimateEnabledFor = function (variable, option) {
            const matches = vm.standardModel.variableMetadata.generalOptionData[variable.title]?.filter(x => x.optionValue === option);
            if (matches == null || matches.length === 0) {
                return false;
            }
            const match = matches[0];
            return match.costEstimateEnabled;
        }
        vm.copyCostEstimatesToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Cost Estimates ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    // If it's not a new record, save to the backend
                    if (!vm.newRecord) {
                        await standardmodelservice.copyCostEstimatesToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    }

                    // Get the model data and update the frontend
                    let sourceModel = await standardmodelservice.getStandardModel(vm.newRecord ? selectedVariationId : vm.standardModel.standardHomeModelId);
                    if (sourceModel) {
                        for (const key in sourceModel.variableMetadata.generalOptionData) {
                            for (let i = 0; i < sourceModel.variableMetadata.generalOptionData[key].length; i++) {
                                if (vm.standardModel.variableMetadata.generalOptionData[key] &&
                                    vm.standardModel.variableMetadata.generalOptionData[key][i]) {
                                    vm.standardModel.variableMetadata.generalOptionData[key][i].costEstimateData =
                                        sourceModel.variableMetadata.generalOptionData[key][i].costEstimateData;
                                }
                            }
                        }
                        vm.updateLinkedVarValues();
                    }
                }
                else {
                    await standardmodelservice.copyCostEstimatesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.restoreCostEstimateDefaults = function () {
            const model = vm.standardModel;
            const defaults = vm.project.energyLabsSettings.variableOptionDefaults;

            for (const key in vm.project.energyLabsSettings.properties) {

                // If there are no values present for this key, it means that
                // NO DEFAULTS have been set and therefore they should revert to blank.
                // UPDATE: Apparently Client wants them to remain unchanged...
                // const values = defaults.generalOptionData[key];
                // if (values == null || values.length === 0) {
                //     model.variableMetadata.generalOptionData[key] = [];
                // }

                defaults.generalOptionData[key]?.forEach(def => {

                    // Ensure this option value is even valid for this specific home design.
                    const valid = model.variableOptions[key].includes(def.optionValue);
                    if (!valid)
                        return;

                    const match = model.variableMetadata.generalOptionData[key]?.find(x => x.optionValue === def.optionValue && x.costEstimateData != null);

                    if (match != null) {
                        if (def.costEstimateData == null) {
                            // match.costEstimateEnabled = false;
                            // match.costEstimateData = null;
                            return;
                        }
                        match.costEstimateData.costItemId = def.costEstimateData.costItemId;
                        match.costEstimateData.quantity = def.costEstimateData.quantity;
                        match.costEstimateData.quantityVarRefJson = def.costEstimateData.quantityVarRefJson;
                        match.costEstimateData.unitOfMeasure = def.costEstimateData.unitOfMeasure;
                        match.costEstimateData.ratePerUnit = def.costEstimateData.ratePerUnit;
                        match.costEstimateData.rounding = def.costEstimateData.rounding;
                        match.costEstimateData.margin = def.costEstimateData.margin;
                        match.costEstimateData.notes = def.costEstimateData.notes;
                    } else {
                        // UPDATE: Do not touch any value which is not in the defaults list.
                        // if (match == null) {
                        //
                        //     if (model.variableMetadata.generalOptionData[key] == null)
                        //         model.variableMetadata.generalOptionData[key] = [];
                        //
                        //     model.variableMetadata.generalOptionData[key].push(angular.copy(def));
                        // } else {
                        //     match.costEstimateData = angular.copy(def.costEstimateData);
                        // }
                    }

                });

                // UPDATE: Client wants this to be left as-is...?
                // ... we also need to loop the _other_ way. We are looking for values which exist on the model
                // but NOT in the defaults. This implies the value in the model needs to be 'nullified'.
                // model.variableMetadata.generalOptionData[key]?.forEach(optX => {
                //
                //     const def = defaults.generalOptionData[key]?.find(x => x.optionValue === optX.optionValue);
                //
                //     if (def == null) {
                //         optX.costEstimateEnabled = false;
                //         optX.costEstimateData = null;
                //     }
                // });
            }

            flattenCostData();
        }
        vm.clearAllCostEstimates = function () {
            // We no longer clear the data, just leave it as is
            // The data will still be shown in the UI regardless of the toggle state
        }
        vm.itemCodesForCategory = function (category) {
            return vm.client.clientCostItems.filter(i => i.category == category);
        }
        vm.openVariableSelectModal = function (theObject, varRefKey) {
            let modalScope = $rootScope.$new();
            modalScope.zoneSummaryBuildingData = vm.standardModel.zoneSummaryBuildingData;
            modalScope.drawingAreasData = {
                drawingAreas: vm.standardModel.drawingAreas,
                drawingAreasTotals: vm.drawingAreasTotals
            };
            modalScope.zoneSummary = vm.zoneSummaryDataUnfiltered?.zoneSummary;
            modalScope.interiorSummary = vm.zoneSummaryDataUnfiltered?.interiorSummary;
            modalScope.varRefObj = theObject[varRefKey] != null ? JSON.parse(theObject[varRefKey]) : null;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/zone-summary-variable-select-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                multiple: true,
                skipHide: vm.isModal // Keep parent modal open when this is opened from a modal
            }).then(newVarRefObj => {
                theObject[varRefKey] = JSON.stringify(newVarRefObj);
                vm.updateLinkedVarValues();
            });
        }
        vm.costItemChanged = function (row, costItem) {
            // Link may be broken when come back to page if the parent Cost Item has changed category
            if (costItem == null || costItem.category != row.costEstimateData.category) {
                row.costEstimateData.costItemId = null;
            } else {
                row.costEstimateData.unitOfMeasure = costItem.unitOfMeasure;
                row.costEstimateData.ratePerUnit = costItem.ratePerUnit;
                row.costEstimateData.margin = costItem.margin;
                row.costEstimateData.rounding = costItem.rounding;
                row.costEstimateData.notes = costItem.notes;
            }
        }

        // Design Insight
        vm.addDesignInsight = function() {
            if (vm.standardModel.variableMetadata.designInsights == null)
                vm.standardModel.variableMetadata.designInsights = [];
            vm.standardModel.variableMetadata.designInsights.push({});
        }
        vm.removeDesignInsight = function(insight) {
            const index = vm.standardModel.variableMetadata.designInsights.indexOf(insight);
            vm.standardModel.variableMetadata.designInsights.splice(index, 1);
        }
        vm.cloneDesignInsight = function(insight) {
            // Legit just copying the notes.
            const index =  vm.standardModel.variableMetadata.designInsights.indexOf(insight);
            vm.standardModel.variableMetadata.designInsights.splice(index+1, 0, { notes: insight.notes });
        }
        vm.ensureInsightsAreUnique = function(insight, option, currentVariable){
            // For each insight in our list (excluding the current one) we check
            // to ensure all other insights do not contain the same combination
            // or options across both available variables (climateZone and
            // northOffset). If it's not unique, we pop the last option of the
            // list.
            setTimeout(() => {
                vm.standardModel.variableMetadata.designInsights.forEach(x => {
                    if (x === insight)
                        return;

                    let hasDuplicateClimateZone = false;
                    let hasDuplicateNorthOffset = false;

                    x.climateZones.forEach(cz => {
                        const match = insight.climateZones?.find(czb => czb === cz);
                        if (match != null)
                            hasDuplicateClimateZone = true;
                    });

                    x.northOffsets.forEach(cz => {
                        const match = insight.northOffsets?.find(czb => czb === cz);
                        if (match != null)
                            hasDuplicateNorthOffset = true;
                    });

                    if (hasDuplicateClimateZone && hasDuplicateNorthOffset){
                        common.logger.logWarning("Cannot feature design insights with duplicate data. Removing last input.", null, null, true);

                        const index = insight[currentVariable].indexOf(option);
                        insight[currentVariable].splice(index, 1);
                    }

                    // We should only need to compare it with the current insight.
                    // Others should all be unique as they are checked on every
                    // change also.
                });
            }, 100);
        }
        vm.copyDesignInsightsToVariation = function (insight, copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Design Insights ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(async function (selectedVariationId) {
                await standardmodelservice.copyDesignInsightsToVariation(selectedVariationId, [insight]);
                if (copyFrom) {
                    await standardmodelservice.copyDesignInsightsToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    let dbModel = await standardmodelservice.getStandardModel(vm.standardModel.standardHomeModelId);
                }
                else {
                    await standardmodelservice.copyDesignInsightsToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }
        vm.copyCostEstimatesToVariation = function (copyFrom = false) {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            modalScope.modalTitle = `Copy Cost Estimates ${copyFrom ? 'From' : 'To'} Variation`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-variation-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (selectedVariationId) {
                if (copyFrom) {
                    await standardmodelservice.copyCostEstimatesToVariation(selectedVariationId, vm.standardModel.standardHomeModelId);
                    let dbModel = await standardmodelservice.getStandardModel(vm.standardModel.standardHomeModelId);
                    for (const key in dbModel.variableMetadata.generalOptionData) {
                        for (let i = 0; i < dbModel.variableMetadata.generalOptionData[key].length; i++) {
                            vm.standardModel.variableMetadata.generalOptionData[key][i].costEstimateData = dbModel.variableMetadata.generalOptionData[key][i].costEstimateData;
                        }
                    }
                    vm.updateLinkedVarValues();
                }
                else {
                    await standardmodelservice.copyCostEstimatesToVariation(vm.standardModel.standardHomeModelId, selectedVariationId);
                }
            });
        }

        // Design Metadata
        vm.roundBathroomsToNearestHalf = function() {
            setTimeout(() => {
                vm.standardModel.numberOfBathrooms = common.roundToStep(vm.standardModel.numberOfBathrooms, 0.5)
            }, 50);
        }

        // Bulk Select
        vm.selectAllCheckboxes = (a, b, c) => {
            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                safeApply();
            }, 25);
        }
        vm.updateBulkSelectStatus = updateBulkSelectStatus;
        vm.launchCostEstimateBulkEdit = async function() {
            let modalScope = $rootScope.$new();
            modalScope.type = "model";
            let selectedItems = vm.flattenedCostData.filter(x => x.checkboxSelected);
            // IF all selected items have same Category, add this Category's Cost Item options
            if (selectedItems.every(x => x.costEstimateData.category == selectedItems[0].costEstimateData.category)) {
                modalScope.clientCostItems = vm.itemCodesForCategory(selectedItems[0].costEstimateData.category);
            }
            modalScope.zoneSummaryBuildingData = vm.standardModel.zoneSummaryBuildingData;
            modalScope.drawingAreas = vm.standardModel.drawingAreas;
            modalScope.drawingAreasTotals = vm.drawingAreasTotals;
            modalScope.zoneSummary = angular.copy(vm.zoneSummaryDataUnfiltered?.zoneSummary);
            modalScope.envelopeSummary = angular.copy(vm.zoneSummaryDataUnfiltered?.envelopeSummary);
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-cost-estimate-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {
                selectedItems.forEach(x => {

                    if (response.costItemCustomSelected) {
                        x.costEstimateData.costItemId = null;
                    }
                    else if (response.costItemId) {
                        x.costEstimateData.costItemId = response.costItemId;
                    }

                    if (response.quantity) {
                        x.costEstimateData.quantity = response.quantity;
                        x.costEstimateData.quantityVarRefJson = response.quantityVarRefJson;
                    }

                    // Only if Item Code was before or is now 'Custom', OR a new Item Code was just selected
                    if (x.costEstimateData.costItemId == null || response.costItemId) {
                        if (response.unitOfMeasure)
                            x.costEstimateData.unitOfMeasure = response.unitOfMeasure;

                        if (response.ratePerUnit)
                            x.costEstimateData.ratePerUnit = response.ratePerUnit;

                        if (response.margin)
                            x.costEstimateData.margin = response.margin;

                        if (response.rounding)
                            x.costEstimateData.rounding = response.rounding;

                        if (response.notes)
                            x.costEstimateData.notes = response.notes;
                    }

                    x.checkboxSelected = false;

                });
                vm.costEstimateBulkStatus.isIndeterminate = false;
                vm.costEstimateBulkStatus.selectAllCheckboxState = false;
            });
        }
        vm.launchDesignInsightBulkEdit = async function() {
            let modalScope = $rootScope.$new();
            modalScope.options = vm.standardModel.variableOptions;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.variationOfId = vm.standardModel.isVariationOfHomeModelId;
            modalScope.variationOptionsList = vm.variationOptionsList;
            modalScope.variationOptionsSettings = vm.variationOptionsSettings;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-design-insight-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {
                const bulkSelected = vm.standardModel.variableMetadata.designInsights.filter(x => x.checkboxSelected);
                if (response.bulkEditAction === "COPYTOMODEL") {
                    await standardmodelservice.copyDesignInsightsToVariation(response.selectedVariationId, bulkSelected);
                    bulkSelected.forEach(d => d.checkboxSelected = false);
                } else {
                    bulkSelected.forEach(x => {
                        if (response.bulkEditAction === "DELETE") {
                            vm.removeDesignInsight(x);
                            return;
                        } else if (response.bulkEditAction === "COPY") {
                            vm.cloneDesignInsight(x);
                            return;
                        }

                        if (response.climateZones)
                            x.climateZones = response.climateZones;

                        if (response.northOffsets)
                            x.northOffsets = response.northOffsets;

                        if (response.notes)
                            x.notes = response.notes;

                        x.checkboxSelected = false;
                    });
                }
                vm.designInsightsBulkStatus.isIndeterminate = false;
                vm.designInsightsBulkStatus.selectAllCheckboxState = false;
            });
        }

        // Attempt to navigate
        vm.navigateAttempt = async function (page, params) {
            if (vm.hasChanges()) {
                let modalScope = $rootScope.$new();
                modalScope.title = vm.standardModel.title;
                modalScope.bodyText = `You have unsaved changes for "${vm.standardModel.title}". Would you like to save before navigating?`;
                modalScope.buttons = [{
                    isPrimary: true,
                    text: "Save",
                    onClick: () => vm.save(() => $state.go(page, params))
                },{
                    text: "Don't Save",
                    onClick: () => $state.go(page, params)
                },{
                    isCancel: true,
                    text: "Cancel"
                }];
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                });
            } else {
                $state.go(page, params);
            }
        }

        // UI apply
        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        // - ---------------------- - //
        // - CANCEL / SAVE / DELETE - //
        // - ---------------------- - //

        // Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            } else {
                $state.go("standard-model-updateform", { standardHomeModelId: vm.standardModel.isVariationOfHomeModelId });
            }
        }

        // Save
        vm.save = function (finishedCallback) {
            vm.isBusy = true;
            // Remove "willDelete" Drawing Areas
            vm.standardModel.drawingAreas = vm.standardModel.drawingAreas.filter(a => !a.willDelete);
            if (vm.newRecord == true){
                standardmodelservice.createStandardModel(vm.standardModel).then(data => {
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                standardmodelservice.updateStandardModel(vm.standardModel).then(async () => {
                    if (vm.clearVariableOptions) {
                        await standardmodelservice.clearModelVariables(vm.standardModel.projectId, vm.standardModel.standardHomeModelId);
                        vm.clearVariableOptions = false;
                    }
                    vm.resetChangeDetection();
                    if (finishedCallback != null) {
                        finishedCallback();
                    }
                    vm.isBusy = false;
                });
            }
        }

        // Delete
        vm.delete = function () {
            vm.isBusy = true;
            standardmodelservice.deleteStandardModel(vm.standardModel.standardHomeModelId).then(() => {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        // Undo Delete
        vm.undoDelete = function () {
            vm.isBusy = true;
            standardmodelservice.undoDeleteStandardModel(vm.standardModel.standardHomeModelId).then(() => {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialise();

        // -------- //
        // - TEST - //
        // -------- //
        //vm.openScratchImportModal();
        // -------- //

    }
})();