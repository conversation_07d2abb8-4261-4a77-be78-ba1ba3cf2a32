// Name: zonetypeservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'zonetypeservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'zoneservice', zonetypeservice]);

    function zonetypeservice(common, config, $http, zoneservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        const baseUrl = config.servicesUrlPrefix + 'zonetype/';

        let nccClassificationList = [];
        zoneservice.getNccClassificationList().then((data) => { nccClassificationList = data; });

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getZoneType: getZoneType,
            createZoneType: createZoneType,
            updateZoneType: updateZoneType,
            deleteZoneType:deleteZoneType,
            undoDeleteZoneType: undoDeleteZoneType,
            determineProjectClassificationBasedOnZones: determineProjectClassificationBasedOnZones
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting ZoneType list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getZoneType(zoneTypeCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {zoneTypeCode: zoneTypeCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting ZoneType: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createZoneType(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Space Type Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created ZoneType: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateZoneType(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Space Type Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating ZoneType: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteZoneType(zoneTypeCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { zoneTypeCode: zoneTypeCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting ZoneType: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteZoneType(zoneTypeCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { zoneTypeCode: zoneTypeCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for ZoneType: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /**
         * Determines the desired Project Classification (Project type) of an Assessment 
         * based on the given zones within the building.
         * 
         * @param {any} zones The Zones (formerly Artificial Lighting) to base upon.
         * @returns {string} The determined CODE of the desired ProjectClassification.
         */
        function determineProjectClassificationBasedOnZones(zones) {

            if (zones == null || zones.length == 0)
                return null;

            // For each nccClassification zone that exists, check all zones to see if one of them is off that type.
            // If it is, add it to our array.
            // THEN, for each item in the array, join them together with a ',' unless the next one will be the last,
            // in which case join with an 'and'.
            let found = [];
            for (let i = 0; i < nccClassificationList.length; i++) {
                let classification = nccClassificationList[i];

                // Check all zones.
                for (let y = 0; y < zones.length; y++) {
                    let zone = zones[y];

                    // If matching, add to list and break out.
                    if (zone.nccClassification?.nccClassificationCode === classification.nccClassificationCode) {
                        found.push(classification);
                        break; // Continue to check next classification against zones.
                    }
                }
            }

            if (found.length == 0)
                return "";

            // Simply return first description.
            if (found.length == 1) {
                return found[0].description;
            }

            let final = "";
            for (let i = 0; i < found.length; i++) {

                final += found[i].description;

                // The next will be the last, so append 'and' between values.
                if (i + 2 == found.length) {
                    final += " and ";
                } else if (i + 1 == found.length) {
                    // This IS the last, so append nothing
                } else {
                    // append ', '
                    final += ", ";
                }
            }

            return final;
        }
    }
})();
