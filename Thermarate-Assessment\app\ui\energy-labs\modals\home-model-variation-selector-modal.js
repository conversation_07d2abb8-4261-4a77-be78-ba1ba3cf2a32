(function () {
    'use strict';
    var controllerId = 'HomeModelVariationSelectorModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'standardmodelservice', copyDesignToHomeModelModalController]);
    function copyDesignToHomeModelModalController(common, $scope, $mdDialog, standardmodelservice) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;
        vm.isBusy = true;

        vm.modalTitle = $scope.modalTitle;
        vm.thisHomeModelId = $scope.thisHomeModelId;
        vm.variationOfId = $scope.variationOfId;
        vm.variationOptionsList = $scope.variationOptionsList;
        vm.variationOptionsSettings = $scope.variationOptionsSettings;

        vm.selectedVariationId = null;

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Confirm
        vm.confirm = function () {
            $mdDialog.hide(vm.selectedVariationId);
        }

        // Cancel
        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();