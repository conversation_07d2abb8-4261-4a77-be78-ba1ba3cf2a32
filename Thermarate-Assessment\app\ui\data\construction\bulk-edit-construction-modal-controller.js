(function () {
    'use strict';
    var controllerId = 'BulkEditConstructionModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', bulkEditConstructionModalController]);
    function bulkEditConstructionModalController(common, $scope, $mdDialog) {

        // - VARIABLES - //

        let vm = this;
        vm.isBusy = false;

        vm.data = { 
            bulkEditAction: "COPY"
        };

        // - HANDLES - //

        vm.confirm = function () {
            $mdDialog.hide(vm.data.bulkEditAction);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();
