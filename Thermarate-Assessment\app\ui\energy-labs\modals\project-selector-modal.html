<form name="ProjectSelectorModal"
      data-ng-controller='ProjectSelectorModalCtrl as vm'>

    <div data-cc-widget-header
         data-title="{{vm.title}}"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div class="content-container">

        <div class="select-text">
            Select a Project
        </div>

        <div class="project-selection-container">
            <div class="project-item {{project.selected ? 'project-selected' : ''}}"
                 ng-repeat="project in vm.projectList track by project.projectId"
                 ng-click="vm.selectProject(project)">
                {{project.projectName}}
            </div>
        </div>

        <div data-cc-widget-button-bar
             layout="row"
             class="buttons-container">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()"
                       ng-disabled="vm.selectedProjectId == null">
                Confirm
            </md-button>

            <md-button class="md-raised" ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>
    .content-container {
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        row-gap: 20px;
    }

    .select-text {
        font-size: 14px;
        text-align: left;
    }

    .project-selection-container {
        margin-top: 15px;
        width: 100%;
        height: 200px;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 8px;
        background-color: #eeeeee;
        font-size: 16px;
        overflow: auto;
    }

    .project-item {
        height: 20px;
        padding: 7px 11px;
        border-radius: 5px;
        cursor: pointer;
    }

    .project-item:hover {
        background-color: #bcbcbc;
    }

    .project-selected,
    .project-selected:hover {
        background-color: #bfdba1;
    }

    .buttons-container {
        margin-top: 30px;
    }
</style>
