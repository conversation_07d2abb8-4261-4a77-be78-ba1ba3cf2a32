// Name: fileservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'fileservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', fileservice]);

    function fileservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'file/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getFile: getFile,
            getByCategory: getByCategory,
            createFile: createFile,
            updateFile: updateFile,
            deleteFile:deleteFile,
            deleteForCategory:deleteForCategory,
            undoDeleteFile: undoDeleteFile,
            deleteSplitFiles: deleteSplitFiles,
            fileDownloadUrl,
            downloadFile,
            downloadFileForceDialog,
            zipFilesFromIds,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting File list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getFile(fileId) {
            return $http({
                url: baseUrl + 'Get',
                params: {fileId: fileId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getByCategory(category) {
            return $http({
                url: baseUrl + 'GetByCategory',
                params: {category: category},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createFile(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("File Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateFile(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("File Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteFile(fileId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { fileId: fileId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteForCategory(category) {
            return $http({
                url: baseUrl + 'DeleteForCategory',
                params: { category: category },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteSplitFiles(fileId, fileIds) {
            return $http({
                url: baseUrl + 'DeleteSplitFiles',
                params: { parentFileId: fileId },
                data: JSON.stringify(fileIds),
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Files: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteFile(fileId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { fileId: fileId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        
        function fileDownloadUrl(fileId) {
            return baseUrl + 'GetDownloadItem?fileId=' + fileId;
        }

        function downloadFile(fileDto, fileName = null) {
            var a = document.createElement('a');
            a.href = fileDto.url;
            if (fileName != null) {
                a.download = fileName;
            }
            a.target = '_blank';
            a.click();
        }

        function downloadFileForceDialog(fileDto) {
            var a = document.createElement('a');
            a.href = fileDownloadUrl(fileDto.fileId);
            a.download = fileDto.fileName;
            a.click();
        }
        
        function zipFilesFromIds(fileIds) {
            return $http({
                url: baseUrl + 'ZipFilesFromIds',
                data: JSON.stringify(fileIds),
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting File: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
  
    }
})();
