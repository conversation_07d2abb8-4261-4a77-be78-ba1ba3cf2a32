/*
* filter list returning records that have a match on all words in the filter value.
* the filter value is split into words based on spaces.
* all properties in each list record are checked for a possible match.
*/
angular.module('app').filter('matchOnStartingWord', function () {
    return function (records, word) {

        return records.filter(function (record) {
            if (!checkObjectHasStartingValue(record, word)) {
                return false;  // Word not found in record.
            }
            return true;
        });
    };

    function checkObjectHasStartingValue(record, value) {
        if (typeof record === 'string' || record instanceof String) {
            if ((record + "").toLowerCase().indexOf((value + "").toLowerCase()) === 0) {
                return true
            }
        }
        else {
            for (var prop in record) {
                if (record.hasOwnProperty(prop)) {
                    if ((record[prop] + "").toLowerCase().indexOf((value + "").toLowerCase()) === 0) {
                        return true
                    }
                }
            }
        }
        return false;
    }
});