﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;

namespace TenureExtraction
{
    public class StandardHomeModelExtractor
    {
        private const int START_ROW = 2;

        /// <summary>
        /// Extract contents of excel file data stream and insert into database.
        /// </summary>
        public static void Extract(string connectionString, Stream fileStream, Guid standardHomeModelId)
        {
            using (ExcelPackage package = new ExcelPackage(fileStream))
            {
                InnerExtract(connectionString, package, standardHomeModelId);
            }
        }
        
        /// <summary>
        /// Extract contents of excel file at given path and insert into database.
        /// </summary>
        public static void Extract(string connectionString, string pathToExcelFile, Guid standardHomeModelId)
        {
            FileInfo existingFile = new FileInfo(pathToExcelFile);
            using (ExcelPackage package = new ExcelPackage(existingFile))
            {
                InnerExtract(connectionString, package, standardHomeModelId);
            }
        }

        private static void InnerExtract(string connectionString, ExcelPackage package, Guid standardHomeModelId)
        {
            var rows = new List<StandardHomeModelOptionRow>();
            
            ExcelWorksheet worksheet = package.Workbook.Worksheets[0];

            int currentRow = START_ROW;
            
            

            try
            {
                do
                {
                    var row = new StandardHomeModelOptionRow();

                    object climateZoneRaw = worksheet.Cells[currentRow, Columns.ClimateZone].Value;

                    if (climateZoneRaw == null)
                        break;


                    row.NatHERSClimateZone = Convert.ToByte(climateZoneRaw);
                    row.SiteExposure = (string)worksheet.Cells[currentRow, Columns.SiteExposure].Value;
                    row.FloorHeight = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.FloorHeight].Value);

                    // Remove annoying "degree" symbol.
                    string northOffsetStr = (string)worksheet.Cells[currentRow, Columns.NorthOffset].Value;
                    northOffsetStr = northOffsetStr.Substring(0, northOffsetStr.Length - 1); 
                    row.NorthOffset = Convert.ToInt16(northOffsetStr);
                    
                    row.AssessmentMethod = (string)worksheet.Cells[currentRow, Columns.AssessmentMethod].Value;
                    row.BlockType = (string)worksheet.Cells[currentRow, Columns.BlockType].Value;
                    row.RoofConstruction = (string)worksheet.Cells[currentRow, Columns.RoofConstruction].Value;
                    row.RoofInsulation = (string)worksheet.Cells[currentRow, Columns.RoofInsulation].Value;
                    row.RoofSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.RoofSolarAbsorptance].Value);
                    row.CeilingConstruction = (string)worksheet.Cells[currentRow, Columns.CeilingConstruction].Value;
                    row.CeilingInsulation = (string)worksheet.Cells[currentRow, Columns.CeilingInsulation].Value;
                    row.ExteriorWallConstruction = (string)worksheet.Cells[currentRow, Columns.ExteriorWallConstruction].Value;
                    row.ExteriorWallInsulation = (string)worksheet.Cells[currentRow, Columns.ExteriorWallInsulation].Value;
                    row.ExteriorWallSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.ExteriorWallSolarAbsorptance].Value);
                    row.InteriorWallConstruction = (string)worksheet.Cells[currentRow, Columns.InteriorWallConstruction].Value;
                    row.InteriorWallInsulation = (string)worksheet.Cells[currentRow, Columns.InteriorWallInsulation].Value;
                    row.InteriorWallSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.InteriorWallSolarAbsorptance].Value);
                    row.ExteriorGlazing = (string)worksheet.Cells[currentRow, Columns.ExteriorGlazing].Value;
                    row.ExteriorGlazingFrameSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.ExteriorGlazingFrameSolarAbsorptance].Value);
                    row.ExteriorDoorSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.ExteriorDoorSolarAbsorptance].Value);
                    row.GarageDoorSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.GarageDoorSolarAbsorptance].Value);
                    row.FloorConstruction = (string)worksheet.Cells[currentRow, Columns.FloorConstruction].Value;
                    row.FloorInsulation = (string)worksheet.Cells[currentRow, Columns.FloorInsulation].Value;
                    row.FloorCoverings = (string)worksheet.Cells[currentRow, Columns.FloorCoverings].Value;
                    row.FloorSolarAbsorptance = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.FloorSolarAbsorptance].Value);
                    row.CeilingFans = (string)(worksheet.Cells[currentRow, Columns.CeilingFans].Value);
                    row.RecessedLightFittings = (string)(worksheet.Cells[currentRow, Columns.RecessedLightFittings].Value);
                    row.HeatingLoad = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.HeatingLoad].Value);
                    row.CoolingLoad = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.CoolingLoad].Value);
                    row.TotalEnergyLoad = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.TotalEnergyLoad].Value);
                    row.EnergyRating = Convert.ToDecimal(worksheet.Cells[currentRow, Columns.EnergyRating].Value);
                    row.Active = (string)worksheet.Cells[currentRow, Columns.Active].Value == "true";
                    row.Description = (string)(worksheet.Cells[currentRow, Columns.Description].Value);
                    row.Comments = (string)(worksheet.Cells[currentRow, Columns.Comments].Value);
                    
                    row.Row = currentRow;

                    rows.Add(row);
                    
                    currentRow++;

                } while (true);
            }
            catch (Exception e)
            {
                ;
                throw;
            }
            
            // Now that we have compiled all the new data successfully, we:
            // 1. Delete existing data
            // 2. Insert new data.
            // 3. Determine which options are available to select from for which variables, and then insert them into
            //    a json array or something that the parent StandardHomeModelRow can decipher? json, comma-delimited..?

            SqlConnection connection = null;
            SqlTransaction transaction = null;
            try
            {
                connection = new SqlConnection(connectionString);
                connection.Open();
                
                transaction = connection.BeginTransaction();
                
                var cmd = new SqlCommand(
                    "DELETE FROM dbo.RSS_StandardHomeModelOption WHERE StandardHomeModelId = @StandardHomeModelId", 
                    connection, 
                    transaction);
                
                cmd.Parameters.AddWithValue("StandardHomeModelId", standardHomeModelId);

                cmd.CommandTimeout = 600;
                cmd.ExecuteNonQuery();
                
                // Use a Datatable w/ bulk copy for the rest of the rows.
                DataTable dataTable = new DataTable("RSS_StandardHomeModelOption", "dbo");
                dataTable.Columns.Add(new DataColumn("StandardHomeModelOptionId", typeof(Guid)));
                dataTable.Columns.Add(new DataColumn("StandardHomeModelId", typeof(Guid)));
                dataTable.Columns.Add(new DataColumn("AssessmentMethod", typeof(string)));
                dataTable.Columns.Add(new DataColumn("NatHERSClimateZone", typeof(byte)));
                dataTable.Columns.Add(new DataColumn("SiteExposure", typeof(string)));
                dataTable.Columns.Add(new DataColumn("FloorHeight", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("NorthOffset", typeof(short)));
                dataTable.Columns.Add(new DataColumn("BlockType", typeof(string)));
                dataTable.Columns.Add(new DataColumn("RoofConstruction", typeof(string)));
                dataTable.Columns.Add(new DataColumn("RoofInsulation", typeof(string)));
                dataTable.Columns.Add(new DataColumn("RoofSolarAbsorptance", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("CeilingConstruction", typeof(string)));
                dataTable.Columns.Add(new DataColumn("CeilingInsulation", typeof(string)));
                dataTable.Columns.Add(new DataColumn("ExteriorWallConstruction", typeof(string)));
                dataTable.Columns.Add(new DataColumn("ExteriorWallInsulation", typeof(string)));
                dataTable.Columns.Add(new DataColumn("ExteriorWallSolarAbsorptance", typeof(string)));
                dataTable.Columns.Add(new DataColumn("InteriorWallConstruction", typeof(string)));
                dataTable.Columns.Add(new DataColumn("InteriorWallInsulation", typeof(string)));
                dataTable.Columns.Add(new DataColumn("InteriorWallSolarAbsorptance", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("ExteriorGlazing", typeof(string)));
                dataTable.Columns.Add(new DataColumn("ExteriorGlazingFrameSolarAbsorptance", typeof(string)));
                dataTable.Columns.Add(new DataColumn("FloorConstruction", typeof(string)));
                dataTable.Columns.Add(new DataColumn("FloorInsulation", typeof(string)));
                dataTable.Columns.Add(new DataColumn("FloorCoverings", typeof(string)));
                dataTable.Columns.Add(new DataColumn("FloorSolarAbsorptance", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("HeatingLoad", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("CoolingLoad", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("TotalEnergyLoad", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("EnergyRating", typeof(decimal)));
                dataTable.Columns.Add(new DataColumn("Row", typeof(int)));
                dataTable.Columns.Add(new DataColumn("Active", typeof(bool))); 
                dataTable.Columns.Add(new DataColumn("Description", typeof(string)));
                dataTable.Columns.Add(new DataColumn("Comments", typeof(string)));
                dataTable.Columns.Add(new DataColumn("CeilingFans", typeof(string)));
                dataTable.Columns.Add(new DataColumn("RecessedLightFittings", typeof(string)));
                dataTable.Columns.Add(new DataColumn("ExteriorDoorSolarAbsorptance", typeof(string)));
                dataTable.Columns.Add(new DataColumn("GarageDoorSolarAbsorptance", typeof(string)));

                SqlBulkCopy bulkCopy = new SqlBulkCopy(connection, SqlBulkCopyOptions.Default, transaction);
                bulkCopy.DestinationTableName = "RSS_StandardHomeModelOption";
                bulkCopy.BulkCopyTimeout = bulkCopy.BulkCopyTimeout = 600; // Seconds;
                
                bulkCopy.ColumnMappings.Add("StandardHomeModelOptionId", "StandardHomeModelOptionId");
                bulkCopy.ColumnMappings.Add("StandardHomeModelId", "StandardHomeModelId");
                bulkCopy.ColumnMappings.Add("AssessmentMethod", "AssessmentMethod");
                bulkCopy.ColumnMappings.Add("NatHERSClimateZone", "NatHERSClimateZone");
                bulkCopy.ColumnMappings.Add("SiteExposure", "SiteExposure");
                bulkCopy.ColumnMappings.Add("FloorHeight", "FloorHeight");
                bulkCopy.ColumnMappings.Add("NorthOffset", "NorthOffset");
                bulkCopy.ColumnMappings.Add("BlockType", "BlockType");
                bulkCopy.ColumnMappings.Add("RoofConstruction", "RoofConstruction");
                bulkCopy.ColumnMappings.Add("RoofInsulation", "RoofInsulation");
                bulkCopy.ColumnMappings.Add("RoofSolarAbsorptance", "RoofSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("CeilingConstruction", "CeilingConstruction");
                bulkCopy.ColumnMappings.Add("CeilingInsulation", "CeilingInsulation");
                bulkCopy.ColumnMappings.Add("ExteriorWallConstruction", "ExteriorWallConstruction");
                bulkCopy.ColumnMappings.Add("ExteriorWallInsulation", "ExteriorWallInsulation");
                bulkCopy.ColumnMappings.Add("ExteriorWallSolarAbsorptance", "ExteriorWallSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("InteriorWallConstruction", "InteriorWallConstruction");
                bulkCopy.ColumnMappings.Add("InteriorWallInsulation", "InteriorWallInsulation");
                bulkCopy.ColumnMappings.Add("InteriorWallSolarAbsorptance", "InteriorWallSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("ExteriorGlazing", "ExteriorGlazing");
                bulkCopy.ColumnMappings.Add("ExteriorGlazingFrameSolarAbsorptance", "ExteriorGlazingFrameSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("FloorConstruction", "FloorConstruction");
                bulkCopy.ColumnMappings.Add("FloorInsulation", "FloorInsulation");
                bulkCopy.ColumnMappings.Add("FloorCoverings", "FloorCoverings");
                bulkCopy.ColumnMappings.Add("FloorSolarAbsorptance", "FloorSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("HeatingLoad", "HeatingLoad");
                bulkCopy.ColumnMappings.Add("CoolingLoad", "CoolingLoad");
                bulkCopy.ColumnMappings.Add("TotalEnergyLoad", "TotalEnergyLoad");
                bulkCopy.ColumnMappings.Add("EnergyRating", "EnergyRating");
                bulkCopy.ColumnMappings.Add("Row", "Row");
                bulkCopy.ColumnMappings.Add("Active", "Active");
                bulkCopy.ColumnMappings.Add("Description", "Description");
                bulkCopy.ColumnMappings.Add("Comments", "Comments");
                bulkCopy.ColumnMappings.Add("CeilingFans", "CeilingFans");
                bulkCopy.ColumnMappings.Add("RecessedLightFittings", "RecessedLightFittings");
                bulkCopy.ColumnMappings.Add("ExteriorDoorSolarAbsorptance", "ExteriorDoorSolarAbsorptance");
                bulkCopy.ColumnMappings.Add("GarageDoorSolarAbsorptance", "GarageDoorSolarAbsorptance");

                foreach (var option in rows)
                {
                    DataRow dr = dataTable.NewRow();

                    dr["StandardHomeModelOptionId"] = Guid.NewGuid();
                    dr["StandardHomeModelId"] = standardHomeModelId;
                    dr["AssessmentMethod"] = option.AssessmentMethod;
                    dr["NatHERSClimateZone"] = option.NatHERSClimateZone;
                    dr["SiteExposure"] = option.SiteExposure;
                    dr["FloorHeight"] = option.FloorHeight;
                    dr["NorthOffset"] = option.NorthOffset;
                    dr["BlockType"] = option.BlockType;
                    dr["RoofConstruction"] = option.RoofConstruction;
                    dr["RoofInsulation"] = option.RoofInsulation;
                    dr["RoofSolarAbsorptance"] = option.RoofSolarAbsorptance;
                    dr["CeilingConstruction"] = option.CeilingConstruction;
                    dr["CeilingInsulation"] = option.CeilingInsulation;
                    dr["ExteriorWallConstruction"] = option.ExteriorWallConstruction;
                    dr["ExteriorWallInsulation"] = option.ExteriorWallInsulation;
                    dr["ExteriorWallSolarAbsorptance"] = option.ExteriorWallSolarAbsorptance;
                    dr["InteriorWallConstruction"] = option.InteriorWallConstruction;
                    dr["InteriorWallInsulation"] = option.InteriorWallInsulation;
                    dr["InteriorWallSolarAbsorptance"] = option.InteriorWallSolarAbsorptance;
                    dr["ExteriorGlazing"] = option.ExteriorGlazing;
                    dr["ExteriorGlazingFrameSolarAbsorptance"] = option.ExteriorGlazingFrameSolarAbsorptance;
                    dr["FloorConstruction"] = option.FloorConstruction;
                    dr["FloorInsulation"] = option.FloorInsulation;
                    dr["FloorCoverings"] = option.FloorCoverings;
                    dr["FloorSolarAbsorptance"] = option.FloorSolarAbsorptance;
                    dr["HeatingLoad"] = option.HeatingLoad;
                    dr["CoolingLoad"] = option.CoolingLoad;
                    dr["TotalEnergyLoad"] = option.TotalEnergyLoad;
                    dr["EnergyRating"] = option.EnergyRating;
                    dr["Row"] = option.Row;
                    dr["Active"] = option.Active;
                    dr["Description"] = option.Description;
                    dr["Comments"] = option.Comments;
                    dr["CeilingFans"] = option.CeilingFans;
                    dr["RecessedLightFittings"] = option.RecessedLightFittings;
                    dr["ExteriorDoorSolarAbsorptance"] = option.ExteriorDoorSolarAbsorptance;
                    dr["GarageDoorSolarAbsorptance"] = option.GarageDoorSolarAbsorptance;

                    dataTable.Rows.Add(dr);
                }
                
                
                bulkCopy.WriteToServer(dataTable);
                
                // Ok now all our data has been written to the server. For each known option for this model, we need to
                // determine which variables have multiple options, and then store that for use later in the UI.
                // We also need to assign defaults that have been set at the client level.

                var variableOptionsJson = DetermineDistinctVariableOptions(connection, transaction, standardHomeModelId);

                cmd = new SqlCommand(
                    "UPDATE dbo.RSS_StandardHomeModel SET VariableOptionsJson = @VariableOptionsJson WHERE StandardHomeModelId = @StandardHomeModelId",
                    connection, 
                    transaction);

                cmd.Parameters.AddWithValue("VariableOptionsJson", variableOptionsJson.ToString());
                cmd.Parameters.AddWithValue("StandardHomeModelId", standardHomeModelId);

                cmd.CommandTimeout = 600;
                cmd.ExecuteNonQuery();

                transaction.Commit();
                
            }
            catch (Exception e)
            {
                transaction?.Rollback();
                throw;
            }
            finally
            {
                transaction?.Dispose();
                
                connection?.Close();
                connection?.Dispose();
            }
        }

        public static void DetermineDistinctVariableOptions(string connectionString, Guid standardHomeModelId)
        {
         
            var connection = new SqlConnection(connectionString);
            
            try
            {
                connection.Open();
                var transaction = connection.BeginTransaction();
                DetermineDistinctVariableOptions(connection, transaction, standardHomeModelId);
                transaction.Commit();
                transaction.Dispose();
            }
            catch (Exception e)
            {
                ;
            }
            finally
            {
                connection.Close();
                connection.Dispose();
            }
            
        }

        private static JObject DetermineDistinctVariableOptions(SqlConnection connection, SqlTransaction transaction, Guid standardHomeModelId)
        {
            var variableOptionsJson = new JObject();
            
            var type = typeof(StandardHomeModelOptionRow);
            var props = type.GetProperties();

            foreach (var prop in props)
            {
                // We will never be searching on these lol.
                var shouldSkip = prop.GetCustomAttributes(typeof(IgnoreDistinctCheckAttribute), true).Any();
                    
                if (shouldSkip)
                    continue;

                var jsonArray = new JArray();
                
                string sql =
                    $"SELECT DISTINCT {prop.Name} FROM dbo.RSS_StandardHomeModelOption WHERE StandardHomeModelId = @StandardHomeModelId ORDER BY {prop.Name} ASC";
                
                var cmd = new SqlCommand(
                    sql,
                    connection,
                    transaction);
                
                cmd.Parameters.AddWithValue("StandardHomeModelId", standardHomeModelId);

                cmd.CommandTimeout = 600;
                var reader = cmd.ExecuteReader();

                while (reader.Read())
                {
                    jsonArray.Add(reader[prop.Name]);
                }
                
                reader.Close();
                
                variableOptionsJson.Add(prop.Name.ToCamelCase(), jsonArray);

            }


            
            return variableOptionsJson;
        }

        public class StandardHomeModelOptionData
        {
            public string AssessmentMethod { get; set; }
            public byte NatHERSClimateZone { get; set; }              
            
            public string SiteExposure { get; set; }
            public decimal FloorHeight { get; set; }
            
            public short NorthOffset { get; set; }              
            public string BlockType { get; set; }                
            public string RoofConstruction { get; set; }         
            public string RoofInsulation { get; set; }           
            public decimal RoofSolarAbsorptance { get; set; }     
            
            public string CeilingConstruction { get; set; }     
            public string CeilingInsulation { get; set; }        
            public string ExteriorWallConstruction { get; set; } 
            public string ExteriorWallInsulation { get; set; }   
            public decimal ExteriorWallSolarAbsorptance { get; set; }   
            
            public string InteriorWallConstruction { get; set; } 
            public string InteriorWallInsulation { get; set; }   
            public decimal InteriorWallSolarAbsorptance { get; set; }
            
            public string ExteriorGlazing { get; set; }          
            public decimal ExteriorGlazingFrameSolarAbsorptance { get; set; }          
            public string FloorConstruction { get; set; }        
            public string FloorInsulation { get; set; }          
            public string FloorCoverings { get; set; }
            public decimal FloorSolarAbsorptance { get; set; }

            public string CeilingFans { get; set; }
            public string RecessedLightFittings { get; set; }

            public decimal ExteriorDoorSolarAbsorptance { get; set; }
            public decimal GarageDoorSolarAbsorptance { get; set; }
        }

        public class StandardHomeModelOptionRow : StandardHomeModelOptionData
        {
            [IgnoreDistinctCheck]
            public Guid StandardHomeModelOptionId { get; set; }
            
            [IgnoreDistinctCheck]
            public Guid StandardHomeModelId { get; set; }

            [IgnoreDistinctCheck]
            public decimal HeatingLoad { get; set; } 
            
            [IgnoreDistinctCheck]
            public decimal CoolingLoad { get; set; } 
            
            [IgnoreDistinctCheck]
            public decimal TotalEnergyLoad { get; set; }  
            
            [IgnoreDistinctCheck]
            public decimal EnergyRating { get; set; }             
            
            [IgnoreDistinctCheck]
            public int Row { get; set; }

            [IgnoreDistinctCheck] 
            public bool Active { get; set; }
        
            [IgnoreDistinctCheck]
            public string Description { get; set; }
            
            [IgnoreDistinctCheck]
            public string Comments { get; set; }
        }

        private class IgnoreDistinctCheckAttribute : Attribute
        {
        }

        internal static class Columns
        {
            internal const ushort AssessmentMethod = 3;
            internal const ushort ClimateZone = 4;
            internal const ushort SiteExposure = 5;
            internal const ushort FloorHeight = 6;
            
            internal const ushort NorthOffset = 7;
            internal const ushort BlockType = 8;
            internal const ushort RoofConstruction = 9;
            internal const ushort RoofInsulation = 10;
            internal const ushort RoofSolarAbsorptance = 11;
            internal const ushort CeilingConstruction = 12;
            internal const ushort CeilingInsulation = 13;
            internal const ushort ExteriorWallConstruction = 14;
            internal const ushort ExteriorWallInsulation = 15;
            internal const ushort ExteriorWallSolarAbsorptance = 16;
            internal const ushort InteriorWallConstruction = 17;
            internal const ushort InteriorWallInsulation = 18;
            internal const ushort InteriorWallSolarAbsorptance = 19;
            
            internal const ushort ExteriorGlazing = 20;
            internal const ushort ExteriorGlazingFrameSolarAbsorptance = 21;
            internal const ushort ExteriorDoorSolarAbsorptance = 22;
            internal const ushort GarageDoorSolarAbsorptance = 23;
            internal const ushort FloorConstruction = 24;
            internal const ushort FloorInsulation = 25;
            internal const ushort FloorCoverings = 26;
            internal const ushort FloorSolarAbsorptance = 27;

            internal const ushort CeilingFans = 28;
            internal const ushort RecessedLightFittings = 29;

            internal const ushort Active = 30;
            internal const ushort Description = 31;
            
            internal const ushort EnergyRating = 32;
            internal const ushort HeatingLoad = 33;
            internal const ushort CoolingLoad = 34;
            internal const ushort TotalEnergyLoad = 35;
            
            internal const ushort Comments = 36;

        }
    }
    
}
