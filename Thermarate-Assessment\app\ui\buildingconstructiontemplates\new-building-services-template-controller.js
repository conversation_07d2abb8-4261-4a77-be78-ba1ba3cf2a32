(function () {
    'use strict';
    var controllerId = 'NewBuildingServicesTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$scope', '$mdDialog', '$state', 'buildingservicestemplateservice', newBuildingServicesTemplateCtrl]);
    function newBuildingServicesTemplateCtrl($scope, $mdDialog, $state, buildingservicestemplateservice) {
        var vm = this;
        vm.isBusy = false;
        //keep track of radio button selected.
        // Values are 'new' and 'copy'
        vm.currentTemplateType = "new";
        //currently selected template when teh radio is on the 'copy' value. Selected from 'vm.currentTemplatesList' 
        vm.selectedTemplate = null;

        vm.currentTemplatesList = [];
        buildingservicestemplateservice.getAll().then(function (data) {
            vm.currentTemplatesList = data;
        });

        vm.submit = function () {
            vm.isBusy = true;
            switch (vm.currentTemplateType) {
                case "new":
                    buildingservicestemplateservice.createEmpty().then(function (data) {
                        console.log("Just created new template, data is:");
                        console.log(data);
                        vm.isBusy = false;
                        $state.go("buildingservicestemplate", { templateId: data.buildingServicesTemplateId });
                        $mdDialog.hide();
                    });
                    break;
                case "copy":
                    buildingservicestemplateservice.copyTemplate(vm.selectedTemplate.buildingServicesTemplateId).then(function (templateId) {
                        vm.isBusy = false;
                        $state.go("buildingservicestemplate", { templateId: templateId });
                        $mdDialog.hide();
                    });
                    break;
            }
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }
    }
})();