﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive("phoneFormat", function () {
        return {
            restrict: "A",
            require: "ngModel",
            link: function (scope, element, attr, ngModelCtrl) {
                //Parsing is performed from angular from view to model (e.g. update input box)
                //Sounds like you just want the number without the hyphens, so take them out with replace
                var maxLength = 11;
                var excludePrefixes = [];
                if (attr.isServiceNumber != undefined) {
                    maxLength = 10;
                    excludePrefixes = ['13', '18', '19'];
                }
                var phoneParse = function (value) {
                    if (value != undefined) {
                        var numbers = value && value.replace(/-/g, "");
                        var numbers = numbers && numbers.replace(' ', "");
                        if (numbers.length == 11 && maxLength >= 11) {
                            if (/^\d{11}$/.test(numbers)) {
                                return numbers;
                            }
                        }
                        else if (numbers.length == 10) {
                            if (excludePrefixes.length > 0) {
                                for (var i = 0, len = excludePrefixes.length; i < len; i++) {
                                    if (excludePrefixes[i] == numbers.substr(0, excludePrefixes[i].length)) {
                                        return "";
                                    }
                                }
                            }
                            if (/^\d{10}$/.test(numbers)) {
                                return numbers;
                            }
                        }
                        else if (numbers.length == 0) {
                            return "";
                        }
                    }
                    return "";
                }
                //Formatting is done from view to model (e.g. when you set $scope.telephone)
                //Function to insert hyphens if 10 digits were entered.
                var phoneFormat = function (value) {
                    if (value != null) {
                        var numbers = value && value.replace(/-/g, "");
                        numbers = numbers && numbers.replace(/[\s]/g, "");
                        if (numbers.length == 11 && maxLength >= 11) {
                            var matches = numbers && numbers.substring(0, 11) != "00000000000" && numbers.match(/^(\d{2})(\d{1})(\d{4})(\d{4})$/);
                            if (matches) {
                                return matches[1] + "-" + matches[2] + "-" + matches[3] + "-" + matches[4];
                            }
                        }
                        else if (numbers.length == 10
                                 && numbers.substring(0, 2) != "19"
                                 && numbers.substring(0, 2) != "18"
                                 && numbers.substring(0, 2) != "13"
                                 && numbers.substring(0, 10) != "0000000000") {
                            var matches = numbers && numbers.match(/^(\d{2})(\d{4})(\d{4})$/);
                            if (matches) {
                                return matches[1] + "-" + matches[2] + "-" + matches[3];
                            }
                        }
                        else if (numbers.length == 0) {
                            return numbers;
                        }
                        return undefined;
                    }
                }

                //Add these functions to the formatter and parser pipelines
                ngModelCtrl.$parsers.push(phoneParse);
                ngModelCtrl.$formatters.push(phoneFormat);
                //Since you want to update the error message on blur, call $setValidity on blur
                element.bind("keyup", function () {
                    var value = phoneFormat(element.val());
                    var isValid = !!value;
                    if (isValid) {
                        ngModelCtrl.$setViewValue(value);
                        ngModelCtrl.$render();
                    }
                    if (element.val() == "") {
                        isValid = true;
                    }
                    ngModelCtrl.$setValidity("validatePhoneField", isValid);
                    //call scope.$apply() since blur event happens "outside of angular"
                    scope.$apply();
                });
            }
        };
    });
})();