(function () {
    // The ManufacturerUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ManufacturerUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'manufacturerservice', 'security', manufacturerUpdateController]);
function manufacturerUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  manufacturerservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Manufacturer';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.manufacturerId = null;
        vm.manufacturer = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Manufacturer";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.manufacturerId = $scope.manufacturerId;
            }
            vm.hideActionBar = true;
        } else {
            vm.manufacturerId = $stateParams.manufacturerId;
        }

        // Get data for object to display on page
        var manufacturerIdPromise = null;
        if (vm.manufacturerId != null) {
            manufacturerIdPromise = manufacturerservice.getManufacturer(vm.manufacturerId)
            .then(function (data) {
                if (data != null) {
                    vm.manufacturer = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("manufacturer-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                manufacturerservice.createManufacturer(vm.manufacturer).then(function(data){
                    vm.manufacturer = data;
                    vm.manufacturerId = vm.manufacturer.manufacturerId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                manufacturerservice.updateManufacturer(vm.manufacturer).then(function(data){
                    if (data != null) {
                        vm.manufacturer = data;
                        vm.manufacturerId = vm.manufacturer.manufacturerId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            manufacturerservice.deleteManufacturer(vm.manufacturerId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            manufacturerservice.undoDeleteManufacturer(vm.manufacturerId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();