// Name: assessmentcomplianceoptionservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'assessmentcomplianceoptionservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'zonetypeservice', 'constructionservice', 'zoneservice', assessmentcomplianceoptionservice]);

    function assessmentcomplianceoptionservice(common, config, $http, zonetypeservice, constructionservice, zoneservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'assessmentcomplianceoption/';

        let kFloorNames = [
            "Ground Floor",
            "First Floor",
            "Second Floor",
            "Third Floor",
            "Fourth Floor",
        ];

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getAssessmentComplianceOption: getAssessmentComplianceOption,
            createAssessmentComplianceOption: createAssessmentComplianceOption,
            updateAssessmentComplianceOption: updateAssessmentComplianceOption,
            deleteAssessmentComplianceOption:deleteAssessmentComplianceOption,
            undoDeleteAssessmentComplianceOption:undoDeleteAssessmentComplianceOption,
            determineInitialHeatingAndCoolingRuleset,
            generateStoreys,
            setStoreyNames,
            processSimulationFiles,
            processScratchFiles,
            applyScratchData,
            floorNames: kFloorNames
        };

        
            
        return service;
      
        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting AssessmentComplianceOption list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getAssessmentComplianceOption(complianceOptionsId) {
            return $http({
                url: baseUrl + 'Get',
                params: {complianceOptionsId: complianceOptionsId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentComplianceOption: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAssessmentComplianceOption(option, softCreate) {
            option.saveLoading = true;
            var url = baseUrl + 'Create';
            if (softCreate) {
                url += "?softCreate=true"
            }
            return $http.post(url, option).then(success, fail)
            function success(resp) {
                option.saveLoading = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created AssessmentComplianceOption: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAssessmentComplianceOption(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Compliance Option Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating AssessmentComplianceOption: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteAssessmentComplianceOption(complianceOptionsId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { complianceOptionsId: complianceOptionsId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting AssessmentComplianceOption: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAssessmentComplianceOption(complianceOptionsId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { complianceOptionsId: complianceOptionsId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for AssessmentComplianceOption: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** 
         * Must be called per-building (if required).
         * 
         * If there is existingFloor data, updates the existing floors based on whether 
         * are more or less floors than before, i.e. it PRESERVES EXISTING DATA WHERE POSSIBLE.
         * 
         * If there is NO existing floor data (or we're happy to blow it away) then it will simply
         * construct a new 'default' set of storeys based on the count.
         * 
         * The existing or hardcoded names/descriptions will always be used. If you wish to update the storey names,
         * call setStoreyNames().
         */
        function generateStoreys(newCount, existingStoreys) {

            if (newCount == null || newCount == 0)
                return [];

            // If we don't have existing floor data, generate a
            // 'default' set.
            if(existingStoreys == null || existingStoreys.length == 0) {

                existingStoreys = [];
                let newStoreys = [];

                for (let i = 0; i < newCount; i++) {

                    let floor = {
                        name: kFloorNames[i],
                        description: null,
                        floor: i,
                        heightOffGround: null,
                    };

                    newStoreys.push(floor);
                }

                return newStoreys;

            } else {

                // 9 - 7 = 2 == need to add 2 
                // 2 - 4 = -2 == need to subtract 2
                let diff = newCount - existingStoreys.length;

                if (diff < 0) {

                    // Pop some off the stack.
                    for (let iy = 0; iy < -diff; iy++) {
                        existingStoreys.pop();
                    }
                    
                } else if (diff > 0) {

                    // Push some onto the stack
                    for (let iu = 0; iu < diff; iu++) {

                        existingStoreys.push({
                            name: kFloorNames[existingStoreys.length],
                            description: null,
                            floor: existingStoreys.length,
                            heightOffGround: null,
                        });
                    }
                }

                return existingStoreys;
            }
        }

        /**
         * Updates the name/description of the given floors. Use the hardcoded values by default (kFloorNames)
         * however the user can pass an epFloor array (as defined in the Client Defaults and Building Description
         * types) if they wish to override the default values.
         * 
         * @param {[]} existingStoreys REQUIRED.
         * @param {[]} nameOverrides OPTIONAL. EPFloor overrides.
         * @param {number} updateAboveIndex OPTIONAL. Limit name changes to indexes above the given. Use if 
         * don't want to update floor names below a certain floor.
         */
        function setStoreyNames(existingStoreys, nameOverrides, updateAboveIndex)
        {
            if(updateAboveIndex == null)
                updateAboveIndex = 0;

            for (let i = updateAboveIndex; i < existingStoreys.length; i++) {

                // If the caller has supplied overrides for the default floornames (for instance,
                // if the BuildingDescription client default is changed but the user has ALREADY
                // set some client-specific storey names) then we prefer the overrides.
                let newName = kFloorNames[i];

                if(i >= updateAboveIndex && nameOverrides != null && nameOverrides[i] != null && 
                    (nameOverrides[i].description != null && nameOverrides[i].description != ""))
                    newName = nameOverrides[i].description;

                existingStoreys[i].name = newName;
            }

            return existingStoreys;
        }

        /**
         * Using the uploaded files (MUST BE PRESENT), sends a request to the server to
         * process them and returns the converted zone and construction data.
         * 
         * If the files given are unable to be processed, a user-friendly error
         * message should be returned for display to the user (e.g. "Incorrect
         * detailzonemap.txt was uploaded. Are you sure it's for the same assessment?").
         */
        function processSimulationFiles(complianceOptionsId, assessmentComplianceBuildingId, assessmentSoftwareCode, 
                                        fileBUrl, fileCUrl, fileDUrl, fileEUrl, climateZone, processWithMismatch = false) {

            return $http({
                url: baseUrl + 'ProcessAssessmentFiles',
                params: { complianceOptionsId, assessmentComplianceBuildingId, 
                    fileBUrl, fileCUrl, fileDUrl, fileEUrl, 
                    assessmentSoftwareCode, climateZone, processWithMismatch },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                return resp.data;
            }
            function fail(error) {
                console.log(error);
                var msg = error.data.message;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /**
         * Using the passed file objects, attempts to process as much scratch data as possible. Used when the files are
         * not already updated to an S3 bucket.
         *
         * @param {String} assessmentSoftwareCode The code of the assessment software selected. Required for processing.
         * @param {String[]} files An array of scratch files to upload IN STRING FORMAT.
         * 
         * @returns {any} the same data as 'processSimulationFiles'
         */
        function processScratchFiles(assessmentSoftwareCode, files) {
            
            return $http({
                method: 'POST',
                url: baseUrl + 'ProcessScratchFiles',
                headers: {'Content-Type': 'text/*'},
                params: { assessmentSoftwareCode },
                data: files
                
            }).then(success, fail);
            
            function success(resp) {
                return resp.data;
            }
            
            function fail(error) {
                console.log(error);
                var msg = error.data.message + (error.data.exceptionMessage || "");

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        async function applyScratchData(building, data) {

            // data will contain a list of storeys, zones.
            building.buildingZonesTemplateId = null; // Nullify template so it is not locked.
            building.constructionTemplateId = null;
            building.constructionTemplateTitle = null;
            building.openingTemplateId = null;
            building.openingTemplateTitle = null;

            building.storeys = angular.copy(data.storeys);
            building.surfaces = angular.copy(data.surfaces);
            building.openings = angular.copy(data.openings);
            building.buildingOrientation = data.northOffset;
            building.energyUsageSummary = angular.copy(data.energyUsageSummary);
            
            // Exterior zones are never found in scratch file, so we wish to retain any custom ones set.
            building.zones = [...angular.copy(data.interiorZones)];

            building.projectDescription = data.projectDescription;
            building.projectClassification = zonetypeservice
                .determineProjectClassificationBasedOnZones(building.zones);

            building.lowestLivingAreaFloorType = data.lowestLivingAreaFloorType;
            building.masonryWalls = data.fullMasonry;
            
            
            // Set Zone "Not Applicable" data.
            const floorPlanSpacesBackup = building.zoneTypesNotApplicable?.floorPlanSpaces;
            const generalroofsBackup = building.zoneTypesNotApplicable?.generalroofs;
            
            building.zoneTypesNotApplicable = {};
            building.zoneTypesNotApplicable.interior = !(zoneservice.interiorZones(building.zones)?.length > 0);
            building.zoneTypesNotApplicable.roofspace = !(zoneservice.roofSpaceZones(building.zones)?.length > 0);
            building.zoneTypesNotApplicable.subfloorspace = !(zoneservice.subfloorSpaceZones(building.zones)?.length > 0);
            building.zoneTypesNotApplicable.groundsurface = !(zoneservice.groundSurfaceZones(building.zones)?.length > 0);
            building.zoneTypesNotApplicable.floorPlanSpaces = floorPlanSpacesBackup;
            building.zoneTypesNotApplicable.generalroofs = generalroofsBackup;
            
            // Loop over all construction (NOT service) categories and for any that have no items, mark as
            // 'not required'.
            let categories = await constructionservice.getConstructionCategoryList();

            categories.forEach(category => {

                const o = building.openings.filter(x => x.category.constructionCategoryCode === category.constructionCategoryCode);
                const s = building.surfaces.filter(x => x.category.constructionCategoryCode === category.constructionCategoryCode);

                const sum = o?.length + s?.length;

                if(building.categoriesNotRequired == null)
                    building.categoriesNotRequired = {};
                    
                building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] = sum === 0;
            });

            log.logSuccess("Scratch data applied successfully");
            building.processingScratch = false;

            
        }

        function determineInitialHeatingAndCoolingRuleset(certification, stateList, stateCode, client) {
            
            const certificationEnabled = certification.heatingAndCoolingRulesetCode !== 'Disabled';
            const state = stateList.filter(x => x.stateCode === stateCode)[0];
            const stateEnabled = state?.heatingAndCoolingRulesetCode === 'Inherit' ||
                                 state?.heatingAndCoolingRulesetCode === 'Enabled';
            const clientEnabled = client.clientOptions.heatingAndCoolingRulesetCode === 'Inherit' ||
                                  client.clientOptions.heatingAndCoolingRulesetCode === 'Enabled';

            const hierarchyEnabled = clientEnabled && stateEnabled && certificationEnabled;

            // console.log("hierarchyEnabled: ", hierarchyEnabled);

            return hierarchyEnabled
                ? "Enabled"
                : "Disabled";
        }

    }
})();
