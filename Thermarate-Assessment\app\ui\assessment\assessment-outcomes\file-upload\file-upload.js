(function () {
    'use strict';
    angular
        .module('app')
        .component('fileUpload', {
            bindings: {
                assessment: '<',
                job: '<',
                fileObject: '<',
                propName: '@',
                formName: '@',
                jobFiles: '<',
                isLocked: '<',
                forceEdit: '<',
                isRequired: '<',
                requiredMessage: '<',
                category: '<',
                classification: '<',
                onChange: '&',
                label: '@',
                externalClickHandler: '&',
                accept: '<',    // string, use to limit allowed file types
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/file-upload/file-upload.html',
            controller: FileUpload,
            controllerAs: 'vm'
        });

    FileUpload.$inject = ['Upload', 'fileservice', 'common'];

    function FileUpload(Upload, fileservice, common) {
        var vm = this;

        vm.accept = vm.accept ?? "";

        if (vm.formName == null || vm.formName == "")
            vm.formName = "FileUploadForm";

        if (vm.requiredMessage == null)
            vm.requiredMessage = "Field is required.";

        vm.uploadIsInProgress = uploadIsInProgress;
        vm.uploadFile = uploadFile;
        vm.downloadFile = downloadFile;
        vm.downloadFileForceDialog = downloadFileForceDialog;
        vm.setToNull = setToNull;
        vm.fileChanged = function () {
            if (vm.onChange) {
                setTimeout(function () {
                    vm.onChange();
                });
            }
        }

        function setToNull(context, field) {
            if (context != undefined && context != null && context[field] != undefined) {
                context[field] = null;
                if (context[field + "Id"] != undefined) {
                    context[field + "Id"] = null;
                }
            }
            vm.fileChanged();
        }

        function downloadFile(f) { fileservice.downloadFile(f); }
        function downloadFileForceDialog(f) { fileservice.downloadFileForceDialog(f); }

        function uploadFile($file, target, field) {
            common.uploadSoftwareFile(
                vm.assessment,
                $file,
                target,
                field,
                vm.jobFiles,
                vm.category,
                vm.classification,
                vm.fileChanged,
                vm.externalClickHandler
            );
        }

        function uploadIsInProgress() {
            return vm.fileObject[vm.propName + 'UploadProgress'] != null && 
                   vm.fileObject[vm.propName + 'UploadProgress'] !== 100;
        }
    }
})();