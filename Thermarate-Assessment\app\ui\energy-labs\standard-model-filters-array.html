<div class="design-filters" style="margin: 6px; margin-bottom: 20px;">

    <div ng-repeat="filter in vm.filters track by filter.field"
         class="el-filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected-array' : ''}}">
        <!-- Label -->
        <label class="el-filter-label">{{filter.name}}</label>
        <!-- Clear Button -->
        <img src="/content/images/cross.png"
             class="el-filter-clear-button-array"
             ng-click="vm.clearFilter(filter);$event.stopPropagation()"
        />
        <!-- Normal Field -->
        <md-input-container ng-if="!['features', 'categories'].includes(filter.field)"
                            class="md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex">
            <md-select name="{{filter.field}}"
                       class="vertically-condensed vertically-condensed-ex"
                       style="margin-bottom: 26px"
                       ng-required="false"
                       multiple="true"
                       md-selected-text="vm.filterText(filter.field)"
                       ng-change="vm.filterChanged()"
                       ng-model="vm.appliedFilters[filter.field]">
                <md-option>
                    Any
                </md-option>
                <md-option ng-show="vm.filterCountData[filter.field][option] > 0"
                           ng-repeat="option in vm.settings.distinctDesignOptions[filter.field] track by $index"
                           ng-value="option">
                    {{option}} ({{vm.filterCountData[filter.field][option]}})
                </md-option>
            </md-select>
        </md-input-container>
        <!-- Features -->
        <md-input-container ng-if="filter.field == 'features'"
                            class="el-filter-dropdown md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex md-input-focused"
                            style="padding-bottom:20px;">
            <md-select name="features"
                       style="margin-bottom: 6px"
                       ng-required="false"
                       multiple="true"
                       md-selected-text="vm.filterText('features')"
                       ng-change="vm.filterChanged()"
                       ng-model="vm.appliedFilters['features']">
                <md-option>
                    Any
                </md-option>
                <div ng-repeat="section in vm.featuresSections" ng-show="section.features.length > 0">
                    <hr ng-if="vm.featureSectionHasItems(section)" class="md-select-divider" />
                    <md-option ng-repeat="option in section.features track by $index"
                               ng-show="vm.filterCountData['features'][option] > 0"
                               ng-value="option">
                        {{vm.featureName(option)}} ({{vm.filterCountData['features'][option]}})
                    </md-option>
                </div>
            </md-select>
        </md-input-container>
        <!-- Categrories -->
        <md-input-container ng-if="filter.field == 'categories'"
                            class="el-filter-dropdown md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex md-input-focused"
                            style="padding-bottom:20px;margin-bottom:-15px;">
            <md-select name="categories"
                       style="margin-bottom: 6px"
                       ng-required="false"
                       multiple="true"
                       md-selected-text="vm.filterText('categories')"
                       ng-change="vm.filterChanged()"
                       ng-model="vm.appliedFilters['categories']">
                <md-option>
                    Any
                </md-option>
                <hr ng-if="vm.categoriesHasItems()" class="md-select-divider" ng-show="vm.categories.length > 0" />
                <md-option ng-show="vm.filterCountData['categories'][option] > 0"
                           ng-repeat="option in vm.categories track by $index"
                           ng-value="option">
                    {{vm.featureName(option)}} ({{vm.filterCountData['categories'][option]}})
                </md-option>
            </md-select>
        </md-input-container>
    </div>
</div>

<style>

    .el-filter {
        position: relative;
    }

    .el-filter-label {
        font-size: 9px;
    }

    hr.md-select-divider {
        border: 0;
        height: 1px;
        background: var(--thermarate-grey);
    }

    .el-filter-clear-button-array {
        display: none;
        position: absolute;
        right: 6px;
        bottom: 33px;
        z-index: 50;
        width: 9px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

        .el-filter-clear-button-array:hover {
            background-color: #f5f5f5;
        }

    .el-filter.options-selected-array > .el-filter-clear-button-array {
        display: inherit !important;
    }

    .el-filter.options-selected-array .md-select-icon {
        margin-left: -46px;
    }

</style>