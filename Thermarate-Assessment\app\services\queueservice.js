// Name: queueservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'queueservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', queueservice]);

    function queueservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
      
        var service = {
            /* These are the operations that are available from this service. */
            getQueueRecords: getQueueRecords,
            getQueues: getQueues,
            currentFilter: function () { return currentFilter },
            
        };

        return service;

        //#region main application operations
        // ----------------------------------

       
        //Fetch Queue Records List from server
        function getQueueRecords(forFilter, fromDate, toDate, queueIds, pageSize, pageIndex, sort, filter, aggregate) {
          
            var isDeleted = false;
            var wkUrl = '../api/queue/getRecords';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            var filterdates = "";

            currentFilter = forFilter;
            switch (forFilter) {
                case 'Deleted':
                    isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get Integration Audit List from the Server 
            var params = { fromDate: fromDate, toDate: toDate, queueIds: queueIds, isDeleted: isDeleted };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
              
                return resp.data; 
                
            }
            function fail(error) {
               
                var msg = "Error getting queue records list: " + error;

                log.logError(msg, error, null, true);
            }
        }

        //Fetch Queue Records List from server
        function getQueues(forFilter, fromDate, toDate) {
          
            var isDeleted = false;
            var wkUrl = '../api/queue/get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            var filterdates = "";

            currentFilter = forFilter;
            switch (forFilter) {
                case 'Deleted':
                    isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get Queues from server

            return $http({
                url: wkUrl,
                params: {isDeleted: isDeleted},
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
              
                return resp.data; 
                
            }
            function fail(error) {
               
                var msg = "Error getting queues: " + error;

                log.logError(msg, error, null, true);
            }
        }
    }

})();
