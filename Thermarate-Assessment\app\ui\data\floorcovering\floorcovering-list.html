<section id="floor-covering-list-view" class="main-content-wrapper" data-ng-controller="FloorCoveringListCtrl as vm">

    <div class="widget">
        <div layout="row"
             style="padding: 10px 0px; ">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
        </div>

        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="FloorCovering"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                   st-table="vm.floorCoveringList"
                   st-table-filtered-list="exportList"
                   st-global-search="vm.listFilter"
                   st-persist="floorCoveringList"
                   st-pipe="vm.callServer"
                   st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="title" class="can-sort text-left">Description</th>
                        <th st-sort="solarAbsorptance" class="can-sort text-left">Classification</th>
                        <th st-sort="manufacturer.description" class="can-sort text-left">Material Codes</th>
                        <th style="width: 50px;"></th>
<!--                        <th style="width: 50px;">-->
<!--                            <div style="display: grid; justify-content: center;">-->
<!--                                <md-checkbox style="margin: auto; text-align: center; width: 0;"-->
<!--                                             ng-model="vm.bulkSelected"-->
<!--                                             ng-click="vm.bulkSelect(!vm.bulkSelected);"></md-checkbox>-->
<!--                            </div>-->
<!--                        </th>-->
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.floorCoveringList">
                        <td data-title="Action" class="action-col">
                            <md-button class="md-primary list-select" 
                                       ui-sref="floorcovering-updateform({ floorCoveringCode: row.adjacentFloorCoveringCode})">
                                Select
                            </md-button>  
                        </td>
                        <td data-title="Title" class="text-left">{{::row.description }}</td>
                        <td data-title="Title" class="text-left">{{::row.classification }}</td>
                        <td data-title="Manufacturer" class="text-left">{{vm.formattedMaterialCodes(row.materialCodesArray) }}</td>

                        <!-- Delete -->
                        <td data-title="Notes" class="text-center">
                            <div layout="row"
                                 layout-align="center center">
                                <md-button class="md-raised md-icon-button"
                                           title="Delete Template"
                                           ng-click="vm.delete(row)">
                                    <i class="fa fa-eraser fa-lg"></i>
                                </md-button>
                            </div>
                        </td>

<!--                        &lt;!&ndash; Bulk Select &ndash;&gt;-->
<!--                        <td data-title="Manufacturer" class="text-left">-->
<!--                            <div style="display: grid; justify-content: center;">-->
<!--                                <md-checkbox style="margin: auto; text-align: center; width: 0;"-->
<!--                                             ng-model="row.isBulkSelected">-->

<!--                                </md-checkbox>-->
<!--                            </div>-->
<!--                        </td>-->
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="9999" class="text-center">
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; background-color: rgb(250,250,250);">

                                <!-- Just empty. -->
                                <div></div>

                                <!-- Pagination Display -->
                                <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                                <!-- Bulk Delete -->
                                <md-button ng-if="vm.bulkSelectionsExist()"
                                           ng-click="vm.bulkDelete()"
                                           class="md-raised md-warn"
                                           style="align-self: center; justify-self: end;">
                                    BULK DELETE
                                </md-button>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
            <div>

            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
