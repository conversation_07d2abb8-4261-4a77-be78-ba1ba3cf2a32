﻿<md-dialog aria-label={{title}}>
    <form ng-cloak>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>{{title}}</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content>
            <div class="md-dialog-content">
                <div ng-show="bigInfo!=null" class="big-info">{{bigInfo}}</div>
                <p ng-if="bigInfo==null">{{message}}</p>
                <p ng-if="bigInfo!=null" class="text-center">{{message}}</p>
                <md-progress-circular ng-if="progress!=null" md-mode="indeterminate" style="align-items: center;"></md-progress-circular>
                <div ng-show="deleteReasons">
                    <div class="radio" ng-repeat="del in deleteReasons">
                        <label>
                            <input type="radio" ng-model="vm.deleteReasonId" ng-value="del.deleteReasonId">
                            {{del.description}}
                        </label>
                    </div>
                    <input ng-show="vm.deleteReasonId==3" name="note" ng-model="vm.note" class="form-control" placeholder="delete reason" required />
                </div>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">
            
            <md-button ng-hide="hideOk==true" 
                       class="md-raised md-primary" 
                       ng-click="ok()" 
                       ng-disabled="deleteReasons!=null && vm.deleteReasonId==null">
                {{okText}}
            </md-button>
            
            <md-button ng-show="yesText!=null" class="md-raised md-primary" ng-click="yes()">{{yesText}}</md-button>
            <md-button ng-show="noText!=null" class="md-raised" ng-click="no()">{{noText}}</md-button>
            <md-button ng-hide="bigInfo!=null" class="md-raised" ng-click="cancel()">{{cancelText}}</md-button>
        </md-dialog-actions>
    </form>
</md-dialog>