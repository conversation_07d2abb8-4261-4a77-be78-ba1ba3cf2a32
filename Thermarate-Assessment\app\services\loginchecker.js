﻿//periodicaly checks if user is still logged in

// Check ui code version matches latest server version available.
// Check is done every 10 minutes, and a warning toast displayed if a newer version is available.
(function () {
    'use strict';
    var serviceId = 'logincheckerservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', '$interval', '$location', 'security', logincheckerservice]);

    function logincheckerservice(common, config, $http, $interval, $location, security) {
        var $q = common.$q;
        var log = common.logger;

        var service = {
            /* These are the operations that are available from this service. */
            initialise: initialise,
        };

        return service;

        //#region main application operations
        // ----------------------------------

        function initialise() {
            var intervalTime = 10 * 1000; //in ms
            $interval(function () {
                checkLogin();
            }, intervalTime);
            
        } // end initialise()


        function checkLogin() {
            var wkUrl = config.servicesUrlPrefix + 'LoginChecker/Hello';
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
            }
            function fail(error) {
                //something went wrong
                if (error.status == 401) {
                    $location.path('/login');
                }
            }
        }
    }
})();