(function () {

    'use strict';
    angular.module('app').component('homePlanOptionsSwitcher', {
        bindings: {
            theParentModel: '=',
            getFullVariation: '<',
            showNorthOffset: '<',
            showLabel: '<',
            view3dFloorPlans: '<',
            floorplannerLink: '<',
            onModelChanged: '&',
            optionButtonClickedCallback: '&',
            iconsClasses: '<',
            viewingModelsCount: '<'
        },
        templateUrl: 'app/ui/energy-labs/home-plan-options-switcher.html',
        controller: HomePlanOptionsSwitcherController,
        controllerAs: 'vm'
    });

    HomePlanOptionsSwitcherController.$inject = ['$rootScope', '$mdDialog', 'common', 'standardmodelservice'];

    function HomePlanOptionsSwitcherController($rootScope, $mdDialog, common, standardmodelservice) {

        let vm = this;

        // - INITIALISE - //

        vm.tooltipDelay = 1200;

        vm.firstlanViewIndexControl = 0;
        vm.secondlanViewIndexControl = 0;

        vm.variationCategories = StandardModelConstants.variationCategories;

        function initialise() {
            vm.theParentModel.dropdownFields = {};
            vm.variationOptionsList = vm.theParentModel.variationOptionsList;
            vm.allFieldNames = vm.variationCategories.map(cat => common.firstCharLowerCase(cat)).filter(cat => vm.theParentModel.variationOptionsSettings[`${cat}IsActive`]);
            vm.allFieldNames.forEach(fieldName => {
                let defaultValueId = vm.theParentModel.selectedVariation[`variation${common.firstCharUpperCase(fieldName)}Id`];
                vm.theParentModel.dropdownFields[fieldName] = {
                    options: null,
                    selected: vm.variationOptionsList.find(o => o.standardHomeModelVariationOptionId == defaultValueId),
                    expanded: false
                };
            });
            // Only get options and variation if optioData has not already been set
            vm.getOptionsAndVariation(null, null, vm.theParentModel.selectedVariation.optionData == null);
            // Get all of these 3 lists as a list
            vm.theList = [vm.firstHalfDropdowns(), vm.middleDropdown(), vm.secondHalfDropdowns()];
        }

        // - HANDLES - //

        // Get options based on selections
        vm.getOptionsAndVariation = function (optionChangedName, fromOptionSelected, setVariationData = true) {
            let currentSelections = {
                parentHomeModelId: vm.theParentModel.standardHomeModelId,
                filterByVariationIdList: vm.theParentModel.variationIdList
            };
            vm.allFieldNames.forEach(f => {
                currentSelections[`${f}Id`] = vm.theParentModel.dropdownFields[f].selected?.standardHomeModelVariationOptionId;
            });
            let fieldNamesToGet = vm.allFieldNames;
            // IF just changed option, only get options for dropdowns after the changed dropdown
            if (optionChangedName) {
                fieldNamesToGet = fieldNamesToGet.slice(-(fieldNamesToGet.length - fieldNamesToGet.indexOf(optionChangedName) - 1));
            }
            // List of calls
            let allFetches = fieldNamesToGet.map(f => standardmodelservice.getVariationOptionsForStage(
                `variation${common.firstCharUpperCase(f)}`,
                currentSelections
            ));
            Promise.all(allFetches).then(results => {
                // Set new options
                results.forEach((result, i) => {
                    let fieldName = fieldNamesToGet[i];
                    vm.theParentModel.dropdownFields[fieldName].options = vm.variationOptionsList.filter(o => o.variationCategoryCode == common.firstCharUpperCase(fieldName) && result.data.includes(o.standardHomeModelVariationOptionId));
                    // IF has one option, auto-select this option
                    if (vm.theParentModel.dropdownFields[fieldName].options.length == 1) {
                        vm.theParentModel.dropdownFields[fieldName].selected = vm.theParentModel.dropdownFields[fieldName].options[0];
                    }
                });
                // Check if current selections are still acceptable
                fieldNamesToGet.forEach(fieldName => {
                    let thisField = vm.theParentModel.dropdownFields[fieldName];
                    if (!thisField.options.map(o => o.standardHomeModelVariationOptionId).includes(thisField.selected.standardHomeModelVariationOptionId)) {
                        thisField.selected = null;
                    }
                });
                // Get new Variation values
                vm.getVariation(fromOptionSelected, setVariationData);
            });
        }

        // Expand field
        vm.expandField = function (expandingFieldName) {
            document.getElementById(`options-switcher-container-${vm.theParentModel.standardHomeModelId}`)?.focus();
            vm.allFieldNames.forEach(name => {
                if (name != expandingFieldName) {
                    vm.theParentModel.dropdownFields[name].expanded = false;
                    vm.theParentModel.dropdownFields[name].justExpanded = false;
                }
            });
            // IF this was already expanded, set to false (do this way because of timing of "vm.switcherBlur()")
            if (vm.theParentModel.dropdownFields[expandingFieldName].justExpanded) {
                vm.theParentModel.dropdownFields[expandingFieldName].expanded = false;
                vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = false;
            // Expand only if has more than 1 option
            } else if (vm.theParentModel.dropdownFields[expandingFieldName].options.length > 1) {
                vm.theParentModel.dropdownFields[expandingFieldName].expanded = true;
                vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = true;
            }
            // Check if now in options selection mode
            vm.theParentModel.inOptionsSelectionsMode = vm.allFieldNames.some(fieldName => vm.theParentModel.dropdownFields[fieldName].expanded);
            // Parent option clicked callback
            vm.optionButtonClickedCallback();
        }

        // Collapse field
        vm.collapseField = function (expandingFieldName) {
            vm.theParentModel.dropdownFields[expandingFieldName].expanded = false;
            vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = false;
            vm.theParentModel.inOptionsSelectionsMode = false;
        }

        // Click outside
        vm.switcherBlur = function () {
            vm.allFieldNames.forEach(fieldName => vm.theParentModel.dropdownFields[fieldName].expanded = false);
            vm.theParentModel.inOptionsSelectionsMode = false;
            setTimeout(() => {
                vm.allFieldNames.forEach(fieldName => { vm.theParentModel.dropdownFields[fieldName].justExpanded = false });
            }, 100);
        }

        // Check if any dropdown is open
        vm.anyDropdownOpen = function () {
            return vm.allFieldNames.some(f => vm.theParentModel.dropdownFields[f].expanded);
        }

        // Select option
        vm.selectOption = function (fieldName, option) {
            // Collapse
            vm.collapseField(fieldName);
            // IF option has changed
            if (option.standardHomeModelVariationOptionId != vm.theParentModel.dropdownFields[fieldName].selected.standardHomeModelVariationOptionId) {
                vm.theParentModel.dropdownFields[fieldName].selected = option;
                // Get options for dropdowns after this dropdown
                vm.getOptionsAndVariation(fieldName, true);
            }
        }

        // Get Variation based selections
        vm.getVariation = function (fromOptionSelected, setVariationData = true) {
            standardmodelservice.getVariationFromSelections(
                vm.theParentModel.standardHomeModelId,
                vm.theParentModel.dropdownFields['floorplan']?.selected.standardHomeModelVariationOptionId,
                vm.theParentModel.dropdownFields['designOption']?.selected.standardHomeModelVariationOptionId,
                vm.theParentModel.dropdownFields['facade']?.selected.standardHomeModelVariationOptionId,
                vm.theParentModel.dropdownFields['specification']?.selected.standardHomeModelVariationOptionId,
                vm.theParentModel.dropdownFields['configuration']?.selected.standardHomeModelVariationOptionId,
                vm.getFullVariation
            ).then(data => {
                // Set new Variation
                if (setVariationData) {
                    vm.theParentModel.selectedVariation = {
                        ...vm.theParentModel.selectedVariation, // Make sure any other data from parent processess is not erased
                        ...data
                    };
                    // Trigger parent onChange
                    if (vm.onModelChanged != null) {
                        vm.onModelChanged();
                    }
                }
                // Flip home plans
                if (fromOptionSelected) {
                    vm.theParentModel.useSecondPlanSwitcher = vm.theParentModel.useSecondPlanSwitcher == null ? true : !vm.theParentModel.useSecondPlanSwitcher;
                }
                // Check if 3D floor plans should be enabled by checking all three levels:
                // 1. Project level (passed in as view3dFloorPlans)
                // 2. Home Design level (theParentModel.view3dFloorPlans)
                // 3. Variation level (theParentModel.selectedVariation.view3dFloorPlans)
                vm.show3dFloorPlans = vm.view3dFloorPlans &&
                                     vm.theParentModel.view3dFloorPlans &&
                                     vm.theParentModel.selectedVariation.view3dFloorPlans;

                if (!vm.theParentModel.useSecondPlanSwitcher) {
                    vm.firstViewingPlans = vm.theParentModel.selectedVariation.standardHomeModelFiles;
                    vm.firstPlanViewIndexControl = 0;
                    vm.firstIconsData = {
                        standardHomeModelId: vm.theParentModel.selectedVariation.standardHomeModelId,
                        livingAreas: vm.theParentModel.selectedVariation.livingAreas,
                        numberOfBedrooms: vm.theParentModel.selectedVariation.numberOfBedrooms,
                        numberOfBathrooms: vm.theParentModel.selectedVariation.numberOfBathrooms,
                        numberOfGarageSpots: vm.theParentModel.selectedVariation.numberOfGarageSpots,
                        width: vm.theParentModel.selectedVariation.width,
                        displayFloorArea: vm.theParentModel.selectedVariation.displayFloorArea
                    };
                } else {
                    vm.secondViewingPlans = vm.theParentModel.selectedVariation.standardHomeModelFiles;
                    vm.secondPlanViewIndexControl = 0;
                    vm.secondIconsData = {
                        standardHomeModelId: vm.theParentModel.selectedVariation.standardHomeModelId,
                        livingAreas: vm.theParentModel.selectedVariation.livingAreas,
                        numberOfBedrooms: vm.theParentModel.selectedVariation.numberOfBedrooms,
                        numberOfBathrooms: vm.theParentModel.selectedVariation.numberOfBathrooms,
                        numberOfGarageSpots: vm.theParentModel.selectedVariation.numberOfGarageSpots,
                        width: vm.theParentModel.selectedVariation.width,
                        displayFloorArea: vm.theParentModel.selectedVariation.displayFloorArea
                    };
                }
            });
        }

        // - BUILD - //

        // Get the first half of dropdowns for column 1/3
        vm.firstHalfDropdowns = function () {
            let list = vm.allFieldNames.slice(0, vm.allFieldNames.length/2);
            return list;
        }

        // Get middle item if odd, otherwise return empty, for column 2/3
        vm.middleDropdown = function () {
            if (vm.allFieldNames.length % 2 == 1) {
                let item = vm.allFieldNames[Number((vm.allFieldNames.length/2).toFixed())-1];
                return [item];
            } else {
                return [];
            }
        }

        // Get the second half of dropdowns for column 3/3
        vm.secondHalfDropdowns = function () {
            if (vm.allFieldNames.length % 2 == 1) {
                let list = vm.allFieldNames.slice((vm.allFieldNames.length/2)+1, vm.allFieldNames.length);
                return list;
            } else {
                let list = vm.allFieldNames.slice(vm.allFieldNames.length/2, vm.allFieldNames.length);
                return list;
            }
        }

        vm.moreThanOneDropdowns = function () {
            return vm.allFieldNames.length > 1;
        }

        // - RUN INITIALISE - //

        initialise();
    }

})();