(function () {
    // The StatusUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'StatusUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'statusservice', statusUpdateController]);
function statusUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  statusservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Status';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.statusCode = null;
        vm.status = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Status";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.statusCode = $scope.statusCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.statusCode = $stateParams.statusCode;
        }

        // Get data for object to display on page
        var statusCodePromise = null;
        if (vm.statusCode != null) {
            statusCodePromise = statusservice.getStatus(vm.statusCode)
            .then(function (data) {
                if (data != null) {
                    vm.status = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("status-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                statusservice.createStatus(vm.status).then(function(data){
                    vm.status = data;
                    vm.statusCode = vm.status.statusCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                statusservice.updateStatus(vm.status).then(function(data){
                    if (data != null) {
                        vm.status = data;
                        vm.statusCode = vm.status.statusCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            statusservice.deleteStatus(vm.statusCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            statusservice.undoDeleteStatus(vm.statusCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();