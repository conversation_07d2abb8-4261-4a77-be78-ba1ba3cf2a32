﻿(function () {
	'use strict';

	angular
		.module('appservices')
		.factory('addressservice', [
			'common',
			'config',
			'$http',
			'nathersclimatezoneservice',
            'nccclimatezonedataservice',
			AddressService,
		]);

	function AddressService(common, config, $http, nathersclimatezoneservice, nccclimatezonedataservice) {
		var log = common.logger;
		var baseUrl = config.servicesUrlPrefix + 'address/';

		var service = {
			query: queryGeoscape, // Keeping the old name for backward compatibility
			queryGeoscape: queryGeoscape,
			querySuburb: querySuburb,
			climateZoneCodeByPostCode: climateZoneCodeByPostCode,
			getSuburbFromName: getSuburbFromName,
			querySuburbByPostcode: querySuburbByPostcode,
			queryStreetType: queryStreetType,
			getLocalGovernment: getLocalGovernment,
			getDetails: getDetails,
			reverseGeocode: reverseGeocode,
			insertAddressDataIntoObject: insertAddressDataIntoObject,
            setSuburbToAssessment: setSuburbToAssessment,
            setSuburbToProject: setSuburbToProject
		};
		return service;

		function queryGeoscape(query) {
			return $http({
				url: baseUrl + 'Query' + '/' + query,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address from Geoscape: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function querySuburb(query) {
			return $http({
				url: baseUrl + 'QuerySuburb/' + query,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function climateZoneCodeByPostCode(postcode) {
			return $http({
				url: baseUrl + 'ClimateZoneCodeByPostCode/' + postcode,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function getSuburbFromName(name) {
			return $http({
				url: baseUrl + 'GetSuburbFromName/' + name,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function querySuburbByPostcode(name) {
			return $http({
				url: baseUrl + 'QuerySuburbByPostcode/' + name,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function queryStreetType(query) {
			return $http({
				url: baseUrl + 'QueryStreetType/' + query,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function getLocalGovernment(lat, lng) {
			return $http({
				url: baseUrl + 'getLocalGovernment/' + lat + '/' + lng,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		function getDetails(addressString) {
			return $http({
				url: baseUrl + 'GetDetails' + '/' + addressString,
				method: 'GET',
			}).then(success, fail)
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			function fail(error) {
				var msg = "Error getting Address: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}

		/**
		 * Attempts to find address data for the given latitude and longitude.
		 *
		 * @param {any} lat Latitude
		 * @param {any} lng Longitude
		 */
		function reverseGeocode(lat, lng) {

			return $http({
				url: baseUrl + 'ReverseGeocode',
				params: { lat, lng },
				method: 'GET',
			})
			.then(success, fail);

			function success(resp) {

				//console.log(resp.data);

				if (resp != null && resp.data != undefined && resp.data != null)
					return resp.data;
				else
					return null;
			}

			function fail(error) {
				var msg = "Error with ReverseGeocode: " + error;
				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}

		}

		/**
		 *  Inserts address data into the given Address or Assessment object.
		 *
		 * @param {any} address The address data to insert.
		 * @param {any} receiver The object to insert the address to. Can either be an Assessment or an Address.
		 * @param {any} receiverType "ASSESSMENT" or "ADDRESS"
		 */
		function insertAddressDataIntoObject(address, receiver, receiverType) {


			if (receiverType === "ASSESSMENT") {

				// Fill passed in assessment info inc address.
				receiver.natHERSClimateZoneCode = address.natHERSClimateZoneCode;
				nathersclimatezoneservice.getNatHERSClimateZone(address.natHERSClimateZoneCode).then(nathersClimateZone => {
					receiver.natHERSClimateZone     = nathersClimateZone;
				});

				receiver.assessmentProjectDetail.latitude = address.latitude;
				receiver.assessmentProjectDetail.longitude = address.longitude;
				receiver.assessmentProjectDetail.localGovernmentAuthority = address.localGovernment;
				receiver.assessmentProjectDetail.localGovernmentAuthorityShort = address.localGovernmentShort;
				receiver.assessmentProjectDetail.lotNumber = address.lotNumber;

				receiver.assessmentProjectDetail.suburb = address.suburb;
				receiver.assessmentProjectDetail.stateCode = address.state;
				receiver.assessmentProjectDetail.postcode = address.postCode;
				receiver.assessmentProjectDetail.streetName = address.street + ' ' + (address.streetType != null ? address.streetType : "");
				receiver.assessmentProjectDetail.houseNumber = address.streetNumber;

				receiver.assessmentProjectDetail.coordinatesEditable = false;
				receiver.assessmentProjectDetail.coordinatesState = 'CONFIRMED';

				receiver.assessmentProjectDetail.planType = address.surveyType;
				receiver.assessmentProjectDetail.depositedPlanNumber = address.surveyNumber;
				receiver.assessmentProjectDetail.certificateOfTitle = address.certificateOfTitle;
				receiver.assessmentProjectDetail.parcelArea = address.landArea;

				receiver.assessmentProjectDetail.lotNumber = address.lotNumber;
				receiver.assessmentProjectDetail.boundaryGeometry = address.boundaryGeometry;
				receiver.assessmentProjectDetail.cornerBlock = address.cornerBlock;
				receiver.assessmentProjectDetail.boundarySides = address.boundarySides;

				receiver.assessmentProjectDetail.projectOwner = address.projectOwner;

			} else if (receiverType === "ADDRESS") {

				// Fill in passed address info only.
				receiver.latitude = address.latitude;
				receiver.longitude = address.longitude;
				receiver.localGovernmentAuthority = address.localGovernment;
				receiver.localGovernmentAuthorityShort = address.localGovernmentShort;
				receiver.lotNumber = address.lotNumber;

				receiver.suburb = address.suburb;
				receiver.stateCode = address.state;
				receiver.postcode = address.postCode;
				receiver.streetName = address.street + ' ' + (address.streetType != null ? address.streetType : "");
				receiver.houseNumber = address.streetNumber;
				receiver.depositedPlanNumber = address.surveyNumber;
				receiver.originalDepositedPlanNumber = address.surveyNumber;

				receiver.lotNumber = address.lotNumber;
				receiver.boundaryGeometry = address.boundaryGeometry;
				receiver.assessmentProjectDetail.cornerBlock = address.cornerBlock;
				receiver.assessmentProjectDetail.boundarySides = address.boundarySides;

				receiver.assessmentProjectDetail.projectOwner = address.projectOwner;

			} else if(receiverType === "GPS ONLY") {

				// In this instance, the receiver is probably an assessment, but we do not wish to keep all of data we
				// usually would.

				receiver.assessmentProjectDetail.latitude = address.latitude;
				receiver.assessmentProjectDetail.longitude = address.longitude;
				receiver.assessmentProjectDetail.localGovernmentAuthority = address.localGovernment;
				receiver.assessmentProjectDetail.localGovernmentAuthorityShort = address.localGovernmentShort;

				// Fill passed in assessment info inc address.
				receiver.natHERSClimateZoneCode = address.natHERSClimateZoneCode;
				nathersclimatezoneservice
					.getNatHERSClimateZone(address.natHERSClimateZoneCode)
					.then(nathersClimateZone => {
						receiver.natHERSClimateZone     = nathersClimateZone;
					});


				receiver.assessmentProjectDetail.localGovernmentAuthority = address.localGovernment;
				receiver.assessmentProjectDetail.localGovernmentAuthorityShort = address.localGovernmentShort;
				receiver.assessmentProjectDetail.boundaryGeometry = null;

				receiver.assessmentProjectDetail.coordinatesEditable = false;
				receiver.assessmentProjectDetail.coordinatesState = 'CONFIRMED';

				// Nullify certain data
				// receiver.buildingExposureCode = null; -- retain
				receiver.assessmentProjectDetail.planType = null;
				receiver.assessmentProjectDetail.depositedPlanNumber = null;
				receiver.assessmentProjectDetail.certificateOfTitle = null;
				receiver.assessmentProjectDetail.parcelArea = null;
				receiver.assessmentProjectDetail.boundarySides = null;
				receiver.assessmentProjectDetail.lotDescription = null;
				receiver.assessmentProjectDetail.cornerBlock = null;
				receiver.assessmentProjectDetail.rearLaneway = null;
				receiver.assessmentProjectDetail.ruralLot = null;
				receiver.assessmentProjectDetail.lotWidth = null;
				receiver.assessmentProjectDetail.narrowLot = null;

			}
		}

        function setSuburbToAssessment(assessment, suburb, finishedCallback) {
            assessment.natHERSClimateZoneCode = suburb.natHERSClimateZoneCode;
            assessment.assessmentProjectDetail.suburb = suburb.name;
            assessment.assessmentProjectDetail.postcode = suburb.postcode;
            assessment.assessmentProjectDetail.stateCode = suburb.stateCode;
            assessment.assessmentProjectDetail.latitude = suburb.latitude;
            assessment.assessmentProjectDetail.longitude = suburb.longitude;
            assessment.assessmentProjectDetail.boundaryGeometry = suburb.boundary;

            if (suburb.latitude && suburb.longitude) {
                this.getLocalGovernment(suburb.latitude, suburb.longitude).then(function (data) {
                    if (data) {
                        assessment.assessmentProjectDetail.localGovernmentAuthority = data.name;
                        assessment.assessmentProjectDetail.localGovernmentAuthorityShort = data.nameShort;
                    }
                });
            }

            nathersclimatezoneservice
                .getNatHERSClimateZone(suburb.natHERSClimateZoneCode)
                .then(nathersClimateZone => {
                    assessment.natHERSClimateZone = nathersClimateZone;
                    finishedCallback();
                });
        }

        function setSuburbToProject(project, suburb, finishedCallback) {
            project.natHERSClimateZoneCode = suburb.natHERSClimateZoneCode;
            nathersclimatezoneservice.getNatHERSClimateZone(suburb.natHERSClimateZoneCode).then(climateZone => project.natHERSClimateZoneDescription = climateZone.description);

			project.suburbCode = suburb.suburbCode;
			project.suburbName = suburb.name;
            project.stateCode  = suburb.stateCode;
            project.latitude   = suburb.latitude;
            project.longitude  = suburb.longitude;

            if (suburb.latitude && suburb.longitude) {
                this.getLocalGovernment(suburb.latitude, suburb.longitude).then(localGov => {
                    if (localGov) {
                        project.lga = localGov.name;
                        project.lgaShort = localGov.nameShort;
                    }
                });
                nccclimatezonedataservice.getClimateZone(suburb.latitude, suburb.longitude).then(data => {
                    project.nccClimateZoneCode = "NCC" + data;
                    project.nccClimateZoneDescription = data;
				    finishedCallback();
                });
            } else {
				finishedCallback();
            }
        }
	}
})();