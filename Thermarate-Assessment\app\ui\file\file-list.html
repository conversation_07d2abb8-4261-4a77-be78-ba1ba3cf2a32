<section id="file-list-view" class="main-content-wrapper" data-ng-controller="FileListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="File"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.fileList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="fileList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="displayName" class="can-sort text-left">Display Name</th>
                        <th st-sort="fileName" class="can-sort text-left">File Name</th>
                        <th st-sort="folderName" class="can-sort text-left">Folder Name</th>
                        <th st-sort="uRL" class="can-sort text-left">U R L</th>
                        <th st-sort="versionNo" class="can-sort text-right">Version No</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.fileList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="file-updateform({ fileId: row.fileId})">Select</md-button>  </td>
                        <td data-title="Display Name" class="text-left">{{::row.displayName }}</td>
                        <td data-title="File Name" class="text-left">{{::row.fileName }}</td>
                        <td data-title="Folder Name" class="text-left">{{::row.folderName }}</td>
                        <td data-title="U R L" class="text-left">{{::row.uRL }}</td>
                        <td data-title="Version No" class="text-right">{{::row.versionNo | number }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
