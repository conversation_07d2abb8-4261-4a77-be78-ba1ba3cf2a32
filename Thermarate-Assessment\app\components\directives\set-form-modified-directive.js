﻿(function () {
    'use strict';

    var app = angular.module('app');
    // Watch a form and when a field is modified set the isModified flag.
    // If the form is Saved then clear the $Dirty flags on the form so isModified can be set again is something changes.
    app.directive('setFormModified', ['$parse', function ($parse) {
        return {
            restrict: 'A',
            require: '^form',
            link: link

        };

        function link(scope, element, attrs, controller) {
            var setFormModifiedPropertyName = attrs.setFormModified;
            var setFormModifiedGetter = $parse(setFormModifiedPropertyName);
            var setFormModified = setFormModifiedGetter.assign;
            var undoWatch = scope.$watch(function () { return controller.$dirty; },
                                function (newVal, oldVal) {
                                    //check if the form is dirty
                                    if (newVal == true) {
                                        // Set isModified flag on the required object.
                                        var val = setFormModified(scope, true);
                                        resetFormState(val, scope, controller);
                                    }
                                });
            scope.$watch(function () { return setFormModifiedGetter(scope); },
                                function (newVal, oldVal) {
                                    //check if the isModified Flag has been reset (after a save)
                                    if (oldVal == true && newVal == false) {
                                        // Reset the form back to clean and no changes.
                                        controller.$setPristine();
                                        controller.$setUntouched();
                                    }
                                });

        }

        function resetFormState(modField, scope, controller) {
            scope.$watch(function () { return modField },
                                function (newVal, oldVal) {
                                    //check if the form is dirty
                                    if (newVal == false) {
                                        controller.form.$setPristine();
                                        controller.form.$setUntouched();
                                    }
                                });
        }
    }]);
})();