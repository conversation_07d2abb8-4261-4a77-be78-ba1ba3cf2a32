
(function () {

    'use strict';
    var controllerId = 'ProjectListController';
    angular.module('app')
        .controller(controllerId, ['$timeout', '$scope', 'common', 'projectservice', 'clientservice', 'stateservice', 'projecttypeservice', projectListController]);

    function projectListController($timeout, $scope, common, projectservice, clientservice, stateservice, projecttypeservice) {

        // Main
        var vm = this;
        vm.isBusy = true;
        vm.title = 'Projects';
        vm.projectList = [];

        // Filtering
        vm.filters = [
            { name: 'status', field: 'isActive' },
            { name: 'client', field: 'clientName' },
            { name: 'type',   field: 'projectTypeCode' },
            { name: 'state',  field: 'stateCode' }
        ];
        vm.filterOptions = {
            status: [
                { value: 'Any', description: 'Any' },
                { value: true,  description: "Active" },
                { value: false, description: "Not Active" },
            ],
            client: [],
            type: [],
            state: []
        }
        vm.searchFields = [
            "projectName",
            "clientName",
            "suburbName",
            "stateCode",
            "stateName",
            "suburbName",
        ];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};

        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.anyFiltersApplied = common.anyFiltersApplied;

        // Sorting
        vm.sortByOptions = [
            { code: "clientName",                description: "Client" },
            { code: "modifiedOn",                description: "Date Modified" },
            { code: "designsCount",              description: "Home Designs" },
            { code: "lga",                       description: "Local Government Authority" },
            { code: "lotArea",                   description: "Lot Area" },
            { code: "lots",                      description: "Lots" },
            { code: "natHERSClimateZoneCode",    description: "NatHERS Climate Zone" },
            { code: "nccClimateZoneDescription", description: "NCC Climate Zone" },
            { code: "projectName",               description: "Name" },
            { code: "projectTypeDescription",    description: "Type" },
            { code: "stateCode",                 description: "State" },
            { code: "suburbName",                description: "Suburb" },
            { code: "usersCount",                description: "Users" },
        ];
        vm.defaultSort = "modifiedOn";
        vm.defaultSortDir = "desc";
        vm.sort = { sortBy: [{ field: vm.defaultSort, dir: vm.defaultSortDir }] };

        // For any non-mandatory fields, if no projects have this field set, remove this sort option
        function removeUnusedSortFields() {
            let optionsToRemove = [
                "suburbName",
                "lga",
                "stateName",
                "natHERSClimateZoneCode",
                "nccClimateZoneDescription",
                "lots",
                "lotArea",
            ];
            optionsToRemove.forEach(option => {
                if (!vm.projectList?.some(project => (project[option] != null && project[option] != "") || project[option] > 0)) {
                    vm.sortByOptions = vm.sortByOptions.filter(sortOption => sortOption.code != option);
                }
            });
        }

        // Refresh
        vm.refresh = function () {

            vm.isBusy = true;

            projectservice.getListCancel();
            projectservice.getList().then(
                result => {  
                    // Set address text
                    result?.forEach(project => {
                        // Set address text
                        if (project.suburbName?.length > 0 && project.stateCode?.length > 0) {
                            project.addressText = `${project.suburbName}, ${project.stateCode} ${project.postcode}`;
                        }
                        else if (project.stateCode?.length > 0) {
                            project.addressText = `${project.stateCode}`;
                        }
                        else {
                            project.addressText = '';
                        }
                        // Set tools count
                        project.toolsCount = 0;
                        project.toolsCount += project.energyLabsSettings.toolIdentifyEnabled    ? 1 : 0;
                        project.toolsCount += project.energyLabsSettings.toolConfigureEnabled   ? 1 : 0;
                        project.toolsCount += project.energyLabsSettings.toolOptimiseEnabled    ? 1 : 0;
                        project.toolsCount += project.energyLabsSettings.toolWholeOfHomeEnabled ? 1 : 0;
                    });

                    if (result == null || angular.equals(result, vm.projectList)) {
                        vm.showList = true;
                        vm.isBusy = false;
                        return;
                    }

                    vm.projectList = result;

                    vm.filteredProjectList = vm.projectList;

                    common.initialiseMultiFilters(vm.filters, vm.projectList, vm.filterOptions, vm.appliedFilters);
                    // Custom for 'Status'
                    vm.filterOptions['isActive'] = [
                        { name: 'Any', value: 'Any' },
                        { name: 'Active', value: true },
                        { name: 'Not Active', value: false },
                    ];
                    vm.applyFilters();

                    removeUnusedSortFields();
                    vm.isBusy = false;
                    vm.showList = true;
                },
                error => vm.isBusy = false
            );
        };

        vm.applyFilters = function () {
            vm.showList = false;
            common.adjustFiltersForSelection(vm.filters, vm.appliedFilters, vm.appliedFiltersOld);
            vm.filteredProjectList = common.getNewFilteredList(vm.filters, vm.projectList, vm.appliedFilters, vm.searchString, vm.searchFields);
            vm.currentTotal = vm.filteredProjectList?.length ?? 0;
            common.updateFilterCountData(vm.filters, vm.filterOptions, vm.projectList, vm.appliedFilters, vm.searchString, vm.searchFields, vm.filterCountData);
            vm.filteredProjectList = common.sortList(vm.sort, vm.filteredProjectList);
            vm.appliedFiltersOld = angular.copy(vm.appliedFilters);
            vm.showList = true;
        }

        vm.getFilterSelectedText = common.getMultiFilterSelectedText;

        vm.getFilterLabel = (text) => text.charAt(0).toUpperCase() + text.slice(1);

        vm.applySort = function () {
            let field = null;
            let dir = null;
            if (vm.sort?.sortBy != null) {
                field = vm.sort.sortBy[0].field;
                dir = vm.sort.sortBy[0].dir;
            }  else {
                field = 'projectName';
                dir = 'asc';
            }
            vm.filteredProjectList = vm.filteredProjectList.sort((a, b) =>
                dir == 'asc' ? a[field] > b[field] || b[field] == null ? 1 : -1
                             : a[field] < b[field] || a[field] == null ? 1 : -1
            );
        }

        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);
            vm.applyFilters();
        }
        vm.clearFilters = function () {
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.applyFilters();
        }

        // Continuously fetch data
        var refreshListTimeout = null;
        function constantRefresh(initializing) {
            vm.refresh(initializing);
            refreshListTimeout = $timeout(constantRefresh, 5000);
        }
        constantRefresh(true);

        $scope.$on('$destroy', function () {
            $timeout.cancel(refreshListTimeout);
        });

        //Return to home page
        vm.cancel = function () {
            $state.go("/");
        }
    }
})();