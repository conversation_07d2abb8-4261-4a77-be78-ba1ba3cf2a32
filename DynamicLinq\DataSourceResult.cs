﻿using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Linq.Dynamic;

namespace Kendo.DynamicLinq
{
    /// <summary>
    /// Describes the result of Kendo DataSource read operation. 
    /// </summary>
    [KnownType("GetKnownTypes")]
    public class DataSourceResult<T>
    {


        public IQueryable<T> Query { get; set; }
        public int ChunkSize { get; set; } = 50000;
        private int _chunkIndex = 0;
        public bool NextChunk()
        {
            Data = Query.Skip(_chunkIndex * ChunkSize).Take(ChunkSize);
            bool hasData = (Total - (_chunkIndex * ChunkSize)) > 0;
            _chunkIndex++;
            return hasData;
        }

        /// <summary>
        /// filename if saving to csv/pdf etc
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Represents a single page of processed data.
        /// </summary>
        public IEnumerable<T> Data { get; set; }

        /// <summary>
        /// The total number of records available.
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// Represents a requested aggregates.
        /// </summary>
        public object Aggregates { get; set; }

        /// <summary>
        /// Used by the KnownType attribute which is required for WCF serialization support
        /// </summary>
        /// <returns></returns>
        private static Type[] GetKnownTypes()
        {
            var assembly = AppDomain.CurrentDomain
                                    .GetAssemblies()
                                    .FirstOrDefault(a => a.FullName.StartsWith("DynamicClasses"));

            if (assembly == null)
            {
                return new Type[0];
            }

            return assembly.GetTypes()
                           .Where(t => t.Name.StartsWith("DynamicClass"))
                           .ToArray();
        }
    }
}
