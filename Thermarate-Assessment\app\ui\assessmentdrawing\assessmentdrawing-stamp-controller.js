(function () {
    // The AssessmentdrawingStampCtrl supports the manipulation of stamp location on drawings
    'use strict';
    var controllerId = 'AssessmentdrawingStampCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$timeout', 'assessmentservice', 'assessmentdrawingservice', AssessmentdrawingStampController]);
    function AssessmentdrawingStampController($rootScope, $scope, $mdDialog, $stateParams, $state, $timeout, assessmentservice, assessmentdrawingservice) {
        // The model for this form 
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Stamp Drawings';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.clientOptions = angular.copy($scope.clientOptions);
        vm.hideActionBar = false;
        vm.assessment = {};
        vm.maxAreaWidth = "0px";
        vm.maxAreaHeight = "0px";
        vm.displayList = [];
        vm.drawingList = [];
        vm.svgScript = null;
        vm.hasStampLoaded = false;
        vm.stampX = "0px";
        vm.stampY = "0px";

        vm.defaultStampWidth = 226;
        vm.defaultStampAspectRatio = 0.6;
        vm.defaultStampHeight = parseFloat(vm.defaultStampWidth * vm.defaultStampAspectRatio);
        vm.stampWidth = vm.defaultStampWidth + 'px';
        vm.stampHeight = vm.defaultStampHeight + 'px';
        vm.expectedNaturalWidth = 0;
        vm.expectedNaturalHeight = 0;
        vm.slideIndex = 0;
        vm.isImageLoaded = false;
        vm.drawingDescription = null;
        vm.expectedNumOfDrawings = -1;
        vm.assessmentId = -1;
        vm.assessmentDrawings = angular.copy($scope.assessmentDrawings);

        vm.pageSizeList = [
            { id: 0, description: 'A0', scale: 1.10 },
            { id: 1, description: 'A1', scale: 1.05 },
            { id: 2, description: 'A2', scale: 1.00 },
            { id: 3, description: 'A3', scale: 0.95 },
            { id: 4, description: 'A4', scale: 0.90 }
        ];

        vm.currentPageSize = function() {
           const currentPageSize = vm.displayList[vm.slideIndex].pageSize;
           const match = vm.pageSizeList.find(x => x.id === currentPageSize);
           return match;
        }

        vm.jumpToDrawingId = $scope.assessmentDrawingId;

        if ($scope.expectedNumOfDrawings) {
            vm.expectedNumOfDrawings = $scope.expectedNumOfDrawings;
        }

        if (vm.isModal) {
            vm.hideActionBar = true;
        }

        if ($scope.assessmentId) {
            vm.assessmentId = $scope.assessmentId;
        }

        window.onresize = resize;

        function removePx(str) {
            return str.replace('px', '');
        }

        var tempRotation = 0;
        vm.rotate = async function (direction) {

            vm.isBusy = true;

            tempRotation = tempRotation != null ? tempRotation : 0;
            switch (direction) {
                case "left":
                    tempRotation = tempRotation + 90;
                    break;
                case "right":
                    tempRotation = tempRotation - 90;
                    break;
            }

            const currentPage = vm.displayList[vm.slideIndex];
            currentPage.stampX = null;
            currentPage.stampY = null;
            currentPage.isStamped = false;
            currentPage.rotation = tempRotation;

            vm.isImageLoaded = false;
            vm.expectedNaturalWidth = vm.drawnImageNaturalWidth();
            vm.expectedNaturalHeight = vm.drawnImageNaturalHeight();

            const result = await assessmentdrawingservice
                .getDefaultStampPosition(vm.currentUrl, tempRotation);

            vm.stampX = result.stampLeft + "px";
            vm.stampY = result.stampTop + "px";
            vm.stampHeight = result.height + "px";
            vm.stampWidth = result.width + "px";
            vm.NaturalToClient();
            updateVisualImageRotation();
            vm.isImageLoaded = true;
            vm.isBusy = false;

        }

        /** Updates the visual rotation of the image in the UI. Does not modify underlying data */
        function updateVisualImageRotation() {
            var element = angular.element(document.querySelector('#drawnImage'));
            var offset = 0;
            var height = element[0].clientHeight;
            var width = element[0].clientWidth;
            if (tempRotation % 4) {
                height = element[0].clientWidth;
                width = element[0].clientHeight;
                offset = (height - width) / 2;
            }

            vm.maxAreaWidth = width + "px";
            vm.maxAreaHeight = height + "px";

            element.css('transform', 'rotate(' + tempRotation + 'deg)');
            element.css('margin-left', -offset);
            element.css('margin-top', offset);
        }

        vm.resizeClientWidth = 0;
        vm.resizeClientHeight = 0;
        function resize() {
            var xratio = vm.stampXInt() / vm.resizeClientWidth; // x% old
            var yratio = vm.stampYInt() / vm.resizeClientHeight; // y% old

            var newWidth = vm.drawnImageWidth();
            var newHeight = vm.drawnImageHeight();

            var aX = xratio * newWidth;
            var aY = yratio * newHeight;

            vm.stampX = aX + 'px';
            vm.stampY = aY + 'px';
            vm.isStamped = true;

            vm.stampWidth = vm.defaultStampWidth * vm.NaturalToClientWidth() + 'px';
            vm.stampHeight = vm.defaultStampHeight * vm.NaturalToClientHeight() + 'px';

            vm.resizeClientWidth = newWidth;
            vm.resizeClientHeight = newHeight;
        }

        // We store the PNG in Assessment, this method will determine if the link is from Job or Assessment and then find the PNG appropriately.
        vm.getCurrentURL = function () {
            var url = "";

            const currentDrawing = vm.displayList[vm.slideIndex];

            if(currentDrawing == null)
                return "";

            if (vm.displayList[vm.slideIndex].archived) 
                url = vm.displayList[vm.slideIndex].attachment.originalFileUrl;
            else
                url = vm.displayList[vm.slideIndex].attachment.url;

            const fileSansExtension = url.substr(0, url.lastIndexOf("."));
            const file = fileSansExtension + ".png";
            return file;
        }

        vm.initialiseImage = function () {
            var myImage = new Image();
            myImage.onload = setExpectedImage;
            vm.currentUrl = vm.getCurrentURL();
            myImage.src = vm.currentUrl;

        }

        vm.toggleIncludeInReport = function () {
            if (vm.clientOptions.includeDrawingsInReport) {
                vm.displayList[vm.slideIndex].isIncludedInReport = !vm.displayList[vm.slideIndex].isIncludedInReport;
                if (!vm.displayList[vm.slideIndex].isIncludedInReport) {
                    vm.displayList[vm.slideIndex].toStamp = false;
                }
            }
        }

        vm.getListWithExpected = function () {
            if (vm.assessmentId != null && vm.assessmentId != -1) {
                vm.isBusy = true;

                if (vm.assessmentDrawings != undefined && vm.assessmentDrawings != null && vm.assessmentDrawings.length > 0) {
                    vm.drawingList = _.filter(vm.assessmentDrawings, function (val) { return val.archived != true && val.deleted != true });
                    createDrawingDisplayList(vm.drawingList);

                    vm.hasList = true;
                    checkJumpToDrawing();

                } else {
                    vm.hasList = false;
                }
                vm.isBusy = false;
            }
            else {
                vm.isBusy = false;
            }
        };

        vm.calculateImageDivWidth = function () {
            tempRotation = vm.displayList[vm.slideIndex].rotation || 0;
            updateVisualImageRotation();
        }

        // A single Drawing file can contain many pages that need stamping, so convert to displayList with an entry for each page.
        function createDrawingDisplayList(data) {
            vm.displayList = [];
            for (var i = 0, len = data.length; i < len; i++) {
                var rec = data[i];
                if (rec.attachment) {
                    var stampXArray = [];
                    var stampYArray = [];
                    if (rec.stampX != null && rec.stampX != "" && rec.stampY != null && rec.stampY != "") {
                        stampXArray = rec.stampX.split(',');
                        stampYArray = rec.stampY.split(',');
                    }
                    //Not 100% sure if the copy is still needed with the introduction of pdf splitting, but keeping it ust in case
                    //dot add if it's the original pdf of a multipage pdf (the pages will be added individually.)
                    if (!(rec.attachment.pageCount > 1)) {
                        var drawingRec = angular.copy(rec);
                        drawingRec.stampX = "";
                        drawingRec.stampY = "";
                        if (stampXArray && stampXArray[0]) {
                            drawingRec.stampX = stampXArray[0];
                        }
                        if (stampYArray && stampYArray[0]) {
                            drawingRec.stampY = stampYArray[0];
                        }
                        vm.displayList.push(drawingRec);
                    }
                }
            }
        }

        $scope.$on('$destroy', function () {
            for (var i = 0, len = eventListenerList; i < len; i++) {
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function (data) {
            if ($scope.modalInstance) {
                $mdDialog.hide(data);
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("assessment-list");
                }
            }
        }

        vm.save = function () {

            if (vm.hasList) {
                vm.ClientToNatural();
                vm.displayList[vm.slideIndex].stampX = vm.stampXInt();
                vm.displayList[vm.slideIndex].stampY = vm.stampYInt();
                vm.displayList[vm.slideIndex].stampWidth = vm.stampWidthInt();
                vm.displayList[vm.slideIndex].isStamped = true;
            }

        	for (var ii = 0; ii < vm.displayList.length; ii++) {
        	    vm.drawingList[ii].drawingDescription = vm.displayList[ii].drawingDescription;
        	    vm.drawingList[ii].sheetNumber = vm.displayList[ii].sheetNumber;
        	    vm.drawingList[ii].revision = vm.displayList[ii].revision;
        	    vm.drawingList[ii].revisionDate = vm.displayList[ii].revisionDate;
        	    vm.drawingList[ii].isIncludedInReport = vm.displayList[ii].isIncludedInReport;
                vm.drawingList[ii].deleted = vm.displayList[ii].deleted;
                vm.drawingList[ii].rotation = vm.displayList[ii].rotation;
                vm.drawingList[ii].toStamp = vm.displayList[ii].toStamp;
                vm.drawingList[ii].isStamped = true;
                vm.drawingList[ii].pageSize = vm.displayList[ii].pageSize;
        	}

        	updateDrawingList();

        	assessmentdrawingservice.updateList(vm.drawingList);

        	vm.cancel(vm.drawingList);
        }

        // Update the drawing list from the Display List (multiple Display list entries may match 1 drawingList entry)
        function updateDrawingList() {
            for (var i = 0, len = vm.drawingList.length; i < len; i++) {
                var drwObj = vm.drawingList[i];
                drwObj.stampX = "";
                drwObj.stampY = "";
                for (var z = 0, lenZ = vm.displayList.length; z < lenZ; z++) {
                    var page = vm.displayList[z];
                    if (page.assessmentDrawingId == drwObj.assessmentDrawingId) {
                        if (drwObj.stampX != "") {
                            drwObj.stampX = drwObj.stampX + ",";
                            drwObj.stampY = drwObj.stampY + ",";
                        }
                        drwObj.stampX = drwObj.stampX + page.stampX;
                        drwObj.stampY = drwObj.stampY + page.stampY;
                        drwObj.stampWidth = page.stampWidth;
                    }
                }
            }
        }

        vm.drawnImageWidth = function () {
            var width = document.getElementById("drawnImage").clientWidth;
            if (tempRotation % 4) {
                width = document.getElementById("drawnImage").clientHeight;
            }
            return width;
        }

        vm.drawnImageHeight = function () {
            var height = document.getElementById("drawnImage").clientHeight;
            if (tempRotation % 4) {
                height = document.getElementById("drawnImage").clientWidth;
            }
            return height;
        }

        vm.drawnImageNaturalWidth = function () {
            var width = document.getElementById("drawnImage").naturalWidth;
            if (tempRotation % 4) {
                width = document.getElementById("drawnImage").naturalHeight;
            }
            return width;
        }

        vm.drawnImageNaturalHeight = function () {
            var height = document.getElementById("drawnImage").naturalHeight;
            if (tempRotation % 4) {
                height = document.getElementById("drawnImage").naturalWidth;
            }
            return height;
           // return document.getElementById("drawnImage").naturalHeight;
        }

        function setExpectedImage() {
            vm.expectedNaturalWidth = this.naturalWidth;
            vm.expectedNaturalHeight = this.naturalHeight;

            $timeout(function () { vm.initializeStampLocation(); }, 500);
        }

        vm.slideIndexNext = function () {
            vm.isBusy = true;
            vm.ClientToNatural();
            vm.displayList[vm.slideIndex].stampX = vm.stampXInt();
            vm.displayList[vm.slideIndex].stampY = vm.stampYInt();
            vm.displayList[vm.slideIndex].stampWidth = vm.stampWidthInt();

            var total = vm.displayList.length;
            var count = 0;
            var found = false;
            while (!found && count < total) {
                vm.slideIndex++;
                if (vm.slideIndex >= vm.displayList.length) {
                    vm.slideIndex = 0;
                }
                if (vm.displayList[vm.slideIndex].deleted !== true) {
                    found = true;
                }
                count++;
            }
            if (!found) {
                vm.hasList = false;
                vm.isBusy = false;
                return;
            }

            vm.isImageLoaded = false;

            var myImage = new Image();
            myImage.onload = setExpectedImage;
            vm.currentUrl = vm.getCurrentURL();
            myImage.src = vm.currentUrl;
        }

        vm.slideIndexPrev = function () {
            vm.isBusy = true;
            vm.ClientToNatural();
            vm.displayList[vm.slideIndex].stampX = vm.stampXInt();
            vm.displayList[vm.slideIndex].stampY = vm.stampYInt();
            vm.displayList[vm.slideIndex].stampWidth = vm.stampWidthInt();

            var total = vm.displayList.length;
            var count = 0;
            var found = false;
            while (!found && count < total) {
                vm.slideIndex--;
                if (vm.slideIndex < 0) {
                    vm.slideIndex = vm.displayList.length - 1;
                }
                if (vm.displayList[vm.slideIndex].deleted !== true) {
                    found = true;
                }
                count++;
            }
            if (!found) {
                vm.hasList = false;
                vm.isBusy = false;
                return;
            }

            vm.isImageLoaded = false;

            var myImage = new Image();
            myImage.onload = setExpectedImage;
            vm.currentUrl = vm.getCurrentURL();
            myImage.src = vm.currentUrl;
        }

        vm.stampXInt = function () {
            return parseFloat(removePx(vm.stampX));
        }

        vm.stampYInt = function () {
            return parseFloat(removePx(vm.stampY));
        }

        vm.stampWidthInt = function () {
            return parseFloat(removePx(vm.stampWidth));
        }

        vm.stampHeightInt = function () {
            return parseFloat(removePx(vm.stampHeight));
        }

        vm.ClientToNatural = function () {
            vm.stampX = vm.stampXInt() * vm.ClientToNaturalWidth() + 'px';
            vm.stampY = vm.stampYInt() * vm.ClientToNaturalHeight() + 'px';
            vm.stampWidth = vm.stampWidthInt() * vm.ClientToNaturalWidth() + 'px';
            vm.stampHeight = vm.stampWidthInt() * vm.defaultStampAspectRatio + 'px';
        }

        vm.NaturalToClient = function () {
            vm.stampX = vm.stampXInt() * vm.NaturalToClientWidth() + 'px';
            vm.stampY = vm.stampYInt() * vm.NaturalToClientHeight() + 'px';
            vm.stampWidth = vm.stampWidthInt() * vm.NaturalToClientWidth() + 'px';
            vm.stampHeight = vm.stampWidthInt() * vm.defaultStampAspectRatio + 'px';
        }

        //Downscale width
        vm.NaturalToClientWidth = function () {
            return vm.drawnImageWidth() / vm.drawnImageNaturalWidth();
        }

        //Downscale height
        vm.NaturalToClientHeight = function () {
            return vm.drawnImageHeight() / vm.drawnImageNaturalHeight();
        }

        //Upscale width
        vm.ClientToNaturalWidth = function () {
            return vm.drawnImageNaturalWidth() / vm.drawnImageWidth();
        }

        //Upscale height
        vm.ClientToNaturalHeight = function () {
            return vm.drawnImageNaturalHeight() / vm.drawnImageHeight();
        }

        vm.mydragg = function () {
            return {
                move: function (divid, xpos, ypos) {
                    var divDoc = document.getElementById(divid);
                    divDoc.style.left = xpos + 'px';
                    divDoc.style.top = ypos + 'px';
                    vm.stampX = xpos + 'px';
                    vm.stampY = ypos + 'px';
                },
                startMoving: function (divid, container, evt) {
                    evt = evt || window.event;
                    var posX = evt.clientX,
                        posY = evt.clientY;

                    var divDoc = document.getElementById(divid),
                        divTop = divDoc.style.top,
                        divLeft = divDoc.style.left;

                    var eWi = parseFloat(divDoc.clientWidth),
                        eHe = parseFloat(divDoc.clientHeight);

                    var cWi = 0, cHe = 0;
                    if(document.getElementById(container).clientWidth) {
                        cWi = document.getElementById(container).clientWidth;
                    } else {
                        cWi = parseFloat(document.getElementById(container).style.width);
                    }

                    if (document.getElementById(container).clientHeight) {
                        cHe = document.getElementById(container).clientHeight;
                    } else {
                        cHe = parseFloat(document.getElementById(container).style.height);
                    } 

                    document.getElementById(container).style.cursor = 'move';
                    divTop = divTop.replace('px', '');
                    divLeft = divLeft.replace('px', '');
                    var diffX = posX - divLeft,
                        diffY = posY - divTop;
                    document.onmousemove = function (evt) {
                        evt = evt || window.event;
                        var posX = evt.clientX,
                            posY = evt.clientY,
                            aX = posX - diffX,
                            aY = posY - diffY;
                        if (aX < 0) aX = 0;
                        if (aY < 0) aY = 0;
                        if (aX + eWi > cWi) aX = cWi - eWi;
                        if (aY + eHe > cHe) aY = cHe - eHe;
                        vm.mydragg.move(divid, aX, aY);
                    }
                },
                stopMoving: function (container) {
                    var a = document.createElement('script');
                    document.getElementById(container).style.cursor = 'default';
                    document.onmousemove = function () { }
                },
            }
        }();

        vm.initializeStampLocation = function() {

            const page = vm.displayList[vm.slideIndex];

            vm.stampX = page.stampX + "px";
            vm.stampY = page.stampY + "px";
            vm.stampWidth = page.stampWidth + "px";
            vm.stampHeight = page.stampHeight + "px";

            vm.expectedNaturalWidth = vm.drawnImageNaturalWidth();
            vm.expectedNaturalHeight = vm.drawnImageNaturalHeight();

            vm.NaturalToClient();
            updateVisualImageRotation();

            vm.isImageLoaded = true;
            vm.isBusy = false;

        }

        vm.setStampToHere = function (x, y) {
            vm.stampX = x;
            vm.stampY = y;
        }

        vm.deleteItem = function (index) {
            vm.displayList[index].deleted = true;
            vm.slideIndexPrev();
        }

        function checkJumpToDrawing() {
            if (vm.jumpToDrawingId != null) {
                for (var i = 0, len = vm.displayList.length; i < len; i++) {
                    if (vm.displayList[i].assessmentDrawingId == vm.jumpToDrawingId) {
                        vm.slideIndex = i;
                        vm.initialiseImage();
                        break;
                    }
                }
            }
        }

        const allowedIncrements = 4;
        vm.scaleIncrement = determineStepIncrementFromMax(2, allowedIncrements);
        let minWidth = determineExtent(vm.defaultStampWidth, 1 / vm.scaleIncrement, allowedIncrements);
        let maxWidth = determineExtent(vm.defaultStampWidth, vm.scaleIncrement, allowedIncrements);
        vm.scale = function (factor) {

            let rawWidth = vm.stampWidthInt() * factor;

            if (rawWidth > maxWidth)
                return; // Abort if width would be larger than allowed

            if (rawWidth < minWidth)
                return;// Abort if width would be less than allowed

            vm.stampWidth = (rawWidth) + "px";
            vm.stampHeight = (rawWidth * vm.defaultStampAspectRatio ) + "px";
        }

        /** 
         *  Given the base value, the increment (as a percentage, 0.00+) and the number of allowed
         *  increments, determines what the max value will be at the end of all allowed increments.
         *  There's probably some simple mathematic formula for this but I forget it :-) ...
         */
        function determineExtent(base, increment, allowedIncrements) {
            let val = base;
            for (let i = 0; i < allowedIncrements; i++) {
                val *= increment;
            }
            return val;
        }

        /** 
         *  Given allowed percentage increase (or decrease), and the number of allowed increments,
         *  returns what the incremental steps should be.
         *  
         *  @param percentage Value 0+. 1 = "100%" (i.e. no change). 2 = "200%" etc.
         *  @param increments The number of allowed steps between the base (1.0) and the percentage value.
         */
        function determineStepIncrementFromMax(percentage, increments) {
            // The nth root of x is the same as x to the power of 1/n
            let increment = Math.pow(percentage, 1 / increments);
            return increment;
        }

        /**
         * Resets the stamp to the original location as determined by the auto-stamp algorithm.
         * 
         * Can optionally specify "ignorePageSize = true" so that stamps are not scaled according to the
         * determined page size within the auto-stamp algorithm.
         * @param {any} ignorePageSize
         */
        vm.resetStampLocation = async function (rotation)
        {
            let result = await assessmentdrawingservice.getDefaultStampPosition(vm.currentUrl, rotation);

            vm.stampX = result.stampLeft + "px";
            vm.stampY = result.stampTop + "px";
            vm.stampHeight = result.height + "px";
            vm.stampWidth = result.width + "px";

            vm.NaturalToClient();
        }

        vm.resetToDefault = async function () {
            vm.isBusy = true;

            tempRotation = 0;
            await vm.resetStampLocation(tempRotation);

            const currentPage = vm.displayList[vm.slideIndex];

            currentPage.pageSize = currentPage.originalPageSize;
            currentPage.rotation = 0;

            updateVisualImageRotation();

            vm.isBusy = false;
        }

        vm.getListWithExpected();
    }
})();