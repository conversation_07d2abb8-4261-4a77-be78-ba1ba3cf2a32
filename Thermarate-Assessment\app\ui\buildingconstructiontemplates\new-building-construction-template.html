<md-dialog ng-controller="NewBuildingConstructionTemplateCtrl as vm">
    <form ng-cloak name="NewTemplateForm" novalidate>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Add Template</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <md-icon>close</md-icon>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content layout layout-wrap layout-padding style="width:400px;">
            <div flex="100" layout layout-wrap layout-padding>
                <md-radio-group ng-model="vm.currentTemplateType" required flex="100">
                    <md-radio-button value="new">New empty template</md-radio-button>
                    <md-radio-button value="copy">Copy existing template</md-radio-button>
                </md-radio-group>
                <br />
                <md-input-container flex="100" ng-if="vm.currentTemplateType=='copy'">
                    <label>Select existing template</label>
                    <md-select name="existingTemplateId" ng-model="vm.selectedTemplate" required>
                        <md-option ng-if="!vm.currentTemplatesList.length" ng-value>No templates to copy</md-option>
                        <md-option ng-repeat="template in vm.currentTemplatesList" ng-value="template">
                            {{template.templateName}}
                        </md-option>
                    </md-select>
                </md-input-container>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout class="template-modal-footer ">
            <div layout layout-align="center center" style="width:90px;height:100%;" ng-show="vm.isBusy">
                <md-progress-circular md-mode="indeterminate" md-diameter="30"></md-progress-circular>
            </div>
            <md-button class="md-raised md-primary" ng-disabled="NewTemplateForm.$invalid" ng-click="vm.submit()" ng-show="!vm.isBusy">Ok</md-button>
            <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
        </md-dialog-actions>
    </form>
</md-dialog>