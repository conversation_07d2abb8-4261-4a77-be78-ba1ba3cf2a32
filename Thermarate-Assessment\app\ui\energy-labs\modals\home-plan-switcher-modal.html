<md-dialog data-ng-controller='HomePlanSwitcherModalCtrl as vm' class="modal-main-container">

    <!-- <Prev Button -->
    <div ng-click="vm.prevImage($event)"
         class="modal-prev-button clickable"
         show="{{vm.planViewIndex !== 0}}">
        <i class="fa fa-angle-left fa-0" />
    </div>

    <!-- Image -->
    <img ng-if="vm.planViewIndex > 0"
         class="plan-image-1"
         ng-class="{ 'run-move-left-1': vm.runAnimationLeft }"
         src="{{vm.planViews[vm.planViewIndex-1].file.url}}"
         alt="Building floor plan drawing">
    <img class="plan-image-2"
         ng-class="{ 'run-move-left-2': vm.runAnimationLeft, 'run-move-right-2': vm.runAnimationRight }"
         src="{{vm.planViews[vm.planViewIndex].file.url}}"
         alt="Building floor plan drawing">
    <img ng-if="vm.planViewIndex < vm.planViews.length-1"
         class="plan-image-3"
         ng-class="{ 'run-move-right-3': vm.runAnimationRight }"
         src="{{vm.planViews[vm.planViewIndex+1].file.url}}"
         alt="Building floor plan drawing">

    <!-- Label -->
    <span class="modal-image-label" ng-show="vm.planViews.length >= 2">{{vm.planViews[vm.planViewIndex].label}}</span>

    <!-- Next Button -->
    <div ng-click="vm.nextImage($event)"
         class="modal-next-button clickable"
         show="{{vm.planViewIndex < (vm.planViews.length - 1)}}">
        <i class="fa fa-angle-right fa-0" />
    </div>

    <!-- Cancel Button -->
    <div ng-click="vm.cancel()" class="modal-cancel-button clickable">
        <i class="fa fa-close fa-0" />
    </div>
</md-dialog>

<style>

    .md-dialog-backdrop {
        opacity: 0.8 !important;
    }

    .modal-main-container {
        overflow: visible;
        position: static;
        width: 90vw;
        height: 90vh;
    }

    /* Image */
    .plan-image-1, .plan-image-2, .plan-image-3 {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        min-width: 100%;
        max-width: 100%;
        min-height: calc(100% + 1px);
        max-height: calc(100% + 1px);
        object-fit: contain;
    }
    .plan-image-1 {
        left: -50%;
        opacity: 0;
    }
    .plan-image-2 {
        left: 50%;
        opacity: 1;
    }
    .plan-image-3 {
        left: 150%;
        opacity: 0;
    }
    /* Left Animations */
    .plan-image-1.run-move-left-1 { animation: move-left-1 0.5s; }
    .plan-image-2.run-move-left-2 { animation: move-left-2 0.5s; }
    .plan-image-3.run-move-left-3 { animation: move-left-3 0.5s; }
    @keyframes move-left-1 { 0% { left: 50%;  opacity: 1; } 100% { left: -50%; opacity: 0; } }
    @keyframes move-left-2 { 0% { left: 150%; opacity: 0; } 100% { left: 50%; opacity: 1;  } }
    /* Right Animations */
    .plan-image-1.run-move-right-1 { animation: move-right-1 0.5s; }
    .plan-image-2.run-move-right-2 { animation: move-right-2 0.5s; }
    .plan-image-3.run-move-right-3 { animation: move-right-3 0.5s; }
    @keyframes move-right-2 { 0% { left: -50%; opacity: 0; } 100% { left: 50%; opacity: 1;  } }
    @keyframes move-right-3 { 0% { left: 50%; opacity: 1;  } 100% { left: 150%; opacity: 0; } }

    .modal-image-label {
        width: 100%;
        text-align: center;
        font-size: 20px;
        position: absolute;
        color: white;
        left: 0px;
        bottom: -48px;
    }

    .modal-prev-button,
    .modal-next-button {
        z-index: 999;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        display: none;
        height: 65px;
        width: 65px;
        border-radius: 50%;
        color: white;
        font-size: 45px;
        user-select: none;
    }

    .modal-prev-button:hover,
    .modal-next-button:hover {
        background-color: white;
        color: #afb7bc;
        transition: 150ms linear;
    }

    .modal-prev-button[show='true'],
    .modal-next-button[show='true'] {
        display: block;
    }

    .modal-prev-button { left: -90px; }
    .modal-prev-button > i { margin-left: 21px; margin-top: 9px; }

    .modal-next-button { right: -90px; }
    .modal-next-button > i { margin-left: 26px;  margin-top: 9px; }

    .modal-cancel-button {
        color: white;
        border-radius: 50%;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(50vw, -50vh) translate(-110px, 30px);
        font-size: 30px;
        padding: 15px 23px;
    }

    .modal-cancel-button:hover {
        background-color: white;
        color: #b93d0c;
        transition: 150ms linear;
    }

</style>