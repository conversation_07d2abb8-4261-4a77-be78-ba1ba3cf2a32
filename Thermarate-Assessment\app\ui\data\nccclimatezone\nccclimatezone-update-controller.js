(function () {
    // The NccclimatezoneUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'NccclimatezoneUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'nccclimatezoneservice', 'security', nccclimatezoneUpdateController]);
function nccclimatezoneUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  nccclimatezoneservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit NCC Climate Zone';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.nccClimateZoneCode = null;
        vm.nccclimatezone = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New NCC Climate Zone";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.nccClimateZoneCode = $scope.nccClimateZoneCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.nccClimateZoneCode = $stateParams.nccClimateZoneCode;
        }

        // Get data for object to display on page
        var nccClimateZoneCodePromise = null;
        if (vm.nccClimateZoneCode != null) {
            nccClimateZoneCodePromise = nccclimatezoneservice.getNCCClimateZone(vm.nccClimateZoneCode)
            .then(function (data) {
                if (data != null) {
                    vm.nccclimatezone = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("nccclimatezone-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                nccclimatezoneservice.createNCCClimateZone(vm.nccclimatezone).then(function(data){
                    vm.nccclimatezone = data;
                    vm.nccClimateZoneCode = vm.nccclimatezone.nccClimateZoneCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                nccclimatezoneservice.updateNCCClimateZone(vm.nccclimatezone).then(function(data){
                    if (data != null) {
                        vm.nccclimatezone = data;
                        vm.nccClimateZoneCode = vm.nccclimatezone.nccClimateZoneCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            nccclimatezoneservice.deleteNCCClimateZone(vm.nccClimateZoneCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            nccclimatezoneservice.undoDeleteNCCClimateZone(vm.nccClimateZoneCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();