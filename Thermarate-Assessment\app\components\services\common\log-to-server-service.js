(function () {
    'use strict';
    var serviceId = 'loggingservice';
    angular.module('appservices').factory(serviceId,
        ['config', '$window', loggingservice]);

    function loggingservice(config, $window) {
        var lastErrorLogged = {};
        lastErrorLogged.message = null;
        lastErrorLogged.time = null;
        lastErrorLogged.count = null;
        var service = {
            /* These are the operations that are available from this service. */
            saveLog: function (exception, cause) { return saveLog(exception, cause) }
        };
        return service;

        // Save log to server
        function saveLog(exception, cause) {
            
            try {
                var wkUrl = config.servicesUrlPrefix + 'Logs';
                //Get Stack Trace error by Global function of stacktrace.js
                var stackTrace = printStackTrace({e: exception});
                var stackString = "";
                for (var i = 0, len = stackTrace.length; i < len; i++) {
                    stackString = stackString + stackTrace[i] + "\n";
                }
                var message = "At Url: " + $window.location.href + "  " + exception.toString();
                
                var sendToServer = false;
                if (message == lastErrorLogged.message && lastErrorLogged.time != null) {
                    var d = angular.copy(lastErrorLogged.time);
                    d.setSeconds(d.getSeconds() + 300); // Every 5 minutes
                    if (d > new Date()) {
                        // Last logged within the last 5 minutes
                        lastErrorLogged.count++;
                        if (lastErrorLogged.count < 5) {
                            sendToServer = true;
                            if (lastErrorLogged.count == 4) {
                                // Add to message on last one sent for a 5 minute period.
                                message = "**This message will not be sent again for 5minutes**" + message;
                            }
                        }
                    }
                    else {
                        sendToServer = true;
                        lastErrorLogged.message = message;
                        lastErrorLogged.time = new Date();
                        lastErrorLogged.count = 1;
                    }
                }
                else {
                    sendToServer = true;
                    lastErrorLogged.message = message;
                    lastErrorLogged.time = new Date();
                    lastErrorLogged.count = 1;
                }
                
                if (sendToServer == true) {
                    //Send error details the Server 
                    $.ajax({ type: "POST", 
                            url: config.servicesUrlPrefix + 'Logs',
                            contentType: "application/json", 
                            xhrFields: {
                                 withCredentials: true
                             },
                            data: angular.toJson({ 
                                title: "UI-> " + $window.location.href, 
                                message: message, 
                                severity: "Error",
                                stackTrace: stackString, 
                                }) 
                           }); 
                }
                
            }
            catch(ex) {
            }
        }
    }

})();