<!-- General / Design -->
<md-card ng-form="BuildingGeneralDesignForm{{vm.complianceOption.optionIndex}}{{vm.sourceType}}">
  <md-card-header>
    <div class="md-title">
      Design
    </div>
  </md-card-header>
  <md-card-content>

    <!-- Building Description -->
    <md-autocomplete flex="100" flex-gt-sm="50"
                     ng-disabled="vm.disabled"
                     ng-required="true"
                     ng-if="vm.showProjectDescriptions == true && vm.source.projectDescription.projectDescriptionCode!='PDOther'"
                     md-input-name="BuildingDescription"
                     md-selected-item="vm.source.projectDescription"
                     md-search-text="vm.source.projectDescriptionSearchText"
                     md-items="listItem in vm.projectDescriptionList"
                     md-search-text-change="vm.projectDescriptionSearchChanged(vm.source, vm.source.projectDescriptionSearchText)"
                     md-item-text="listItem.description"
                     md-min-length="0"
                     md-select-on-match="true"
                     md-require-match="true"
                     md-floating-label="Building Description"
                     ng-class="{'input-black' : !vm.disabled }">
      <md-item-template ng-class="{'input-black' : !vm.disabled }">
        <div md-highlight-text="vm.source.projectDescriptionSearchText"
             md-highlight-flags="^i"
             ng-click="vm.projectDescriptionChanged(vm.source, listItem)"
             ng-class="{'input-black' : !vm.disabled }">
          {{listItem.description}}
        </div>
      </md-item-template>
    </md-autocomplete>
    <md-input-container class="md-block" flex="100" flex-gt-sm="50" ng-if="vm.source.projectDescription.projectDescriptionCode=='PDOther'">
      <div layout="row" layout-wrap>
        <div flex="90">
          <label>Building Description Other</label>
          <input type="text" name="BuildingDescriptionOther"
                 ng-model="vm.source.projectDescriptionOther" />
        </div>
        <div flex="5">
          <md-button class="md-icon-button" ng-click="vm.clearProjectDescription(vm.source)" ng-show="!vm.disabled"><i class="material-icons">clear</i></md-button>
        </div>
      </div>
    </md-input-container>

    <!-- Project Classification -->
    <md-input-container class="md-block"
                        flex="100"
                        flex-gt-sm="50">
      <label>Project Classification</label>
      <input type="text"
             name="ProjectClassification"
             disabled
             ng-required="true"
             ng-model="vm.source.projectClassification" />
    </md-input-container>

    <!-- House Type (Design) Field (Optional) -->
    <md-input-container class="md-block"
                        ng-class="{'input-black': true}"
                        flex="100"
                        flex-gt-sm="50">
      <label>House Type</label>
      <input type="text"
             ng-required="false"
             ng-model="vm.source.design" />
    </md-input-container>

    <!-- North Offset -->
    <div layout="row"
         flex="100"
         layout-align="start center"
         style="margin: 0px; padding: 0px;">
      <md-input-container flex="100"
                          flex-gt-sm="50"
                          class="md-block"
                          ng-class="{'input-black' : !vm.disabled }"
                          style="margin-bottom: auto;">
        <label>North Offset ({{vm.symbol("degrees")}})</label>
        <input type="number"
               name="NorthOffset"
               ng-model="vm.source.buildingOrientation"
               ng-required="true"
               ng-disabled="vm.disabled"
               min="0"
               max="360" />
<!--        <div ng-messages="BuildingGeneralDesignForm{{vm.complianceOption.optionIndex}}{{vm.sourceType}}.northOffset.$error">-->
<!--          <div ng-message="required">North Offset is mandatory</div>-->
<!--          <div ng-message="number">You did not enter a valid number</div>-->
<!--          <div ng-message="min">Your number is less than the minimum allowed (0)</div>-->
<!--          <div ng-message="max">Your number is more than the maximum allowed (360)</div>-->
<!--        </div>-->
      </md-input-container>
    </div>

    <!-- Lowest Living Floor Type -->
    <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
      <label>Lowest Living Area Floor Type</label>
      <md-select name="LowestLivingAreaFloorType"
                 disabled
                 ng-required="true"
                 ng-model="vm.source.lowestLivingAreaFloorType">
        <md-option value="CSOG Only">CSOG Only</md-option>
        <md-option value="Suspended Only">Suspended Only</md-option>
        <md-option value="CSOG & Suspended">CSOG & Suspended</md-option>
      </md-select>
    </md-input-container>

    <!-- Masonry Walls -->
    <md-input-container flex="100"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex">
      <label>Full Masonry Exterior & Interior Walls</label>
      <md-select ng-model="vm.source.masonryWalls"
                 ng-required="true"
                 disabled>
        <md-option ng-value="false">No</md-option>
        <md-option ng-value="true">Yes</md-option>
      </md-select>
    </md-input-container>

    <!-- Overall Building Width (m)-->
    <md-input-container class="md-block"
                        ng-class="{'input-black': true}"
                        flex="100"
                        flex-gt-sm="50">
      <label>Overall Building Width (m)</label>
      <input type="text"
             name="HouseType"
             ng-model="vm.source.buildingWidth" />
    </md-input-container>

    <!-- Overall Building Length (m)-->
    <md-input-container class="md-block"
                        ng-class="{'input-black': true}"
                        flex="100"
                        flex-gt-sm="50">
      <label>Overall Building Length (m)</label>
      <input type="text"
             name="HouseType"
             ng-model="vm.source.buildingLength" />
    </md-input-container>

  </md-card-content>
</md-card>