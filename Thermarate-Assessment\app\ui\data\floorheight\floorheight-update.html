<form name="floorHeightform" class="main-content-wrapper" novalidate data-ng-controller='FloorHeightUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="column">
                <div flex="100">
                    <md-card>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__edit">

                                <md-input-container class="md-block" flex="100">
                                    <label>Floor Height Tolerance Limit</label>
                                    <input ng-model="vm.floorHeightParam.parmString"
                                           formatted-number
                                           decimals="2"
                                           ng-blur="vm.clearResults()" />
                                    <div ng-messages="floorHeightform.floorHeightClassification.$error">
                                        <div ng-message="required">Floor Height Tolerance Limit is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 10.</div>
                                    </div>
                                </md-input-container>

                            </fieldset>

                            <div class="col-md-12" 
                                 ng-if="vm.newRecord==false"
                                 style="margin-top: 20px;">
                                <div rd-display-created-modified ng-model="vm.floorHeight"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>

            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="floorHeightform.$invalid || vm.editPermission == false"
                           ng-show="vm.floorHeight.deleted!=true"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>   

<style>
    .material-code-row {
        display: grid;
        grid-template-columns: 50px 50px;
        align-items: center;
    }

    /* 
      This hides the (rather annoying imo) UP / DOWN buttons that automatically get added to type="number" input fields.
    */
    .hide-updown::-webkit-inner-spin-button,
    .hide-updown::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        margin: 0;
    }
</style>
