﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('ccWidgetContent', function () {
        //Usage:
        //<div data-cc-widget-content data-is-modal="vm.isModal">content here</div>
        var directive = {
            link: link,
            scope: {
                'isModal': '=',
                'noActionBar': '='
            },
            restrict: 'A',
        };
        return directive;

        function link(scope, element, attrs) {
            if (attrs.isModal != undefined && scope.isModal == true) {
                attrs.$set('class', 'modal-content-wrapper');
            }
            else {
                attrs.$set('class', 'widget-content');
                if (scope.noActionBar == true) {
                    attrs.$set('class', 'widget-content no-action-bar');
                }
            }
        }
    });
})();