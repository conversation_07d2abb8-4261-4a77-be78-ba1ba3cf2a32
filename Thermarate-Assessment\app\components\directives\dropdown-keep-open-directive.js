﻿// FAIRLY CERTAIN THIS IS OBSOLETE
// SEARCH FOR 'dropdown-keep-open'
(function () {
    'use strict';

    var app = angular.module('app');
    // Keep a bootstrap downdown open when clicking within the dropdown itself.
    // Useful for forms, etc within a dropdown.
    app.directive('dropdownKeepOpen', ['$timeout', function ($timeout) {
        return {
            restrict: 'A',
            link: function (scope, elem, attrs) {
                $timeout(function () {
                    $(elem).on({
                        "shown.bs.dropdown": function () { this.closable = true; },
                        "click": function () { this.closable = true; },
                        "hide.bs.dropdown": function () { return this.closable; }
                    });
                    elem.children('ul').click(function (e) { e.stopPropagation(); })
                }, 1000);
            }
        };
    }]);
})();