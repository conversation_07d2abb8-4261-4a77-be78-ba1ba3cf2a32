﻿(function () {
    'use strict';

    var app = angular.module('app');
    //Directive for calling a function on image load
    app.directive('imageOnLoad', ["$timeout", function ($timeout) {
        return {
            restrict: 'A',
            scope: {
                'imageOnLoad': '='
            },
            link: function (scope, element, attrs) {
                var timeout = null;
                element.bind('load', function (event) {
                    timeout = $timeout(function () {
                        scope.imageOnLoad(event);
                    });
                });

                scope.$on('$destroy', function () {
                    if (timeout) {
                        $timeout.cancel(timeout);
                        timeout = null;
                    }
                });
            }
        };
    }]);
})();