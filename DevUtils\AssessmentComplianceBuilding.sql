USE [thermarate];

SELECT TOP 100
       [building].[AssessmentComplianceBuildingId]
      ,[building].[AssessmentComplianceOptionId]
      ,[assessment].[AssessmentId] [__AssessmentId]
      ,[building].[StoreysJson]
      ,[building].[Description]
      ,[building].[CreatedOn]
      ,[building].[CreatedByName]
      ,[building].[ModifiedOn]
      ,[building].[ModifiedByName]
      ,[building].[Deleted]
      ,[building].[FileBId]
      ,[building].[MarkupFileId]
      ,[building].[FileAId]
      ,[building].[FileCId]
      ,[building].[HouseEnergyRating]
      ,[building].[Heating]
      ,[building].[Cooling]
      ,[building].[TotalEnergyLoad]
      ,[building].[ConditionedFloorArea]
      ,[building].[UnconditionedFloorArea]
      ,[building].[AttachedGarageFloorArea]
      ,[building].[SurfacesJson]
      ,[building].[OpeningsJson]
      ,[building].[ServicesJson]
      ,[building].[ConstructionTemplateTitle]
      ,[building].[IncludeBuildingElementsInReport]
      ,[building].[StoreysJson]
      ,[building].[BuildingZonesTemplateId]
      ,[building].[ConstructionTemplateId]
      ,[building].[GlazingCalcFilesJson]
      ,[building].[CategoriesWithExternalDataJson]
      ,[building].[LowestLivingAreaFloorType]
      ,[building].[Classification]
      ,[building].[MasonryWalls]
      ,[building].[OpeningSpecification]
      ,[building].[OpeningTemplateTitle]
      ,[building].[OpeningTemplateId]
      ,[building].[ProjectClassification]
      ,[building].[ProjectDescriptionCode]
      ,[building].[ProjectDescriptionOther]
      ,[building].[DesignFeaturesJson]
      ,[building].[DesignWasBlankFromTemplate]
      ,[building].[Design]
      ,[building].[ServicesTemplateId]
      ,[building].[ServicesTemplateTitle]
      ,[building].[CategoriesNotRequiredJson]
      ,[building].[BuildingOrientation]
      ,[building].[ZoneTypesNotApplicableJson]
      ,[building].[FileDId]
      ,[building].[EnergyUsageSummaryJson]
      ,[building].[Ncc2019AreaCorrectionFactor]
      ,[building].[Ncc2022AreaCorrectionFactor]
      ,[building].[GarageLocation]
      ,[building].[BuildingWidth]
      ,[building].[BuildingLength]
      ,[building].[BuildingPerimeter]
      ,[building].[RoofArea]
      ,[building].[SpacesJson]
      ,[building].[HeatingLoadLimitCorrectionFactor]
      ,[building].[CoolingLoadLimitCorrectionFactor]
      ,[building].[HeatingOriginal]
      ,[building].[CoolingOriginal]
      ,[building].[TotalEnergyLoadOriginal]
      ,[building].[HouseEnergyRatingOverride]
      ,[building].[OverrideEnergyLoads]
      ,[building].[FileEId]
      ,[building].[EnergyResultsChartDataJson]
    --   ,[building].[RoofsJson]
      ,[building].[EnvelopeSummaryJson]
      ,[building].[ZoneSummaryJson]
      ,[building].[EnvelopeSummaryConditionedJson]
      ,[building].[EnvelopeSummaryHabitableJson]
  FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
  INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
  INNER JOIN [dbo].[RSS_Assessment] [assessment] ON [option].[AssessmentId] = [assessment].[AssessmentId]
  INNER JOIN [dbo].[RSS_Job] [job] ON [assessment].[JobId] = [job].[JobId]
  INNER JOIN [dbo].[RSS_Client] [client] ON [job].[ClientId] = [client].[ClientId]
  WHERE 1=1
    -- AND [client].[ClientId] = '8fa9ba80-40d6-7937-9521-39fdd4029cde'
    -- AND [building].[AssessmentComplianceBuildingId] = '09508d17-8d64-46be-857f-b6ec74b8435d'
    --AND [building].[AssessmentComplianceOptionId] = 'FA0EC1F5-A0B9-4502-A35F-70CEE92CCD3A'
    AND [assessment].[AssessmentId] = '0c0f0b74-7a55-4824-84e6-6daf8c93aa63'
    AND [LowestLivingAreaFloorType] IS NOT NULL
  ORDER BY [CreatedOn] DESC