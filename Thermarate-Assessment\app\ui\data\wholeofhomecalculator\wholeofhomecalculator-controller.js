(function () {

    // The WorksDescriptionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'WholeOfHomeCalculatorCtrl';
    angular.module('app').controller(controllerId, ['wholeofhomeservice', 'servicetemplateservice', 'systemparametersservice', wholeOfHomeCalculatorCtrlController]);

    function wholeOfHomeCalculatorCtrlController(wholeofhomeservice, servicetemplateservice, systemparametersservice) {
        var vm = this;

        vm.title = "Whole-of-Home Calculator Defaults";

        vm.showEnergyRating = wholeofhomeservice.showEnergyRating;

        async function initialise() {
            vm.isBusy = true;

            vm.constants = WholeOfHomeConstants;

            // Get all Service Types
            servicetemplateservice.getServiceTypes().then(data => {
                vm.serviceTypes = data;
                vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                    ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                    'title',
                    vm.serviceTypes
                );
            });

            // Get current defaults
            systemparametersservice.getSystemParameters("WholeOfHomeCalculatorDefaults").then(() => {
                vm.dbSystemParam = systemparametersservice.systemparameters();
                vm.defaults = JSON.parse(vm.dbSystemParam.parmString);
                vm.isBusy = false;
            });

            vm.isBusy = false;
        }
        initialise();

        vm.save = function () {
            vm.isBusy = true
            vm.dbSystemParam.parmString = JSON.stringify(vm.defaults);
            systemparametersservice.saveSystemParameters(vm.dbSystemParam).then(() => vm.isBusy = false);
        }

    }
})();