(function () {
    angular
        .module('app')
        .component('buildingServicesTemplateSelector', {
            bindings: {
                building: '<',      // Actual building.
                buildingType: '<',  // proposed or reference
                option: '<',        // The compliance option
                disabled: '<',      // Disable all inputs?
                showCopyPrevious: '<', // boolean,
                newJob: '<',
                required: '<',        // boolean
            },
            templateUrl: 'app/ui/buildingconstructiontemplates/building-services-template-selector.html',
            controller: buildingServicesTemplateSelectorCtrl,
            controllerAs: 'vm'
        });

    buildingServicesTemplateSelectorCtrl.$inject = [
        '$scope', '$anchorScroll',
        'buildingservicestemplateservice', 'servicetemplateservice', 'common'];

    function buildingServicesTemplateSelectorCtrl($scope, $anchorScroll,
        buildingservicestemplateservice, servicetemplateservice, common) {

        let vm = this;

        vm.templateList = [];

        vm.categories = []
        servicetemplateservice.getServiceCategories()
            .then((data) => vm.categories = data);

        const blankTemplate = {

            categoriesNotRequired: {},
            servicesNotRequired: false,
            zoneTypesNotApplicable: {}, // REQUIRED?

            services: [],
            openings: [],
            surfaces: [],
            categoriesWithExternalData: {},
            classification: null

        };

        buildingservicestemplateservice
            .getAll()
            .then(function (data) {
                vm.templateList = data;
            });

        /**
         * Applies the given template, "intelligently" merging the required data as
         * required (e.g. only applies Surface data if this is a surface template, leaves
         * openings alone). Due to frequent changes on which data should live where, we
         * discard any vestigial data from the template which no longer pplies for the 
         * current rules.
         *
         * @param {any} option The compliance option the building belongs to.
         * @param {any} building The building object
         * @param {any} template The template to apply.
         */
        vm.applyTemplate = async function (option, building, template) {

            await buildingservicestemplateservice.applyTemplate(option, building, template);

            // Scroll to the top of our page after a slight delay (otherwise, when changing templates,
            // you can end up halfway down the page)
            setTimeout(() => { $anchorScroll(); }, 500);

        }

        /** 
         *  Deletes all services for the given building.
         */
        vm.nullifyTemplateData = function(option, building, id) {

            setTimeout(async () => {

                await vm.applyTemplate(option, building, {
                    ...blankTemplate,
                });

                // This is to ensure our UI selector displays properly
                // Reset everything for this particular building.
                building.servicesTemplateId = id;
                building.servicesTemplateTitle = "";

            }, 151);
        }

        vm.label = function () {

            let label = common.toTitleCase(vm.buildingType);

            if (vm.buildingType === 'reference') {
                label = vm.option.complianceMethod.complianceMethodCode === 'CMPerfSolution'
                    ? 'Reference'
                    : 'Deemed-to-Satisfy';
            }

            let s = `${vm.showCopyPrevious || vm.newJob ? label : ''} Services Template`;
            return s;
        }

    }
})();