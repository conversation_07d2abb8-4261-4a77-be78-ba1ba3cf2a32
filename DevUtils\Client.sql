USE [thermarate];

SELECT [client].[ClientId]
      ,[client].[ClientName]
      ,[client].[CreatedOn]
      ,[client].[CreatedByName]
      ,[client].[ModifiedOn]
      ,[client].[ModifiedByName]
      ,[client].[Deleted]
      ,[client].[AccountsFirstName]
      ,[client].[AccountsPhone]
      ,[client].[AccountsEmail]
      ,[client].[AccountsNote]
      ,[client].[ClientDefaultId]
      ,[client].[AccountsLastName]
      ,[client].[AccountsSameAsContact]
      ,[client].[IsFavourite]
      ,[client].[DefaultClientAssigneeUserId]
      ,[client].[ClientOptionsJson]
      ,[client].[ReportSettingsJson]
      ,[client].[ClientCostItemsJson]
  FROM [dbo].[RSS_Client] [client]
  LEFT JOIN [dbo].[RSS_User] [user] ON [client].[DefaultClientAssigneeUserId] = [user].[UserId]
  WHERE 1=1
	--AND [client].[Deleted] = 0
	--AND [client].[ClientName] = 'TEST_JC'
    --AND [client].[ClientId] = '7D76BC43-8273-AD85-FF3D-3A07209DA1F0'
  ORDER BY [ClientName]

--   UPDATE [dbo].[RSS_Client]
--   SET [Deleted] = 0
--   WHERE [ClientId] = 'c2d280bc-4274-c026-6c65-3a0b53d165e5'


--   UPDATE [dbo].[RSS_Client]
--   SET [ModifiedOn] = '2021-06-12 11:40:56.533'
--   WHERE [ClientId] = 'c2d280bc-4274-c026-6c65-3a0b53d165e5'
--   UPDATE [dbo].[RSS_Project]
--   SET [ModifiedOn] = '2021-06-12 11:40:56.533'
--   WHERE [ProjectId] = '4fdc16f0-9e35-416a-aeaa-a715d0efaaa9'
--   UPDATE [dbo].[RSS_StandardHomeModel]
--   SET [ModifiedOn] = '2021-06-12 11:40:56.533'
--   WHERE [StandardHomeModelId] = 'c509071c-16e3-45fe-91f1-8470d933bc68'