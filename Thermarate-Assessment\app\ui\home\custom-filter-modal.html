<md-dialog ng-controller="CustomFilterCtrl as vm" class="smallModal">
    <form>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Active Filter</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="row" layout-padding layout-wrap>
            <span>Select values below to filter the displayed Jobs on the previous list.</span>

            <div flex="100" layout layout-wrap>
                <!-- ******** Client ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Client</label>
                    <md-select name="client"
                                ng-model="vm.response.clientId" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.clientId"
                                    ng-repeat="item in vm.clientList track by item.clientId">
                            {{item.clientName}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- ******** Creator ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Creator</label>
                    <md-select name="client"
                                ng-model="vm.response.contactId" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.contactId"
                                    ng-repeat="item in vm.contactList track by item.contactId">
                            {{item.fullName}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- ******** Assessor ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Assessor</label>
                    <md-select name="client"
                                ng-model="vm.response.assessorUserId" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.userId"
                                    ng-repeat="item in vm.employeeList track by item.userId">
                            {{item.fullName}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- ******** Project Description ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Building Description</label>
                    <md-select name="client"
                                ng-model="vm.response.projectDescriptionCode" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.projectDescriptionCode"
                                    ng-repeat="item in vm.projectDescriptionList track by item.projectDescriptionCode">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- ******** Overdue ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Overdue</label>
                    <md-select name="client"
                                ng-model="vm.response.overdueCode" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.overdueCode"
                                    ng-repeat="item in vm.overdueList track by item.overdueCode">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- ******** Priority ******** -->
                <md-input-container class="md-block" flex="100">
                    <label>Priority</label>
                    <md-select name="client"
                                ng-model="vm.response.priorityCode" flex>
                        <md-option ng-value="">&nbsp;</md-option>
                        <md-option ng-value="item.priorityCode"
                                    ng-repeat="item in vm.priorityList track by item.priorityCode">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </md-input-container>

            </div>
        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.clearAll()" ng-if="vm.clearAllShow()">
                Clear All
            </md-button>
            <span flex></span>
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
            <md-button ng-click="vm.submitSelection()">
                Save
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>