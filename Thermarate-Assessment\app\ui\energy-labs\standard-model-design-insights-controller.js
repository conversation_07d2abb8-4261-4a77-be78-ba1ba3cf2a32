(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelDesignInsights', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
      },
      templateUrl: 'app/ui/energy-labs/standard-model-design-insights.html',
      controller: StandardModelDesignInsightsController,
      controllerAs: 'vm'
    });

  StandardModelDesignInsightsController.$inject = ['$scope', 'common', 'standardmodelservice'];

  function StandardModelDesignInsightsController($scope, common, standardmodelservice) {

    let vm = this;

    vm.designInsightMatch = determineMatch(vm.source);

    const unwatchA = $scope.$watch('vm.source.optionData.natHERSClimateZone', () => vm.designInsightMatch = determineMatch(vm.source));
    const unwatchB = $scope.$watch('vm.source.optionData.northOffset', () => vm.designInsightMatch = determineMatch(vm.source));

    $scope.$on('$destroy', function () {
      unwatchA();
      unwatchB();
    });

    function determineMatch(building) {

      if (!building.variableMetadata?.designInsightsEnabled)
        return false;

      // Otherwise need to need to look for a match where the current
      // climate zone AND north offset are round.
      const climateZone = building.optionData?.natHERSClimateZone;
      const northOffset = building.optionData?.northOffset;

      const match = building.variableMetadata.designInsights?.find(x => {
        const hasCZ = x.climateZones.includes(climateZone);
        const hasNO = x.northOffsets.includes(northOffset);

        return hasCZ && hasNO;
      });

      return match;
    }

  }

})();