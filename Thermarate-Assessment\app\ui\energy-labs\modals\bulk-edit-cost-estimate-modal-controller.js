(function () {
    'use strict';
    var controllerId = 'BulkEditCostEstimateModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$rootScope', '$mdDialog', 'selectvariablelinkservice', bulkEditCostEstimateModalController]);
    function bulkEditCostEstimateModalController(common, $scope, $rootScope, $mdDialog, selectvariablelinkservice) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;
        vm.type = $scope.type;
        vm.clientCostItems = $scope.clientCostItems;
        vm.zoneSummaryBuildingData = $scope.zoneSummaryBuildingData;
        vm.drawingAreas = $scope.drawingAreas;
        vm.drawingAreasTotals = $scope.drawingAreasTotals;
        vm.zoneSummary = $scope.zoneSummary;
        vm.envelopeSummary = $scope.envelopeSummary;

        vm.data = { costItemCustomSelected: false }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        vm.costItemChanged = function (costItem) {
            vm.data.costItemCustomSelected = false;
            vm.data.unitOfMeasure = costItem.unitOfMeasure;
            vm.data.ratePerUnit = costItem.ratePerUnit;
            vm.data.margin = costItem.margin;
            vm.data.rounding = costItem.rounding;
            vm.data.notes = costItem.notes;
        }

        vm.openVariableSelectModal = function (option) {
            let modalScope = $rootScope.$new();
            modalScope.zoneSummaryBuildingData = vm.zoneSummaryBuildingData;
            modalScope.drawingAreasData = {
                drawingAreas: vm.drawingAreas,
                drawingAreasTotals: vm.drawingAreasTotals
            };
            modalScope.zoneSummary = vm.zoneSummary;
            modalScope.varRefObj = vm.data.quantityVarRefJson != null ? JSON.parse(vm.data.quantityVarRefJson) : null;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/zone-summary-variable-select-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                multiple: true,
                skipHide: true
            }).then(newVarRefObj => {
                vm.data.quantityVarRefJson = newVarRefObj?.variablePathObj != null ? angular.toJson(newVarRefObj) : null;
            });
        }

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        vm.roundUpInt = common.roundUpInt;
    }
})();