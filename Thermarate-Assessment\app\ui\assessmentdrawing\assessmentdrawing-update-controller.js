(function () {
    // The AssessmentdrawingUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'AssessmentdrawingUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'assessmentservice', 'fileservice', 'assessmentdrawingservice', assessmentdrawingUpdateController]);
function assessmentdrawingUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  assessmentservice, fileservice, assessmentdrawingservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Assessment Drawing';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.assessmentDrawingId = null;
        vm.assessmentdrawing = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Assessment Drawing";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.assessmentDrawingId = $scope.assessmentDrawingId;
            }
            vm.hideActionBar = true;
        } else {
            vm.assessmentDrawingId = $stateParams.assessmentDrawingId;
        }

        // Get data for object to display on page
        var assessmentDrawingIdPromise = null;
        if (vm.assessmentDrawingId != null) {
            assessmentDrawingIdPromise = assessmentdrawingservice.getAssessmentDrawing(vm.assessmentDrawingId)
            .then(function (data) {
                if (data != null) {
                    vm.assessmentdrawing = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead
        vm.getassessments = function(searchTerm) {
            var filter = [{ field: "sealedExhaustFansDetails", operator: "startswith", value: searchTerm }];
            return assessmentservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateAssessment', function(event){
            event.stopPropagation();
            vm.createAssessment() // function to launch add modal;
            }));

        vm.createAssessment = function() {
            // Add logic to display create modal form.
        }

        vm.getfiles = function(searchTerm) {
            var filter = [{ field: "displayName", operator: "startswith", value: searchTerm }];
            return fileservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateFile', function(event){
            event.stopPropagation();
            vm.createFile() // function to launch add modal;
            }));

        vm.createFile = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("assessmentdrawing-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                assessmentdrawingservice.createAssessmentDrawing(vm.assessmentdrawing).then(function(data){
                    vm.assessmentdrawing = data;
                    vm.assessmentDrawingId = vm.assessmentdrawing.assessmentDrawingId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                assessmentdrawingservice.updateAssessmentDrawing(vm.assessmentdrawing).then(function(data){
                    if (data != null) {
                        vm.assessmentdrawing = data;
                        vm.assessmentDrawingId = vm.assessmentdrawing.assessmentDrawingId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            assessmentdrawingservice.deleteAssessmentDrawing(vm.assessmentDrawingId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            assessmentdrawingservice.undoDeleteAssessmentDrawing(vm.assessmentDrawingId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();