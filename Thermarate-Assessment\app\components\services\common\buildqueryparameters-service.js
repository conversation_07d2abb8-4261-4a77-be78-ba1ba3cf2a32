/// <reference path="buildqueryparameters-service.js" />
(function () {
    'use strict';
    
    angular.module('common').factory('buildqueryparameters', buildqueryparameters);

    function buildqueryparameters() {
        var service = {
            build: build
        };

        return service;

        function build(params, pageSize, pageIndex, sort, filters, aggregates) {
            if (params == null) {
                params = {};
            }

            params.pageSize = pageSize;
            params.pageIndex = pageIndex;

            if (sort != null && sort.field != null && sort.field != "") {
                params.sort = sort.field + "@@" + (sort.dir != null ? sort.dir : "asc");
            } else if (sort != null && Array.isArray(sort)) {

                params.sort = [];

                sort.forEach(component => {
                    params.sort.push(component.field + "@@" + (component.dir != null ? component.dir : "asc"));
                });
            }

            if (filters != null) {
                if (Array.isArray(filters)) {
                    params.filter = [];
                    for (var i = 0; i < filters.length; i++) {
                        var fl = filters[i];
                        if (ignoreFilter(fl) == false) {
                            params.filter = params.filter.concat(buildLine(fl));
                        }
                    }
                }
                else {
                    if (ignoreFilter(filters) == false) {
                        params.filter = params.filter.concat(buildLine(filters));
                    }
                }
            }

            return params;

        }

        // Ignore string filters that have no search value
        function ignoreFilter(fl) {
            if (fl.operator == "startswith" ||
                fl.operator == "endswith" ||
                fl.operator == "contains") {
                if (fl.value == undefined || fl.value == null || fl.value == "") {
                    return true;
                }
            }
            return false;
        }

        function buildLine(fl) {
            var value1 = fl.value;
            var value2 = fl.value2;
            var lines = [];
            if (fl.operator == "between") {
                var line = "(" + fl.field + "@@" + "gte" + "@@" + value1 + "@@" + (fl.valueType != null ? fl.valueType : "string") + "@@" + "and";
                lines.push(line);
                var line2 = fl.field + "@@" + "lte" + "@@" + value2 + "@@" + (fl.valueType != null ? fl.valueType : "string") + "@@" + (fl.logic != null ? fl.logic : "") + ")";
                lines.push(line2);
            }
            else {            
                var line = fl.field + "@@" + fl.operator + "@@" + value1 + "@@" + (fl.valueType != null ? fl.valueType : "string") + "@@" + (fl.logic != null ? fl.logic : "");
                lines.push(line);
            }
            return lines;
        }

    }
})();