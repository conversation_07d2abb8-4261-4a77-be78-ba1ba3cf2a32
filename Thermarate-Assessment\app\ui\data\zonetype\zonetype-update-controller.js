(function () {
    // The ZoneTypeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ZoneTypeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'zonetypeservice', zonetypeUpdateController]);
function zonetypeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  zonetypeservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Zone Type';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.zoneTypeCode = null;
        vm.zonetype = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Zone Type";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.zoneTypeCode = $scope.zoneTypeCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.zoneTypeCode = $stateParams.zoneTypeCode;
        }

        // Get data for object to display on page
        var zoneTypeCodePromise = null;
        if (vm.zoneTypeCode != null) {
            zoneTypeCodePromise = zonetypeservice.getZoneType(vm.zoneTypeCode)
            .then(function (data) {
                if (data != null) {
                    vm.zonetype = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("zonetype-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                zonetypeservice.createZoneType(vm.zonetype).then(function(data){
                    vm.zonetype = data;
                    vm.zoneTypeCode = vm.zonetype.zoneTypeCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                zonetypeservice.updateZoneType(vm.zonetype).then(function(data){
                    if (data != null) {
                        vm.zonetype = data;
                        vm.zoneTypeCode = vm.zonetype.zoneTypeCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            zonetypeservice.deleteZoneType(vm.zoneTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            zonetypeservice.undoDeleteZoneType(vm.zoneTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();