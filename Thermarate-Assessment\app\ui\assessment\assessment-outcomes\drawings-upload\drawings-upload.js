(function () {
    'use strict';
    angular
        .module('app')
        .component('drawingsUpload', {
            bindings: {
                assessment: '<',
                option: '<',
                isLocked: '<',
                isRequired: '<',
                onChange: '&',
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/drawings-upload/drawings-upload.html',
            controller: DrawingsUpload,
            controllerAs: 'vm'
        });

    DrawingsUpload.$inject = ['Upload', 'assessmentdrawingservice', 'fileservice'];

    function DrawingsUpload(Upload, assessmentdrawingservice, fileservice) {
        var vm = this;

        if (vm.option.assessmentDrawings == null) {
            vm.option.assessmentDrawings = [];
        }
        vm.uploadBusy = false;
        vm.progressMode = "determinate";
        vm.uploadFile = uploadFile;
        vm.downloadFile = downloadFile;
        vm.setToNull = setToNull;
        vm.fileChanged = function () {
            if (vm.onChange) {
                setTimeout(function () {
                    vm.onChange();
                });
            }
        }

        function setToNull(context, field) {
            if (context != undefined && context != null && context[field] != undefined) {
                context[field] = null;
                if (context[field + "Id"] != undefined) {
                    context[field + "Id"] = null;
                }
            }
            vm.fileChanged();
        }
        function downloadFile(fileDto) {
            var a = document.createElement('a');
            a.href = fileDto.url;
            //a.setAttribute('download', fileDto.fileName);
            a.target = '_blank';
            a.click();
        }
        function uploadFile(files, target, field, index) {
            if (files == null) {
                return;
            }

            if (Array.isArray(files)) {
                ///--TODO: Target should be pushed to a list for multiple file display
                for (var i = 0; i < files.length; i++) {
                    var $file = files[i];
                    uploadFile2($file, field);
                }
            }
            else {
                uploadFile2(files, field);
            }
        }
        function uploadFile2($file, field) {
            vm.uploadBusy = true;
            var target = { id: Math.random() };
            vm.option.assessmentDrawings.push(target);
            if ($file == undefined || $file == null) {
                return;
            }
            target[field + "UploadProgress"] = null;
            console.log(target);

            var url = "../api/Assessment/UploadFile?assessmentId=" + vm.assessment.assessmentId;
            url += "&jobId=" + vm.assessment.jobId;
            url += `&category=Assessment Drawing`
            url += `&classification=Generic`

            var promise = Upload.upload({
                url: url,
                method: "POST",
                file: $file
            });

            promise.progress(function (evt) {
                target[field + "UploadProgress"] = 80 * (evt.loaded / evt.total); // By 80 to allow server time to do stuff.
            }).success(function (data, status, headers, config) {
                target[field] = data;
                target[field + "UploadProgress"] = null;
                if (field == "attachment") {
                    vm.progressMode = "indeterminate";

                    assessmentdrawingservice.startProcessingPdf(
                        vm.assessment.assessmentId, 
                        data.url, 
                        data.fileId, 
                        data.fileName, 
                        null, 
                        vm.option.complianceOptionsId)
                    .then(function (splitFiles) {
                        vm.uploadBusy = false;
                        vm.progressMode = "determinate";
                        target.attachmentDisplayName = target.attachment.displayName;
                        target.drawingDescription = target.attachment.displayName;
                        target.archived = false;
                        target.deleted = false;
                        target.isIncludedInReport = true;
                        target.isShownToClient = true;
                        target.useDefaultStampPosition = false;
                        target.stampX = null;
                        target.stampY = null;
                        target.drawingNumber = vm.option.assessmentDrawings.length;

                        //Keep track of the Splitted files of the Parent
                        //Note: Only return File records (attachment)
                        target.splitFiles = _.map(splitFiles, function (rec) { return rec.attachment });

                        vm.option.assessmentDrawings.push(splitFiles);

                    });
                } else {
                    vm.uploadBusy = false;
                }
               // getFilesList();
            }).error(function (data, status, headers, config) {
                vm.uploadBusy = false;
            });
        }

        vm.deleteFile = function (item) {
            if (!item.attachment) { return; }
            var promise = null;
            var file = null;

            //Find file in Target list
            if (vm.option.assessmentDrawings != undefined && vm.option.assessmentDrawings != null) {
                for (var ii = 0; ii < vm.option.assessmentDrawings.length; ii++) {
                    if (vm.option.assessmentDrawings[ii].attachment && vm.option.assessmentDrawings[ii].attachment.fileId == item.attachment.fileId) {
                        file = vm.option.assessmentDrawings[ii];
                        break;
                    }
                }
            }

            if (file && file.splitFiles && file.splitFiles.length > 0) {
                //Remove parent and split files
                var fileIds = _.map(file.splitFiles, function (rec) { return rec.fileId });
                promise = fileservice.deleteSplitFiles(item.attachment.fileId, fileIds);
            } else {
                //Remove file
                promise = fileservice.deleteFile(item.attachment.fileId);
            }

            //Remove file from Target list
            promise.then(function () {
                vm.option.assessmentDrawings = _.without(vm.option.assessmentDrawings, _.findWhere(vm.option.assessmentDrawings, {
                    id: file.id
                }));
            });
        };

        vm.dropSuccess = function () {
            if (vm.option.assessmentDrawings != undefined && vm.option.assessmentDrawings != null) {
                for (var ii = 0; ii < vm.option.assessmentDrawings.length; ii++) {
                    vm.option.assessmentDrawings[ii].drawingNumber = ii + 1;
                }
            }
        }

    }
})();