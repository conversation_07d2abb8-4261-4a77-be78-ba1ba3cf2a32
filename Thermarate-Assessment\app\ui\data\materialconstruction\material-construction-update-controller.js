(function () {
    'use strict';

    const CONTROLLER_ID = 'materialConstructionUpdateController';

    angular.module('app')
        .controller(CONTROLLER_ID,
            ['materialconstruction', materialConstructionUpdateController]);

function materialConstructionUpdateController(materialconstruction) {

    var vm = this;

    vm.processing = false;

    vm.processNewDataset = function (file) {

        if (file == null)
            return;

        vm.processing = true;

        try {
            materialconstruction.processDataset(file).then(data => {
                vm.processing = false;
            });

        } catch (e) {
            console.log(e);
            vm.processing = false;
        } 
    }

}
})();