(function () {
    'use strict';
    var controllerId = 'CopyDesignToHomeModelModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'standardmodelservice', copyDesignToHomeModelModalController]);
    function copyDesignToHomeModelModalController(common, $scope, $mdDialog, standardmodelservice) {

        // - VARIABLES - //

        let vm = this;
        vm.isBusy = true;

        vm.modalTitle = $scope.modalTitle;
        var thisHomeModelId = $scope.thisHomeModelId;
        var projectId = $scope.projectId;

        // - INITIALISE - //

        standardmodelservice.getForProject(projectId, false, true).then(
            result => {
                if (result == null)
                    return;
                vm.homeModelList = result;
                vm.homeModelList = vm.homeModelList.filter(x => x.standardHomeModelId != thisHomeModelId);
                vm.isBusy = false;
            },
            error => {
                console.log(error);
                vm.isBusy = false
            }
        );

        // - HANDLES - //

        vm.selectHomeModel = function (homeModel) {
            vm.homeModelList.forEach(x => x.selected = false);
            homeModel.selected = true;
            vm.selectedHomeModelId = homeModel.standardHomeModelId;
        }

        vm.confirm = function () {
            $mdDialog.hide(vm.selectedHomeModelId);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();