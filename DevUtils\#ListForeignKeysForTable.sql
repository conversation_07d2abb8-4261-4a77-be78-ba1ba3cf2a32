DECLARE @TableName NVARCHAR(128) = 'AspNetUsers'; -- Replace with your table name

WITH ForeignKeyHierarchy AS (
    -- Layer 1: Foreign keys referencing the given table
    SELECT 
        fk.name AS ForeignKeyName,
        tp.name AS ReferencingTable,
        c.name AS ReferencingColumn,
        tr.name AS ReferencedTable,
        cr.name AS ReferencedColumn,
        1 AS Depth
    FROM sys.foreign_keys fk
    JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
    JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
    JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
    JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
    JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
    WHERE tr.name = @TableName

    UNION ALL

    -- Layer 2 and 3: Foreign keys referencing the tables found in previous layers
    SELECT 
        fk.name AS ForeignKeyName,
        tp.name AS ReferencingTable,
        c.name AS ReferencingColumn,
        tr.name AS ReferencedTable,
        cr.name AS ReferencedColumn,
        fkh.Depth + 1 AS Depth
    FROM sys.foreign_keys fk
    JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
    JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
    JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
    JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
    JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
    JOIN ForeignKeyHierarchy fkh ON tr.name = fkh.ReferencingTable
    WHERE fkh.Depth < 3 -- Limit recursion to 3 layers
)

SELECT * FROM ForeignKeyHierarchy
ORDER BY Depth, ReferencingTable, ReferencingColumn;
