(function () {
    'use strict';
    var controllerId = 'AnalyticsCtrl';
    angular.module('app')
        .controller(controllerId, ['$q', '$scope', '$mdDialog', 'jobanalyticsservice', analyticsModalController]);
    function analyticsModalController($q, $scope, $mdDialog, jobanalyticsservice) {

        var vm = this;

        vm.filterData = $scope.filterData;

        vm.availableComplianceMethods = $scope.availableComplianceMethods;
        vm.availableComplianceMethods.sort((a,b) => a.count < b.count ? 1 : -1);
        vm.assessmentMethod = vm.availableComplianceMethods[0];

        vm.certificationList = $scope.certificationList;
        vm.certificationList.sort((a,b) => a.count < b.count ? 1 : -1);
        vm.certification = vm.certificationList[0];

        vm.version1Only = true;
        vm.includeVirtualSims = false;

        vm.calcs = {};

        // Jobs List
        vm.showingToCnt = 10;
        var savedTableState = null;

        vm.refresh = function(tableState) {
            vm.isBusy = true;
            if (tableState != null) {
                savedTableState = tableState;
            }
            vm.showHER = vm.assessmentMethod.code == 'CMHouseEnergyRating' || vm.assessmentMethod.code == 'CMPerfSolutionHER' || vm.assessmentMethod.code == 'CMPerfWAProtocolHER';
            let sortBy = {};
            if (savedTableState?.sort != null) {
                sortBy.field = savedTableState.sort.predicate;
                sortBy.dir = savedTableState.sort.reverse ? "desc" : "asc";
            }
            jobanalyticsservice.getAnalytics(vm.showingToCnt, vm.certification.id, vm.assessmentMethod.code, vm.includeVirtualSims, vm.version1Only, vm.filterData.filters, vm.filterData.filterOptions, vm.filterData.appliedFilters, vm.filterData.searchFilter, sortBy).then(
                result => {
                    if (result == undefined || result == null)
                        return; // Its been cancelled so get out of here.
                    vm.calcs = result.data;
                    vm.isBusy = false;
                },
                error => vm.isBusy = false
            );
        }
        vm.refresh();

        vm.getMoreResults = function () {
            vm.showingToCnt += 10;
            vm.refresh();
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        };
    }
})();