<div id="top-of-dialog"
     class="padding-plz"
     style="width: 600px; overflow-x: hidden;"
     data-ng-controller='ConstructionImportCtrl as vm'>

    <!-- LOADING / WHITEOUT DURING FURTHER PROCESSING -->
    <div ng-if="vm.isProcessing"
         style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                background-color: rgba(255, 255, 255, 0.8); z-index: 777;
                display: flex; flex-direction: column; justify-content: center; align-items: center;">
        <h1>Processing, please wait...</h1>
    </div>

    <!-- Success! -->
    <div ng-if="vm.response.success == true">
        <h1>Import Success</h1>

        <div ng-if="vm.response.warnings.length == 0 && vm.response.failed == 0"
             style="margin: 1.5em 0;">
            Import completed successfully with no errors or warnings.
        </div>

        <div ng-if="vm.response.warnings.length > 0 && vm.response.failed == 0"
             style="margin: 1.5em 0;">
            Import completed successfully with some warnings.
        </div>

        <div ng-if="vm.response.failed > 0"
             style="margin: 1.5em 0;">
            Import completed successfully with some errors and/or warnings. This
            may signify certain rows have not been inserted into the database. You can
            try to fix the excel file and then try again.
        </div>

        <div style="font-weight: bold; margin: 0.4em 0;">Details:</div>
        <div>New: {{vm.response.inserted}}</div>
        <div>Updated: {{vm.response.updated}}</div>
        <div>Warnings: {{vm.response.warnings.length}}</div>
        <div>Failures: {{vm.response.failed}}</div>

        <div class="spacing-plz"
             ng-if="vm.response.errors.length > 0">
            <div style="font-weight: bold; margin: 0.4em 0;">
                The following errors were encountered:
            </div>

            <div ng-repeat="error in vm.response.errors track by $index">
                <span class="error-text">{{error}}</span>
            </div>
        </div>

        <div class="spacing-plz"
             ng-if="vm.response.warnings.length > 0">

            <div style="font-weight: bold; margin: 0.4em 0;">
                The following warnings were encountered:
            </div>

            <div ng-repeat="warning in vm.response.warnings track by $index">
                <span class="warning-text">{{warning}}</span>
            </div>
        </div>

        <div style="text-align: right; margin: 1em 0em;">
            <md-button class="md-primary md-raised"
                       style="margin-left: auto; margin: 1em;"
                       ng-click="vm.ok()">
                OK
            </md-button>
        </div>
    </div>

    <!-- FAILURE during PREPROCESSING-->
    <div ng-if="vm.response.success == false && vm.isInitialProcess == true">

        <h1 ng-if="vm.response.errors.length == 0">Warning</h1>
        <h1 ng-if="vm.response.errors.length > 0">Failure</h1>

        <div class="spacing-plz">
            Problems were found while pre-processing the excel file.
        </div>

        <!--
        If there are ERRORS at the initial stage it means we probably should NOT let
        the user try to process the file at all.
    -->
        <div class="spacing-plz"
             ng-if="vm.response.errors.length > 0">
            <div style="font-weight: bold; margin: 0.4em 0;">
                The following errors were encountered:
            </div>

            <div ng-repeat="error in vm.response.errors">
                <span class="error-text">{{error}}</span>
            </div>
        </div>

        <div class="spacing-plz"
             ng-if="vm.response.warnings.length > 0">

            <div style="font-weight: bold; margin: 0.4em 0;">
                The following warnings were encountered:
            </div>

            <div ng-repeat="warning in vm.response.warnings">
                <span class="warning-text">{{warning}}</span>
            </div>
        </div>

        <div class="spacing-plz"
             ng-if="vm.response.errors.length == 0">
            You can choose to import the excel file anyway, or you can attempt to fix the
            warnings within the excel file first and try again later. If you decide to import
            anyway, there may be missing data.
            <span style="font-weight: bold;">
                If the problem is related to missing manufacturers,
                colours etc, please ensure the values in those cells align with items that exist
                within the data config menus.
            </span>
        </div>

        <div class="spacing-plz"
             ng-if="vm.response.errors.length > 0">
            <div style="font-weight: bold;">
                Please ensure you are uploading the correct file (I.e. Construction Database
                for the Construction Database section and the Opening Database for the Opening
                Database section).
            </div>
            </br>
            <div>
                These errors would most likely cause un-recoverable problems during import.
                Please attempt to rectify the errors within the excel file if possible or
                contact support for help.
            </div>
        </div>

        <div class="spacing-plz"
             style="text-align: right;">
            <md-button ng-if="vm.response.errors.length == 0"
                       class="md-warn"
                       ng-click="vm.forceImport()">
                IMPORT
            </md-button>
            <md-button class="md-primary md-raised"
                       ng-click="vm.ok()">
                CANCEL
            </md-button>
        </div>
    </div>

    <!-- FAILURE during REAL PROCESSING! -->
    <!-- NOTE: There is probably no path to this happening at the moment...? -->
    <div ng-if="vm.response.success == false && vm.isInitialProcess == false">

        <h1>Failure</h1>

        <div class="spacing-plz">
            Errors were encountered while trying to process the excel file.
        </div>

        <!--
            If there are ERRORS at the initial stage it means we probably should NOT let
            the user try to process the file at all.
        -->
        <div class="spacing-plz"
             ng-if="vm.response.errors.length > 0">
            <div style="font-weight: bold; margin: 0.4em 0;">
                The following errors were encountered:
            </div>

            <div ng-repeat="error in vm.response.errors">
                <span class="error-text">{{error}}</span>
            </div>
        </div>

        <div class="spacing-plz"
             ng-if="vm.response.warnings.length > 0">

            <div style="font-weight: bold; margin: 0.4em 0;">
                The following warnings were encountered:
            </div>

            <div ng-repeat="warning in vm.response.warnings">
                <span class="warning-text">{{warning}}</span>
            </div>
        </div>

        <div class="spacing-plz">
            <div style="font-weight: bold;">
                Please contact support for help.
            </div>
        </div>

        <div class="spacing-plz"
             style="text-align: right;">
            <md-button class="md-warn md-raised"
                       ng-click="vm.ok()">
                CANCEL
            </md-button>
        </div>
    </div>

</div>   

<style>
    .padding-plz {
        padding: 2em 3em;
    }

    .spacing-plz {
        margin: 1.5em 0em;
    }

    .warning-text {
        color:orange;
    }

    .error-text {
        color: red;
    }
</style>    
