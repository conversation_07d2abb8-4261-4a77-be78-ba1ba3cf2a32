(function () {
    // The ProjectdescriptionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ProjectdescriptionUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', 
    '$state', 'projectdescriptionservice', 
    'assessmentcomplianceoptionservice', 'security', projectdescriptionUpdateController]);
function projectdescriptionUpdateController($rootScope, $scope, $mdDialog, $stateParams,
     $state,  projectdescriptionservice, 
     assessmentcomplianceoptionservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Building Description';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.projectDescriptionCode = null;
        vm.projectRatingModeList = [];
        vm.projectdescription = { };
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Building Description";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.projectDescriptionCode = $scope.projectDescriptionCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.projectDescriptionCode = $stateParams.projectDescriptionCode;
        }

        // Get data for object to display on page
        var projectDescriptionCodePromise = null;
        if (vm.projectDescriptionCode != null) {
            projectDescriptionCodePromise = projectdescriptionservice.getProjectDescription(vm.projectDescriptionCode)
            .then(function (data) {
                if (data != null) {
                    vm.projectdescription = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        vm.getProjectRatingModes = function () {
            return projectdescriptionservice.getProjectRatingModeList(null, null, null, null, null)
                .then(function (data) {
                    vm.projectRatingModeList = data.data;
                });
        }

        vm.getProjectRatingModes();

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("projectdescription-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                projectdescriptionservice.createProjectDescription(vm.projectdescription).then(function(data){
                    vm.projectdescription = data;
                    vm.projectDescriptionCode = vm.projectdescription.projectDescriptionCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                projectdescriptionservice.updateProjectDescription(vm.projectdescription).then(function(data){
                    if (data != null) {
                        vm.projectdescription = data;
                        vm.projectDescriptionCode = vm.projectdescription.projectDescriptionCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            projectdescriptionservice.deleteProjectDescription(vm.projectDescriptionCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            projectdescriptionservice.undoDeleteProjectDescription(vm.projectDescriptionCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.floorsWithGlazingChanged = function (count) {

            // Allow nulls ("No Default") on this page.
            if (count == null) {
                vm.floorsWithGlazing = null;
                vm.projectdescription.storeys = null;
                return;
            }

            let newFloors = assessmentcomplianceoptionservice.generateStoreys(count, vm.projectdescription.storeys);
            vm.floorsWithGlazing = newFloors.length;
            vm.projectdescription.storeys = newFloors;

        }

    }
})();