// Service used to retreive weather data for specific NCC Climate Zones 
// (e.g.climate zone 8, climate zone 43, etc)
(function () {
    'use strict';

    const SERVICE_ID = 'energyplusweatherdata';

    angular.module('appservices').factory(SERVICE_ID,
        ['common', 'config', '$http', 'Upload', energyplusweatherdata]);

    function energyplusweatherdata(common, config, $http, Upload) {

        const log = common.logger;
        const baseUrl = config.servicesUrlPrefix + 'energyplusweatherdata/';

        // These are the operations that are available from this service.
        var service = {
            get,
            processZip,
            excelArchiveUrl
        };

        /**
         * Returns a (giant) array of data broken up between years/months/days, including
         * information such as temperature, average rainfall, and so on.
         * 
         * @param {any} climateZone The NCC Climate Zone of the data we want.
         */
        async function get(climateZone) {

            return $http({
                url: baseUrl + 'get',
                params: { climateZone },
                method: 'GET',
            }).then(
                (response) => {

                    // Note: This object has a special 'DO_NOT_PROCESS_DATES' value set
                    // and THEN the data we want is in 'rows'.
                    if(response != null && response.data != undefined && response.data != null)
                        return response.data.rows;
                    else 
                        return null;
                },

                (error) => handleFail(error, "Error getting Climate Zone data."));
        }

        /**
         * Uploads a zip file to our server which is then extracted and processed into the
         * database, over-writing any prior .epw climate data.
         * 
         * @param {any} zipFile The zip file to upload. The EPW files should be directly in
         *                      the root of the archive.
         */
        function processZip(zipFile) {

            let url = baseUrl + 'ProcessZip';
            return $http({
                url: url, 
                method: "POST",
                data: zipFile,
                headers: {
                    "Content-Type": "application/zip"
                }
            }).then(
                (data) => handleSuccess(data, "Database successfully Upated"),
                (error) => handleFail(error, "Error processing zip file!")
            );
        }

        /**
         * This endpoint should be used in conjunction with the <a href="..." download>button</a>
         * tag to enable the browsers default 'save as' dialog button to pop-up.
         */
        function excelArchiveUrl() { return baseUrl + 'downloadZippedDataset' };
                
        /** Generic success handling function. Checks for success and returns data if so. */
        function handleSuccess(response, popupMessage = null) {
            // console.log("Got response", response);
            if (response != null && response.data != undefined && response.data != null) {

                if (popupMessage != null)
                    log.logSuccess(popupMessage);
                
                return response.data;
            }
            else {
                return null;
            }
        }

        /** Generic failure handling function. Logs a message with small popup. */
        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        return service;

    }
})();
