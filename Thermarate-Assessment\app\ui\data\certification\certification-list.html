<section id="certification-list-view" class="main-content-wrapper" data-ng-controller="CertificationListCtrl as vm">

    <div class="widget">
        <div layout="row"
             style="padding: 10px 0px; ">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
        </div>

        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Certification"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.certificationList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="certificationList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="title" class="can-sort text-left">Title</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.certificationList">
                        <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="certification-updateform({ certificationId: row.certificationId})">Select</md-button>  </td>
                        <td data-title="Title" class="text-left">{{::row.title }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
