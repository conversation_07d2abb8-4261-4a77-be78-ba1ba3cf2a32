<div class="el-plan-view-container" ng-class="{ 'small': vm.small }">

  <!-- Prev Button -->
  <div ng-click="vm.prevImage($event)"
       class="el-plan-view-container-prev-btn clickable"
       show="{{vm.planViewIndex !== 0}}">
    <i class="fa fa-angle-left fa-3x" />
  </div>

  <div class="image-container" ng-class="{ 'small': vm.small }">

        <img ng-if="vm.view3dFloorPlansEnabled"
             class="el-plan-view-3d-view-icon"
             src="../../../content/images/3d-modeling.png"
             ng-click="vm.open3dViewerModal($event)" />

        <img ng-if="vm.viewZoomEnabled"
             class="el-plan-view-magnify-icon"
             src="../../../content/images/zoom-in.png"
             ng-click="vm.openSwitcherModal($event)"/>

        <div>&nbsp;</div>

        <!-- Image -->
        <img ng-if="vm.planViewIndex > 0"
             class="plan-image-1"
             ng-class="{ 'run-move-left-1': vm.runAnimationLeft }"
             src="{{vm.planViews[vm.planViewIndex-1].file.url}}"
             alt="Building floor plan drawing">
        <img class="plan-image-2"
             ng-class="{ 'run-move-left-2': vm.runAnimationLeft, 'run-move-right-2': vm.runAnimationRight }"
             src="{{vm.planViews[vm.planViewIndex].file.url}}"
             alt="Building floor plan drawing">
        <img ng-if="vm.planViewIndex < vm.planViews.length-1"
             class="plan-image-3"
             ng-class="{ 'run-move-right-3': vm.runAnimationRight }"
             src="{{vm.planViews[vm.planViewIndex+1].file.url}}"
             alt="Building floor plan drawing">

        <span class="image-label" ng-show="vm.showLabel && vm.planViews.length >= 2">{{vm.planViews[vm.planViewIndex].label}}</span>

  </div>

  <!-- Next Button -->
  <div ng-click="vm.nextImage($event)"
       class="el-plan-view-container-next-btn clickable"
       show="{{vm.planViewIndex < (vm.planViews.length - 1)}}">
    <i class="fa fa-angle-right fa-3x" />
  </div>

</div>

<style>

    /* Container */
    .el-plan-view-container {
        position: relative;
        margin-bottom: 22px;
        height: 500px;
        display: grid;
        grid-template-columns: 50px 1fr 50px;
        justify-items: center;
        align-items: center;
    }
    .el-plan-view-container.small {
        margin-bottom: 0 !important;
    }

    /* Image Container */
    .image-container {
        position: relative;
        height: 450px;
        width: 425px;
        display: flex;
    }
    .image-container.small {
        height: 300px !important;
        width: 250px !important;
    }

    /* Image */
    .plan-image-1, .plan-image-2, .plan-image-3 {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        width: auto;
        height: 100%;
        max-height: 450px;
        max-width: 425px;
    }
    .plan-image-1 {
        left: -50%;
        opacity: 0;
    }
    .plan-image-2 {
        left: 50%;
        opacity: 1;
    }
    .plan-image-3 {
        left: 150%;
        opacity: 0;
    }
    /* Left Animations */
    .plan-image-1.run-move-left-1 { animation: move-left-1 0.5s; }
    .plan-image-2.run-move-left-2 { animation: move-left-2 0.5s; }
    .plan-image-3.run-move-left-3 { animation: move-left-3 0.5s; }
    @keyframes move-left-1 { 0% { left: 50%;  opacity: 1; } 100% { left: -50%; opacity: 0; } }
    @keyframes move-left-2 { 0% { left: 150%; opacity: 0; } 100% { left: 50%; opacity: 1;  } }
    /* Right Animations */
    .plan-image-1.run-move-right-1 { animation: move-right-1 0.5s; }
    .plan-image-2.run-move-right-2 { animation: move-right-2 0.5s; }
    .plan-image-3.run-move-right-3 { animation: move-right-3 0.5s; }
    @keyframes move-right-2 { 0% { left: -50%; opacity: 0; } 100% { left: 50%; opacity: 1;  } }
    @keyframes move-right-3 { 0% { left: 50%; opacity: 1;  } 100% { left: 150%; opacity: 0; } }

    /* Prev/Next Buttons */
    .el-plan-view-container-prev-btn,
    .el-plan-view-container-next-btn {
        z-index: 999;
        display: grid;
        grid-template-columns: 1fr;
        align-items: center;
        justify-items: center;
        height: 45px;
        width: 45px;
        border-radius: 50%;
        visibility: hidden;
    }
    .el-plan-view-container-prev-btn { margin-right: 22px; }
    .el-plan-view-container-next-btn { margin-left: 22px; }
    .el-plan-view-container-prev-btn > i { margin-top: -2px; margin-left: -3px; }
    .el-plan-view-container-next-btn > i { margin-top: -2px; margin-left: 4px; }
    .el-plan-view-container-prev-btn:hover,
    .el-plan-view-container-next-btn:hover {
        background-color: #eeeeee;
        transition: 150ms linear;
    }
    .el-plan-view-container:hover .el-plan-view-container-prev-btn[show='true'],
    .el-plan-view-container:hover .el-plan-view-container-next-btn[show='true'] {
        visibility: visible;
    }

    /* Image Label */
    .image-label {
        width: 100%;
        text-align: center;
        font-size: 16px;
        position: absolute;
        left: 0px;
        bottom: -36px;
    }

    /* 3d View & Magnify Icons */
    .el-plan-view-3d-view-icon,
    .el-plan-view-magnify-icon {
        width: 40px;
        height: 40px;
        position: absolute;
        z-index: 1;
        right: 10px;
        cursor: pointer;
        box-sizing: border-box;
        opacity: 0.8;
        visibility: hidden;
    }
    .el-plan-view-3d-view-icon {
        bottom: 50px;
        padding: 8px;
    }
    .el-plan-view-magnify-icon {
        bottom: 10px;
        padding: 10px;
    }
    .el-plan-view-container:hover .el-plan-view-3d-view-icon,
    .el-plan-view-container:hover .el-plan-view-magnify-icon {
        visibility: visible;
    }
    .el-plan-view-3d-view-icon:hover,
    .el-plan-view-magnify-icon:hover {
        transition: background-color 200ms, filter 200ms;
        background-color: #585858;
        filter: invert(100%);
    }

</style>