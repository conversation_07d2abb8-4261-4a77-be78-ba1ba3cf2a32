﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using OfficeOpenXml;

namespace TenureExtraction
{
    public class EnergyLoadLimitMatrixExtractor
    {

        // NON-0 Indexed values
        private const int CLIMATE_ZONE_COLUMN = 1;
        private const int REQUIRED_HER_COLUMN = 2;
        private const int LOWEST_LIVING_AREA_COLUMN = 3;
        private const int HEATING_LOAD_LIMIT_COLUMN = 4;
        private const int COOLING_LOAD_LIMIT_COLUMN = 5;
        
        
        private const int START_ROW = 2;
        private const int END_ROW = 2278;
        
        
        /// <summary>
        /// Returns a dictionary where
        ///     Key = Climate Zone
        ///     Value: EnergyLoadLimitRow 
        /// </summary>
        public static Dictionary<int, List<EnergyLoadLimitRow>> Extract(string pathToExcelFile, string sheetName)
        {
            var matrix = new Dictionary<int, List<EnergyLoadLimitRow>>();

            FileInfo existingFile = new FileInfo(pathToExcelFile);
            using (ExcelPackage package = new ExcelPackage(existingFile))
            {
                //get the first worksheet in the workbook
                ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];

                var row = START_ROW;
                do
                {
                    try
                    {
                        int climateZone = Convert.ToInt32(worksheet.Cells[row, CLIMATE_ZONE_COLUMN].Value);
                        string lowestLivingAreaFloorType =
                            worksheet.Cells[row, LOWEST_LIVING_AREA_COLUMN].Value as string;

                        if (lowestLivingAreaFloorType == null)
                            break; // We're at the end.

                        string rawHeatingLoadLimit = "";
                        string rawCoolingLoadLimit = "";

                        try
                        {
                            rawHeatingLoadLimit = worksheet.Cells[row, HEATING_LOAD_LIMIT_COLUMN].Value.ToString();
                            rawCoolingLoadLimit = worksheet.Cells[row, COOLING_LOAD_LIMIT_COLUMN].Value.ToString();
                        }
                        catch (Exception e)
                        {
                            ;
                        }


                        decimal? heatingLoadLimit = null;
                        if (rawHeatingLoadLimit != "N/A")
                            heatingLoadLimit = Convert.ToDecimal(rawHeatingLoadLimit);

                        decimal? coolingLoadLimit = null;
                        if (rawCoolingLoadLimit != "N/A")
                            coolingLoadLimit = Convert.ToDecimal(rawCoolingLoadLimit);



                        string requiredHerLogic = worksheet.Cells[row, REQUIRED_HER_COLUMN].Value.ToString();

                        if (requiredHerLogic == null)
                            throw new Exception(
                                $"Error encountered while parsing Energy Load Limit Matrix. Could not determine 'Required House Energy Rating' for row {row}");

                        requiredHerLogic = requiredHerLogic.Trim();

                        var containsLessThan = requiredHerLogic.Contains("<");
                        var containsGreaterThan = requiredHerLogic.Contains(">");

                        RequiredHerCheckLogic logic;
                        decimal? min;
                        decimal? max;

                        if (containsLessThan && containsGreaterThan)
                        {
                            logic = RequiredHerCheckLogic.Between;

                            var split = requiredHerLogic.Split(' ');
                            min = Convert.ToDecimal(split[1]);
                            max = Convert.ToDecimal(split[4]);
                        }
                        else if (containsLessThan)
                        {
                            logic = RequiredHerCheckLogic.LessThan;
                            var split = requiredHerLogic.Split(' ');
                            min = decimal.MinValue;
                            max = Convert.ToDecimal(split[1]);
                        }
                        else if (containsGreaterThan)
                        {
                            logic = RequiredHerCheckLogic.GreaterThan;
                            var split = requiredHerLogic.Split(' ');
                            min = Convert.ToDecimal(split[1]);
                            max = decimal.MaxValue;
                        }
                        else
                        {
                            logic = RequiredHerCheckLogic.Exactly;
                            min = Convert.ToDecimal(requiredHerLogic);
                            max = Convert.ToDecimal(requiredHerLogic);
                        }

                        if (min == null || max == null)
                            throw new Exception(
                                $"Error encountered while parsing Energy Load Limit Matrix. Malformed data at row {row}");


                        EnergyLoadLimitRow band = new EnergyLoadLimitRow(
                            climateZone,
                            logic,
                            min.Value,
                            max.Value,
                            lowestLivingAreaFloorType,
                            heatingLoadLimit,
                            coolingLoadLimit);

                        if (matrix.ContainsKey(climateZone))
                        {
                            matrix[climateZone].Add(band);
                        }
                        else
                        {
                            matrix.Add(climateZone, new List<EnergyLoadLimitRow>());
                            matrix[climateZone].Add(band);
                        }
                    }
                    catch (Exception e)
                    {
                        ;
                        throw;
                    }

                    row++;

                } while (true);
            }

            return matrix;
        }

        public static EnergyLoadLimitRow GetMatch(
            Dictionary<int, List<EnergyLoadLimitRow>> matrix, 
            int climateZone,
            decimal requiredHer,
            string floorType)
        {
            if (matrix.ContainsKey(climateZone) == false)
                throw new Exception(
                    $"Error: Energy Load Limit Matrix does not contain values for Climate Zone {climateZone}");

            var inZone = matrix[climateZone];

            var withFloorType = inZone.Where(x => x.LowestLivingAreaFloorType == floorType);

            var selected = withFloorType.FirstOrDefault(x =>
                (x.RequiredHerCheckLogic == RequiredHerCheckLogic.Exactly && requiredHer == x.MinHouseEnergyRating) ||
                (x.RequiredHerCheckLogic == RequiredHerCheckLogic.GreaterThan && requiredHer > x.MinHouseEnergyRating) ||
                (x.RequiredHerCheckLogic == RequiredHerCheckLogic.LessThan && requiredHer < x.MaxHouseEnergyRating) ||
                (x.RequiredHerCheckLogic == RequiredHerCheckLogic.Between &&
                 requiredHer > x.MinHouseEnergyRating && requiredHer < x.MaxHouseEnergyRating));

            if (selected == null)
                throw new Exception(
                    "Error: Unable to find match in Energy Load Limit Matrix with the given parameters.");

            return selected;
        }
    }


    public enum RequiredHerCheckLogic
    {
        LessThan,
        Exactly,
        Between,
        GreaterThan
    }

    public class EnergyLoadLimitRow
    {
        /// <summary>
        /// The Corresponding NatHERS climate zone.
        /// </summary>
        public readonly int ClimateZone;
        
        public readonly RequiredHerCheckLogic RequiredHerCheckLogic;
        
        public readonly decimal MinHouseEnergyRating;
        public readonly decimal MaxHouseEnergyRating;
        
        public readonly string  LowestLivingAreaFloorType;
        public readonly decimal? HeatingLoadLimit;
        public readonly decimal? CoolingLoadLimit;
        
        public EnergyLoadLimitRow(
            int climateZone, 
            RequiredHerCheckLogic requiredHerCheckLogic, 
            decimal minHouseEnergyRating, 
            decimal maxHouseEnergyRating, 
            string lowestLivingAreaFloorType, 
            decimal? heatingLoadLimit, 
            decimal? coolingLoadLimit)
        {
            ClimateZone = climateZone;
            RequiredHerCheckLogic = requiredHerCheckLogic;
            MinHouseEnergyRating = minHouseEnergyRating;
            MaxHouseEnergyRating = maxHouseEnergyRating;
            LowestLivingAreaFloorType = lowestLivingAreaFloorType;
            HeatingLoadLimit = heatingLoadLimit;
            CoolingLoadLimit = coolingLoadLimit;
        }
    }

}
