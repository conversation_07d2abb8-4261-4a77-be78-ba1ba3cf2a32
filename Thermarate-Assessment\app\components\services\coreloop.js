﻿// Possibly the most important 'service' in the entire project. Is responsible 
// for containing all logic that needs to run on a loop and continuously. This 
// service is NOT responsible for calling any http services.
//
// At the moment this just provides the functions which other components are
// using to kick off their own calculation loop using setInterval, however at
// some point the idea will most likely be have this control the main loop
// which just calculates everything (when on the assessment page, anyway).
//
// Alternate name: brrrrrrrrrrrrrrrr
(function () {
	'use strict';

	angular
		.module('appservices')
		.factory('coreLoop', [
			'zoneservice',
			'common',
			CoreLoop
		]);

	function CoreLoop(zoneservice, common) {
		
		var service = {
			runCoreLoop,
			computeAreas,
			computeFloorAreas,
			computeOpenability,
			computeLowestLivingAreaFloorType,
			computeFullMasonryExteriorAndInteriorWalls,
			computeZoneRequirementsAndAchievedRatings,
			computeSpaceRequirementsAndAchievedRatings,
			computeFloorTotals,
			computeGlassToExteriorWallRatios,
			clampAzimuths,
			computeDynamicSectors,
			computeEnergyUsage,
			computeChenathAreaCorrectionFactor,
			computeRuralLot,
			computeNarrowLot
		};
		
		
		/** 
		 * Runs ALL functions required for 1 "full" core loop. Should NOT be
		 * run in conjunction with the other sub functions.
		 * 
		 * @param building
		 * @param assessment
		 * @param nccClimateZone
		 */
		function runCoreLoop(building, nccClimateZone, nathersClimateZone = null){
			
			console.log("Running core loop...");
			//console.log('building.assessment:', building.assessment);
			computeAreas(building);
			computeOpenability(building);
			computeFloorAreas(building);
			computeGlassToExteriorWallRatios(building);
			computeLowestLivingAreaFloorType(building);
			computeFullMasonryExteriorAndInteriorWalls(building);
			computeZoneRequirementsAndAchievedRatings(building, nccClimateZone);
			
			if(nathersClimateZone != null)
				computeChenathAreaCorrectionFactor(building, nathersClimateZone)
			
			const interiorZones = zoneservice.interiorZones(building.zones);
			computeFloorTotals(building.storeys, interiorZones);

			clampAzimuths(building);
		}

		/** Calculates areas of surfaces and openings **/
		function computeAreas(building) {

			const horizontalOpenings = building.openings
				.filter(x => x.category.constructionCategoryCode === "HorizontalOpening")
				.map(x => [...x.elements]).reduce((a, b) => [...a, ...b], []);

			const verticalOpenings = building.openings
				.filter(x => x.category.constructionCategoryCode === "VerticalOpening")
				.map(x => [...x.elements]).reduce((a, b) => [...a, ...b], []);

			const interiorDoors = building.surfaces
				.filter(x => x.category.constructionCategoryCode === "InteriorDoor")
				.map(x => [...x.elements]).reduce((a, b) => [...a, ...b], []);


			building.surfaces.forEach(parent => {

				// In this loop, we only care about elements added  external (i.e. via scratch)
				// We have different rules (obviously) for elements added manually.
				parent.elements?.filter(x => x.source === "external").forEach(element => {
					const categoryCode = element.category.constructionCategoryCode;

					if (categoryCode === "InteriorWall" || categoryCode === "InteriorWallAdjacentToNeighbour" || 
						categoryCode === "InteriorWallAdjacentToRoofSpace" || categoryCode === "InteriorWallAdjacentToSubfloorSpace") {

						// Add all vertical openings AND interior doors
						const affectingOpenings = verticalOpenings
							?.filter(x => x.parentZoneId === element.parentZoneId && x.adjacentZoneNumber === element.adjacentZoneNumber);

						const permanentOpeningAdditionValue = affectingOpenings?.map(x => x.grossArea).reduce((a, b) => a + b, 0);

						const affectingDoors = interiorDoors.filter(x => x.parentZoneId === element.parentZoneId &&
							x.adjacentZoneNumber === element.adjacentZoneNumber);
						const doorAdditionValue = affectingDoors?.map(x => x.grossArea).reduce((a, b) => a + b, 0);

						element.grossArea = element.netArea + (permanentOpeningAdditionValue + doorAdditionValue);

					} else if (categoryCode === "IntermediateFloor" || categoryCode === "IntermediateFloorNeighbourBelow") {

						const affectingOpenings = horizontalOpenings
							?.filter(x => x.parentZoneId === element.parentZoneId && x.adjacentZoneNumber === element.adjacentZoneNumber);

						const additionValue = affectingOpenings?.map(x => x.grossArea).reduce((a, b) => a + b, 0);
						element.grossArea = element.netArea + additionValue;

					} else {

						if(element.width != null && element.height != null)
							element.grossArea = element.width * element.height;

						if(element.netArea === null || element.netArea === 0)
							element.netArea = element.grossArea;
					}


					// Gross area should never be less than parent area.
					if(element.grossArea < element.netArea)
						element.grossArea = element.netArea;

					// if calculated Gross Area is within 0.05 greater than Net Area:
					//      Gross Area = Net Area
					if(element.grossArea - element.netArea < 0.05)
						element.grossArea = element.netArea;

				});

				parent.netArea = parent.elements?.map(x => x.netArea).reduce((a,b) => a + b, 0);
				parent.grossArea = parent.elements?.map(x => x.grossArea).reduce((a,b) => a + b, 0);
				parent.width = parent.elements?.map(x => x.width).reduce((a, b) => a + b, 0);

			});

			building?.openings?.forEach(parent => {

				parent.elements?.filter(x => x.source === "external").forEach(element => {
					
					// Don't alter a grossArea that has been overridden.
					if( element.grossAreaIsManuallyOverridden === false && element.width != null && element.height != null) {
						element.grossArea = element.width * element.height;
					}

					if(element.netArea === null)
						element.netArea = element.grossArea;

				});

				parent.netArea = parent.elements?.map(x => x.netArea).reduce((a,b) => a + b, 0);
				parent.grossArea = parent.elements?.map(x => x.grossArea).reduce((a,b) => a + b, 0);

			});


		}
		
		function computeFloorAreas(building) {
			
			const conditionedZones = zoneservice.conditionedZones(building.zones);
			const unconditionedZones = zoneservice.unconditionedZones(building.zones);
			const garageZones = zoneservice.garageZones(building.zones);
			
			building.conditionedFloorArea = conditionedZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
			building.unconditionedFloorArea = unconditionedZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
			building.attachedGarageFloorArea = garageZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
			
		}
		
		function computeGlassToExteriorWallRatios(building) {

			// Get required construction elements
			const allExtWalls = building.surfaces
				?.filter(x => x.category.constructionCategoryCode === "ExteriorWall")
				.map(x => x.elements)
				.reduce((a, b) => [...a, ...b], []);

			const allExtGlazings = building.openings
				?.filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
				.map(x => x.elements)
				.reduce((a, b) => [...a, ...b], []);
			
			building.zones?.forEach(zone => {

				const wallsInZone = allExtWalls.filter(x => x.parentZoneId === zone.linkId);
				const glazingsInZone = allExtGlazings.filter(x => x.parentZoneId === zone.linkId);

				const exteriorWallArea = wallsInZone?.map(x => x.grossArea)?.reduce((a, b) => a + b, 0);
				const glazingArea = glazingsInZone?.map(x => x.grossArea)?.reduce((a, b) => a + b, 0);
				
				zone.exteriorWallArea = exteriorWallArea;
				zone.exteriorGlazingArea = glazingArea;
				
				if(exteriorWallArea === 0)
					zone.glassExteriorWallRatio = 0;
				else
					zone.glassExteriorWallRatio = (glazingArea / exteriorWallArea) * 100;
				
				if(zone.floorArea === 0)
					zone.glassFloorAreaRatio = 0;
				else
					zone.glassFloorAreaRatio = (glazingArea / zone.floorArea) * 100;
				
			});
		}

		/** Calculates the openability % of surfaces and openings */
		function computeOpenability(building) {

			building.openings.forEach(parent => {

				if(parent.openingStyle == null)
					return;

				parent.elements?.forEach(element => {
					innerComputeOpenability(parent, element);
				});
			});

			// Surfaces include interior and exterior doors, so we need to check them too.
			building.surfaces.forEach(parent => {

				if(parent.openingStyle == null)
					return;

				parent.elements?.forEach(element => {
					innerComputeOpenability(parent, element);
				});
			});

			function innerComputeOpenability(parent, element) {

				// Don't alter an openability that has been overridden.
				if(element.source === "manual" && element.openabilityIsManuallyOverridden === true)
					return;

				const nccOpeningStyle = parent.openingStyle.nccOpeningStyle;

				if(element.height == null || element.width == null || nccOpeningStyle == null) {
					element.openability = parent.openingStyle.defaultOpenability;
					return;
				}

				let openability = parent.openingStyle.defaultOpenability;

				// Check case A, B, C etc...
				if (element.height < 1.5 && element.width <= 1.2)
					openability = nccOpeningStyle.caseAOpenability;

				if (element.height < 1.5 && element.width > 1.2 && element.width < 2.7)
					openability = nccOpeningStyle.caseBOpenability;

				if (element.height < 1.5 && element.width >= 2.7)
					openability = nccOpeningStyle.caseCOpenability;

				if (element.height >= 1.5 && element.width <= 1.2)
					openability = nccOpeningStyle.caseDOpenability;

				if (element.height >= 1.5 && element.width > 1.2 && element.width < 2.7)
					openability = nccOpeningStyle.caseEOpenability;

				if (element.height >= 1.5 && element.width >= 2.7)
					openability = nccOpeningStyle.caseFOpenability;

				element.openability = openability;

			}

		}
		
		/** Computes the lowest living floor area type based on floor construction data */
		function computeLowestLivingAreaFloorType(building) {

			const storeys = building.storeys;
			const zones = building.zones;
			const parentSurfaces = building.surfaces;
			
			const floorSurfaces = parentSurfaces.filter(x => x.category.constructionCategoryCode === "ExteriorFloor" ||
				x.category.constructionCategoryCode === "ExteriorFloorElevated" ||
				x.category.constructionCategoryCode === "GroundFloor" ||
				x.category.constructionCategoryCode === "IntermediateFloor" ||
				x.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow");

			let allMatchingConstructions = [];

			if(storeys == null)
				return;


			for(let i = 0; i < storeys.length; i++) {

				const storey = storeys[i];

				const livingZones = zones.filter(x => x.storey === storey.floor &&
					(x.zoneActivity.zoneActivityCode === "ZALiving" ||
						x.zoneActivity.zoneActivityCode === "ZAKitchenLiving"));

				if (livingZones == null || livingZones.length === 0)
					continue;

				for(let ii = 0; ii < livingZones.length; ii++) {

					const zone = livingZones[ii];

					// Grab all floor constructions in zone, of all types.
					const elements = floorSurfaces
						.map(x => x.elements)
						.reduce((a, b) => [...a, ...b], []);

					// Grab all floor constructions in zone, of all types.
					const matchingConstructionsInZone = elements
						.filter(x => x.parentZoneId === zone.linkId);


					if(matchingConstructionsInZone.length === 0)
						continue;

					allMatchingConstructions = allMatchingConstructions.concat(matchingConstructionsInZone);

				}

				if(allMatchingConstructions.length > 0)
					break; // Break on first floor where we have any constructions.
			}

			if(allMatchingConstructions.length === 0) {
				building.lowestLivingAreaFloorType = null;
				return;
			}


			// If ALL instances are classified as Exterior Floor (Connected to Ground),
			// Lowest Living Area Floor Type = CSOG Only
			//     a. i.e. there are NO instances of Exterior Floor (Suspended) and NO instances of Intermediate
			if (allMatchingConstructions.every(x => x.category.constructionCategoryCode === "GroundFloor")) {
				building.lowestLivingAreaFloorType = "CSOG Only";
				return;
			}

			// 7.	If ALL instances are classified as either Exterior Floor (Suspended) OR Intermediate,
			// Lowest Living Area Floor Type = Suspended Only
			//    a. i.e. there are NO instances of Exterior Floor (Connected to Ground)
			if (allMatchingConstructions.every(x => x.category.constructionCategoryCode === "ExteriorFloor" ||
				x.category.constructionCategoryCode === "ExteriorFloorElevated" ||
				x.category.constructionCategoryCode === "IntermediateFloor" ||
				x.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow")) {
				building.lowestLivingAreaFloorType = "Suspended Only";
				return;
			}

			// 8. If there is at least one instance of Exterior Floor (Connected to Ground) AND at least one
			// instance of either Exterior Floor (Suspended) OR Intermediate,
			// Lowest Living Area Floor Type = CSOG and Suspended
			if (allMatchingConstructions.some(x => x.category.constructionCategoryCode === "GroundFloor") &&
				allMatchingConstructions.some(x => x.category.constructionCategoryCode === "ExteriorFloor" ||
					x.category.constructionCategoryCode === "ExteriorFloorElevated" ||
					x.category.constructionCategoryCode === "IntermediateFloor" ||
					x.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow")) {
				building.lowestLivingAreaFloorType = "CSOG & Suspended";
				return;
			}


		}

		/**
		 * Determines whether or not this building has full masonry construction based on construction data.
		 */
		function computeFullMasonryExteriorAndInteriorWalls(building) {

			if(building.zones == null)
				return;

			const zones = building.zones
				.filter(x => 
					x.zoneActivity?.zoneActivityCode === "ZAKitchenLiving" ||
					x.zoneActivity?.zoneActivityCode === "ZALiving" ||
					x.zoneActivity?.zoneActivityCode === "ZADayTime" ||
					x.zoneActivity?.zoneActivityCode === "ZABedroom" ||
					x.zoneActivity?.zoneActivityCode === "ZANightTime" ||
					x.zoneActivity?.zoneActivityCode === "ZAUnconditioned" ||
					x.zoneActivity?.zoneActivityCode === "ZAGarageConditioned");

			const exteriorWallElements = building.surfaces
				.filter(x => x.category.constructionCategoryCode === "ExteriorWall")
				.map(x => x.elements)
				.reduce((a, b) => [...a, ...b], []);

			const interiorWallElements = building.surfaces
				.filter(x => x.category.constructionCategoryCode === "InteriorWall" || 
					         x.category.constructionCategoryCode === "InteriorWallAdjacentToRoofSpace" ||
					         x.category.constructionCategoryCode === "InteriorWallAdjacentToSubfloorSpace")
				.map(x => x.elements)
				.reduce((a, b) => [...a, ...b], []);


			// 1.	Exterior Wall section
			// a.	For all Zones with Zone Activity = Kitchen / Living OR Living OR Day Time OR Bedroom OR Night Time 
			//      OR Unconditioned OR Garage Conditioned (i.e. ALL Zone Activity except Garage):
			//      i.	SUM the Net Area (m2) for ALL Child Rows in the Exterior Wall section
			//          1.	This is Exterior Wall Net Area (m2)
			//      ii.	SUM the Net Area (m2) for the Child Rows that are classified as “Full Masonry” (refer to note 
			//          below for determining Full Masonry)
			//          1. This is the Masonry Exterior Wall Net Area (m2)
			//      iii. Divide the Masonry Exterior Wall Net Area (m2) by Exterior Wall Net Area (m2) and multiply 
			//           by 100 (i.e. this will give a percentage) and record the value
			//          1. This is the Masonry Exterior Wall (%)
			// b.	Noting that the Child Rows where the Parent Zones that have Zone Activity = Garage are NOT included 
			//      in the calculations above
			let totalExteriorWallNetArea = 0;
			let totalMasonryExteriorWallNetArea = 0;

			for(let i = 0; i < zones.length; i++) {

				const zone = zones[i];

				const exteriorWallsInZone = exteriorWallElements
					.filter(x => x.parentZoneId === zone.linkId)

				const exteriorWallNetAreaInZone = exteriorWallsInZone
					.map(x => x.netArea)
					.reduce((a, b) => a + b, 0);

				const masonryExteriorWallNetAreaInZone = exteriorWallsInZone
					.filter(x => x.isFullMasonry)
					.map(x => x.netArea)
					.reduce((a, b) => a + b, 0);

				totalExteriorWallNetArea += exteriorWallNetAreaInZone;
				totalMasonryExteriorWallNetArea += masonryExteriorWallNetAreaInZone;

			}

			const masonryExteriorWallPercent =  (totalMasonryExteriorWallNetArea / totalExteriorWallNetArea) * 100;


			// 2. Interior Wall section. Same as above, but for interior walls.
			let totalInteriorWallNetArea = 0;
			let totalMasonryInteriorWallNetArea = 0;

			for(let i = 0; i < zones.length; i++) {

				const zone = zones[i];

				const interiorWallsInZone = interiorWallElements
					.filter(x => x.parentZoneId === zone.linkId)

				const interiorWallNetAreaInZone = interiorWallsInZone
					.map(x => x.netArea)
					.reduce((a, b) => a + b, 0);

				const masonryInteriorWallNetAreaInZone = interiorWallsInZone
					.filter(x => x.isFullMasonry)
					.map(x => x.netArea)
					.reduce((a, b) => a + b, 0);

				totalInteriorWallNetArea += interiorWallNetAreaInZone;
				totalMasonryInteriorWallNetArea += masonryInteriorWallNetAreaInZone;

			}

			const masonryInteriorWallPercent =  (totalMasonryInteriorWallNetArea / totalInteriorWallNetArea) * 100;

			// 3. If Masonry Exterior Wall (%) ≥ 90.00 AND Masonry Internal Wall (%) ≥ 90.00, Full Masonry Exterior & 
			//    Interior Walls = Yes
			// 4. For all other scenarios, Full Masonry Exterior & Interior Walls = No
			building.masonryWalls = (masonryExteriorWallPercent >= 90 && masonryInteriorWallPercent >= 90);

		}

		function determineTotalArtificialLightingInZone(artificialLightingConstructions, zone) {
			// Loop over all parent constructions.
			let totalArtificialLightingInZone = 0;

			for (let i = 0; i < artificialLightingConstructions?.length; i++) {

				const parent = artificialLightingConstructions[i];

				let contribution = 0;

				let elementsInZone = parent.elements.filter(x => x.parentZoneId === zone.linkId &&
					x.serviceType.serviceTypeCode !== "NotApplicable");

				if (elementsInZone == null || elementsInZone.length === 0)
					continue;

				if (parent.serviceCategory.serviceCategoryCode === "ArtificialLighting") {
					contribution += elementsInZone
						.map(child => parent.lampPowerRating * child.number * child.serviceControlDevice.adjustmentFactor)
						.reduce((a, b) => a + b, 0);
				}

				if (parent.serviceCategory.serviceCategoryCode === "ExhaustFans") {
					contribution += elementsInZone
						.map(child => parent.lampPowerRating * child.number)
						.reduce((a, b) => a + b, 0);
				}

				if (parent.serviceCategory.serviceCategoryCode === "CeilingFans") {
					contribution += elementsInZone
						.map(child => parent.lampPowerRating * child.number)
						.reduce((a, b) => a + b, 0);
				}

				totalArtificialLightingInZone += contribution;
			}
			return totalArtificialLightingInZone;
		}

		/**
		 * Automatically calculates required and achieved values for air movement, ventilation, and light across all
		 * zones.
		 *
		 * @param {any} building The building to run calculations on.
		 * @param {any} nccClimateZone The NCC climate zone 
		 */
		function computeZoneRequirementsAndAchievedRatings(building, nccClimateZone) {

			if(building == null || building.storeys == null || building.zones == null)
				return;

			const ventilationSurfaces = building.surfaces
				.filter(x => x.category.constructionCategoryCode === "ExteriorDoor");

			const ventilationOpenings = building.openings
				.filter(x => x.category.constructionCategoryCode === "ExteriorGlazing" ||
					x.category.constructionCategoryCode === "Skylight" ||
					x.category.constructionCategoryCode === "RoofWindow")

			const allVentilationConstructions = [...ventilationSurfaces, ...ventilationOpenings];

			const artificialLightingConstructions = building.services
				?.filter(x => x.serviceCategory.serviceCategoryCode === "ArtificialLighting" ||
					x.serviceCategory.serviceCategoryCode === "ExhaustFans" ||
					x.serviceCategory.serviceCategoryCode === "CeilingFans");

			for (let i = 0; i < building.storeys.length; i++)
			{
				let zones = building.zones
					.filter(s => s.storey === i && s.zoneActivity?.zoneActivityCode !== "ZAGroundSurface");

				if (zones == null || zones.length === 0)
					continue;

				zones.forEach(zone => {

					// Note: We DO NOT re-calculate zone.floorArea (even if for instance floor constructions change).
					// This might be requested in future...
					const naturalLightAchieved = building.openings
						.filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
						.map(x => x.elements
							.filter(x => x.parentZoneId === zone.linkId)
							.map(ele => ele.width * ele.height)
							.reduce((a, b) => a + b, 0))
						.reduce((a, b) => a + b, 0);

					const naturalSkylightsLightAchieved = building.openings
						.filter(x => x.category.constructionCategoryCode === "Skylight" ||
							x.category.constructionCategoryCode === "RoofWindow")
						.map(x => x.elements
							.filter(x => x.parentZoneId === zone.linkId)
							.map(ele => ele.grossArea * 3.3333333333)
							.reduce((a, b) => a + b, 0))
						.reduce((a, b) => a + b, 0);

					const ventilationConstructionsInZone = allVentilationConstructions
						.map(x => x.elements.filter(x => x.parentZoneId === zone.linkId))
						.reduce((a, b) => [...a, ...b], []);


					const ventilationAchieved = ventilationConstructionsInZone
						.map(ele => ele.netArea * (ele.openability * 0.01))
						.reduce((a, b) => a + b, 0);

					let totalArtificialLightingInZone = determineTotalArtificialLightingInZone(artificialLightingConstructions, zone);

					zone.naturalLightAchievedM2 = naturalLightAchieved + naturalSkylightsLightAchieved;
					zone.naturalLightRequiredPercent = zone.zoneType?.zoneTypeCode === "ZTHabitableRoom" ? 10 : 0;
					zone.naturalLightRequiredM2 = (zone.naturalLightRequiredPercent / 100) * zone.floorArea;

					zone.ventilationAchievedM2 = ventilationAchieved;
					zone.ventilationRequiredPercent = zone.zoneType?.zoneTypeCode === "ZTHabitableRoom" ? 5 : 0;
					zone.ventilationRequiredM2 = (zone.ventilationRequiredPercent / 100) * zone.floorArea;

					zone.airMovementAchievedM2 = ventilationAchieved;
					zone.airMovementRequiredPercent = calculation3(zone, nccClimateZone);
					zone.airMovementRequiredM2 = (zone.airMovementRequiredPercent / 100) * zone.floorArea;

					zone.lampPowerMaximumWM2 = zone.zoneType?.lampPowerMaximumWM2;
					zone.lampPowerMaximumW = zone.lampPowerMaximumWM2 * zone.floorArea;
					zone.lampPowerAchievedW = totalArtificialLightingInZone;

					// Calculate natural ventilation.
					zone.naturallyVentilated = ventilationConstructionsInZone.some(x => x.openability > 0);

				});

			}

			/**
			 * LOCAL: Returns the required air movement for the given zone based upon the
			 * current NCC Climate Zone. Returns as a "full percentage" i.e.
			 * 5 = "5%", NOT "500%"
			 *
			 * @param {any} zone The Zone to compare.
			 * @param {any} nccClimateZone The NCC climate zone affects the calculation
			 */
			function calculation3(zone, nccClimateZone) {

				let code = Number(nccClimateZone);
				
				if (zone?.zoneType?.zoneTypeCode !== 'ZTHabitableRoom')
					return 0;

				if (code === 1 ||
					code === 2) {

					if (!zone.ceilingFan && !zone.evaporativeCooler) return 10;
					if (zone.ceilingFan && !zone.evaporativeCooler) return 7.5;
					if (!zone.ceilingFan && zone.evaporativeCooler) return 10;
					if (zone.ceilingFan && zone.evaporativeCooler) return 10;
					return 0;

				} else if (code === 3) {

					if (!zone.ceilingFan && !zone.evaporativeCooler) return 10;
					if (zone.ceilingFan && !zone.evaporativeCooler) return 7.5;
					if (!zone.ceilingFan && zone.evaporativeCooler) return 7.5;
					if (zone.ceilingFan && zone.evaporativeCooler) return 7.5;
					return 0;

				} else if (code === 4) {

					if (!zone.ceilingFan && !zone.evaporativeCooler) return 10;
					if (zone.ceilingFan && !zone.evaporativeCooler) return 5;
					if (!zone.ceilingFan && zone.evaporativeCooler) return 5;
					if (zone.ceilingFan && zone.evaporativeCooler) return 5;
					return 0;

				} else if (code === 5) {

					if (!zone.ceilingFan && !zone.evaporativeCooler) return 7.5;
					if (zone.ceilingFan && !zone.evaporativeCooler) return 5;
					if (!zone.ceilingFan && zone.evaporativeCooler) return 5;
					if (zone.ceilingFan && zone.evaporativeCooler) return 7.5;
					return 0;

				} else if (code === 6 ||
					code === 7 ||
					code === 8) {
					return 5;
				}

				return 0; // ERROR STATE really.
			}

		}
		
		function computeSpaceRequirementsAndAchievedRatings(building) {

			const artificialLightingConstructions = building.services
				?.filter(x => x.serviceCategory.serviceCategoryCode === "ArtificialLighting" ||
					x.serviceCategory.serviceCategoryCode === "ExhaustFans" ||
					x.serviceCategory.serviceCategoryCode === "CeilingFans");
			
			building.spaces.forEach(space => {

				let totalArtificialLightingInZone = determineTotalArtificialLightingInZone(artificialLightingConstructions, space);

				space.lampPowerMaximumWM2 = space.zoneType?.lampPowerMaximumWM2;
				space.lampPowerMaximumW = space.lampPowerMaximumWM2 * space.floorArea;
				space.lampPowerAchievedW = totalArtificialLightingInZone;
			});
			
		}

		/**
		 * Calculates the floor totals and adds them to the 'vm.source.zones.stories' object
 		 */
		function computeFloorTotals(storeys, interiorZones) {
			
			storeys?.forEach(storey => {
				const zonesInStorey = interiorZones.filter(zone => zone.storey === storey.floor);

				storey.ceilingArea = zonesInStorey.map(x => x.ceilingArea).reduce((a, b) => a + b, 0);
				storey.floorArea= zonesInStorey.map(x => x.floorArea).reduce((a, b) => a + b, 0);
			});
		}
		
		function computeEnergyUsage(building, option) {
			
			if(building.energyUsageSummary == null)
				return;
			
			const ruleset = option.certification.chenathRulesetCode;
			
			const correctionFactor = ruleset === "NCC 2019"
				? Number(building.ncc2019AreaCorrectionFactor.toFixed(8))
				: Number(building.ncc2022AreaCorrectionFactor.toFixed(8));
			
			const conditionedFloorArea = Number(building.conditionedFloorArea.toFixed(1));
			
			const heatingLoadFromMonthly = building.energyUsageSummary.monthlyHeating.reduce((a, b) => a + b) ;
			building.heating = (heatingLoadFromMonthly / conditionedFloorArea) * correctionFactor;
			
			const coolingLoadFromMonthly = building.energyUsageSummary.monthlyCooling.reduce((a, b) => a + b)
																		+ (building.energyUsageSummary.outputSummaryData?.ceilingFanEnergyUse || 0);
			building.cooling = (coolingLoadFromMonthly / conditionedFloorArea)  * correctionFactor;
			
			building.totalEnergyLoad = ((heatingLoadFromMonthly + coolingLoadFromMonthly) / conditionedFloorArea) * correctionFactor;
			
		}

		// Note that this is 0 indexed, NatHERS climate zones are 1 indexed. So For NatHERS zone = 1, use index 0 etc
		const chenath2019StaticCorrectionFactors = [
			{ a: -1.18798688E-14, b:  3.30295867E-11, c: -3.43614968E-08, d:  1.68021265E-05, e: -4.41898846E-03, f: 4.19200551E-01 },
			{ a: -9.75316238E-15, b:  2.71796754E-11, c: -2.86309894E-08, d:  1.45181471E-05, e: -4.15259368E-03, f: 4.20913016E-01 },
			{ a: -7.53731812E-15, b:  2.08748515E-11, c: -2.21334992E-08, d:  1.16668395E-05, e: -3.79676175E-03, f: 4.18989960E-01 },
			{ a: -6.88809806E-15, b:  2.05393447E-11, c: -2.43599628E-08, d:  1.49526703E-05, e: -5.00971278E-03, f: 5.61951008E-01 },
			{ a: -1.56095593E-14, b:  4.27713530E-11, c: -4.34705997E-08, d:  2.04852714E-05, e: -5.10244178E-03, f: 4.70167750E-01 },
			{ a: -3.69253568E-15, b:  1.05874378E-11, c: -1.23616805E-08, d:  7.86214295E-06, e: -3.32606945E-03, f: 4.17493461E-01 },
			{ a: -1.11459534E-14, b:  3.09090112E-11, c: -3.24246793E-08, d:  1.64612853E-05, e: -4.70737131E-03, f: 4.75725374E-01 },
			{ a:  0.00000000E+00, b:  8.03643986E-13, c: -3.03704960E-09, d:  4.29478970E-06, e: -2.88091926E-03, f: 4.00312785E-01 },
			{ a: -3.85383319E-15, b:  1.10456264E-11, c: -1.28488311E-08, d:  8.11094086E-06, e: -3.32609715E-03, f: 4.11751891E-01 },
			{ a: -3.40158114E-15, b:  1.06867448E-11, c: -1.39362552E-08, d:  9.74856936E-06, e: -4.10626385E-03, f: 5.10384360E-01 },
			{ a: -6.76171937E-16, b:  3.02093752E-12, c: -6.10654028E-09, d:  6.33328919E-06, e: -3.38181485E-03, f: 4.55891457E-01 },
			{ a:  5.67412978E-16, b: -1.17135512E-13, c: -3.35675440E-09, d:  5.36749504E-06, e: -3.27397362E-03, f: 4.52597504E-01 },
			{ a:  3.60414703E-15, b: -8.67841614E-12, c:  5.50090251E-09, d:  1.29960260E-06, e: -2.53682387E-03, f: 4.10013852E-01 },
			{ a:  9.24973391E-15, b: -2.31783318E-11, c:  1.85620926E-08, d: -3.47324009E-06, e: -1.78213053E-03, f: 3.70344605E-01 },
			{ a:  7.01759306E-15, b: -1.77086584E-11, c:  1.38519961E-08, d: -1.69157079E-06, e: -2.18486651E-03, f: 4.07450810E-01 },
			{ a:  6.32917359E-15, b: -1.56252296E-11, c:  1.16555203E-08, d: -8.50183826E-07, e: -2.25574010E-03, f: 4.00288384E-01 },
			{ a:  4.54478866E-15, b: -1.12352519E-11, c:  7.96205890E-09, d:  3.56055916E-07, e: -2.38920812E-03, f: 4.04860599E-01 },
			{ a:  7.82021869E-15, b: -1.95550348E-11, c:  1.53084626E-08, d: -2.21628261E-06, e: -2.02369872E-03, f: 3.86897393E-01 },
			{ a: -2.96833303E-15, b:  8.85450681E-12, c: -1.10699114E-08, d:  7.67769595E-06, e: -3.39810407E-03, f: 4.31595766E-01 },
			{ a:  6.39090792E-15, b: -1.56558826E-11, c:  1.15707873E-08, d: -8.33133806E-07, e: -2.20568397E-03, f: 3.92954519E-01 },
			{ a:  1.03147878E-14, b: -2.56393863E-11, c:  2.02945852E-08, d: -3.68330004E-06, e: -1.96421905E-03, f: 4.03992325E-01 },
			{ a:  1.00819730E-14, b: -2.53897114E-11, c:  2.06642198E-08, d: -4.31870411E-06, e: -1.62415316E-03, f: 3.59116219E-01 },
			{ a:  9.61480853E-15, b: -2.38273710E-11, c:  1.87569386E-08, d: -3.31638159E-06, e: -1.81442633E-03, f: 3.71048499E-01 },
			{ a:  7.09846601E-15, b: -1.73394692E-11, c:  1.28837303E-08, d: -1.15868319E-06, e: -2.14061620E-03, f: 3.85460032E-01 },
			{ a:  4.90264751E-15, b: -1.11865168E-11, c:  6.76605667E-09, d:  1.26036151E-06, e: -2.37815022E-03, f: 3.77000258E-01 },
			{ a:  1.15498934E-14, b: -2.88417852E-11, c:  2.33054522E-08, d: -4.96185294E-06, e: -1.60144805E-03, f: 3.65640635E-01 },
			{ a:  3.43182318E-15, b: -7.93556459E-12, c:  4.44533348E-09, d:  1.88284884E-06, e: -2.64389558E-03, f: 4.16238526E-01 },
			{ a:  4.34088585E-15, b: -1.09921599E-11, c:  8.16500502E-09, d: -2.03097382E-08, e: -2.23652794E-03, f: 3.83070353E-01 },
			{ a: -1.24512953E-14, b:  3.46966369E-11, c: -3.62281417E-08, d:  1.78241075E-05, e: -4.68213669E-03, f: 4.41488798E-01 },
			{ a: -8.86992739E-15, b:  2.46690467E-11, c: -2.59598131E-08, d:  1.31953241E-05, e: -3.81504664E-03, f: 3.87407836E-01 },
			{ a: -1.46783904E-14, b:  4.04223366E-11, c: -4.13785918E-08, d:  1.96918533E-05, e: -4.90345634E-03, f: 4.46265623E-01 },
			{ a: -1.79405042E-14, b:  4.96781786E-11, c: -5.11077522E-08, d:  2.43289398E-05, e: -5.96141723E-03, f: 5.39405496E-01 },
			{ a: -1.16203893E-14, b:  3.25044576E-11, c: -3.41495780E-08, d:  1.69698912E-05, e: -4.51640747E-03, f: 4.29133126E-01 },
			{ a: -8.88777962E-15, b:  2.46906997E-11, c: -2.60301033E-08, d:  1.33512805E-05, e: -4.05033593E-03, f: 4.32540149E-01 },
			{ a: -1.74882838E-14, b:  4.86304852E-11, c: -5.02940819E-08, d:  2.40728691E-05, e: -5.89074065E-03, f: 5.25804593E-01 },
			{ a: -1.06524102E-14, b:  2.96137764E-11, c: -3.12838414E-08, d:  1.61450960E-05, e: -4.74620378E-03, f: 4.93940051E-01 },
			{ a: -9.17240375E-15, b:  2.54993203E-11, c: -2.67793292E-08, d:  1.35317610E-05, e: -3.91062536E-03, f: 3.98876440E-01 },
			{ a: -1.01885809E-14, b:  2.81110302E-11, c: -2.91605351E-08, d:  1.44440171E-05, e: -4.15439330E-03, f: 4.23815871E-01 },
			{ a: -8.74180438E-15, b:  2.43379497E-11, c: -2.57712283E-08, d:  1.33292991E-05, e: -4.05325989E-03, f: 4.28630005E-01 },
			{ a: -7.12610146E-15, b:  1.98700969E-11, c: -2.12493396E-08, d:  1.12848809E-05, e: -3.73862800E-03, f: 4.16784399E-01 },
			{ a: -6.17911276E-15, b:  1.73882550E-11, c: -1.90920617E-08, d:  1.07313501E-05, e: -3.84859329E-03, f: 4.48082586E-01 },
			{ a: -5.54256321E-15, b:  1.54333135E-11, c: -1.69707777E-08, d:  9.83788889E-06, e: -3.82897022E-03, f: 4.68515448E-01 },
			{ a: -3.23719545E-15, b:  9.39283511E-12, c: -1.12800845E-08, d:  7.49799287E-06, e: -3.30647177E-03, f: 4.18220113E-01 },
			{ a:  3.74772098E-15, b: -9.00838332E-12, c:  5.78343950E-09, d:  1.14658975E-06, e: -2.52815226E-03, f: 4.10328309E-01 },
			{ a:  5.44123691E-16, b: -2.85587146E-13, c: -2.71744169E-09, d:  4.59815131E-06, e: -3.04248176E-03, f: 4.29200045E-01 },
			{ a:  2.19857517E-15, b: -5.10928061E-12, c:  2.37236804E-09, d:  2.34857893E-06, e: -2.67047970E-03, f: 4.11304310E-01 },
			{ a:  7.65104267E-15, b: -1.91707808E-11, c:  1.50628358E-08, d: -2.22984346E-06, e: -2.00489085E-03, f: 3.86235053E-01 },
			{ a:  6.13251026E-15, b: -1.50892225E-11, c:  1.11407815E-08, d: -6.54641818E-07, e: -2.26895757E-03, f: 3.99609069E-01 },
			{ a:  7.27662696E-15, b: -1.84515097E-11, c:  1.47770601E-08, d: -2.38364817E-06, e: -1.93855401E-03, f: 3.79802956E-01 },
			{ a:  2.51557277E-15, b: -5.88312714E-12, c:  3.04656410E-09, d:  2.07563820E-06, e: -2.57924533E-03, f: 4.00870496E-01 },
			{ a:  7.61341356E-15, b: -1.95811890E-11, c:  1.61993698E-08, d: -3.20561822E-06, e: -1.77353912E-03, f: 3.69047436E-01 },
			{ a:  8.85385742E-15, b: -2.21579907E-11, c:  1.73682136E-08, d: -2.51749541E-06, e: -2.23980256E-03, f: 4.32958930E-01 },
			{ a:  8.31609750E-15, b: -2.09503807E-11, c:  1.66973085E-08, d: -2.75870972E-06, e: -1.97691812E-03, f: 3.89930418E-01 },
			{ a:  4.29912375E-15, b: -1.03911659E-11, c:  6.83183863E-09, d:  1.09673456E-06, e: -2.58300215E-03, f: 4.22480241E-01 },
			{ a:  1.43349927E-14, b: -3.68395193E-11, c:  3.14938044E-08, d: -8.32644630E-06, e: -1.24655870E-03, f: 3.72750479E-01 },
			{ a:  6.35829179E-15, b: -1.59942777E-11, c:  1.22178297E-08, d: -9.60613359E-07, e: -2.35240885E-03, f: 4.19117091E-01 },
			{ a:  9.90961111E-15, b: -2.51275349E-11, c:  2.06762902E-08, d: -4.47290694E-06, e: -1.58155082E-03, f: 3.55869151E-01 },
			{ a:  1.38426183E-14, b: -3.51405694E-11, c:  2.93786201E-08, d: -7.21200296E-06, e: -1.42025914E-03, f: 3.78956360E-01 },
			{ a:  7.13952353E-15, b: -1.71901842E-11, c:  1.24921284E-08, d: -9.77381812E-07, e: -2.06143267E-03, f: 3.67484906E-01 },
			{ a:  9.42975979E-15, b: -2.34782843E-11, c:  1.85592567E-08, d: -3.24439684E-06, e: -1.90030168E-03, f: 3.84515760E-01 },
			{ a:  1.07866075E-14, b: -2.71407597E-11, c:  2.21257942E-08, d: -4.73730541E-06, e: -1.60164475E-03, f: 3.62182323E-01 },
			{ a:  1.13603209E-14, b: -2.85042950E-11, c:  2.31625870E-08, d: -4.95081514E-06, e: -1.65653806E-03, f: 3.76936891E-01 },
			{ a:  1.05690718E-14, b: -2.64133194E-11, c:  2.12444700E-08, d: -4.28676164E-06, e: -1.68696031E-03, f: 3.67547874E-01 },
			{ a:  1.20885408E-14, b: -3.02972107E-11, c:  2.46553294E-08, d: -5.39925115E-06, e: -1.58784342E-03, f: 3.72282725E-01 },
			{ a:  7.44595465E-15, b: -1.82162641E-11, c:  1.37449118E-08, d: -1.63478994E-06, e: -1.94216411E-03, f: 3.60570641E-01 },
			{ a:  7.64840700E-15, b: -1.86424633E-11, c:  1.39361624E-08, d: -1.50840981E-06, e: -2.06000157E-03, f: 3.76623262E-01 },
			{ a:  1.35254103E-14, b: -3.41731958E-11, c:  2.83974215E-08, d: -6.91473606E-06, e: -1.34702891E-03, f: 3.59702957E-01 },
			{ a:  9.33500120E-15, b: -2.30187737E-11, c:  1.79657281E-08, d: -3.06829212E-06, e: -1.79167685E-03, f: 3.61968879E-01 },
			{ a:  6.04731068E-15, b: -1.42898874E-11, c:  9.76447749E-09, d:  6.88476556E-08, e: -2.21204762E-03, f: 3.72316457E-01 },
		];

		const chenath2022StaticCorrectionFactors = [
			{ a: -9.51090457907993E-15, b:  2.68003937680872E-11, c: -2.85272962015768E-08, d:  1.44965640584421E-05, e: -4.09037901462087E-03, f: 4.10786067381584E-01 },
			{ a: -1.01224366318883E-14, b:  2.87516344786686E-11, c: -3.07936903060156E-08, d:  1.56059182819645E-05, e: -4.35255333773930E-03, f: 4.34675471350574E-01 },
			{ a: -6.93631531510536E-15, b:  1.95414646817332E-11, c: -2.11530467818078E-08, d:  1.13464514336916E-05, e: -3.76139763473045E-03, f: 4.22105974773653E-01 },
			{ a: -1.03965687641384E-14, b:  2.96016845419149E-11, c: -3.24935987361797E-08, d:  1.77692482671488E-05, e: -5.35184091673154E-03, f: 5.59139443022376E-01 },
			{ a: -1.65259595876919E-14, b:  4.67723016645870E-11, c: -4.91623580292198E-08, d:  2.35801824285873E-05, e: -5.62961302577417E-03, f: 4.85946597290915E-01 },
			{ a: -4.17396116098915E-15, b:  1.18048919357644E-11, c: -1.33968663395436E-08, d:  8.15054267331504E-06, e: -3.36851814254741E-03, f: 4.17748598326134E-01 },
			{ a: -1.17216229562395E-14, b:  3.30215025305931E-11, c: -3.50540924831878E-08, d:  1.76678410890769E-05, e: -4.84764513814672E-03, f: 4.76861143865945E-01 },
			{ a:  2.77388278568590E-16, b:  3.29661395375676E-13, c: -3.15451212737186E-09, d:  4.66289715880055E-06, e: -3.02585503602574E-03, f: 4.30353005768065E-01 },
			{ a: -1.87986770313142E-15, b:  6.02820512598020E-12, c: -8.39209272470506E-09, d:  6.50724979766310E-06, e: -3.08478969540540E-03, f: 4.01945999081520E-01 },
			{ a: -1.22768668813425E-14, b:  3.42669924624777E-11, c: -3.61055482849149E-08, d:  1.82416172691079E-05, e: -5.12205701576832E-03, f: 5.14597998154879E-01 },
			{ a:  6.82414055664360E-16, b:  1.00342679444229E-13, c: -4.18705076337182E-09, d:  5.95849523381336E-06, e: -3.36603348100550E-03, f: 4.54546729841025E-01 },
			{ a:  1.19850159999338E-15, b: -1.67797801610393E-12, c: -2.02159826082706E-09, d:  4.90771029124146E-06, e: -3.18944457036042E-03, f: 4.46351829065105E-01 },
			{ a:  3.07006611652762E-15, b: -6.87576938382159E-12, c:  3.32502963398695E-09, d:  2.37079771433205E-06, e: -2.75711846900298E-03, f: 4.28227121659226E-01 },
			{ a:  1.26385812949095E-14, b: -3.19126273059849E-11, c:  2.64209330265717E-08, d: -6.27655181010535E-06, e: -1.45092975594210E-03, f: 3.72458724498558E-01 },
			{ a:  5.47027417369611E-15, b: -1.32037169166290E-11, c:  8.99453842655241E-09, d:  6.62532201065778E-07, e: -2.66058082260822E-03, f: 4.43309751759075E-01 },
			{ a:  6.87956171619268E-15, b: -1.71452548972134E-11, c:  1.31999046288728E-08, d: -1.56253006614987E-06, e: -2.14667345062771E-03, f: 3.97931670962001E-01 },
			{ a:  6.06454779867648E-15, b: -1.48389378852218E-11, c:  1.06511331989118E-08, d: -8.81245456766685E-08, e: -2.43401255295826E-03, f: 4.21422315864956E-01 },
			{ a:  9.01290342028714E-15, b: -2.22589064752432E-11, c:  1.71859036888596E-08, d: -2.48449716735926E-06, e: -2.12468175611904E-03, f: 4.09500925832858E-01 },
			{ a: -4.50210072371081E-15, b:  1.29181597369377E-11, c: -1.47801481761760E-08, d:  8.89977435860015E-06, e: -3.51037344045535E-03, f: 4.27436664225016E-01 },
			{ a:  7.34768682407484E-15, b: -1.81820752182129E-11, c:  1.38744331270275E-08, d: -1.60948778947633E-06, e: -2.20211162697389E-03, f: 4.10594440875507E-01 },
			{ a:  9.73697963581431E-15, b: -2.41850457360618E-11, c:  1.90250772501438E-08, d: -3.23837631051659E-06, e: -2.02363427791803E-03, f: 4.10914332201570E-01 },
			{ a:  1.31300171224856E-14, b: -3.30059230890020E-11, c:  2.71233374301390E-08, d: -6.29872421933024E-06, e: -1.51908017408278E-03, f: 3.81456880651312E-01 },
			{ a:  1.39886382628637E-14, b: -3.51749298648623E-11, c:  2.90378999988138E-08, d: -6.99342857506816E-06, e: -1.45157816673311E-03, f: 3.84991001445635E-01 },
			{ a:  9.70679957607665E-15, b: -2.40936171895364E-11, c:  1.90083452782303E-08, d: -3.38400689993412E-06, e: -1.91937320518570E-03, f: 3.95731143291489E-01 },
			{ a:  8.24558510145404E-15, b: -1.99092101354370E-11, c:  1.47921101897559E-08, d: -1.75371775624472E-06, e: -1.98314835332702E-03, f: 3.73885777793419E-01 },
			{ a:  1.52389012252397E-14, b: -3.83131380762807E-11, c:  3.17442760868969E-08, d: -7.87908297447640E-06, e: -1.31105198635770E-03, f: 3.77140464463518E-01 },
			{ a:  3.27346899581217E-15, b: -7.54384059328913E-12, c:  4.08576769603164E-09, d:  2.05321768950082E-06, e: -2.71555869531689E-03, f: 4.27142346090568E-01 },
			{ a:  4.60827018896534E-15, b: -1.08278621370391E-11, c:  6.72496804633469E-09, d:  1.44697021331936E-06, e: -2.69292392346928E-03, f: 4.30695934720305E-01 },
			{ a: -1.24746565222785E-14, b:  3.45933340753975E-11, c: -3.58419495888938E-08, d:  1.74172358805257E-05, e: -4.54532701952770E-03, f: 4.31291395095753E-01 },
			{ a: -7.50247528835692E-15, b:  2.11976771319314E-11, c: -2.28551584405008E-08, d:  1.20265624683901E-05, e: -3.65800371887552E-03, f: 3.88084900012181E-01 },
			{ a: -2.18456706064028E-14, b:  6.13384253419560E-11, c: -6.35164574999314E-08, d:  2.95710586790819E-05, e: -6.58986040711852E-03, f: 5.35429132621674E-01 },
			{ a: -1.28672322811012E-14, b:  3.61000137761121E-11, c: -3.79352405270182E-08, d:  1.86860289764597E-05, e: -4.93749711257220E-03, f: 4.77089167228805E-01 },
			{ a: -9.71294185379395E-15, b:  2.73840921319189E-11, c: -2.91372494414695E-08, d:  1.47655549135727E-05, e: -4.12935089125940E-03, f: 4.13308959116114E-01 },
			{ a: -7.62763701469676E-15, b:  2.14581484797277E-11, c: -2.30058881837817E-08, d:  1.20174251435717E-05, e: -3.75538374726915E-03, f: 4.06433343125489E-01 },
			{ a: -1.79413624944222E-14, b:  4.98720880208183E-11, c: -5.15136202322785E-08, d:  2.45744696582358E-05, e: -5.97035415225628E-03, f: 5.32778588295594E-01 },
			{ a: -1.39955052116540E-14, b:  3.91905128905964E-11, c: -4.10056013482881E-08, d:  2.00127483827804E-05, e: -5.10719874954934E-03, f: 4.70259548082473E-01 },
			{ a: -1.06978926677849E-14, b:  2.95612556335360E-11, c: -3.06271159526565E-08, d:  1.50376668103815E-05, e: -4.16865829771455E-03, f: 4.13462453681418E-01 },
			{ a: -8.90901643603908E-15, b:  2.46401935794894E-11, c: -2.56968065603450E-08, d:  1.28646190921690E-05, e: -3.82659476313427E-03, f: 4.03336752389026E-01 },
			{ a: -8.60620254164242E-15, b:  2.36824058186854E-11, c: -2.46257574548600E-08, d:  1.24020319756850E-05, e: -3.74902291698205E-03, f: 3.98850676906008E-01 },
			{ a: -7.74740730321712E-15, b:  2.15699204250527E-11, c: -2.29672647611578E-08, d:  1.20860719218149E-05, e: -3.92719070676570E-03, f: 4.38302856363312E-01 },
			{ a: -8.47114230486322E-15, b:  2.36583765108423E-11, c: -2.51569441819932E-08, d:  1.30484073716551E-05, e: -4.19018675903443E-03, f: 4.62091000020507E-01 },
			{ a: -6.43364632770097E-15, b:  1.77760094010728E-11, c: -1.89196759169601E-08, d:  1.01425263196815E-05, e: -3.61951733189926E-03, f: 4.23798251490985E-01 },
			{ a: -4.49344534458029E-15, b:  1.29300704965867E-11, c: -1.48600387571704E-08, d:  8.99587940933633E-06, e: -3.58118129601177E-03, f: 4.37393947578605E-01 },
			{ a:  2.64978825532690E-15, b: -6.42090389406388E-12, c:  3.76159542850301E-09, d:  1.72064780677295E-06, e: -2.57595937846207E-03, f: 4.13583256747607E-01 },
			{ a:  1.78351283676625E-15, b: -3.81272154791421E-12, c:  8.87337630300453E-10, d:  3.08196337088836E-06, e: -2.84636668781980E-03, f: 4.31163238643707E-01 },
			{ a:  2.88837423289397E-16, b:  3.06543782904928E-13, c: -3.15603361711118E-09, d:  4.69202792312137E-06, e: -3.05492795165556E-03, f: 4.32722690516826E-01 },
			{ a:  6.80031754049434E-15, b: -1.67054637540093E-11, c:  1.24289740997938E-08, d: -9.77626716672738E-07, e: -2.24852309925188E-03, f: 4.05405371492177E-01 },
			{ a:  6.93414103440263E-15, b: -1.74978711113831E-11, c:  1.37336148668501E-08, d: -1.78527187933118E-06, e: -2.18432131788744E-03, f: 4.15820867805354E-01 },
			{ a:  1.06502257814988E-14, b: -2.70425590954125E-11, c:  2.23042978542725E-08, d: -4.86478867734877E-06, e: -1.71215993745030E-03, f: 3.90464390683502E-01 },
			{ a:  4.62743050149440E-15, b: -1.07317581795388E-11, c:  6.49446617510705E-09, d:  1.53485507440317E-06, e: -2.76622135664561E-03, f: 4.46457256671645E-01 },
			{ a:  5.70224324785895E-15, b: -1.43197645302677E-11, c:  1.09791502797114E-08, d: -1.00803291117613E-06, e: -2.13340298786809E-03, f: 3.90541994314726E-01 },
			{ a:  9.82005092915033E-16, b: -1.49553076722397E-12, c: -1.69673879626818E-09, d:  4.53006152053126E-06, e: -3.08962440752541E-03, f: 4.39257249601734E-01 },
			{ a:  8.39183373661598E-15, b: -2.12486230756577E-11, c:  1.71566217120304E-08, d: -3.10850447849663E-06, e: -1.92400708269994E-03, f: 3.94330876604553E-01 },
			{ a:  3.10418884993717E-15, b: -6.65398203303509E-12, c:  2.56015197545286E-09, d:  3.18347109374003E-06, e: -2.97994862688755E-03, f: 4.45232593841338E-01 },
			{ a:  1.73853315067403E-14, b: -4.42508996403663E-11, c:  3.75493750021851E-08, d: -1.01075813520508E-05, e: -1.05880079650452E-03, f: 3.80469104542204E-01 },
			{ a:  7.64686640935750E-15, b: -1.90034990522470E-11, c:  1.44505021982958E-08, d: -1.38496349961461E-06, e: -2.32201601108114E-03, f: 4.30860933880441E-01 },
			{ a:  1.33194574125952E-14, b: -3.41018718982507E-11, c:  2.88485782971132E-08, d: -7.23658854809782E-06, e: -1.37675871622888E-03, f: 3.80573935098638E-01 },
			{ a:  2.01312004178601E-14, b: -5.13247535154894E-11, c:  4.38281673236378E-08, d: -1.21880449712581E-05, e: -8.63722895888381E-04, f: 3.82934455024952E-01 },
			{ a:  1.12904420358335E-14, b: -2.79641205465775E-11, c:  2.22448236904161E-08, d: -4.44038552561819E-06, e: -1.74184605872440E-03, f: 3.85611364166380E-01 },
			{ a:  1.19472161014126E-14, b: -2.99449960714824E-11, c:  2.43166672476579E-08, d: -5.22216633423813E-06, e: -1.74022006497837E-03, f: 4.01058708137408E-01 },
			{ a:  1.49413474245523E-14, b: -3.75976084869004E-11, c:  3.11446625775022E-08, d: -7.66008729114957E-06, e: -1.37701715410568E-03, f: 3.84421862570799E-01 },
			{ a:  1.28520368079144E-14, b: -3.21765690686215E-11, c:  2.61304378117853E-08, d: -5.67985662770812E-06, e: -1.77333500469417E-03, f: 4.12764403140874E-01 },
			{ a:  1.53922313514166E-14, b: -3.89969378748295E-11, c:  3.27440294919499E-08, d: -8.45906074428089E-06, e: -1.21758814382216E-03, f: 3.72770852292113E-01 },
			{ a:  1.72267620128181E-14, b: -4.35142024081954E-11, c:  3.65122623193037E-08, d: -9.61012605762238E-06, e: -1.09869483029531E-03, f: 3.74597183524776E-01 },
			{ a:  9.12168487734128E-15, b: -2.22506674207282E-11, c:  1.68918956383900E-08, d: -2.34410564788588E-06, e: -2.05481048802969E-03, f: 3.95758127301106E-01 },
			{ a:  1.06262670258619E-14, b: -2.62340381363947E-11, c:  2.06287963271144E-08, d: -3.78530571059076E-06, e: -1.87807527538332E-03, f: 3.94610192747716E-01 },
			{ a:  1.67187231415464E-14, b: -4.22174827734959E-11, c:  3.54252585853020E-08, d: -9.35086821206340E-06, e: -1.04458084634849E-03, f: 3.60584732497080E-01 },
			{ a:  1.28616771314514E-14, b: -3.20869876853616E-11, c:  2.60393825053635E-08, d: -5.82393210350043E-06, e: -1.55283894337695E-03, f: 3.79418533983423E-01 },
			{ a:  8.55248040205823E-15, b: -2.07617782559128E-11, c:  1.56450774779785E-08, d: -2.11256438826626E-06, e: -1.92613813402146E-03, f: 3.71192873712770E-01 }
		];

		// Refer to THR-155 & THR-204
		function computeChenathAreaCorrectionFactor(building, nathersClimateZone) {

			const aTotalOuterFabricM2 = aTotalOuterFabric(building);
			const aTotalSharedFabricM2 = aTotalSharedFabric(building);

			// Apply final calculation.
			const calc = calculateChenath2019CorrectionFactor(
				building.conditionedFloorArea, nathersClimateZone, aTotalOuterFabricM2, aTotalSharedFabricM2);

			const calc2022 = calculateChenath2022CorrectionFactor(
				building.conditionedFloorArea, nathersClimateZone, aTotalOuterFabricM2, aTotalSharedFabricM2);

			building.ncc2019AreaCorrectionFactor = calc;
			building.ncc2022AreaCorrectionFactor = calc2022;
		}

		function calculateChenath2019CorrectionFactor(
			conditionedFloorArea, nathersClimateZone, aTotalOuterFabricM2, aTotalSharedFabricM2) {

			// Clamp our area to not be below 50 or above 1000
			const clampedArea = conditionedFloorArea < 50
				? 50
				: conditionedFloorArea > 1000
					? 1000
					: conditionedFloorArea;

			// Determine our 'factor e' based on the other factors plus our area.
			const factorRow = chenath2019StaticCorrectionFactors[nathersClimateZone - 1];
			
			if(factorRow == null)
				return null;
			
			const factorE =
				(factorRow.f + factorRow.e * clampedArea) +
				(factorRow.d * (clampedArea ** 2)) +
				(factorRow.c * (clampedArea ** 3)) +
				(factorRow.b * (clampedArea ** 4)) +
				(factorRow.a * (clampedArea ** 5));

			return 1 - (factorE * ((aTotalOuterFabricM2 - aTotalSharedFabricM2) / aTotalOuterFabricM2));
		}

		function calculateChenath2022CorrectionFactor(
			conditionedFloorArea, nathersClimateZone, aTotalOuterFabricM2, aTotalSharedFabricM2) {

			// Clamp our area to not be below 50 or above 1000
			const clampedArea = conditionedFloorArea < 50
				? 50
				: conditionedFloorArea > 1000
					? 1000
					: conditionedFloorArea;

			// Determine our 'factor e' based on the other factors plus our area.
			const factorRow = chenath2022StaticCorrectionFactors[nathersClimateZone - 1];

			if(factorRow == null)
				return null;
			
			const factorE =
				(factorRow.f + factorRow.e * clampedArea) +
				(factorRow.d * (clampedArea ** 2)) +
				(factorRow.c * (clampedArea ** 3)) +
				(factorRow.b * (clampedArea ** 4)) +
				(factorRow.a * (clampedArea ** 5));

			return 1 - (factorE * ((aTotalOuterFabricM2 - aTotalSharedFabricM2) / aTotalOuterFabricM2));
		}

		function aTotalOuterFabric(building) {

			// ATotalOuterFabric (m2) = SUM areas of ALL constructions in the scratch file with CHENATH Code Ranges 
			// A, B, C, D or E AND ALSO J, K, L 
			// 1-20 Exterior Glazing (A)
			// 21-40 Skylights/Roof Windows (B)
			// 41-90  Exterior Walls/Exterior Doors (C)
			// 91-190 Roofs (D)
			// 191-240 Floors to outdoors (E)
			
			const interiorZones = zoneservice.interiorZones(building.zones);
			
			const matchingOpenings = building.openings
				?.filter(s => interiorZones.find(x => x.linkId === s.parentZoneId) != null)
				?.filter(s => {
					const code = s.category.constructionCategoryCode;
					const has = code === "ExteriorGlazing";                    // A
					return has;
				});

			const matchingSurfaces = building.surfaces
				?.filter(s => interiorZones.find(x => x.linkId === s.parentZoneId) != null)
				?.filter(s => {
					const code = s.category.constructionCategoryCode;
					const has =
						
						code === "RoofWindow" || code === "Skylight" ||         // B
						code === "ExteriorWall" || code === "ExteriorDoor" ||   // C
						code === "SubfloorWall" ||
						code === "Roof" ||                                      // D
						code === "ExteriorFloor" ||   // E
						code === "ExteriorFloorElevated" ||
						code === "IntermediateFloorNeighbourBelow" ||           // J
						code === "CeilingNeighbourAbove" ||                     // K
						code === "InteriorWallAdjacentToNeighbour"              // L
					return has;
				});

			const calcA = matchingOpenings
				.map(x => x.netArea != null && x.netArea !== 0 ? x.netArea : x.grossArea) // ?
				.reduce((a, b) => a + b, 0);
			
			const calcB = matchingSurfaces
				.map(x => x.netArea != null && x.netArea !== 0 ? x.netArea : x.grossArea) // ?
				.reduce((a, b) => a + b, 0);
			
			return calcA + calcB;
		}

		function aTotalSharedFabric(building) {

			// ATotalSharedFabric (m2) = SUM areas of ALL constructions in the scratch file with CHENATH Code Ranges 
			// J, K or L
			// 521-550 Floors to neighbour (J)
			// 551-580 Ceilings ro neighbour (K)
			// 581-610 Walls to neighbour (L)

			const interiorZones = zoneservice.interiorZones(building.zones);

			const matchingSurfaces = building.surfaces
				?.filter(s => interiorZones.find(x => x.linkId === s.parentZoneId) != null)
				?.filter(s => {
					const code = s.category.constructionCategoryCode;
					const has =
						code === "IntermediateFloorNeighbourBelow" ||           // J
						code === "CeilingNeighbourAbove" ||                     // K
						code === "InteriorWallAdjacentToNeighbour"              // L
					return has;
				});

			const calc = matchingSurfaces
				.map(x => x.netArea != null && x.netArea !== 0 ? x.netArea : x.grossArea) // ?
				.reduce((a, b) => a + b, 0);

			return calc;


		}

		/**
		 * Keep the Azimuth between 0 and 359 (inclusive).
 		 */
		function clampAzimuths(building) {

			// For exceeding the max value: 0 is a lot cleaner than 359.99 and basically means the same thing.

			building?.surfaces.forEach(parentSurface => {

				parentSurface.elements.forEach(childSurface => {
					if (childSurface.azimuth < 0 || childSurface.azimuth > 359)
						childSurface.azimuth = 0;
				});

			});

			building?.openings.forEach(parentOpening => {

				parentOpening.elements.forEach(childOpening => {
					if (childOpening.azimuth < 0 || childOpening.azimuth > 359)
						childOpening.azimuth = 0;
				});

			});
		}

		function computeDynamicSectors(building, sectorDetermination) {

			if (!sectorDetermination)
				return;

			// Set so other function can have access (hack for now).
			// serviceSectorDeterminationVal = sectorDetermination;
			
			building?.surfaces.forEach(parentSurface => {
				parentSurface.elements.forEach(childSurface => {
					childSurface.sector = common.determineSectorType(childSurface.azimuth, sectorDetermination.sectors);
				});
			});

			building?.openings.forEach(parentOpening => {
				parentOpening.elements.forEach(childOpening => {
					childOpening.sector = common.determineSectorType(childOpening.azimuth, sectorDetermination.sectors);
				});
			});

			building?.services.forEach(parentService => {
				parentService.elements.forEach(childOpening => {
					childOpening.sector = common.determineSectorType(childOpening.azimuth, sectorDetermination.sectors);
				});
			});

		}

		


		/** Assessment **/

		/**
		 * Computes the Rural Lot field for the Site -> Lot tab, based on the Parcel Area field
 		 */
		 function computeRuralLot(assessment) {

			// Was not specified that user input should have priority over this override.

			if (assessment.assessmentProjectDetail != null) {
				
				const parcelArea = assessment.assessmentProjectDetail.parcelArea;

				if (!parcelArea || parcelArea < 2000 )
					assessment.assessmentProjectDetail.ruralLot = false;
				else if (2000 <= parcelArea)
					assessment.assessmentProjectDetail.ruralLot = true;
				else
					throw "Invalid Parcel Area supplied!";
			}
			
		}

		/**
		 * Computes the Narrow Lot field for the Site -> Lot tab, based on the lotWidth field
 		 */
		function computeNarrowLot(assessment) {

			// User Input has priority over this override.

			if (assessment.assessmentProjectDetail != null) {

				// User override will have priority over recalculated value from lotWidth field.
				if (assessment.assessmentProjectDetail.narrowLotIsManuallyOverridden === true)
					return;

				const lotWidth = assessment.assessmentProjectDetail.lotWidth;

				if (!lotWidth)
					assessment.assessmentProjectDetail.narrowLot = null;
				else if (lotWidth <= 10 )
					assessment.assessmentProjectDetail.narrowLot = true;
				else if (10 < lotWidth)
					assessment.assessmentProjectDetail.narrowLot = false;
			}
			
		}
		
		return service;
	}
})();