(function () {
    'use strict';
    var controllerId = 'ShadingInfoModalCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'constructionservice', 'common', shadingInfoControl]);
    function shadingInfoControl($rootScope, $scope, $mdDialog, constructionservice, common) {

        var vm = this;
        vm.isBusy = false;
        vm.previousRoute = $rootScope.previousState;

        /* Input variables */
        // The clicked object
        vm.item = $scope.item;

        // The property name we are displaying/updating
        vm.propertyName = $scope.property;

		vm.disabled = $scope.disabled;

        /* Local variables */
		vm.title = '';

        /* Functions */
		/* Horizontal Shading */
		vm.calculateAllEaveFields = function() {
			vm.calculateEaveFields(vm.item.horizontalProjection1?.eave, vm.item.height);
			vm.calculateEaveFields(vm.item.horizontalProjection2?.eave, vm.item.height);

			vm.item.horizontalProjection1.shading = constructionservice.greaterShading(
				vm.item.horizontalProjection1.eave.shading,
				vm.item.horizontalProjection1.pergola.shading);

			vm.item.horizontalProjection2.shading = constructionservice.greaterShading(
				vm.item.horizontalProjection2.eave.shading,
				vm.item.horizontalProjection2.pergola.shading);
		}

		vm.calculateAllPergolaFields = function() {
			vm.calculatePergolaFields(vm.item.horizontalProjection1?.pergola, vm.item.height);
			vm.calculatePergolaFields(vm.item.horizontalProjection2?.pergola, vm.item.height);

			vm.item.horizontalProjection1.shading = constructionservice.greaterShading(
				vm.item.horizontalProjection1.eave.shading,
				vm.item.horizontalProjection1.pergola.shading);

			vm.item.horizontalProjection2.shading = constructionservice.greaterShading(
				vm.item.horizontalProjection2.eave.shading,
				vm.item.horizontalProjection2.pergola.shading);
		}

		vm.calculateEaveFields = function(eave, height) {

			if(eave == null)
				return;

			if((eave.projection == null || eave.projection === 0) &&
				(eave.verticalOffset == null || eave.verticalOffset === 0) &&
				(eave.rhOffset == null || eave.rhOffset === 0) &&
				(eave.length == null || eave.length === 0)) {
				eave.shadeAngle = null;
				eave.shading = "";
				return;
			}

			let portion = (height + eave.verticalOffset) || 0;
			eave.shadeAngle = portion === 0
				? 0
				: radiansToDegrees(Math.atan((eave.projection || 0) / portion));
			eave.shading = calcEaveHorizontalShading(eave);

		}

		vm.calculatePergolaFields = function(pergola, height) {

			if(pergola == null)
				return;

			if((pergola.projection == null || pergola.projection === 0) &&
				(pergola.verticalOffset == null || pergola.verticalOffset === 0) &&
				(pergola.rhOffset == null || pergola.rOffset === 0) &&
				(pergola.length == null || pergola.length === 0)) {
				pergola.shadeAngle = null;
				pergola.shading = "";
				return;
			}

			let portion = (height + pergola.verticalOffset) || 0;
			pergola.shadeAngle = portion === 0
				? 0
				: radiansToDegrees(Math.atan((pergola.projection || 0) / portion));

			pergola.shading = calcPergolaHorizontalShading(pergola);

		}

		function calcEaveHorizontalShading(eave) {
			// Default
			let eaveHorizontalShading = "";

			// Minimal
			if (eave.projection >= 0 && eave.verticalOffset != null &&
				eave.rhOffset >= -0.35 && eave.lhOffset >= -0.35 &&
				eave.length >= 0 && eave.shadeAngle < 15)
			{
				eaveHorizontalShading = "Minimal";
			}

			// Moderate
			else if (eave.projection >= 0 && eave.verticalOffset != null &&
				eave.rhOffset >= -0.35 && eave.lhOffset >= -0.35 &&
				eave.length >= 0 && 15 <= eave.shadeAngle && eave.shadeAngle < 30)
			{
				eaveHorizontalShading = "Moderate";
			}

			// High
			else if (eave.projection >= 0 && eave.verticalOffset != null &&
				eave.rhOffset >= -0.35 && eave.lhOffset >= -0.35 &&
				eave.length >= 0 && 30 <= eave.shadeAngle && eave.shadeAngle < 45)
			{
				eaveHorizontalShading = "High";
			}

			// Obstructive
			else if (eave.projection >= 0 && eave.verticalOffset != null &&
				eave.rhOffset >= -0.35 && eave.lhOffset >= -0.35 &&
				eave.length >= 0 && 45 <= eave.shadeAngle)
			{
				eaveHorizontalShading = "Obstructive";
			}

			return eaveHorizontalShading;
		}

		function calcPergolaHorizontalShading(pergola) {
			// Default
			let pergolaHorizontalShading = "";

			// NULL
			if (pergola.projection === 0 && pergola.verticalOffset === 0 &&
				pergola.rhOffset === 0 && pergola.lhOffset === 0 &&
				pergola.length === 0)
			{
				pergolaHorizontalShading =  "";
			}

			// Minimal
			else if (pergola.projection >= 0 && pergola.verticalOffset != null &&
				pergola.rhOffset >= -0.35 && pergola.lhOffset >= -0.35 &&
				pergola.length >= 0 && pergola.shadeAngle < 15)
			{
				pergolaHorizontalShading = "Minimal";
			}

			// Moderate
			else if (pergola.projection >= 0 && pergola.verticalOffset != null &&
				pergola.rhOffset >= -0.35 && pergola.lhOffset >= -0.35 &&
				pergola.length >= 0 && 15 <= pergola.shadeAngle && pergola.shadeAngle < 30)
			{
				pergolaHorizontalShading = "Moderate";
			}

			// High
			else if (pergola.projection >= 0 && pergola.verticalOffset != null &&
				pergola.rhOffset >= -0.35 && pergola.lhOffset >= -0.35 &&
				pergola.length >= 0 && 30 <= pergola.shadeAngle && pergola.shadeAngle < 45)
			{
				pergolaHorizontalShading = "High";
			}

			// Obstructive
			else if (pergola.projection >= 0 && pergola.verticalOffset != null &&
				pergola.rhOffset >= -0.35 && pergola.lhOffset >= -0.35 &&
				pergola.length >= 0 && 45 <= pergola.shadeAngle)
			{
				pergolaHorizontalShading = "Obstructive";
			}

			return pergolaHorizontalShading;
		}

		/* Vertical Shading */
		vm.calculateAllVerticalShading = function() {

			vm.calculateVerticalShadingFields(vm.item.verticalScreen1, vm.item.height);
			vm.calculateVerticalShadingFields(vm.item.verticalScreen2, vm.item.height);
			vm.calculateVerticalShadingFields(vm.item.verticalScreen3, vm.item.height);

		}

		vm.calculateVerticalShadingFields = function(screen, height) {

			if(screen == null)
				return;

			if(screen.perpendicularDistance != null) {
				screen.verticalShadeAngle = screen.perpendicularDistance === 0
					? 0
					: radiansToDegrees(Math.atan((screen.verticalOffset || 0) / (screen.perpendicularDistance || 1)));

				screen.shading = calcVerticalShading(screen, height);
			}
		}

		// Both sets of conditions are in spreadsheet, kept the same logic layout here to easily compare between the two
		function calcVerticalShading(screen, height) {

			if(screen == null)
				return "";

			if (screen.screenHeight >= 0 && screen.screenWidth >= 0 &&
				screen.rhOffset >= -0.35 && screen.lhOffset >= -0.35)
			{
				if (screen.perpendicularDistance <= 0.35)
				{
					if (screen.verticalOffset < -((2 / 3) * height))
						return "Minimal";
					if ( -(2 / 3) <= screen.verticalOffset && screen.verticalOffset < -((1 / 3) * height))
						return "Moderate";
					if ( -((1 / 3) * height) <= screen.verticalOffset && screen.verticalOffset < 0)
						return "High";
					if (screen.verticalOffset >= 0)
						return "Obstructive";
				}
				else
				{
					if (screen.verticalShadeAngle < -15)
						return "Minimal";
					if (-15 <= screen.verticalShadeAngle && screen.verticalShadeAngle < 0)
						return "Moderate";
					if (0 <= screen.verticalShadeAngle && screen.verticalShadeAngle < 15)
						return "High";
					if (screen.verticalShadeAngle >= 15)
						return "Obstructive";
				}

				return "Minimal";
			}

			return "Minimal";
		}

		vm.calculateWingWallShadingFields = function(wall, width) {

			if(wall == null)
				return;

			if(wall.projection == null || wall.horizontalOffset == null) {
				wall.shadeAngle = null;
				wall.shading = "";
				return;
			}

			// Shade Angle | ATAN (Projection / [ WindowORWallORDoor Width + Horizontal Offset] )
			// If any 0 -> 0

			let portion = (width + wall.horizontalOffset) || 0;
			wall.shadeAngle = portion === 0
				? 0
				: radiansToDegrees(Math.atan((wall.projection || 0) / portion));

			// Left Wing Shading
			wall.shading = calculateWingWallShading(wall, vm.item.height);
		}

		function calculateWingWallShading(shading, height) {

			if(shading == null || shading.projection == null)
				return "";

			// Default
			let wingWallShading = 'Minimal';

			// Minimal
			if (shading.projection >= 0 && shading.horizontalOffset >= 0 &&
				shading.verticalOffset >= (-0.2 * height) && shading.shadeAngle < 15)
			{
				wingWallShading = 'Minimal';
			}
			// Moderate
			else if (shading.projection >= 0 && shading.horizontalOffset >= 0 &&
				shading.verticalOffset >= (-0.2 * height) && 15 <= shading.shadeAngle && shading.shadeAngle < 30)
			{
				wingWallShading = 'Moderate';
			}
			// High
			else if (shading.projection >= 0 && shading.horizontalOffset >= 0 &&
				shading.verticalOffset >= (-0.2 * height) && 30 <= shading.shadeAngle && shading.shadeAngle < 45)
			{
				wingWallShading = 'High';
			}
			// Obstructive
			else if (shading.projection >= 0 && shading.horizontalOffset >= 0 &&
				shading.verticalOffset >= (-0.2 * height) && 30 <= shading.shadeAngle && shading.shadeAngle >= 45)
			{
				wingWallShading = 'Obstructive';
			}

			return wingWallShading;
		}

		function radiansToDegrees(radians) {
			return radians * (180 / Math.PI);
		}

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        vm.save = function() {
            $mdDialog.hide(vm.item);
        }

        vm.symbol = function(symbolName) {
            return common.symbol(symbolName);
        }

		function init() {

			switch ($scope.property) {
				case 'horizontalShading':
					vm.title = 'Horizontal Shading';

					if(vm.item.horizontalProjection1 == null)
						vm.item.horizontalProjection1 = { eave: {}, pergola: {} };

					if(vm.item.horizontalProjection2 == null)
						vm.item.horizontalProjection2 = { eave: {}, pergola: {} };

					vm.calculateAllEaveFields();
					vm.calculateAllPergolaFields();
					vm.horizontalShading = [vm.item.horizontalProjection1, vm.item.horizontalProjection2];

					break;
				case 'verticalShading':
					vm.title = 'Vertical Shading';

					if(vm.item.verticalScreen1 == null)
						vm.item.verticalScreen1 = {}

					if(vm.item.verticalScreen2 == null)
						vm.item.verticalScreen2 = {}

					if(vm.item.verticalScreen3 == null)
						vm.item.verticalScreen3 = {}

					vm.calculateAllVerticalShading();
					vm.verticalShading = [vm.item.verticalScreen1, vm.item.verticalScreen2, vm.item.verticalScreen3];

					break;
				case 'leftWingWall':
					vm.title = 'Left Wing Wall';

					if(vm.item.leftWingWall == null)
						vm.item.leftWingWall = {};

					vm.calculateWingWallShadingFields(vm.item.leftWingWall, vm.item.width);
					break;
				case 'rightWingWall':
					vm.title = 'Right Wing Wall';

					if(vm.item.rightWingWall == null)
						vm.item.rightWingWall = {};

					vm.calculateWingWallShadingFields(vm.item.rightWingWall, vm.item.width);
					break;
				default:
					vm.title = 'Shading';
					break;
			}
		}

		// Run on startup
		init();
	}
})();