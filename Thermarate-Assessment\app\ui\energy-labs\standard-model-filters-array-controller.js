(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelFiltersArray', {
      bindings: {
        currentList: '<',
        appliedFilters: '<',
        settings: '<',
        filterCountData: '<',
        onFilterChanged: '<',
        onSortChanged: '<'
      },
      templateUrl: 'app/ui/energy-labs/standard-model-filters-array.html',
      controller: StandardModelFiltersController,
      controllerAs: 'vm'
    });

  StandardModelFiltersController.$inject = ['common', 'standardmodelservice'];

  function StandardModelFiltersController(common, standardmodelservice) {

    const vm = this;

    // Design variables we wish to filter on. Note these need to be the same as
    // the underlying .net (and thus SQL) name, NOT what is displayed in the UI
    // (refer to keyToName for that....);
    vm.filters = standardmodelservice.multiFiltersFields;

    vm.featuresSections = standardmodelservice.buildFeaturesDropdownSections();
    vm.featureSectionHasItems = function (section) {
        return vm.filterCountData != null && section.features.some(feature => vm.filterCountData['features'][feature] > 0);
    }
    vm.categoriesHasItems = function () {
        return vm.filterCountData != null && vm.categories.some(category => vm.filterCountData['categories'][category] > 0);
    }
    vm.categories = standardmodelservice.categories;
    vm.featureName = standardmodelservice.featureName;
    vm.keyToName = standardmodelservice.keyToName;
    vm.toSplitTitleCase = common.toSplitTitleCase;

    vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
    vm.anyFiltersApplied = common.anyFiltersApplied;

    vm.initialize = function () {
        // Initially set all dropdowns to 'Any'
        vm.filters.forEach(f => vm.appliedFilters[f.field] = ['Any']);
        vm.appliedFilters.features = ['Any'];
        vm.appliedFilters.categories = ['Any'];
        setTimeout(() => vm.initialised = true, 100);
    }

    vm.selectAllFeatures = function() {
      vm.appliedFilters.features = null; // Same as all being selected.
      vm.filterChanged();
    }

    /**
     * Determines what the md-select should say when in the 'closed' position,
     * based on how many values are selected etc
     */
    vm.filterText = function(field) {
        const selectedCount = vm.appliedFilters[field]?.length;

        if (selectedCount === 1) {
            if (vm.appliedFilters[field][0] == 'Any') {
                return 'Any';
            }
            else if (field == "features" || field == "categories") {
                return vm.featureName(vm.appliedFilters[field][0]);
            } else {
                return vm.appliedFilters[field][0];
            }
        }
        else {
            return `${selectedCount} Selected`;
        }
    }

    var filtersToApply = {}; // To pass to service to actually filter and keep track of what was selected before
    vm.filterChanged = function() {
        if (vm.initialised) {
            filtersToApply = getFilterDataForService(vm.appliedFilters, filtersToApply);
            if (vm.onFilterChanged) {
                vm.onFilterChanged({ filterData: filtersToApply });
            }
            vm.filtersApplied = vm.anyFiltersApplied(null, vm.filters, vm.appliedFilters);
        }
    }

    // Set selections based on 'Any' option logic
    function getFilterDataForService(uiData, oldServiceData) {
        return common.applyMultiSelectChangedLogic(uiData, oldServiceData);
    }

    vm.clearFilter = function (filter) {
        vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
        vm.filterChanged();
    }

    vm.initialize();

  }

})();