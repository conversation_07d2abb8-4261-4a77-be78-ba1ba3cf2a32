-- First, update Asp<PERSON>Users to set UserName to '[DELETED]'
UPDATE [dbo].[AspNetUsers]
SET [UserName] = '[DELETED]'
WHERE [Id] NOT IN (
    '6BD08875-CA99-4952-9FA3-6B47F9D530BE', -- admin
    'A368DD06-FFB9-4986-AE21-479904571E4E', -- <EMAIL>
    '49c2546d-6bdb-401d-978b-d073febd0faf', -- <EMAIL>
    'D8A5ADD6-A1F3-47E2-AF78-76868CF4C18B'  -- systemRediBGUser
)
AND [UserName] != '[DELETED]';

-- Print the number of AspNetUsers updated
PRINT 'Updated ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' users in AspNetUsers';

-- Next, update the related RSS_User records to set Deleted = 1
UPDATE [RSS_User]
SET [EmailAddress] = '[DELETED]',
    [Deleted] = 1
WHERE [AspNetUserId] IS NULL OR [AspNetUserId] NOT IN (
    '6BD08875-CA99-4952-9FA3-6B47F9D530BE', -- admin
    'A368DD06-FFB9-4986-AE21-479904571E4E', -- <EMAIL>
    '49c2546d-6bdb-401d-978b-d073febd0faf', -- <EMAIL>
    'D8A5ADD6-A1F3-47E2-AF78-76868CF4C18B'  -- systemRediBGUser
)

-- Print the number of RSS_User records updated
PRINT 'Updated ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' records in RSS_User';