-- USE [thermarate];

SELECT [AssessmentDrawingId]
      ,[AssessmentId]
      ,[DrawingNumber]
      ,[DrawingDescription]
      ,[Attachment]
    --   ,[SheetNum<PERSON>]
    --   ,[Revision]
    --   ,[RevisionDate]
    --   ,[IsIncludedInReport]
    --   ,[CreatedOn]
    --   ,[CreatedByName]
    --   ,[ModifiedOn]
    --   ,[ModifiedByName]
    --   ,[Deleted]
    --   ,[StampX]
    --   ,[StampY]
    --   ,[StampWidth]
    --   ,[DocumentId]
    --   ,[PageNumber]
    --   ,[Archived]
    --   ,[Superseded]
    --   ,[Rotation]
    --   ,[UseDefaultStampPosition]
    --   ,[ComplianceOptionId]
    --   ,[IsStamped]
    --   ,[StampSizePerc]
    --   ,[OriginalPageSize]
    --   ,[PageSize]
      ,[ProcessingProgress]
      ,[ProcessingStatus]
      ,[NotifiedClientOfProcessingCompletion]
      ,[OriginalWidth]
      ,[OriginalHeight]
      ,[IsShownToClient]
      ,[ToStamp]
  FROM [dbo].[RSS_AssessmentDrawing]
  WHERE 1=1
    --AND [DrawingDescription] = '17027H_pg2'
  ORDER BY [CreatedOn] DESC