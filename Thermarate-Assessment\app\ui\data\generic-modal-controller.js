
(function () {

    'use strict';

    angular.module('app') .controller("GenericModalCtrl", ['$scope', '$mdDialog', genericModalController]);

    function genericModalController($scope, $mdDialog) {

        // - VARIABLES - //

        var vm = this;
        vm.title = $scope.title;
        vm.bodyText = $scope.bodyText;
        vm.buttons = $scope.buttons;

        // - HANDLES - //

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.buttonClick = function (button) {
            if (button.isCancel) {
                vm.cancel();
            } else {
                $mdDialog.hide();
            }
            if (button.onClick != null) {
                button.onClick();
            }
        }

    }

})();