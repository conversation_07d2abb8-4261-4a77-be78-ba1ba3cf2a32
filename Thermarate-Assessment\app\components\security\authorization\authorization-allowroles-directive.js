/*
* Security Authorisation Module
* -----------------------------
* Ensures the user is authorised for the requested route.
*
*/

var mod = angular.module('security.authorization');
mod.directive('rediAllowRoles', ['security', function (Auth) {
    // Description:
    //      Checks if a user has access to the named role.  If no access then element is hidden.
    //      Provide an ARRAY OF STRINGS.
    // Usage:
    //      <div redi-allow-roles="['administrator', 'user-edit']"></div>
    return {
        restrict: 'A',
        link: function ($scope, element, attrs) {

            
            
            let prevDisp = element.css('display');
            let userRoles = Auth.currentUser.userRoles;
            
            // Convert from array string into actual array.
            let allowRoles = $scope.$eval(attrs.rediAllowRoles);

            // Watch for changes to the users roles.
            $scope.currentUser = Auth.currentUser;
            $scope.$watch('currentUser', function (user) {
                
                if (user != undefined && user.userRoles)
                    userRoles = user.userRoles;
                
                updateCSS();
                
            }, true);

            // update the CSS on the element (display or hide)
            function updateCSS() {
                
                if (allowRoles == null || allowRoles == "" || allowRoles == "[]" || allowRoles.length == 0) {
                    element.css('display', prevDisp);
                    return;
                }
                
                if (!userRoles && !allowRoles)
                    return;
                
                for (var i = 0; i < userRoles.length; i++) {
                    if(allowRoles.some(x => x === userRoles[i].toLowerCase())) {
                        element.css('display', prevDisp);
                        return;
                    }
                }

                element.css('display', 'none');

                // Attempted to actually hide the element (similar to ng-if, instead of ng-show)
                // but unfortunately this caused a lot of problems (e.g. tabs started appearing out of
                // order and other whacky stuff). Best to just leave this as-is...
                // angular.element(element).remove();
                
            }
        }
    };
}]);