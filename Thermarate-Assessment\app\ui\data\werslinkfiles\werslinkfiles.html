<form name="WersLinkFilesForm"
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='WersLinkFilesCtrl as vm'>

    <div class="widget" ng-cloak>

        <div data-cc-widget-header data-title="{{vm.title}}" />

        <md-card>

            <md-card-content>

                <div>
                    <label style="font-size: 9px; color: gray;">Residential File</label>
                    <generic-file-upload class="vertically-condensed wers-link-file-upload"
                                         label="Residential File"
                                         on-change="vm.residentialOnChange(data);"
                                         accept-array="false"
                                         category="'Wers Link Residential'"
                                         classification="vm.residentialFile.category"
                                         file-object="vm.residentialFile"
                                         prop-name="file"
                                         accept="'.xlsx'">
                    </generic-file-upload>
                </div>
                <div>
                    <label style="font-size: 9px; color: gray;">Commercial File</label>
                    <generic-file-upload class="vertically-condensed"
                                         label="Commercial File"
                                         on-change="vm.commercialOnChange(data);"
                                         accept-array="false"
                                         category="'Wers Link Commercial'"
                                         classification="vm.commercialFile.category"
                                         file-object="vm.commercialFile"
                                         prop-name="file"
                                         accept="'.xlsx'">
                    </generic-file-upload>
                </div>

            </md-card-content>

        </md-card>

    </div>

</form>
<style>
    .wers-link-file-upload button {
        height: 50px;
    }
</style>