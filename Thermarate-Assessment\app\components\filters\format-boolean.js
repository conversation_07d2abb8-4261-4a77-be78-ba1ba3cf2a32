﻿var app = angular.module('app');
app.filter('yesNo', function () {
    return function (input) {
        return input ? 'yes' : 'no';
    }
});
app.filter('yesBlank', function () {
    return function (input) {
        return input ? 'yes' : '';
    }
});
app.filter('noBlank', function () {
    return function (input) {
        return input ? '' : 'no';
    }
});
app.filter('trueFalse', function () {
    return function (input) {
        return input ? 'true' : 'false';
    }
});
app.filter('trueBlank', function () {
    return function (input) {
        return input ? 'true' : '';
    }
});
app.filter('falseBlank', function () {
    return function (input) {
        return input ? '' : 'false';
    }
});
app.filter('tickCross', function () {
    return function (input) {
        return input ? "<i class='fa fa-check'></i>" : "<i class='fa fa-times'></i>";
    }
});
app.filter('tickBlank', function () {
    return function (input) {
        return input ? "<i class='fa fa-check'></i>" : "";
    }
});
app.filter('crossBlank', function () {
    return function (input) {
        return input ? "" : "<i class='fa fa-times'></i>";
    }
});