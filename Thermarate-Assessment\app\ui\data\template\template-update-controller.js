(function () {
    // The TemplateUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'TemplateUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'templatecategoryservice', 'templateservice', templateUpdateController]);
function templateUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  templatecategoryservice, templateservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Template';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.templateId = null;
        vm.template = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Template";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.templateId = $scope.templateId;
            }
            vm.hideActionBar = true;
        } else {
            vm.templateId = $stateParams.templateId;
        }

        // Get data for object to display on page
        var templateIdPromise = null;
        if (vm.templateId != null) {
            templateIdPromise = templateservice.getTemplate(vm.templateId)
            .then(function (data) {
                if (data != null) {
                    vm.template = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.templateCategoryList = [];
        var templateCategoryPromise = templatecategoryservice.getList()
            .then(function(data){
                vm.templateCategoryList = data.data;
            });

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("template-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                templateservice.createTemplate(vm.template).then(function(data){
                    vm.template = data;
                    vm.templateId = vm.template.templateId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                templateservice.updateTemplate(vm.template).then(function(data){
                    if (data != null) {
                        vm.template = data;
                        vm.templateId = vm.template.templateId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            templateservice.deleteTemplate(vm.templateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            templateservice.undoDeleteTemplate(vm.templateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();