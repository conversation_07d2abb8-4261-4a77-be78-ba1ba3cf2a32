// Name: usersettingservice
// Type: Angular Service
// Purpose: To manage user settings data.  User settings can be read from this service or can be updated.
// Design: On initialisation this service loads the user settings from local storage.
(function () {
    'use strict';
    var serviceId = 'usersettingservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', '$rootScope', usersettingservice]);

    function usersettingservice(common, config, $http, $rootScope) {
        var $q = common.$q;
        var log = common.logger;
        var initPromise,
            initFailed;
        var initialised = false;
        var usersettingData = {};
        usersettingData.userType = [];
        usersettingData.salesDetail = [];
        usersettingData.helpBar = "";
        var service = {
            /* These are the operations that are available from this service. */
            initialise: initialise,
            refresh: refresh,
            userType: function () { return usersettingData.userType; },
            salesDetail: function () { return usersettingData.salesDetail; },
            helpBarDetail: function() {return usersettingData.helpBar;},
            setUserTypeData: function (userType) { return setUserTypeData(userType); },
            setSalesType: function (selectedSalesType) { return setSalesType(selectedSalesType); },
            setHelpBar: function (helpBarVal) { return setHelpBar(helpBarVal); }
        };

        return service;

        function initialise() {
            if (initialised === true) {
                return $q.when(); // Already Initialised.
            }
            if (initPromise && !initFailed) {
                return initPromise; // already initialized/ing
            }
            initFailed = false;
            initPromise = null;
            // Get all the data from the server
            initPromise = getData().then(function() { initialised = true });
            return initPromise;
        } // end initialise()

        function refresh() {
            initPromise = null;
            // Get all the data from the server
            initPromise = getData().then(function() { initialised = true });
            return initPromise;
        }

        function getData() {
            // Get the userType and salesDetail from cookie.
            // The requests are queued into a single promise
            initPromise = $q.all([getSavedSettings()]);
            // Add a timeout for the promise.
            initPromise = Q.timeout(initPromise, config.serverTimeoutMs)
                .fail(initialzeServerTimeout)
                .to$q(); // converts Q.js promise to $q promise

            function failure(error) {
                initFailed = true;
                log.logError(error.message, "Data initialization failed", "datacontext", true);
                throw error; // so downstream fail handlers hear it too
            }

            function initialzeServerTimeout(error) {
                if (/timed out/i.test(error.message)) {
                    error.message = 'System ' + error.message + '; System may be currently unavailable.';
                    failure(error);
                }
                throw error;
            }

            return initPromise;
        }
        function getSavedSettings() {
            usersettingData.userType = JSON.parse(localStorage.getItem('userType'));
            usersettingData.salesDetail = JSON.parse(localStorage.getItem('selectedSalesType'));
            usersettingData.helpBar = JSON.parse(localStorage.getItem('selectedHelpBar'));
        }

        function setUserTypeData(usertype) {
            usersettingData.userType = usertype;
            localStorage.setItem('userType', JSON.stringify(usertype));
            notifyUserSettingsChanged();
        }

        function setSalesType(selectedSalesType) {
            usersettingData.salesDetail = selectedSalesType;
            localStorage.setItem('selectedSalesType', JSON.stringify(selectedSalesType));
            notifyUserSettingsChanged();
        }
        function setHelpBar(helpBarVal) {
           // alert('sethelpbar funciton--->'+helpBarVal);
            usersettingData.helpBar = helpBarVal;
            localStorage.setItem('selectedHelpBar', JSON.stringify(helpBarVal));
            notifyUserSettingsChanged();
        }
        // Broadcast an event so other code may take action when user settings change
        function notifyUserSettingsChanged() {
            // Broadcast a global event that the user settings have changed.
            $rootScope.$broadcast('userSettingsChanged');
        }

        // Force initialisation of user settings immediately.
        initialise();
    }

})();
