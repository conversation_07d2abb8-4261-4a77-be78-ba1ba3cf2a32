(function () {
    'use strict';
    angular
        .module('app')
        .component('outcomesSummary', {
            bindings: {
                assessment: '<',
                clientId: '<',
                complianceStatusList: '<',
                complianceMethodCode: '<',
                complianceMethodList: '<',
                initialComplianceMet: '<',
                selectedComplianceOptionCode: '<',
                assessmentSoftwareList: '<',
                setFinalComplianceMethod: '&',
                isLocked: '<',
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/outcomes-summary/outcomes-summary.html',
            controller: OutcomesSummary,
            controllerAs: 'vm'
        });

    OutcomesSummary.$inject = ['performancerequirementp262service'];

    function OutcomesSummary(performancerequirementp262service) {
        var vm = this;
        vm.assessment.p261Description = null;
        vm.performanceRequirementP262List = [];

        vm.$onInit = onInit;
        vm.$onChanges = onChanges;

        function onInit() {
            performancerequirementp262service.getList().then(function (data) {
                vm.performanceRequirementP262List = data.data;
            });

            vm.setFinalComplianceMethod();
        }

        function onChanges(changes) {
            if (changes.complianceMethodCode || changes.initialComplianceMet || changes.selectedComplianceOptionCode) {
                vm.setFinalComplianceMethod();
            }
        }

        /** Return the selected compliance option or the baseline as a fallback */
        vm.selectedComplianceOption = function () {
            const selected = vm.assessment.allComplianceOptions.find(x => x.isSelected);
            return selected ?? vm.assessment.allComplianceOptions[0];
        }
    }
})();