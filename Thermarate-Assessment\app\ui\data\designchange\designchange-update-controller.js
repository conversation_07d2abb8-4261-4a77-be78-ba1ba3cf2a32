(function () {
    // The DesignChangeUpdateCtrl supports a list page.
    'use strict';
    const controllerId = 'DesignChangeUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'designchangeservice', 'manufacturerservice', 'uuid4', 'security', designChangeUpdateCtrl]);
    function designChangeUpdateCtrl($rootScope, $scope, $mdDialog, $stateParams,
        $state, designchangeservice, manufacturerservice, uuid4, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Design Change'
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.designChange = {
            designChangeId: uuid4.generate(),
            description: null,
        };

        vm.manufacturers = [];
        var manufacturerPromise = manufacturerservice.getList()
            .then(data => { vm.manufacturers = data.data; });

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New DesignChange";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.designChangeId = $scope.designChangeId;
            }
            vm.hideActionBar = true;
        } else {
            vm.designChangeId = $stateParams.designChangeId;
        }

        // Get data for object to display on page
        var designChangePromise = null;
        if (vm.designChangeId != null) {
            designChangePromise = designchangeservice.getDesignChange(vm.designChangeId)
            .then(function (data) {
                if (data != null) {
                    vm.designChange = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("designchange-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                designchangeservice.createDesignChange(vm.designChange).then(function(data){
                    vm.designChange = data;
                    vm.designChangeId = vm.designChange.designChangeId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                designchangeservice.updateDesignChange(vm.designChange).then(function(data){
                    if (data != null) {
                        vm.designChange = data;
                        vm.designChangeId = vm.designChange.designChangeId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            designchangeservice.deleteDesignChange(vm.designChangeId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            designchangeservice.undoDeleteDesignChange(vm.designChangeId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();