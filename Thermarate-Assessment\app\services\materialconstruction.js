// Service used to retreive weather data for specific NCC Climate Zones 
// (e.g.climate zone 8, climate zone 43, etc)
(function () {
    'use strict';

    const SERVICE_ID = 'materialconstruction';

    angular.module('appservices').factory(SERVICE_ID,
        ['common', 'config', '$http',  materialconstruction]);

    function materialconstruction(common, config, $http) {

        const log = common.logger;
        const baseUrl = config.servicesUrlPrefix + 'materialconstruction/';

        // These are the operations that are available from this service.
        var service = {
            processDataset,
        };

        /**
         * Uploads a excel file to our server which is then extracted and processed into the
         * database, over-writing any prior data.
         * 
         * @param {any} excelFile The excel file to upload. 
         */
        function processDataset(excelFile) {

            let url = baseUrl + 'ProcessDataset';
            return $http({
                url: url, 
                method: "POST",
                data: excelFile,
                headers: {
                    "Content-Type": "application/vnd.ms-excel"
                }
            }).then(
                (data) => handleSuccess(data, "Database successfully Upated"),
                (error) => handleFail(error, "Error processing new dataset!")
            );
        }
       
        /** Generic success handling function. Checks for success and returns data if so. */
        function handleSuccess(response, popupMessage = null) {
            // console.log("Got response", response);
            if (response != null && response.data != undefined && response.data != null) {

                if (popupMessage != null)
                    log.logSuccess(popupMessage);
                
                return response.data;
            }
            else {
                return null;
            }
        }

        /** Generic failure handling function. Logs a message with small popup. */
        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        return service;

    }
})();
