// Generical modal where you can pass an array of options, and the html form will ve flled out
// as needed to collect the given options.
// NOTE: Only support 'select' UI input type at the moment, should be easy to add the rest as needed.
// Current format is as follows:
// let options = [
//    {
//        title: "Construction Category",
//        field: 'constructionCategoryCode',
//        operator: 'eq',
//        elementType: "select", // This is what is used to change the element type in our modal!
//        values: [
//            { title: 'Roof', value: 'Roof' },
//            { title: 'Ground Floor', value: 'GroundFloor' },
//            { title: 'Exterior Floor', value: 'ExteriorFloor' },
//            { title: 'Interior Glazing', value: 'InteriorGlazing' },
//            { title: 'Skylight', value: 'Skylight' },
//            { title: 'Roof Window', value: 'RoofWindow' },
//        ]
//    }
// ];
// 
// And the response format is the same as all smart tabels use, e.g.:
// filter = [
//      { field: "description", operator: "startswith", value: val, logic: "or" },
//      { field: "constructionCategoryTitle", operator: "contains", value: val }
// ];

(function () {
    'use strict';
    var controllerId = 'GenericFilterCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'clientservice', 'userservice', 'projectdescriptionservice', 'priorityservice', genericFilterModalController]);
    function genericFilterModalController($scope, $mdDialog, clientservice, userservice, projectdescriptionservice, priorityservice) {
        var vm = this;

        vm.filterData = {};
        vm.options = $scope.options;

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.submitSelection = function () {

            // Transform our response, which is currently in the form of
            // { key1: value1, key2: value2 }
            // into an array of actual filters as required by our smart table.
            let filters = [];
            vm.options.forEach((option, i) => {
                if (vm.filterData[option.field] != null) {

                    let transformedFilter = {
                        field: option.field,
                        operator: option.operator,
                        value: vm.filterData[option.field],
                    };

                    // Need to add a joining and, so not on last
                    console.log('i < vm.options.length - 1:', i < vm.options.length - 1)
                    if (i < vm.options.length - 1)
                        transformedFilter.logic = 'and';

                    // String looks to be the default
                    if (option.field === 'manufacturerId')
                        transformedFilter.valueType = 'guid?'

                    filters.push(transformedFilter);
                }
            });

            $mdDialog.hide(filters);
        };

        vm.clearAll = function () {
            vm.filterData = {};
        };

        vm.clearAllShow = () => !!(Object.keys(vm.filterData).length);
    }
})();