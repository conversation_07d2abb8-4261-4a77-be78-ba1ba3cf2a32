-- USE [thermarate];

SELECT TOP (100)
    --    [ErrorId]
    --    [TimeUtc]
      ,FORMAT(DATEADD(HOUR, 8, [TimeUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__TimeFormatted]
    --   ,[Application]
    --   ,[Host]
    --   ,[Type]
    --   ,[Source]
      ,[Message]
    --   ,[User]
    --   ,[StatusCode]
    --   ,[Sequence]
      ,[AllXml]
  FROM [dbo].[ELMAH_Error]
  WHERE 1=1
    -- AND [TimeUtc] > '2025-02-17 02:34' AND [TimeUtc] < '2025-02-17 02:36'
  ORDER BY [TimeUtc] DESC

--   SELECT COUNT([ErrorId])
--   FROM [dbo].[ELMAH_Error]