<form name="businessunitform" 
      class="main-content-wrapper" 
      novalidate
      data-ng-controller='BusinessunitUpdateCtrl as vm'
      ng-style="{ 'width' : vm.isModal ? '680px' : '' }">

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex">
                    <md-card>
                        <md-card-header>
                            Business Unit
                        </md-card-header>
                        <md-card-content>

                            <fieldset ng-disabled="(vm.newRecord && vm.createPermission == false) || (!vm.newRecord && vm.editPermission == false)">

                                <!-- ******** Name ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Name</label>
                                    <input type="text" name="name" 
                                           ng-model="vm.businessUnit.name" md-autofocus 
                                           md-maxlength="150"
                                           required/>
                                    <div ng-messages="businessunitform.name.$error">
                                        <div ng-message="required">Name is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 150.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Business Unit Type ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Business Unit Type</label>
                                    <md-select name="businessUnitType"  
                                            required
                                            ng-model="vm.businessUnit.businessUnitType"
                                            ng-model-options="{trackBy: '$value.type'}">
                                        <md-option ng-value="item" 
                                                   ng-repeat="item in vm.businessUnitTypeList track by item.type">
                                            {{item.type}}
                                        </md-option>
                                    </md-select>
                                    <div ng-messages="businessunitform.businessUnitType.$error">
                                        <div ng-message="required">Business Unit Type is required.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Manager Employee ******** -->
                                <md-autocomplete md-input-name="managerUserId"  
                                             md-input-minlength="2"
                                             md-min-length="0"
                                             md-selected-item="vm.businessUnit.manager"
                                             md-search-text="vm.managerUserIdSearchText"
                                             md-items="item in vm.getemployees(vm.managerUserIdSearchText)"
                                             md-item-text="item.fullName"
                                             md-require-match
                                             md-floating-label="Manager Employee">
                                    <md-item-template>
                                        <span md-highlight-text="vm.managerUserIdSearchText">{{item.fullName}}</span>
                                    </md-item-template>
                                </md-autocomplete>

                                <!-- ******** Business Unit Prefix ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Business Unit Prefix</label>
                                    <input type="text" name="businessUnitPrefix" 
                                            ng-model="vm.businessUnit.businessUnitPrefix"  
                                            md-maxlength="4"
                                        />
                                    <div ng-messages="businessunitform.businessUnitPrefix.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 4.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Email Address ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Email Address</label>
                                    <input type="email" name="emailAddress" 
                                            ng-model="vm.businessUnit.emailAddress"  
                                            md-maxlength="150"
                                        />
                                    <div ng-messages="businessunitform.emailAddress.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 150.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Phone ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Phone</label>
                                    <input type="text" name="phone" 
                                            ng-model="vm.businessUnit.phone"  
                                            md-maxlength="20"
                                           ui-mask="(99) 9999 9999" 
                                           placeholder="(00) 0000 0000"
                                           model-view-value="true"
                                        />
                                    <div ng-messages="businessunitform.phone.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Mobile ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Mobile</label>
                                    <input type="text" name="mobile" 
                                            ng-model="vm.businessUnit.mobile"  
                                            md-maxlength="20"
                                            phone-format
                                        />
                                    <div ng-messages="businessunitform.mobile.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Fax ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Fax</label>
                                    <input type="text" name="fax" 
                                            ng-model="vm.businessUnit.fax"  
                                            md-maxlength="20"
                                        />
                                    <div ng-messages="businessunitform.fax.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Address Line1 ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address Line1</label>
                                    <input type="text" name="addressLine1" 
                                            ng-model="vm.businessUnit.addressLine1"  
                                            md-maxlength="80"
                                        />
                                    <div ng-messages="businessunitform.addressLine1.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 80.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Address Line2 ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address Line2</label>
                                    <input type="text" name="addressLine2" 
                                            ng-model="vm.businessUnit.addressLine2"  
                                            md-maxlength="80"
                                        />
                                    <div ng-messages="businessunitform.addressLine2.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 80.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Address Suburb ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address Suburb</label>
                                    <input type="text" name="addressSuburb" 
                                            ng-model="vm.businessUnit.addressSuburb"  
                                            md-maxlength="80"
                                        />
                                    <div ng-messages="businessunitform.addressSuburb.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 80.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Address State ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address State</label>
                                    <md-select name="addressStateCode"  
                                            ng-model="vm.businessUnit.addressStateCode">
                                        <md-option ng-value>none</md-option>
                                        <md-option ng-value="item.stateCode" 
                                                ng-repeat="item in vm.stateList track by item.stateCode">
                                            {{item.name}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Address Post Code ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address Post Code</label>
                                    <input type="text" name="addressPostCode" 
                                            ng-model="vm.businessUnit.addressPostCode"  
                                            md-maxlength="15"/>
                                    <div ng-messages="businessunitform.addressPostCode.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 15.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Address Formatted ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Address Formatted</label>
                                    <input type="text" name="addressFormatted" 
                                            ng-model="vm.businessUnit.addressFormatted"  
                                            md-maxlength="400"
                                        />
                                    <div ng-messages="businessunitform.addressFormatted.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 400.</div>
                                    </div>
                                </md-input-container>

                            </fieldset>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.businessUnit"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="businessunitform.$invalid || vm.editPermission == false" 
                           ng-show="vm.businessUnit.deleted != true" 
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="admin__businessunit__delete"
                           ng-show="vm.businessUnit.businessUnitId > 0 && vm.businessUnit.deleted != true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="admin__businessunit__delete"
                           ng-show="vm.businessUnit.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
