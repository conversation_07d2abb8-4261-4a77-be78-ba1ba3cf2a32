(function () {
    // The AssessmentsoftwareUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'AssessmentsoftwareUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q', 
        'assessmentsoftwareservice', 'assessmentsoftwarecompliancemethodservice', 'compliancemethodservice', 'security', assessmentsoftwareUpdateController]);
    function assessmentsoftwareUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, $q, 
        assessmentsoftwareservice, assessmentsoftwarecompliancemethodservice, compliancemethodservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Assessment Software';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.assessmentSoftwareCode = null;
        vm.assessmentsoftware = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.energyLoadUnitsList = [
            "kWh",
            "MJ",
        ];
        if (vm.newRecord) {
            vm.title = "New Assessment Software";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.assessmentSoftwareCode = $scope.assessmentSoftwareCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.assessmentSoftwareCode = $stateParams.assessmentSoftwareCode;
        }

        // Get data for object to display on page
        var assessmentSoftwareCodePromise = null;
        if (vm.assessmentSoftwareCode != null) {
            assessmentSoftwareCodePromise = assessmentsoftwareservice.getAssessmentSoftware(vm.assessmentSoftwareCode)
            .then(function (data) {
                if (data != null) {
                    vm.assessmentsoftware = data;
                } else {
                    vm.isBusy = false;
                }
            });
        }
        else {
            vm.isBusy = false;
        }

        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
            });

        $q.all([complianceMethodPromise, assessmentSoftwareCodePromise]).then(function () {
            for (var ii = 0; ii < vm.complianceMethodList.length; ii++) {
                var foundCM = false;
                for (var jj = 0; jj < vm.assessmentsoftware?.assessmentSoftwareComplianceMethods?.length; jj++) {
                    if (vm.complianceMethodList[ii].complianceMethodCode === vm.assessmentsoftware.assessmentSoftwareComplianceMethods[jj].complianceMethodCode) {
                        vm.complianceMethodList[ii].isAvailable = vm.assessmentsoftware.assessmentSoftwareComplianceMethods[jj].isAvailable;
                        foundCM = true;
                        break;
                    }
                }
                if (!foundCM) {
                    vm.complianceMethodList[ii].isAvailable = false;
                }
            }
            vm.isBusy = false;
        });

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("assessmentsoftware-list");
                }
            }
        }

        vm.combineAssessmentSoftwareComplianceMethods = function () {
            var ascm = [];

            for (var ii = 0; ii < vm.complianceMethodList.length; ii++) {
                ascm.push({ complianceMethodCode: vm.complianceMethodList[ii].complianceMethodCode, isAvailable: vm.complianceMethodList[ii].isAvailable });
            }

            return ascm;
        }

        vm.save = function () {
            vm.isBusy = true;
            var ascm = vm.combineAssessmentSoftwareComplianceMethods();
            if(vm.newRecord == true){
                assessmentsoftwareservice.createAssessmentSoftware(vm.assessmentsoftware, ascm).then(function(data){
                    vm.assessmentsoftware = data;
                    vm.assessmentSoftwareCode = vm.assessmentsoftware.assessmentSoftwareCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                assessmentsoftwareservice.updateAssessmentSoftware(vm.assessmentsoftware, ascm).then(function(data){
                    if (data != null) {
                        vm.assessmentsoftware = data;
                        vm.assessmentSoftwareCode = vm.assessmentsoftware.assessmentSoftwareCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            assessmentsoftwareservice.deleteAssessmentSoftware(vm.assessmentSoftwareCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            assessmentsoftwareservice.undoDeleteAssessmentSoftware(vm.assessmentSoftwareCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.softwareFileTypes = assessmentsoftwareservice.softwareFileTypes;

    }
})();