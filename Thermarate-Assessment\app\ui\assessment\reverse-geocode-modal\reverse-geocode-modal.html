
<md-dialog data-ng-controller="reverseGeocodeModalController as vm" 
           aria-label={{title}}>
    <form ng-cloak>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Change Site Location</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content style="padding: 20px;">
            <div style="font-weight: bold; font-size: 16px; margin: 10px 0 45px 0;">
                {{vm.newAddress}}
            </div>
            <div>
                Do you wish to update the address and all associated information, or GPS coordinates only?
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">

            <md-button class="md-raised md-primary"
                       ng-click="vm.ok('all')">
                ALL DATA
            </md-button>

            <md-button class="md-raised md-primary"
                       ng-click="vm.ok('gps')">
                GPS COORDINATES ONLY
            </md-button>

            <md-button class="md-raised" 
                       ng-click="vm.cancel()">
                CANCEL
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>