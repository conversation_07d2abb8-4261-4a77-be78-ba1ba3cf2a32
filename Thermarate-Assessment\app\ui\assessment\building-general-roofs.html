<md-card ng-form="GeneralRoofsDataForm{{vm.complianceOption.optionIndex}}{{vm.sourceType}}">
  <md-card-content>

    <!-- Roofs -->
    <div class="table-responsive-vertical"
         style="margin-bottom: 15px; padding-top: 1px;">

      <!-- Title + "Not Applicable" checkbox -->
      <div style="display: flex; justify-content: space-between; align-items: center;">

        <h2 style="margin: 15px;">Roofs</h2>

        <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['generalroofs']"
                     class="not-applicable-checkbox"
                     ng-change="vm.removeRoofs(vm.source.roofs)"
                     ng-disabled="vm.disabledEx()">
          Not Applicable
        </md-checkbox>
      </div>

      <table ng-if="vm.source.zoneTypesNotApplicable['generalroofs'] == false || vm.source.zoneTypesNotApplicable['generalroofs'] == null"
             class="table table-striped table-hover table-condensed">
        <thead>
        <tr>
          <th style="width: 20px; text-align: center;">
            <md-checkbox ng-model="vm.selectedForRoofBulkEdit"
                         ng-disabled="vm.disabledEx()"
                         ng-click="vm.toggleBulkEditAll(!vm.selectedForRoofBulkEdit, 'selectedForRoofBulkEdit', vm.source.roofs)"
                         style="margin: auto;">
            </md-checkbox>
          </th>
          <th class="text-left clickable" ng-click="vm.sortBy('Roof', 'zoneNumber');">
            Roof Number
            <i ng-if="vm.tableSortInfo.column === 'zoneNumber' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'zoneNumber' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-left clickable" ng-click="vm.sortBy('Roof', 'zoneDescription');">
            Roof Name
            <i ng-if="vm.tableSortInfo.column === 'zoneDescription' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'zoneDescription' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-left clickable" ng-click="vm.sortBy('Roof', 'zoneType.description');">
            Space Type
            <i ng-if="vm.tableSortInfo.column === 'zoneType.description' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'zoneType.description' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-right clickable" ng-click="vm.sortBy('Roof', 'roofPitch');">
            Roof Pitch ({{vm.symbol("degrees")}})
            <i ng-if="vm.tableSortInfo.column === 'roofPitch' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'roofPitch' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-right clickable" ng-click="vm.sortBy('Roof', 'roofArea');">
            Roof Area - Horizontal (m<sup>2</sup>)
            <i ng-if="vm.tableSortInfo.column === 'floorArea' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'floorArea' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-right clickable" ng-click="vm.sortBy('Roof', 'roofArea');">
            Roof Area - Pitched (m<sup>2</sup>)
            <i ng-if="vm.tableSortInfo.column === 'floorArea' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'floorArea' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>
          <th class="text-right clickable" ng-click="vm.sortBy('Roof', 'perimeter');">
            Perimeter (m)
            <i ng-if="vm.tableSortInfo.column === 'perimeter' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'perimeter' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>

          <th class="text-right clickable" ng-click="vm.sortBy('Roof', 'storey.floor');">
            Storey
            <i ng-if="vm.tableSortInfo.column === 'storey.floor' && vm.tableSortInfo.direction === 'ASC'" class="fa fa-caret-up"></i>
            <i ng-if="vm.tableSortInfo.column === 'storey.floor' && vm.tableSortInfo.direction === 'DESC'" class="fa fa-caret-down"></i>
          </th>

          <!-- Actions -->
          <th class="text-left" style="width: 32px;"></th>

        </tr>

        </thead>
        <tbody>

        <tr ng-repeat="item in vm.sortedTableFunc()"
            lr-drag-src="roofs"
            lr-drop-target="roofs"
            lr-drop-success="vm.renumberRoofs()"
            lr-match-property="spaceId"
            lr-drag-data="vm.source.roofs"
            lr-match-value="{{item.zoneId}}"
            lr-index="vm.source.roofs.indexOf(item)">

          <td style="text-align: center;">
            <md-checkbox ng-model="item.selectedForRoofBulkEdit"
                         ng-click="vm.bulkEditAllRoofs = false;"
                         ng-disabled="vm.disabledEx()"
                         style="margin: auto;" />
          </td>

          <!-- Roof Number -->
          <td data-title="Roof Number" ng-class="{ 'draggable': !vm.disabledEx() }">
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx() }">

              <input type="text"
                     name="RoofNumber{{$index}}"
                     ng-model="item.zoneNumber"
                     ng-required="true"
                     ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                     ng-blur="vm.updateSource(item, item.zoneNumber, 'R');" />
              <div ng-messages="RoofListForm['RoofNumber'+$index].$error">
              </div>
            </md-input-container>
          </td>

          <!-- Roof Name -->
          <td data-title="Roof Name"
              lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">

              <input type="text"
                     name="RoofName{{$index}}"
                     ng-model="item.zoneDescription"
                     ng-required="true"
                     ng-disabled="vm.disabledEx()" />
              <div ng-messages="RoofListForm['RoofName'].$error">
              </div>
            </md-input-container>
          </td>

          <!-- Space Type -->
          <td data-title="Roof Type" class="text-left" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">

              <input type="text"
                     ng-value="'Roof'"
                     ng-disabled="true" />
            </md-input-container>
          </td>

          <!-- Roof Pitch -->
          <td data-title="RoofPitch" class="text-right" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">

              <input type="text"
                     name="RoofPitch{{$index}}"
                     ng-model="item.roofPitch"
                     ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                     formatted-number
                     decimals="2"
                     ng-required="true"
                     ng-disabled="vm.disabledEx()" />
              <div ng-messages="RoofListForm['RoofPitch'+$index].$error">
              </div>
            </md-input-container>
          </td>

          <!-- Roof Area - Horizontal -->
          <td data-title="Roof Area" class="text-right" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">
              <input type="text"
                     name="RoofAreaHorizontal{{$index}}"
                     ng-model="item.roofArea"
                     ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                     formatted-number
                     decimals="2"
                     ng-required="true"
                     ng-disabled="vm.disabledEx()" />
            </md-input-container>
          </td>

          <!-- Roof Area - Pitch -->
          <td data-title="Roof Area" class="text-right" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">
              <input type="text"
                     name="RoofAreaPitch{{$index}}"
                     value="{{vm.calcRoofAreaPitch(item)}}"
                     ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                     decimals="2"
                     ng-disabled="true" />
            </md-input-container>
          </td>

          <!-- Perimeter -->
          <td data-title="Perimeter" class="text-right" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                ng-class="{'input-black' : !vm.disabledEx()}">

              <input type="text" name="RoofPerimeter{{$index}}"
                     ng-model="item.perimeter"
                     ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                     formatted-number
                     ng-required="false"
                     decimals="2"
                     ng-disabled="vm.disabledEx()" />
              <div ng-messages="RoofListForm['Perimeter'+$index].$error">
            </md-input-container>
          </td>

          <!-- Storey -->
          <td data-title="Storey" class="text-right" lr-no-drag>
            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

              <md-select ng-required="true"
                         style="margin: 0;"
                         name="RoofStorey{{$index}}"
                         ng-disabled="vm.disabledEx()"
                         ng-model="item.storey"
                         ng-change="vm.calculateAllowance(item)">
                <md-option ng-value="x.floor"
                           ng-repeat="x in vm.source.storeys">
                  {{x.name}}
                </md-option>
              </md-select>
            </md-input-container>
          </td>

          <!-- Action Buttons -->
          <td data-title="Clear"
              lr-no-drag
              class="text-center">

            <div ng-include="'roof-more-actions-multi-button'"
                 style="display: flex; justify-content: center; align-content: center;"/>
          </td>

        </tr>

        </tbody>

      </table>

      <div ng-if="vm.source.zoneTypesNotApplicable['generalroofs'] == false || vm.source.zoneTypesNotApplicable['generalroofs'] == null"
           layout="row"
           style="padding: 10px 2px;">

        <md-button class="md-raised md-primary"
                   ng-click="vm.addRoof(vm.source.roofs, 'R')"
                   ng-show="!vm.disabledEx()"
                   ng-disabled="vm.disabledEx()">
          Add
        </md-button>

        <md-button class="md-raised md-primary"
                   ng-click="vm.showBulkEdit('selectedForRoofBulkEdit', 'bulkEditAllRoofs', vm.source.roofs, 'R')"
                   ng-show="!vm.disabledEx()"
                   ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForRoofBulkEdit')">
          Bulk Edit
        </md-button>

      </div>
    </div>

  </md-card-content>
</md-card>

<script type="text/ng-template" id="roof-more-actions-multi-button">

  <!-- 'More' button w/ Popup -->
  <md-menu ng-show="!vm.disabledEx()">

    <!-- Initial '...' button, which launches options -->
    <img md-menu-origin
         class="clickable"
         ng-click="$mdOpenMenu()"
         src="/content/feather/more-horizontal.svg"
         ng-disabled="vm.disabled"/>
    <md-menu-content>

      <md-menu-item>

        <!-- Duplicate -->
        <md-button ng-click="vm.cloneRoof(item, 'R')">
          Duplicate
        </md-button>
      </md-menu-item>

      <md-menu-item>
        <!-- Delete -->
        <md-button ng-click="vm.removeRoof(item)">
          <span style="color: orangered;">Delete</span>
        </md-button>
      </md-menu-item>

    </md-menu-content>
  </md-menu>

</script>
