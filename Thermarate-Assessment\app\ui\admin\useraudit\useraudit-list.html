<section id="useraudit-list-view" class="main-content-wrapper" data-ng-controller="UserauditListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-new-record="vm.newRecord()"
                data-new-record-text="New Useraudit"
                data-show-add-button="false"
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Suburb"
                data-query-builder-current="vm.currentQuery">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.userauditList"
                    st-global-search="vm.listFilter"
                    st-table-filtered-list="exportList"
                    st-persist="userauditList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="createdOn" class="can-sort text-center">Date</th>
                        <th st-sort="userName" class="can-sort text-left">Username</th>
                        <th st-sort="eventType" class="can-sort text-left">Event Type</th>
                        <th st-sort="eventDescription" class="can-sort text-left">Event Description</th>
                        <th st-sort="ipAddress" class="can-sort text-left">Ip Address</th>
                        <th st-sort="browserDesc" class="can-sort text-left">Browser Desc</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.userauditList">
                        <td class="text-center">{{row.createdOn | date: 'dd/MM/yyyy hh:mma'}}</td>
                        <td class="text-left">{{row.userName}}</td>
                        <td class="text-left">{{row.eventType}}</td>
                        <td class="text-left"><span ng-class="{'label label-warning': row.eventDescription != 'Logged in successfully' && row.eventType == 'Login'}">{{row.eventDescription}}</span></td>
                        <td class="text-left">{{row.ipAddress}}</td>
                        <td class="text-left">{{row.browserDesc}}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6" class="text-center">
                            <div st-pagination="" st-items-by-page="50" st-displayed-pages="7"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
