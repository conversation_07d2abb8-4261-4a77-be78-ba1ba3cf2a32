﻿(function () {
    'use strict';
    var serviceId = 'joblistexportservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', joblistexportservice]);

    function joblistexportservice(common, config, $http) {
        var log = common.logger;
        var baseUrl = config.servicesUrlPrefix + 'jobListExport/';

        var service = {
            /* These are the operations that are available from this service. */
            exportList: exportList
        };

        return service;

        function exportList(data) {
            var url = baseUrl + 'Export';
            return $http.post(url, data).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    log.logSuccess("Export Successfully created");
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Job List Export file: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();