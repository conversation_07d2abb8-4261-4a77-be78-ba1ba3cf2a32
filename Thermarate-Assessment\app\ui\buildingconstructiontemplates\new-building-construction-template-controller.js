(function () {
    'use strict';
    var controllerId = 'NewBuildingConstructionTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$scope', '$mdDialog', '$state', 'buildingconstructiontemplateservice', copyAssessmentController]);
    function copyAssessmentController($scope, $mdDialog, $state, buildingconstructiontemplateservice) {
        var vm = this;
        vm.isBusy = false;
        //keep track of radio button selected.
        // Values are 'new' and 'copy'
        vm.currentTemplateType = "new";
        //currently selected template when teh radio is on the 'copy' value. Selected from 'vm.currentTemplatesList' 
        vm.selectedTemplate = null;

        vm.currentTemplatesList = [];
        buildingconstructiontemplateservice.getAll('construction').then(function (data) {
            vm.currentTemplatesList = data;
        });

        vm.submit = function () {
            vm.isBusy = true;
            switch (vm.currentTemplateType) {
                case "new":
                    buildingconstructiontemplateservice.createEmpty($scope.type.toLowerCase()).then(function (data) {
                        console.log("Just created new template, data is:");
                        console.log(data);
                        vm.isBusy = false;
                        $state.go("buildingconstructiontemplate", { templateId: data.buildingConstructionTemplateId });
                        $mdDialog.hide();
                    });
                    break;
                case "copy":
                    buildingconstructiontemplateservice.copyTemplate(vm.selectedTemplate.buildingConstructionTemplateId).then(function (templateId) {
                        vm.isBusy = false;
                        $state.go("buildingconstructiontemplate", { templateId: templateId });
                        $mdDialog.hide();
                    });
                    break;
            }
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }
    }
})();