(function () {

  'use strict';

  // The name of the component in camelCase.
  // This is what you will use in the widget tree (but converted to <snake-case/>)
  const COMPONENT_NAME = "buildingGeneralDesign";

  // The URL of the HTML template this controller will control.
  const HTML_TEMPLATE_URL = "app/ui/assessment/building-general-design.html";

  angular
    .module('app')
    .component(COMPONENT_NAME, {

      // PARAMETERS THAT CAN BE PASSED TO COMPONENT
      bindings: {
        source: '<', // The building which is the source of this data.
        sourceType: '<',
        complianceOption: '<',
        disabled: '<'
      },

      templateUrl: HTML_TEMPLATE_URL,
      controller: Controller,

      controllerAs: 'vm'
    });

  // Inject all services required here (and make sure to add to the controller params too.)
  Controller.$inject = ['$scope', 'common', 'uuid4', '$q', 'assessmentcomplianceoptionservice', 'projectdescriptionservice'];

  function Controller($scope, common, uuid4, $q, assessmentcomplianceoptionservice, projectdescriptionservice) {

    let vm = this;

    vm.projectDescriptionList = [];
    var projectDescriptionPromise = projectdescriptionservice.getList()
      .then(function (data) {
        vm.projectDescriptionList = data.data;
      });

    $q.all([projectDescriptionPromise]).then(() => {
      // HACK We have to delay showing this otherwise md-autocomplete won't update to new available options...
      setTimeout(() => {
        vm.showProjectDescriptions = true;
      }, 150);
    });

    // Save searchtext so it can be set to the other field if user selects other from the dropdown.
    vm.projectDescriptionSearchChanged = function (item, searchText) {
      item["projectDescriptionPreviousSearchText"] = searchText;
    }

    vm.clearProjectDescription = function (item) {
      item.projectDescriptionSearchText = "";
      item.projectDescriptionPreviousSearchText = "";
      item.projectDescription = null;
      item.projectDescriptionOther = null;
    }

    vm.projectDescriptionChanged = function (item, newprojectDescription) {
      if (newprojectDescription != null && newprojectDescription.projectDescriptionCode === 'PDOther') {
        item.projectDescriptionOther = item.projectDescriptionPreviousSearchText;
      }

      // Update # storeys with glazing
      vm.storeysChanged(newprojectDescription)
    }

    vm.storeysChanged = function (projectDescription) {

      let newStoreys = assessmentcomplianceoptionservice
        .generateStoreys(projectDescription.numberOfStoreysWithGlazing, vm.source.storeys);

      assessmentcomplianceoptionservice.setStoreyNames(newStoreys);
      vm.source.storeys = newStoreys;

    }

    vm.symbol = function(symbolName) {
      return common.symbol(symbolName);
    }

  }
})();