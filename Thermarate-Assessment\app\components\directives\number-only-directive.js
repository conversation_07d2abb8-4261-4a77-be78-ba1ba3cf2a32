﻿(function () {
    'use strict';

    var app = angular.module('app');
    //Directive for numeric value
    app.directive('numberOnly', function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            scope: {
                ngModel: '='
            },
            link: function (scope) {
                scope.$watch('ngModel', function (newValue, oldValue) {
                    if (oldValue != undefined && oldValue.length > 0) {
                        if (newValue != undefined) {
                            if (typeof newValue == 'string') {
                                var notNumberCheck = newValue.replace(oldValue, '');
                                if (isNaN(newValue)) {
                                    if (notNumberCheck != '.') {
                                        scope.ngModel = oldValue;
                                        return;
                                    }
                                }
                            }
                        } else {
                            scope.ngModel = "";
                            return;
                        }
                    } else {
                        if (isNaN(newValue) && newValue != '.') {
                            scope.ngModel = "";
                            return;
                        }
                    }
                    var arr = String(newValue).split("");
                    if (arr.length === 0) return;
                    if (arr.length === 1 && (arr[0] == '-' || arr[0] === '.')) return;
                    if (arr.length === 2 && newValue === '-.') return;
                    if (isNaN(newValue)) {
                        scope.ngModel = oldValue;
                    }
                });
            }
        };
    });
})();