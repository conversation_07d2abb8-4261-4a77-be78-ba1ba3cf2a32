(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "resultsOverview";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/assessment/results/results-overview.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {

            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                assessment: '<',
                option: '<',
                building: '<',
                buildingType: '<',
                disabled: '<'
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: Controller,

            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    Controller.$inject = ['$scope', 'common','uuid4', '$q', 'zoneservice'];

    function Controller($scope, common, uuid4, $q, zoneservice) {

        let vm = this;

        vm.tabIdentifier = vm.option.optionIndex + (vm.option.proposed === vm.building ? 'Proposed' : 'Reference');
        vm.annualEnergyOptions = new EnergyResultsChartOptions("annual-energy-consumption" + vm.tabIdentifier,
            "Annual", "Daily", ["Heating", "Cooling"]);

        // vm.zoneEnergyOptions = new EnergyResultsChartOptions("zone-energy-use" + vm.tabIdentifier,
        //     "Annual", "Monthly", ["Heating", "Cooling"]);
        vm.zoneEnergyOptions = new ZoneEnergyResultsChartOptions("zone-energy-use" + vm.tabIdentifier,
            "Annual", "Monthly", ["Heating", "Cooling"], "Zone Name");

        // vm.zonePerAreaEnergyOptions = new EnergyResultsChartOptions("zone-energy-use-area" + vm.tabIdentifier,
        //     "Annual", "Monthly", ["Heating", "Cooling"]);
        vm.zonePerAreaEnergyOptions = new ZoneEnergyResultsChartOptions("zone-energy-use-area" + vm.tabIdentifier,
            "Annual", "Monthly", ["Heating", "Cooling"], "Zone Name");

        const ZONE_GROUPINGS = [
            "Zone Name",
            "Zone Activity",
            "Zone Type",
            "NCC Classification"
        ];
        vm.ZONE_GROUPINGS = ZONE_GROUPINGS;

        const PERIODS = [
            "Annual",
            "Summer",
            "Autumn",
            "Winter",
            "Spring",
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ];
        vm.PERIODS = PERIODS;

        const NAMED_PERIODS = {
            "Annual":      { months: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] },
            "January":      { months: [0] },
            "February":     { months: [1] },
            "March":        { months: [2] },
            "April":        { months: [3] },
            "May":          { months: [4] },
            "June":         { months: [5] },
            "July":         { months: [6] },
            "August":       { months: [7] },
            "September":    { months: [8] },
            "October":      { months: [9] },
            "November":     { months: [10] },
            "December":     { months: [11] },
            "Summer":       { months: [11, 0,  1] },
            "Autumn":       { months: [ 2, 3,  4] },
            "Winter":       { months: [ 5, 6,  7] },
            "Spring":       { months: [ 8, 9, 10] },
        }

        const VIEWS = [
            "Heating",
            "Cooling",
            "Total",
        ];
        vm.VIEWS = VIEWS;

        const MONTHS = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
        ];

        let thermalPerformanceCenterTooltipElement = null;
        let thermalPerformanceCenterElement = null;

        // TODO: Maybe move into shared service and then update climate-overview.js too
        function saveChart(chart, width = null, height = null) {
            // Get the chart's SVG code
            var svg = chart.getSVG({
                exporting: {
                    // Have to set to original value otherwise looks to be changing depending on screen size
                    sourceWidth: width || 878,
                    sourceHeight: height || 600
                    // sourceWidth: chart.chartWidth,
                    // sourceHeight: chart.chartHeight
                },
            });
            return svg;
        }

        // Setup a watch to check for changes in the certification.
        let unwatchA = $scope.$watch('vm.option.certification', refreshData);
        let unwatchB = $scope.$watch('vm.building.energyUsageSummary', refreshData);
        let unwatchC = $scope.$watch('vm.building.heating', refreshData);
        let unwatchD = $scope.$watch('vm.building.cooling', refreshData);
        let unwatchE = $scope.$watch('vm.building.totalEnergyLoad', refreshData);
        let unwatchZonesList = [];
        vm.building.zones.forEach((zone, i) => {
            unwatchZonesList.push($scope.$watch(`vm.building.zones[${i}].zoneDescription`, refreshData));
        });

        function refreshData(newVal, oldVal) {

            if(newVal == null)
                return;

            if(oldVal !== newVal)
                initialize(vm.assessment, vm.option, vm.building);
        }

        $scope.$on("$destroy", () => {
            if(unwatchA != null)
                unwatchA();

            if(unwatchB != null)
                unwatchB();

            if(unwatchC != null)
                unwatchC();

            if(unwatchD != null)
                unwatchD();

            if(unwatchE != null)
                unwatchE();

            unwatchZonesList.forEach(unwatchZone => {
                if(unwatchZone != null)
                    unwatchZone();
            })
        });

        const COLOUR_HEATING = "#E63845";
        const COLOUR_COOLING = "#3F87C4";
        const COLOUR_TOTAL   = "#B1B1B1";

        /**
         * Retrieve and initialize any required data here,
         * not just floating around the controller.
         */
        async function initialize(assessment, option, building) {

            const energyData = building.energyUsageSummary;

            if(energyData == null)
                return;

            const energyUnits = vm.option?.assessmentSoftware?.energyLoadUnits || '?';
            vm.energyUnits = energyUnits;

            // Donut Thermal Performance Summary
            let thermalPerformanceSummaryChart = Highcharts.chart('thermal-performance-summary' + vm.tabIdentifier, {

                chart: { type: 'pie', height: 600 },
                title: {
                    text: 'Thermal Performance Summary',
                    subtitle: {
                        text: 'Total',
                        verticalAlign: 'middle',
                        align: 'center',
                        y: 10,
                        floating: true,
                    },
                },
                credits: { enabled: false },

                legend: {
                    labelFormatter: function () { return this.name + ` (${energyUnits}/m<sup>2</sup>)`; },
                    itemStyle: { color: '#333333', fontWeight: 'normal' },
                    align: 'center',
                    verticalAlign: 'top',
                    y: 25,
                    floating: true,
                    backgroundColor: Highcharts.defaultOptions.legend.backgroundColor || 'white',
                    shadow: false,
                    useHTML: true
                },

                tooltip: {
                    pointFormat: 'Energy Usage: {point.y:.1f} <b>({point.percentage:.1f}%)</b>',
                    enabled: true,
                    useHTML: true
                },

                plotOptions: {
                    pie: {
                        dataLabels: {
                            enabled: true,
                            formatter: function () {
                                return this.y.toFixed(1)
                            },
                            distance: -50,
                            style: {
                                fontWeight: 'bold',
                                fontSize: '22px',
                                color: 'white'
                            }
                        },
                        size: '90%', // or will cut into legend
                        showInLegend: true,
                        startAngle: 0,
                        endAngle: 360,
                    },
                    series: { enableMouseTracking: true },
                },

                series: [{
                    type: 'pie',
                    name: energyUnits + ' / m<sup>2</sup>',
                    innerSize: '50%',
                    data: [
                        { name: 'Heating', y: building.heating, color: COLOUR_HEATING },
                        { name: 'Cooling', y: building.cooling, color: COLOUR_COOLING },
                    ]
                }]
            });

            let buildingEnergyUseChart          = buildBuildingEnergyUseChart(energyData, vm.annualEnergyOptions)
            let zoneEnergyUseChart              = buildZoneEnergyUseChart(energyData, vm.zoneEnergyOptions, false);
            let zoneEnergyUsePerAreaChart       = buildZoneEnergyUseChart(energyData, vm.zonePerAreaEnergyOptions, true);

            thermalPerformanceCenterElement = document
                .querySelector("#thermal-performance-center" + vm.tabIdentifier);

            thermalPerformanceCenterTooltipElement = document
                .querySelectorAll("#thermal-performance-center-tooltip" + vm.tabIdentifier);

            document.addEventListener('mousemove', updateThermalPerformanceCenterTooltip, false);

            // Save chart data SVG for use in reports.
            let chartData = { }

            chartData.thermalPerformanceSummaryChartSVG = saveChart(thermalPerformanceSummaryChart);

            // Set width and height explicitly to work with dimensions we require in report.
            chartData.buildingEnergyUseChartSVG = saveChart(buildingEnergyUseChart, 1400);
            chartData.zoneEnergyUseChartSVG = saveChart(zoneEnergyUseChart, 1400);
            // [THR-670] The last label on y-axis was being pushed to the 2nd row
            chartData.zoneEnergyUseChartSVG = chartData.zoneEnergyUseChartSVG.replace("width=\"1400\"", "width=\"1500\"");
            chartData.zoneEnergyUseChartSVG = chartData.zoneEnergyUseChartSVG.replace(" 1400 ", " 1500 ");
            chartData.zoneEnergyUsePerAreaChartSVG = saveChart(zoneEnergyUsePerAreaChart, 1400);
            chartData.zoneEnergyUsePerAreaChartSVG = chartData.zoneEnergyUsePerAreaChartSVG.replace("width=\"1400\"", "width=\"1500\"");
            chartData.zoneEnergyUsePerAreaChartSVG = chartData.zoneEnergyUsePerAreaChartSVG.replace(" 1400 ", " 1500 ");

            building.energyResultsChartData = chartData;
            building.energyResultsChartDataJson = JSON.stringify(chartData);

        }

        /**
         *
         *
         * @param energyData
         * @param {EnergyResultsChartOptions} options Options to use when generating the chart.
         */
        function buildBuildingEnergyUseChart(energyData, options) {

            if(energyData == null)
                return;

            const energyUnits = vm.option?.assessmentSoftware?.energyLoadUnits || '?'
            const chartData = calculateEnergyForPeriod(energyData, options.period, options.grouping);
            const series = createSeriesForView(chartData, options.view, energyUnits);

            return Highcharts.chart(options.id, {

                chart: {
                    type: 'column',
                    height: 600,

                    events: {

                        // Custom render function to center-align labels (yes, this functionality was not available
                        // natively via Highcharts.
                        // See https://www.highcharts.com/forum/viewtopic.php?t=7211 and
                        // https://jsfiddle.net/m0rxacd1/1/
                        render() {

                            if(this.xAxis[0].labelGroup == null)
                                return;

                            if(options.grouping === 'Monthly')
                                return;

                            var ticks = this.xAxis[0].ticks,
                                ticksPositions = this.xAxis[0].tickPositions,
                                tick0x,
                                tick1x,
                                getPosition = function (tick) {
                                    var axis = tick.axis;
                                    return Highcharts.Tick.prototype.getPosition.call(tick, axis.horiz, tick.pos, axis.tickmarkOffset);
                                };

                            tick0x = getPosition(ticks[ticksPositions[0]]).x;
                            tick1x = getPosition(ticks[ticksPositions[1]]).x;

                            this.xAxis[0].labelGroup.translate((tick1x - tick0x)/2)
                        }
                    },
                },

                plotOptions: {
                    series: {
                        dataLabels: {
                            useHTML: true
                        }
                    }
                },
                title: { text: 'Building Energy Use' },
                credits: { enabled: false },

                xAxis: chartData.xAxis,
                yAxis: {
                    min: 0,
                    title: {
                        text: "Energy Use (" + energyUnits + "/m<sup>2</sup>)",
                        useHTML: true
                    }
                },

                tooltip: {
                    pointFormat: "{point.series.name}: <b>{point.y:.1f}</b>",
                    useHTML: true
                },

                legend: {
                    itemStyle: { color: '#333333', fontWeight: 'normal' },
                    align: 'center',
                    verticalAlign: 'top',
                    y: 25,
                    floating: true,
                    backgroundColor: Highcharts.defaultOptions.legend.backgroundColor || 'white',
                    shadow: false,
                    useHTML: true
                },

                series: series
            });
        }

        /**
         * Calc
         *
         * @param energyData
         * @param { 'Annual' | 'Summer' | 'October' | string } period Refer to  vm.PERIODS for all available.
         * @param { 'Annually' | 'Monthly' | 'Daily' } grouping
         * @param {EnergyResultsChartOptions} perFloorArea If true, values will be shown as energyUnit/m2.
         *
         * @returns {EnergyResultsChartTransformedData}
         */
        function calculateEnergyForPeriod(energyData, period, grouping) {

            const periodToCheck = NAMED_PERIODS[period];

            let groupedHeatingForPeriod = [];
            let groupedCoolingForPeriod = [];
            let groupedTotalsForPeriod = [];

            let xAxis = {};

            if(grouping === "Monthly") {

                // Get actual months selected. No need to reverse for summer (which includes dec -> jan jump)
                const selectedMonths = [];
                periodToCheck.months.forEach(p => selectedMonths.push(MONTHS[p]));

                xAxis = {
                    categories: selectedMonths
                }

                let periodHeating = 0;
                let periodCooling = 0;
                let periodTotal = 0;

                // Add data from each month we are actually interested in (based on selected period)
                for(let m = 0; m < periodToCheck.months.length; m++) {
                    let monthIndex = periodToCheck.months[m];
                    periodHeating = energyData.monthlyHeating[monthIndex];
                    periodCooling = energyData.monthlyCooling[monthIndex];
                    periodTotal = periodHeating + periodCooling;

                    groupedHeatingForPeriod.push(periodHeating);
                    groupedCoolingForPeriod.push(periodCooling);
                    groupedTotalsForPeriod.push(periodTotal);
                }

            } else if(grouping === "Daily") {

                const dayIncrement = 1000 * 3600 * 24;
                const ticksInYear = dayIncrement * 365;

                let lowTick = Number.MAX_SAFE_INTEGER;
                let highTick = 0;

                for(let m = 0; m < periodToCheck.months.length; m++) {

                    let monthIndex = periodToCheck.months[m];

                    for(let i = 0; i < 365; i++) {

                        const day = energyData.dailyHeating[i];
                        day.date = new Date(day.date);

                        let ticks = day.date.getTime();

                        if(day.date.getMonth() !== monthIndex)
                            continue;

                        // When period === "Summer", we need to specify that the december month is actually from 1 year ago.
                        if(period === "Summer" && monthIndex === 11)
                            ticks -= ticksInYear;

                        if(ticks < lowTick)
                            lowTick = ticks;

                        if(ticks > highTick)
                            highTick = ticks;

                        let dayHeating = energyData.dailyHeating[i].energyUsage;
                        let dayCooling = energyData.dailyCooling[i].energyUsage;
                        let dayTotal = dayHeating + dayCooling;

                        groupedHeatingForPeriod.push([ticks, dayHeating]);
                        groupedCoolingForPeriod.push([ticks, dayCooling]);
                        groupedTotalsForPeriod.push([ticks, dayTotal]);
                    }

                }

                const min = new Date(lowTick);
                const max = new Date(highTick);

                const minUtc = Date.UTC(min.getFullYear(), min.getMonth(), min.getDate());
                const maxUtc = Date.UTC(max.getFullYear(), max.getMonth(), max.getDate());

                xAxis = {
                    type: 'datetime',
                    min: minUtc,
                    max: maxUtc,
                    startOnTick: true,
                    endOnTick: true,
                    showFirstLabel: true,
                    showLastLabel: false,

                    tickPositioner: function () {

                        const year = 2003;
                        var positions = [],
                            tick = Math.floor(this.dataMin  + (1000 * 3600 * 24));

                        positions.push(tick);

                        // Loop over months to determine how many 'ticks' are in each based on # days
                        for(let i = 0; i < periodToCheck.months.length; i++) {

                            // Determine number of days in THIS month by getting the date of the LAST DAY of the PRIOR month.
                            // (day 0 = last day of prior month fyi)
                            let monthIndex = periodToCheck.months[i];
                            let monthIndexToCheck = monthIndex === 11 ? 0 : monthIndex + 1;

                            let daysInMonth = new Date(year, monthIndexToCheck, 0).getDate();
                            tick += dayIncrement * daysInMonth;
                            positions.push(tick);
                        }

                        return positions;
                    },
                    labels: {
                        align: 'center',
                        formatter() {
                            return Highcharts.dateFormat('%b', this.value);
                        },
                    }
                };

            }

            return {
                xAxis: xAxis,
                heatingData: groupedHeatingForPeriod,
                coolingData: groupedCoolingForPeriod,
                totalsData: groupedTotalsForPeriod
            };

        }

        /**
         *
         * @param energyData
         * @param {EnergyResultsChartOptions} options
         * @param {EnergyResultsChartOptions} perFloorArea If true, values will be shown as kWh/m2.
         */
        function buildZoneEnergyUseChart(energyData, options, perFloorArea) {

            if(energyData == null)
                return;

            const energyUnits = vm.option?.assessmentSoftware?.energyLoadUnits || '?'
            const title = perFloorArea ? "Zone Energy Use (Per Conditioned Floor Area)" : "Zone Energy Use (Total)";
            const measurement = perFloorArea ? `${energyUnits}/m<sup>2</sup>` : energyUnits;

            const chartData = calculateZoneEnergyForPeriod(energyData, options.period, options.grouping, perFloorArea, options.zoneGrouping);

            const series = createSeriesForView(chartData, options.view, measurement);

            let selectedSeriesIndex = null;

            return Highcharts.chart(options.id, {

                chart: { type: 'bar', height: 600 },
                title: { text: title },
                credits: { enabled: false },
                xAxis: { categories: chartData.labelData, crosshair: true },
                yAxis: {
                    min: 0,
                    title: {
                        text: "Energy Use (" + measurement + ")",
                        useHTML: true
                    }
                },

                plotOptions: {
                    series: {
                      events: {
                        mouseOver: function() {
                            selectedSeriesIndex = this.index;
                        },
                      },
                      dataLabels: {
                        useHTML: true
                      }
                    }
                },

                tooltip: {
                    // pointFormat: "{point.series.name}: <b>{point.y:.1f}</b>"
                    formatter: function () {
                        let percentageData = '';
                        if (!perFloorArea) // Only show percentage for Total graph
                            percentageData = generatePercentageDataString(chartData, selectedSeriesIndex, this.y);

                        const energyUsage = parseFloat(this.y).toFixed(1);

                        let tooltip = `<span style="font-size: 11px">${this.x}</span><br><span style="color: ${this.color};">\u25CF&nbsp;</span>${this.series.name}: ${energyUsage} ${percentageData}`;
                        return tooltip;
                    },
                    useHTML: true
                },

                legend: {
                    itemStyle: { color: '#333333', fontWeight: 'normal' },
                    align: 'center',
                    verticalAlign: 'top',
                    y: -20,
                    floating: false,
                    backgroundColor: 'transparent',
                    shadow: false,
                    useHTML: true
                },

                series: series
            });
        }

        function generatePercentageDataString(chartData, seriesIndex, yCoordinate) {
            let heatingIndex = null;
            let coolingIndex = null;
            let totalIndex = null;
            let counter = 0;

            // Find the index of each series
            // The order is always heating, cooling, total but any combination of the 3 can be selected.
            if (vm.zoneEnergyOptions.view.includes('Heating'))
                heatingIndex = counter++;
            if (vm.zoneEnergyOptions.view.includes('Cooling'))
                coolingIndex = counter++;
            if (vm.zoneEnergyOptions.view.includes('Total'))
                totalIndex = counter++;

            // Find the hovered items average datapoint using the index of the hovered item in the hovered series.
            let indexOfData = -1;
            let data = null;
            switch (seriesIndex) {
                case heatingIndex:
                    indexOfData = chartData.heatingData.indexOf(yCoordinate);
                    data = indexOfData !== -1 ? parseFloat(chartData.heatingPercentageData[indexOfData]) : null;
                    break;
                case coolingIndex:
                    indexOfData = chartData.coolingData.indexOf(yCoordinate);
                    data = indexOfData !== -1 ? parseFloat(chartData.coolingPercentageData[indexOfData]) : null;
                    break;
                case totalIndex:
                    indexOfData = chartData.totalsData.indexOf(yCoordinate);
                    data = indexOfData !== -1 ? parseFloat(chartData.totalsPercentageData[indexOfData]) : null;
                    break;
                default:
                    break;
            }

            const percentageDataString = data ? `(${data.toFixed(1)}%)` : '';

            return percentageDataString;
        }

        /**
         * Calc
         *
         * @param energyData
         * @param { 'Annual' | 'Summer' | 'October' | string } period Refer to vm.PERIODS for all available.
         * @param { 'Annually' | 'Monthly' | 'Daily' } grouping
         * @param {EnergyResultsChartOptions} perFloorArea If true, values will be shown as energyUnits/m2.
         * @param { 'Zone Name' | 'Zone Activity' | string} zoneGrouping Refer to vm.ZONE_GROUPINGS for all available.
         *
         * @returns {EnergyResultsChartTransformedData}
         */
        function calculateZoneEnergyForPeriod(energyData, period, grouping, perFloorArea, zoneGrouping) {

            const groupedZones = getGroupedZones(zoneGrouping);

            const periodToCheck = NAMED_PERIODS[period];

            const perZonePeriodHeating = [];
            const perZonePeriodCooling = [];
            const perZonePeriodTotals = [];

            for(let i = 0; i < groupedZones.length; i++) {

                let periodHeating = 0;
                let periodCooling = 0;
                let periodTotal = 0;

                if(period !== "Annual") {

                    // Loop over all months adding data we are actually interested in (based on selected period)
                    for(let m = 0; m < periodToCheck.months.length; m++) {
                        let monthIndex = periodToCheck.months[m];
                        // Can be multiple zoneNumbers per grouped zone
                        groupedZones[i].zonesInGroup.forEach(zoneIdentifiers => {

                            const zoneIdentifier = zoneIdentifiers.zoneNumber;

                            periodHeating += energyData.perZoneMonthlyHeating[zoneIdentifier]?.[monthIndex];
                            periodCooling += energyData.perZoneMonthlyCooling[zoneIdentifier]?.[monthIndex];
                        });
                        // Total doesn't need to be inside loop.
                        periodTotal = (periodHeating + periodCooling);
                    }
                } else {
                    // Can be multiple zoneNumbers per grouped zone
                    groupedZones[i].zonesInGroup.forEach(zoneIdentifiers => {

                        const zoneIdentifier = zoneIdentifiers.zoneNumber;

                        periodHeating += energyData.perZoneAnnualHeating[zoneIdentifier];
                        periodCooling += energyData.perZoneAnnualCooling[zoneIdentifier];
                    });
                    // Total doesn't need to be inside loop.
                    periodTotal = (periodHeating + periodCooling);
                }

                if(perFloorArea === true) {
                    // Floor area is summed during the zone grouping stage so this should still work.
                    periodHeating /= groupedZones[i].floorArea;
                    periodCooling /= groupedZones[i].floorArea;

                    periodHeating = Number(periodHeating.toFixed(1));
                    periodCooling = Number(periodCooling.toFixed(1));
                    periodTotal = (periodHeating + periodCooling);
                }

                perZonePeriodHeating.push(periodHeating);
                perZonePeriodCooling.push(periodCooling);
                perZonePeriodTotals.push(periodTotal);
            }

            // Calculate percentages (this zone / all zones).
            const perZonePeriodHeatingPercentages = calculatePercentages(perZonePeriodHeating);
            const perZonePeriodCoolingPercentages = calculatePercentages(perZonePeriodCooling);
            const perZonePeriodTotalPercentages = calculatePercentages(perZonePeriodTotals);

            return {
                labelData: groupedZones.map(x => x.name),
                heatingData: perZonePeriodHeating,
                heatingPercentageData: perZonePeriodHeatingPercentages,
                coolingData: perZonePeriodCooling,
                coolingPercentageData: perZonePeriodCoolingPercentages,
                totalsData: perZonePeriodTotals,
                totalsPercentageData: perZonePeriodTotalPercentages
            };
        }

        /**
         * Calculates the percentages values for an array.
         * @param { 'Zone Name' | 'Zone Activity' | string} zoneGrouping Refer to vm.ZONE_GROUPINGS for all available.
         *
         * @returns { { name: string, zonesInGroup string[], floorArea: number, groupId: string } } Group object.
         */
        function getGroupedZones(zoneGrouping) {
            let zones = zoneservice.interiorZones(vm.building.zones).filter(x => x.conditioned === true);

            let groupedZones = [];

            // Combine all zones that share the same 'zoneGrouping' id.
            zones.forEach(zone => {
                // Keeping track of both name and groupId will seperate any groups that have the same name.
                let name = null;
                let groupId = null;
                if (zoneGrouping === 'Zone Name') { // Default
                    name = zone.zoneDescription;
                    groupId = zone.zoneDescription.toLowerCase();
                }
                if (zoneGrouping === 'Zone Activity') {
                    name = zone.zoneActivity.description;
                    groupId = zone.zoneActivity.zoneActivityCode.toLowerCase();
                }
                if (zoneGrouping === 'Zone Type') {
                    name = zone.zoneType.description;
                    groupId = zone.zoneType.zoneTypeCode.toLowerCase();
                }
                if (zoneGrouping === 'NCC Classification') {
                    name = zone.nccClassification.description;
                    groupId = zone.nccClassification.nccClassificationCode.toLowerCase();
                }

                // Check if already exists
                let foundGroup = groupedZones.find(foundGroup => foundGroup.groupId === groupId);
                if (foundGroup) {
                    // Found - name and groupId are the same.
                    foundGroup.zonesInGroup.push({
                        zoneNumber: zone.zoneNumber.toLowerCase(),
                        zoneDescription: zone.zoneDescription.toLowerCase() });
                    foundGroup.floorArea += zone.floorArea;
                }
                else {
                    // Add new entry
                    let newZone = {
                        name: name,
                        zonesInGroup: [{
                            zoneNumber: zone.zoneNumber.toLowerCase(),
                            zoneDescription: zone.zoneDescription.toLowerCase()
                        }], // Can have multiple
                        floorArea: zone.floorArea,
                        groupId: groupId
                    };
                    groupedZones.push(newZone);
                }
            });

            return groupedZones;
        }

        /**
         * Calculates the percentages values for an array.
         * @param { number[] } data
         *
         * @returns { number[] } Array of percentage values.
         */
        function calculatePercentages(data) {
            const total = data.reduce((a, b) => a + b, 0);
            const percentages = data.map(x => (x / total * 100).toFixed(1));
            return percentages;
        }

        /**
         * @param { EnergyResultsChartOptions } chartData
         * @param { 'Heating + Cooling + Total' | 'Heating' | 'Cooling' | 'Totals' | string } view What data to show.
         * @param { string } measurement Measurement to show in the label.
         *
         * @returns []
         */
        function createSeriesForView(chartData, view, measurement) {
            let series = [];

            if(view.includes("Heating")) {
                series.push({
                    name: `Heating (${measurement})`,
                    data: chartData.heatingData,
                    percentageData: chartData.heatingPercentageData,
                    color: COLOUR_HEATING,
                    lineWidth: 0,
                    useHTML: true
                });
            }

            if(view.includes("Cooling")) {
                series.push({
                    name: `Cooling (${measurement})`,
                    data: chartData.coolingData,
                    percentageData: chartData.coolingPercentageData,
                    color: COLOUR_COOLING,
                    lineWidth: 0,
                    useHTML: true
                });
            }

            if(view.includes("Total")) {
                series.push({
                    name: `Total (${measurement})`,
                    data: chartData.totalsData,
                    percentageData: chartData.totalsPercentageData,
                    color: COLOUR_TOTAL,
                    lineWidth: 0,
                    useHTML: true
                });
            }

            return series
        }

        // Small timeout to ensure DOM graph holders have loaded before we init.
        setTimeout(() => { initialize(vm.assessment, vm.option, vm.building); }, 150);

        vm.buildZoneEnergyUseChart = buildZoneEnergyUseChart;
        vm.buildAnnualEnergyConsumptionChart = buildBuildingEnergyUseChart;

        function updateThermalPerformanceCenterTooltip(e) {

            if(thermalPerformanceCenterTooltipElement == null || thermalPerformanceCenterElement == null)
                return;

            // The tooltop positioning after all other offsets are applied (Still scales with screen size).
            const FIXED_OFFSET_X = 10;
            const FIXED_OFFSET_Y = 40;

            const absoluteParent = thermalPerformanceCenterElement;
            const tooltip = thermalPerformanceCenterTooltipElement[0];
            // X
            const tooltipOffsetX = tooltip.offsetWidth / 2;
            const absoluteParentOffsetX = absoluteParent.offsetLeft + (absoluteParent.offsetWidth / 2);
            const cursorOffsetX = (document.body.clientWidth / 2) - e.clientX;

            // Y
            const tooltipOffsetY = tooltip.offsetHeight / 2;
            const absoluteParentOffsetY = absoluteParent.offsetTop + (absoluteParent.offsetHeight / 2);
            const cursorOffsetY = (document.body.clientHeight / 2) - e.clientY;
            // Scrolling also affects the y positioning.
            const scrollOffsetY = window.scrollY - absoluteParent.offsetTop;

            const x = (document.body.clientWidth / 2) - cursorOffsetX - absoluteParentOffsetX - tooltipOffsetX + FIXED_OFFSET_X;
            const y = (document.body.clientHeight / 2) - cursorOffsetY - absoluteParentOffsetY - tooltipOffsetY + scrollOffsetY + FIXED_OFFSET_Y;

            tooltip.style.left = x + 'px';
            tooltip.style.top  = y + 'px';
        }

    }

    /**
     * Energy usage data that has been transformed so it can be applied to a Highcharts chart.
     */
    class EnergyResultsChartTransformedData {

        // Months, days, quarters etc.
        labelData;

        // For when just 'labelData' won't do and you need fine grained control
        xAxis;

        // Data with as many elements as their are labels!
        heatingData;
        coolingData;
        totalsData;
    }

    class EnergyResultsChartOptions {

        id;
        period;
        grouping;
        view;

        constructor(id, period, grouping, view) {
            this.id = id;
            this.period = period;
            this.grouping = grouping;
            this.view = view;
        }
    }

    // A seperate class is needed for Zone Energy Use Charts as Zone Grouping is required
    // { 'Zone Name', 'Zone Activity', 'Zone Type', 'NCC Classification'}
    class ZoneEnergyResultsChartOptions {

        id;
        period;
        grouping;
        view;
        zoneGrouping

        constructor(id, period, grouping, view, zoneGrouping) {
            this.id = id;
            this.period = period;
            this.grouping = grouping;
            this.view = view;
            this.zoneGrouping = zoneGrouping;
        }
    }
})();