<form name="statusform" class="main-content-wrapper" novalidate data-ng-controller='StatusUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex">
                    <md-card>
                        <md-card-header>
                            Status
                        </md-card-header>
                        <md-card-content>

<!-- ******** Status Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Status Code</label>
                                <input type="text" name="statusCode" 
                                        ng-model="vm.status.statusCode" md-autofocus 
                                        md-maxlength="20"
                                        required
                                    />
                                <div ng-messages="statusform.statusCode.$error">
                                    <div ng-message="required">Status Code is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Description</label>
                                <input type="text" name="description" 
                                        ng-model="vm.status.description"  
                                        md-maxlength="120"
                                        required
                                    />
                                <div ng-messages="statusform.description.$error">
                                    <div ng-message="required">Description is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 120.</div>
                                </div>
                            </md-input-container>

<!-- ******** Sort Order ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Sort Order</label>
                                <input type="text" name="sortOrder" 
                                        ng-model="vm.status.sortOrder"  
                                        required
                                        only-numeric
                                    />
                                <div ng-messages="statusform.sortOrder.$error">
                                    <div ng-message="required">Sort Order is required.</div>
                                </div>
                            </md-input-container>

<!-- ******** Hidden ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <md-checkbox name="hidden"
                                        ng-model="vm.status.hidden"  >
                                    Hidden
                                </md-checkbox>
                                <div ng-messages="statusform.hidden.$error">
                                    <div ng-message="required">Hidden is required.</div>
                                </div>
                            </md-input-container>

<!-- ******** Status Type Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Status Type Code</label>
                                <input type="text" name="statusTypeCode" 
                                        ng-model="vm.status.statusTypeCode"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="statusform.statusTypeCode.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.status"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="statusform.$invalid" ng-show="vm.status.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.status.statusCode!=null && vm.status.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.status.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
