<section id="assessmentsoftware-list-view" class="main-content-wrapper" data-ng-controller="AssessmentsoftwareListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Assessmentsoftware"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.assessmentsoftwareList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="assessmentsoftwareList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="assessmentSoftwareCode" class="can-sort text-left">Code</th>
                        <th st-sort="simulationEngine" class="can-sort text-left">Simulation Engine</th>
                        <th st-sort="energyLoadUnits" class="can-sort text-left">Energy Load Units</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.assessmentsoftwareList">
                        <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="assessmentsoftware-updateform({ assessmentSoftwareCode: row.assessmentSoftwareCode})">Select</md-button>  </td>
                        <td data-title="Description" class="text-left">{{::row.description }}</td>
                        <td data-title="Code" class="text-left">{{::row.assessmentSoftwareCode }}</td>
                        <td data-title="Simulation Engine" class="text-left">{{::row.simulationEngine }}</td>
                        <td data-title="Energy Load Units" class="text-left">{{::row.energyLoadUnits }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
