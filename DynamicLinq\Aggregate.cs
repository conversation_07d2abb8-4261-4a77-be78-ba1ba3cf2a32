using System;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.Serialization;

namespace Kendo.DynamicLinq
{
	/// <summary>
	/// Represents a aggregate expression of Kendo DataSource.
	/// </summary>
	[DataContract(Name = "aggregate")]
	public class Aggregator
	{
		/// <summary>
		/// Gets or sets the name of the aggregated field (property).
		/// </summary>
		[DataMember(Name = "field")]
		public string Field { get; set; }

		/// <summary>
		/// Gets or sets the aggregate.
		/// </summary>
		[DataMember(Name = "aggregate")]
		public string Aggregate { get; set; }

        

    }
}
