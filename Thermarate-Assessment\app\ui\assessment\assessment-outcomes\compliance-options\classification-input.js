(function () {

    'use strict';
    let controllerId = 'ClassificationInputCtrl';

    angular
        .module('app')
        .component('classificationInput', {
            bindings: {
                classification: '<',    // Current classification.
                onClick: '&'            // Fired whenever a classification is clicked. Should have an int named 'value' as argument.
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/classification-input.html',
            controller: classificationInputController,
            controllerAs: 'vm'
        });

    classificationInputController.$inject = ['common'];

    function classificationInputController(common) {

        let vm = this;

        /**
         * Fired when an icon is clicked. Clicking an icon sets the value to whatever that icon is
         * UNLESS that specific icon is already selected, in which case it goes 1 level lower.
         * This is also how you get to 0.
         * 
         * @param {any} alreadySelected State of the clicked icon (true | false)
         * @param {any} value The value of the clicked icon (1-5).
         */
        vm.clicked = function (alreadySelected, value) {

            if (alreadySelected && value == vm.classification) {
                vm.classification--;
            } else if (alreadySelected && value != vm.classification) {
                vm.classification = value;
            } else {
                vm.classification = value;
            }

            vm.onClick({ value: vm.classification });

        }

    }
    // END
})();