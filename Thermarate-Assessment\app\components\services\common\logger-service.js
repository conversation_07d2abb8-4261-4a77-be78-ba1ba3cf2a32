(function () {
    'use strict';
    
    angular.module('common').factory('logger', ['$log', logger]);

    function logger($log) {
        var previousErrors = [];
        var service = {
            getLogFn: getLogFn,
            log: log,
            logError: logError,
            logSuccess: logSuccess,
            logWarning: logWarning
        };

        return service;

        function getLogFn(moduleId, fnName) {
            fnName = fnName || 'log';
            switch (fnName.toLowerCase()) { // convert aliases
                case 'success':
                    fnName = 'logSuccess'; break;
                case 'error':
                    fnName = 'logError'; break;
                case 'warn':
                    fnName = 'logWarning'; break;
                case 'warning':
                    fnName = 'logWarning'; break;
            }

            var logFn = service[fnName] || service.log;
            return function (msg, data, showToast) {
                logFn(msg, data, moduleId, (showToast === undefined) ? true : showToast);
            };
        }

        function log(message, data, source, showToast) {
            logIt(message, data, source, showToast, 'info');
        }

        function logWarning(message, data, source, showToast) {
            logIt(message, data, source, showToast, 'warning');
        }

        function logSuccess(message, data, source, showToast) {
            logIt(message, data, source, showToast, 'success');
        }

        function logSuccess(message) {
            logIt(message, "", "", true, 'success');
        }

        function logError(message, data, source, showToast) {
            var logMsg = true;
            var found = false;
            if (previousErrors != null && previousErrors.length > 0) {
                for (var i = 0, len = previousErrors.length; i < len; i++) {
                    var err = previousErrors[i];

                    if (err.message == message && err.time != null) {
                        found = true;
                        logMsg = false;
                        var d = angular.copy(err.time);
                        d.setSeconds(d.getSeconds() + 120);
                        if (d > new Date()) {
                            // Within the last 2 minutes
                            err.count++;
                            if (err.count < 4) {
                                // Log the 1st 3 times.
                                logMsg = true;
                            }
                        }
                        else {
                            logMsg = true;
                            err.count = 1;
                            err.time = new Date();
                        }
                        break;
                    }
                }
            }
            if (found == false) {
                var newErr = { message: message, time: new Date(), count: 1 };
                previousErrors.push(newErr);
            }

            if (previousErrors.length > 10) {
                // If its getting to big empty the list.
                previousErrors = [];
            }

            if (logMsg == true) {
                logIt(message, data, source, showToast, 'error');
            }
        }

        function logIt(message, data, source, showToast, toastType) {
            var write = (toastType === 'error') ? $log.error : $log.log;
            source = source ? '[' + source + '] ' : '';
            write(source, message, data);
            if (showToast) {
                if (toastType === 'error') {
                    toastr.error(message);
                } else if (toastType === 'warning') {
                    toastr.warning(message);
                } else if (toastType === 'success') {
                    toastr.success(message);
                } else {
                    toastr.info(message);
                }
            }
        }
    }
})();