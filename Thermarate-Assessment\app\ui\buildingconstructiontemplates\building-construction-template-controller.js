(function () {
    // The BuildingConstructionTemplateCtrl supports a list page.
    'use strict';
    var controllerId = 'BuildingConstructionTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',
            'buildingconstructiontemplateservice', 'constructionservice', 'security', buildingConstructionTemplateController]);
    function buildingConstructionTemplateController($rootScope, $scope, $mdDialog, $stateParams, $state,
        buildingconstructiontemplateservice, constructionservice, securityservice) {

        // The model for this form 
        var vm = this;
        vm.isBusy = true;
        vm.title = '';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.newRecord = $scope.newRecord != undefined && $scope.newRecord == true;

        vm.buildingConstructionTemplateId = $stateParams.templateId;
        vm.type = $stateParams.type;

        vm.editPermission = securityservice.immediateCheckRoles('admin__template__edit');
        vm.deletePermission = securityservice.immediateCheckRoles('admin__template__delete');

        function initialize() {

            if (vm.newRecord)
                vm.title = "New Building Construction Template";

            // Get data for object to display on page
            var buildingConstructionTemplateIdPromise = null;
            if (vm.buildingConstructionTemplateId != null) {
                buildingConstructionTemplateIdPromise = buildingconstructiontemplateservice.getTemplate(vm.buildingConstructionTemplateId)
                .then(function (data) {

                    if (data != null) {
                        vm.template = data;
                        vm.title = vm.template.templateType == "construction"
                            ? "Edit Building Construction Template"
                            : "Edit Building Openings Template";

                        vm.subheading = vm.template.templateType == "construction"
                            ? "Building Construction Elements"
                            : "Building Opening Elements";

                        constructionservice.getConstructionCategoryList()
                            .then((data) => {
                                vm.constructionCategoryList = data;

                                if (vm.template.templateType == 'construction') {

                                    vm.shownConstructionCategories = vm.constructionCategoryList
                                        .filter(x => x.type == 'surface' &&
                                            (x.constructionCategoryCode != "ExteriorDoor" &&
                                                x.constructionCategoryCode != "InteriorDoor"));

                                } if (vm.template.templateType == 'opening') {

                                    vm.shownConstructionCategories = vm.constructionCategoryList
                                        .filter(x => x.type != 'surface' ||
                                            (x.constructionCategoryCode == "ExteriorDoor" ||
                                                x.constructionCategoryCode == "InteriorDoor"));
                                }
                            });
                    }

                    vm.isBusy = false;
                });
            }
            else {
                vm.isBusy = false;
            }
        }

        initialize();

        // Cancel - Route back to last page.
        vm.cancel = function () {
            $state.go("buildingconstructiontemplate-listform");
        }

        vm.save = function () {

            vm.isBusy = true;

            if(vm.newRecord == true){

                buildingconstructiontemplateservice.createTemplate(vm.template).then(function(data){
                    vm.buildingConstructionTemplateId = vm.template.buildingConstructionTemplateId;
                    vm.isBusy = false;
                });

            } else {

                buildingconstructiontemplateservice.updateTemplate(vm.template).then(function(data){
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            buildingconstructiontemplateservice.deleteTemplate(vm.buildingConstructionTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            buildingconstructiontemplateservice.undoDeleteTemplate(vm.buildingConstructionTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }
    }
})();