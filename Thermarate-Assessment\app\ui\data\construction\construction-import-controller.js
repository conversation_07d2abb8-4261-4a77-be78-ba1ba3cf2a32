(function () {
    // The ConstructionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ConstructionImportCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', 'constructionservice', 'servicetemplateservice', '$anchorScroll', constructionImportController]);
    function constructionImportController($rootScope, $scope, $mdDialog, $stateParams, constructionservice, servicetemplateservice, $anchorScroll) {

        // The model for this form
        var vm = this;
        vm.isModal = true;
        vm.type = $scope.type;
        vm.excelData = $scope.excelData;
        vm.response = $scope.response;
        vm.extractor = $scope.extractor;
        vm.isProcessing = false;

        vm.isInitialProcess = $scope.isInitialProcess;

        console.log("Modal data is:", vm.response);
        console.log(vm.isInitialProcess);

        vm.ok = function () {
            $mdDialog.hide({force: true});
            // Not the best to force a page refresh but better than being
            // presented with a blank page...
            location.reload(); 
        }

        /** 
         * Fired when the user has chosen to import the excel template
         * even when warnings are present.
         */
        vm.forceImport = function () {

            $anchorScroll("top-of-dialog");
            vm.isProcessing = true;

            setTimeout(() => {

                if (vm.extractor === 'construction' || vm.extractor === 'opening')
                    constructionservice.uploadConstructionDatabase(vm.type, vm.excelData, true)
                        .then(showFinalReport)
                        .catch(r => { vm.isProcessing = false; });
                else if (vm.extractor === 'services')
                    servicetemplateservice.uploadTemplateDatabase(vm.excelData, true)
                        .then(showFinalReport)
                        .catch(r => { vm.isProcessing = false; });

            }, 250);

            function showFinalReport(data) {

                $mdDialog.hide();

                console.log("New reponse: ", data);
                vm.response = data;
                vm.isProcessing = false;

                var modalScope = $rootScope.$new();
                modalScope.excelData = vm.excelData;
                modalScope.response = data;
                modalScope.type = vm.type;
                modalScope.isInitialProcess = false;
                modalScope.extractor = 'construction';

                modalScope.modalInstance = $mdDialog.show({
                    templateUrl: 'app/ui/data/construction/construction-import.html',
                    scope: modalScope,
                });

            }

        }
    }
})();