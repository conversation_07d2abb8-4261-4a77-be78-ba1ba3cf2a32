﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('httpPrefix', function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            link: function (scope, element, attrs, ngModelCtrl) {
                function ensureHttpPrefix(value) {
                    // Need to add prefix if we don't have http:// prefix already AND we don't have part of it
                    if (value && !/^(http):\/\//i.test(value)
                       && 'http://'.indexOf(value) === -1) {
                        ngModelCtrl.$setViewValue('http://' + value);
                        ngModelCtrl.$render();
                        return 'http://' + value;
                    }
                    else
                        return value;
                }
                ngModelCtrl.$parsers.push(ensureHttpPrefix);
                ngModelCtrl.$formatters.push(ensureHttpPrefix);

                element.bind("keyup", function () {
                    var value = element.val();
                    var RegExp = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;
                    var isValid = !!value;
                    if (RegExp.test(element.val())) {
                        isValid = true;
                        ngModelCtrl.$setViewValue(value);
                        ngModelCtrl.$render();
                    }
                    else {
                        isValid = false;
                    }
                    if (element.val() == "") {
                        isValid = true;
                    }
                    ngModelCtrl.$setValidity("validateUrlField", isValid);
                    //call scope.$apply() since blur event happens "outside of angular"
                    scope.$apply();
                });
            }
        };
    });
})();