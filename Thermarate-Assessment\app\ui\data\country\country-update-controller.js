(function () {
    // The CountryUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'CountryUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'countryservice', countryUpdateController]);
function countryUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  countryservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Country';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.countryCode = null;
        vm.country = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Country";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.countryCode = $scope.countryCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.countryCode = $stateParams.countryCode;
        }

        // Get data for object to display on page
        var countryCodePromise = null;
        if (vm.countryCode != null) {
            countryCodePromise = countryservice.getCountry(vm.countryCode)
            .then(function (data) {
                if (data != null) {
                    vm.country = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("country-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                countryservice.createCountry(vm.country).then(function(data){
                    vm.country = data;
                    vm.countryCode = vm.country.countryCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                countryservice.updateCountry(vm.country).then(function(data){
                    if (data != null) {
                        vm.country = data;
                        vm.countryCode = vm.country.countryCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            countryservice.deleteCountry(vm.countryCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            countryservice.undoDeleteCountry(vm.countryCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();