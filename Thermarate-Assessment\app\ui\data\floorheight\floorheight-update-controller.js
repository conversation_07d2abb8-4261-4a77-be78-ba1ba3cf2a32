(function () {
    // The AdjacentFloorHeightUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'FloorHeightUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', 'systemparametersservice', 'manufacturerservice', 'uuid4', 'security', floorHeightUpdateController]);
    function floorHeightUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, systemparametersservice, manufacturerservice, uuid4, securityservice) {

        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Edit Floor Height';
        vm.editPermission = securityservice.immediateCheckRoles('settings__edit');

        vm.floorHeightParam = null;

        function initialise() {
            systemparametersservice.getSystemParameters("FloorHeightTolerance").then(() => {
                vm.floorHeightParam = systemparametersservice.systemparameters();
                vm.isBusy = false;
            });
        }
        initialise();

        vm.save = function () {
            vm.isBusy = true;
            systemparametersservice.saveSystemParameters(vm.floorHeightParam).then(() => vm.isBusy = false);
        }

    }
})();