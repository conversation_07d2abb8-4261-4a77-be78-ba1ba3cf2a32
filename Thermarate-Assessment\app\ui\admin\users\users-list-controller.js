// *****************************************************************************
// DO NOT CONFUSE THIS WITH user-list-controller.js !!!!
// *****************************************************************************
(function () {
    // The UsersListCtrl provides the behaviour for Users information.
    'use strict';
    var controllerId = 'UsersListCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'aspnetusersservice', usersListController]);
    function usersListController($rootScope, $scope, $mdDialog, aspnetusersservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = false;
        vm.title = "Users List";
        vm.usersList = [];
        vm.allRoles = [];
        vm.listFilter = "";
        vm.textSearch = "";
        vm.actionButtons = [];
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.filterOptions = [{ code: 'All', name: 'All' }, { code: 'Deleted', name: 'Deleted' }
        ];
        vm.currentFilter = "All";
        vm.currentQuery = {};
        vm.filterByCode = 'AllUsers';

        vm.filterColumns = null;
        // vm.filterLocalName = "UsersListCtrl-columnFilter"

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.textSearch = filter;
            vm.callServer(null);
        };

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "userName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }

            aspnetusersservice.getListCancel();
            aspnetusersservice.getList(vm.textSearch, pageSize, pageIndex, sort, filter, null, vm.filterByCode)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = aspnetusersservice.currentFilter();
                    vm.usersList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.usersList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                    function (error) {
                        vm.isBusy = false;
                    });
        };

        /* Show Modal for a New Users */
        vm.newRecord = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            var modalOptions = {
                templateUrl: 'app/ui/admin/users/users-detail.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                // Cancelled.
                vm.refreshList(null);
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        vm.filterOption = function (code) {
            vm.filterByCode = code;
            vm.refreshList(vm.textSearch);
        }

        function setFilterActions() {
            vm.typeOptions = [];
            vm.typeOptions.push({
                onclick: vm.filterOption,
                name: 'all users',
                code: 'AllUsers',
                visible: true,
            });
            vm.typeOptions.push({
                onclick: vm.filterOption,
                name: 'none-client users',
                code: 'NoneClientUsers',
                visible: true,
            });
            vm.typeOptions.push({
                onclick: vm.filterOption,
                name: 'client users',
                code: 'ClientUsers',
                visible: true,
            });
        }

        setFilterActions();

    }
})();
