<form name="BulkEditDesignInsightModal"
      data-ng-controller='BulkEditDesignInsightModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row"
                        ng-model="vm.data.bulkEditAction">
            <md-radio-button ng-value="'COPYTOMODEL'">
                Copy To Home Design Variation
            </md-radio-button>
            <md-radio-button ng-value="'EDIT'">
                Edit
            </md-radio-button>
            <md-radio-button ng-value="'COPY'">
                Duplicate
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <div ng-if="vm.data.bulkEditAction == 'COPYTOMODEL'"
             style="text-align: center;">

            <div class="content-container">
                <div class="select-text">
                    Select a destination
                </div>
                <standard-model-variation-selector-list
                    variation-of-id="vm.variationOfId"
                    exclude-variation-id="vm.thisHomeModelId"
                    variation-options-list="vm.variationOptionsList"
                    variation-options-settings="vm.variationOptionsSettings"
                    selected-variation-id="vm.data.selectedVariationId">
                </standard-model-variation-selector-list>
            </div>

        </div>

        <fieldset id="edit-inputs"
                  ng-if="vm.data.bulkEditAction == 'EDIT'">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Climate Zone -->
                    <tr>
                        <td>
                            Climate Zone
                        </td>
                        <td>
                            <md-input-container class="md-block el-design-insight kindly-remove-error-spacer vertically-condensed-ex">
                                <md-select class="kindly-remove-error-spacer"
                                           md-container-class="md-select-show-all"
                                           ng-required="false"
                                           multiple="true"
                                           ng-disabled="vm.data.northOffsets != null"
                                           ng-model="vm.data.climateZones">
                                    <md-option
                                        ng-repeat="option in vm.options['climateZone'] track by $index"
                                        ng-value="option">
                                        {{option}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- North Offset -->
                    <tr >
                        <td>
                            North Offset
                        </td>
                        <td>
                            <md-input-container class="md-block el-design-insight kindly-remove-error-spacer vertically-condensed-ex">
                                <md-select class="kindly-remove-error-spacer"
                                           md-container-class="md-select-show-all"
                                           ng-required="false"
                                           ng-disabled="vm.data.climateZones != null"
                                           multiple="true"
                                           ng-model="vm.data.northOffsets">
                                    <md-option
                                        ng-repeat="option in vm.options['northOffset'] track by $index"
                                        ng-value="option">
                                        {{option}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Design Insight -->
                    <tr>
                        <td>
                            Design Insight
                        </td>
                        <td>
                            <textarea ng-model="vm.data.notes"
                                      auto-grow
                                      class="el-design-insights"></textarea>
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Design Insights will be duplicated. Only the 'notes' data is retained on the duplicates.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Design Insights will be deleted. </span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-disabled="vm.data.bulkEditAction == 'COPYTOMODEL' && vm.data.selectedVariationId == null"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>

    .select-text {
        font-size: 14px;
        text-align: left;
        margin-bottom: 20px;
    }

</style>