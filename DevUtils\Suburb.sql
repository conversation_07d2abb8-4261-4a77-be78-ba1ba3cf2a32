USE [thermarate];

SELECT [suburb].[SuburbCode]
      ,[suburb].[Name]
      ,[suburb].[StateCode]
      ,[suburb].[Postcode]
      ,[suburb].[CreatedOn]
      ,[suburb].[CreatedByName]
      ,[suburb].[ModifiedOn]
      ,[suburb].[ModifiedByName]
      ,[suburb].[Deleted]
      ,[suburb].[LocalGovernmentArea]
      ,[suburb].[LocalityId]
      ,[suburb].[Latitude]
      ,[suburb].[Longitude]
      ,[suburb].[BoundaryJson]
  FROM [dbo].[RSS_Suburb] [suburb]
  --INNER JOIN [dbo].[RSS_NatHERSClimateZonePostcode] [climateZonePostcode]
  WHERE 1=1
	--AND [StateCode] != 'WA'
  ORDER BY [Postcode]