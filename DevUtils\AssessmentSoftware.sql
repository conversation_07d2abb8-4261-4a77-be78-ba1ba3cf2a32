USE [thermarate];

SELECT [software].[AssessmentSoftwareCode]
      ,[software].[Description]
      ,[software].[CreatedOn]
      ,[software].[CreatedByName]
      ,[software].[ModifiedOn]
      ,[software].[ModifiedByName]
      ,[software].[Deleted]
  FROM [dbo].[RSS_AssessmentSoftware] [software]
  WHERE 1=1
    --AND [software].[Deleted] = 0
    --AND [software].[AssessmentSoftwareCode] = 'SoftwareCode'
  ORDER BY [software].[Description]

-- Update assessment software
-- UPDATE [dbo].[RSS_AssessmentSoftware]
-- SET [Description] = 'New Description'
-- WHERE [AssessmentSoftwareCode] = 'SoftwareCode'

-- Add new assessment software
-- INSERT INTO [dbo].[RSS_AssessmentSoftware]
-- ([AssessmentSoftwareCode], [Description], [CreatedOn], [CreatedByName], [Deleted])
-- VALUES
-- ('NewCode', 'New Assessment Software', GETDATE(), 'System', 0)
