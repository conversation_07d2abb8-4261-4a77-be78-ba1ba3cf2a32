/*
* Localized Messages
* ------------------
*
* Provides a function to return the requested message text.
* Can be expanded to supported messages for different languages.
*
*/
(function () {
    'use strict';

    var serviceId = 'localizedMessages';
    angular.module('services.localizedMessages', []).factory(serviceId, ['$interpolate', 'I18N.MESSAGES', function ($interpolate, i18nmessages) {

        var handleNotFound = function (msg, msgKey) {
            return msg || '?' + msgKey + '?';
        };

        return {
            get: function (msgKey, interpolateParams) {
                var msg = i18nmessages[msgKey];
                if (msg) {
                    return $interpolate(msg)(interpolateParams);
                } else {
                    return handleNotFound(msg, msgKey);
                }
            }
        };
    }]);
})();