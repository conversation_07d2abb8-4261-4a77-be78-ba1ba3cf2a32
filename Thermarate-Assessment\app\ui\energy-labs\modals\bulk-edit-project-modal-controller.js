(function () {
    'use strict';
    var controllerId = 'BulkEditProjectModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', bulkEditStandardModelModalController]);
    function bulkEditStandardModelModalController($scope, $mdDialog) {

        let vm = this;

        // We don't have direct access to the selected projects here
        // The default value will be set by the parent controller when opening the modal
        // For now, default to true
        let allInactive = false;

        // If the parent passed selectedProjects data, use it
        if ($scope.selectedProjects) {
            allInactive = $scope.selectedProjects.length > 0 &&
                         $scope.selectedProjects.every(p => !p.isActive);
        }

        vm.data = {
            bulkEditAction: "EDIT",
            isActive: !allInactive, // Default to false if all selected projects are inactive
            toggleChildrenIsActive: true, // Default to apply to all levels
            costEstimateEnabled: true,
            designInsightsEnabled: true
        };

        // Handle isActive toggle change
        vm.handleIsActiveChange = function() {
            // If toggling OFF, set flag to apply to all levels
            // Backend will handle turning off child values
            if (vm.data.isActive === false) {
                vm.data.toggleChildrenIsActive = true;
            }
            // If toggling ON, default to apply to all levels
            // User can change this in the radio buttons
        };

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();