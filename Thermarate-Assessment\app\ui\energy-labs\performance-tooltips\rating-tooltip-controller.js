(function () {

  'use strict';
  angular
    .module('app')
    .component('elRatingTooltip', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
        targetEnergyRating: '<',
      },
      templateUrl: 'app/ui/energy-labs/performance-tooltips/rating-tooltip.html',
      controller: ElRatingTooltipController,
      controllerAs: 'vm'
    });

  ElRatingTooltipController.$inject = ['common'];

  function ElRatingTooltipController(common) {

    let vm = this;

    vm.lessThan = common.lessThanOrEqualish;
  }

})();