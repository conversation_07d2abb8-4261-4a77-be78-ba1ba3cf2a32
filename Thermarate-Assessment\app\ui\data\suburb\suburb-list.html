<section id="suburb-list-view" class="main-content-wrapper" data-ng-controller="SuburbListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-primary upload-panel" ngf-drop="vm.uploadFile($file)" ngf-drag-over-class="'panel-success'">
                    <div class="panel-heading">
                        Drag and drop a file for uploading and processing.
                    </div>
                    <div class="panel-body">

                        <!--Uploading info-->
                        <div class="col-md-12 upload-data-section">
                            <div class="col-md-12 page-title">
                                Upload Status: {{vm.statusText}}
                                <br />
                                <br />
                            </div>
                            <div class="col-md-5">
                                <md-progress-linear md-mode="determinate" value="{{vm.progress}}"></md-progress-linear>
                                {{vm.progressString}}
                            </div>
                        </div>
                    </div>
                </div>

                <div data-cc-widget-button-bar
                        data-is-modal="vm.isModal">
                    <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                    <md-button class="md-raised md-primary" ngf-select="vm.uploadFile($file)"><i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>Select File</md-button>
                    <div class="clearfix"></div>
                </div>
            </div>
            <br />
        </div>

        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Suburb"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.suburbList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="suburbList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="name" class="can-sort text-left">Name</th>
                        <th st-sort="stateName" class="can-sort text-left">State</th>
                        <th st-sort="postcode" class="can-sort text-left">Postcode</th>
                        <th st-sort="latitude" class="can-sort">Latitude</th>
                        <th st-sort="longitude" class="can-sort">Longitude</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.suburbList">
                        <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="suburb-updateform({ suburbCode: row.suburbCode})">Select</md-button>  </td>
                        <td data-title="Name" class="text-left">{{::row.name }}</td>
                        <td data-title="State" class="text-left">{{::row.stateName }}</td>
                        <td data-title="Postcode" class="text-left">{{::row.postcode }}</td>
                        <td data-title="Latitude">{{::row.latitude}}</td>
                        <td data-title="Longitude">{{::row.longitude}}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
