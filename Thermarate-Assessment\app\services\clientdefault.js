﻿// Name: clientdefaultservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'clientdefaultservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', clientdefaultservice]);

    function clientdefaultservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'clientDefault/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getClientDefault: getClientDefault,
            createClientDefault: createClientDefault,
            updateClientDefault: updateClientDefault,
            deleteClientDefault: deleteClientDefault,
            undoDeleteClientDefault:undoDeleteClientDefault,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp && resp.data) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status === 0 || error.status === -1) {
                    return;
                }
                var msg = "Error getting Client Defaults list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller) {
                canceller.resolve();
            }
        }
        
        function getClientDefault(clientId) {
            return $http({
                url: baseUrl + 'Get',
                params: {clientId: clientId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp && resp.data) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Client Default: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createClientDefault(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Client Default Created");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error creating Client Default: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateClientDefault(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Client Default Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Client Default: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteClientDefault(clientDefaultsId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { clientDefaultsId: clientDefaultsId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Client Default: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteClientDefault(clientDefaultsId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { clientDefaultsId: clientDefaultsId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Client Default: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
