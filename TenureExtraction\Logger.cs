﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TenureExtraction
{
    /// <summary>
    /// Callback delegate that Callers can implemented tolog  messages generated during the download or extraction process.
    /// </summary>
    public delegate void LogCallback(string message);

    public class Logger
    {
        private LogCallback callback;

        public Logger(LogCallback callback)
        {
            this.callback = callback;
        }

        /// <summary>
        /// Attempts to log an error message using a callback provided by the caller.
        /// </summary>
        internal void Log(string message)
        {
            if(callback != null)
                callback(message);
        }
    }
}
