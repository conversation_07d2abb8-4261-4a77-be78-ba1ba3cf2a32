<md-dialog ng-controller="NewClientContactCtrl as vm"
           style="min-width: 680px; width: 680px;">
    <form name="newclientform">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>New Client</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="row" layout-padding layout-wrap>
            <md-card flex="100">
                <md-card-header>
                    Client
                </md-card-header>
                <md-card-content>
                    <div layout-xs="row" layout-wrap>
                        <!-- ******** Client Name ******** -->
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Client Name</label>
                            <input type="text"
                                   ng-model="vm.response.clientName"
                                   md-maxlength="100"
                                   required />
                        </md-input-container>
                    </div>
                </md-card-content>
            </md-card>

            <!-- External User Card -->
            <md-card flex="100">
                <md-card-header>
                    External User
                </md-card-header>
                <md-card-content>
                    <new-user-form user="vm.response.user"
                                   user-type-restriction="'EXTERNAL'"
                                   hide-client-select="true">
                    </new-user-form>
                </md-card-content>
            </md-card>

            <!-- Accounts Contact Card -->
            <md-card flex="100">
                <md-card-header>
                    Accounts Contact
                    <span flex></span>
                    <md-checkbox ng-model="vm.response.accountsSameAsContact"
                                 ng-change="vm.accountsMatchContactChange()">
                        Same as contact
                    </md-checkbox>
                </md-card-header>
                <md-card-content>
                    <div layout-xs="row" layout-wrap>
                        <!-- ******** Accounts First Name ******** -->
                        <md-input-container class="md-block vertically-condensed"
                                            flex="100"
                                            ng-show="!vm.response.accountsSameAsContact">
                            <label>First Name</label>
                            <input type="text"
                                   ng-model="vm.response.accountsFirstName"
                                   md-maxlength="400" />
                        </md-input-container>

                        <!-- ******** Accounts Last Name ******** -->
                        <md-input-container class="md-block vertically-condensed"
                                            flex="100"
                                            ng-show="!vm.response.accountsSameAsContact">
                            <label>Last Name</label>
                            <input type="text"
                                   ng-model="vm.response.accountsLastName"
                                   md-maxlength="400" />
                        </md-input-container>

                        <!-- ******** Accounts Phone ******** -->
                        <md-input-container class="md-block vertically-condensed"
                                            flex="100"
                                            ng-show="!vm.response.accountsSameAsContact">
                            <label>Phone</label>
                            <input type="text"
                                   ng-model="vm.response.accountsPhone"
                                   md-maxlength="20"
                                   ui-mask="(99) 9999 9999"
                                   placeholder="(00) 0000 0000"
                                   model-view-value="true" />
                        </md-input-container>

                        <!-- ******** Accounts Email Address ******** -->
                        <md-input-container class="md-block vertically-condensed"
                                            flex="100"
                                            ng-show="!vm.response.accountsSameAsContact">
                            <label>Email Address</label>
                            <input type="text"
                                   ng-model="vm.response.accountsEmailAddress"
                                   md-maxlength="400" />
                        </md-input-container>

                        <!-- ******** Accounts Note ******** -->
                        <md-input-container class="md-block vertically-condensed"
                                            flex="100">
                            <label>Notes</label>
                            <textarea ng-model="vm.response.accountsNote"
                                      md-maxlength="2000"
                                      rows="5" />
                        </md-input-container>
                    </div>
                </md-card-content>
            </md-card>

            <!-- External User Card -->
            <md-card flex="100">
                <md-card-header>
                    Purchase Order Settings
                </md-card-header>
                <md-card-content>
                    <!-- ******** Purchase Order ******** -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Purchase Order</label>
                        <md-select name="purchaseorder"
                                    ng-disabled="vm.editPermission == false"
                                    ng-model="vm.response.purchaseOrderCode">
                            <md-option ng-repeat="item in vm.purchaseOrderList track by item.purchaseOrderCode"
                                        ng-value="item.purchaseOrderCode">
                                {{item.description}}
                            </md-option>
                        </md-select>
                    </md-input-container>
                </md-card-content>
            </md-card>

            <!-- Client Options Card-->
            <md-card flex="100"
                     style="max-width: 100%; overflow:hidden;">
                <md-card-header>
                    Client Settings
                </md-card-header>
                <md-card-content>
                    <client-options-form options="vm.client.clientOptions"
                                         defaults="vm.client.clientDefault"
                                         option-data="vm.clientOptionsData"
                                         form-style="'CONDENSED'">
                    </client-options-form>
                </md-card-content>
            </md-card>

            <!-- Client Portal Options Card-->
            <md-card flex="100"
                     style="max-width: 100%; overflow:hidden;">
                <md-card-header>
                    Client Portal Settings
                </md-card-header>
                <md-card-content>
                    <client-portal-options-form options="vm.client.clientOptions"
                                         defaults="vm.client.clientDefault"
                                         option-data="vm.clientOptionsData"
                                         form-style="'CONDENSED'">
                    </client-portal-options-form>
                </md-card-content>
            </md-card>

        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.submitSelection()"
                       ng-disabled="newclientform.$invalid"
                       class="md-raised md-primary">
                Ok
            </md-button>
            <md-button ng-click="vm.cancel()"
                       style="margin-right: auto;"
                       class="md-raised">
                Cancel
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>