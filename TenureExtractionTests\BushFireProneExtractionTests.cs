﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;

namespace ExtractionTests
{
    [TestClass]
    public class BushFireProneExtractionTests
    {



        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public void TestFullProcess()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";

            // Process the Dataset into our DB.
            TenureExtraction.BushFireProneExtractor ex = new TenureExtraction.BushFireProneExtractor(
                directory,
                "TODO",
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            ex.RunFullProcess();
            ex.Dispose();
        }
    }
}
