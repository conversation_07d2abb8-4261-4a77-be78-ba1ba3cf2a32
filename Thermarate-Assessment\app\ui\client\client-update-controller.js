(function () {
    // The ClientUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ClientUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['common', '$rootScope', '$scope', '$q', '$mdDialog', '$stateParams',
            '$state', '$timeout', 'clientservice', 'bootstrap.dialog', 'uuid4',
            'daterangehelper', 'jobservice', 'projectdescriptionservice',
            'compliancemethodservice', 'assessmentsoftwareservice',
            'priorityservice', 'buildingexposureservice', 'bushfireattacklevelservice',
            'userservice', 'clientdefaultservice', 'stateservice',
            'nccclimatezoneservice', 'bootstrap.dialog', 'zoneservice',
            'nominatedbuildingsurveyorservice', 'worksdescriptionservice', 'assessmentcomplianceoptionservice',
            'buildingconstructiontemplateservice', 'buildingdesigntemplateservice', 'certificationservice',
            'buildingservicestemplateservice', 'security', 'servicetemplateservice', 'wholeofhomeservice', 'standardmodelservice', 'projectservice',
            clientUpdateController]);
    function clientUpdateController(common, $rootScope, $scope, $q, $mdDialog, $stateParams,
        $state, $timeout, clientservice, modalDialog, uuid4,
        daterangehelper, jobservice, projectdescriptionservice,
        compliancemethodservice, assessmentsoftwareservice,
        priorityservice, buildingexposureservice, bushfireattacklevelservice,
        userservice, clientdefaultservice,  stateservice,
        nccclimatezoneservice, bootstrapDialog, zoneservice,
        nominatedbuildingsurveyorservice, worksdescriptionservice, assessmentcomplianceoptionservice,
        buildingconstructiontemplateservice, buildingdesigntemplateservice, certificationservice,
        buildingservicestemplateservice, securityservice, servicetemplateservice, wholeofhomeservice, standardmodelservice, projectservice) {

        // The model for this form
        var vm = this;
        vm.isBusy = true;
        vm.title = 'Edit Client';
        vm.previousRoute = $rootScope.previousState;
        vm.clientId = $stateParams.clientId;
        vm.selectedTab = $stateParams.tabIndex ?? 0;

        vm.editPermission = securityservice.immediateCheckRoles('admin__client__edit');
        vm.deletePermission = securityservice.immediateCheckRoles('admin__client__delete');

        vm.constants = WholeOfHomeConstants;

        // Global construction template list.
        vm.buildingConstructionTemplates = [];
        buildingconstructiontemplateservice
            .getAll('construction')
            .then((list) => vm.buildingConstructionTemplates = list);

        vm.buildingOpeningTemplates = [];
        buildingconstructiontemplateservice
            .getAll('opening')
            .then((list) => vm.buildingOpeningTemplates = list);

        vm.buildingServicesTemplates = [];
        buildingservicestemplateservice
            .getAll()
            .then((list) => vm.buildingServicesTemplates = list);

        vm.buildingDesignTemplates = [];
        let designTemplatePromise = buildingdesigntemplateservice
            .getAll("DESIGN")
            .then((list) => vm.buildingDesignTemplates = list);

        servicetemplateservice.getServiceTypes().then(data => {
            vm.serviceTypes = data;
            vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                'title',
                vm.serviceTypes
            );
        });

        vm.allAssessors = [];
        userservice.getList(null, null, null, null, null, null,
          [{ field: "isExternal", operator: "eq", value: false, valueType: "boolean"}])
          .then(data => {
              vm.allAssessors = data.data;
              setAssessorFilterOptions(vm.allAssessors, null);
          });

        vm.purchaseOrderList = clientservice.purchaseOrderSettingsList;

        vm.client = {
            clientDefault: {},
            clientOptions: {},
        };

        vm.resetChangeDetection = function () {
            vm.clientOriginal = angular.copy(vm.client);
            vm.projectListOriginal = angular.copy(vm.projectList);
        }

        vm.hasChanges = function () {
            // Check if client data has changed
            const clientChanged = angular.toJson(vm.client) != angular.toJson(vm.clientOriginal);

            // Check if project list has changed
            let projectsChanged = false;

            // If projects list lengths are different, something has changed
            if (vm.projectList.length !== vm.projectListOriginal.length) {
                projectsChanged = true;
            } else {
                // Check each project for changes
                for (let i = 0; i < vm.projectList.length; i++) {
                    const currentProject = vm.projectList[i];
                    const originalProject = vm.projectListOriginal[i];

                    // Check if isActive or sortOrder has changed
                    if (currentProject.isActive !== originalProject.isActive ||
                        currentProject.sortOrder !== originalProject.sortOrder ||
                        currentProject.pendingActiveChange === true) {
                        projectsChanged = true;
                        break;
                    }
                }
            }

            return clientChanged || projectsChanged;
        }

        vm.title = 'Client';
        vm.clientJobsList = [];
        vm.listFilter = "";
        vm.filterButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.filterByCode = "All";
        vm.statusCode = "All";
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'contactId',
                    description: 'Contact',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'clientJobNumber',
                    description: 'Client Job Number',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'projectOwner',
                    description: 'Project Owner',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'orderDate',
                    description: 'Created',
                    dataType: 'date',
                    operators: []
                },
                {
                    name: 'complianceMethodCode',
                    description: 'Baseline Assessment Method',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'assessmentVersion',
                    description: 'Assessment Version',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'jobReference',
                    description: 'Job Reference',
                    dataType: 'string',
                    operators: []
                },

                {
                    name: 'nominatedBuildingSurveyor',
                    description: 'Nominated Building Surveyor',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'assessorUserId',
                    description: 'Assigned To Assessor',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'statusCode',
                    description: 'Status',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        vm.flattenedCostData = [];

        vm.costItemsBulkStatus = new MasterBulkStatus();
        vm.costItemsBulkStatus.checkboxId = "sm-cost-items-bulk-checkbox";
        vm.costItemsBulkStatus.isIndeterminate = false;
        vm.costItemsBulkStatus.selectAllCheckboxState = false;

        vm.projectsFromProjectsList = [];
        vm.projectList = [];
        vm.projectListOriginal = [];

        var persistRangeName = "clientJobList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        vm.refreshList = function (filter) {
            vm.callServer(null, null, filter);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        }

        vm.refreshContacts = function () {
            vm.isBusy = true;
            var contactpromise = userservice.getList(null, null, null, null, null, null, null, null, vm.clientId)
                .then(function (data) {
                    if (data != null) {
                        vm.clientUsers = data.data;
                        vm.setDefaultContact();
                        vm.resetChangeDetection();
                    }
                    vm.isBusy = false;
                });

            //Ensure to default select the first contact if one exists and one is not selected previously
            contactpromise.then(function () {
                if (vm.clientUsers.length > 0) {
                    var contact = _.findWhere(vm.clientUsers, { checked: true });
                    if (!contact) {
                        vm.clientUsers[0].checked = true;
                        vm.client.defaultClientAssigneeUserId = vm.clientUsers[0].userId;
                        vm.defaultClientAssigneeUserId = vm.clientUsers[0].userId;
                        vm.resetChangeDetection();
                    }
                }
            });
        }

        // Load client data
        var clientPromise = clientservice.getClient(vm.clientId)
            .then(function (data) {
                vm.client = data;
                vm.subtitle = vm.client.clientName;
                vm.floorsWithGlazing = vm.client.clientDefault.storeys?.length;
                vm.tempMinHouseEnergyRating = vm.client.clientDefault.minHouseEnergyRating?.toFixed(1);
                vm.updateAvailableSoftware();
                return data;
            });

        // Function to refresh the project list
        vm.refreshProjectList = function() {
            vm.isBusy = true;
            return projectservice.getForClient(vm.clientId, null, null, null, null, null, false)
                .then(function (result) {
                    if (result != null) {
                        vm.projectList = result.data;
                        vm.projectListOriginal = angular.copy(vm.projectList);
                        vm.projectsFromProjectsList = vm.projectList;

                        // Format numbers for display
                        vm.projectList.forEach(project => {
                            project.lots = Number(project.lots).toLocaleString();
                            project.lotArea = Number(project.lotArea).toLocaleString();
                        });

                        // Update the project table state
                        vm.projectCallServer(null);
                    }
                    vm.isBusy = false;
                    return result;
                });
        };

        // Project list functionality (integrated from project-list-admin)
        vm.projectBulkStatus = new MasterBulkStatus();
        vm.projectBulkStatus.checkboxId = "sm-allCheckbox";
        vm.projectBulkStatus.isIndeterminate = false;
        vm.projectBulkStatus.selectAllCheckboxState = false;

        vm.projectTotalRecords = 0;
        vm.projectShowingFromCnt = 0;
        vm.projectShowingToCnt = 0;
        vm.projectSaveTableState = null;

        // Call server function for project list
        vm.projectCallServer = function(tableState) {
            vm.isBusy = true;

            if (tableState != null)
                vm.projectSaveTableState = tableState;

            if (vm.projectSaveTableState == null) {
                vm.projectSaveTableState = {
                    pagination: {
                        start: 0,
                        number: 1000,
                        numberOfPages: 1
                    }
                };
            }

            var pagination = vm.projectSaveTableState.pagination;
            var start = pagination.start || 0;
            var pageSize = pagination.number || 1000;

            // Save sort field to vm so know whether to show "Move Down" and "Move Up"
            if (vm.projectSaveTableState.sort != null) {
                vm.projectSortField = vm.projectSaveTableState.sort.predicate;
            }

            // Update pagination info
            if (vm.projectList) {
                vm.projectTotalRecords = vm.projectList.length;
                vm.projectSaveTableState.pagination.numberOfPages = Math.ceil(vm.projectList.length / pageSize);
                vm.projectShowingFromCnt = vm.projectList.length > 0 ? 1 : 0;
                vm.projectShowingToCnt = vm.projectList.length;
            }

            vm.isBusy = false;
        };

        // Create new project
        vm.createProject = function() {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.clientId = vm.clientId;
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/project/project-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function() {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function(data) {
                // Project was created successfully, refresh the list from the backend
                if (data) {
                    // Refresh the project list from the backend
                    vm.refreshProjectList();
                }
            }, function() {
                // Cancelled, no need to refresh
            })['finally'](function() {
                modalScope.modalInstance = undefined;
            });
        };

        // Move project up in the list
        vm.projectMoveUp = function(project) {
            // Find project before
            let projectBefore = vm.projectList.sort((a,b) => a.sortOrder < b.sortOrder ? 1 : -1).find(p => p.sortOrder < project.sortOrder);
            if (projectBefore == null) {
                project.sortOrder = 1;
            } else {
                // Swap sort orders
                let tempSortOrder = projectBefore.sortOrder;
                projectBefore.sortOrder = project.sortOrder;
                project.sortOrder = tempSortOrder;
            }
            // Sort new list
            vm.projectList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);

            // Mark projects as changed
            project.pendingActiveChange = true;
            if (projectBefore) projectBefore.pendingActiveChange = true;
        };

        // Move project down in the list
        vm.projectMoveDown = function(project) {
            // Find option after
            let projectAfter = vm.projectList.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1).find(p => p.sortOrder > project.sortOrder);
            if (projectAfter == null) {
                project.sortOrder = 1;
            } else {
                // Swap sort orders
                let tempSortOrder = projectAfter.sortOrder;
                projectAfter.sortOrder = project.sortOrder;
                project.sortOrder = tempSortOrder;
            }
            // Sort new list
            vm.projectList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);

            // Mark projects as changed
            project.pendingActiveChange = true;
            if (projectAfter) projectAfter.pendingActiveChange = true;
        };

        // Delete a project
        vm.projectDelete = async function(row, override) {
            if (!override) {
                let modalScope = $rootScope.$new();
                modalScope.confirmationHeader = "Confirm Delete";
                modalScope.confirmationText = "Are you sure you want to delete this project?";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(async function(confirmed) {
                    if (confirmed) {
                        await vm.projectDeleteImpl(row);
                    }
                });
            } else {
                await vm.projectDeleteImpl(row);
            }
        };

        // Implementation of project delete
        vm.projectDeleteImpl = async function(row) {
            await projectservice.deleteProject(row.projectId);

            row.deleted = true;
            vm.projectList = vm.projectList.filter(x => x.projectId !== row.projectId);

            vm.projectTotalRecords = vm.projectList.length;
            if (vm.projectSaveTableState && vm.projectSaveTableState.pagination) {
                const pageSize = vm.projectSaveTableState.pagination.number || 1000;
                vm.projectSaveTableState.pagination.numberOfPages = Math.ceil(vm.projectList.length / pageSize);
            }
            vm.projectShowingFromCnt = vm.projectList.length > 0 ? 1 : 0;
            vm.projectShowingToCnt = vm.projectList.length;
        };

        // Clone a project
        vm.projectClone = function(row) {
            projectservice.copyProject(row.projectId).then(() => vm.refreshProjectList());
        };

        // Select all checkboxes
        vm.projectSelectAllCheckboxes = (a, b, c) => {
            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                vm.projectSafeApply();
            }, 25);
        };

        // Safe apply for Angular digest cycle
        vm.projectSafeApply = function() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        };

        // Update bulk select status
        vm.projectUpdateBulkSelectStatus = function(list, bulkStatus) {
            updateBulkSelectStatus(list, bulkStatus);
        };

        // Bulk copy projects
        vm.projectBulkCopyProjects = function() {
            const bulkSelected = vm.projectList.filter(x => x.checkboxSelected);
            bulkSelected.forEach(x => {
                vm.projectClone(x);
                x.checkboxSelected = false;
            });
            vm.projectBulkStatus.isIndeterminate = false;
            vm.projectBulkStatus.selectAllCheckboxState = false;
        };

        // Bulk delete projects
        vm.projectBulkDeleteProjects = function() {
            const bulkSelected = vm.projectList.filter(x => x.checkboxSelected);
            bulkSelected.forEach(x => {
                vm.projectDelete(x, true);
                x.checkboxSelected = false;
            });
            vm.projectBulkStatus.isIndeterminate = false;
            vm.projectBulkStatus.selectAllCheckboxState = false;
        };

        // Handle click on isActive switch
        vm.projectHandleIsActiveClick = function(event, row) {
            // Get the current state
            const currentValue = row.isActive;

            // If currently OFF and trying to turn ON, show modal
            if (currentValue === false) {
                // Prevent the default toggle behavior
                event.preventDefault();

                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Active Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function(response) {
                    // Update the UI
                    row.isActive = true; // Toggle ON
                    row.toggleChildrenIsActive = response.applyToAllLevels; // Store the user's choice
                    row.pendingActiveChange = true; // Mark as pending change
                });
            } else {
                // If toggling OFF, let the default behavior happen and just mark as pending change
                row.toggleChildrenIsActive = true; // Default to apply to all levels when toggling OFF
                row.pendingActiveChange = true; // Mark as pending change
            }
        };

        // Launch bulk edit modal
        vm.projectLaunchBulkEditModal = function() {
            let modalScope = $rootScope.$new();

            // Get selected projects and pass them to the modal
            const selectedProjects = vm.projectList.filter(x => x.checkboxSelected);
            modalScope.selectedProjects = selectedProjects;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-project-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function(response) {
                if (response.bulkEditAction === "DELETE") {
                    let modalScope = $rootScope.$new();
                    modalScope.confirmationHeader = "Confirm Delete";
                    modalScope.confirmationText = "Are you sure you want to delete these projects?";
                    $mdDialog.show({
                        scope: modalScope,
                        templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                        parent: angular.element(document.body),
                        clickOutsideToClose: false,
                    }).then(async function(confirmed) {
                        if (confirmed) {
                            vm.projectBulkDeleteProjects();
                        }
                    });
                } else if (response.bulkEditAction === "COPY") {
                    vm.projectBulkCopyProjects();
                } else {
                    // Get all selected projects
                    const bulkSelected = vm.projectList.filter(x => x.checkboxSelected);

                    // Update each selected project in the UI only
                    bulkSelected.forEach(project => {
                        project.isActive = response.isActive;
                        project.toggleChildrenIsActive = response.toggleChildrenIsActive;
                        project.pendingActiveChange = true; // Mark as pending change
                        // Clear checkbox selection
                        project.checkboxSelected = false;
                    });
                }

                // Reset bulk selection status
                vm.projectBulkStatus.isIndeterminate = false;
                vm.projectBulkStatus.selectAllCheckboxState = false;
            });
        };

        // Load project list for the client
        var projectListPromise = vm.refreshProjectList();

        // Wait for both promises to complete
        $q.all([clientPromise, projectListPromise])
            .then(function () {
                vm.refreshContacts();
                vm.resetChangeDetection();
            });

        $q.all([designTemplatePromise, clientPromise]).then(() => {

            // If our template is still 'linked' here, we want to show the latest values. Any changes to these values on this
            // page will 'unlink' the template.
            if (vm.client.clientDefault.buildingZonesTemplateId != null && vm.client.clientDefault.buildingZonesTemplateId != 'BLANK_TEMPLATE') {

                let filtered = vm.buildingDesignTemplates.filter(x => x.buildingDesignTemplateId == vm.client.clientDefault.buildingZonesTemplateId);

                if (filtered != null && filtered.length > 0) {
                    vm.applyDesignTemplate(filtered[0]);
                }
            }

        });

        vm.testaroo = 2;

        var saveTableState = null;
        vm.callServer = function callServer(tableState, e, filterOption) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "clientJobNumber", operator: "startswith", value: val, logic: "or" },
                { field: "clientName", operator: "startswith", value: val, logic: "or" },
                { field: "clientAssigneeFullName", operator: "startswith", value: val, logic: "or" },
                { field: "clientJobNumber", operator: "startswith", value: val, logic: "or" },
                { field: "jobReference", operator: "startswith", value: val, logic: "or" },
                { field: "projectOwner", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            jobservice.getClientListCancel();
            jobservice.getClientList(vm.clientId, filterOption, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter, vm.statusCode)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = clientservice.currentFilter();
                    vm.clientJobsList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.clientJobsList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        vm.filterOption = function (code) {
            vm.filterByCode = code;
            vm.statusCode = code;
            vm.refreshList(code);
        }

        vm.getCurrentTabName = function () {
            let namesList = [
                "Details",
                "Reports",
                "Users",
                "Accounts",
                "Jobs",
                "Settings",
                "Client Defaults",
                "EnergyLab"
            ];
            return namesList[vm.selectedTab];
        }

        // Get async data
        vm.nominatedBuildingSurveyorList = [];
        var nominatedBuildingSurveyorPromise = nominatedbuildingsurveyorservice.getList()
            .then(function (data) {
                vm.nominatedBuildingSurveyorList = data;
            });

        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.certificationList = data.data;
            });

        vm.assessmentSoftwareList = [];
        var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
            .then(function (data) {
                vm.assessmentSoftwareList = data;
                vm.updateAvailableSoftware();
            });

        // Select Item Changes
        vm.minHouseEnergyRatingSearchText = "";
        vm.minHouseEnergyRatingChoices = [];
        for (var i = 0; i <= 10; i = i + 0.1) {
            vm.minHouseEnergyRatingChoices.push({ val: i.toFixed(1), description: i.toFixed(1).toString() });
        }

        vm.projectDescriptionList = [];
        var projectDescriptionPromise = projectdescriptionservice.getList()
            .then(function (data) {
                vm.projectDescriptionList = data.data;
            });

        vm.worksDescriptionList = [];
        var worksDescriptionPromise = worksdescriptionservice.getList()
            .then(function (data) {
                vm.worksDescriptionList = data;
            });

        vm.priorityList = [];
        var priorityPromise = priorityservice.getList()
            .then(function (data) {
                vm.priorityList = data.data;
            });

        vm.buildingExposureList = [];
        var buildingExposurePromise = buildingexposureservice.getAll()
            .then(function (data) {
                vm.buildingExposureList = data;
            });

        vm.bushfireAttackLevelList = [];
        var bushfireAttackLevelPromise = bushfireattacklevelservice.getList()
            .then(function (data) {
                vm.bushfireAttackLevelList = data.data;
            });

        vm.stateList = [];
        var statePromise = stateservice.getList()
            .then(function (data) {
                vm.stateList = data.data;
            });

        vm.nccClimateZoneCode = [];
        var nCCClimateZonePromise = nccclimatezoneservice.getList()
            .then(function (data) {
                vm.nccClimateZoneCode = data.data;
            });

        vm.getEmployees = function (searchTerm) {

            var filter = [
                { field: "fullName", operator: "startswith", value: searchTerm, logic: "and" },
                { field: "isExternal", operator: "eq", value: false, valueType: "boolean" }
            ];

            return userservice.getList(null, null, null, null, null, null, filter, null)
                .then(function (data) {
                 data.data.push({ fullName: "No Default", userId:null});
                return data.data;
            });
        }

        vm.assessorEmployeeChanged = function (item) {
            if (!item) { return;}
            if (item.fullName == "no default") {
                vm.client.clientDefault.assessorUserId = null;
                angular.element(document.querySelector('#assignedAssessorAutoComplete'))[0].querySelector('input').blur();
                return;
            }
            vm.client.clientDefault.assessorUserId = item.userId;
        }

        vm.clearProjectDescription = function (item) {
            item.projectDescriptionSearchText = "";
            item.projectDescriptionPreviousSearchText = "";
            item.projectDescription = null;
            item.projectDescriptionOther = null;
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if (vm.previousRoute != undefined && vm.previousRoute != null) {
                $state.go(vm.previousRoute);
            }
            else {
                $state.go("client-listform");
            }
        }

        vm.save = function (finishedCallback) {
            vm.isBusy = true;

            // Make sure all assessmentId's are nullified. They will be applied when the template itself is applied.
            vm.client.clientDefault.zones?.forEach(row => {
                row.createdOn = new Date().toUTCString();
            })

            clientservice.updateClient(vm.client).then(function (data) {

                if (data != null) {
                    vm.client = data;
                    vm.refreshContacts();
                }

                // Process any pending active changes
                const pendingProjects = vm.projectList.filter(project => project.pendingActiveChange);
                if (pendingProjects.length > 0) {
                    // Apply the toggleChildrenIsActive flag for each project
                    const updatePromises = pendingProjects.map(project => {
                        if (project.toggleChildrenIsActive) {
                            // Update child models if needed
                            return projectservice.updateIsActive(project.projectId, project.isActive, true);
                        }
                        return Promise.resolve();
                    });

                    // Clear the pending flags
                    pendingProjects.forEach(project => {
                        project.pendingActiveChange = false;
                    });

                    // Wait for all updateIsActive calls to complete
                    Promise.all(updatePromises);
                }

                // Update all projects
                projectservice.updateProjectsList(
                    vm.projectList.map(p => { return {
                        projectId: p.projectId,
                        isActive: p.isActive,
                        sortOrder: p.sortOrder,
                    }})
                ).then(() => {
                    vm.resetChangeDetection();
                    if (finishedCallback != null) {
                        finishedCallback();
                    }
                    vm.isBusy = false;
                });

            });
        }

        vm.createContact = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/contact/contact-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshContacts();
            }, function () {
                vm.refreshContacts();
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        vm.selectDefault = function ($event, userId) {
            if ($event) {
                $event.stopPropagation();
            }

            if (userId) {
                vm.client.defaultClientAssigneeUserId = userId;
                vm.defaultClientAssigneeUserId = userId;
            }
            for (var ii = 0, len = vm.clientUsers.length; ii < len; ii++) {
                if (vm.clientUsers[ii].userId == userId) {
                    vm.clientUsers[ii].checked = true;
                } else {
                    vm.clientUsers[ii].checked = false;
                }
            }
        }

        vm.setDefaultContact = function () {

            var idToUse = vm.defaultClientAssigneeUserId
                ? vm.defaultClientAssigneeUserId
                : vm.client.defaultClientAssigneeUserId
                    ? vm.client.defaultClientAssigneeUserId
                    : null;

            vm.client.defaultClientAssigneeUserId = idToUse;

            if (idToUse) {
                for (var ii = 0, len = vm.clientUsers.length; ii < len; ii++) {
                    if (vm.clientUsers[ii].userId == idToUse) {
                        vm.clientUsers[ii].checked = true;
                    }
                }
            }
        }

        vm.toggleSelection = function (item, list) {
            var idx = list.indexOf(item);
            if (idx > -1) {
                list.splice(idx, 1);
            }
            else {
                list.push(item);
            }
        };

        vm.exists = function (item, list) {
            return list.indexOf(item) > -1;
        };

        function setAssessorFilterOptions(allAssessors) {

            if(allAssessors == null || allAssessors.length === 0)
                return;

            if(vm.assessorOptions == null || vm.assessorOptions.length === 0) {

                vm.assessorOptions = [];
                vm.assessorOptions.push({
                    onclick: vm.updateAssessorFilter,
                    name: 'All',
                    code: 'All',
                    visible: true,
                    alwaysShown: true
                });

                allAssessors.forEach(user => {
                    vm.assessorOptions.push({
                        code: user.fullName,
                        name: user.fullName,
                        onclick: vm.updateAssessorFilter,
                        visible: true})
                });

            }
        }

        const allStatusCodes = [
            { name: 'Processing (new job)', code: 'JDraft'},
            { name: 'Processing (option selected)', code: 'JOptionSelected'},
            { name: 'Processing (recertification)', code: 'JRecertification'},
            { name: 'In Progress', code: 'JInProgress'},
            { name: 'Preliminary Review', code: 'JPreliminaryReview'},
            { name: 'Options Provided', code: 'JCompliance'},
            { name: 'Final Review', code: 'JComplete'},
            { name: 'Complete', code: 'JIssued'},
            { name: 'Cancelled', code: 'JCancelled'},
        ];

        function setStatusFilterOptions(allStatusCodes) {

            if(allStatusCodes == null || allStatusCodes.length === 0)
                return;

            // Add all status codes ONE TIME. After that, we only show/hide depending on what data is restricted.
            // This gets around the brief flicker where UI displayed duplicate values.
            if(vm.statusOptions == null || vm.statusOptions.length === 0) {

                vm.statusOptions = [];

                // Always add these 2 special values.
                vm.statusOptions.push({
                    onclick: vm.filterOption,
                    name: 'All',
                    code: 'All',
                    position: 'left',
                    visible: true,
                    alwaysShown: true
                });

                vm.statusOptions.push({
                    onclick: vm.filterOption,
                    name: 'Active',
                    code: 'Active',
                    position: 'middle',
                    visible: true,
                    alwaysShown: true
                });

                allStatusCodes.forEach(x => {
                    vm.statusOptions.push({
                        onclick: vm.filterOption,
                        name: x.name,
                        code: x.code,
                        position: 'middle',
                        visible: true
                    })
                });

            }

        }
        setStatusFilterOptions(allStatusCodes, null);

        vm.filterColumns = null;
        vm.filterLocalName = "clientUpdateCtrl-columnFilter"
        vm.columnOptions = {
            jobReference: true,
            contact: true,
            clientRef: true,
            owner: true,
            orderDate: true,
            complianceOption: true,
            assessmentVersion: true,
            assessor: true,
            priority: true,
            status: true,
            projectAddress: true,
            projectDescription: true,
            columnCount: 14,
            columnList: [
                { sortOrder: 0, reference: 'jobReference', description: 'Ref' },
                { sortOrder: 1, reference: 'contact', description: 'Contact' },
                { sortOrder: 2, reference: 'clientRef', description: 'Client Ref' },
                { sortOrder: 3, reference: 'owner', description: 'Owner' },
                { sortOrder: 5, reference: 'orderDate', description: 'Created' },
                { sortOrder: 6, reference: 'complianceOption', description: 'Compliance Option' },
                { sortOrder: 8, reference: 'assessmentVersion', description: 'Version' },
                { sortOrder: 9, reference: 'assessor', description: 'Assessor' },
                { sortOrder: 10, reference: 'priority', description: 'Priority' },
                { sortOrder: 11, reference: 'status', description: 'Status' },
                { sortOrder: 12, reference: 'projectAddress', description: 'Project Address' },
                { sortOrder: 13, reference: 'projectDescription', description: 'Building Description' },
            ],
        };

        vm.resetColumnOptions = function () {

            for (var ii = 0, len = vm.columnOptions.columnList.length; ii < len; ii++) {
                vm.columnOptions[vm.columnOptions.columnList[ii].reference] = false;
            }
        }

        vm.getDefaultColumnOptions = function () {
            var savedState = JSON.parse(localStorage.getItem(vm.filterLocalName));
            if (savedState) {
                vm.columnOptions = savedState;

                var filterColumns = [];
                for (var ii = 0, len = vm.columnOptions.columnList.length; ii < len; ii++) {
                    if (vm.columnOptions[vm.columnOptions.columnList[ii].reference]) {
                        filterColumns.push(vm.columnOptions.columnList[ii].reference);
                    }
                }
                vm.filterColumns = filterColumns;
            } else {
                vm.filterColumns = ['jobReference', 'contact', 'clientRef', 'owner', 'orderDate', 'complianceOption', 'assessmentVersion', 'assessor', 'priority', 'status', 'projectAddress', 'projectDescription']
            }
        }

        vm.getDefaultColumnOptions();

        vm.filterColumnsChanged = function (newValue, oldValue) {
            if (newValue != undefined && newValue != null && newValue != oldValue) {
                vm.resetColumnOptions();
                for (var ii = 0, len = newValue.length; ii < len; ii++) {
                    vm.columnOptions[newValue[ii]] = true;
                }
                vm.columnOptions.columnCount = newValue.length;
                localStorage.setItem(vm.filterLocalName, JSON.stringify(vm.columnOptions));
            }
        };

        var newJobListener = $rootScope.$on('newJob', function () {
            vm.refreshList(vm.filterByCode);
        });

        //Wait for the required dropdown lists to set so the client options can filter them
        $q.all([projectDescriptionPromise, complianceMethodPromise,
             assessmentSoftwarePromise, clientPromise, nominatedBuildingSurveyorPromise,
            worksDescriptionPromise, certificationPromise]).then(function () {

            //Check if the Performance Compliance Option field should be disabled and set
           // vm.validateComplianceOption();

            // We set a small timeout to ensure there is no race condition between this firing and
            // our .then's firing on the individual promises.
            setTimeout(() => {
                vm.clientOptionsData = {
                    nominatedBuildingSurveyorList: vm.nominatedBuildingSurveyorList,
                    complianceMethodList: vm.complianceMethodList,
                    assessmentSoftwareList: vm.assessmentSoftwareList,
                    minHouseEnergyRatingChoices: vm.minHouseEnergyRatingChoices,
                    certificationList: vm.certificationList,
                    worksDescriptionList: vm.worksDescriptionList

                }
            }, 100);
        });

        vm.delete = function () {
            clientservice.deleteClient(vm.clientId).then(function () {
                $state.go("client-listform");
            });
        }

        vm.undoDelete = function () {
            clientservice.undoDeleteClient(vm.clientId).then(function () {
                $state.go("client-listform");
            });
        }

        $scope.$on('$destroy', function () {
            newJobListener();
        });

        // Functions to get data for Typeahead
        vm.getProjectDescriptionList = function (searchText, includeOther) {

            var list = [];

            if (vm.projectDescriptionList) {

                var typeOther = null;
                if (searchText == undefined || searchText == null) {
                    searchText = "";
                }

                searchText = searchText.toLowerCase();

                for (var ii = 0; ii < vm.projectDescriptionList.length; ii++) {

                    let projectDescription = vm.projectDescriptionList[ii];

                    if (projectDescription.projectDescriptionCode == 'PDOther') {
                        typeOther = projectDescription;
                        continue;
                    }

                    if (projectDescription.description.toLowerCase().includes(searchText)) {
                        list.push(projectDescription);
                    }

                }

                //Always have Other at the bottom of the list before 'No Default'
                if (typeOther != null && includeOther) {
                    list.push(typeOther);
                }
            }

            return list;
        }

        // Functions to get data for Typeahead
        vm.getWorksDescriptionList = function (searchText, includeOther) {

            var list = [];

            if (vm.worksDescriptionList) {

                var typeOther = null;
                if (searchText == undefined || searchText == null) {
                    searchText = "";
                }

                searchText = searchText.toLowerCase();

                for (var ii = 0; ii < vm.worksDescriptionList.length; ii++) {

                    let worksDescription = vm.worksDescriptionList[ii];

                    if (worksDescription.description.toLowerCase().includes(searchText))
                        list.push(worksDescription);

                }

                //Always have Other at the bottom of the list before 'No Default'
                if (typeOther != null && includeOther) {
                    list.push(typeOther);
                }
            }

            return list;
        }

        vm.clearTemplateProjectDescription = function (item) {
            item.projectDescription = null;
            item.projectDescriptionOther = null;
        }

        vm.floorsWithGlazingChanged = function (count, buildingFloors, forceRename) {

            // Allow nulls ("No Default") on this page.
            if (count == null) {
                vm.floorsWithGlazing = null;
                vm.client.clientDefault.storeys = null;
                return;
            }

            let existingFloorCount = vm.client.clientDefault.storeys?.length || 0;

            if(forceRename == true)
                existingFloorCount = 0;

            let newStoreys = assessmentcomplianceoptionservice.generateStoreys(count, vm.client.clientDefault.storeys);
            newStoreys = assessmentcomplianceoptionservice.setStoreyNames(newStoreys, buildingFloors, existingFloorCount);
            vm.floorsWithGlazing = newStoreys.length;

            vm.client.clientDefault.storeys = newStoreys;

            if (vm.client.clientDefault.allComplianceOptions != null) {
                for (var i = 0; i < vm.client.clientDefault.allComplianceOptions.length; i++) {

                    var option = vm.client.clientDefault.allComplianceOptions[i];
                }
            }
        }

        //called when compliance method on compliance option gets changed.
        vm.validatePreliminaryComplianceMethodChange = function () {

            vm.updateAvailableSoftware();

            //Ensure when Baseline Assessment Method equals 'House Energy Rating (HER)' the 'Not Required' option is unavailable
            var index = _.findIndex(vm.minHouseEnergyRatingChoices, { val: -1 });

            if (vm.client.clientDefault.preliminaryComplianceMethodCode === "CMHouseEnergyRating" ||
                vm.client.clientDefault.preliminaryComplianceMethodCode === "CMPerSolutionHER" ||
                vm.client.clientDefault.preliminaryComplianceMethodCode === "CMPerfWAProtocolHER") {
                vm.minHouseEnergyRatingChoices.splice(index, 1);
                //Deselect the 'Not Required' option
                if (vm.minHouseEnergyRating && vm.minHouseEnergyRating.val == -1) {
                    vm.minHouseEnergyRating = null;
                }
            }
        }

        vm.updateAvailableSoftware = function () {

            var currentSoftwareCode = vm.client.clientDefault.assessmentSoftwareCode;
            var currentComplianceCode = vm.client.clientDefault.preliminaryComplianceMethodCode;

            for (var ii = 0; ii < vm.assessmentSoftwareList.length; ii++) {

                var softwareFound = false;
                for (var jj = 0; jj < vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods.length; jj++) {
                    if (vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].complianceMethodCode == currentComplianceCode && vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].isAvailable) {
                        vm.assessmentSoftwareList[ii].isAvailable = true;
                        softwareFound = true;
                        break;
                    }
                }
                if (!softwareFound) {
                    vm.assessmentSoftwareList[ii].isAvailable = false;
                }

                if (currentSoftwareCode == vm.assessmentSoftwareList[ii].assessmentSoftwareCode && !vm.assessmentSoftwareList[ii].isAvailable) {
                    vm.client.clientDefault.assessmentSoftwareCode = null; // clear selected option because software is invalid.
                }
            }
        }

        vm.formatActualMinHouseEnergyRating = function () {
            vm.client.clientDefault.minHouseEnergyRating = parseFloat(vm.tempMinHouseEnergyRating);
        }

        vm.applyDesignTemplate = function (template) {

            // Design fields need to be pre-populated here.
            // The template needs to be re-applied on every load (in case the template itself changes!)
            // Note that while a template is applied though, that the user cannot modify any values.

            vm.client.clientDefault.projectClassification = template.projectClassification;
            vm.client.clientDefault.projectDescription = template.projectDescription.description;
            vm.client.clientDefault.projectDescriptionOther = template.projectDescriptionOther;

            vm.client.clientDefault.design = template.design;

            vm.client.clientDefault.storeys = template.storeys;
            vm.floorsWithGlazing = template.storeys.length;
            vm.client.clientDefault.storeys = template.storeys;

        }

        /** Filters ALL surveyors and returns a list of those present in the Client Settings. */
        vm.availableWorksDescriptionList = function () {
            return vm.worksDescriptionList
                ?.filter(x => vm.client.clientOptions.availableWorksDescriptions?.some(y => y == x.worksDescriptionCode));
        }

        /** Filters ALL surveyors and returns a list of those present in the Client Settings. */
        vm.availableBuildingSurveyors = function () {
            return vm.nominatedBuildingSurveyorList
                ?.filter(x => vm.client.clientOptions.availableBuildingSurveyorIds?.some(y => y == x.buildingSurveyorId));
        }

        /** Filters ALL compliance methods and returns a list of those present in the Client Settings. */
        vm.availableComplianceMethods = function () {
            return compliancemethodservice.determineAvailableComplianceMethods(
                vm.complianceMethodList?.filter(x => vm.client.clientOptions.availableComplianceMethodCodes?.some(y => y === x.complianceMethodCode)),
                vm.client.clientDefault?.worksDescription?.worksDescriptionCode);
        }

        // No longer filtering by client options, return it all!
        vm.availableAssessmentSoftware = function () {
            return vm.assessmentSoftwareList;
        }

        /** Filters ALL certifications and returns a list of those present in the Client Settings. */
        vm.availableCertifications = function () {
            return vm.certificationList
                ?.filter(x => vm.client.clientOptions.availableCertifications?.some(y => y == x.certificationId));
        }

        vm.disableDesignInputs = function () {
            return vm.client.clientDefault.designTemplateId != 'MODIFIED';
        }

        /** This is a callback which gets passed to, and fired from, the building floors area component */
        vm.onClassificationUpdate = function (projectClassification) {
            vm.client.clientDefault.projectClassification = projectClassification;
        }

        vm.costEstimateEnabledFor = function(variable, option) {

            const matches = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable]?.filter(x => x.optionValue === option);

            if(matches == null || matches.length === 0)
                return false;

            const match = matches[0];
            return match.costEstimateEnabled;
        }

        vm.isDefaultForVariable = (variable, option) => vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable]?.filter(x => x.optionValue === option)[0]?.isDefaultForVariable;

        vm.ordered = function(options) {
            return options.sort((a, b) => a.sortOrder - b.sortOrder);
        }

        vm.reorderOptions = function(variable, optionsInOrder) {

            // options will be in the order we want so simply loop over and apply
            const allOptions = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];
            optionsInOrder.forEach((opt, i) => {
                let match = allOptions.find(x => x.optionValue === opt);

                if(match == null && match !== 0) {
                    allOptions.push({ optionValue: opt });
                    match = allOptions.find(x => x.optionValue === opt);
                }

                match.sortOrder = i;
            });

            flattenCostData();
        }

        vm.selectAllCheckboxes = (a, b, c) => {
            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                safeApply();
            }, 25);
        }

        // Click on Model row
        vm.variationRowClick = function (projectId) {
            vm.navigateAttempt('project-updateform', { projectId: projectId });
        }

        // Attempt to navigate
        vm.navigateAttempt = async function (page, params) {
            if (vm.hasChanges()) {
                let modalScope = $rootScope.$new();
                modalScope.title = vm.client.clientName;
                modalScope.bodyText = `You have unsaved changes for "${vm.client.clientName}". Would you like to save before navigating?`;
                modalScope.buttons = [{
                    isPrimary: true,
                    text: "Save",
                    onClick: () => vm.save(() => $state.go(page, params))
                },{
                    text: "Don't Save",
                    onClick: () => $state.go(page, params)
                },{
                    isCancel: true,
                    text: "Cancel"
                }];
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                });
            } else {
                $state.go(page, params);
            }
        }

        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        vm.updateBulkSelectStatus = updateBulkSelectStatus;

        vm.launchCostItemsBulkEdit = async function() {

            let modalScope = $rootScope.$new();

            // Modal Inputs
            modalScope.type = "client";
            modalScope.allCostItemCategories = vm.allCostItemCategories;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-cost-items-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {

                const bulkSelected = vm.client.clientCostItems.filter(x => x.checkboxSelected);

                bulkSelected.forEach(x => {

                    if (response.category)
                        x.category = response.category;

                    if (response.unitOfMeasure)
                        x.unitOfMeasure = response.unitOfMeasure;

                    if (response.ratePerUnit)
                        x.ratePerUnit = response.ratePerUnit;

                    if (response.margin)
                        x.margin = response.margin;

                    if (response.rounding)
                        x.rounding = response.rounding;

                    if (response.notes)
                        x.notes = response.notes;

                    if (response.clientPortalEnabled)
                        x.clientPortalEnabled = response.clientPortalEnabled == "Yes" ? true : false;

                    x.checkboxSelected = false;

                });

                vm.costItemsBulkStatus.isIndeterminate = false;
                vm.costItemsBulkStatus.selectAllCheckboxState = false;

            });
        }

        vm.heatingSystemTypesForCategoryCode = (code) => servicetemplateservice
          .heatingSystemTypesForCategoryCode(code, vm.heatingSystemTypes);

        vm.showEnergyRatingForCode = wholeofhomeservice.showEnergyRatingForCode;

        vm.showEnergyRating = wholeofhomeservice.showEnergyRating;

        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.keyToName = standardmodelservice.keyToName;

        // - ---------- - //
        // - Cost Items - //
        // - ---------- - //

        vm.allCostItemCategories = Object.keys(standardmodelservice.allPossibleVariableOptions);
        vm.allCostItemCategories.sort((a,b) => a > b ? 1 : -1);

        // Create
        vm.createCostItem = function () {
            let newItem = { clientCostItemId: uuid4.generate() };
            if (vm.client.clientCostItems == null) {
                vm.client.clientCostItems = [newItem];
            } else {
                vm.client.clientCostItems.push(newItem);
            }
        }

        // Clone
        vm.cloneCostItem = function (item) {
            let index = vm.client.clientCostItems.findIndex(i => i.clientCostItemId == item.clientCostItemId);
            let newItem = angular.copy(item);
            newItem.clientCostItemId = uuid4.generate();
            vm.client.clientCostItems.splice(index + 1, 0, newItem);
        }

        // Delete
        vm.deleteCostItem = function (item) {
            vm.client.clientCostItems = vm.client.clientCostItems.filter(i => i.clientCostItemId != item.clientCostItemId);
        }

    }
})();