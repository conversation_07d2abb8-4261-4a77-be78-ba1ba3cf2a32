// Name: bushfirestatedataservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'bushfirestatedataservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', bushfirestatedataservice]);

    function bushfirestatedataservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'bushfirestatedata/';

        var service = {
            /* These are the operations that are available from this service. */
            getByCode: getByCode,
            stateHasBushfireData: stateHasBushfireData,
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            update
        };
            
        return service;

        function getByCode(stateCode) {
            return $http({
                url: baseUrl + 'GetByCode',
                params: { stateCode: stateCode },
                method: 'GET',
            }).then(success, fail);

            function success(resp) {

                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {

                var msg = "Error checking BushFireProne: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        async function stateHasBushfireData(stateCode) {
            return (await getByCode(stateCode)).hasBushfireData;
        }

        function getList(forFilter, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            let params = common.buildqueryparameters.build(null, pageSize, pageIndex, sort, filter, aggregate);
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting BushfireAttackLevel list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function update(bushfireStateData) {
            var url = baseUrl + 'Update';
            return $http.post(url, bushfireStateData).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Bushfire State Data: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
