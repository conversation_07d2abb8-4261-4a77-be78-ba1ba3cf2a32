<md-menu>
    <md-button class="md-primary"
               style="margin-left: auto; display: block; margin-top: 6px;"
               ng-disabled="vm.disabled"
               ng-click="vm.openMenu($mdOpenMenu, $event)">
        ADD ELEMENT
    </md-button>
    <md-menu-content width="4">
        <md-menu-item>
            <md-button ng-click="vm.addElement(vm.parent, vm.building)">
                Single Element
            </md-button>
        </md-menu-item>
        <!-- the ng-if on these 2 is to disable the multi-add option when we're coming from a template, i.e no zones -->
        <md-menu-divider ng-if="vm.building.zones != null && vm.building.zones.length > 0"></md-menu-divider>
        <md-menu-item ng-if="vm.building.zones != null && vm.building.zones.length > 0">

            <md-menu style="margin: 0; padding: 0;">
                <md-button ng-mouseenter="vm.openMenu($mdOpenMenu, $event)"
                           class="multi-add-button">
                    Multiple Elements
                </md-button>
                <md-menu-content style="overflow-x: hidden;">

                    <!-- Don't have inside loop otherwise the first item per group is always highlighted even when not hovered on-->
                    <md-menu-item ng-if="elementType.show" ng-repeat="elementType in vm.addElementTypeGroups[0] track by $index" style="display: block;">
                        <md-button ng-click="elementType.callback()" style="display: block;">
                            {{elementType.title}}
                        </md-button>
                    </md-menu-item>

                    <!-- Dividers only display if the group below exists -->
                    <md-menu-divider ng-if="vm.showGroupDividers[1]"></md-menu-divider>
                    <md-menu-item ng-if="elementType.show" ng-repeat="elementType in vm.addElementTypeGroups[1] track by $index" style="display: block;">
                        <md-button ng-click="elementType.callback()" style="display: block;">
                            {{elementType.title}}
                        </md-button>
                    </md-menu-item>

                    <md-menu-divider ng-if="vm.showGroupDividers[2]"></md-menu-divider>
                    <md-menu-item ng-if="elementType.show" ng-repeat="elementType in vm.addElementTypeGroups[2] track by $index" style="display: block;">
                        <md-button ng-click="elementType.callback()" style="display: block;">
                            {{elementType.title}}
                        </md-button>
                    </md-menu-item>

                    <md-menu-divider ng-if="vm.showGroupDividers[3]"></md-menu-divider>
                    <md-menu-item ng-if="elementType.show" ng-repeat="elementType in vm.addElementTypeGroups[3] track by $index" style="display: block;">
                        <md-button ng-click="elementType.callback()" style="display: block;">
                            {{elementType.title}}
                        </md-button>
                    </md-menu-item>

                    <md-menu-divider ng-if="vm.showGroupDividers[4]"></md-menu-divider>
                    <md-menu-item ng-if="elementType.show" ng-repeat="elementType in vm.addElementTypeGroups[4] track by $index" style="display: block;">
                        <md-button ng-click="elementType.callback()" style="display: block;">
                            {{elementType.title}}
                        </md-button>
                    </md-menu-item>

                </md-menu-content>
            </md-menu>
        </md-menu-item>
    </md-menu-content>
</md-menu>

<style>
    .multi-add-button {
        text-align: left;
        display: inline-block;
        border-radius: 0;
        margin: auto 0;
        font-size: 15px;
        text-transform: none;
        font-weight: 400;
        height: 100%;
        padding-left: 16px;
        padding-right: 16px;
        width: 100%;
        border: 0;
    }
</style>
