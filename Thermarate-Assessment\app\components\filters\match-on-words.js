﻿/*
* filter list returning records that have a match on all words in the filter value.
* the filter value is split into words based on spaces.
* all properties in each list record are checked for a possible match.
*/
angular.module('app').filter('matchOnWords', function () {
    return function (records, words) {
        var wordsArray = [];
        if (words != null && words.length > 0) {
             wordsArray = words.split(' ');
        }
        return records.filter(function (record) {

            for (var i in wordsArray) {
                if (!checkObjectHasValue(record, wordsArray[i]))  {
                    return false;  // Word not found in record.
                }
            }
            return true;

        });
    };

    function checkObjectHasValue(record, value) {
        for (var prop in record) {
            if (record.hasOwnProperty(prop)) {
                if ((typeof record[prop] == 'string' && record[prop].toLowerCase().indexOf(value.toLowerCase()) > -1)
                    || (typeof record[prop] == 'number' && record[prop] == value)) {
                    return true
                }
            }
        }
        return false;
    }
});