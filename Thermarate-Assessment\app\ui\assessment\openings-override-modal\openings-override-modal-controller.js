(function () {
    'use strict';
    var controllerId = 'OpeningsOverrideModalCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'common', newUserModalControl]);
    function newUserModalControl($rootScope, $scope, $mdDialog, common) {

        var vm = this;
        vm.isBusy = false;
        vm.previousRoute = $rootScope.previousState;

        /* Input variables */
        vm.item = $scope.item;
        vm.disabled = $scope.disabled;
        vm.propertyName = $scope.property;

        if(vm.propertyName === "grossArea") {
            vm.title = "Area";
            vm.originalValueText = "Original Value";
            vm.manualOverrideSubtext = "Manual Override";
        } else if(vm.propertyName === "openability") {
            vm.title = "Openability";
            vm.originalValueText = "Original Value";
            vm.manualOverrideSubtext = "Manual Override";
        }

        // Generated property name strings based on the passed in property
        vm.originalValuePropertyName = vm.propertyName + 'OriginalValue';
        vm.isOverriddenPropertyName = vm.propertyName + 'IsManuallyOverridden';        

        // Copy to local variables to prevent updating passed in object in real time.
        // Use the OriginalValue property if it exists, otherwise use the normal property.
        vm.originalValue = vm.item[vm.originalValuePropertyName] ?? vm.item[vm.propertyName];
        vm.isOverridden = vm.item[vm.isOverriddenPropertyName];
        vm.overrideValue = vm.item[vm.propertyName];

        /* Functions */

        vm.reset = function() {
            // Reset to non overridden state
            // vm.originalValue doesn't change
            // vm.isOverridden = false; // ?
            vm.overrideValue = vm.originalValue;
            common.forceBlurInputWithId("override-value-focus-node");
        }

        vm.toggleOverride = function() {
            // vm.isOverriden will be the current state, so if clicking from false -> true it will be true
            if (!vm.isOverridden) {
                vm.overrideValue = vm.originalValue;
                common.forceBlurInputWithId("override-value-focus-node");
            }
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        vm.save = function() {
            // Save assessment button handles the actual saving.
            // Just need to send back the new values for the caller to set them.
            $mdDialog.hide({
                originalValue: vm.originalValue,
                isOverridden: vm.isOverridden,
                overrideValue: vm.overrideValue
            });
        }
    }
})();