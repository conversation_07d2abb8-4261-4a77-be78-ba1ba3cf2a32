USE [thermarate];

SELECT [Identifier]
      ,[StateOfAustralia]
      ,[NccClimateZone]
      ,[SpaceHeatingSystemTypeCode]
      ,[SpaceHeatingSystemMinEnergyRatingGems2012]
      ,[SpaceHeatingSystemMaxEnergyRatingGems2012]
      ,[SpaceHeatingSystemMinEnergyRatingGems2019]
      ,[SpaceHeatingSystemMaxEnergyRatingGems2019]
      ,[SpaceCoolingSystemTypeCode]
      ,[SpaceCoolingSystemMinEnergyRatingGems2012]
      ,[SpaceCoolingSystemMaxEnergyRatingGems2012]
      ,[SpaceCoolingSystemMinEnergyRatingGems2019]
      ,[SpaceCoolingSystemMaxEnergyRatingGems2019]
      ,[ElectricStorageStandard]
      ,[ElectricStorageOffPeak]
      ,[HeatPumpStandard]
      ,[HeatPumpOffPeak]
      ,[SolarElectricStandard]
      ,[GasStorage]
      ,[GasInstantaneous]
      ,[SolarGas]
      ,[OtherOrNoneSpecified]
  FROM [dbo].[RSS_WholeOfHomeEfficiencyFactor]
  WHERE 1=1
	-- AND [StateOfAustralia] = 'VIC'
	-- AND [NccClimateZone] = 4
	-- AND [SpaceHeatingSystemTypeCode] = 'GasDucted'
	-- AND [SpaceCoolingSystemTypeCode] = 'Evaporative'
