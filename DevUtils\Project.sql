USE [thermarate];

SELECT [ProjectId]
      ,[ClientId]
      ,[ProjectName]
      ,[Description]
      ,[ProjectTypeCode]
      ,[IsActive]
      ,[Lots]
      ,[LotArea]
      ,[LogoFileId]
      ,[SuburbCode]
      ,[StateCode]
      ,[LGA]
      ,[LGAShort]
      ,[NatHERSClimateZoneCode]
      ,[NCCClimateZoneCode]
      ,[Latitude]
      ,[Longitude]
      ,[LockWOHLocation]
      ,[EnergyLabsSettingsJson]
      ,[VariationOptionsJson]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[Deleted]
  FROM [dbo].[RSS_Project]
  WHERE 1=1
	AND [Deleted] = 0
	AND [ProjectId] = '4fdc16f0-9e35-416a-aeaa-a715d0efaaa9'
	--AND [ClientId] = 'B0139FEE-0D56-6D81-4442-3A0B3032EF11'
  --INNER JOIN [dbo].[RSS_ProjectType] []
  --ORDER BY [CreatedOn] DESC
  ORDER BY [ModifiedOn] DESC


  --UPDATE [dbo].[RSS_Project]
  --SET [Deleted] = 0
  --WHERE [ProjectId] IN ('34248D63-FA15-4727-88A5-63440CA3935A', '88dd5383-211c-436b-baa9-e0dbf0e38341', '4dc7011c-0f91-486c-ba2d-e9b0558ba3c8')

  
  --UPDATE [dbo].[RSS_Project]
  --SET [LotArea] = 10.123456789
  --WHERE [ProjectId] = '13082b88-6eef-4508-b0fb-9982b1e9447e'