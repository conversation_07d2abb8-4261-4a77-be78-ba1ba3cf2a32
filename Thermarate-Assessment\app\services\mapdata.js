// Name: mapdataservice
// Type: Angular Service
// Purpose: Get data from teh 3rd party map data service.
// See http://apps.nowwhere.com.au/MDSApiDocumentation/WebServices/Place#suggest
//
// 1. We call the Context service to get available suburbs
// 2. We call the place/suggest service to get the street (this also returns DP and Lot).
// 3. We call the Context service again to get the address broken down
// 4. We then call the Shape/Searchpoint service to get the NatHERS data.

(function () {
    'use strict';
    var serviceId = 'mapdataservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', mapdataservice]);

    function mapdataservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var baseUrl = config.servicesUrlPrefix + "mapdataproxy/" //'http://api.nowwhere.com.au/1.1.2/';

        var service = {
            /* These are the operations that are available from this service. */
            searchSuburbs: searchSuburbs,
            searchStreets: searchStreets,
            getNatHers: getNatHers,
            searchStreetsByDp: searchStreetsByDp,
            getPlanNumberFromField: getPlanNumberFromField
        };
            
        return service;

        function searchSuburbs(searchTerm, stateCode) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'place/suggest/';
            // See http://apps.nowwhere.com.au/MDSApiDocumentation/WebServices/Place#suggest
            
            //Get suburbs List from 3rd party service 
            return $http({
                url: wkUrl,
                params: {
                    query: searchTerm,
                    filters: "region:" + stateCode,
                    p: false,
                    datasets: "suburbpostcodeau",
                    limit: 50,
                    fields: "locality,postcode,region,lga_name"
                },
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Suburb list from map data service: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function searchStreets(searchTerm, suburb, stateCode, postcode) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'place/suggest/';
            // See http://apps.nowwhere.com.au/MDSApiDocumentation/WebServices/Place#suggest
            
            if (suburb != null && suburb.indexOf(" ") > -1) {
                // Map data has a problem with spaces in the locality filter.
                suburb = suburb.substring(0, suburb.indexOf(" "));
            }

            var suburbFilter = ",locality:" + suburb;
            if (suburb == null || suburb == "") {
                suburbFilter = "";
            }
            if (postcode != null) {
                suburbFilter = suburbFilter + ",postcode:" + postcode;
            }
            //Get streets List from 3rd party service 
            return $http({
                url: wkUrl,
                params: {
                    filters: 'region:' + stateCode + suburbFilter,
                    datasets: 'gnaf',
                    query: searchTerm, 
                    limit: 50,
                    fields: "subpremise,number,route,locality,postcode,region,lot_number,legal_parcel_id,lga_name"
                },
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null && resp.data.Results != null) {
                    for (var i = 0, len = resp.data.Results.length; i < len; i++) {
                        var rec = resp.data.Results[i];
                        rec.Value = "";
                        if (rec.CustomFields.lot_number != '') {
                            rec.Value = "Lot " + rec.CustomFields.lot_number + " ";
                        }
                        if (rec.CustomFields.number != '') {
                            rec.Value = rec.Value + "(#" + rec.CustomFields.number + ") ";
                        }
                        rec.CustomFields.route = common.toTitleCase(rec.CustomFields.route);
                        rec.CustomFields.locality = common.toTitleCase(rec.CustomFields.locality);
                        rec.Value = rec.Value + rec.CustomFields.route + ", " + rec.CustomFields.locality;
                    }
                    var sortedArray = _(resp.data.Results).chain().sortBy(function (rec) {
                        return rec.CustomFields.lot_number != '' ? rec.CustomFields.lot_number : rec.CustomFields.number;
                    }).sortBy(function (rec) {
                        return rec.CustomFields.route;
                    }).value();
                    resp.data.Results = sortedArray;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting streets list from map data service: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function searchStreetsByDp(dpNumber) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'place/suggest/';
            // See http://apps.nowwhere.com.au/MDSApiDocumentation/WebServices/Place#suggest

            
            //Get Streets by DPId from 3rd party service 
            return $http({
                url: wkUrl,
                params: {
                    datasets: 'gnaflpid',
                    query: dpNumber,
                    limit: 50,
                    fields: "subpremise,number,route,locality,postcode,region,lot_number,formattedaddress,lga_name"
                },
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Address list by DP from map data service: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getNatHers(coordinate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'shape/searchpoint/';
            // See http://apps.nowwhere.com.au/MDSApiDocumentation/WebServices/Place#suggest

            //Get NatHERS Id from 3rd party service 
            return $http({
                url: wkUrl,
                params: {
                    point: "[" + coordinate + "]",
                    datasets: 'nathers'
                },
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting NatHERS data from map data service: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getPlanNumberFromField(legal_parcel_id, region) {
            var parts = legal_parcel_id.split("/");
            var ret = { lot: "", planNumber : "" };
            switch (region) {
                case "WA":
                    if (parts != null && parts.length > 1) {
                        ret.lot = parts[0];
                        ret.planNumber = parts[1];
                    }
                    else {
                        parts = legal_parcel_id.split(" ");
                        if (parts != null && parts.length > 0) {
                            ret.lot = parts[parts.length - 1];
                            ret.planNumber = parts[0];
                        }
                    }
                    break;
                case "SA":
                    if (parts != null && parts.length > 1) {
                        ret.planNumber = parts[0];
                    }
                    break;
                case "NSW":
                    if (parts != null && parts.length > 2) {
                        ret.lot = parts[0];
                        ret.planNumber = parts[2];
                    }
                    break;
                case "NT":
                    break;
                case "VIC":
                    if (parts != null && parts.length > 2) {
                        ret.lot = parts[0];
                        ret.planNumber = parts[1];
                        if (ret.planNumber.indexOf("~") >= 0) {
                            ret.planNumber = ret.planNumber.substring(0, ret.planNumber.indexOf("~"));
                        }
                    }
                    break
                case "ACT":
                    break;
                case "QLD":
                case "TAS":
                    if (parts != null && parts.length > 1) {
                        ret.lot = parts[0];
                        ret.planNumber = parts[1];
                    }
                    break;
                default:
                    break;
            }

            return ret;
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        
    }
})();

