(function () {
    'use strict';
    var serviceId = 'wholeofhomeexportservice';
    angular.module('appservices').factory(serviceId, ['common', 'config', '$http', 'zoneservice', 'servicetemplateservice', wholeofhomeexportservice]);

    function wholeofhomeexportservice(common, config, $http, zoneservice, servicetemplateservice) {

        var $q = common.$q;
        var log = common.logger;
        var baseUrl = config.servicesUrlPrefix + 'wholeofhomeexport/';

        var service = {
            generateWholeOfHomeExport,
            wholeOfHomeExport
        };

        return service;

        function generateWholeOfHomeExport(assessment) {
            // Floor Area
            let building = ( assessment.allComplianceOptions.find(x => x.isSelected) ?? assessment.allComplianceOptions[0] ).proposed;
            const allInteriorZones = zoneservice.interiorZones(building.zones);
            const garageZones = allInteriorZones.filter(z => z.zoneActivity?.zoneActivityCode === "ZAGarage");
            const allInteriorArea = allInteriorZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            const allGarageArea = garageZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            let floorArea = Number((allInteriorArea - allGarageArea).toFixed(2));

            // Heat Pump
            let heatPumpType = building.services?.some(s => s.isPrimary && (s.serviceType?.serviceTypeCode == "HeatPumpDucted" || s.serviceType?.serviceTypeCode == "HeatPumpNonDucted"))
                                ? "Seasonal Star Rating (2019)"
                                : "";

            // Heating
            let heatingServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "SpaceHeatingSystem" && s.isPrimary);
            let heatingTypesCheck = ['GasDucted', 'GasNonDucted', 'HydronicGas', 'HydronicUnderfloor', 'HeatPumpDucted', 'HeatPumpNonDucted'];
            let heatingType = building.categoriesNotRequired['spaceheatingsystem']
                                ? "Other"
                                : heatingServiceMatch != null
                                    ? servicetemplateservice.getMappedSystemType(heatingServiceMatch.serviceType, 'SpaceHeatingSystem', 'exportTitle')
                                    : "";
            let useHeatingRating = !building.categoriesNotRequired['spaceheatingsystem'] && heatingServiceMatch != null && heatingTypesCheck.includes(heatingServiceMatch.serviceType.serviceTypeCode);
            let heatingRating = useHeatingRating ? heatingServiceMatch.starRating2019 : "";
            // Convert rating to be acceptable for xlsx sheet
            if (useHeatingRating && heatingRating != null && ['HeatPumpDucted', 'HeatPumpNonDucted'].includes(heatingServiceMatch.serviceType.serviceTypeCode)) {
                if (heatingRating  < 2.25)                         { heatingRating = "< 2.25"; }
                if (heatingRating >= 2.25 && heatingRating < 3)    { heatingRating = "2.25 to < 3"; }
                if (heatingRating >= 3    && heatingRating < 3.75) { heatingRating = "3 to < 3.75"; }
                if (heatingRating >= 3)                            { heatingRating = common.symbol("greaterThanEqual") + " 3.75"; }
            }
            else if (useHeatingRating && heatingRating != null && ['GasDucted', 'GasNonDucted'].includes(heatingServiceMatch.serviceType.serviceTypeCode)) {
                if (heatingRating  < 3)                           { heatingRating = "< 3"; }
                if (heatingRating >= 3 && heatingRating < 4.5)    { heatingRating = "3 to < 4.5"; }
                if (heatingRating >= 4.5    && heatingRating < 6) { heatingRating = "4.5 to  < 6"; }
                if (heatingRating >= 6)                           { heatingRating = common.symbol("greaterThanEqual") + " 6"; }
            }

            // Cooling
            let coolingServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "SpaceCoolingSystem" && s.isPrimary);
            let coolingTypesCheck = ['HeatPumpDucted', 'HeatPumpNonDucted'];
            let coolingType = building.categoriesNotRequired['spacecoolingsystem']
                                ? "Other"
                                : coolingServiceMatch != null
                                    ? servicetemplateservice.getMappedSystemType(coolingServiceMatch.serviceType, 'SpaceCoolingSystem', 'exportTitle')
                                    : "";
            let useCoolingRating = !building.categoriesNotRequired['spaceheatingsystem'] && coolingServiceMatch != null && coolingTypesCheck.includes(coolingServiceMatch.serviceType.serviceTypeCode);
            let coolingRating = useCoolingRating ? coolingServiceMatch.starRating2019 : "";
            // Convert rating to be acceptable for xlsx sheet
            if (useCoolingRating && coolingRating != null && ['HeatPumpDucted', 'HeatPumpNonDucted'].includes(coolingServiceMatch.serviceType.serviceTypeCode)) {
                if (coolingRating  < 2.25)                         { coolingRating = "< 2.25"; }
                if (coolingRating >= 2.25 && coolingRating < 3)    { coolingRating = "2.25 to < 3"; }
                if (coolingRating >= 3    && coolingRating < 3.75) { coolingRating = "3 to < 3.75"; }
                if (coolingRating >= 3.75)                         { coolingRating = common.symbol("greaterThanEqual") + " 3.75"; }
            }

            // Pool
            let poolServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "SwimmingPool");
            let poolVolume = poolServiceMatch?.volume ?? null;
            let poolRating = poolServiceMatch?.starRating2019;

            // Spa
            let spaServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "Spa");
            let spaVolume = spaServiceMatch?.volume ?? null;

            // Water Heater
            let hotWaterServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "HotWaterSystem" && s.isPrimary);
            let waterHeaterType = building.categoriesNotRequired['hotwatersystem']
                                    ? "Gas Storage"
                                    : hotWaterServiceMatch != null
                                        ? servicetemplateservice.getMappedSystemType(hotWaterServiceMatch.serviceType, 'HotWaterSystem', 'exportTitle')
                                        : "";

            // Photovoltaic
            let photovoltaicServiceMatch = building.services?.find(s => s.serviceCategory.serviceCategoryCode == "PhotovoltaicSystem");
            let photovoltaicCapacity = !building.categoriesNotRequired['photovoltaicsystem'] && photovoltaicServiceMatch != null
                                    ? photovoltaicServiceMatch.systemCapacity
                                    : null;

            wholeOfHomeExport({
                clientJobNumber: assessment.assessmentProjectDetail.clientJobNumber,
                stateCode: assessment.assessmentProjectDetail.stateCode,
                nccClimateZoneDescription: assessment.nccClimateZone.description,
                totalFloorArea: floorArea,
                nccBuildingClassification: 1,
                heatPumpRatingType: heatPumpType,
                mainSpaceConHeatingType: heatingType,
                mainSpaceConHeatingRating: heatingRating,
                mainSpaceConCoolingType: coolingType,
                mainSpaceConCoolingRating: coolingRating,
                poolVolume: poolVolume,
                poolPumpRating: poolRating,
                spaVolume: spaVolume,
                waterHeaterType: waterHeaterType,
                photovoltaicCapacity: photovoltaicCapacity,
                versionNumber: assessment.assessmentProjectDetail.assessmentVersion.toFixed(1)
            });
        }

        function wholeOfHomeExport(wohData) {

            wohData.poolPumpRating = wohData.poolPumpRating != null ? common.roundToStep(wohData.poolPumpRating, 0.5) : "";

            var url = baseUrl + 'GenerateWholeOfHomeExport';
            return $http.post(url,wohData, { responseType: "blob" })
                        .then(success, fail);

            function success(resp) {
                // Create a dummy anchor element with a data uri and simulate a click on it.
                // This will show the download pop-up to the user.
                var a = window.document.createElement('a');
                a.href = window.URL.createObjectURL(resp.data);
                a.download = wohData.fileName;
                document.body.appendChild(a)
                a.click();

                // Finally, remove our anchor from the dom.
                document.body.removeChild(a);

                log.logSuccess("Whole-of-Home Calculator generated successfully. See downloads.");
                return resp;
            }

            function fail(error) {
                var msg = "Error generating Whole-of-Home Calculator: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

    }
})();
