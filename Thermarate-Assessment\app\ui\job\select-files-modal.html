<!-- FAIRLY CERTAIN THIS IS NOT IN USE ... -->
<md-dialog ng-controller="SelectFilesModalCtrl as vm">
    <form>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Copy Assessment</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="column" layout-align="center center" layout-padding>
            <md-checkbox ng-model="vm.response.copyDrawings" flex="100">
                Copy Drawings
            </md-checkbox>
            <!-- ******** Assessors ******** -->
            <md-input-container class="md-block" flex="100">
                <label>Assigned Assessor</label>
                <md-select name="assignedAssessor"
                           ng-model="vm.response.assessorId">
                    <md-option ng-value="">Do not assign</md-option>
                    <md-option ng-value="item.employeeId"
                               ng-repeat="item in vm.assessorList track by item.userId">
                        {{item.fullName}} <span ng-if="vm.currentAssessorId==item.userId">(Current)</span>
                    </md-option>
                </md-select>
            </md-input-container>
            <md-input-container class="md-block" flex="100">
                <label>Purchase Order</label>
                <input type="text" ng-maxlength="200" ng-model="vm.response.purchaseOrder"/>
            </md-input-container>
        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
            <md-button ng-click="vm.submitSelection()">
                Ok
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>