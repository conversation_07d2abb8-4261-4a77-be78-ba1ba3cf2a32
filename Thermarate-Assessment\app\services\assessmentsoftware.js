// Name: assessmentsoftwareservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'assessmentsoftwareservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', assessmentsoftwareservice]);

    function assessmentsoftwareservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'assessmentsoftware/';
        
        const cache = {
            assessmentSoftware: {}, 
        };
        
        const softwareFileTypes = [
            {
                name: 'Primary Assessment File',
                prop: 'fileA',
            },
            {
                name: 'Secondary Assessment File',
                prop: 'fileB',
            },
            {
                name: 'Tertiary Assessment File',
                prop: 'fileC',
            },
            {
                name: 'Output File',
                prop: 'fileD',
            },
            {
                name: 'Output Summary File',
                prop: 'fileE',
            },
        ];

        const primarySoftwareFileTypes = [
            {
                name: 'Primary Assessment File',
                prop: 'fileA',
            },
            {
                name: 'Secondary Assessment File',
                prop: 'fileB',
            },
            {
                name: 'Tertiary Assessment File',
                prop: 'fileC',
            },
        ];

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            getAll,
            currentFilter: function () { return currentFilter },
            getAssessmentSoftware: getAssessmentSoftware,
            createAssessmentSoftware: createAssessmentSoftware,
            updateAssessmentSoftware: updateAssessmentSoftware,
            deleteAssessmentSoftware:deleteAssessmentSoftware,
            undoDeleteAssessmentSoftware:undoDeleteAssessmentSoftware,
            softwareFileTypes,
            primarySoftwareFileTypes,
        };
            
        return service;
        

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting AssessmentSoftware list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** Just returns ALL items **/
        function getAll() {
            
            //Get error List from the Server 
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(
              r => r.data,
              fail)


            function fail(error) {
                if (error.status === 0 || error.status === -1)
                    return;

                let msg = "Error getting Assessment Software list: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getAssessmentSoftware(assessmentSoftwareCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {assessmentSoftwareCode: assessmentSoftwareCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting AssessmentSoftware: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createAssessmentSoftware(data, ascm) {
            var url = baseUrl + 'Create';
            return $http.post(url, { dto: data, assessmentSoftwareComplianceMethods: ascm }).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Software Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created AssessmentSoftware: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateAssessmentSoftware(data, ascm) {
            var url = baseUrl + 'Update';
            return $http.post(url, { dto: data, assessmentSoftwareComplianceMethods: ascm }).then(success, fail)
            function success(resp) {
                log.logSuccess("Assessment Software Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating AssessmentSoftware: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteAssessmentSoftware(assessmentSoftwareCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { assessmentSoftwareCode: assessmentSoftwareCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting AssessmentSoftware: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteAssessmentSoftware(assessmentSoftwareCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { assessmentSoftwareCode: assessmentSoftwareCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for AssessmentSoftware: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
