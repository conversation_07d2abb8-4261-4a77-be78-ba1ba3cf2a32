﻿<div class="pagination content-center" ng-if="pages.length >= 2">
    <md-content layout-padding layout-align="center center">
        <md-grid-list>
            <md-grid-title>
                <md-button class="md-small" ng-click="selectPage(1)" aria-label="First">
                    <md-icon class="material-icons">first_page</md-icon>
                </md-button>
            </md-grid-title>
            <md-grid-title>
                <md-button class="md-small" ng-click="selectPage(currentPage-1)" aria-label="Previous">
                    <md-icon class="material-icons">chevron_left</md-icon>
                </md-button>
            </md-grid-title>
            <md-grid-title ng-repeat="page in pages"><md-button class="md-small" ng-class="{'md-primary md-raised': page==currentPage}" ng-click="selectPage(page)">{{page}}</md-button></md-grid-title>
            <md-grid-title>
                <md-button class="md-small" ng-click="selectPage(currentPage+1)" aria-label="Next">
                    <md-icon class="material-icons">chevron_right</md-icon>
                </md-button>
            </md-grid-title>
            <md-grid-title>
                <md-button class="md-small" ng-click="selectPage(numPages)" aria-label="Last">
                    <md-icon class="material-icons">last_page</md-icon>
                </md-button>
            <md-grid-title>
        </md-grid-list>
    </md-content>
</div>