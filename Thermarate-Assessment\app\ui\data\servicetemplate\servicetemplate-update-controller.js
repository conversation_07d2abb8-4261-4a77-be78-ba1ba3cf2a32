(function () {
    // The ServiceTemplateUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ServiceTemplateUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'servicetemplateservice', 'manufacturerservice', 'constructionservice', 'security', serviceTemplateUpdateController]);
    function serviceTemplateUpdateController($rootScope, $scope, $mdDialog, $stateParams,
            $state, servicetemplateservice, manufacturerservice, constructionservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = false;
        var eventListenerList = [];
        vm.title = 'Edit Services Template';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.serviceTemplateId = null;

        vm.getMappedSystemType = servicetemplateservice.getMappedSystemType;

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.serviceTemplate = {
            lifeCycleData: {},
        };

        vm.newRecord = vm.isModal && $scope.newRecord !== undefined && $scope.newRecord === true;
        if (vm.newRecord) {
            vm.title = "New Service Template";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord === false) {
                vm.serviceTemplateId = $scope.serviceTemplateId;
            }
            vm.hideActionBar = true;
        } else {
            vm.serviceTemplateId = $stateParams.serviceTemplateId;
        }

        vm.serviceTypes = [];
        if (vm.serviceTemplateId != null) {
            servicetemplateservice.getServiceTemplate(vm.serviceTemplateId).then(data => {
                vm.serviceTemplate = data;
                servicetemplateservice.getServiceTypes().then(data => {
                    vm.serviceTypes = data;
                    if (vm.serviceTemplate.serviceCategory.serviceTypeUIDisplay) {
                        vm.serviceTemplate.serviceCategory.serviceTypesForThisCategory = servicetemplateservice.serviceTypesGrouped(
                            [vm.serviceTemplate.serviceCategory.serviceCategoryCode],
                            'title',
                            vm.serviceTypes
                        )[vm.serviceTemplate.serviceCategory.serviceCategoryCode];
                    }
                });
            });
        }

        // Get required dropdown values.
        vm.manufacturers = [];
        var manufacturerPromise = manufacturerservice.getList()
            .then(data => { vm.manufacturers = data.data; });

        vm.serviceCategories = [];
        servicetemplateservice.getServiceCategories().then(catData => {
            vm.serviceCategories = catData;
            // IF new, get System Types for all Categories
            if (vm.serviceTemplateId == null) {
                servicetemplateservice.getServiceTypes().then(serviceTypesData => {
                vm.serviceTypes = serviceTypesData;
                    vm.serviceCategories.forEach(cat => {
                        if (cat.serviceTypeUIDisplay != null) {
                            cat.serviceTypesForThisCategory = servicetemplateservice.serviceTypesGrouped(
                                [cat.serviceCategoryCode],
                                'title',
                                vm.serviceTypes
                            )[cat.serviceCategoryCode];
                        }
                    }); 
                });
            }
        });

        vm.heatingSystemTypes = [];
        servicetemplateservice.getHeatingSystemTypes().then(data => vm.heatingSystemTypes = data);

        vm.icRatings = [];
        servicetemplateservice.getICRatings().then(data => vm.icRatings = data);

        vm.serviceControlDevices = [];
        servicetemplateservice.getServiceControlDevices().then(data => vm.serviceControlDevices = data);

        vm.serviceFuelTypes = [];
        servicetemplateservice.getServiceFuelTypes().then(data => vm.serviceFuelTypes = data);

        vm.serviceBatteryTypes = [];
        servicetemplateservice.getServiceBatteryTypes().then(data => vm.serviceBatteryTypes = data);

        vm.servicePumpTypes = [];
        servicetemplateservice.getServicePumpTypes().then(data => vm.servicePumpTypes = data);

        vm.unitOfMeasureList = [];
        constructionservice.getUnitOfMeasureList().then(data => vm.unitOfMeasureList = data);

        vm.batteryTypeChange = function() {
            if (vm.serviceTemplate.serviceBatteryType.serviceBatteryTypeCode == 'None') {
                vm.serviceTemplate.batteryCapacity = '0.00';
            }
        }

        // Functions to get data for Typeahead
        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("servicetemplate-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if (vm.newRecord == true) {

                servicetemplateservice.createServiceTemplate(vm.serviceTemplate).then(function (data) {
                    navToNewService(data);
                });

            } else {
                servicetemplateservice.updateServiceTemplate(vm.serviceTemplate).then(function (data) {
                    vm.isBusy = false;
                });
            }

            vm.isBusy = false;
        }

        /**
         * Navigate to construction update page of given id.
         * @param {any} id
         */
        function navToNewService(id) {
            vm.isBusy = false;
            vm.cancel();
            $state.go("servicetemplate-updateform", { serviceTemplateId: id});
        }

        vm.delete = function () {
            vm.isBusy = true;
            servicetemplateservice.deleteServiceTemplate(vm.serviceTemplateId).then(function () {
                vm.serviceTemplate.deleted = true;
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            servicetemplateservice.undoDeleteServiceTemplate(vm.serviceTemplateId).then(function () {
                vm.serviceTemplate.deleted = false;
                vm.isBusy = false;
            });
        }

        vm.heatingSystemTypesForCategoryCode = (code) => servicetemplateservice.heatingSystemTypesForCategoryCode(code, vm.heatingSystemTypes);

        vm.showStarRating           = code => ["SpaceHeatingSystem", "SpaceCoolingSystem", "HotWaterSystem", "SwimmingPool", "Spa", "Cooktop", "Oven"].includes(code);
        vm.showFuelType             = code => ["SpaceHeatingSystem", "SpaceCoolingSystem", "HotWaterSystem", "SwimmingPool", "Spa", "Cooktop", "Oven"].includes(code);
        vm.showPumpType             = code => ["SwimmingPool", "Spa"].includes(code);
        vm.showDucted               = code => ["SpaceHeatingSystem", "SpaceCoolingSystem"].includes(code);
        vm.showFlued                = code => ["SpaceHeatingSystem"].includes(code);
        vm.showLampPowerRating      = code => ["ArtificialLighting", "ExhaustFans", "CeilingFans"].includes(code);
        vm.showControlDevice        = code => ["ArtificialLighting"].includes(code);
        vm.showRecessed             = code => ["ArtificialLighting", "ExhaustFans", "CeilingVents"].includes(code);
        vm.showSealed               = code => ["ArtificialLighting", "ExhaustFans", "CeilingVents"].includes(code);
        vm.showICRating             = code => ["ArtificialLighting"].includes(code);
        vm.showCutOutDiameter       = code => ["ArtificialLighting", "ExhaustFans"].includes(code);
        vm.showLength               = code => ["CeilingVents"].includes(code);
        vm.showWidth                = code => ["CeilingVents"].includes(code);
        vm.showBladeDiameter        = code => ["CeilingFans"].includes(code);
        vm.showPermanentlyInstalled = code => ["CeilingFans"].includes(code);
        vm.showSpeedController      = code => ["CeilingFans"].includes(code);
        vm.showArrayCapacity        = code => ["PhotovoltaicSystem"].includes(code);
        vm.showArea                 = code => ["PhotovoltaicSystem"].includes(code);
        vm.showAzimuth              = code => ["PhotovoltaicSystem"].includes(code);
        vm.showPitch                = code => ["PhotovoltaicSystem"].includes(code);
        vm.showShadeFactor          = code => ["PhotovoltaicSystem"].includes(code);
        vm.showInverterCapacity     = code => ["PhotovoltaicSystem"].includes(code);
        vm.showBatteryType          = code => ["PhotovoltaicSystem"].includes(code);
        vm.showBatteryCapacity      = code => ["PhotovoltaicSystem"].includes(code);
        vm.showVolume               = code => ["SwimmingPool", "Spa"].includes(code);
        vm.showHeatingSystem        = code => ["SwimmingPool", "Spa"].includes(code);
        vm.showCover                = code => ["SwimmingPool", "Spa"].includes(code);
        vm.showTimeSwitch           = code => ["SwimmingPool", "Spa"].includes(code);

        // ---

        vm.setLampPowerIfApplicable = function(serviceType) {
            if(serviceType.serviceTypeCode === "NotApplicable")
                vm.serviceTemplate.lampPowerRating = 0;
        }
    }
})();