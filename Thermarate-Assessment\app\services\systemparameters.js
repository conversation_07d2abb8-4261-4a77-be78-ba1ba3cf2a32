// Name: systemparametersservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'systemparametersservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', systemparametersservice]);

function systemparametersservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var initPromise,
            initFailed;
        var initialised = false;
        var systemparametersData = {};
        var currentFilter = "";
        systemparametersData.systemparametersList = [];

        var service = {
            /* These are the operations that are available from this service. */
            initialise: initialise,
            refresh: refresh,
            currentFilter: function () { return currentFilter },
            systemparametersList: function () { return systemparametersData.systemparametersList },
            systemparameters: function () { return systemparametersData.systemparameters },
            getSystemParameters: function (parmCode) { return getSystemParameters(parmCode) },
            saveSystemParameters: function (systemparameters) { return saveSystemParameters(systemparameters) },
            deleteSystemParameters: function (parmCode) { return deleteSystemParameters(parmCode) },
            saveSystemParametersList: function (systemparametersList) { return saveSystemParametersList(systemparametersList) },
        };

        return service;

        //#region main application operations
        // ----------------------------------

        function initialise() {

            if (initialised == true) {
                return $q.when(); // Already Initialised.
            }

            if (initPromise && !initFailed) {
                return initPromise; // already initialized/ing
            }
            initFailed = false;

            initPromise = null;
            // Get all the data from the server
            initPromise = getData().then(initialised = true);

            return initPromise;

        } // end initialise()


        function refresh(forFilter) {

            initPromise = null;

            // Get all the data from the server
            initPromise = getData(forFilter).then(initialised = true);

            return initPromise;
        }

        function getData(forFilter) {

            // Get the list of systemparameters from the server.
            // The requests are queued into a single promise

            var dataPromise = $q.all([ getSystemParametersList(forFilter),
            ]);

            // Add a timeout for the promise.
            dataPromise = Q.timeout(dataPromise, config.serverTimeoutMs)
                .fail(initialzeServerTimeout)
                .to$q(); // converts Q.js promise to $q promise

            function failure(error) {
                initFailed = true;
                log.logError(error.message, "Data initialization failed", "datacontext", true);
                throw error; // so downstream fail handlers hear it too
            }

            //Time Out Error Message 
            function initialzeServerTimeout(error) {
                if (/timed out/i.test(error.message)) {
                    error.message = 'System ' + error.message + '; System may be currently unavailable.';
                    failure(error);
                }
                throw error;
            }

            return dataPromise;
        }


        //Fetch SystemParameters List from server
        function getSystemParametersList(forFilter) {
            var wkUrl = '../api/systemparameters/Get';  // Gets All SystemParameters Not Deleted
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            switch (forFilter) {
                case 'Deleted':
                    wkUrl = '../api/systemparameters/GetDeleted'; // Gets all deleted systemparameters
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get systemparameters List from the Server 
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {

                systemparametersData.systemparametersList = resp.data; //Assign data to systemparameters List
            }
            function fail(error) {
                var msg = "Error getting systemparameters list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Fetch the requested parmCode from the server
        function getSystemParameters(parmCode) {
            //Get systemparameters from the Server 
            return $http({
                url: '../api/systemparameters/Get/' + parmCode,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {

                systemparametersData.systemparameters = resp.data; //Assign data to systemparameters
            }
            function fail(error) {
                var msg = "Error getting systemparameters list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Save systemparameters Detail on Server Call
        function saveSystemParameters(systemparameters) {
            var isNew = systemparameters.parmCode == null ? true : false;

            //Save SystemParameters Detail on the Server
            return $http.post('../api/systemparameters/Post', systemparameters)
                .then(success, fail);

            function success(resp) {
                systemparametersData.systemparameters = resp.data;
                log.logSuccess('SystemParameters Saved.');
                refresh(currentFilter); // Force the list of systemparameters to be refreshed.
            }
            function fail(error) {
                var msg = "Error saving systemparameters detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Delete systemparameters Detail on Server Call
        function deleteSystemParameters(parmCode) {
            initPromise = null;
            // Delete systemparameters Detail from the server
            initPromise = $http.delete('../api/systemparameters/Delete/' + parmCode).then(success, fail);

            function success(resp) {
                log.logSuccess('SystemParameters Deleted.');
                refresh(currentFilter);  // Force the list of systemparameters to be refreshed.
            }
            function fail(error) {
                var msg = "Error deleting systemparameters detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }

        function saveSystemParametersList(systemparametersList) {

            //Save SystemParameters List on the Server
            return $http.post('../api/systemparameters/SaveSystemParameters', systemparametersList)
                .then(success, fail);

            function success(resp) {
                log.logSuccess('SystemParameters Saved.');
                refresh(currentFilter); // Force the list of systemparameters to be refreshed.
            }
            function fail(error) {
                var msg = "Error saving systemparameters detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

    }

})();
