(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelFilters', {
        bindings: {
            currentList: '<',
            appliedFilters: '<',
            settings: '<',
            filterCountData: '<',
            onFilterChanged: '<',
            onSortChanged: '<',
            totalItemsWithoutFilters: '<',
            currentTotal: '<'
          },
          templateUrl: 'app/ui/energy-labs/standard-model-filters.html',
          controller: StandardModelFiltersController,
          controllerAs: 'vm'
        });

  StandardModelFiltersController.$inject = ['common', 'standardmodelservice'];

  function StandardModelFiltersController(common, standardmodelservice) {

    const vm = this;

    // Filtering
    vm.filters = standardmodelservice.multiFiltersFields;

    vm.featuresSections = standardmodelservice.buildFeaturesDropdownSections();
    vm.featureSectionHasItems = function (section) {
        return section.features.some(feature => vm.filterCountData['features'][feature] > 0);
    }
    vm.categories = standardmodelservice.categories;
    vm.featureName = standardmodelservice.featureName;
    vm.keyToName = standardmodelservice.keyToName;
    vm.toSplitTitleCase = common.toSplitTitleCase;

    vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
    vm.anyFiltersApplied = common.anyFiltersApplied;

    vm.sortByOptions = [
        { code: "modifiedOn", description: "Date Modified" },
        { code: "displayFloorArea", description: "House Area" },
        { code: "depth", description: "Lot Length" },
        { code: "width", description: "Lot Width" },
        { code: "title", description: "Name" },
    ];
    vm.defaultSort = "title";
    vm.sort = {};

    vm.initialize = function () {
        // Initially set all dropdowns to 'Any'
        vm.filters.forEach(f => vm.appliedFilters[f.field] = ['Any']);
        vm.appliedFilters.features = ['Any'];
        vm.appliedFilters.categories = ['Any'];
        setTimeout(() => vm.initialised = true, 100);
    }
    vm.initialize();

    /**
     * Determines what the md-select should say when in the 'closed' position,
     * based on how many values are selected etc
     */
    vm.filterText = function(field) {
        const selectedCount = vm.appliedFilters[field]?.length;

        if (selectedCount === 1) {
            if (vm.appliedFilters[field][0] == 'Any') {
                return 'Any';
            }
            else if (field == "features" || field == "categories") {
                return vm.featureName(vm.appliedFilters[field][0]);
            } else {
                return vm.appliedFilters[field][0];
            }
        } else {
            return `${selectedCount} Selected`;
        }
    }

    var filtersToApply = {}; // To pass to service to actually filter and keep track of what was selected before
    vm.filterChanged = function () {
        if (vm.initialised) {
            filtersToApply = getFilterDataForService(vm.appliedFilters, filtersToApply);
            if (vm.onFilterChanged) {
                vm.onFilterChanged({ filterData: filtersToApply });
            }
            vm.filtersApplied = vm.anyFiltersApplied(null, vm.filters, vm.appliedFilters);
        }
    }

    function getFilterDataForService(uiData, oldServiceData) {
        // Set selections based on if 'Any' option logic
        common.applyMultiSelectChangedLogic(uiData, oldServiceData);
        // Copy UI filters to send to service
        let newServiceData = angular.copy(uiData);
        // Remove 'Any' filters so those filters ignored
        for (const key in newServiceData) {
            if (newServiceData[key][0] == 'Any') {
                delete newServiceData[key];
            }
        }
        return newServiceData;
    }

    vm.applySort = function() {
        vm.onSortChanged(vm.sort?.sortBy);
    }

    vm.clearFilter = function (filter) {
        vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
        vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);
        vm.filterChanged();
    }
    vm.clearFilters = function () {
        for (const key in vm.appliedFilters) {
            vm.appliedFilters[key] = [];
        }
        vm.filterChanged();
    }

  }

})();