(function () {
    // The ColourUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ColourUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'colourservice', 'manufacturerservice', 'uuid4', 'security', colourUpdateController]);
    function colourUpdateController($rootScope, $scope, $mdDialog, $stateParams,
        $state, colourservice, manufacturerservice, uuid4, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Colour';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.colour = {
            colourId: uuid4.generate(),
            title: null,
            solarAbsorptance: null,
            manufacturer: null
        };

        vm.manufacturers = [];
        var manufacturerPromise = manufacturerservice.getList()
            .then(data => { vm.manufacturers = data.data; });

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Colour";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.colourId = $scope.colourId;
            }
            vm.hideActionBar = true;
        } else {
            vm.colourId = $stateParams.colourId;
        }

        // Get data for object to display on page
        var colourCodePromise = null;
        if (vm.colourId != null) {
            colourCodePromise = colourservice.getColour(vm.colourId)
            .then(function (data) {
                if (data != null) {
                    vm.colour = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("colour-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                colourservice.createColour(vm.colour).then(function(data){
                    vm.colour = data;
                    vm.colourId = vm.colour.colourId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                colourservice.updateColour(vm.colour).then(function(data){
                    if (data != null) {
                        vm.colour = data;
                        vm.colourId = vm.colour.colourId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            colourservice.deleteColour(vm.colourId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            colourservice.undoDeleteColour(vm.colourId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();