<section id="construction-list-view"
         class="main-content-wrapper"
         data-ng-controller="ConstructionListCtrl as vm">

    <div class="widget">

        <div layout="row"
             style="padding: 10px 0px; ">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
            <div layout-align="center center"
                 style="margin-top: 4px; margin-left: 20px;">
                <md-button class="md-primary"
                           ngf-select="vm.uploadFile($file)"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Upload Spreadsheet
                </md-button>
                <md-button class="md-primary"
                           ng-click="vm.exportDatabase()"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Export
                </md-button>
            </div>
        </div>

        <md-toolbar class="widget-actionbar-wrapper header-container {{vm.filtersExpanded ? 'header-container-expanded' : null}}" flex>
            <div class="md-toolbar-tools widget-actionbar header-inner-container">
                <div class="filters-container">
                    <!-- Search -->
                    <div class="filter">
                        <div class="filter-label">Search</div>
                        <div class="search-input-container">
                            <input class="search-input"
                                   type="text"
                                   placeholder="Quick Filter"
                                   ng-model="vm.searchString"
                                   ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                   ng-blur="vm.refreshList()">
                            <img src="/content/images/cross.png"
                                 class="search-clear-button"
                                 ng-show="vm.searchString"
                                 ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                        </div>
                    </div>

                    <!-- Multi-Filters -->
                    <div ng-repeat="filter in vm.filters track by filter.field"
                         ng-if="filter.name != null"
                         ng-show="filter.section == 1 || vm.filtersExpanded"
                         class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                        <!-- Label -->
                        <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                        <!-- Clear Button -->
                        <img src="/content/images/cross.png"
                             class="filter-clear-button"
                             ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                             ng-show="vm.anyOptionsSelectedOnField(filter, vm.appliedFilters)"
                        />

                        <!-- Filter Options -->
                        <md-select ng-model="vm.appliedFilters[filter.field]"
                                  class="filter-dropdown"
                                  multiple
                                  placeholder="Any"
                                  md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                                  ng-change="vm.refreshList()">
                            <md-option ng-repeat="option in vm.filterOptions[filter.field] track by option.value"
                                      ng-show="vm.getFilterCountItem(filter, option) > 0"
                                      ng-value="option.value">
                                {{option.name}} ({{vm.getFilterCountItem(filter, option)}})
                            </md-option>
                        </md-select>

                        <!-- More/Less Button -->
                        <div ng-if="filter.name == '[moreLessButton]'"
                             class="more-filters-button {{vm.filtersExpanded ? 'adjust-padding' : null}}"
                             ng-click="vm.filtersExpanded = !vm.filtersExpanded"
                        >
                            <img class="more-filters-icon" src="../../../content/images/settings.png" /> {{vm.filtersExpanded ? 'Less Filters' : 'More Filters'}}
                        </div>

                        <!-- Blank -->
                        <div ng-if="filter.name == '[blank]'"></div>
                    </div>
                </div>
            </div>

            <!-- Number of Items -->
            <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="!vm.filtersApplied">
                Showing {{vm.totalConstructions}} {{$stateParams.type.toLowerCase()}} items
            </div>
            <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="vm.filtersApplied">
                {{vm.totalFilteredConstructions}} of {{vm.totalConstructions}} {{$stateParams.type.toLowerCase()}} items match your filters
                (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
            </div>
        </md-toolbar>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                   st-table="vm.constructionList"
                   st-table-filtered-list="exportList"
                   st-global-search="vm.listFilter"
                   st-persist="constructionList"
                   st-pipe="vm.orderBy"
                   st-sticky-header>
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="vm.bulkSelected"
                                             ng-click="vm.bulkSelect(!vm.bulkSelected);"></md-checkbox>
                            </div>
                        </th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="constructionCategoryTitle" class="can-sort text-center">Category</th>
                        <!-- Frame column for opening records only -->
                        <th ng-if="!vm.isConstruction()" st-sort="frameMaterialTitle" class="can-sort text-center">Frame</th>
                        <!-- Conditional -->
                        <th ng-if="vm.isConstruction()" st-sort="constructionSubCategoryTitle" class="can-sort text-center">Construction Type</th>
                        <th ng-if="!vm.isConstruction()" st-sort="openingStyleTitle" class="can-sort text-center">Opening Style</th>
                        <!-- Manufacturer column for opening records only -->
                        <th ng-if="!vm.isConstruction()" st-sort="manufacturerDescription" class="can-sort text-center">Manufacturer</th>
                        <!-- Glass Type column for opening records only -->
                        <th ng-if="!vm.isConstruction()" st-sort="glassTypeTitle" class="can-sort text-center">Glass Type</th>
                        <!-- Glass Colour column for opening records only -->
                        <th ng-if="!vm.isConstruction()" st-sort="glassColourTitle" class="can-sort text-center">Glass Colour</th>
                        <!-- Low-E column for opening records only -->
                        <th ng-if="!vm.isConstruction()" st-sort="lowECoating" class="can-sort text-center">Low-E</th>
                        <!-- Insulation column only for construction type -->
                        <th ng-if="vm.isConstruction()" st-sort="insulationDescription" class="can-sort text-center">Insulation</th>
                        <th st-sort="isFavourite" class="can-sort text-center" style="width: 1%; white-space: nowrap;">Favourite</th>
                        <th style="width: 50px;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.constructionList track by row.constructionId" class="list-row clickable">
                        <td>
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-disabled="vm.isPermanentOpening(row)"
                                             ng-model="row.isBulkSelected"
                                             ng-click="$event.stopPropagation();">
                                </md-checkbox>
                            </div>
                        </td>
                        <td data-title="Description" ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{::row.description }}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Category"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.constructionCategoryTitle}}
                        </td>
                        <!-- Frame column for opening records only -->
                        <td ng-if="!vm.isConstruction()"
                            data-title="Frame"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.frameMaterialTitle == 'Not Specified' ? '' : row.frameMaterialTitle}}
                        </td>
                        <!-- Conditional -->
                        <td ng-if="vm.isConstruction()"
                            data-title="ConstructionType"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.constructionSubCategoryTitle}}
                        </td>
                        <td ng-if="!vm.isConstruction()"
                            data-title="OpeningStyle"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.openingStyleTitle}}
                        </td>
                        <!-- Manufacturer column for opening records only -->
                        <td ng-if="!vm.isConstruction()"
                            data-title="Manufacturer"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.manufacturerDescription == 'Not Specified' ? '' : row.manufacturerDescription}}
                        </td>
                        <!-- Glass Type column for opening records only -->
                        <td ng-if="!vm.isConstruction()"
                            data-title="GlassType"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.glassTypeTitle == 'Not Specified' ? '' : row.glassTypeTitle}}
                        </td>
                        <!-- Glass Colour column for opening records only -->
                        <td ng-if="!vm.isConstruction()"
                            data-title="GlassColour"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.glassColourTitle == 'Not Specified' ? '' : row.glassColourTitle}}
                        </td>
                        <!-- Low-E column for opening records only -->
                        <td ng-if="!vm.isConstruction()"
                            data-title="LowE"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.lowECoating == 'Not Specified' ? '' : row.lowECoating}}
                        </td>
                        <!-- Insulation column only for construction type -->
                        <td ng-if="vm.isConstruction()"
                            data-title="Insulation"
                            class="text-center"
                            ng-click="vm.goToConstruction(row.constructionId, row.type)">
                            {{::row.insulationDescription == 'Not Specified' ? '' : row.insulationDescription}}
                        </td>
                        <td data-title="Favourite" class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-checkbox ng-model="row.isFavourite"
                                             style="margin: 0;"
                                             ng-click="vm.setFavouriteStatus(row.constructionId, !row.isFavourite);$event.stopPropagation();"/>
                            </div>
                        </td>
                        <td class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-menu>
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu(); $event.stopPropagation();"
                                         src="/content/feather/more-horizontal.svg"/>
                                    <md-menu-content>
                                        <!-- Duplicate -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.clone(row)" ng-disabled="vm.isPermanentOpening(row)">
                                                Duplicate
                                            </md-button>
                                        </md-menu-item>
                                        <md-menu-divider></md-menu-divider>
                                        <!-- Delete -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.delete(row)" ng-disabled="vm.isPermanentOpening(row)">
                                                <span style="color: orangered;">Delete</span>
                                            </md-button>
                                        </md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr style="background-color: rgb(250, 250, 250);">
                        <td colspan="9999" class="text-center">
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr;">

                                <!-- Just empty. -->
                                <div></div>

                                <!-- Pagination Display -->
                                <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                                <!-- Just empty. -->
                                <div></div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager" style="text-align: center;">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: -36px; margin-left: 10px;">
                <!-- Bulk Edit Button -->
                <md-button ng-click="vm.showBulkEditModal()"
                           ng-disabled="!vm.bulkSelectionsExist()"
                           class="md-raised md-primary"
                           style="margin-bottom: 20px;">
                    BULK EDIT
                </md-button>

                <div></div>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>
    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

    .go-to-variation-button:hover {
        background-color: #d1d1d1;
    }

    .go-to-variation-button > img {
        position: absolute;
        top: 50%;
        left: 54%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: auto;
    }

    .header-container {
        min-height: 150px !important;
        justify-content: center;
    }

    .header-container-expanded {
        min-height: 400px !important;
    }

    .header-inner-container {
        gap: 1%;
        max-height: max-content;
        height: max-content;
        align-items: flex-start;
    }

    /* Multi-filter styles copied from Jobs page */
    .filters-container {
        flex: 10;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        column-gap: 2%;
        row-gap: 15px;
    }

    .filter {
        position: relative;
        margin-top: 32px; /* To account for heading above that is positioned absolute */
        font-size: 1.2rem !important;
    }

    .search-input-container {
        position: relative;
        width: 100%;
    }

    .search-input {
        width: 100%;
        height: 45px;
        margin-top: -6px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        border: none;
        background-color: #e2e2e2;
        color: black !important;
    }

    .search-input:focus-visible {
        border: none !important;
    }

    .search-input::placeholder {
        color: black !important;
    }

    .search-input:-ms-input-placeholder {
        color: black !important;
    }

    .search-clear-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .search-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter-label {
        position: absolute;
        top: -32px;
        left: 15px;
        font-size: 12px;
        color: #8e888e;
    }

    .filter-dropdown {
        margin: -6px 0 0 2px;
        width: 100%;
        height: 45px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        background-color: #e2e2e2;
        color: black !important;
    }

    .filter-dropdown span {
        margin-top: -2px;
    }

    .filter-dropdown .md-select-placeholder {
        color: black !important;
    }

    .filter-dropdown .md-select-value {
        border-bottom: none !important;
    }

    .filter-clear-button {
        display: none;
        position: absolute;
        right: 3px;
        bottom: 13px;
        z-index: 50;
        width: 16px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .filter-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter.options-selected > .filter-clear-button {
        display: inherit !important;
    }

    .filter.options-selected .md-select-icon {
        margin-left: -36px;
    }

    .current-filter-description {
        margin-left: 20px;
        margin-top: 20px;
        color: #7b7b7b;
        font-size: 12px;
    }

    .current-filter-description.add-margin {
        margin-top: 30px;
    }

    /* More/Less Filters button styles - copied exactly from Jobs/Home pages */
    .more-filters-button {
        margin-top: -6px;
        position: relative;
        height: 45px;
        padding-top: 17px;
        padding-left: 70px;
        background-color: white;
        color: #8e888e;
        cursor: pointer;
        user-select: none;
    }

    .more-filters-button.adjust-padding {
        padding-top: 15px;
    }

    .more-filters-icon {
        position: absolute;
        left: 25px;
        top: 11px;
        width: 24px;
        height: 24px;
        margin-right: 10px;
        opacity: 0.7;
    }

</style>
