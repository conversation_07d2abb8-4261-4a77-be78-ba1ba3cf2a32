﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using OfficeOpenXml;

namespace TenureExtraction
{
    public class WholeOfHomeEfficiencyFactorExtractor
    {

        // NON-0 Indexed values

        private static string DIRECTORY = "/content";
        
        
        private const int DATA_START_ROW = 5;
        
        private static int[] ClimateZones => new[] { 1, 2, 3, 4, 5, 6, 7, 8 };

        private static string[] States => new[]
        {
            "NSW",
            "VIC",
            "QLD",
            "SA",
            "WA",
            "TAS",
            "NT",
            "ACT",
        };
        
        public static List<WholeOfHomeEfficiencyFactorRow> Extract(string directory, Stream stream)
        {
            if (Directory.Exists(directory) == false)
                Directory.CreateDirectory(directory);

            const string xlsxFileName = "/woh_TEMP.zip";

            // Save contents of stream to a zip file
            var fileStream = File.Create(directory + xlsxFileName);
            stream.Position = 0;
            stream.CopyTo(fileStream);
            fileStream.Close();

            var allRows = Extract(directory + xlsxFileName);
            
            return allRows;
        }

        public static List<WholeOfHomeEfficiencyFactorRow> Extract(string pathToExcelFile)
        {
            var allRows = new List<WholeOfHomeEfficiencyFactorRow>();

            FileInfo existingFile = new FileInfo(pathToExcelFile);
            using (ExcelPackage package = new ExcelPackage(existingFile))
            {
                foreach (string state in States)
                {
                    allRows.AddRange(ExtractSheet(package, state));
                }
            }
            
            return allRows;
        }
        
        /// <summary>
        /// Returns a dictionary where
        ///     Key = Climate Zone
        ///     Value: EnergyLoadLimitRow 
        /// </summary>
        public static List<WholeOfHomeEfficiencyFactorRow> ExtractSheet(ExcelPackage package, string stateCode)
        {
            var matrix = ExtractInner(package, stateCode);
            return matrix;
        }

        public static List<WholeOfHomeEfficiencyFactorRow> ExtractInner(ExcelPackage package, string stateCode)
        {
            var rows = new List<WholeOfHomeEfficiencyFactorRow>();
            var errors = new List<Exception>();
            
            ExcelWorksheet worksheet = package.Workbook.Worksheets[stateCode + " (EE)"];
            
            int row = DATA_START_ROW;
            
            string currentHeatingTypeValue = "";
            string currentHeatingRating2012Value = ""; 
            string currentHeatingRating2019Value = ""; 
            
            string currentCoolingTypeValue = "";
            string currentCoolingRating2012Value = "";
            string currentCoolingRating2019Value = "";

            do
            {

                string heatingType = worksheet.Cells[row, Columns.SpaceHeatingType].Value as string;
                string heatingRating2012 = worksheet.Cells[row, Columns.SpaceHeatingRating2012].Value as string;
                string heatingRating2019 = worksheet.Cells[row, Columns.SpaceHeatingRating2019].Value as string;

                string coolingType = worksheet.Cells[row, Columns.SpaceCoolingType].Value as string;
                string coolingRating2012 = worksheet.Cells[row, Columns.SpaceCoolingRating2012].Value as string;
                string coolingRating2019 = worksheet.Cells[row, Columns.SpaceCoolingRating2019].Value as string;

                double? factorCheck = worksheet.Cells[row, Columns.FirstFactorColumn].Value as double?;

                // The absence of data in both of these columns indicates we are finished processing.
                if (heatingType == null && factorCheck == null)
                    break;

                // Check to see if we have moved onto a new 'block' yet (merged rows in excel)
                if (heatingType == null)
                    heatingType = currentHeatingTypeValue;
                else
                    currentHeatingTypeValue = heatingType;

                // These values can be 'null' as a string but contain a value of 0.0 as a decimal...
                if (heatingRating2012 == null && worksheet.Cells[row, Columns.SpaceHeatingRating2012].Value == null)
                    heatingRating2012 = currentHeatingRating2012Value;
                else
                    currentHeatingRating2012Value = heatingRating2012;

                if (heatingRating2019 == null && worksheet.Cells[row, Columns.SpaceHeatingRating2019].Value == null)
                    heatingRating2019 = currentHeatingRating2019Value;
                else
                    currentHeatingRating2019Value = heatingRating2019;

                if (coolingRating2012 == null && worksheet.Cells[row, Columns.SpaceCoolingRating2012].Value == null)
                    coolingRating2012 = currentCoolingRating2012Value;
                else
                    currentCoolingRating2012Value = coolingRating2012;

                if (coolingRating2019 == null && worksheet.Cells[row, Columns.SpaceCoolingRating2019].Value == null)
                    coolingRating2019 = currentCoolingRating2019Value;
                else
                    currentCoolingRating2019Value = coolingRating2019;

                if (coolingType == null)
                    coolingType = currentCoolingTypeValue;
                else
                    currentCoolingTypeValue = coolingType;


                // Get GEMS rating info.
                var heatingRatingMinMax2012 = DetermineGemsRating(heatingRating2012);
                var heatingRatingMinMax2019 = DetermineGemsRating(heatingRating2019);
                var coolingRatingMinMax2012 = DetermineGemsRating(coolingRating2012);
                var coolingRatingMinMax2019 = DetermineGemsRating(coolingRating2019);


            // Loop over every NCC Climate zone and add 1 row per zone.
                int columnOffset = 0;

                do
                {
                    string climateZoneDesc =
                        worksheet.Cells[2, Columns.FirstFactorColumn + columnOffset].Value as string;

                    if (climateZoneDesc == null)
                        break;
                    
                    int climateZone = Convert.ToInt32(climateZoneDesc.Substring(climateZoneDesc.Length - 1, 1));

                    try
                    {
                        decimal electricStorageStandard =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 0]
                                .Value);
                        decimal electricStorageOffPeak =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 1]
                                .Value);
                        decimal heatPumpStandard =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 2]
                                .Value);
                        decimal heatPumpOffPeak =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 3]
                                .Value);
                        decimal solarElectricStandard =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 4]
                                .Value);
                        decimal gasStorage =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 5]
                                .Value);
                        decimal gasInstantaneous =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 6]
                                .Value);
                        decimal solarGas =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 7]
                                .Value);
                        decimal otherOrNoneSpecifed =
                            Convert.ToDecimal(worksheet.Cells[row, Columns.FirstFactorColumn + columnOffset + 8]
                                .Value);
                        
                        // Some of these values will have to be transformed from within the main project.
                        rows.Add(new WholeOfHomeEfficiencyFactorRow()
                        {
                            Identifier = $"{stateCode}:NCC{climateZone}:Row{row}",
                            StateOfAustralia = stateCode,
                            NccClimateZone = climateZone,

                            SpaceHeatingSystemType = heatingType,
                            SpaceHeatingSystemMinEnergyRatingGems2012 = heatingRatingMinMax2012.min,
                            SpaceHeatingSystemMaxEnergyRatingGems2012 = heatingRatingMinMax2012.max,
                            SpaceHeatingSystemMinEnergyRatingGems2019 = heatingRatingMinMax2019.min,
                            SpaceHeatingSystemMaxEnergyRatingGems2019 = heatingRatingMinMax2019.max,
                        
                            SpaceCoolingSystemType = coolingType,
                            SpaceCoolingSystemMinEnergyRatingGems2012 = coolingRatingMinMax2012.min,
                            SpaceCoolingSystemMaxEnergyRatingGems2012 = coolingRatingMinMax2012.max,
                            SpaceCoolingSystemMinEnergyRatingGems2019 = coolingRatingMinMax2019.min,
                            SpaceCoolingSystemMaxEnergyRatingGems2019 = coolingRatingMinMax2019.max,
                            
                            ElectricStorageStandard = electricStorageStandard,
                            ElectricStorageOffPeak = electricStorageOffPeak, 
                            HeatPumpStandard = heatPumpStandard,       
                            HeatPumpOffPeak = heatPumpOffPeak,        
                            SolarElectricStandard = solarElectricStandard,  
                            GasStorage = gasStorage,             
                            GasInstantaneous = gasInstantaneous,       
                            SolarGas = solarGas,               
                            OtherOrNoneSpecified = otherOrNoneSpecifed

                        });

                    }
                    catch (Exception e)
                    {
                        errors.Add(e);
                    }
                    
                    
                    columnOffset += 9;


                } while (true);
                
                row++;
                
            } while (true);

            // TODO: Return all errors, not just the first... (not important atm)
            if (errors.Count > 0)
                throw new Exception("Errors encountered while processing dataset: ", errors[0]);

            return rows;
        }
        

        private static Regex ratingRangeRegex = new Regex(@"≥\s*(?<min>[\d*.\d]*)\s*AND\s*<\s(?<max>[\d*.\d*]*)");

        private static (decimal min, decimal max) DetermineGemsRating(string rating)
        {
            // Note: This is not an exhaustive check of every possible combination, it only has enough logic to handle
            // the required data as of the 15-06-2022 dataset.

            if (rating == null)
                return (min: 0, max: 0);
            
            if (rating.Contains("≥") && rating.Contains("<"))
            {
                var m1 = ratingRangeRegex.Match(rating);

                if (m1.Length != 0)
                {
                    // We have a match, so extract the non capture group.
                    var minStr = m1.Groups["min"].Value.Trim();
                    var maxStr = m1.Groups["max"].Value.Trim();
                    
                    var min = Convert.ToDecimal(minStr);
                    var max = Convert.ToDecimal(maxStr);
                    return (min, max);
                    ;
                }

                throw new Exception("Regex failed to find match in rating. Rating  was: " + rating);
                
            }
            
            if (rating.Contains("<"))
            {
                var value = Convert.ToDecimal(rating.Substring(2));
                return (min: 0, max: value);
            }

            if (rating.Contains("≥"))
            {
                var value = Convert.ToDecimal(rating.Substring(2));
                return (min: value, max: 99);
            }
            
            throw new NotImplementedException(
                "Unknown combination of logic encountered when determining min/max energy rating. Logic was: "  + rating);

        }
    }


    internal static class Columns
    {
        internal const int SpaceHeatingType         = 2;
        internal const int SpaceHeatingRating2012   = 3;
        internal const int SpaceHeatingRating2019   = 4;
        
        internal const int SpaceCoolingType        = 5;
        internal const int SpaceCoolingRating2012  = 6;
        internal const int SpaceCoolingRating2019  = 7;

        /// <summary>
        /// Used here purely to check the exists of any value.
        /// </summary>
        internal const int FirstFactorColumn = 8;
    }
    
    internal static class ClimateZoneFactorColumns
    {
        internal const int ElectricStorageStandard = 0;
        internal const int ElectricStorageOffPeak  = 1;
        internal const int HeatPumpStandard        = 2;
        internal const int HeatPumpOffPeak         = 3;
        internal const int SolarElectricStandard   = 4;
        internal const int GasStorage              = 5;
        internal const int GasInstantaneous        = 6;
        internal const int SolarGas                = 7;
        internal const int OtherOrNoneSpecified    = 8;
    }
    
    
    public struct WholeOfHomeEfficiencyFactorRow
    {
        public string  Identifier { get; set; }
        public string  StateOfAustralia { get; set; }
        public int     NccClimateZone { get; set; }
        
        public string  SpaceHeatingSystemType { get; set; }
        public decimal SpaceHeatingSystemMinEnergyRatingGems2012 { get; set; }
        public decimal SpaceHeatingSystemMaxEnergyRatingGems2012 { get; set; }
        public decimal SpaceHeatingSystemMinEnergyRatingGems2019 { get; set; }
        public decimal SpaceHeatingSystemMaxEnergyRatingGems2019 { get; set; }
        public string  SpaceCoolingSystemType { get; set; }
        public decimal SpaceCoolingSystemMinEnergyRatingGems2012 { get; set; }
        public decimal SpaceCoolingSystemMaxEnergyRatingGems2012 { get; set; }
        public decimal SpaceCoolingSystemMinEnergyRatingGems2019 { get; set; }
        public decimal SpaceCoolingSystemMaxEnergyRatingGems2019 { get; set; }
        
        public decimal ElectricStorageStandard { get; set; }
        public decimal ElectricStorageOffPeak { get; set; }
        public decimal HeatPumpStandard { get; set; }
        public decimal HeatPumpOffPeak { get; set; }
        public decimal SolarElectricStandard { get; set; }
        public decimal GasStorage { get; set; }
        public decimal GasInstantaneous { get; set; }
        public decimal SolarGas { get; set; }
        public decimal OtherOrNoneSpecified { get; set; }


        public override string ToString()
        {
            return $"{SpaceHeatingSystemType}, {SpaceCoolingSystemType}";
        }
    }


}
