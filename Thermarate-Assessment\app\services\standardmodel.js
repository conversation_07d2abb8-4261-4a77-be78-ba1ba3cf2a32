// Name: standardmodelservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'standardmodelservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'uuid4', 'Upload', standardmodelservice]);

    function standardmodelservice(common, config, $http, uuid4, Upload) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'standardmodel/';


        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);

            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }

            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting StandardModel list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: false,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting StandardModel List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }

        function getForProject(projectId, sort, ignoreVariation = false) {
            var params = { projectId: projectId, ignoreVariation: ignoreVariation };
            params = common.buildqueryparameters.build(params, null, null, sort, null, null);
            return $http({
                url: baseUrl + 'GetForProject',
                method: 'GET',
                params: params,
                cache: false,
            }).then(success, fail)

            function success(resp) { return resp?.data }
            function fail(error) {
                var msg = "Error getting StandardModel List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getForProjectMultiFiltered(projectId, requestData) {

            canceller = $q.defer();
            var wkUrl = `${baseUrl}GetForProjectMultiFiltered?projectId=${projectId}`;

            return $http.post(wkUrl, requestData).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Standard Models list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getVariationFromSelections(isVariationOfHomeModelId, floorplanId, designOptionId, facadeId, specificationId, configurationId, getFull) {
            var params = {
                isVariationOfHomeModelId: isVariationOfHomeModelId,
                floorplanId: floorplanId,
                designOptionId: designOptionId,
                facadeId: facadeId,
                specificationId: specificationId,
                configurationId: configurationId,
                getFull
             };
            return $http({
                url: baseUrl + (getFull ? 'GetVariationFromSelectionsFull' : 'GetVariationFromSelections'),
                method: 'GET',
                params: params,
                cache: false,
            }).then(success, fail)

            function success(resp) { return resp?.data }
            function fail(error) {
                var msg = "Error getting StandardModel List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getModelVariations(homeModelId) {
            var params = { homeModelId: homeModelId };
            return $http({
                url: baseUrl + 'GetModelVariations',
                method: 'GET',
                params: params,
                cache: false,
            }).then(success, fail)

            function success(resp) { return resp?.data }
            function fail(error) {
                var msg = "Error getting StandardModel List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getModelVariationsQuery(homeModelId, forFilter, pageSize, pageIndex, sort, filter, aggregate) {
            var params = { homeModelId: homeModelId };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            return $http({
                url: baseUrl + 'GetModelVariationsQuery',
                method: 'GET',
                params: params,
                cache: false,
            }).then(success, fail)

            function success(resp) { return resp?.data }
            function fail(error) {
                var msg = "Error getting StandardModel List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getStandardModel(id) {
            return $http({
                url: baseUrl + 'Get',
                params: { id },
                method: 'GET',
                cache: false,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getStandardModelOptions(id) {
            return $http({
                url: baseUrl + 'GetOptions',
                params: { id },
                method: 'GET',
                cache:false,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getStandardModelOption(id) {
            return $http({
                url: baseUrl + 'GetOption',
                params: { id },
                method: 'GET',
                cache: false,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Standard Model Option: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getVariationOptionsForStage(forStage, selectedOptions) {
            var url = `${baseUrl}GetVariationOptionsForStage?forStage=${forStage}`;
            return $http.post(url, selectedOptions).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error getting Standard Model Options: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createStandardModel(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("StandardModel Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateStandardModel(data, logSuccess = true) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                if (logSuccess) {
                    log.logSuccess("StandardModel Changes Saved");
                }
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateVariableSettingsInAllModels(projectId) {
            var url = baseUrl + 'UpdateVariableSettingsInAllModels?projectId='+projectId;
            return $http.post(url).then(success, fail)
            function success(resp) {
                log.logSuccess("Home Designs updated.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Home Designs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateServicesDefaultsInAllModels(projectId, parentModelId, fieldsToCopy, newData) {
            var url = `${baseUrl}UpdateServicesDefaultsInAllModels?projectId=${projectId}`;
            if (parentModelId != null) {
                url += `&parentModelId=${parentModelId}`;
            }
            return $http({
                url: url,
                method: 'POST',
                params: { projectId },
                data: { fieldsToCopy: fieldsToCopy, newData: newData }
            }).then(success, fail)
            function success(resp) {
                log.logSuccess("Home Designs updated.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Home Designs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateCostEstimatesInAllModels(projectId, costEstimates) {
            var url = baseUrl + 'UpdateCostEstimatesInAllModels?projectId='+projectId;
            return $http.post(url, costEstimates).then(success, fail)
            function success(resp) {
                log.logSuccess("Home Designs updated.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Home Designs: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateVariationsList(variationsLis) {
            var url = baseUrl + 'UpdateVariationsList';
            return $http.post(url, variationsLis).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateModelsList(modelList) {
            return $http({
                url: baseUrl + 'UpdateModelsList',
                data: modelList,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error updating projects' SortOrders: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateZoneSummaryBuildingData(variationId, summaryBuilding) {
            return $http({
                url: baseUrl + 'UpdateZoneSummaryBuildingData?variationId=' + variationId,
                data: summaryBuilding,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error updating projects' SortOrders: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyCategoriesToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyCategoriesToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Categories have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Categories: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyVariationOptionsToModel(fromModelId, toModelId) {
            const url = baseUrl + 'CopyVariationOptionsToModel';
            return $http({
                url,
                method: "POST",
                params: { fromModelId, toModelId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Variation Options have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Variation Options: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyDesignMetadataToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyDesignMetadataToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Design Metadata has been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Design Metadata: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyFloorMetricsToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyFloorMetricsToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Floor Area and Glazing Metrics have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Floor Area and Glazing Metrics: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyDrawingAreasToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyDrawingAreasToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Drawing Areas have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Drawing Areas: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyDesignMetricsToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyDesignMetricsToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Drawing Areas have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Drawing Areas: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyFeaturesToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyFeaturesToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Features have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Features: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyVariablesToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyVariablesToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Variables have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Variables: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyServiceDefaultsToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyServiceDefaultsToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Services Defaults have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Service Defaults: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyCostEstimatesToVariation(fromVariationId, toVariationId) {
            const url = baseUrl + 'CopyCostEstimatesToVariation';
            return $http({
                url,
                method: "POST",
                params: { fromVariationId, toVariationId }
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Cost Estimates have been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Cost Estimates: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function copyDesignInsightsToVariation(toVariationId, insights) {
            const url = baseUrl + 'CopyDesignInsightsToVariation';
            return $http({
                url,
                method: "POST",
                params: { toVariationId },
                data: insights
            }).then(success, fail);
            function success(resp) {
                log.logSuccess("Design Insight has been copied.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to copy Design Insight: " + error.data.exceptionMessage;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function deleteStandardModel(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteStandardModel(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for StandardModel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyStandardModel(modelId, projectId, variationOfId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { modelId, projectId, variationOfId },
                method: 'POST',
            }).then(success, fail)

            function success(resp) {
                log.logSuccess("Home Design copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Home Design: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function addVariationOptions(toModelId, newVariationsOptions) {
            var url = `${baseUrl}AddVariationOptions?toModelId=${toModelId}`;
            return $http.post(url, newVariationsOptions).then(success, fail);
            function success(resp) {
                log.logSuccess("Home Design Variation options copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error setting Home Design variations down: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function moveVariationDown(modelId) {
            var url = `${baseUrl}MoveVariationDown?modelId=${modelId}`;
            return $http.post(url).then(success, fail);
            function success(resp) {
                log.logSuccess("Home Design Variation moved down successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error moving Home Design down: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function moveVariationUp(modelId) {
            var url = `${baseUrl}MoveVariationUp?modelId=${modelId}`;
            return $http.post(url).then(success, fail);
            function success(resp) {
                log.logSuccess("Home Design Variation moved up successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error moving Home Design up: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function compare(projectId, comparisonOptions, targetEnergyRating) {

            const url = baseUrl + 'Compare';
            return $http({
                url,
                method: "POST",
                params: { projectId, targetEnergyRating },
                data: comparisonOptions }
            ).then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to run comparison: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }

        }

        function orientate(projectId, comparisonOptions, targetEnergyRating) {

            const url = baseUrl + 'Orientate';
            return $http({
                url,
                method: "POST",
                params: { projectId, targetEnergyRating },
                data: comparisonOptions }
            ).then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to run comparison: " + error.data.exceptionMessage;
                log.logError(msg, error, null, true);
                throw error;
            }

        }

        function identify(projectId, criteria, state, targetEnergyRating) {

            const url = baseUrl + 'Identify';
            return $http({
                url,
                method: "POST",
                params: { projectId, state, targetEnergyRating },
                data: criteria}
            ).then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to run comparison: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }

        }

        function optimise(projectId, criteria) {

            const url = baseUrl + 'Optimise';
            return $http({
                url,
                method: "POST",
                params: { projectId },
                data: criteria}
            ).then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to run comparison: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }

        }

        function updateVariationOptionSelection(standardHomeModelId, category, option) {
            const url = baseUrl + 'UpdateVariationOptionSelection';
            return $http({
                url: url,
                params: { standardHomeModelId, category, option },
                method: 'POST',
            }).then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to update 'isActive': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function updateIsActive(standardHomeModelId, isActive) {
            const url = baseUrl + 'UpdateIsActive';
            return $http({
                url: url,
                params: { standardHomeModelId, isActive },
                method: 'POST',
            })
              .then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to update 'isActive': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function updateCostEstimateEnabled(standardHomeModelId, isEnabled) {
            const url = baseUrl + 'UpdateCostEstimateEnabled';
            return $http({
                url: url,
                params: { standardHomeModelId, isEnabled },
                method: 'POST',
            })
              .then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to update 'costEstimateEnabled': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function updateDesignInsightEnabled(standardHomeModelId, isEnabled) {
            const url = baseUrl + 'UpdateDesignInsightEnabled';
            return $http({
                url: url,
                params: { standardHomeModelId, isEnabled },
                method: 'POST',
            })
              .then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to update 'designInsightEnabled': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function processSpreadsheet(file, standardHomeModelId) {

            let url = baseUrl + 'ProcessSpreadsheet';
            var promise = Upload.upload({
                url: url, // webapi url
                method: "POST",
                file: file,
                params: { standardHomeModelId }
            });

            return promise.progress(function (evt) {

            }).success(function (data, status, headers, config) {

                log.logSuccess("Home Design Database Updated");
                return data;

            }).error(function (data, status, headers, config) {

                log.logError("Error updating Home Design Database. Please check input file.", null, null, true);
            });
        }

        function linkOptionsToEstimates(building, clientProperties) {

            const optionEstimates = [];

            if(building == null || building.optionData == null)
                return [];

            for(const key in building.optionData) {

                if (!building.optionData.hasOwnProperty(key) || clientProperties[key] === false)
                    continue;

                const categoryMatch = building.variableMetadata.generalOptionData[key];

                if(categoryMatch == null)
                    continue;

                const match = categoryMatch.filter(x => x.optionValue === building.optionData[key])[0];

                if(match != null && match?.costEstimateEnabled && match?.costEstimateData != null)
                    optionEstimates.push(match.costEstimateData);

            }

            return optionEstimates;
        }

        function assignDefaults(standardModels) {
            standardModels.forEach(model => {
                for(const key in model.variableOptions) {
                    const match = model.variableMetadata.generalOptionData[key]?.find(x => x.isDefaultForVariable);

                    model.optionData[key] = match?.optionValue;
                }
            });
        }

        function assignProjectDefaults(standardModels, projectEnergyLabsSettings) {

            const defaultVars = projectEnergyLabsSettings.variableOptionDefaults.generalOptionData;
            const allOptions = projectEnergyLabsSettings.allVariablesAndOptionsAcrossDesigns;

            standardModels.forEach(model => {
                for(const key in allOptions) {
                    const match = defaultVars[key]?.find(x => x.isDefaultForVariable);
                    model.optionData[key] = match?.optionValue;
                }
            });
        }

        function clearModelVariables(projectId, standardHomeModelId) {
            const url = baseUrl + 'ClearModelVariables';
            return $http({
                url: url,
                params: { projectId, standardHomeModelId },
                method: 'POST',
            })
                .then(success, fail);

            function success(resp) {
                log.logSuccess("Design variables cleared.");
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to delete model's design variables: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function addCompare(modelsList, cardClickedIndex) {
            let modelToCopy = angular.copy(modelsList[cardClickedIndex]);
            modelToCopy.uiRenderId = uuid4.generate();
            modelsList.splice(cardClickedIndex, 0, modelToCopy);
            modelsList.forEach(building => building.selectedVariation.copyAcrossData = {});
        }

        function removeCompare(modelsList, cardClickedIndex) {
            modelsList.splice(cardClickedIndex, 1);
        }

        function copyFromHighlightEffect(index, toggle) {
            toggle
                ? document.getElementsByClassName("model-card-" + index)[0].classList.add("force-shadow")
                : document.getElementsByClassName("model-card-" + index)[0].classList.remove("force-shadow");
        }

        function getSelectedVariationOptionNames(variation, variationOptions) {
            let names = [];
            if (variation.variationFloorplanId) {
                names.push(variationOptions.find(o => o.standardHomeModelVariationOptionId == variation.variationFloorplanId).optionName);
            }
            if (variation.variationDesignOptionId) {
                names.push(variationOptions.find(o => o.standardHomeModelVariationOptionId == variation.variationDesignOptionId).optionName);
            }
            if (variation.variationFacadeId) {
                names.push(variationOptions.find(o => o.standardHomeModelVariationOptionId == variation.variationFacadeId).optionName);
            }
            if (variation.variationSpecificationId) {
                names.push(variationOptions.find(o => o.standardHomeModelVariationOptionId == variation.variationSpecificationId).optionName);
            }
            if (variation.variationConfigurationId) {
                names.push(variationOptions.find(o => o.standardHomeModelVariationOptionId == variation.variationConfigurationId).optionName);
            }
            return names;
        }

        /**
         * Return true as long as 1 option has matching cost estimate data AND
         * the feature is enabled for this model.
         */
        function showCostEstimates(building, clientProperties) {

            if(!building.costEstimateEnabled)
                return false;

            const options = linkOptionsToEstimates(building, clientProperties);
            return options.length > 0;
        }

        function showDesignInsights(building) {

            if (!building.variableMetadata?.designInsightsEnabled)
                return false;

            // Otherwise need to need to look for a match where the current
            // climate zone AND north offset are round.
            const climateZone = building.optionData.natHERSClimateZone;
            const northOffset = building.optionData.northOffset;

            const match = building.variableMetadata.designInsights?.find(x => {
                const hasCZ = x.climateZones.includes(climateZone);
                const hasNO = x.northOffsets.includes(northOffset);

                return hasCZ && hasNO;
            });

            return match != null;
        }

        const NAME_MAPPING = {
            "livingAreas":                            { name: "Living Areas",                          group: "N/A" },
            "width":                                  { name: "Lot Width (m)",                         group: "N/A" },
            "depth":                                  { name: "Lot Length (m)",                        group: "N/A" },
            "numberOfBedrooms":                       { name: "Bedrooms",                              group: "N/A" },
            "numberOfBathrooms":                      { name: "Bathrooms",                             group: "N/A" },
            "numberOfGarageSpots":                    { name: "Parking Spaces",                        group: "N/A" },
            "floorHeight":                            { name: "Floor Height (m)",                      group: "N/A" },
            "natHERSClimateZone":                     { name: "NatHERS Climate Zone",                  group: "N/A" },
            "nccClimateZone":                         { name: "NCC Climate Zone",                      group: "N/A" },

            "featuresKitchenLivingDiningGroundFloor": { name: "Kitchen/Living/Dining on Ground Floor", group: "kitchenLivingDining" },
            "featuresKitchenLivingDiningUpperFloor":  { name: "Kitchen/Living/Dining on Upper Floor",  group: "kitchenLivingDining" },
            "featuresKitchenLivingDiningRear":        { name: "Kitchen/Living/Dining at Rear",         group: "kitchenLivingDining" },
            "featuresKitchenLivingDiningMiddle":      { name: "Kitchen/Living/Dining in Middle",       group: "kitchenLivingDining" },
            "featuresKitchenLivingDiningFront":       { name: "Kitchen/Living/Dining at Front",        group: "kitchenLivingDining" },

            "featuresButlersPantry":                  { name: "Butler's Pantry",                       group: "kitchen" },
            "featuresScullery":                       { name: "Scullery",                              group: "kitchen" },
            "featuresWalkInPantry":                   { name: "Walk-in Pantry",                        group: "kitchen" },

            "featuresMasterSuiteGroundFloor":         { name: "Master Suite on Ground Floor",          group: "masterSuite" },
            "featuresMasterSuiteFirstFloor":          { name: "Master Suite on Upper Floor",           group: "masterSuite" },
            "featuresMasterSuiteAtFront":             { name: "Master Suite at Front",                 group: "masterSuite" },
            "featuresMasterSuiteAtMiddle":            { name: "Master Suite in Middle",                group: "masterSuite" },
            "featuresMasterSuiteAtRear":              { name: "Master Suite at Rear",                  group: "masterSuite" },

            "featuresDressingRoom":                   { name: "Dressing Room",                         group: "masterSuiteWir" },
            "featuresHisHerWir":                      { name: "His & Her WIR",                         group: "masterSuiteWir" },
            "featuresWalkInRobe":                     { name: "Walk-in Robe",                          group: "masterSuiteWir" },

            "featuresAudioVisual":                    { name: "Audio Visual",                          group: "homeTheatre" },
            "featuresHomeCinema":                     { name: "Home Cinema",                           group: "homeTheatre" },
            "featuresHomeTheatre":                    { name: "Home Theatre",                          group: "homeTheatre" },
            "featuresMedia":                          { name: "Media",                                 group: "homeTheatre" },

            "featuresActivity":                       { name: "Activity Room",                         group: "secondaryLivingSpaces" },
            "featuresEntertainment":                  { name: "Entertainment Room",                    group: "secondaryLivingSpaces" },
            "featuresFamily":                         { name: "Family Room",                           group: "secondaryLivingSpaces" },
            "featuresFormalLounge":                   { name: "Formal Lounge",                         group: "secondaryLivingSpaces" },
            "featuresGames":                          { name: "Games",                                 group: "secondaryLivingSpaces" },
            "featuresLeisure":                        { name: "Leisure Room",                          group: "secondaryLivingSpaces" },
            "featuresLounge":                         { name: "Lounge",                                group: "secondaryLivingSpaces" },
            "featuresRumpus":                         { name: "Rumpus",                                group: "secondaryLivingSpaces" },
            "featuresSecondLiving":                   { name: "Second Living",                         group: "secondaryLivingSpaces" },
            "featuresMultipurposeRoom":               { name: "Multipurpose Room",                     group: "secondaryLivingSpaces" },
            "featuresRetreat":                        { name: "Retreat",                               group: "secondaryLivingSpaces" },

            "featuresComputerNook":                   { name: "Computer Nook",                         group: "study" },
            "featuresENook":                          { name: "E-Nook",                                group: "study" },
            "featuresHomeOffice":                     { name: "Home Office",                           group: "study" },
            "featuresStudy":                          { name: "Study",                                 group: "study" },
            "featuresStudyNook":                      { name: "Study Nook",                            group: "study" },

            "featuresCellar":                         { name: "Cellar",                                group: "other" },
            "featuresCloakRoom":                      { name: "Cloak Room",                            group: "other" },
            "featuresGuestBedroom":                   { name: "Guest Bedroom",                         group: "other" },
            "featuresGym":                            { name: "Gym",                                   group: "other" },
            "featuresMudRoom":                        { name: "Mud Room",                              group: "other" },
            "featuresNannysQuarters":                 { name: "Nanny's Quarters",                      group: "other" },
            "featuresPowderRoom":                     { name: "Powder Room",                           group: "other" },
            "featuresStoreRoom":                      { name: "Store Room",                            group: "other" },
            "featuresWalkInLinen":                    { name: "Walk-in Linen",                         group: "other" },

            "featuresCarport":                        { name: "Carport",                               group: "carParking" },
            "featuresLHCarport":                      { name: "Left-hand Carport",                     group: "carParking" },
            "featuresRHCarport":                      { name: "Right-hand Carport",                    group: "carParking" },
            "featuresGarage":                         { name: "Garage",                                group: "carParking" },
            "featuresLHGarage":                       { name: "Left-hand Garage",                      group: "carParking" },
            "featuresRHGarage":                       { name: "Right-hand Garage",                     group: "carParking" },
            "featuresRearAccess":                     { name: "Rear Access",                           group: "carParking" },
            "featuresRearLoaded":                     { name: "Rear Loaded",                           group: "carParking" },
            "featuresWorkshop":                       { name: "Workshop",                              group: "carParking" },

            "featuresAlfresco":                       { name: "Alfresco",                              group: "outdoorLiving" },
            "featuresMLAlfresco":                     { name: "Middle-Left Alfresco",                  group: "outdoorLiving" },
            "featuresMRAlfresco":                     { name: "Middle-Right Alfresco",                 group: "outdoorLiving" },
            "featuresRCAlfresco":                     { name: "Rear-Centre Alfresco",                  group: "outdoorLiving" },
            "featuresRLAlfresco":                     { name: "Rear-Left Alfresco",                    group: "outdoorLiving" },
            "featuresRRAlfresco":                     { name: "Rear-Right Alfresco",                   group: "outdoorLiving" },
            "featuresBalcony":                        { name: "Balcony",                               group: "outdoorLiving" },
            "featuresFrontBalcony":                   { name: "Front Balcony",                         group: "outdoorLiving" },
            "featuresRearBalcony":                    { name: "Rear Balcony",                          group: "outdoorLiving" },
            "featuresCourtyard":                      { name: "Courtyard",                             group: "outdoorLiving" },
            "featuresLHCourtyard":                    { name: "Left-hand Courtyard",                   group: "outdoorLiving" },
            "featuresRHCourtyard":                    { name: "Right-hand Courtyard",                  group: "outdoorLiving" },
            "featuresOutdoorLiving":                  { name: "Outdoor Living",                        group: "outdoorLiving" },
            "featuresMLOutdoorLiving":                { name: "Middle-Left Outdoor Living",            group: "outdoorLiving" },
            "featuresMROutdoorLiving":                { name: "Middle-Right Outdoor Living",           group: "outdoorLiving" },
            "featuresRCOutdoorLiving":                { name: "Rear-Centre Outdoor Living",            group: "outdoorLiving" },
            "featuresRLOutdoorLiving":                { name: "Rear-Left Outdoor Living",              group: "outdoorLiving" },
            "featuresRROutdoorLiving":                { name: "Rear-Right Outdoor Living",             group: "outdoorLiving" },
            "featuresVerandah":                       { name: "Verandah",                              group: "outdoorLiving" },

            "categoryAcreage":                        { name: "Acreage",                               group: "N/A" },
            "categoryDisplayHome":                    { name: "Display Home",                          group: "N/A" },
            "categoryDualOccupancy":                  { name: "Dual Occupancy",                        group: "N/A" },
            "categoryDuplex":                         { name: "Duplex",                                group: "N/A" },
            "categoryFarmhouse":                      { name: "Farmhouse",                             group: "N/A" },
            "categoryGrannyFlat":                     { name: "Granny Flat",                           group: "N/A" },
            "categoryNarrowLot":                      { name: "Narrow Lot",                            group: "N/A" },
            "categoryNewDesign":                      { name: "New Design",                            group: "N/A" },
            "categoryRearLoaded":                     { name: "Rear Loaded",                           group: "N/A" },
            "categorySingleStorey":                   { name: "Single Storey",                         group: "N/A" },
            "categorySplitLevel":                     { name: "Split Level",                           group: "N/A" },
            "categoryTwoStorey":                      { name: "Two Storey",                            group: "N/A" },
            "categoryThreeStorey":                    { name: "Three Storey",                          group: "N/A" }
        }

        const GROUP_MAPPING = {
            "kitchenLivingDining":   { name: "Kitchen/Living/Dining",   column: 1, dropdownSection: 1 },
            "kitchen":               { name: "Kitchen",                 column: 1, dropdownSection: 1 },
            "masterSuite":           { name: "Master Suite",            column: 1, dropdownSection: 1 },
            "masterSuiteWir":        { name: "Master Suite WIR",        column: 1, dropdownSection: 1 },
            "homeTheatre":           { name: "Home Theatre",            column: 2, dropdownSection: 1 },
            "secondaryLivingSpaces": { name: "Secondary Living Spaces", column: 2, dropdownSection: 1 },
            "study":                 { name: "Study",                   column: 2, dropdownSection: 1 },
            "other":                 { name: "Other",                   column: 2, dropdownSection: 1 },
            "carParking":            { name: "Car Parking",             column: 3, dropdownSection: 2 },
            "outdoorLiving":         { name: "Outdoor Living",          column: 3, dropdownSection: 3 },
        }

        function buildFeaturesColumns() {
            let allGroups = Object.keys(GROUP_MAPPING);
            let allFeatures = Object.keys(NAME_MAPPING).filter(n => n.startsWith("features"));
            let columnNums = common.uniqueValues(allGroups.map(g => GROUP_MAPPING[g].column));
            let columns = columnNums.map(n => {
                let groupCodes = allGroups.filter(g => GROUP_MAPPING[g].column == n);
                return {
                    groups: groupCodes.map(g => {
                        return {
                            name: GROUP_MAPPING[g].name,
                            features: allFeatures.filter(f => NAME_MAPPING[f].group == g)
                        };
                    })
                };
            });
            return columns;
        }

        function buildFeaturesDropdownSections() {
            let allGroups = Object.keys(GROUP_MAPPING);
            let allFeatures = Object.keys(NAME_MAPPING).filter(n => n.startsWith("features"));
            let sectionNums = common.uniqueValues(allGroups.map(g => GROUP_MAPPING[g].dropdownSection));
            let sections = sectionNums.map(n => {
                let groupCodes = allGroups.filter(g => GROUP_MAPPING[g].dropdownSection == n);
                return {
                    features: allFeatures.filter(f => groupCodes.includes(NAME_MAPPING[f].group))
                };
            });
            return sections;
        }

        const CATEGORIES = [
            "categoryAcreage",
            "categoryDisplayHome",
            "categoryDualOccupancy",
            "categoryDuplex",
            "categoryFarmhouse",
            "categoryGrannyFlat",
            "categoryNarrowLot",
            "categoryNewDesign",
            "categoryRearLoaded",
            "categorySingleStorey",
            "categorySplitLevel",
            "categoryTwoStorey",
            "categoryThreeStorey",
        ];

        const NOT_SPECIFICATION_VARIABLES = [
          "natHERSClimateZone",
          "nccClimateZone",
          "northOffset",
          "blockType",
          "assessmentMethod",
          "targetEnergyRating",
          "costEstimateEnabledDefault",
          "siteExposure",
          "floorHeight",
          "standardHomeModelOptionId",
          "standardHomeModelId",
          "heatingLoad",
          "coolingLoad",
          "totalEnergyLoad",
          "energyRating",
          "row",
          "active",
          "description",
          "comments",
        ];

        const SPEC_PROP_ORDER = {
          "roofConstruction": 0,
          "roofInsulation": 1,
          "roofSolarAbsorptance": 2,
          "ceilingConstruction": 3,
          "ceilingInsulation": 4,
          "exteriorWallConstruction": 5,
          "exteriorWallInsulation": 6,
          "exteriorWallSolarAbsorptance": 7,
          "interiorWallConstruction": 8,
          "interiorWallInsulation": 9,
          "interiorWallSolarAbsorptance": 10,
          "exteriorGlazing": 11,
          "exteriorGlazingFrameSolarAbsorptance": 12,
          "exteriorDoorSolarAbsorptance": 13,
          "garageDoorSolarAbsorptance": 14,
          "floorConstruction": 15,
          "floorInsulation": 16,
          "floorCoverings": 17,
          "floorSolarAbsorptance": 18,
          "ceilingFans": 19,
          "recessedLightFittings": 20,
        };

        const SPEC_GRID_ORDER = [
            ["assessmentMethod",        "roofConstruction",         "roofInsulation",           "roofSolarAbsorptance"],
            ["natHERSClimateZone",      "ceilingConstruction",      "ceilingInsulation",        "garageDoorSolarAbsorptance" ],
            ["siteExposure",            "exteriorWallConstruction", "exteriorWallInsulation",   "exteriorWallSolarAbsorptance"],
            ["floorHeight",             "interiorWallConstruction", "interiorWallInsulation",   "interiorWallSolarAbsorptance"],
            ["blockType",               "floorConstruction",        "floorInsulation",          "floorSolarAbsorptance"],
            ["recessedLightFittings",   "floorCoverings",           "ceilingFans",              "exteriorDoorSolarAbsorptance"],
            ["northOffset",             "exteriorGlazing",          "",                         "exteriorGlazingFrameSolarAbsorptance"],
        ];

        const MULTI_FILTERS_FIELDS = [
            { name: 'Width',          field: 'width' },
            { name: 'Depth',          field: 'depth' },
            { name: 'Storeys',        field: 'storeys' },
            { name: 'Bedrooms',       field: 'numberOfBedrooms' },
            { name: 'Bathrooms',      field: 'numberOfBathrooms' },
            { name: 'Living Areas',   field: 'livingAreas' },
            { name: 'Parking Spaces', field: 'numberOfGarageSpots' },
            { name: 'Features',       field: 'features' },
            { name: 'Categories',     field: 'categories' }
        ];

        /**
         * In most cases, the name of the variable as recorded in the
         * spreadsheet is sufficient for UI display purposes. However in some
         * instances, we need to transform the 'key' into a known mapped value.
         */
        function keyToName(key) {
            var mapping = NAME_MAPPING[key]?.name;
            return mapping ?? common.toSplitTitleCase(key);
        }

        /** Return mapping if it exists OR removes 'Features' from key */
        function featureName(key) {
            if(key == null)
                return "";

            const mapping = NAME_MAPPING[key]?.name;
            const s = common.toSplitTitleCase(key);

            return mapping ?? s.substring(9, s.length);
        }

        function groupName(key) {
            if(key == null)
                return "";

            const mapping = GROUP_MAPPING[key]?.name;
            const s = common.toSplitTitleCase(key);

            return mapping ?? s.substring(9, s.length);
        }

        function applyFilters(filterData, standardModels) {

            if (standardModels == null)
                return;

            standardModels.forEach(x => x.include = false);

            // No filter, show all.
            if (filterData == null || allFilterDataIsEmpty(filterData)){
                standardModels.forEach(x => x.include = true);
                return;
            }

            // Filter generically over design variables then features and categories.
            let filteredBuildings = standardModels;
            for (const key in filterData) {

                if (key === 'features' || key === 'categories')
                    continue;

                const allowedValues = filterData[key];

                if (allowedValues == null || allowedValues.length === 0)
                    continue;

                filteredBuildings = filteredBuildings.filter(building => allowedValues.some(val => val == building[key]));

            }

            filterData.features?.forEach(feature => {
                filteredBuildings = filteredBuildings.filter(building => building[feature]);
            });

            filterData.categories?.forEach(category => {
                filteredBuildings = filteredBuildings.filter(building => building[category]);
            });

            filteredBuildings.forEach(x => x.include = true);
        }

        function anyFiltersApplied(filterData) {
            let check = false;
            for (const key in filterData) {
                if (filterData[key].length > 0 && !(filterData[key][0] == 'Any')) {
                    check = true;
                }
            }
            return check;
        }

        function applySort(sortData, standardModels) {
            let field = sortData[0].field;
            let dir = sortData[0].dir;
            return standardModels.sort((a, b) => {
                if (dir == 'asc') {
                    return (field == "title" ? a[field] < b[field] : a.selectedVariation[field] < b.selectedVariation[field]) ? -1 : 1;
                } else {
                    return (field == "title" ? a[field] > b[field] : a.selectedVariation[field] > b.selectedVariation[field]) ? -1 : 1;
                }
            });
        }

        function allFilterDataIsEmpty(filterData) {

            for(const key in filterData){
                if(filterData[key] !== null && filterData[key].length !== 0)
                    return false;
            }

            return true;
        }

        const ALL_POSSIBLE_VARIABLE_OPTIONS = {
            "assessmentMethod": [
                { "optionValue": "House Energy Rating (HER)" },
                { "optionValue": "Performance Solution (Energy Load Limits)" }
            ],
            "natHERSClimateZone": [
                { "optionValue": 13 },
                { "optionValue": 47 }
            ],
            "siteExposure": [
                { "optionValue": "Suburban" }
            ],
            "floorHeight": [
                { "optionValue": 0.15 }
            ],
            "northOffset": [
                { "optionValue": 0 },
                { "optionValue": 90 },
                { "optionValue": 315 }
            ],
            "blockType": [
                { "optionValue": "Rectangular" }
            ],
            "roofConstruction": [
                { "optionValue": "Metal Roof (unventilated)" },
                { "optionValue": "Tiled Roof (unventilated)" },
                { "optionValue": "Metal Roof (timber frame, ventilated)" }
            ],
            "roofInsulation": [
                { "optionValue": "60mm Reflective Blanket (R1.3)" },
                { "optionValue": "Sarking (reflective)" }
            ],
            "roofSolarAbsorptance": [
                { "optionValue": 0.3 },
                { "optionValue": 0.85 }
            ],
            "ceilingInsulation": [
                { "optionValue": "R4.0 Batts" },
                { "optionValue": "R6.0 Batts" }
            ],
            "exteriorWallConstruction": [
                { "optionValue": "Cavity Masonry" }
            ],
            "exteriorWallInsulation": [
                { "optionValue": "Reflective Foil (throughout)" },
                { "optionValue": "Nil" }
            ],
            "exteriorWallSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "interiorWallConstruction": [
                { "optionValue": "Single Brick" }
            ],
            "interiorWallInsulation": [
                { "optionValue": "Nil" }
            ],
            "interiorWallSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "exteriorGlazing": [
                { "optionValue": "Single Glazed, Low-E (habitable rooms)" },
                { "optionValue": "Single Glazed, Clear (throughout)" }
            ],
            "exteriorGlazingFrameSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "floorConstruction": [
                { "optionValue": "Concrete Slab on Ground" }
            ],
            "floorInsulation": [
                { "optionValue": "Nil" }
            ],
            "floorCoverings": [
                { "optionValue": "Default" }
            ],
            "floorSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "ceilingFans": [
                { "optionValue": "Nil" }
            ],
            "recessedLightFittings": [
                { "optionValue": "Nil" }
            ],
            "exteriorDoorSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "garageDoorSolarAbsorptance": [
                { "optionValue": 0.5 }
            ],
            "ceilingConstruction": [
                { "optionValue": "10mm plasterboard (timber frame)" }
            ]
        };

        return {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getStandardModel,
            getStandardModelOptions,
            getStandardModelOption,
            getVariationOptionsForStage,
            createStandardModel: createStandardModel,
            updateStandardModel,
            updateVariableSettingsInAllModels,
            updateServicesDefaultsInAllModels,
            updateCostEstimatesInAllModels,
            updateVariationsList,
            updateModelsList,
            updateZoneSummaryBuildingData,
            copyVariationOptionsToModel,
            copyCategoriesToVariation,
            copyDesignMetadataToVariation,
            copyFloorMetricsToVariation,
            copyDrawingAreasToVariation,
            copyDesignMetricsToVariation,
            copyFeaturesToVariation,
            copyVariablesToVariation,
            copyServiceDefaultsToVariation,
            copyCostEstimatesToVariation,
            copyDesignInsightsToVariation,
            deleteStandardModel,
            undoDeleteStandardModel,
            copyStandardModel,
            addVariationOptions,
            moveVariationDown,
            moveVariationUp,
            getAll,
            getForProject,
            getForProjectMultiFiltered,
            getVariationFromSelections,
            getModelVariations,
            getModelVariationsQuery,
            compare,
            orientate,
            identify,
            optimise,
            processSpreadsheet,
            keyToName,
            featureName,
            groupName,
            applyFilters,
            anyFiltersApplied,
            applySort,
            updateVariationOptionSelection,
            updateIsActive,
            updateCostEstimateEnabled,
            updateDesignInsightEnabled,
            showCostEstimates,
            showDesignInsights,
            linkOptionsToEstimates,
            assignDefaults,
            assignProjectDefaults,
            clearModelVariables,
            addCompare,
            removeCompare,
            copyFromHighlightEffect,
            getSelectedVariationOptionNames,
            allFeatures: Object.keys(NAME_MAPPING).filter(n => n.startsWith("features")),
            buildFeaturesColumns: buildFeaturesColumns,
            buildFeaturesDropdownSections: buildFeaturesDropdownSections,
            categories: CATEGORIES,
            notSpecificationVariables: NOT_SPECIFICATION_VARIABLES,
            specPropOrder: SPEC_PROP_ORDER,
            specGridOrder: SPEC_GRID_ORDER,
            multiFiltersFields: MULTI_FILTERS_FIELDS,
            allPossibleVariableOptions: ALL_POSSIBLE_VARIABLE_OPTIONS
        };
    }
})();
