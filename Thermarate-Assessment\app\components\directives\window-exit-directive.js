﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('windowExit', ['$window', 'currentsalemodel', function ($window, currentsalemodel) {
        return {
            restrict: 'AE',
            //performance will be improved in compile
            compile: function (element, attrs) {
                var myEvent = $window.attachEvent || $window.addEventListener,
                chkevent = $window.attachEvent ? 'onbeforeunload' : 'beforeunload'; /// make IE7, IE8 compatable

                myEvent(chkevent, function (e) { // For >=IE7, Chrome, Firefox
                    var confirmationMessage = ' ';  // a space
                    //(e || $window.event).returnValue = "Are you sure that you'd like to close the browser?";
                    //console.log("currentsalemodel.currentSale.saleId:");
                    //console.log(currentsalemodel.currentSale.saleId);
                    if (localStorage.getItem('OpenSale' + currentsalemodel.currentSale.saleId) != null) {
                        localStorage.removeItem('OpenSale' + currentsalemodel.currentSale.saleId);
                    }
                    return confirmationMessage;
                });
            }
        };
    }]);
})();