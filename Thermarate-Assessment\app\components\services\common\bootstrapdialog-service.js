(function () {
    'use strict';

    angular.module('common.bootstrap', ['ngMaterial'])
    .factory('bootstrap.dialog', ['$mdDialog', '$templateCache', modalDialog]);

    function modalDialog($mdDialog, $templateCache) {
        var service = {
            deleteDialog: deleteDialog,
            confirmationDialog: confirmationDialog,
            deleteDialogWithReason: deleteDialogWithReason,
            infoDialog: infoDialog,
            errorDialog: errorDialog,
            yesNoDialog: yesNoDialog
        };

        return service;

        function deleteDialog(itemName) {
            var title = 'Confirm Delete';
            itemName = itemName || 'item';
            var msg = "Delete '" + itemName + "' ?";

            return confirmationDialog(title, msg, 'Delete');
        }

        function deleteDialogWithReason(itemName) {
            var title = 'Confirm Delete';
            itemName = itemName || 'item';
            var msg = 'Delete ' + itemName + '?';

            return confirmationDialog(title, msg, 'Delete', null,"");
        }

        function confirmationDialog(title, msg, okText, cancelText, deleteReasons) {
            var deleteClass = "";
            if (deleteReasons != null) {
                deleteClass = 'deleteModal';
            }

            var modalOptions = {
                templateUrl: 'app/components/directives/templates/modalDialog.tpl.html',
                controller: ModalInstance,
                resolve: {
                    options: function () {
                        return {
                            title: title,
                            message: msg,
                            okText: okText,
                            cancelText: cancelText,
                            deleteReasons: deleteReasons,
                        };
                    }
                },
                skipHide: true, // DON'T HIDE THE MODAL
            };

            return $mdDialog.show(modalOptions);
        }

        function yesNoDialog(title, msg, yesText, noText, cancelText) {
            var modalOptions = {
                templateUrl: 'app/components/directives/templates/modalDialog.tpl.html',
                controller: ModalInstance,
                resolve: {
                    options: function () {
                        return {
                            title: title,
                            message: msg,
                            yesText: yesText,
                            noText: noText,
                            cancelText: cancelText,
                            hideOk: true,
                        };
                    }
                },
                skipHide: true, // DON'T HIDE THE MODAL
            };

            return $mdDialog.show(modalOptions);
        }

        function infoDialog(title, bigInfo, msg, okText, progress) {

            var modalOptions = {
                templateUrl: 'app/components/directives/templates/modalDialog.tpl.html',
                controller: ModalInstance,
                resolve: {
                    options: function () {
                        return {
                            title: title,
                            message: msg,
                            okText: okText,
                            bigInfo: bigInfo,
                            progress: progress,
                        };
                    }
                },
                skipHide: true, // DON'T HIDE THE MODAL
            };

            return $mdDialog.show(modalOptions);
        }
        
        function errorDialog(title, bigInfo, msg, okText) {

            var modalOptions = {
                templateUrl: 'app/components/directives/templates/modalDialog.tpl.html',
                controller: ModalInstance,
                resolve: {
                    options: function () {
                        return {
                            title: title,
                            message: msg,
                            okText: okText,
                            bigInfo: bigInfo,
                            hideOk: true,
                            cancelText: "Close"
                        };
                    }
                },
                skipHide: true, // DON'T HIDE THE MODAL
            };

            return $mdDialog.show(modalOptions);
        }
    }

    var ModalInstance = ['$scope', '$mdDialog', 'options',
        function ($scope, $mdDialog, options) {
            $scope.vm = {};
            $scope.vm.deleteReasonId = null;
            $scope.vm.note = null;
            $scope.title = options.title || 'Title';
            $scope.message = options.message || '';
            $scope.okText = options.okText || 'OK';
            $scope.yesText = options.yesText || null;
            $scope.noText = options.noText || null;
            $scope.cancelText = options.cancelText || 'Cancel';
            $scope.bigInfo = options.bigInfo || null;
            $scope.ok = function () {
                $mdDialog.hide({ reasonId: $scope.vm.deleteReasonId, note: $scope.vm.note });
            };
            $scope.yes = function () {
                $mdDialog.hide(true);
            }
            $scope.no = function () {
                $mdDialog.hide(false);
            }
            $scope.cancel = function () { $mdDialog.cancel('cancel'); };
            $scope.deleteReasons = options.deleteReasons || null;
            $scope.hideOk = options.hideOk || false;
        }];
})();