<!--
    These are the possible inputs for Client Settings (Essentially, inputs where you can
    restrict available client selections in the new job and assessment pages.

    You will probably want to wrap this in a card
-->

<!-- ******** Allowable Works Descriptions ******** -->
<md-input-container class="md-block"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                    flex="100">
    <label>Allowable Works Description</label>
    <md-select name="AllowableWorksDescription"
               ng-model="vm.options.availableWorksDescriptions"
               ng-disabled="vm.disabled === true"
               multiple="true">
        <md-option ng-value="item.worksDescriptionCode"
                   ng-repeat="item in vm.optionData.worksDescriptionList">
            {{item.description}}
        </md-option>
    </md-select>
</md-input-container>

<!-- ******** Allowable Certifications ******** -->
<md-input-container class="md-block"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                    flex="100">
    <label>Allowable Certifications</label>
    <md-select name="allowableCertifications"
               ng-model="vm.options.availableCertifications"
               ng-disabled="vm.disabled === true"
               multiple="true">
        <md-option ng-value="item.certificationId"
                   ng-repeat="item in vm.optionData.certificationList">
            {{item.title}}
        </md-option>
    </md-select>
</md-input-container>

<!-- ******** Allowable Assessment Method Options ******** -->
<md-input-container class="md-block"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                    flex="100">
    <label>Allowable Assessment Method</label>
    <md-select name="preliminaryComplianceMethodCode"
               ng-disabled="vm.disabled === true"
               ng-model="vm.options.availableComplianceMethodCodes"
               multiple="true">
        <md-option ng-value="item.complianceMethodCode"
                   ng-repeat="item in vm.optionData.complianceMethodList track by item.complianceMethodCode">
            {{item.description}}
        </md-option>
    </md-select>
</md-input-container>

<!-- ******** Required House Energy Rating Options ******** -->
<md-input-container class="md-block"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                    flex="100"
                    ng-if="vm.hasCMHouseEnergyRatingComplianceMethod()">
    <label>Required House Energy Rating</label>
    <md-select name="assessmentSoftwareCode"
               ng-model="vm.options.availableHouseEnergyRatings"
               ng-disabled="vm.disabled === true"
               multiple="true">
        <md-option ng-value="item.val"
                   ng-repeat="item in vm.optionData.minHouseEnergyRatingChoices">
            {{item.description}}
        </md-option>
    </md-select>
</md-input-container>

<!-- ******** Allowable Nominated Building Surveyor ******** -->
<md-input-container class="md-block"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}"
                    flex="100">
    <label>Allowable Nominated Building Surveyor</label>
    <md-select name="NominatedBuildingSurveyor"
               ng-model="vm.options.availableBuildingSurveyorIds"
               ng-disabled="vm.disabled === true"
               multiple="true">
        <md-option ng-value="item.buildingSurveyorId"
                   ng-repeat="item in vm.optionData.nominatedBuildingSurveyorList">
            {{item.description}}
        </md-option>
    </md-select>
</md-input-container>

<!-- Maximum Heating and Cooling -->
<md-input-container class="md-block" flex="100"
                    ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}">
    <label>Maximum Heating and Cooling</label>
    <md-select required
               ng-disabled="vm.disabled === true"
               name="maximumHeatingAndCoolingRulesetCode"
               ng-model="vm.options.heatingAndCoolingRulesetCode">
        <md-option ng-value="'Inherit'">
            As per State
        </md-option>
        <md-option ng-value="'Disabled'">
            Disabled
        </md-option>
    </md-select>
</md-input-container>

<!-- ******** Include Drawings in Report ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100"
                    style="margin-bottom: 36px;">
    <label>Include Drawings in Report</label>
    <div layout="row" layout-wrap class="spacing-above">
        <md-radio-group ng-model="vm.options.includeDrawingsInReport"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">Yes</md-radio-button>
            <md-radio-button ng-value="false" ng-click="vm.options.stampDrawings = false; vm.defaults.includeDrawingsInReport = false; vm.defaults.stampDrawings = false;">No</md-radio-button>
        </md-radio-group>
    </div>
</md-input-container>

<!-- ******** Stamp ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100"
                    style="margin-bottom: 36px;">
    <label>Stamp</label>
    <div layout="row" layout-wrap class="spacing-above">
        <md-radio-group ng-model="vm.options.stampDrawings"
                        layout="row"
                        ng-disabled="vm.disabled === true || !vm.options.includeDrawingsInReport"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">Yes</md-radio-button>
            <md-radio-button ng-value="false" ng-click="vm.defaults.stampDrawings = false;">No</md-radio-button>
        </md-radio-group>
    </div>
</md-input-container>

<!-- ******** QR Code ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100"
                    style="margin-bottom: 36px;">
    <label>QR Code</label>
    <div layout="row" layout-wrap class="spacing-above">

        <md-radio-group ng-model="vm.options.isQRCodeAvailable"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true"
                             ng-click="vm.toggleQrCode(true)">
                Yes
            </md-radio-button>
            <md-radio-button ng-value="false"
                             ng-click="vm.defaults.qrCodeEnabled = false;">
                No
            </md-radio-button>
        </md-radio-group>

    </div>
</md-input-container>