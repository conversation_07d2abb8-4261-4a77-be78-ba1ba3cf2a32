﻿(function () {
    'use strict';
    angular.module('app')
        .component('jSignature', {
            bindings: {
                signatureSvg: '=',
                signatureBase64: '=',
                previewHeight: '<' // OPTIONAL: Controls the height of the preview image. The default (170) seems to be too high.
            },
            templateUrl: 'app/components/directives/templates/jSignature-canvas-tpl.html',
            controller: JSignatureController,
            controllerAs: 'vm'
        });

    JSignatureController.$inject = ['common', '$scope', '$timeout'];

    function JSignatureController(common, $scope, $timeout) {
        var vm = this;
        var jSignatureElem = $("#jSignature");

        vm.defaultPreviewHeight = 170;

        //Initialise
        jSignatureElem.jSignature({
            width: '100%',
            height: '200px',
            'background': '#00f'
        });

        // Initialise Draw/Upload toggle
        vm.drawSelected = vm.signatureSvg ? true : false

        var timeout = null;
        jSignatureElem.bind('change',
            function (e, aa) {
                /* 'e.target' will refer to div with "#signature" */

                timeout = $timeout(function () { 
        
                    var datapair = jSignatureElem.jSignature("getData", "svg");
                    //find height attribute and associated value
                    var heightStartPos = datapair[1].indexOf("height=\"");
                    var height = null;
                    if (heightStartPos > -1) {
                        var endOfHeight = datapair[1].indexOf("\"", (heightStartPos + 8));
                        height = Number(datapair[1].substring(heightStartPos + 8, endOfHeight));
                    }

                    if (height > 0) {
                        //Wrap path in 'g' tag with transform
                        var calcScale = 40 / height;
                        var scale = calcScale > 1 ? 1 : calcScale;
                        var pathStart = datapair[1].indexOf("<path");
                        var pathEnd = datapair[1].lastIndexOf("/>");
                        var path = datapair[1].substring(pathStart, (pathEnd + 2));

                        var modifiedSignature = `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="283.465" height="${vm.previewHeight || vm.defaultPreviewHeight}">` +
                            "<g transform=\"scale(" + scale + ")\">" +
                            path +
                            "</g></svg>";
                        //Lots of magic to get the stroke width right on different signature scales. Default stroke-width is 2.
                        var strokeWidth = scale >= 0.8 ? 2 : (scale > 0.4 ? 3 : ((1 - scale) + 1) * 3);

                        //Set newly modified 'signature' 
                        vm.signatureSvg = modifiedSignature.replace(/stroke-width=\"2\"/g, "stroke-width=\"" + strokeWidth +"\"");

                        //Show modified svg
                        $(vm.signatureSvg).replaceAll($("#jSignatureSvg").find("svg")); // append the image (SVG) to DOM.
                        console.log(height);
                    }
                });
            });

        // Toggle 'DRAW'
        vm.toggleDraw = function () {
            vm.drawSelected = true;
            vm.signatureBase64 = null;
        }

        // Toggle 'UPLOAD PNG'
        vm.imgSrc = null;
        vm.uploadSignaturePNG = function (files) {

            if (files == null || (Array.isArray(files) && files.length == 0)) { return; }

            vm.uploadBusy = true;

            var file = Array.isArray(files) ? files[0] : files

            var fileReader = new FileReader();
            fileReader.onload = function () {
                $scope.vm.signatureBase64 = fileReader.result;
                $scope.vm.uploadBusy = false;
            };
            fileReader.readAsDataURL(file);

            vm.signatureSvg = null;
            vm.drawSelected = false;

        }

        //Reset signature canvas
        vm.reset = function () {
            jSignatureElem.jSignature('reset');
            vm.signature = "";
            $("<svg></svg>").replaceAll($("#jSignatureSvg").find("svg")); // append the image (SVG) to DOM.
        };

        $scope.$on('$destroy', function () {
            if (timeout) {
                $timeout.cancel(timeout);
                timeout = null;
            }
        });
        
    }
})();