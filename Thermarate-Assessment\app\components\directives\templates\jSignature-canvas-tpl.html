﻿
<div flex="200" layout layout-wrap>

    <!--Draw/Upload Toggle-->
    <div style="margin-bottom:10px;">
        <md-button ng-hide="vm.uploadSignatureToggle"
                   class="md-raised"
                   ng-click="vm.toggleDraw()">
            Draw
        </md-button>
        <md-button ng-hide="vm.uploadSignatureToggle"
                   class="md-raised"
                   ngf-select="vm.uploadSignaturePNG($files)"
                   ngf-accept="'.png'"
                   ngf-multiple="false">
            Upload PNG
        </md-button>
    </div>

    <!--Draw-->
    <div ng-show="vm.drawSelected">
        <div flex="100"
             id="jSignature"
             style="background-color:aliceblue"></div>
        <div layout="row" style="margin: 30px 5px;">
            <div>
                <md-button class="md-raised"
                           ng-click="vm.reset()">
                    Reset
                </md-button>
            </div>

            <div id="jSignatureSvg"
                 flex="100"
                 style="margin-left: 20px; margin-top: 3px;">
                <svg ng-style="{'height': vm.previewHeight || vm.defaultPreviewHeight}"></svg>
            </div>
        </div>
    </div>

    <!--Upload-->
    <div data-ng-show="vm.uploadBusy" data-cc-spinner="vm.spinnerOptions" style="margin-left:32px;margin-top:14px;"></div>
    <div ng-show="!vm.drawSelected">
        <img id="signaturePng" src="{{vm.signatureBase64}}" width="500" />
    </div>

</div>