﻿// Used on input fields to format numbers to '123,456,789.00' format to the user
// while retaining the underlying value of the input as an actual number.
// Taken from the below with not insignificant changes to better handle rounding to a given number of decimal places.
// (Really be careful if you need to change anything in here, so many timing, first-click, etc related things to look for)
// See: https://stackoverflow.com/questions/24001895/angularjs-number-input-formatted-view/48511292#48511292

// Example usage:
// <input formatted-number decimals="2"/>

(function () {
    'use strict';

    var app = angular.module('app');

    // adds commas to numbers as they are entered into an input 
    // allows negative numbers and a decimal. Good idea to use this with an input type="tel"
    // in cases where the number does not need to be negative or a decimal
    // so that smartphone browsers bring up number keyboard, but also will allow for commas.
    // otherwise must use input type="text". accepts data-integer and data-positive attributes
    app.directive('formattedNumber', function ($filter) {
        return {
            require: 'ngModel',
            link: function (scope, elem, attrs, ngModelCtrl) {

                ngModelCtrl.$formatters.push(function (modelValue) {
                    return determineDisplayNumber(modelValue, true);
                });

                // it's best to change the displayed text using elem.val() rather than
                // ngModelCtrl.$setViewValue because the latter will re-trigger the parser
                // and not necessarily in the correct order with the changed value last.
                // see http://radify.io/blog/understanding-ngmodelcontroller-by-example-part-1/
                // for an explanation of how ngModelCtrl works.
                ngModelCtrl.$parsers.push(function (viewValue) {

                    if(attrs.decimals == 0)
                        viewValue = viewValue.replace('.', '');
                    
                    let newVal = determineDisplayNumber(viewValue);

                    if (newVal !== null)
                        elem.val((newVal === '0') ? '' : newVal);
                    
                    // THIS LINE HERE (specifically, the RETURN) is what is actually setting the "ngModel" value.
                    return determineUnderlyingValue(newVal);

                });
                
                // We wish to add default decimal .0's as required.
                // To prevent being unable to delete 'default' ".00"'s we only run this ON BLUR!
                elem.bind('blur', function (e) {
                    let val = this.value;
                    doFinalDecimalRounding(val);
                });

                function doFinalDecimalRounding(val) {
                    // figure out what the 'default' value of val would be if it was null + decimals
                    // so that we can bypass this function if we encounter this state (otherwise you get issue on
                    // first click and so on).
                    let decOnly = ".";
                    for (let d = 0; d < attrs.decimals; d++)
                        decOnly += "0";

                    if (val === null || val === "" || val == decOnly)
                        return;

                    let index = val.indexOf(".");
                    if (index != -1) {

                        // It contains decimals, we format it one last time with the current value,
                        // this time rounding up to the correct number of decimal places.
                        let newVal = determineDisplayNumber(val, true);
                        elem.val((newVal === '0') ? '0' : newVal);
                        
                    } else {

                        // Don't add any decimals if we shouldn't be..
                        if(attrs.decimals == 0)
                            return;
                        
                        // If it contains no decimals add required decimals on the end.
                        val += decOnly;

                        elem.val(val);
                    }
                }

                /**
                 * Determines what the final DISPLAY value of the given value is. Does not set the display. Returns only.
                 * 
                 * @param {any} val The actual value to modify. Can be formatted already or not.
                 * @param {any} roundToDecimals BOOL. If TRUE will round to number of decimals desired (only run onBlur)
                 */
                function determineDisplayNumber(val, roundToDecimals) {

                    var valStr, displayValue;

                    if (typeof val === 'undefined' || val === null) { 
                        return null;
                    }

                    //if (firstClick && val == null && (val === ".0" || val === "0.0")) {
                    //    firstClick = false;
                    //    return null;
                    //}

                    valStr = val.toString();

                    let containsDecimals = valStr.indexOf(".") != -1;

                    displayValue = valStr.replace(/,/g, '').replace(/[A-Za-z]/g, '');
                    displayValue = parseFloat(displayValue);
                    displayValue = (!isNaN(displayValue)) ? displayValue.toString() : '';

                    // handle leading character -/0
                    if (valStr.length === 1 && valStr[0] === '-') {
                        displayValue = valStr[0];
                    } else if (valStr.length === 1 && valStr[0] === '0') {
                        displayValue = '0';
                    } else {
                        displayValue = $filter('number')(displayValue);
                    }

                    // handle decimal
                    if (attrs.decimals != 0) {

                        // If the display value does NOT have decimals (then add them in for some reason wtf)
                        if (displayValue.indexOf('.') === -1) {

                            // If the last character is a dot, add the dot to the final displayValue...
                            if (valStr.slice(-1) === '.') {
                                displayValue += '.';
                            } else if (valStr.slice(-2) === '.0') {
                                displayValue += '.0';
                            } else if (valStr.slice(-3) === '.00') {
                                displayValue += '.00';
                            }
                        }

                        // Generally this is only fired onBlur otherwise all sorts of whacky issues ensue.
                        // Rounds to the number of required decimal places.
                        if (roundToDecimals && containsDecimals) {

                            let split = displayValue.replaceAll(',', '').split(".");

                            // Don't override if all we have is a decimal place atm.
                            if (split.length == 2 && (split[1] != null && split[1] != "")) {

                                let whole = Number(split[0]).toLocaleString();
                                let decimals = "0." + split[1];
                                decimals = parseFloat(decimals);
                                decimals = decimals.toFixed(attrs.decimals);
                                
                                let carry = Number(decimals) >= 1 ? 1 : 0;
                                
                                // Get actual decimal part again.
                                let decimalsOnly = decimals.split(".")[1];
                                displayValue = (whole + carry) + "." + decimalsOnly;
                            }
                        }
                    }

                    if (attrs.positive && displayValue[0] === '-') {
                        displayValue = displayValue.substring(1);
                    }

                    while (displayValue.length > 0 && displayValue.split('.').pop().slice(-1) == '0') {
                        displayValue = displayValue.slice(0, -1);
                    }

                    return displayValue;
                }

                function determineUnderlyingValue(val) {
                    var modelNum = val.toString().replace(/,/g, '').replace(/[A-Za-z]/g, '');
                    modelNum = parseFloat(modelNum);
                    modelNum = (!isNaN(modelNum)) ? modelNum : 0;
                    
                    if (modelNum.toString().indexOf('.') !== -1) {
                        modelNum = Number(modelNum.toFixed(attrs.decimals));
                    }
                    
                    if (attrs.positive) {
                        modelNum = Math.abs(modelNum);
                    }
                    
                    return modelNum;
                }

                // Ideally this would be called in an onLoad or onInit or something
                // but I couldn't figure it out atm and this is already taking too much time.
                // Basically sets the initial rounding to match what is desired.
                setTimeout(() => {
                    let val = elem[0].value;
                    doFinalDecimalRounding(val);
                }, 50);
            }
        };
    });
})();