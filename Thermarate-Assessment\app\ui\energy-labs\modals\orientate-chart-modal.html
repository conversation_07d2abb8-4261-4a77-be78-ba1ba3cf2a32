<md-dialog data-ng-controller='OrientateChartModalCtrl as vm' class="modal-main-container">

    <!-- <Prev Button -->
    <div ng-click="vm.prevChart($event)"
         class="modal-prev-button clickable"
         show="{{vm.chartIndex !== 0}}">
        <i class="fa fa-angle-left fa-0" />
    </div>

    <!-- Chart -->
    <div class="chart-container">

        <!-- Inner Container -->
        <div class="chart-inner-container">

            <!-- Donut Chart -->
            <div id="central-north-offset-donut-chart" class="donut-chart">
            </div>

            <!-- House in Center -->
            <div class="floor-plan-switcher">
                <home-plan-switcher
                    plan-views="vm.resultsList[vm.chartIndex].homeModelFiles"
                    view-3d-floor-plans-enabled="false"
                    view-zoom-enabled="false"
                    floorplanner-link="vm.resultsList[vm.chartIndex].floorplannerLink"
                    small="true"
                ></home-plan-switcher>
            </div>
            <div class="floor-plan-switcher floor-plan-switcher-2">
                <home-plan-switcher
                    plan-views="vm.resultsList[vm.chartIndex].homeModelFiles"
                    view-3d-floor-plans-enabled="false"
                    view-zoom-enabled="false"
                    floorplanner-link="vm.resultsList[vm.chartIndex].floorplannerLink"
                    small="true"
                ></home-plan-switcher>
            </div>
            <!-- Floating 'North Offset' arrows -->
            <div ng-if="vm.resultsList.length > 0"
                    class="el-north-offset-web north-offset-arrows-container">
                <div ng-repeat="option in vm.resultsList[vm.chartIndex].results track by $index"
                        style="position: absolute; transform: rotateZ(calc({{option.northOffset}}deg - 90deg)) translateX(calc(var(--web-distance))) rotateZ(90deg)">
                    <img class="el-orientation-icon" src="/content/images/energy-labs/el-north-arrow-icon.svg" alt="North arrow icon">
                </div>
            </div>

        </div>

    </div>

    <!-- Label -->
    <span class="modal-image-label-1">{{vm.resultsList[vm.chartIndex].label1}}</span>
    <span class="modal-image-label-2">{{vm.resultsList[vm.chartIndex].label2}}</span>

    <!-- Next Button -->
    <div ng-click="vm.nextChart($event)"
         class="modal-next-button clickable"
         show="{{vm.chartIndex < (vm.resultsList.length - 1)}}">
        <i class="fa fa-angle-right fa-0" />
    </div>

    <!-- Cancel Button -->
    <div ng-click="vm.cancel()" class="modal-cancel-button clickable">
        <i class="fa fa-close fa-0" />
    </div>

</md-dialog>
<style>

    /* Backdrop */
    .md-dialog-backdrop {
        opacity: 0.8 !important;
    }

    /* Modal Container */
    .modal-main-container {
        position: static;
        width: 90vw;
        height: 89vh;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: visible;
    }

        /* Chart Container */
        .chart-container {
            margin: auto;
            width: 100%;
            height: 1540px;
            max-height: 100%;
            display: flex;
            justify-content: center;
            overflow-x: hidden;
            overflow-y: auto;
        }
            /* Inner Container */
            .chart-inner-container {
                position: relative;
                width: max-content;
                height: max-content;
                transform: scale(1.1);
            }
                /* Chart */
                .donut-chart {
                    margin-top: 50px;
                    height: 1400px;
                    border-start-start-radius: 60%;
                }
                /* Floor Plan Switcher */
                .floor-plan-switcher {
                    margin-top: 43px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: auto;
                    height: max-content;
                    z-index: -1;
                }
                .floor-plan-switcher-2 {
                    z-index: 50 !important;
                }
                /* North Offset Arrows Container */
                .north-offset-arrows-container {
                    margin-top: 43px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }

    /* Prev/Next Buttons */
    .modal-prev-button,
    .modal-next-button {
        height: 65px;
        width: 65px;
        border-radius: 50%;
        color: white;
        font-size: 45px;
        display: none;
        user-select: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }
    .modal-prev-button:hover,
    .modal-next-button:hover {
        background-color: white;
        color: #afb7bc;
        transition: 150ms linear;
    }
    .modal-prev-button[show='true'],
    .modal-next-button[show='true'] {
        display: block;
    }
    .modal-prev-button {
        left: -90px;
    }
    .modal-prev-button > i {
        margin-left: 21px;
        margin-top: 9px;
    }
    .modal-next-button {
        right: -90px;
    }
    .modal-next-button > i {
        margin-left: 26px;
        margin-top: 9px;
    }

    /* Label */
    .modal-image-label-1,
    .modal-image-label-2 {
        width: 100%;
        text-align: center;
        font-size: 20px;
        position: absolute;
        color: white;
        left: 0px;
        bottom: -48px;
    }
    .modal-image-label-1 {
        bottom: -34px;
    }
    .modal-image-label-2 {
        bottom: -60px;
    }

    /* Hide Chart Button */
    .highcharts-contextbutton {
        visibility: hidden;
    }

    .select-text {
        font-size: 14px;
        text-align: left;
        margin-bottom: 20px;
    }

    .modal-cancel-button {
        color: white;
        border-radius: 50%;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(50vw, -50vh) translate(-110px, 30px);
        font-size: 30px;
        padding: 15px 23px;
    }
    .modal-cancel-button:hover {
        background-color: white;
        color: #b93d0c;
        transition: 150ms linear;
    }

</style>