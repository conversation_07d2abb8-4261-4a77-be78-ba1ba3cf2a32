﻿class WholeOfHomeConstants {

  static class1Dwelling = "Class 1 (Dwelling)";
  static class2Apartment = "Class 2 (Apartment)";
  static nccBuildingClassifications = [
    WholeOfHomeConstants.class1Dwelling,
    WholeOfHomeConstants.class2Apartment
  ];

  static states = [
    "NSW",
    "VIC",
    "QLD",
    "WA",
    "SA",
    "TAS",
    "ACT",
    "NT"
  ]

  static nswClimateZones = [2, 4, 5, 6, 7, 8];
  static vicClimateZones = [4, 6, 7, 8 ];
  static qldClimateZones = [1, 2, 3, 5];
  static saClimateZones = [4, 5, 6];
  static waClimateZones = [1, 3, 4, 5, 6];
  static tasClimateZones = [7, 8];
  static ntClimateZones = [1, 3];
  static actClimateZones = [7];

  static permittedClimateZones = {
    NSW: this.nswClimateZones,
    VIC: this.vicClimateZones,
    QLD:  this.qldClimateZones,
    SA: this.saClimateZones,
    WA: this.waClimateZones,
    TAS: this.tasClimateZones,
    NT: this.ntClimateZones,
    ACT: this.actClimateZones
  };

  static spaceHeatingTypesNotNeedingGemsRating = [
    "ElectricResistancePanel",
    "ElectricResistanceSlab",
    "WoodHeater",
    "Evaporative",
    "OtherOrNoneSpecified"
  ];

  static areaAdjustmentFactors = [
    { min:   0, max:  50, factor: 0.0123 },
    { min:  50, max:  60, factor: 0.0119 },
    { min:  60, max:  70, factor: 0.0116 },
    { min:  70, max:  80, factor: 0.0113 },
    { min:  80, max:  90, factor: 0.0111 },
    { min:  90, max: 100, factor: 0.0108 },
    { min: 100, max: 110, factor: 0.0106 },
    { min: 110, max: 120, factor: 0.0105 },
    { min: 120, max: 130, factor: 0.0103 },
    { min: 130, max: 140, factor: 0.0101 },
    { min: 140, max: 150, factor: 0.0100 },
    { min: 150, max: 160, factor: 0.0099 },
    { min: 160, max: 170, factor: 0.0097 },
    { min: 170, max: 180, factor: 0.0096 },
    { min: 180, max: 190, factor: 0.0095 },
    { min: 190, max: 200, factor: 0.0094 },
    { min: 200, max: 210, factor: 0.0093 },
    { min: 210, max: 220, factor: 0.0092 },
    { min: 220, max: 230, factor: 0.0091 },
    { min: 230, max: 240, factor: 0.0090 },
    { min: 240, max: 250, factor: 0.0090 },
    { min: 250, max: 260, factor: 0.0089 },
    { min: 260, max: 270, factor: 0.0088 },
    { min: 270, max: 280, factor: 0.0087 },
    { min: 280, max: 290, factor: 0.0087 },
    { min: 290, max: 300, factor: 0.0086 },
    { min: 300, max: 310, factor: 0.0085 },
    { min: 310, max: 320, factor: 0.0085 },
    { min: 320, max: 330, factor: 0.0084 },
    { min: 330, max: 340, factor: 0.0083 },
    { min: 340, max: 350, factor: 0.0083 },
    { min: 350, max: 360, factor: 0.0082 },
    { min: 360, max: 370, factor: 0.0082 },
    { min: 370, max: 380, factor: 0.0081 },
    { min: 380, max: 390, factor: 0.0081 },
    { min: 390, max: 400, factor: 0.0080 },
    { min: 400, max: 410, factor: 0.0080 },
    { min: 410, max: 420, factor: 0.0079 },
    { min: 420, max: 430, factor: 0.0079 },
    { min: 430, max: 440, factor: 0.0078 },
    { min: 440, max: 450, factor: 0.0078 },
    { min: 450, max: 460, factor: 0.0077 },
    { min: 460, max: 470, factor: 0.0077 },
    { min: 470, max: 480, factor: 0.0077 },
    { min: 480, max: 490, factor: 0.0076 },
    { min: 490, max: 500, factor: 0.0076 },
    { min: 500, max: Number.MAX_VALUE, factor: 0.0075 }
  ]

  static energyFactors = [

    { state: "NSW", climateZone:	2, class1:	1.32, class2:	1.88 },
    { state: "NSW", climateZone:	4, class1:	1.80, class2:	2.57 },
    { state: "NSW", climateZone:	5, class1:	1.75, class2:	2.50 },
    { state: "NSW", climateZone:	6, class1:	2.40, class2:	3.43 },
    { state: "NSW", climateZone:	7, class1:	2.33, class2:	3.32 },
    { state: "NSW", climateZone:	8, class1:	3.99, class2:	5.70 },
    { state: "VIC", climateZone:	4, class1:	1.25, class2:	1.79 },
    { state: "VIC", climateZone:	6, class1:	1.63, class2:	2.32 },
    { state: "VIC", climateZone:	7, class1:	1.62, class2:	2.32 },
    { state: "VIC", climateZone:	8, class1:	2.82, class2:	4.02 },
    { state: "QLD", climateZone:	1, class1:	2.77, class2:	3.95 },
    { state: "QLD", climateZone:	2, class1:	1.78, class2:	2.54 },
    { state: "QLD", climateZone:	3, class1:	2.46, class2:	3.52 },
    { state: "QLD", climateZone:	5, class1:	2.28, class2:	3.26 },
    { state: "SA",  climateZone:	4, class1:	1.86, class2:	2.65 },
    { state: "SA",  climateZone:	5, class1:	1.79, class2:	2.56 },
    { state: "SA",  climateZone:	6, class1:	2.51, class2:	3.58 },
    { state: "WA",  climateZone:	1, class1:	3.25, class2:	4.64 },
    { state: "WA",  climateZone:	3, class1:	2.87, class2:	4.10 },
    { state: "WA",  climateZone:	4, class1:	2.34, class2:	3.34 },
    { state: "WA",  climateZone:	5, class1:	2.35, class2:	3.36 },
    { state: "WA",  climateZone:	6, class1:	3.20, class2:	4.58 },
    { state: "TAS", climateZone:	7, class1:	3.08, class2:	4.41 },
    { state: "TAS", climateZone:	8, class1:	3.92, class2:	5.60 },
    { state: "NT",  climateZone:	1, class1:	1.91, class2:	2.73 },
    { state: "NT",  climateZone:	3, class1:	1.23, class2:	1.76 },
    { state: "ACT", climateZone:	7, class1:	2.56, class2:	3.66 },
  ];

  static poolPumpFactors = [
    { rating:  1.0, NSW: 0.060, VIC: 0.049, QLD: 0.046, SA: 0.068, WA: 0.063, TAS: 0.061, NT: 0.028, ACT: 0.056 },
    { rating:  1.5, NSW: 0.050, VIC: 0.041, QLD: 0.039, SA: 0.057, WA: 0.053, TAS: 0.052, NT: 0.023, ACT: 0.048 },
    { rating:  2.0, NSW: 0.044, VIC: 0.036, QLD: 0.034, SA: 0.050, WA: 0.046, TAS: 0.045, NT: 0.020, ACT: 0.041 },
    { rating:  2.5, NSW: 0.039, VIC: 0.032, QLD: 0.030, SA: 0.044, WA: 0.041, TAS: 0.040, NT: 0.018, ACT: 0.037 },
    { rating:  3.0, NSW: 0.035, VIC: 0.028, QLD: 0.027, SA: 0.039, WA: 0.036, TAS: 0.035, NT: 0.016, ACT: 0.033 },
    { rating:  3.5, NSW: 0.031, VIC: 0.025, QLD: 0.024, SA: 0.035, WA: 0.033, TAS: 0.032, NT: 0.014, ACT: 0.029 },
    { rating:  4.0, NSW: 0.028, VIC: 0.023, QLD: 0.021, SA: 0.032, WA: 0.029, TAS: 0.029, NT: 0.013, ACT: 0.026 },
    { rating:  4.5, NSW: 0.025, VIC: 0.021, QLD: 0.019, SA: 0.029, WA: 0.027, TAS: 0.026, NT: 0.012, ACT: 0.024 },
    { rating:  5.0, NSW: 0.023, VIC: 0.019, QLD: 0.018, SA: 0.026, WA: 0.024, TAS: 0.023, NT: 0.011, ACT: 0.022 },
    { rating:  5.5, NSW: 0.021, VIC: 0.017, QLD: 0.016, SA: 0.023, WA: 0.022, TAS: 0.021, NT: 0.010, ACT: 0.020 },
    { rating:  6.0, NSW: 0.019, VIC: 0.015, QLD: 0.014, SA: 0.021, WA: 0.020, TAS: 0.019, NT: 0.009, ACT: 0.018 },
    { rating:  6.5, NSW: 0.017, VIC: 0.014, QLD: 0.013, SA: 0.019, WA: 0.018, TAS: 0.017, NT: 0.008, ACT: 0.016 },
    { rating:  7.0, NSW: 0.015, VIC: 0.012, QLD: 0.012, SA: 0.017, WA: 0.016, TAS: 0.016, NT: 0.007, ACT: 0.014 },
    { rating:  7.5, NSW: 0.013, VIC: 0.011, QLD: 0.010, SA: 0.015, WA: 0.014, TAS: 0.014, NT: 0.006, ACT: 0.013 },
    { rating:  8.0, NSW: 0.012, VIC: 0.010, QLD: 0.009, SA: 0.014, WA: 0.013, TAS: 0.012, NT: 0.006, ACT: 0.011 },
    { rating:  8.5, NSW: 0.011, VIC: 0.009, QLD: 0.008, SA: 0.012, WA: 0.011, TAS: 0.011, NT: 0.005, ACT: 0.010 },
    { rating:  9.0, NSW: 0.009, VIC: 0.008, QLD: 0.007, SA: 0.011, WA: 0.010, TAS: 0.010, NT: 0.004, ACT: 0.009 },
    { rating:  9.5, NSW: 0.008, VIC: 0.007, QLD: 0.006, SA: 0.009, WA: 0.009, TAS: 0.008, NT: 0.004, ACT: 0.008 },
    { rating: 10.0, NSW: 0.007, VIC: 0.006, QLD: 0.005, SA: 0.008, WA: 0.007, TAS: 0.007, NT: 0.003, ACT: 0.007 },
  ];

  static spaPumpFactor = {
    NSW: 0.071,
    VIC: 0.058,
    QLD: 0.055,
    SA:  0.081,
    WA:  0.075,
    TAS: 0.073,
    NT:  0.033,
    ACT: 0.067
  };


}
