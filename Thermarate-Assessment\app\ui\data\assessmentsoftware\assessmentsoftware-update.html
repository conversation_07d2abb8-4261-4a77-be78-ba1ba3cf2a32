<form name="assessmentsoftwareform" class="main-content-wrapper" novalidate data-ng-controller='AssessmentsoftwareUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex">
                    <md-card>
                        <md-card-header>
                            Assessment Software
                        </md-card-header>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- ******** Assessment Software Code ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Assessment Software Code</label>
                                    <input type="text" name="assessmentSoftwareCode"
                                           ng-model="vm.assessmentsoftware.assessmentSoftwareCode" 
                                           ng-disabled="vm.newRecord == false"
                                           md-autofocus
                                           md-maxlength="20"
                                           required />
                                    <div ng-messages="assessmentsoftwareform.assessmentSoftwareCode.$error">
                                        <div ng-message="required">Assessment Software Code is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Description ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Description</label>
                                    <input type="text" name="description"
                                           ng-model="vm.assessmentsoftware.description"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="assessmentsoftwareform.description.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Software Version ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Software Version</label>
                                    <input type="text"
                                           name="version"
                                           ng-model="vm.assessmentsoftware.softwareVersion"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="assessmentsoftwareform.version.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Simulation Engine -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Simulation Engine</label>
                                    <md-select name="energyLoadUnits"
                                               ng-model="vm.assessmentsoftware.simulationEngine" required>
                                        <md-option ng-value="'CHENATH'">CHENATH</md-option>
                                        <md-option ng-value="'EnergyPlus'">EnergyPlus</md-option>
                                        <md-option ng-value="'Other'">Other</md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Energy Load Units ******** -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Energy Load Units</label>
                                    <md-select name="energyLoadUnits"
                                               ng-model="vm.assessmentsoftware.energyLoadUnits" required>
                                        <md-option value="kWh">kWh</md-option>
                                        <md-option value="MJ">MJ</md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- ******** Available Assessment Methods ******** -->
                                <h3 style="font-weight: bold; margin-bottom: 14px;">
                                    Available Assessment Methods
                                </h3>
                                <div layout="column"
                                     layout-wrap
                                     flex-gt-sm>

                                    <md-input-container style="margin:0;"
                                                        class="md-block"
                                                        ng-repeat="item in vm.complianceMethodList track by item.complianceMethodCode">
                                        <div>
                                            <md-checkbox ng-model="item.isAvailable"
                                                         redi-enable-roles="settings__settings__edit">
                                                {{item.description}}
                                            </md-checkbox>
                                        </div>
                                    </md-input-container>
                                </div>

                                <h3>Software File Metadata</h3>
                                <table class="table table-striped table-hover table-condensed shadow-z-1">
                                    <thead>
                                    <tr>
                                        <th>File</th>
                                        <th>Display Name</th>
                                        <th>File Extension</th>
                                        <th>Required</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    <tr ng-repeat="fileType in vm.softwareFileTypes">
                                        <td>{{fileType.name}}</td>

                                        <td>
                                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer" flex-gt-sm>
                                                <label>Display Name</label>
                                                <input type="text"
                                                       name="version"
                                                       ng-model="vm.assessmentsoftware[fileType.prop +'Name']"/>
                                            </md-input-container>
                                        </td>

                                        <td>
                                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer" flex-gt-sm>
                                                <label>File Extension</label>
                                                <input type="text"
                                                       name="version"
                                                       ng-model="vm.assessmentsoftware[fileType.prop +'Extension']"/>
                                            </md-input-container>
                                        </td>

                                        <td>
                                            <md-input-container class="md-block vertically-condensed vertically-condensed-ex" flex-gt-sm>
                                                <label>Required</label>
                                                <md-select ng-model="vm.assessmentsoftware[fileType.prop +'Required']"
                                                           style="margin-bottom: 0px;">
                                                    <md-option ng-value="true">Yes</md-option>
                                                    <md-option ng-value="false">No</md-option>
                                                </md-select>
                                            </md-input-container>
                                        </td>

                                    </tr>

                                    </tbody>
                                </table>

                            </fieldset>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.assessmentsoftware"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           ng-disabled="assessmentsoftwareform.$invalid || vm.editPermission == false" 
                           ng-show="vm.assessmentsoftware.deleted!=true" 
                           ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" 
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.assessmentsoftware.assessmentSoftwareCode!=null && vm.assessmentsoftware.deleted!=true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.assessmentsoftware.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
