(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelAssessment', {
      bindings: {
        theModel: '<',          // The 'StandardHomeModel' used as the basis of the option data
        project: '<',
        variableOptions: '<',
        onDataChanged: '&',     // Callback fired when any variable is changed.
        copyAcrossEnabled: '<', // Whether to show Copy Across button
        copyAcrossData: '=?',   // Tell parent which option of which field to set in all buildings
        copyAcrossTrigger: '&', // Trigger Copy Across in parent
        targetEnergyRatingEnabled: '<',
        costEstimateEnabled: '<',
      },
      templateUrl: 'app/ui/energy-labs/standard-model-assessment-form.html',
      controller: StandardModelAssessmentController,
      controllerAs: 'vm'
    });

  StandardModelAssessmentController.$inject = ['$timeout', 'common'];

  function StandardModelAssessmentController($timeout, common) {

    let vm = this;

    vm.onLoadCostEstimateEnabled = vm.costEstimateEnabled;

    vm.targetEnergyRatingOptions = common.targetEnergyRatingOptions();

    vm.showInputFor = function(key) {
      if(vm.project == null) {
        return vm.variableOptions[key] != null;
      } else {
        return vm.project.energyLabsSettings.properties[key] === true;
      }
    }

    vm.refreshVisibility = function () {
        // Show Assessment Method
        if (vm.showInputFor('assessmentMethod')) {
            vm.showAssessmentMethod = true;
        } else {
            vm.showAssessmentMethod = false;
        }
        // Show Target Energy Rating
        if (vm.targetEnergyRatingEnabled && vm.theModel.variableOptions.assessmentMethod == 'House Energy Rating (HER)') {
            vm.showTargetEnergyRating = true;
        } else {
            vm.showTargetEnergyRating = false;
        }
        // Show cost estimate
        if (vm.theModel.variableOptions != null) {
            vm.theModel.variableOptions.costEstimateEnabledDefault = vm.costEstimateEnabled;
        }
    }
    vm.refreshVisibility();

    vm.dataChanged = function () {
        vm.refreshVisibility();
        vm.onDataChanged();
    }

    vm.copyOptionAcross = function (event, field, option) {
        // Tell parent the field and option so it can set on every other building
        vm.copyAcrossData.field = field;
        vm.copyAcrossData.option = option;
        $timeout(() => {
            // Now tell parent to copy across
            vm.copyAcrossTrigger();
            // Make sure option still clicked so dropdown collapses
            event.currentTarget.previousElementSibling.click();
        });
    }

  }

})();