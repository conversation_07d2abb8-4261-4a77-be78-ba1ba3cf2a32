<div data-ng-controller="EnergyLabsIdentifyController as vm"
     style="margin-top: -20px; margin-bottom: 300px;"
     class="el-poppins">

  <div class="el-heading-banner"
       style="height: 350px; padding-top: 30px;">

    <h1 style="margin: 0;">Identify</h1>
    <h3 style="margin: 0;">Identify the best performing home designs for a specific block.</h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>

  </div>
  <hr class="el-title-divider"/>

  <div class="navigation-text" ng-show="vm.project!=null">
    <div ng-click="vm.backToProjects()" class="clickable">Projects</div>
    <div>></div>
    <div ui-sref="energy-labs({projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}} [{{vm.project.clientName}}]</div>
    <div>></div>
    <b>Identify</b>
  </div>

  <div class="central-grid-container" style="margin-top:22px; width:900px;">
    <div class="central-grid-container" style="margin-bottom: 85px;">
      <button ng-click="vm.restoreDefaults()"
              class="el-calculate-button">
        START OVER <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
      </button>
    </div>

    <div ng-form="vm.energyLabsForm"
         class="el-card padded el-card-body"
         style="justify-items: normal; position: relative; box-sizing: border-box;">

        <!-- Drop Down -->
        <md-menu style="z-index:99; position: absolute; right: 25px; top: 20px;">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                    class="clickable el-menu-launch"
                    ng-click="$mdOpenMenu()"
                    src="/content/feather/more-horizontal.svg" />
            <md-menu-content>

                <!-- Restore Defaults -->
                <md-menu-item>
                <md-button ng-click="vm.restoreDefaults();">
                    <span>Restore Defaults</span>
                </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Clear -->
                <md-menu-item>
                <md-button ng-click="vm.clearForm(building);">
                    <span style="color: orangered;">Clear</span>
                </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

      <!-- Design Data -->
      <div>
        <div class="el-section-title">
          <img class="el-section-icon"
               src="content/images/energy-labs/el-design-icon.svg"
               alt="Icon of a floor plan">
          Design
        </div>

        <standard-model-filters-array ng-if="vm.project.energyLabsSettings != null && vm.existingStandardModels != null"
                                      current-list="vm.existingStandardModels"
                                      applied-filters="vm.filterData"
                                      settings="vm.project.energyLabsSettings"
                                      filter-count-data="vm.filterCountData"
                                      on-filter-changed="vm.updateDesignFilters"
                                      on-sort-changed="vm.updateSort">
        </standard-model-filters-array>

      </div>

      <!-- Block Data -->
      <standard-model-block the-model="vm.standardModel"
                            variable-options="vm.availableOptionData"
                            configuration="identify"
                            required="true"
                            disabled="true"
                            on-data-changed="vm.clearResults()"
                            climate-zone-is-invalid="vm.modelBlockShowError"
                            show-north-offset="true">
      </standard-model-block>

      <!-- Specification Data -->
      <standard-model-specifications the-model="vm.standardModel"
                                     variable-options="vm.availableOptionData"
                                     project="vm.project"
                                     required="true"
                                     disabled="false"
                                     on-data-changed="vm.clearResults()">
      </standard-model-specifications>

      <standard-model-assessment the-model="vm.standardModel"
                                 project="vm.project"
                                 variable-options="vm.availableOptionData"
                                 on-data-changed="vm.clearResults()">
      </standard-model-assessment>

    </div>

    <div ng-if="!vm.showResults"
         class="central-grid-container"
         style="margin-top: 5em;">

      <button ng-click="vm.identify()"
              ng-disabled="vm.energyLabsForm.$invalid || vm.isBusy"
              class="el-calculate-button">
        CALCULATE <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
      </button>
    </div>

    <div ng-show="vm.showResults"
         class="el-card padded el-card-body v-margin"
         style="justify-items: normal; overflow: visible;">
      <div>
        <div class="el-section-title">
          <img class="el-section-icon"
               src="content/images/energy-labs/el-performance-icon.svg"
               alt="Icon of a home design which is stamped or has awards">
          Performance
        </div>

        <!-- Empty div that fills screen for click logic -->
        <div class="div-fill-screen"
             ng-if="vm.anyLaunchDropdownsExpanded()"
             ng-click="vm.collapseAllLaunchDropdowns(); $event.stopPropagation();"
        />

        <!-- Table if results found -->
        <table ng-if="vm.matchingOptions.length > 0"
               class="el-identify-performance-table table table-condensed table-data-centered table-hover">
          <thead>
          <tr>
            <th style="border-bottom: none;"></th>
            <th style="border-bottom: none;"></th>
            <th>Heating (MJ/m<sup>2</sup>)</th>
            <th>Cooling (MJ/m<sup>2</sup>)</th>
            <th>Total (MJ/m<sup>2</sup>)</th>
            <th ng-if="vm.matchingOptions[0].assessmentMethod !== 'Performance Solution (Energy Load Limits)'">Energy Rating</th>
          </tr>
          </thead>
          <tbody>
          <!-- Number + Expanded Result -->
          <tr ng-repeat="option in vm.matchingOptions track by $index"
              class="clickable {{option.uiExpanded ? 'row-highlighted' : ''}}"
              style="position: relative !important"
              ng-click="vm.expandResult($event, vm.matchingOptions, option)">
            <td>
                <div style="display: grid; justify-items: center; align-items: center; background-color: var(--thermarate-green); height: 30px; width: 30px;">
                    <span style="color: white;">{{$index + 1}}</span>
                </div>
                <div class="expanded-result-gap" ng-style="{ 'height': !option.uiExpanded ? 0 : vm.expandedHeight(option)+15+'px' }"/>
                <div class="expanded-result"
                     ng-style="{
                        'opacity': !option.uiExpanded ? '0' : '1',
                        'height': !option.uiExpanded ? '0' : vm.expandedHeight(option)+'px',
                        'padding-top': !option.uiExpanded ? 0 : '12px',
                        'padding-bottom': !option.uiExpanded ? 0 : '10px',
                        'overflow': !option.uiExpanded ? 'hidden' : 'visible'
                     }">
                    <div ng-if="option.variationFloorplanName != null" class="expanded-result-item">
                        <div class="expanded-result-label">Floorplan</div>
                        <div class="expanded-result-value">{{option.variationFloorplanName}}</div>
                    </div>
                    <div ng-if="option.variationDesignOptionName != null" class="expanded-result-item">
                        <div class="expanded-result-label">Design Option</div>
                        <div class="expanded-result-value">{{option.variationDesignOptionName}}</div>
                    </div>
                    <div ng-if="option.variationFacadeName != null" class="expanded-result-item">
                        <div class="expanded-result-label">Facade</div>
                        <div class="expanded-result-value">{{option.variationFacadeName}}</div>
                    </div>
                    <div ng-if="option.variationSpecificationName != null" class="expanded-result-item">
                        <div class="expanded-result-label">Specification</div>
                        <div class="expanded-result-value">{{option.variationSpecificationName}}</div>
                    </div>
                    <div ng-if="option.variationConfigurationName != null" class="expanded-result-item">
                        <div class="expanded-result-label">Configuration</div>
                        <div class="expanded-result-value">{{option.variationConfigurationName}}</div>
                    </div>
                    <!-- Configure Button -->
                    <div class="expanded-result-item launch-button-container">
                        <!-- Button -->
                        <div class="launch-button" ng-click="option.launchExpanded = !option.launchExpanded; $event.stopPropagation();">
                            <div class="launch-button-text">LAUNCH</div>
                            <img src="/content/images/energy-labs/el-launch-arrow-icon.svg" style="width:16px; height:auto;" />
                            <!-- Expanded -->
                            <div class="launch-button-options" ng-class="{ 'expanded': option.launchExpanded }">
                                <div ng-if="option.view3dFloorPlans" ng-click="vm.open3dViewerModal(option, $event);$event.stopPropagation();">3D Model</div>
                                <div ng-click="vm.goToOtherTool('configure', option);$event.stopPropagation();">Configure</div>
                                <div ng-click="vm.openSwitcherModal(option, $event);$event.stopPropagation();">Floor Plan</div>
                                <div ng-click="vm.goToOtherTool('optimise', option);$event.stopPropagation();">Optimise</div>
                                <div ng-click="vm.goToOtherTool('woh', option);$event.stopPropagation();">Whole-of-Home</div>
                            </div>
                        </div>
                    </div>
                </div>
            </td>

            <!-- Name -->
            <td class="performance-result-name">
                <div style="margin-top:8px; margin-bottom:-5px; text-align:start; display:flex; font-weight:normal;">
                  <span style="text-align: start">{{option.identifyResultTitle}}</span>
                  <img
                    class="expand-icon"
                    ng-style="{ transform: !option.uiExpanded ? 'rotateX(180deg)' : none }"
                    src="../../../content/images/arrow-up-skinny.png"
                    ng-click="vm.expandResult($event, vm.matchingOptions, option)"
                  />
                </div>
            </td>

            <!-- Heating Load -->
            <td>
                <div class="result-value" ng-style="{ color: vm.determineHeatCoolResultColour(vm.project.energyLabsSettings.heatingCoolingLoadLimits, option, 'heatingLoad', 'heatingLoadLimit') }">
                    {{option.heatingLoad.toFixed(1)}}
                    <!-- Popup with Additional Data -->
                    <md-tooltip class="solid-popup" md-direction="bottom">
                        <el-heating-tooltip source="option" assessment-method="option.assessmentMethod" heating-cooling-load-limits="vm.project.energyLabsSettings.heatingCoolingLoadLimits"></el-heating-tooltip>
                    </md-tooltip>
                </div>
            </td>

            <!-- Cooling Load -->
            <td>
                <div class="result-value" ng-style="{ color: vm.determineHeatCoolResultColour(vm.project.energyLabsSettings.heatingCoolingLoadLimits, option, 'coolingLoad', 'coolingLoadLimit') }">
                    {{option.coolingLoad.toFixed(1)}}
                    <!-- Popup with Additional Data -->
                    <md-tooltip class="solid-popup" md-direction="bottom">
                        <el-cooling-tooltip source="option" assessment-method="option.assessmentMethod" heating-cooling-load-limits="vm.project.energyLabsSettings.heatingCoolingLoadLimits"></el-cooling-tooltip>
                    </md-tooltip>
                </div>
            </td>

            <!-- Total Energy -->
            <td>
                <div class="result-value" ng-style="{ color: option.totalEnergyLoad <= option.energyLoadLimits.calculatedMaxEnergy ? 'var(--thermarate-green)' : 'var(--warning)' }">
                    {{option.totalEnergyLoad.toFixed(1)}}
                    <!-- Popup with Additional Data -->
                    <md-tooltip class="solid-popup" md-direction="bottom">
                        <el-total-tooltip source="option"></el-total-tooltip>
                    </md-tooltip>
                </div>
            </td>

            <!-- Calculated Energy Rating -->
            <td class="energy-rating" ng-if="option.assessmentMethod !== 'Performance Solution (Energy Load Limits)'">
                <div class="result-value" ng-style="{ color: option.energyRating >= 7 ? 'var(--thermarate-green)' : 'var(--warning)' }">
                    {{option.energyRating.toFixed(1)}}
                    <!-- Popup with Additional Data -->
                    <md-tooltip ng-if="option.assessmentMethod !== 'Performance Solution (Energy Load Limits)'"
                                class="solid-popup"
                                md-direction="bottom">
                        <el-rating-tooltip source="option" target-energy-rating="vm.targetEnergyRating"></el-rating-tooltip>
                    </md-tooltip>
                </div>
            </td>
          </tr>
          </tbody>
        </table>

        <!-- Info if no results found -->
        <div ng-if="vm.matchingOptions === null || vm.matchingOptions.length === 0">
          <div class="el-category" style="text-align: center; color: orangered; margin: 2rem;">
            No matches found for given configuration.
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<style>

    .el-identify-performance-table {
        border-collapse: collapse;
    }

    .el-identify-performance-table tr:hover {
        background-color: rgba(0, 0, 0, 0.12);
    }

    .expand-icon {
        visibility: hidden;
        margin-top:-3px;
        margin-left: 7px;
        width: 13px;
        height: 8px;
        padding: 8px 6px;
        border-radius: 6px;
    }

    tr:hover .expand-icon {
        visibility: visible;
    }

    .result-value {
        margin-top: 8px;
        margin-bottom: -6px;
        font-weight: bold;
    }

    .drawing-tooltip {
        position: absolute;
        transform: translateY(-47.4%);
        z-index: 999;
        border-radius: 5px;
        background-color: #eeeeee;
        height: max-content;
        width: max-content;
    }

    .el-identify-performance-table thead tr th {
        text-align: center;
        color: grey;
    }

    .el-identify-performance-table thead tr th.energy-rating {
        font-weight: bold;
        color: #85BA38;
    }

    .el-identify-performance-table tbody tr td {
        text-align: center;
        vertical-align: top !important;
    }

    /* Name */
    .el-identify-performance-table .performance-result-name {
        display: flex;
        column-gap: 12px;
        text-shadow: none;
        user-select: none;
    }

    .el-identify-performance-table tr:hover .performance-result-name,
    .row-highlighted .performance-result-name {
        text-shadow: 0px 0px 1px black;
    }

    /* Energy Rating */
    .el-identify-performance-table tbody tr td.energy-rating {
        color: var(--thermarate-green);
    }

    .preventHover > td {
        background-color: white !important;
        filter: none !important;
        transition: none !important;
    }

    .expanded-result {
        z-index: 50;
        position: absolute !important;
        top: 46px !important;
        left: 50% !important;
        transform: translateX(-50%);
        width: 870px;
        background-color: white;
        transition: all 0.3s ease-out;
    }
    .expanded-result-gap {
        transition: height 0.3s ease-out;
    }

    .expanded-result-item {
        margin: auto;
        margin-top: 6px;
        width: 400px;
        height: max-content;
        padding: 8px;
        border-bottom: var(--thermarate-grey) 1px solid;
    }

    .expanded-result-label {
        margin-bottom: 5px;
        font-size: 9px;
        text-align: left;
    }

    .expanded-result-value {
        text-align: left;
    }

    /* Emptry div that fills screen for click logic */
    .div-fill-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10;
        background-color: transparent;
        cursor: default;
    }
    /* Launch Container */
    .launch-button-container {
        border-bottom: none;
    }
    /* Launch Button */
    .launch-button {
        position: relative;
        float: right;
        width: max-content;
        padding: 6px 15px;
        display: flex;
        column-gap: 10px;
        border-radius: 8px;
        background-color: #cccccc;
        color: white;
        transition: background-color 0.2s;
    }
    /* Launch Button (Hover) */
    .launch-button:hover {
        background-color: var(--thermarate-green);
    }
    /* Launch Button Text */
    .launch-button-text {
        margin-top: 2px;
        font-weight: bold;
    }
    /* Launch Button Options */
    .launch-button-options {
        position: absolute;
        bottom: -10px;
        left: 0;
        transform: scaleY(0) translateY(100%);
        width: max-content;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.12);
        opacity: 0;
        transition: all 0.3s;
        background-color: white;
    }
    /* Launch Button Options Expanded */
    .launch-button-options.expanded {
        transform: scaleY(1) translateY(100%) !important;
        opacity: 1;
    }
    /* Launch Button Options Option */
    .launch-button-options > div {
        padding: 5px 15px;
        color: black;
    }
    /* Launch Button Options Option (Hover) */
    .launch-button-options > div:hover {
        background-color: var(--thermarate-grey);
    }

    .el-identify-performance-table tr:hover td {
        filter: none !important; /* This fixes issue where hovering over row would cause expanded results div to shift to left */
        background-color: transparent !important;
    }

</style>