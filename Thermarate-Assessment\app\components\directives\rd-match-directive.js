﻿(function () {
    'use strict';

    var app = angular.module('app');
    /*!
    * angular-input-match
    * Checks if one input matches another
    * @version v1.0.0
    * @link https://github.com/TheSharpieOne/angular-input-match
    * @license MIT License, http://www.opensource.org/licenses/MIT
    */
    app.directive('rdMatch', match);
    function match() {
        return {
            require: '?ngModel',
            restrict: 'A',
            scope: {
                rdMatch: '='
            },
            link: function (scope, elem, attrs, ctrl) {
                if (!ctrl) {
                    console && console.warn('Match validation requires ngModel to be on the element');
                    return;
                }

                scope.$watch(function () {
                    var modelValue = angular.isUndefined(ctrl.$modelValue) ? ctrl.$$invalidModelValue : ctrl.$modelValue;
                    return (ctrl.$pristine && angular.isUndefined(modelValue)) || scope.rdMatch === modelValue;
                }, function (currentValue) {
                    ctrl.$setValidity('rdMatch', currentValue);
                });
            }
        };
    }
})();