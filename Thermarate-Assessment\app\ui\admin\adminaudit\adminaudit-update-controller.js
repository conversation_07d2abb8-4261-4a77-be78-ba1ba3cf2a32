(function () {
    // The AdminauditUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'AdminauditUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'adminauditservice', adminauditUpdateController]);
function adminauditUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  adminauditservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Admin Audit';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.adminAuditId = null;
        vm.adminaudit = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Admin Audit";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.adminAuditId = $scope.adminAuditId;
            }
            vm.hideActionBar = true;
        } else {
            vm.adminAuditId = $stateParams.adminAuditId;
        }

        // Get data for object to display on page
        var adminAuditIdPromise = null;
        if (vm.adminAuditId != null) {
            adminAuditIdPromise = adminauditservice.getAdminAudit(vm.adminAuditId)
            .then(function (data) {
                if (data != null) {
                    vm.adminaudit = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("adminaudit-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                adminauditservice.createAdminAudit(vm.adminaudit).then(function(data){
                    vm.adminaudit = data;
                    vm.adminAuditId = vm.adminaudit.adminAuditId;
                    vm.cancel();
                });
            }else{
                adminauditservice.updateAdminAudit(vm.adminaudit).then(function(data){
                    if (data != null) {
                        vm.adminaudit = data;
                        vm.adminAuditId = vm.adminaudit.adminAuditId;
                    }
                });
            }
        }

        vm.delete = function () {
            adminauditservice.deleteAdminAudit(vm.adminAuditId).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            adminauditservice.undoDeleteAdminAudit(vm.adminAuditId).then(function () {
                vm.cancel();
            });
        }

    }
})();