<form name="VariationScratchImportModal" data-ng-controller='VariationScratchImportModalCtrl as vm'>

    <!-- Title -->
    <div data-cc-widget-header
         data-title="Scratch Import"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <!-- ------------------ -->
    <!-- Body (File Upload) -->
    <!-- ------------------ -->
    <div ng-if="!vm.showZoneResults" class="content-container" style="width: 1000px;">

        <!-- Assessment Software -->
        <md-input-container class="md-block vertically-condensed">
            <label>Assessment Software</label>
            <md-select name="assessmentSoftwareCode"
                       ng-model="vm.selectedAssessmentSoftware"
                       ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}"
                       ng-required>
                <md-option ng-value="item"
                           ng-repeat="item in vm.assessmentSoftwareList track by item.assessmentSoftwareCode">
                    {{item.description}}
                </md-option>
            </md-select>
        </md-input-container>

        <!-- Scratch file upload + Process button -->
        <div>

            <!-- Initialising -->
            <div ng-if="!vm.initialised"
                 data-cc-spinner="vm.spinnerOptions"
                 style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                    background-color: rgba(255, 255, 255, 0.8); z-index: 999;
                    display: flex; flex-direction: column; justify-content: center; align-items: center;" />

            <!-- Is Busy -->
            <div ng-if="vm.processingScratch"
                 class="export-processing-overlay">
                <div>
                    <h2>Processing scratch file, please wait...</h2>
                </div>
            </div>

            <!-- Files -->
            <h3>Scratch Data</h3>
            <div style="display: grid; grid-template-columns: 6fr 4fr; align-items: center; margin-bottom: 12px;">

                <div ng-if="vm.selectedAssessmentSoftware == null">
                    Please select an Assessment Software to be shown upload options.
                </div>

                <table ng-if="vm.selectedAssessmentSoftware != null">
                    <tbody>
                    <tr ng-repeat="fileType in vm.softwareFileTypes"
                        ng-if="vm.selectedAssessmentSoftware[fileType.prop  + 'Required'] === true && fileType.prop !== 'fileA'"
                        ngf-drop="vm.selectScratchFile($file, fileType.prop)"
                        ngf-drag-over-class="'file-dragover'">

                        <!-- Software file display name -->
                        <td style="width: 170px;">
                            {{vm.selectedAssessmentSoftware[fileType.prop + 'Name']}}
                        </td>

                        <!-- Actual upload button -->
                        <td style="display: grid; grid-template-columns: 300px 60px; align-items: center;">

                            <!-- File name -->
                            <md-input-container class="md-block vertically-condenxed kindly-remove-error-spacer">
                                <input ng-disabled="true"
                                        ng-value="vm.scratchData[fileType.prop].name || 'Upload a File'"/>
                            </md-input-container>

                            <!-- Upload Button -->
                            <md-button ng-if="vm.scratchData[fileType.prop] == null"
                                        class="md-raised md-icon-button small-icon-button"
                                        ngf-select="vm.selectScratchFile($file, fileType.prop)"
                                        ngf-accept="vm.selectedAssessmentSoftware[fileType.prop + 'Extension']"
                                        style="justify-self: end;">
                                <i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>
                            </md-button>

                            <!-- Delete Button -->
                            <button ng-if="vm.scratchData[fileType.prop] != null"
                                    class="feather-icon-button"
                                    ng-click="vm.scratchData[fileType.prop] = null;"
                                    style="justify-self: end;">
                                <img src="/content/feather/trash.svg" />
                            </button>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <!-- Process button -->
                <md-button class="md-raised md-primary"
                            ng-disabled="!vm.scratchFilesAreValid(vm.scratchData, vm.softwareFileTypes)"
                            ng-click="vm.process()">
                    PROCESS
                </md-button>
            </div>
        </div>

    </div>

    <!-- ---------------- -->
    <!-- Body (Zone List) -->
    <!-- ---------------- -->
    <div ng-if="vm.showZoneResults" class="content-container" style="width: 1500px;">

        <div class="table-responsive-vertical shadow-z-1" style="margin-bottom:15px; padding-top:1px;">

            <table class="table table-striped table-hover table-condensed">
                <!-- Header -->
                <thead>
                    <tr>
                        <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneNumber');">
                            Zone Number
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneNumber' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneNumber' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneDescription');">
                            Zone Name
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneDescription' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneDescription' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneActivity.description');">
                            Zone Activity
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneActivity.description' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneActivity.description' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneType.description');">
                            Zone Type
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneType.description' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'zoneType.description' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'conditioned');">
                            Conditioned
                            <i ng-if="vm.InteriorSortInfo.column == 'conditioned' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'conditioned' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'naturallyVentilated');">
                            Naturally Ventilated
                            <i ng-if="vm.InteriorSortInfo.column == 'naturallyVentilated' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'naturallyVentilated' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'nccClassification.nccClassificationCode');">
                            NCC Classification
                            <i ng-if="vm.InteriorSortInfo.column == 'nccClassification.nccClassificationCode' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'nccClassification.nccClassificationCode' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'floorArea');">
                            Floor Area (m<sup>2</sup>)
                            <i ng-if="vm.InteriorSortInfo.column == 'floorArea' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'floorArea' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'ceilingArea');">
                            Ceiling Area (m<sup>2</sup>)
                            <i ng-if="vm.InteriorSortInfo.column == 'ceilingArea' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'ceilingArea' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'volume');">
                            Volume (m<sup>3</sup>)
                            <i ng-if="vm.InteriorSortInfo.column == 'volume' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'volume' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'storey');">
                            Storey
                            <i ng-if="vm.InteriorSortInfo.column == 'storey' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.InteriorSortInfo.column == 'storey' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                    </tr>

                </thead>
                <!-- Body -->
                <tbody>

                    <tr ng-repeat="item in vm.interiorZones()"
                        lr-drag-src="zones"
                        lr-drop-target="zones"
                        lr-drop-success="vm.renumberZones()"
                        lr-match-property="zoneId"
                        lr-drag-data="vm.building.zones"
                        lr-match-value="{{item.zoneId}}"
                        lr-index="vm.building.zones.indexOf(item)">

                        <!-- Zone Number -->
                        <td data-title="Zone Number">
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                <input type="text"
                                        name="ZoneNumber{{$index}}"
                                        ng-model="item.zoneNumber"
                                        required
                                        ng-blur="vm.updateSource(item, item.zoneNumber, 'Z');" />
                                <div ng-messages="ZoneListForm['ZoneNumber'+$index].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Zone Name -->
                        <td data-title="Zone Name"
                            lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <input type="text"
                                        name="ZoneDescription"
                                        ng-model="item.zoneDescription"
                                        ng-model-options="{ updateOn: 'blur' }"
                                        required/>
                                <div ng-messages="ZoneListForm['ZoneDescription'].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Zone Activity -->
                        <td data-title="Zone Activity" class="text-left" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                            style="margin: 0;"
                                            ng-model="item.zoneActivity"
                                            ng-model-options="{trackBy: '$value.zoneActivityCode'}"
                                            required>
                                    <md-option ng-repeat="v in vm.interiorZoneActivityList()"
                                                ng-value="v"
                                                ng-click="vm.zoneActivityChanged(v, item)">
                                        {{v.description}}
                                    </md-option>

                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Zone Type -->
                        <td data-title="Zone Type" class="text-left" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                            style="margin: 0;"
                                            ng-model="item.zoneType"
                                            ng-model-options="{trackBy: '$value.zoneTypeCode'}"
                                            required>
                                    <md-option ng-repeat="v in vm.interiorZoneTypeList()"
                                                ng-value="v"
                                                ng-click="vm.zoneTypeChanged(v, item, true);">
                                        {{v.description}}
                                    </md-option>

                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Conditioned -->
                        <td data-title="Conditioned" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <md-select name="HabitableRoom"
                                            style="margin: 0;"
                                            ng-model="item.conditioned"
                                            required
                                            ng-change="vm.calculateAllowance(item)">
                                    <md-option ng-value="true"
                                                ng-click="vm.calculateAllowance(item)">
                                        Yes
                                    </md-option>
                                    <md-option ng-value="false"
                                                ng-click="vm.calculateAllowance(item)">
                                        No
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Naturally Ventilated -->
                        <td data-title="Naturally Ventilated" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                <md-select name="NaturallyVentilated"
                                            style="margin: 0;"
                                            ng-model="item.naturallyVentilated"
                                            disabled
                                            required>
                                    <md-option ng-value="true">
                                        Yes
                                    </md-option>
                                    <md-option ng-value="false">
                                        No
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- NCC Classification -->
                        <td data-title="NCC Classification" class="text-left" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                            style="margin: 0;"
                                            ng-model="item.nccClassification"
                                            ng-model-options="{trackBy: '$value.nccClassificationCode'}"
                                            required>
                                    <md-option ng-repeat="v in vm.availableInteriorNccClassificationList()"
                                                ng-value="v"
                                                ng-click="vm.onNccClassificationUpdate(item)">
                                        {{v.description}}
                                    </md-option>

                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Floor Area -->
                        <td data-title="Floor Area" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                <input type="text" name="FloorArea{{$index}}"
                                        ng-model="item.floorArea"
                                        ng-change="vm.calculateAllowance(item);"
                                        ng-blur="vm.matchCeilingAreaToFloorArea(item);"
                                        ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                        formatted-number
                                        decimals="2" />
                                <div ng-messages="ZoneListForm['FloorArea'+$index].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Ceiling Area -->
                        <td data-title="Ceiling Area" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                <input id="CeilingAreaInput{{item.zoneId}}"
                                        type="text"
                                        name="CeilingArea{{$index}}"
                                        ng-model="item.ceilingArea"
                                        ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                        formatted-number
                                        decimals="2" />
                            </md-input-container>
                        </td>

                        <!-- Volume -->
                        <td data-title="Volume" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <input type="text" name="Volume{{$index}}"
                                        ng-model="item.volume"
                                        ng-change="vm.calculateAllowance(item);"
                                        ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                        formatted-number
                                        decimals="2" />
                                <div ng-messages="ZoneListForm['Volume'+$index].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Storey -->
                        <td data-title="Storey" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <md-select required
                                            style="margin: 0;"
                                            name="Storey{{$index}}"
                                            ng-model="item.storey"
                                            ng-change="vm.calculateAllowance(item)">
                                    <md-option ng-value="x.floor"
                                                ng-repeat="x in vm.building.storeys">
                                        {{x.name}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>

                        <!-- Action Buttons -->
                        <td data-title="Clear"
                            lr-no-drag
                            class="text-center">
                            <!-- 'More' button w/ Popup -->
                            <md-menu style="display: flex; justify-content: center; align-content: center;">
                                <!-- Initial '...' button, which launches options -->
                                <img md-menu-origin
                                     class="clickable"
                                     ng-click="$mdOpenMenu()"
                                     src="/content/feather/more-horizontal.svg"
                                     ng-disabled="vm.disabled"/>
                                <md-menu-content>
                                    <md-menu-item>
                                        <!-- Duplicate -->
                                        <md-button ng-click="vm.cloneZone(item)">
                                            Duplicate
                                        </md-button>
                                    </md-menu-item>
                                    <md-menu-divider></md-menu-divider>
                                    <md-menu-item>
                                        <!-- Delete -->
                                        <md-button ng-click="vm.removeZone(item)">
                                            <span style="color: orangered;">Delete</span>
                                        </md-button>
                                    </md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </td>

                    </tr>

                </tbody>

            </table>

            <div layout="row" style="padding: 10px 2px;">
                <md-button class="md-raised md-primary"
                           ng-click="vm.addZone(vm.interiorZones(), null, 'Z')">
                    Add
                </md-button>
                <div style="flex-grow:1;" />
                <md-button class="md-raised md-primary"
                           ng-click="vm.saveResults()">
                    SAVE TO VARIATION
                </md-button>
            </div>
        </div>

    </div>

</form>

<style>

    /* Body */
    .content-container {
        padding: 25px 20px 10px 20px;
        box-sizing: border-box;
    }

    /*  */
    .export-processing-overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: grid;
        align-items: center;
        justify-items: center;
        z-index: 9999;
        background-color: rgba(255, 255, 255, 0.75);
    }

    .glazing-calc-modal-header {
        display: grid;
        grid-template-columns: 1fr 200px 30px;
        align-items: center;
        background-color: var(--thermarate-grey);
        padding: 0 24px 0 16px;
        height: 64px;
        max-height: 64px;
    }

        .glazing-calc-modal-header > h4 {
            margin: 0;
        }

</style>