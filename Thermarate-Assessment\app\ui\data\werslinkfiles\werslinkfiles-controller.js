(function () {

    // The WorksDescriptionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'WersLinkFilesCtrl';
    angular.module('app')
        .controller(controllerId, ['fileservice', worksDescriptionUpdateController]);

    function worksDescriptionUpdateController(fileservice) {
        var vm = this;

        vm.title = "WERS Link Files"
        vm.residentialFile = { category: "Wers Link Residential" };
        vm.commercialFile = { category: "Wers Link Commercial" };

        async function initialise() {
            vm.isBusy = true;

            let residentialResults = await fileservice.getByCategory(vm.residentialFile.category);
            if (residentialResults != null && residentialResults.length > 0) {
                vm.residentialFile.file = residentialResults[0];
                vm.residentialFileId = vm.residentialFile.file.fileId;
            }

            let commercialResults = await fileservice.getByCategory(vm.commercialFile.category);
            if (commercialResults != null && commercialResults.length > 0) {
                vm.commercialFile.file = commercialResults[0];
            }

            vm.isBusy = false;
        }
        initialise();

        vm.residentialOnChange = function (uploadedFile) {
            if (uploadedFile == null) {
                fileservice.deleteForCategory(vm.residentialFile.category);
            }
        }

        vm.commercialOnChange = function (uploadedFile) {
            if (uploadedFile == null) {
                fileservice.deleteForCategory(vm.commercialFile.category);
            }
        }

    }
})();