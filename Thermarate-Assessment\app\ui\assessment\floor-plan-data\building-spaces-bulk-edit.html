<form name="BuildingSpacesBulkEdit"
      data-ng-controller='BuildingSpacesBulkEditCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="{{vm.type != 'roofs' ? 'Spaces' : 'Roofs'}} Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row"
                        ng-model="vm.data.bulkEditAction"
                        ng-change="vm.clearAddress()"
                        ng-disabled="vm.isLocked">
            <md-radio-button ng-value="'EDIT'">
                Edit
            </md-radio-button>
            <md-radio-button ng-value="'COPY'"
                             ng-click="vm.clearEditValues()">
                Copy
            </md-radio-button>
            <md-radio-button ng-value="'CLEAR'" 
                             ng-click="vm.clearEditValues()">
                Clear
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'" 
                             ng-click="vm.clearEditValues()">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width:600px; padding: 10px 20px;">

        <fieldset id="edit-inputs"
                  ng-if="vm.data.bulkEditAction == 'EDIT'">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Space Name -->
                    <tr>
                        <td>
                            {{vm.type != "roofs" ? "Space" : "Roof"}} Name
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.zoneDescription" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Space Type -->
                    <tr ng-if="vm.type != 'roofs'">
                        <td>
                            Space Type
                        </td>
                        <td>
                            <md-select ng-model="vm.data.zoneType"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-repeat="zoneType in vm.zoneTypeList"
                                           ng-value="zoneType">
                                    {{zoneType.description}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Location -->
                    <tr ng-if="vm.type != 'roofs'">
                        <td>
                            Location
                        </td>
                        <td>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                           style="margin: 0;"
                                           ng-model="vm.data.location">
                                    <md-option ng-value="'Back Centre'">Back Centre</md-option>
                                    <md-option ng-value="'Back Left'">Back Left</md-option>
                                    <md-option ng-value="'Back Right'">Back Right</md-option>
                                    <md-option ng-value="'Front Centre'">Front Centre</md-option>
                                    <md-option ng-value="'Front Left'">Front Left</md-option>
                                    <md-option ng-value="'Front Right'">Front Right</md-option>
                                    <md-option ng-value="'None'">None</md-option>
                                    <md-option ng-value="'Other'">Other</md-option>
                                    <md-option ng-value="null">
                                        Don't Change
                                    </md-option>
                                </md-select>
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Floor Area (m2) -->
                    <tr ng-if="vm.type != 'roofs'">
                        <td>
                            Floor Area (m<sup>2</sup>)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed  kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.floorArea"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Perimeter (m3) -->
                    <tr>
                        <td>
                            Perimeter (m)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.perimeter"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Storey -->
                    <tr>
                        <td>
                            Storey
                        </td>
                        <td>
                            <md-select ng-model="vm.data.storey"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="storey.floor"
                                           ng-repeat="storey in vm.storeys">
                                    {{storey.name}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Spaces will be copied. Copied spaces will be inserted below their source Space.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'CLEAR'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Spaces will have their data cleared.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Spaces will be deleted. </span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

            <div class="clearfix"></div>
        </div>

    </div>

</form>