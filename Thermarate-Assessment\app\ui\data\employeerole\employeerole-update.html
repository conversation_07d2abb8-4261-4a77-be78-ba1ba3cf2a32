<form name="employeeroleform" class="main-content-wrapper" novalidate data-ng-controller='EmployeeroleUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div flex-gt-sm="50">
                    <md-card>
                        <md-card-header>
                            Business Role
                        </md-card-header>
                        <md-card-content>

                          <fieldset redi-enable-roles="settings__settings__edit">

                            <!-- ******** Role Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Role Code</label>
                              <input type="text" name="roleCode" ng-disabled="vm.newRecord === false"
                                     ng-model="vm.employeerole.roleCode" md-autofocus
                                     md-maxlength="20"
                                     required
                              />
                              <div ng-messages="employeeroleform.roleCode.$error">
                                <div ng-message="required">Role Code is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                              </div>
                            </md-input-container>

                            <!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Description</label>
                              <input type="text" name="description"
                                     ng-model="vm.employeerole.description"
                                     md-maxlength="100"
                                     required
                              />
                              <div ng-messages="employeeroleform.description.$error">
                                <div ng-message="required">Description is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                              </div>
                            </md-input-container>

                          </fieldset>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.employeerole"></div>
                        </div>
                    </md-card-content>
                </md-card>
                </div>
                <div flex-gt-sm="50">
                    <md-card>
                        <md-card-header>
                            Permissions
                        </md-card-header>
                        <md-card-content>

                          <div ng-repeat="group in vm.groupedRoles">

                            <h2 style="margin-top: 20px;">{{group.title}}</h2>

                            <div ng-if="group.subgroups.length == 0"
                                 class="permissions-group">
                              <md-checkbox style="display: block;"
                                           ng-repeat="role in group.roles"
                                           id="{{role.roleCode}}"
                                           value="{{role.roleCode}}"
                                           ng-disabled="vm.editPermission == false"
                                           ng-checked="vm.employeerole.aspnetRoles.indexOf(role.roleCode) != -1"
                                           ng-click="toggleSelection(role.roleCode)">
                                {{role.description || role.roleCode}}
                              </md-checkbox>
                            </div>

                            <!-- 'Assessment' group has special display method -->
                            <div ng-if="group.subgroups.length > 0 && group.title != 'Assessment'"
                                 style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr">
                              <div ng-repeat="subgroup in group.subgroups"
                                   class="permissions-group">
                                <h3>{{subgroup.title}}</h3>
                                <md-checkbox style="display: block;"
                                             ng-repeat="role in subgroup.roles"
                                             id="{{role.roleCode}}"
                                             value="{{role.roleCode}}"
                                             ng-disabled="vm.editPermission == false"
                                             ng-checked="vm.employeerole.aspnetRoles.indexOf(role.roleCode) != -1"
                                             ng-click="toggleSelection(role.roleCode)">
                                  {{role.description || role.roleCode}}
                                </md-checkbox>
                              </div>
                            </div>

                            <div ng-if="group.subgroups.length > 0 && group.title == 'Assessment'"
                                 style="display: grid; grid-template-columns: 1fr">

                              <div ng-repeat="subgroup in group.subgroups"
                                   class="permissions-group">
                                <h3>{{subgroup.title}}</h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;">
                                  <md-checkbox style="display: block;"
                                               ng-repeat="role in subgroup.roles"
                                               id="{{role.roleCode}}"
                                               value="{{role.roleCode}}"
                                               ng-disabled="vm.editPermission == false"
                                               ng-checked="vm.employeerole.aspnetRoles.indexOf(role.roleCode) != -1"
                                               ng-click="toggleSelection(role.roleCode)">
                                    {{role.description || role.roleCode}}
                                  </md-checkbox>
                                </div>

                              </div>

                            </div>

                          </div>
                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="employeeroleform.$invalid" ng-show="vm.employeerole.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.employeerole.roleCode!=null && vm.employeerole.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.employeerole.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
