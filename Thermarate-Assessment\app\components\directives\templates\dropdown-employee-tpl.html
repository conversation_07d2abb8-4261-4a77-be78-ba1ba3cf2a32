﻿<ui-select name="userId"
           ng-model="$parent.model"
           theme="bootstrap"
           reset-search-input="false"
           class=""
           ng-required="$parent.isrequired"
           focus-on="userId">
    <ui-select-match placeholder="Employee" title="employee">
        <abbr ng-hide="{{hideRemove}}" ng-if="!$select.isEmpty()" class="search-choice-clear" ng-click="$select.select(undefined)"></abbr>
        {{$select.selected.fullName}}
    </ui-select-match>
    <ui-select-choices repeat="srow.userId as srow in employees | filter: $select.search"
                       refresh-delay="0">
        <div ng-bind-html="srow.fullName | highlight: $select.search">
        </div>
    </ui-select-choices>
</ui-select>
