// Service used to retreive weather data for specific NCC Climate Zones 
// (e.g.climate zone 8, climate zone 43, etc)
(function () {
    'use strict';

    const SERVICE_ID = 'wholeofhomedataservice';

    angular.module('appservices').factory(SERVICE_ID,
        ['common', 'config', '$http', 'Upload', wholeofhomedata]);

    function wholeofhomedata(common, config, $http, Upload) {

        const log = common.logger;
        const baseUrl = config.servicesUrlPrefix + 'wholeofhomedata/';

        // These are the operations that are available from this service.
        var service = {
            getEfficiencyFactor,
            processExcel,
        };

        /**
         * Returns a (giant) array of data broken up between years/months/days, including
         * information such as temperature, average rainfall, and so on.
         * 
         * @param {any} climateZone The NCC Climate Zone of the data we want.
         */
        async function getEfficiencyFactor(stateCode,
            climateZone,
            heatingServiceCode,
            coolingServiceCode,
            gemsYear,
            heatingEnergyRating,
            coolingEnergyRating) {
            
            if(stateCode == null || climateZone == null || heatingServiceCode == null ||
               coolingServiceCode == null || gemsYear == null || heatingEnergyRating == null ||
                coolingEnergyRating == null)
                return null;

            return $http({
                url: baseUrl + 'getEfficiencyFactor',
                params: { stateCode, climateZone, heatingServiceCode, coolingServiceCode, gemsYear, heatingEnergyRating, coolingEnergyRating },
                method: 'GET',
            }).then(
                (response) => {
                    if(response != null && response.data != null)
                        return response.data[0];
                    else 
                        return null;
                },

                (error) => handleFail(error, "Error getting Efficiency Factor data."));
        }

        /**
         * Uploads an excel file to our server which is then extracted and 
         * processed into the database, over-writing any prior WoH datasets.
         * 
         * @param {any} excelFile The excel file to upload.
         */
        function processExcel(excelFile) {

            let url = baseUrl + 'ProcessExcel';
            return $http({
                url: url, 
                method: "POST",
                data: excelFile,
                headers: {
                    "Content-Type": "application/zip"
                }
            }).then(
                (data) => handleSuccess(data, "Database successfully Upated"),
                (error) => handleFail(error, "Error processing zip file!")
            );
        }
        
        /** Generic success handling function. Checks for success and returns data if so. */
        function handleSuccess(response, popupMessage = null) {
            console.log("Got response", response);
            if (response != null && response.data != null) {

                if (popupMessage != null)
                    log.logSuccess(popupMessage);
                
                return response.data;
            }
            else {
                return null;
            }
        }

        /** Generic failure handling function. Logs a message with small popup. */
        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        return service;

    }
})();
