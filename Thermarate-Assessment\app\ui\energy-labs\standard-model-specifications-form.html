<!-- Specifications (Every variable not found in Block) -->
<div ng-if="vm.showAnyInputs()">

  <div class="el-section-title">
    <img class="el-section-icon"
         src="content/images/energy-labs/el-specifications-icon.svg"
         alt="Icon of cartoon houses divided into their land blocks">
    Specifications
  </div>

  <!-- 
  Generically loop over all known properties for this project and if they are enabled, show all of the
  corresponding variables available for the given standard model
  -->
  <div ng-repeat="key in vm.propertiesInOrder() track by $index" class="el-filter {{vm.multiSelect && vm.anyOptionsSelectedOnField({field: key}, vm.theModel.optionData) ? 'options-selected' : ''}}">

    <label ng-if="vm.multiSelect" class="el-filter-label specification-label">{{vm.keyToName(key)}} *</label>
    <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

        <!-- Single Select -->
        <label ng-if="!vm.multiSelect" class="el-filter-label specification-label">{{vm.keyToName(key)}} *</label>
        <md-select ng-if="!vm.multiSelect"
                   name="{{vm.keyToName(key)}}"
                   ng-required="true"
                   ng-change="vm.dataChanged()"
                   ng-model="vm.theModel.optionData[key]">
        <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions[key] track by $index">
            <md-option ng-value="option">
                {{option}}
            </md-option>
            <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, key, option);"><img src="/content/images/share.png"/></div>
        </div>
        </md-select>

        <!-- Multi Select -->
        <img src="/content/images/cross.png"
             class="el-filter-clear-button"
             ng-click="vm.clearFilter(key);$event.stopPropagation()"
        />
        <md-select ng-if="vm.multiSelect"
                   name="{{vm.keyToName(key)}}"
                   ng-required="false"
                   ng-change="vm.dataChanged()"
                   ng-model="vm.theModel.optionData[key]"
                   style="margin-bottom:8px"
                   md-selected-text="vm.multiFieldText(key)"
                   multiple="true">
            <div class="custom-dropdown-option">
                <md-option>
                    Any 
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, key, 'Any');"><img src="/content/images/share.png"/></div>
            </div>
            <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions[key] track by $index">
                <md-option ng-value="option">
                    {{option}} 
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, key, option);"><img src="/content/images/share.png"/></div>
            </div>
        </md-select>

    </md-input-container>
  </div>

</div>

<style>

    .el-filter {
        position: relative;
    }

    .specification-title {
        margin-bottom: 0px;
    }

    .specification-label {
        font-size: 9px;
        color: rgba(0,0,0,0.54);
    }

    .el-filter-clear-button {
        display: none;
        position: absolute;
        right: 14px;
        bottom: 17px;
        z-index: 50;
        width: 9px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

        .el-filter-clear-button:hover {
            background-color: #f5f5f5;
        }

        .el-filter.options-selected .el-filter-clear-button {
            display: inherit !important;
        }

        .el-filter.options-selected .md-select-icon {
            margin-left: -53px;
        }

</style>