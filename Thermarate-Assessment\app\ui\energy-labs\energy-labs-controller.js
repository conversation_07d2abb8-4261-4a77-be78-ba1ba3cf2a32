(function () {

    'use strict';
    var controllerId = 'EnergyLabsHomeController';
    angular.module('app')
        .controller(controllerId, ['common', '$state', '$stateParams', 'projectservice', energyLabsHomeController]);

    function energyLabsHomeController(common, $state, $stateParams, projectservice) {

        var vm = this;

        vm.backToProjects = function () {
          $state.go('energy-labs-parent-menu');
        }

        projectservice.getProject($stateParams.projectId).then((data) => vm.project = data);

        //Return to home page
        vm.cancel = function () {
            $state.go("/");
        }
    }
})();