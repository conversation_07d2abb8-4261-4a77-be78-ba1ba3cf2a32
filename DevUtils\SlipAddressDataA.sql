USE [thermarate];

SELECT TOP 1000
       [SlipAddressDataId]
      ,[LandId]
      ,[SurveyType]
      ,[SurveyNumber]
      ,[LotNumber]
      ,[UnitType]
      ,[UnitNumber]
      ,[RoadNumber]
      ,[RoadName]
      ,[RoadType]
      ,[Locality]
      ,[PostCode]
      ,[FormattedAddress]
      ,[Latitude]
      ,[Longitude]
      ,[LandArea]
      ,[TitleIdentifier]
      ,[ProjectOwner]
      ,[Boundary]
      ,[BoundaryString]
      ,[BoundaryJson]
  FROM [dbo].[RSS_SlipAddressDataA]