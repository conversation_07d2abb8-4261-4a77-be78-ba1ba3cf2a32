// Name: integrationservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'integrationservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', integrationauditservice]);

    function integrationauditservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
      
        var service = {
            /* These are the operations that are available from this service. */
            currentFilter: function () { return currentFilter },      
            getintegrationAudit: getintegrationAudit,
            getintegrationAuditList: getIntegrationAuditList,
            
        };

        return service;

        //#region main application operations
        // ----------------------------------

        //Fetch Integration Audit List from server
        function getIntegrationAuditList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {
          
            var wkUrl = '../api/integrationaudit/Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            var filterdates = "";

            currentFilter = forFilter;
            switch (forFilter) {
                case 'Deleted':
                    wkUrl = '../api/integrationaudit/GetDeleted'; // Gets all deleted Integration Audit
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get Integration Audit List from the Server 
            var params = {fromDate: fromDate, toDate: toDate};
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);

            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
              
                return resp.data; 
                
            }
            function fail(error) {
               
                var msg = "Error getting integration audit list: " + error;

                log.logError(msg, error, null, true);
            }
        }

        //Fetch the requested userId from the server
        function getintegrationAudit(integrationLogId) {
            if (integrationLogId == undefined) {
                integrationLogId = 0
            }
            //Get integration audit from the Server 
            return $http({
                url: '../api/integrationaudit/Get/' + integrationLogId,
                method: 'GET',
                cache: false,
                isArray: true
            }).then(success, fail)
            function success(resp) {

                return resp.data; //Assign data to employee
            }
            function fail(error) {
                alert(error);
                var msg = "Error getting integration audit list: " + error;

                log.logError(msg, error, null, true);
            }
        }

      


    }

})();
