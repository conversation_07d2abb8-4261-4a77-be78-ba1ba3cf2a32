(function () {
    'use strict';
    var controllerId = 'BulkEditProjectVariationOptionsModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'projectservice', bulkEditProjectVariationOptionsModalController]);
    function bulkEditProjectVariationOptionsModalController(common, $scope, $mdDialog, projectservice) {

        // - VARIABLES - //

        let vm = this;
        vm.isBusy = true;

        var thisProjectId = $scope.thisProjectId;
        var clientId = $scope.clientId;
        vm.categoryName = $scope.categoryName;
        vm.options = $scope.options || [];

        vm.data = { 
            bulkEditAction: "COPYTOPROJECT",
            selectedProjectId: null
        };

        // - INITIALISE - //

        // Get all projects for this client except the current one
        projectservice.getForClient(clientId, null, null, null, null, null, false).then(
            result => {
                if (result == null || !result.data)
                    return;
                vm.projectList = result.data;
                vm.projectList = vm.projectList.filter(x => x.projectId != thisProjectId);
                vm.isBusy = false;
            },
            error => {
                console.log(error);
                vm.isBusy = false;
            }
        );

        // - HANDLES - //

        vm.selectProject = function (project) {
            vm.projectList.forEach(x => x.selected = false);
            project.selected = true;
            vm.data.selectedProjectId = project.projectId;
        }

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();
