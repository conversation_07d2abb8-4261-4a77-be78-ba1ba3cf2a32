(function () {
    // The NathersclimatezoneUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'NathersclimatezoneUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'nathersclimatezoneservice', 'security', nathersclimatezoneUpdateController]);
function nathersclimatezoneUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  nathersclimatezoneservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit NatHERS Climate Zone';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.natHERSClimateZoneCode = null;
        vm.nathersclimatezone = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New NatHERS Climate Zone";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.natHERSClimateZoneCode = $scope.natHERSClimateZoneCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.natHERSClimateZoneCode = $stateParams.natHERSClimateZoneCode;
        }

        // Get data for object to display on page
        var natHERSClimateZoneCodePromise = null;
        if (vm.natHERSClimateZoneCode != null) {
            natHERSClimateZoneCodePromise = nathersclimatezoneservice.getNatHERSClimateZone(vm.natHERSClimateZoneCode)
            .then(function (data) {
                if (data != null) {
                    vm.nathersclimatezone = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("nathersclimatezone-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                nathersclimatezoneservice.createNatHERSClimateZone(vm.nathersclimatezone).then(function(data){
                    vm.nathersclimatezone = data;
                    vm.natHERSClimateZoneCode = vm.nathersclimatezone.natHERSClimateZoneCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                nathersclimatezoneservice.updateNatHERSClimateZone(vm.nathersclimatezone).then(function(data){
                    if (data != null) {
                        vm.nathersclimatezone = data;
                        vm.natHERSClimateZoneCode = vm.nathersclimatezone.natHERSClimateZoneCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            nathersclimatezoneservice.deleteNatHERSClimateZone(vm.natHERSClimateZoneCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            nathersclimatezoneservice.undoDeleteNatHERSClimateZone(vm.natHERSClimateZoneCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();