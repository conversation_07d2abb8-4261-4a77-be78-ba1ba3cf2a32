(function () {
    // The PriorityUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'PriorityUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'priorityservice', priorityUpdateController]);
function priorityUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  priorityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Priority';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.priorityCode = null;
        vm.priority = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Priority";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.priorityCode = $scope.priorityCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.priorityCode = $stateParams.priorityCode;
        }

        // Get data for object to display on page
        var priorityCodePromise = null;
        if (vm.priorityCode != null) {
            priorityCodePromise = priorityservice.getPriority(vm.priorityCode)
            .then(function (data) {
                if (data != null) {
                    vm.priority = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("priority-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                priorityservice.createPriority(vm.priority).then(function(data){
                    vm.priority = data;
                    vm.priorityCode = vm.priority.priorityCode;
                    vm.turnaroundHours = vm.priority.turnaroundHours;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                priorityservice.updatePriority(vm.priority).then(function(data){
                    if (data != null) {
                        vm.priority = data;
                        vm.priorityCode = vm.priority.priorityCode;
                        vm.turnaroundHours = vm.priority.turnaroundHours;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            priorityservice.deletePriority(vm.priorityCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            priorityservice.undoDeletePriority(vm.priorityCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();