(function () {
    // The BuildingDesignTemplateCtrl supports a list page.
    'use strict';
    var controllerId = 'BuildingDesignTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',
            'buildingdesigntemplateservice', 'assessmentcomplianceoptionservice', 'security', buildingDesignTemplateController]);
    function buildingDesignTemplateController($rootScope, $scope, $mdDialog, $stateParams, $state,
        buildingdesigntemplateservice, assessmentcomplianceoptionservice, securityservice) {

        var vm = this;
        vm.loaded = false;
        vm.isBusy = true;
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.newRecord = $scope.newRecord != undefined && $scope.newRecord == true;
        vm.template = null;
        vm.numberOfStoreys = null;

        vm.buildingDesignTemplateId = $stateParams.templateId;

        vm.editPermission = securityservice.immediateCheckRoles('admin__template__edit');
        vm.deletePermission = securityservice.immediateCheckRoles('admin__template__delete');

        function initialize() {

            vm.template = {};

            // Get data for object to display on page
            var buildingDesignTemplateIdPromise = null;
            if (vm.buildingDesignTemplateId != null) {
                buildingDesignTemplateIdPromise = buildingdesigntemplateservice.getTemplate(vm.buildingDesignTemplateId)
                .then(function (data) {

                    vm.template = data;
                    vm.isBusy = false;

                    console.log("Retrieved template was: ", vm.template);
                    vm.loaded = true;
                });
            }
            else {
                vm.isBusy = false;
            }
        }

        initialize();

        // Cancel - Route back to last page.
        vm.cancel = function () {
            $state.go("buildingdesigntemplate-listform");
        }

        vm.save = function () {

            vm.template.zones.forEach(l => {
                l.createdOn = undefined;
                l.modifiedOn = undefined;
            });

            // Update the "blankValues" dictionary within the designFeatures property.
            vm.template.designFeatures.blankValues = {};
            for (const [key, val] of Object.entries(vm.template.designFeatures)) {

                if (key == "blankValues")
                    continue; // We don't want to store itself

                vm.template.designFeatures.blankValues[key] = val == null;
            }

            vm.isBusy = true;

            console.log(vm.template);

            if(vm.newRecord == true){
                buildingdesigntemplateservice.createTemplate(vm.template).then(function(data){
                    vm.buildingDesignTemplateId = vm.template.buildingDesignTemplateId;
                    vm.isBusy = false;
                });

            } else {
                buildingdesigntemplateservice.updateTemplate(vm.template).then(function(data){
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            buildingdesigntemplateservice.deleteTemplate(vm.buildingDesignTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            buildingdesigntemplateservice.undoDeleteTemplate(vm.buildingDesignTemplateId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.title = function () {

            if (vm.newRecord)
                return "New Design Template"
            else (!vm.newRecord)
                return "Edit Design Template"
        }

        /** This is a callback which gets passed to, and fired from, the building floors area component */
        vm.onClassificationUpdate = function (projectClassification) {
            vm.template.projectClassification = projectClassification;
        }
    }
})();