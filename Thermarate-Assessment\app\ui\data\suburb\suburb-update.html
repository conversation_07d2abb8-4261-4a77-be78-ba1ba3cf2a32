<form name="suburbform" class="main-content-wrapper" novalidate data-ng-controller='SuburbUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div flex>
                    <md-card>
                        <md-card-header>
                            Suburb
                        </md-card-header>
                        <md-card-content>

<!-- ******** Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Name</label>
                                <input type="text" name="name" 
                                        ng-model="vm.suburb.name" md-autofocus 
                                        md-maxlength="100"
                                        required
                                    />
                                <div ng-messages="suburbform.name.$error">
                                    <div ng-message="required">Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

<!-- ******** State ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>State</label>
                                <md-select name="stateCode"  
                                        ng-model="vm.suburb.stateCode">
                                    <md-option ng-value>none</md-option>
                                    <md-option ng-value="item.stateCode" 
                                            ng-repeat="item in vm.stateList track by item.stateCode">
                                        {{item.name}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

<!-- ******** Postcode ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Postcode</label>
                                <input type="text" name="postcode" 
                                        ng-model="vm.suburb.postcode"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="suburbform.postcode.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Latitude ******** -->
							<md-input-container class="md-block" flex-gt-sm>
								<label>Latitude</label>
								<input type="text" name="latitude"
										ng-model="vm.suburb.latitude"
										pattern="{{vm.latRegex}}" />
								<div ng-messages="suburbform.latitude.$error">
									<div ng-message="required">Latitude is required.</div>
									<div ng-message="pattern">Incorrect Latitude coordinate</div>
								</div>
							</md-input-container>

							<!-- ******** Longitude ******** -->
							<md-input-container class="md-block" flex-gt-sm>
								<label>Longitude</label>
								<input type="text" name="longitude"
										ng-model="vm.suburb.longitude"
										pattern="{{vm.lonRegex}}" />
								<div ng-messages="suburbform.longitude.$error">
									<div ng-message="required">Longitude is required.</div>
									<div ng-message="pattern">Incorrect Longitude coordinate</div>
								</div>
							</md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.suburb"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="suburbform.$invalid" ng-show="vm.suburb.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.suburb.suburbCode!=null && vm.suburb.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.suburb.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
