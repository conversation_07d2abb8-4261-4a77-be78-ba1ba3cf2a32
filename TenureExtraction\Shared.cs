﻿using Microsoft.SqlServer.Types;
using ProjNet.CoordinateSystems;
using ProjNet.CoordinateSystems.Transformations;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using NetTopologySuite.Geometries;
using GeoAPI.Geometries;

namespace TenureExtraction
{
    static class Shared
    {
        // These are used to transform a coordinate from the 3857 to 4326 coordinate system.
        static ProjectedCoordinateSystem epsg3857ProjectedCoordinateSystem = ProjectedCoordinateSystem.WebMercator;
        static GeographicCoordinateSystem epsg4326GeographicCoordinateSystem = GeographicCoordinateSystem.WGS84;
        static CoordinateTransformationFactory coordinateTransformationFactory = new CoordinateTransformationFactory();
        static ICoordinateTransformation coordinateTransformation = coordinateTransformationFactory.CreateFromCoordinateSystems(epsg3857ProjectedCoordinateSystem, epsg4326GeographicCoordinateSystem);

        public static readonly string GDA_TARGET = "GDA94"; // GDA2020
        private const int SRID = 4326;

        public static bool TableExists(SqlConnection connection, string compareTable)
        {
            var switchParamCmd = new SqlCommand($"SELECT CASE WHEN EXISTS((SELECT * FROM information_schema.tables WHERE table_name = '{compareTable}')) THEN 1 ELSE 0 END;", connection);
            int? exists = switchParamCmd.ExecuteScalar() as int?;

            return (exists.HasValue && exists.Value == 1);
        }

        /// <summary>
        /// Converts e.g. "NAVARRE WAY" to "Navarre Way".
        /// </summary>
        public static string ToTitleCase(string s)
        {
            if (s == null)
                return null;

            return CultureInfo.InvariantCulture.TextInfo.ToTitleCase(s.ToLower()); // Must convert to lower or it assumes words are acronyms.
        }

        /// <summary>
        /// Extracts bundary geometry from the given shapefile for the INDEX specified. Actual geometry data
        /// is returned via the out string geometry argument.
        /// </summary>
        /// <returns>TRUE if successful. FALSE if any errors occurred during the process.</returns>
        public static bool GetGeometry(EGIS.ShapeFileLib.ShapeFile sh, int index, out string geometry)
        {
            try
            {
                var geom = sh.GetShapeDataD(index);
                var geoBuilder = new StringBuilder();

                if (geom == null)
                {
                    geometry = geoBuilder.ToString();
                    return false;
                }
                else
                {
                    // Format points into a JSON string.
                    geoBuilder.Append("[");

                    // We're only interested in the first boundary array.
                    foreach (var p in geom[0])
                    {
                        geoBuilder.Append($"[{p.X}, {p.Y}],");
                    }

                    geoBuilder.Length--; // This actually removes the last character
                    geoBuilder.Append("]");
                    geometry = geoBuilder.ToString();
                    return true;
                }
            }
            catch (IndexOutOfRangeException e)
            {
                throw e; // Debugging.
            }
        }


        /// <summary>
        /// Extracts bundary geometry from the given shapefile for the INDEX specified. Actual geometry data
        /// is returned via the out string geometry argument.
        /// </summary>
        /// <returns>TRUE if successful. FALSE if any errors occurred during the process.</returns>
        public static bool GetShapefileGeometryAsSqlBoundary(EGIS.ShapeFileLib.ShapeFile sh, int index, out SqlGeography geometry)
        {
            try
            {
                var geom = sh.GetShapeDataD(index);
                var geoBuilder = new StringBuilder();

                if (geom == null)
                {
                    geometry = null;
                    return false;
                }
                else
                {
                    // Format points into a JSON string.
                    geoBuilder.Append("POLYGON ((");

                    // We're only interested in the first boundary array.
                    foreach (var p in geom[0])
                    { 
                        //var epsg4326Coordinate = coordinateTransformation.MathTransform.Transform(p.X, p.Y);
                        geoBuilder.Append($"{p.X} {p.Y},");
                    }

                    geoBuilder.Length--; // This actually removes the last character
                    geoBuilder.Append("))");

                    SqlChars chars = new SqlChars(new SqlString(geoBuilder.ToString()));
                    geometry = SqlGeography.STGeomFromText(chars, 4326);
                    geometry = geometry.MakeValid(); // Close any open polygons - No need to RE-reorient.

                    return true;
                }
            }
            catch (IndexOutOfRangeException e)
            {
                throw e; // Debugging.
            }
        }
        
        public static SqlGeography GetSqliteGeometryAsSqlBoundary(
            SqliteDataReader reader, 
            bool reorient = false, 
            bool convertToMulti = false)
        {
            try
            {
                var allColumns =  Enumerable.Range(0, reader.FieldCount).Select(reader.GetName).ToList();
                // Luckily this geometry column name has been consistent across all datasets, but might be something
                // to look for if problems arise...
                string geometryColumn = allColumns.Contains( "geom")? "geom":"SHAPE";
                byte[] geom = reader[geometryColumn] as byte[]; // This will be in some whack format.
                
                // Convert to a 'Geometry' class.
                var converter = new NetTopologySuite.IO.GeoPackageGeoReader();
                var converted = converter.Read(geom);

                SqlGeography geometry;
                string convString = converted.ToString();

                if (convString.StartsWith("MULTIPOLYGON") && !convertToMulti)
                {
                    var chars = new SqlChars(new SqlString(convString));
                    geometry = SqlGeography.STMPolyFromText(chars, SRID);
                    geometry = geometry.MakeValid(); // Close any open polygons

                    if(reorient)
                    {
                        geometry = geometry.ReorientObject();
                    }
                }
                else if (converted.NumGeometries > 1 && convertToMulti)
                {
                    const string polyIdentifier = "POLYGON";
                    convString = convString.Substring(polyIdentifier.Length, convString.Length - polyIdentifier.Length);
                    convString = "MULTIPOLYGON (" + convString + ")";
                    
                    var chars = new SqlChars(new SqlString(convString));
                    geometry = SqlGeography.STMPolyFromText(chars, SRID);
                    geometry = geometry.MakeValid(); // Close any open polygons
                    
                    if(reorient)
                        geometry.ReorientObject();
                }
                else
                {
                    if(reorient)
                        convString = ReverseHandedness(convString, @"\(\(.*\)\)");
                    
                    SqlChars chars = new SqlChars(new SqlString(convString));
                    geometry = SqlGeography.STPolyFromText(chars, SRID);
                    geometry = geometry.MakeValid(); // Close any open polygons
                    
                }
                
                
                return geometry;
                
            }
            catch (IndexOutOfRangeException e)
            {
                throw e; // Debugging.
            }
        }

        
        /// <summary>
        /// Converts a single POLYGON (NOT Multipolygon!) SqlGeography type into an JSON array string in LAT/LNG format.
        /// </summary>
        /// <param name="geography"></param>
        /// <returns></returns>
        public static string ConvertSqlGeographyToJson(SqlGeography geography)
        {
            string s = geography.ToString();

            if (s.ToLower() == "null") 
                return "[[0,0], [0,0]]"; // Dummy data.

            s = s.Replace("MULTIPOLYGON", "");
            s = s.Replace("POLYGON", "");
            s = s.Trim();
            s = s.Trim('(');
            s = s.Trim(')');
            //s = s.Substring(10, s.Length - 12);

            var split = s.Split(',');

            var json = new StringBuilder(s.Length);
            json.Append("[");
            foreach(string ss in split)
            {
                var tr = ss.Trim();
                string[] pair = tr.Split(' ');
                json.Append($"[{pair[1]}, {pair[0]}],");
            }

            json.Length--;
            json.Append("]");

            return json.ToString();
        }
        
        /// <summary>
        /// Reverses the 'handedness' of the given input geometry string. NOTE: SqlGeography.Reorient does NOT work from
        /// what I can see.
        /// 
        /// When inserting geometry into SQL Server, the boundaries have "handednes", which takes into account
        /// the order of the points (Left-to-right or Right-to-left) within the polygon string. Depending on which
        /// direction they are in, this can cause boundaries to consider things INSIDE them or OUTSIDE of them as
        /// being "contained".
        /// 
        /// WKT = "Well Known Text", a siple format for specifying Geography/Geometry.
        /// </summary>
        /// <param name="originalWkt">Full original WKT input string.</param>
        /// <param name="regexMatch">The Regular Expression used to find the inner portion of the input string.</param>
        /// <returns>The input string re-oriented.</returns>
        public static string ReverseHandedness(string originalWkt, string regexMatch)
        {
            bool isMultipolygon = originalWkt.ToLower().StartsWith("multipolygon");

            // Find our inner geometry and split it up into individual polygon boundaries
            // (Note: For singular polygons there will only be 1 boundary).
            Regex rg = new Regex(regexMatch);
            var innerGeometry = rg.Match(originalWkt);
            var innerSplit = innerGeometry.Value.Split(new string[] { ")," }, StringSplitOptions.None);

            // Loop over each boundary and reverse it, storing the result.
            StringBuilder reversedGeoWkt = new StringBuilder();
            for (int i = 0; i < innerSplit.Length; i++)
            {
                // Find and then loop over polygon pairs in reverse and add to our string.
                var removeBrackets = innerSplit[i].Replace(')', ' ').Replace('(', ' ');
                var polygonPairs = removeBrackets.Split(',');

                StringBuilder reversedPolygon = new StringBuilder();
                for (int p = polygonPairs.Length - 1; p >= 0; p--)
                {
                    reversedPolygon.Append(polygonPairs[p] + ",");
                }

                reversedPolygon = reversedPolygon.Remove(reversedPolygon.Length - 1, 1);

                reversedPolygon = isMultipolygon 
                    ? reversedPolygon.Insert(0, "((").Append("))")
                    : reversedPolygon.Insert(0, "(").Append(")");

                reversedGeoWkt.Append(reversedPolygon + ", ");
            }

            // Remove the final ", " added from the above loop.
            reversedGeoWkt = reversedGeoWkt.Remove(reversedGeoWkt.Length - 2, 2);

            if (isMultipolygon)
                return "MULTIPOLYGON (" + reversedGeoWkt + ")";
            else
                return "POLYGON (" + reversedGeoWkt + ")";
        }
    }
}