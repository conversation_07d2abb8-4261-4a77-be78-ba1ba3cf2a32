// NOTE:
// MOST OF THE FUNCTIONALITY IN THIS FILE IS REDUNDANT DUE TO CLIENT CHANGE
// OF MIND. LEFT FOR WHEN POSSIBLY HE CHANGES HIS MIND BACK.
// REFER TO ACTUAL UI TO GET AN IDEA OF WHAT IS ACTUALLY NEEDED (NOT MUCH)
(function () {
    'use strict';
    var controllerId = 'ColourOverrideModalCtrl';
    angular.module('app')
        .controller(controllerId, 
            ['$rootScope', '$scope', '$mdDialog', 'common', 
            colourOverrideModalControl]);

    function colourOverrideModalControl($rootScope, $scope, $mdDialog, common) {

        let vm = this;
        vm.isBusy = false;

        vm.disabled = $scope.disabled;
        vm.item = $scope.item;
        vm.colourProperty = $scope.colourProperty;
        vm.solarAbsorptanceProperty = $scope.solarAbsorptanceProperty;
        vm.colourList = $scope.colourList;

        // Copy to local variables to prevent updating passed in object in real time.
        // Use the OriginalValue property if it exists, otherwise use the normal property.
        // vm.originalValue = vm.item[vm.originalValuePropertyName] ?? vm.item[vm.property];
        // vm.isOverridden = vm.item[vm.isOverriddenPropertyName];
        // vm.overrideValue = vm.item[vm.property];
        console.log("item: ", vm.item);

        vm[vm.colourProperty] = angular.copy(vm.item[vm.colourProperty]);

        // To avoid the scenario where the "original" colour IS actually NULL, but we don't want to revert to the "new"
        // colour (which has no suffix) we change behaviour based on whether manual override is true...
        if(vm.item[vm.colourProperty + "IsManuallyOverridden"]) {
            vm[vm.colourProperty + "OriginalValue"] = angular.copy(vm.item[vm.colourProperty + "OriginalValue"]);
            vm[vm.colourProperty + "IsManuallyOverridden"] = vm.item[vm.colourProperty + "IsManuallyOverridden"];
        } else {
            vm[vm.colourProperty + "OriginalValue"] = angular.copy(vm.item[vm.colourProperty]);
            vm[vm.colourProperty + "IsManuallyOverridden"] = vm.item[vm.colourProperty + "IsManuallyOverridden"];
        }

        vm[vm.solarAbsorptanceProperty] = angular.copy(vm.item[vm.solarAbsorptanceProperty]);
        vm[vm.solarAbsorptanceProperty + "OriginalValue"] = vm.item[vm.solarAbsorptanceProperty + "OriginalValue"] ?? vm.item[vm.solarAbsorptanceProperty];
        vm[vm.solarAbsorptanceProperty + "IsManuallyOverridden"] = vm.item[vm.solarAbsorptanceProperty + "IsManuallyOverridden"];

        if(vm.colourProperty.startsWith("interior")) {
            vm.prefix = "Interior";
        } else if(vm.colourProperty.startsWith("exterior")) {
            vm.prefix = "Exterior";
        } else {
            vm.prefix = "Frame";
        }

        /* Functions */
        vm.reset = function() {
            vm[vm.colourProperty] = vm[vm.colourProperty + "OriginalValue"];
            vm[vm.solarAbsorptanceProperty] = vm[vm.solarAbsorptanceProperty + "OriginalValue"];

            common.forceBlurInputWithId(`override-solar-absorptance-value-focus-node`);
        }

        vm.toggleOverride = function() {
            // Currently storing both values independantly, however client has asked them to be linked,
            // so this is enforced here.
            setTimeout(() => {

                vm[vm.solarAbsorptanceProperty + 'IsManuallyOverridden'] = vm[vm.colourProperty + 'IsManuallyOverridden'];
                if (!vm[vm.colourProperty + 'IsManuallyOverridden']) {
                    vm.reset()
                }
            }, 25);

        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        vm.save = function() {
            // Save assessment button handles the actual saving.
            // Just need to send back the new values for the caller to set them.
            $mdDialog.hide({

                [vm.colourProperty + "OverrideValue"]: vm[vm.colourProperty],
                [vm.colourProperty + "OriginalValue"]: vm[vm.colourProperty + "OriginalValue"],
                [vm.colourProperty + "IsManuallyOverridden"]: vm[vm.colourProperty + "IsManuallyOverridden"],

                [vm.solarAbsorptanceProperty + "OverrideValue"]: vm[vm.solarAbsorptanceProperty],
                [vm.solarAbsorptanceProperty + "OriginalValue"]: vm[vm.solarAbsorptanceProperty + "OriginalValue"],
                [vm.solarAbsorptanceProperty + "IsManuallyOverridden"]: vm[vm.solarAbsorptanceProperty + "IsManuallyOverridden"],

            });
        }

        // md-autocomplete throws a critical error if it is ever supplied with a
        // null value (as can happen before vm.colourList is loaded).
        vm.colourListSafe = () => vm.colourList ?? [];

        // Updates the colour of all elements within a group.
        vm.applyColour = function (colour) {

            vm[vm.colourProperty] = colour;
            vm[vm.solarAbsorptanceProperty] = colour?.solarAbsorptance;
        }
    }
})();