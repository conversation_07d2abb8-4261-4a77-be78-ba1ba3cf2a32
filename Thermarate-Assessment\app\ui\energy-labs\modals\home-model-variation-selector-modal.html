<form name="HomeModelVariationSelectorModal"
      data-ng-controller='HomeModelVariationSelectorModalCtrl as vm'>

    <div data-cc-widget-header
         data-title="{{vm.modalTitle}}"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div class="content-container">

        <div class="select-text">
            Select a Variation
        </div>

        <standard-model-variation-selector-list
            variation-of-id="vm.variationOfId"
            exclude-variation-id="vm.thisHomeModelId"
            variation-options-list="vm.variationOptionsList"
            variation-options-settings="vm.variationOptionsSettings"
            selected-variation-id="vm.selectedVariationId">
        </standard-model-variation-selector-list>

        <div data-cc-widget-button-bar
             layout="row"
             class="buttons-container">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()"
                       ng-disabled="vm.selectedVariationId == null">
                Confirm
            </md-button>

            <md-button class="md-raised" ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>

    .content-container {
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        row-gap: 20px;
    }

        .select-text {
            font-size: 14px;
            text-align: left;
        }

        .home-model-selection-container {
            margin-top: 15px;
            width: 100%;
            height: 200px;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 8px;
            background-color: #eeeeee;
            font-size: 16px;
            overflow: auto;
        }

            .home-modelitem {
                height: 20px;
                padding: 7px 11px;
                border-radius: 5px;
                cursor: pointer;
            }
            .home-modelitem:hover {
                background-color: #bcbcbc;
            }
            .home-modelitem.home-modelselected,
            .home-modelitem-selected:hover {
                background-color: #bfdba1;
            }

        .buttons-container {
            margin-top: 30px;
        }

</style>