(function () {
    // The Performancerequirementp262codeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'Performancerequirementp262codeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'performancerequirementp262service', performancerequirementp262codeUpdateController]);
function performancerequirementp262codeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  performancerequirementp262service) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Performance Requirement P262';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.performanceRequirementP262Code = null;
        vm.performancerequirementp262code = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Performance Requirement P262";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.performanceRequirementP262Code = $scope.performanceRequirementP262Code;
            }
            vm.hideActionBar = true;
        } else {
            vm.performanceRequirementP262Code = $stateParams.performanceRequirementP262Code;
        }

        // Get data for object to display on page
        var performanceRequirementP262CodePromise = null;
        if (vm.performanceRequirementP262Code != null) {
            performanceRequirementP262CodePromise = performancerequirementp262service.getPerformanceRequirementP262(vm.performanceRequirementP262Code)
            .then(function (data) {
                if (data != null) {
                    vm.performancerequirementp262code = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("performancerequirementp262code-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                performancerequirementp262service.createPerformanceRequirementP262(vm.performancerequirementp262code).then(function(data){
                    vm.performancerequirementp262code = data;
                    vm.performanceRequirementP262Code = vm.performancerequirementp262code.performanceRequirementP262Code;
                    vm.cancel();
                });
            }else{
                performancerequirementp262service.updatePerformanceRequirementP262(vm.performancerequirementp262code).then(function(data){
                    if (data != null) {
                        vm.performancerequirementp262code = data;
                        vm.performanceRequirementP262Code = vm.performancerequirementp262code.performanceRequirementP262Code;
                    }
                });
            }
        }

        vm.delete = function () {
            performancerequirementp262service.deletePerformanceRequirementP262(vm.performanceRequirementP262Code).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            performancerequirementp262service.undodeletePerformanceRequirementP262(vm.performanceRequirementP262Code).then(function () {
                vm.cancel();
            });
        }

    }
})();