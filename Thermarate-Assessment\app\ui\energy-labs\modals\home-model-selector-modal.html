<form name="CopyDesignToHomeModelModal"
      data-ng-controller='CopyDesignToHomeModelModalCtrl as vm'>

    <div data-cc-widget-header
         data-title="{{vm.modalTitle}}"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div class="content-container">

        <div class="select-text">
            Select a Home Design
        </div>

        <div class="home-model-selection-container">
            <div class="home-modelitem {{homeModel.selected ? 'home-modelselected' : ''}}" ng-repeat="homeModel in vm.homeModelList track by homeModel.standardHomeModelId" ng-click="vm.selectHomeModel(homeModel)">
                {{homeModel.title}}
            </div>
        </div>

        <div data-cc-widget-button-bar
             layout="row"
             class="buttons-container">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()"
                       ng-disabled="vm.selectedHomeModelId == null">
                Confirm
            </md-button>

            <md-button class="md-raised"
                        ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>

    .content-container {
        padding: 20px;
        width: 600px;
        box-sizing: border-box;
    }

        .select-text {
            font-size: 14px;
            text-align: left;
        }

        .home-model-selection-container {
            margin-top: 15px;
            width: 100%;
            height: 200px;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 8px;
            background-color: #eeeeee;
            font-size: 16px;
            overflow: auto;
        }

            .home-modelitem {
                height: 20px;
                padding: 7px 11px;
                border-radius: 5px;
                cursor: pointer;
            }
            .home-modelitem:hover {
                background-color: #bcbcbc;
            }
            .home-modelitem.home-modelselected,
            .home-modelitem-selected:hover {
                background-color: #bfdba1;
            }

        .buttons-container {
            margin-top: 30px;
        }

</style>