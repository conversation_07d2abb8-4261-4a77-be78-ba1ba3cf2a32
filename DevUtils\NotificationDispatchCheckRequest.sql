-- USE [thermarate];

SELECT [NotificationDispatchCheckRequestId]
      ,[AssessmentId]
      ,[InitialStatusCode]
      ,[NewStatusCode]
    --   ,[SendAfterUtc]
      ,FORMAT(DATEADD(HOUR, 8, [SendAfterUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__SendAfterFormatted]
      ,[Status]
      ,[Deleted]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
  FROM [dbo].[RSS_NotificationDispatchCheckRequest]
  WHERE 1=1
    --AND [NotificationDispatchCheckRequestId] = '195d7b8a-ad48-4e35-9bb0-ca1d8bd307ef'
    AND [AssessmentId] = 'cc8b3c00-e22b-419a-9029-abd597a4b19b'
	--AND [Status] != 'Checked'
    --AND [NewStatusCode] = 'ACompliance'
  ORDER BY [CreatedOn] DESC