(function () {
    // The FileUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'FileUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'fileservice', fileUpdateController]);
function fileUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  fileservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit File';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.fileId = null;
        vm.file = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New File";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.fileId = $scope.fileId;
            }
            vm.hideActionBar = true;
        } else {
            vm.fileId = $stateParams.fileId;
        }

        // Get data for object to display on page
        var fileIdPromise = null;
        if (vm.fileId != null) {
            fileIdPromise = fileservice.getFile(vm.fileId)
            .then(function (data) {
                if (data != null) {
                    vm.file = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("file-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                fileservice.createFile(vm.file).then(function(data){
                    vm.file = data;
                    vm.fileId = vm.file.fileId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                fileservice.updateFile(vm.file).then(function(data){
                    if (data != null) {
                        vm.file = data;
                        vm.fileId = vm.file.fileId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            fileservice.deleteFile(vm.fileId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            fileservice.undoDeleteFile(vm.fileId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();