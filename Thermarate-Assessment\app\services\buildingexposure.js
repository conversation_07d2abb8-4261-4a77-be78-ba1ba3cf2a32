// Name: buildingexposureservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'buildingexposureservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', buildingexposureservice]);

    function buildingexposureservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'buildingexposure/';
        
        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getBuildingExposure: getBuildingExposure,
            createBuildingExposure: createBuildingExposure,
            updateBuildingExposure: updateBuildingExposure,
            deleteBuildingExposure:deleteBuildingExposure,
            undoDeleteBuildingExposure:undoDeleteBuildingExposure,
            getAll,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting BuildingExposure list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        /** Just returns ALL items **/
        function getAll() {
            
            //Get error List from the Server 
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(
              r => r.data, 
              fail)
            
            function fail(error) {
                if (error.status === 0 || error.status === -1) 
                    return;
                
                let msg = "Error getting Building Exposure list: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getBuildingExposure(buildingExposureCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {buildingExposureCode: buildingExposureCode},
                method: 'GET',
                cache: true,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting BuildingExposure: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createBuildingExposure(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Exposure Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created BuildingExposure: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateBuildingExposure(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Exposure Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating BuildingExposure: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteBuildingExposure(buildingExposureCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { buildingExposureCode: buildingExposureCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting BuildingExposure: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteBuildingExposure(buildingExposureCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { buildingExposureCode: buildingExposureCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for BuildingExposure: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
