(function () {
    'use strict';
    let controllerId = 'BuildingSpacesBulkEditCtrl';
    angular
        .module('app')
        .controller(controllerId, ['common', '$scope', '$mdDialog', Controller])

    function Controller(common, $scope, $mdDialog) {

        let vm = this;

        vm.storeys = $scope.storeys;
        vm.zoneTypeList = $scope.zoneTypeList;
        vm.type = $scope.type;

        // This gets populated with any data we wish to apply over existing fields.
        // Any null/undefined values are NOT applied (i.e. nothing gets nullified).
        vm.data = {
            bulkEditAction: 'EDIT' // Valid are EDIT, COPY, CLEAR, DELETE
        }; 

        /** Simply returns any selected values to the caller, who must then apply them. */
        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.clearEditValues = function () {
            let action = vm.data.bulkEditAction;
            vm.data = {
                bulkEditAction: action
            }; 
        }
    }
    // END
})();