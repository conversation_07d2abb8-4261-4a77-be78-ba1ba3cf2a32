(function () {
    // The AssessmentdrawingListCtrl supports a list page.
    'use strict';
    var controllerId = 'AssessmentdrawingListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'assessmentdrawingservice', 'daterangehelper', assessmentdrawingListController]);
function assessmentdrawingListController($rootScope, $scope, $mdDialog, assessmentdrawingservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Assessment Drawings';
        vm.assessmentdrawingList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'assessmentId',
                    description: 'Assessment',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'drawingNumber',
                    description: 'Drawing Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'drawingDescription',
                    description: 'Drawing Description',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'attachment',
                    description: 'Attachment',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'sheetNumber',
                    description: 'Sheet Number',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'revision',
                    description: 'Revision',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'revisionDate',
                    description: 'Revision Date',
                    dataType: 'date',
                    operators: []
                },
                {
                    name: 'isIncludedInReport',
                    description: 'Included In Report',
                    dataType: 'boolean',
                    operators: []
                },
            ],
        };

        var persistRangeName = "assessmentdrawingList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('This Month', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createAssessmentdrawing = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/assessmentdrawing/assessmentdrawing-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "drawingDescription", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            assessmentdrawingservice.getListCancel();
            assessmentdrawingservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = assessmentdrawingservice.currentFilter();
                    vm.assessmentdrawingList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.assessmentdrawingList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createAssessmentdrawing,
                name: 'Add Assessment Drawing',
                desc: 'Add Assessment Drawing',
                roles: ['assessment_actions__editassessment'],
            });
        }

        setActionButtons();
    }
})();