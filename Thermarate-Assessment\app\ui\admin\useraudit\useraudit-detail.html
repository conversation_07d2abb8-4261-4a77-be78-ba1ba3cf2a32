<form name="useraudit-form" class="form-horizontal" novalidate data-ng-controller='UserauditDetailCtrl as vm' rc-submit="vm.saveUseraudit()">
    <section id="useraudit-view" class="mainbar" class="matter">
        <div class="container-fluid">
            <div class="row-fluid">
                <div class="widget wblue">
                    <div data-cc-widget-header
                         data-title="{{vm.title}}"
                         data-is-modal="vm.isModal"
                         data-cancel="vm.cancel()">
                    </div>
                    <div data-cc-widget-action-bar
                         data-quick-find-model=''
                         data-action-buttons='vm.actionButtons'
                         data-refresh-list=''
                         data-spinner-busy='vm.isBusy'
                         data-new-record=""
                         data-new-record-text=""
                         data-is-modal="vm.isModal"
                         data-hide="vm.hideActionBar">
                    </div>
                    <div data-cc-widget-content
                         data-is-modal="vm.isModal">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.userId)}">
                                    <label class="col-md-4 control-label" for="userId">User</label>
                                    <div class="col-md-8 ">
                                        <div proj-employee-select
                                             ng-model="vm.useraudit.auditRecId"
                                             data-hide-remove="false"
                                             data-show-add="false"></div>
                                    </div>
                                </div>
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.userName)}">
                                    <label class="col-md-4 control-label" for="userName">Username</label>
                                    <div class="col-md-8 ">
                                        <input type="text" name="userName" ng-model="vm.useraudit.userName" placeholder="username" md-autofocus class=" form-control" />
                                    </div>
                                </div>
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.eventType)}">
                                    <label class="col-md-4 control-label" for="eventType">Event Type*</label>
                                    <div class="col-md-8 ">
                                        <input type="text" name="eventType" ng-model="vm.useraudit.eventType" placeholder="event type"  class=" form-control"
                                               required />
                                        <span ng-show="useraudit-form.eventType.$dirty && useraudit-form.eventType.$error.required" class="help-block">Event Type is required.</span>
                                    </div>
                                </div>
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.eventDescription)}">
                                    <label class="col-md-4 control-label" for="eventDescription">Event Description*</label>
                                    <div class="col-md-8 ">
                                        <input type="text" name="eventDescription" ng-model="vm.useraudit.eventDescription" placeholder="event description"  class=" form-control"
                                               required />
                                        <span ng-show="useraudit-form.eventDescription.$dirty && useraudit-form.eventDescription.$error.required" class="help-block">Event Description is required.</span>
                                    </div>
                                </div>
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.ipAddress)}">
                                    <label class="col-md-4 control-label" for="ipAddress">Ip Address</label>
                                    <div class="col-md-8 ">
                                        <input type="text" name="ipAddress" ng-model="vm.useraudit.ipAddress" placeholder="ip address"  class=" form-control" />
                                    </div>
                                </div>
                                <div class="form-group" ng-class="{'has-error': rc.useraudit-form.needsAttention(useraudit-form.browserDesc)}">
                                    <label class="col-md-4 control-label" for="browserDesc">Browser Desc</label>
                                    <div class="col-md-8 ">
                                        <input type="text" name="browserDesc" ng-model="vm.useraudit.browserDesc" placeholder="browser desc"  class=" form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 col-md-offset-1">
                            </div>
                            <div class="col-md-12">
                                <div rd-display-created-modified ng-model="vm.useraudit"></div>
                            </div>
                        </div>
                    </div>
                    <div data-cc-widget-button-bar
                         data-is-modal="vm.isModal">
                        <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                        <button class="btn btn-default" type="button" ng-click="vm.cancel()">Cancel</button>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>
