(function () {

    'use strict';

    let controllerId = 'loadsOverrideModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'compliancemethodservice',
            'energyloadlimitservice',
            'assessmentsoftwareservice',
            'buildingconstructiontemplateservice',
            'buildingdesigntemplateservice',
            'constructionservice',
            'buildingservicestemplateservice',
            loadsOverrideModalController]);

    function loadsOverrideModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      compliancemethodservice,
                                      energyloadlimitservice,
                                      assessmentsoftwareservice,
                                      buildingconstructiontemplateservice,
                                      buildingdesigntemplateservice,
                                      constructionservice,
                                      buildingservicestemplateservice) {

        // The model for this form 
        const vm = this;

        vm.disabled = $scope.disabled;
        vm.assessment = $scope.assessment;
        vm.baseline = $scope.baseline;
        vm.option = $scope.option;
        vm.buildingType = $scope.buildingType;
        vm.title = $scope.title;

        vm.energyLoadUnits = $scope.energyLoadUnits;

        vm.data = {
            heating: $scope.heating,
            cooling: $scope.cooling,
            totalEnergyLoad: $scope.totalEnergyLoad,

            heatingOriginal: $scope.heatingOriginal,
            coolingOriginal: $scope.coolingOriginal,
            totalEnergyLoadOriginal: $scope.totalEnergyLoadOriginal,

            herOverride: $scope.herOverride,

            overrideEnergyLoads: $scope.overrideEnergyLoads,

            her: $scope.her || null,

        }

        function initialize() {

            // Determine what the "original" HER or equivalent would be using
            // the ORIGINAL values

            const minHouseEnergyRating = vm.option.requiredHouseEnergyRating;
            const floorType = vm.option.proposed.lowestLivingAreaFloorType;
            const nccClimateZone = vm.assessment.natHERSClimateZoneCode.substr(3, 2);

            const cantProcess = minHouseEnergyRating == null || floorType == null || nccClimateZone == null

            if(cantProcess === false) {
                energyloadlimitservice
                    .getCombinedEnergyData(
                        nccClimateZone,
                        minHouseEnergyRating,
                        floorType,
                        vm.data.totalEnergyLoadOriginal,
                        vm.option.certification.decimalStarbandRulesetCode,
                        vm.assessment.assessmentProjectDetail.stateCode
                    )
                    .then(data => {
                        vm.data.herOriginal = data.calculatedHER;
                    });
            } else {
                vm.data.herOriginal = null;
            }

            // Calculate override compliance if not already overridden to avoid a blank cell.
            if(vm.data.overrideEnergyLoads === false)
                calculateOverrideCompliance();

        }

        initialize();

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = async function () {
            $mdDialog.hide(vm.data);
        }

        vm.calculateTotal = function() {
            vm.data.totalEnergyLoad = vm.data.heating + vm.data.cooling;

            calculateOverrideCompliance();
        }

        function calculateOverrideCompliance() {

            const minHouseEnergyRating = vm.option.requiredHouseEnergyRating;
            const floorType = vm.option.proposed.lowestLivingAreaFloorType;
            const nccClimateZone = vm.assessment.natHERSClimateZoneCode.substr(3, 2);

            if (minHouseEnergyRating == null || floorType == null || nccClimateZone == null) {
                vm.data.herOverride = null;
                return;
            }

            if(vm.option.complianceMethod.complianceMethodCode === "CMPerfELL" && vm.buildingType === 'reference') {
                // do nothing...??
            } else {

                energyloadlimitservice
                    .getCombinedEnergyData(
                        nccClimateZone,
                        minHouseEnergyRating,
                        floorType,
                        vm.data.totalEnergyLoad,
                        vm.option.certification.decimalStarbandRulesetCode,
                        vm.assessment.assessmentProjectDetail.stateCode
                    )
                    .then(data => {
                        vm.data.herOverride = data.calculatedHER;
                    });
            }

        }

        vm.calculateOverrideCompliance = calculateOverrideCompliance;

        vm.herTitle = function() {

            if(vm.option.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" ||
                vm.option.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" || 
                vm.option.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER") {
                return "Calculated HER"
            }

            return "Equivalent HER"
        }

        vm.overrideChanged = function(override) {

            if(override !== false) {
                vm.data.heating = vm.data.heatingOriginal;
                vm.data.cooling = vm.data.coolingOriginal;
                vm.data.totalEnergyLoad = vm.data.totalEnergyLoadOriginal;
                vm.data.herOverride = vm.data.herOriginal;

                // calculateOverrideCompliance();
            }

        }
    }
})();