(function () {
    'use strict';
    var controllerId = 'NewBuildingDesignTemplateCtrl';
    angular.module('app')
        .controller(controllerId, ['$scope', '$mdDialog', '$state', 'buildingdesigntemplateservice', newBuildingDesignTemplateCtrl]);
    function newBuildingDesignTemplateCtrl($scope, $mdDialog, $state, buildingdesigntemplateservice) {
        var vm = this;
        vm.isBusy = false;
        //keep track of radio button selected.
        // Values are 'new' and 'copy'
        vm.currentTemplateType = "new";
        //currently selected template when teh radio is on the 'copy' value. Selected from 'vm.currentTemplatesList' 
        vm.selectedTemplate = null;

        vm.currentTemplatesList = [];
        buildingdesigntemplateservice.getAll().then(function (data) {
            vm.currentTemplatesList = data;
        });

        vm.submit = function () {
            vm.isBusy = true;
            switch (vm.currentTemplateType) {
                case "new":
                    buildingdesigntemplateservice.createEmpty().then(function (data) {
                        console.log("Just created new template, data is:");
                        console.log(data);
                        vm.isBusy = false;
                        $state.go("buildingdesigntemplate", { templateId: data.buildingDesignTemplateId });
                        $mdDialog.hide();
                    });
                    break;
                case "copy":
                    buildingdesigntemplateservice.copyTemplate(vm.selectedTemplate.buildingDesignTemplateId).then(function (templateId) {
                        vm.isBusy = false;
                        $state.go("buildingdesigntemplate", { templateId: templateId });
                        $mdDialog.hide();
                    });
                    break;
            }
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }
    }
})();