<form name="projectForm"
      class="main-content-wrapper"
      style="min-width:600px;"
      novalidate data-ng-controller='ProjectUpdateCtrl as vm'>

    <div class="widget" ng-cloak>

        <!-- Breadcrumbs -->
        <div
            ng-if="!vm.newRecord"
            ng-style="{'visibility': vm.project.clientName == null ? 'hidden' : 'visible'}"
            class="navigation-text"
            style="margin-left:0 !important"
        >
            <div ng-click="vm.navigateAttempt('client-listform', null)" class="clickable">Clients</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId})" class="clickable">{{vm.project.clientName}}</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId, tabIndex: 7})" class="clickable">EnergyLab</div>
            <div>></div>
            <b>{{vm.project.projectName}}</b>
        </div>

        <!-- Back -->
        <md-button style="padding:8px;position:absolute;top:78px;right:20px;" ui-sref="client-updateform({clientId: vm.project.clientId, tabIndex: 7})">Back</md-button>

        <!-- The Basics -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <span class="md-title">Project</span>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr;gap:15px;">

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  Name  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Project</label>
                        <input type="text"
                               name="projectName"
                               ng-model="vm.project.projectName"
                               md-autofocus
                               md-maxlength="100"
                               ng-required />
                        <div ng-messages="projectForm.projectName.$error">
                            <div ng-message="required">Name is required.</div>
                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                        </div>
                    </md-input-container>

                    <!-- Description -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Description</label>
                        <input type="text" name="description"
                               ng-model="vm.project.description"
                               md-maxlength="500"/>
                        <div ng-messages="projectForm.description.$error">
                            <div ng-message="md-maxlength">Too many characters entered, max length is 500.</div>
                        </div>
                    </md-input-container>

                    <!-- Type -->
                    <md-input-container class="md-block">
                        <label>Type</label>
                        <md-select ng-model="vm.project.projectTypeCode"
                                   ng-required="true">
                            <md-option ng-repeat="type in vm.projectTypeList"
                                       ng-value="type.projectTypeCode">
                                {{type.description}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Lots -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Lots</label>
                        <input type="text" name="lots" id="lots"
                               ng-model="vm.project.lots"
                               ng-value="{{vm.project.lots}}"
                               formatted-number
                               decimals="0"
                               ng-if="vm.project != null"/>
                        <div ng-messages="fileform.versionNo.$error">
                        </div>
                    </md-input-container>

                    <!--  Lot Area  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Lot Area (m<sup>2</sup>)</label>
                        <input ng-model="vm.project.lotArea"
                               formatted-number
                               decimals="6" />
                    </md-input-container>

                    <!-- Active -->
                    <div>
                        <label style="font-size: 9px;">Active</label>
                        <md-switch style="width: fit-content;"
                                   ng-model="vm.project.isActive"
                                   ng-change="vm.toggleModelsIsActive()">
                            <span ng-show="vm.project.isActive === false">The project will <b>not</b> appear in the EnergyLab.</span>
                            <span ng-show="vm.project.isActive === true">The project will appear in the EnergyLab.</span>
                        </md-switch>
                    </div>

                    <!--  Target Energy Rating  -->
                    <md-input-container ng-if="!vm.newRecord" class="md-block" flex="100" style="margin-top: 40px; margin-bottom: -5px;">
                        <label>Target Energy Rating</label>
                        <md-select ng-model="vm.project.energyLabsSettings.targetEnergyRating"
                                   ng-required="true">
                            <md-option ng-repeat="option in vm.targetEnergyRatingOptions"
                                       ng-value="option">
                                {{option.toFixed(1)}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Heating/Cooling Load Limits -->
                    <div ng-if="!vm.newRecord">
                        <label style="font-size: 9px;">Heating and Cooling Load Limits</label>
                        <md-switch ng-model="vm.project.energyLabsSettings.heatingCoolingLoadLimits"
                                   style="width: fit-content;">
                        </md-switch>
                    </div>

                    <!-- 3D Model -->
                    <div>
                        <label style="font-size: 9px;">3D Model</label>
                        <md-switch style="width: fit-content;"
                                   ng-model="vm.project.energyLabsSettings.view3dFloorPlans"
                                   ng-change="vm.toggleModels3dFloorplans()">
                        </md-switch>
                    </div>


                    <!-- Cost Estimate -->
                    <div ng-style="{'margin-top': vm.project.energyLabsSettings.view3dFloorPlans ? '0' : '18px'}">
                        <label style="font-size: 9px;">Cost Estimate</label>
                        <md-switch ng-model="vm.project.energyLabsSettings.costEstimateEnabledDefault"
                                   ng-change="vm.toggleModelsCostEstimate()"
                                   style="width: fit-content;">
                            <div style="min-width: 500px;">
                                <span ng-show="vm.project.energyLabsSettings.costEstimateEnabledDefault === false">Newly added standard models will have cost estimates turned OFF by default.</span>
                                <span ng-show="vm.project.energyLabsSettings.costEstimateEnabledDefault === true">Newly added standard models will have cost estimates turned ON by default.</span>
                            </div>
                        </md-switch>
                    </div>

                </fieldset>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!-- Logo -->
                    <div style="width: 320px;">
                        <generic-file-upload class="vertically-condensed"
                                             label="Project Logo"
                                             on-change="vm.newLogoUpload(data);"
                                             accept-array="false"
                                             category="'Images'"
                                             classification="'Project Logo'"
                                             file-object="vm.project.logoFile"
                                             prop-name="file"
                                             accept="'.png,.jpg,.jpeg,.svg'">
                        </generic-file-upload>
                    </div>

                    <img class="project-logo" src="{{vm.project.logoFileUrl != null ? vm.project.logoFileUrl : '../../../content/images/project-logo-placeholder.png'}}"/>

                </fieldset>

                <div ng-if="vm.newRecord==false" class="col-md-12" style="grid-column:1/3;">
                    <div rd-display-created-modified ng-model="vm.project"></div>
                </div>

            </md-card-content>
        </md-card>

        <!-- Project Location -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}" style="position:relative;">
            <md-card-header>
                <span class="md-title">Location</span>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr;gap: 40px;">

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!-- Auto Search Address -->
                    <search-address project="vm.project" address-changed="vm.addressChanged();"></search-address>

                    <!-- Suburb -->
                    <search-suburb initial-suburb-text="vm.project.suburbName"
                                   selected-suburb="vm.project.suburb"
                                   suburb-changed="vm.suburbChanged();"
                                   condensed="true"
                                   change-on-blur="true">
                    </search-suburb>

                    <!-- State -->
                    <md-input-container class="md-block" flex="100">
                        <label>State</label>
                        <div style="display:flex; gap:12px; margin-top:-22px;">
                            <md-select style="flex:9"
                                       ng-model="vm.project.stateCode"
                                       ng-change="vm.toggleLockWOHDisabled()">
                                <md-option ng-repeat="state in vm.stateList"
                                           ng-value="state.stateCode">
                                    {{state.stateCode}}
                                </md-option>
                            </md-select>
                            <div class="clear-button {{vm.project.stateCode == null ? 'clear-hidden' : null}}"
                                 ng-click="vm.project.stateCode = null;vm.toggleLockWOHDisabled();" />
                        </div>
                    </md-input-container>

                    <div style="display: flex; margin-bottom: 35px;">
                        <span style="margin: auto 0px;"><strong>Coordinates</strong></span>
                        <div layout="row"
                                class="my-auto"
                                style="margin: auto 0px;"
                                ng-if="vm.project.longitude && vm.project.latitude">

                            <a ng-href="https://maps.google.com/?q={{vm.project.latitude}},{{vm.project.longitude}}"
                                target="_blank">
                                <i class="fa fa-map" style="padding-left: 10px; color: #81BEF7;"></i>
                                <md-tooltip md-direction="top" style="margin-left: 10px;">
                                    Google Maps
                                </md-tooltip>
                            </a>

                            <a ng-href="https://web.metromap.com.au/map?lat={{vm.project.latitude}}&lng={{vm.project.longitude}}&zoom=19"
                                target="_blank">
                                <i class="fa fa-map" style="padding-left: 10px; color: #F78181;"></i>
                                <md-tooltip md-direction="top" style="margin-left: 10px;">
                                    MetroMaps
                                </md-tooltip>
                            </a>
                        </div>
                    </div>

                    <!--  Latitdude  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Latitude</label>
                        <input ng-model="vm.project.latitude"
							   pattern="{{vm.latRegex}}" />
                    </md-input-container>

                    <!--  longitdude  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Longitude</label>
                        <input ng-model="vm.project.longitude"
                               pattern="{{vm.lonRegex}}" />
                    </md-input-container>

                </fieldset>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!-- LGA -->
                    <md-input-container class="md-block" flex="100">
                        <label>Local Government Authority</label>
                        <input type="text"
                               ng-model="vm.project.lga" />
                    </md-input-container>

                    <!-- NatHERS Climate Zone -->
                    <md-input-container class="md-block" flex="100">
                        <label>NatHERS Climate Zone</label>
                        <div style="display:flex; gap:12px; margin-top:-22px;">
                            <md-select style="flex:9"
                                       name="natHERSClimateZoneCode"
                                       ng-model="vm.project.natHERSClimateZoneCode"
                                       ng-change="vm.toggleLockWOHDisabled()">
                                <md-option ng-value="item.natHERSClimateZoneCode"
                                           ng-repeat="item in vm.natHERSClimateZoneList track by item.natHERSClimateZoneCode">
                                    {{item.description}}
                                </md-option>
                            </md-select>
                            <div class="clear-button {{vm.project.natHERSClimateZoneCode == null ? 'clear-hidden' : null}}"
                                 ng-click="vm.project.natHERSClimateZoneCode = null;vm.toggleLockWOHDisabled();" />
                        </div>
                    </md-input-container>

                    <!-- NCC Climate Zone -->
                    <md-input-container class="md-block" flex="100">
                        <label>NCC Climate Zone</label>
                        <div style="display:flex; gap:12px; margin-top:-22px;">
                            <md-select style="flex:9"
                                       name="nccClimateZoneDescription"
                                       ng-model="vm.project.nccClimateZoneCode"
                                       ng-change="vm.toggleLockWOHDisabled()">
                                <md-option ng-value="item.nccClimateZoneCode"
                                           ng-repeat="item in vm.nccClimateZoneList track by item.nccClimateZoneCode">
                                    {{item.description}}
                                </md-option>
                            </md-select>
                            <div class="clear-button {{vm.project.nccClimateZoneCode == null ? 'clear-hidden' : null}}"
                                 ng-click="vm.project.nccClimateZoneCode = null;vm.toggleLockWOHDisabled();" />
                        </div>
                    </md-input-container>

                    <!-- Lock WOH Location -->
                    <div style="display: grid; justify-items: center;width: max-content;">
                        Lock Location
                        <md-switch ng-model="vm.project.lockWOHLocation"
                                   ng-disabled="vm.lockWOHDisabled"
                                   ng-change="vm.checkLockWOH()">
                        </md-switch>
                    </div>

                </fieldset>

            </md-card-content>
            <md-button class="md-raised md-primary"
                       style="position: absolute; bottom: 10px; right: 10px;"
                       ng-click="vm.clearLocationFields()">
              RESET
            </md-button>
        </md-card>

        <!-- Users -->
        <md-card ng-if="!vm.newRecord" flex="100" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <span class="md-headline">Users</span>
            </md-card-header>
            <md-card-content style="display:grid;grid-template-columns:1fr 1fr 1fr">
                <div>
                    <md-checkbox ng-repeat="user in vm.clientUsers"
                                 ng-model="user.canViewProject"
                                 ng-change="vm.updateUserProject(user.userId, vm.tempChecked)"
                                 class="checkbox-aligner"
                                 style="align-self: end;">
                        {{user.fullName}}
                    </md-checkbox>
                </div>
            </md-card-content>
        </md-card>

        <!-- Tools -->
        <md-card ng-if="!vm.newRecord" flex="100" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <span class="md-headline">Tools</span>
            </md-card-header>
            <md-card-content>

                <span>The following toggles control whether the corresponding tool appears on the client portal.</span>

                <md-switch style="justify-self: start;"
                           ng-model="vm.project.energyLabsSettings.toolIdentifyEnabled">
                    Identify
                </md-switch>

                <md-switch style="justify-self: start;"
                           ng-model="vm.project.energyLabsSettings.toolConfigureEnabled">
                    Configure
                </md-switch>

                <md-switch style="justify-self: start;"
                           ng-model="vm.project.energyLabsSettings.toolWholeOfHomeEnabled">
                    Whole-of-Home
                </md-switch>

                <md-switch style="justify-self: start;"
                           ng-model="vm.project.energyLabsSettings.toolOptimiseEnabled">
                    Optimise
                </md-switch>

            </md-card-content>
        </md-card>

        <!-- Project Variation Options -->
        <md-card flex="100" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header style="padding-bottom:10px;">
                <span class="md-headline">Project Variation Options</span>
                <span flex></span>
                <!-- Menu -->
                <md-menu style="margin-top:5px;">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy From Project -->
                        <md-menu-item><md-button ng-click="vm.copyVariationOptionsProject(true)">
                            Copy From Project
                        </md-button></md-menu-item>
                        <!-- Copy To Project (only show on existing projects) -->
                        <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copyVariationOptionsProject(false)">
                            Copy To Project
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button style="color: orangered;" ng-click="vm.clearAllProjectVariationOptions()">
                            Clear All
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content style="padding-top:0; padding-bottom:35px;">

                <div style="display: flex; flex-direction: column; gap: 50px;">

                    <!-- FOR EACH -->
                    <div ng-repeat="category in vm.variationCategories track by $index">

                        <!-- Label -->
                        <div style="font-size:18px; font-weight:bold;">{{vm.toSplitTitleCase(category)}}</div>

                        <!-- Category Toggle -->
                        <div>
                            <md-switch
                                style="width: fit-content;"
                                ng-model="vm.project.energyLabsSettings['varCategory' + category + 'Active']"
                                ng-change="vm.toggleModelsVarCategories()">
                                <span>Active</span>
                            </md-switch>
                        </div>

                        <!-- List -->
                        <div ng-repeat="option in vm.getProjectVariationOptions(category) track by $index"
                             style="display:flex; column-gap:10px;"
                        >
                            <!-- Checkbox -->
                            <md-input-container class="md-block" style="margin-top: 6px; width:max-content;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="option.checkboxSelected"
                                             ng-change="vm.updateBulkSelectStatus(vm.project.variationOptions, vm.bulkStatus[category]);">
                                </md-checkbox>
                            </md-input-container>
                            <!-- Input -->
                            <md-input-container class="md-block" style="margin:0; flex-grow:1">
                                <input id="option-input-{{$index}}" style="width:100%" type="text" name="option-{{option.optionName}}" ng-model="option.optionName" />
                            </md-input-container>
                            <!-- Menu -->
                            <md-menu style="margin-top:5px;">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Duplicate -->
                                    <md-menu-item><md-button ng-click="vm.copyProjectVariationOption(category, option)">
                                        Duplicate
                                    </md-button></md-menu-item>
                                    <!-- Move Up -->
                                    <md-menu-item ng-show="$index > 0"><md-button ng-click="vm.moveProjectVarOptionUp(category, option)">
                                        Move Up
                                    </md-button></md-menu-item>
                                    <!-- Move Down -->
                                    <md-menu-item ng-show="$index < vm.getProjectVariationOptions(category).length-1"><md-button ng-click="vm.moveProjectVarOptionDown(category, option)">
                                        Move Down
                                    </md-button></md-menu-item>
                                    <md-menu-divider></md-menu-divider>
                                    <!-- Delete -->
                                    <md-menu-item><md-button style="color: orangered;" ng-click="vm.deleteProjectVariationOption(option)">
                                        Delete
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>

                        <!-- Buttons -->
                        <div style="width:100%; display:flex; justify-content:space-between;">
                            <!-- Bulk Edit -->
                            <md-button style="margin-right:-10px"
                                       class="md-raised md-primary"
                                       ng-click="vm.launchProjectOptionsBulkEditModal(category)"
                                       ng-disabled="!(vm.bulkStatus[category].selectAllCheckboxState || vm.bulkStatus[category].isIndeterminate)">
                                BULK EDIT
                            </md-button>
                            <!-- Add Option -->
                            <md-button class="md-raised md-primary"
                                       ng-click="vm.addProjectVariationOption(category)">
                                Add Option
                            </md-button>
                        </div>

                    </div>

                </div>

            </md-card-content>
        </md-card>

        <!-- Variable Settings -->
        <md-card ng-if="!vm.newRecord" flex="100">
            <md-card-header>
                <span class="md-headline">Variable Settings</span>
            </md-card-header>
            <md-card-content>

                <div ng-repeat="group in vm.propertyGroups"
                     style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;">

                    <div ng-repeat="option in group track by $index">

                        <div ng-if="group === 'DUMMY'"></div>
                        <div ng-repeat="(variable, options) in vm.project.energyLabsSettings.allVariablesAndOptionsAcrossDesigns track by $index"
                             ng-if="variable === option"
                             ng-style="{ 'color': vm.project.energyLabsSettings.properties[variable] ? 'black' : 'red'}">

                            <div style="display: grid; grid-template-columns: 240px auto; align-items: center;">
                                <h4>{{vm.keyToName(variable)}}:</h4>
                                <md-switch style="justify-self: start;"
                                           ng-model="vm.project.energyLabsSettings.properties[variable]"
                                           ng-disabled="variable=='natHERSClimateZone' && vm.project.lockWOHLocation">
                                </md-switch>
                            </div>
                            <table class="table table-condensed"
                                   style="border-collapse: collapse;">
                                <tbody>
                                    <tr ng-if="options != null && options.length > 0 && options[0] != null"
                                        ng-repeat="option in vm.ordered(options)"
                                        lro-drag-src="options_{{variable}}"
                                        lro-drop-target="options_{{variable}}"
                                        lro-drop-success="vm.reorderOptions(variable, vm.ordered(options))"
                                        class="variable-option">
                                        <td style="min-width: 215px; max-width: 215px; width: 215px;">{{option}}</td>
                                        <td>
                                            <img class="clickable"
                                                 ng-click="vm.toggleCostEstimate(variable, option)"
                                                 style="width: 18px; margin-left: auto;"
                                                 src="{{vm.costEstimateEnabledFor(variable, option) == true
                                                    ? '/content/images/energy-labs/el-cost-enabled.svg'
                                                    : '/content/images/energy-labs/el-cost-disabled.svg' }}"
                                                 ng-disabled="vm.disabled" />
                                            <img class="clickable"
                                                 ng-click="vm.toggleVariableDefaultOption(variable, option)"
                                                 style="width: 18px; margin-left: 1rem;"
                                                 src="{{vm.isDefaultForVariable(variable, option) == true
                                                    ? '/content/images/energy-labs/el-default-enabled.svg'
                                                    : '/content/images/energy-labs/el-default-disabled.svg' }}"
                                                 ng-disabled="vm.disabled" />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Update All -->
                <md-button class="md-raised md-primary"
                            ng-click="vm.launchVariableSettingsUpdateAllConfrimation()">
                    Update All
                </md-button>

            </md-card-content>
        </md-card>

        <!-- Cost Estimate Settings -->
        <md-card ng-if="!vm.newRecord && vm.client != null" flex="100" flex-gt-lg="{{vm.isModal ? null : 50}}">

            <md-card-header>
                <span class="md-headline">Cost Estimate Settings</span>
            </md-card-header>

            <md-card-content>

                <!-- Cost Estimate Data Entry Table -->
                <table class="table table-hover table-condensed">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <div style="display: grid; justify-content: center;">
                                    <md-checkbox id="sm-cost-estimate-bulk-checkbox"
                                                 style="margin: auto; text-align: center; width: 0;"
                                                 ng-model="vm.costEstimateBulkStatus.selectAllCheckboxState"
                                                 md-indeterminate="vm.costEstimateBulkStatus.isIndeterminate"
                                                 ng-click="vm.selectAllCheckboxes(vm.flattenedCostData, vm.costEstimateBulkStatus.selectAllCheckboxState, vm.costEstimateBulkStatus)">
                                    </md-checkbox>
                                </div>
                            </th>
                            <th>Description</th>
                            <th>Category</th>
                            <th class="el-smuc-center-col">Item Code</th>
                            <th class="el-smuc-center-col">Quantity</th>
                            <th class="el-smuc-center-col">UOM</th>
                            <th class="el-smuc-center-col">Rate ($)</th>
                            <th class="el-smuc-center-col">Margin (%)</th>
                            <th class="el-smuc-center-col">Rounding</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="option in vm.flattenedCostData track by $index"
                            ng-style="{ 'color': vm.project.energyLabsSettings.properties[option.costEstimateData.category] ? 'black' : 'red'}">

                            <!-- Bulk Select -->
                            <td>
                                <div style="display: grid; justify-content: center;">
                                    <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                                 ng-model="option.checkboxSelected"
                                                 ng-change="vm.updateBulkSelectStatus(vm.flattenedCostData, vm.costEstimateBulkStatus);">
                                    </md-checkbox>
                                </div>
                            </td>
                            <!-- Description -->
                            <td>{{option.costEstimateData.description}}</td>
                            <!-- Category -->
                            <td>{{vm.keyToName(option.costEstimateData.category)}}</td>
                            <!-- Item Code -->
                            <td class="el-smuc-center-col">
                                <div>
                                    <md-select class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                               ng-model="option.costEstimateData.costItemId"
                                               ng-required="true">
                                        <md-option ng-repeat="item in vm.itemCodesForCategory(option.costEstimateData.category) track by item.clientCostItemId"
                                                   ng-value="item.clientCostItemId"
                                                   ng-click="vm.costItemChanged(option, item)">
                                            {{item.description}}
                                        </md-option>
                                        <md-option ng-value="null">Custom</md-option>
                                    </md-select>
                                    <md-tooltip class="solid-popup" md-direction="top" md-delay="0" md-autohide="true">
                                        <strong>{{option.costEstimateData.costItemId != null ? (vm.client.clientCostItems | filter:{clientCostItemId:option.costEstimateData.costItemId})[0].description : 'Custom'}}</strong>
                                    </md-tooltip>
                                </div>
                            </td>
                            <!-- Quantity -->
                            <td class="el-smuc-center-col">
                                <div class="var-ref-field">
                                    <!-- Manual: Input -->
                                    <input ng-if="option.costEstimateData.quantityVarRefJson == null"
                                           ng-model="option.costEstimateData.quantity"
                                           class="lightweight var-ref-input"
                                           style="display: inline-grid;"
                                           ng-required="true" />
                                    <!-- Manual: Button -->
                                    <div ng-if="option.costEstimateData.quantityVarRefJson == null" class="var-ref-select-button" ng-click="vm.openVariableSelectModal(option)">
                                        <img src="/content/images/select.png" />
                                    </div>
                                    <!-- Reference: Value -->
                                    <div ng-if="option.costEstimateData.quantityVarRefJson != null"
                                         class="lightweight var-ref-value"
                                         style="display: inline-grid;"
                                         ng-required="true">
                                        {{option.costEstimateData.quantity != null ? option.costEstimateData.quantity : "-"}}
                                    </div>
                                    <!-- Reference: Menu -->
                                    <md-menu ng-if="option.costEstimateData.quantityVarRefJson != null" class="var-ref-three-dot-menu">
                                        <img md-menu-origin
                                             class="clickable"
                                             ng-click="$mdOpenMenu()"
                                             src="/content/feather/more-horizontal.svg"/>
                                        <md-menu-content>
                                            <!-- Select -->
                                            <md-menu-item><md-button ng-click="vm.openVariableSelectModal(option)">
                                                Select
                                            </md-button></md-menu-item>
                                            <!-- Clear -->
                                            <md-menu-item><md-button ng-click="option.costEstimateData.quantityVarRefJson = null">
                                                <span style="color: orangered;">Clear</span>
                                            </md-button></md-menu-item>
                                        </md-menu-content>
                                    </md-menu>
                                </div>
                            </td>
                            <!-- UOM -->
                            <td class="el-smuc-center-col">
                                <md-select class="md-block vertically-condensed vertically-condensed-ex"
                                           ng-model="option.costEstimateData.unitOfMeasure"
                                           ng-disabled="option.costEstimateData.costItemId != null">
                                    <md-option ng-value="'mm'">mm</md-option>
                                    <md-option ng-value="'m'">m</md-option>
                                    <md-option ng-value="'m2'">m<sup>2</sup></md-option>
                                    <md-option ng-value="'m3'">m<sup>3</sup></md-option>
                                    <md-option ng-value="'Ea'">Ea</md-option>
                                    <md-option ng-value="'kg'">kg</md-option>
                                </md-select>
                            </td>
                            <!-- Rate ($) -->
                            <td class="el-smuc-center-col">
                                <input ng-model="option.costEstimateData.ratePerUnit"
                                       type="number"
                                       class="lightweight"
                                       style="display: inline-grid;"
                                       ng-required="true"
                                       ng-disabled="option.costEstimateData.costItemId != null" />
                            </td>
                            <!-- Margin (%) -->
                            <td class="el-smuc-center-col">
                                <input class="lightweight"
                                       style="display: inline-grid;"
                                       ng-model="option.costEstimateData.margin"
                                       ng-disabled="option.costEstimateData.costItemId != null" />
                            </td>
                            <!-- Rounding -->
                            <td class="el-smuc-center-col">
                                <md-select ng-required="true"
                                           class="md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                                           ng-model="option.costEstimateData.rounding"
                                           ng-disabled="option.costEstimateData.costItemId != null">
                                    <md-option ng-value="null">None</md-option>
                                    <md-option ng-value="1">1</md-option>
                                    <md-option ng-value="10">10</md-option>
                                    <md-option ng-value="100">100</md-option>
                                </md-select>
                            </td>
                            <!--  Notes  -->
                            <td>
                                <input ng-model="option.costEstimateData.notes"
                                       ng-disabled="option.costEstimateData.costItemId != null"
                                       class="lightweight"
                                       type="text" />
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="display: flex; justify-content: start; align-items: center;">

                    <!-- Initial bulk edit button, which launches options -->
                    <md-button class="md-raised md-primary"
                               ng-click="vm.launchCostEstimateBulkEdit()"
                               ng-disabled="vm.disabledEx() || !(vm.costEstimateBulkStatus.selectAllCheckboxState || vm.costEstimateBulkStatus.isIndeterminate)">
                        BULK EDIT
                    </md-button>

                    <!-- Update All -->
                    <md-button class="md-raised md-primary"
                               ng-click="vm.launchCostEstimatesUpdateAllConfrimation()"
                               ng-disabled="vm.disabledEx() || !(vm.costEstimateBulkStatus.selectAllCheckboxState || vm.costEstimateBulkStatus.isIndeterminate)">
                        Update All
                    </md-button>

                </div>

            </md-card-content>
        </md-card>

        <!-- Services Defaults -->
        <md-card ng-if="!vm.newRecord" flex="100" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <span class="md-headline">Services Defaults</span>
            </md-card-header>
            <md-card-content style="padding-top: 0 !important;">

                <div>

                    <!-- Select All -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.allServDefsSelected"
                                         ng-click="vm.selectAllServiceDefaultCheckboxes()">
                            </md-checkbox>
                        </md-input-container>
                    </div>

                    <!-- Heating System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceHeatingTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label class="">Heating System</label>
                            <md-select md-container-class="md-select-show-all"
                                       ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.spaceHeatingRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceHeatingRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>{{vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpDucted' || vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Heating System Energy Rating (GEMS 2019)'
                                        : 'Heating System Energy Rating'}}</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Cooling System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceCoolingTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Cooling System</label>
                            <md-select ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode"
                                       md-container-class="md-select-show-all"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.spaceCoolingRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceCoolingRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>{{vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpDucted' || vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Cooling System Energy Rating (GEMS 2019)'
                                        : 'Cooling System Energy Rating'}}</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Water Heater Type -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.waterHeaterTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Water Heater Type</label>
                            <md-select ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.waterHeating.serviceTypeCode"
                                       md-container-class="md-select-show-all">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                    </div>

                    <!-- Swimming Pool -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Swimming Pool</label>
                            <md-select ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.swimmingPool.exists"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Swimming Pool Volume (L) -->
                        <md-input-container ng-if="vm.swimmingPoolVolumeVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolVolumeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Swimming Pool Volume (L)</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.swimmingPool.volume"
                                   formatted-number
                                   decimals="0" />
                        </md-input-container>

                        <!-- Pool Pump Energy Rating -->
                        <md-input-container ng-if="vm.swimmingPoolRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Pool Pump Energy Rating</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.swimmingPool.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Spa -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa</label>
                            <md-select ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spa.exists"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Spa Volume (L) -->
                        <md-input-container ng-if="vm.spaVolumeVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaVolumeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa Volume (L)</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spa.volume"
                                   formatted-number
                                   decimals="0" />
                        </md-input-container>

                        <!-- Spa Pump Energy Rating -->
                        <md-input-container ng-if="vm.spaRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa Pump Energy Rating</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spa.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Photovoltaic (PV) System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.photovoltaicExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Photovoltaic (PV) System</label>
                            <md-select ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.photovoltaic.exists"
                                       ng-change="vm.updateServDefsVisibility();"
                                       ng-required="true">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Photovoltaic (PV) System Capacity (kW) -->
                        <md-input-container ng-if="vm.photovoltaicCapacityVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.photovoltaicCapacitySelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Photovoltaic (PV) System Capacity (kW)</label>
                            <input ng-model="vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.photovoltaic.capacity"
                                   formatted-number
                                   decimals="2" />
                        </md-input-container>

                    </div>
                </div>

                <!-- Update All -->
                <md-button class="md-raised md-primary"
                           ng-click="vm.launchServDefsUpdateAllConfrimation()"
                           ng-disabled="!vm.anyServDefsSelected">
                    Update All
                </md-button>

            </md-card-content>
        </md-card>

        <!-- Home Designs List -->
        <md-card ng-if="!vm.newRecord && vm.project != null" flex="100">
            <md-card-header>
                <span class="md-headline">Home Designs</span>
            </md-card-header>
            <md-card-content>
                <standard-model-list
                    client-id="vm.project.clientId"
                    project-id="vm.projectId"
                    project-energy-labs-settings="vm.project.energyLabsSettings"
                    parent-is-active="vm.project.isActive"
                    parent-3d-model="vm.project.energyLabsSettings.view3dFloorPlans"
                    parent-cost-estimate="vm.project.energyLabsSettings.costEstimateEnabledDefault"
                    on-initialise-complete="vm.resetChangeDetection()"
                    on-delete="vm.onModelDelete()"
                    passed-up-models="vm.project.modelsFromModelList"
                    row-click-callback="vm.modelRowClick(modelId)">
                </standard-model-list>
            </md-card-content>
        </md-card>

        <!-- Save / Delete / Restore / Cancel -->
        <div data-cc-widget-button-bar
             data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised md-primary"
                       ng-disabled="projectForm.$invalid || vm.editPermission == false || (!vm.newRecord && !vm.hasChanges())"
                       ng-show="vm.project.deleted!=true"
                       ng-click="vm.save()">
                Save
            </md-button>
            <md-button class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="vm.project.title!=null && vm.project.deleted!=true"
                       ng-confirm-click="vm.delete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to delete this record.">
                Delete
            </md-button>
            <md-button class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="vm.project.deleted==true"
                       ng-confirm-click="vm.undoDelete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to RESTORE this record.">
                Restore
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>
    </div>
</form>

<style>

    .standard-model-info-text {
        text-align: left;
        color: dimgrey;
        font-size: 11px;
    }

    .variable-option {
        transition: 300ms;
    }

    .variable-option:hover {
        background-color: #f1f1f1;
    }

    .project-logo {
        width: max-content;
        max-width: 370px;
        max-height: 400px;
        margin-top: 15px;
        margin-left: 10px;
    }

    .clear-button {
        margin-top: 27px;
        cursor: pointer;
    }

    .clear-button:before {
        content: "\f00d";
        font-family: FontAwesome;
        font-size: 12px;
        color: grey;
    }

    .clear-hidden {
        visibility: hidden;
    }

    .service-defaults-field {
        display: flex !important;
        min-height: 50px;
    }
    .service-defaults-field > label { margin-left: 35px; }
    .service-defaults-field > md-select { flex: 1; }

    .service-defaults-checkbox {
        margin-right: 5px !important;
    }

    /* ------------- */
    /* Cost Estimate */
    /* ------------- */

    /* Quantity */
    .var-ref-field {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    /* Manual: Input */
    .var-ref-input {
        margin-top: -3px;
        width: 40px;
        text-align: center;
    }
    /* Manual: Button */
    .var-ref-select-button {
        margin-left: 6px;
        margin-right: -6px;
        margin-top: -3px;
        margin-bottom: -4px;
        width: 32px;
        padding: 5px 4px 0 5px;
        box-sizing: border-box;
        display: block;
        border-radius: 5px;
        cursor: pointer;
    }

        .var-ref-select-button:hover {
            background-color: #adc43b !important;
        }

        .var-ref-select-button > img {
            width: 100%;
            height: auto;
        }
    /* Reference: Value */
    .var-ref-value {
        width: 40px;
    }
    /* Reference: Menu */
    .var-ref-three-dot-menu {
        margin-left: 6px;
        margin-right: -6px;
        width: 32px;
    }

        .var-ref-three-dot-menu > img {
            margin-top: 2px;
            margin-right: -5px;
            margin-left: 5px;
            transform: rotate(90deg);
        }

</style>