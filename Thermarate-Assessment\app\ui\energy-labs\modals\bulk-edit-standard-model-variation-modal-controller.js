(function () {
    'use strict';
    var controllerId = 'BulkEdiVariationModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'standardmodelservice', bulkEditStandardModelModalController]);
    function bulkEditStandardModelModalController(common, $scope, $mdDialog, standardmodelservice) {

        let vm = this;

        // Get parent settings from scope
        vm.parentIsActive = $scope.parentIsActive;
        vm.parent3dModel = $scope.parent3dModel;
        vm.parentCostEstimate = $scope.parentCostEstimate;
        vm.parentDesignInsights = $scope.parentDesignInsights;

        // Get default values based on selected variations
        vm.defaultIsActive = $scope.defaultIsActive;
        vm.defaultView3dFloorPlans = $scope.defaultView3dFloorPlans;
        vm.defaultCostEstimateEnabled = $scope.defaultCostEstimateEnabled;
        vm.defaultDesignInsightsEnabled = $scope.defaultDesignInsightsEnabled;

        vm.data = {
            bulkEditAction: "EDIT",
            // Use default values from selected variations, but respect parent settings
            isActive: vm.parentIsActive ? vm.defaultIsActive : false,
            view3dFloorPlans: vm.parent3dModel ? vm.defaultView3dFloorPlans : false,
            costEstimateEnabled: vm.parentCostEstimate ? vm.defaultCostEstimateEnabled : false,
            designInsightsEnabled: vm.parentDesignInsights ? vm.defaultDesignInsightsEnabled : false
        };

        // Track original values to detect changes
        vm.originalValues = {
            isActive: vm.data.isActive,
            view3dFloorPlans: vm.data.view3dFloorPlans,
            costEstimateEnabled: vm.data.costEstimateEnabled,
            designInsightsEnabled: vm.data.designInsightsEnabled
        };

        // Handle toggle changes
        vm.handleToggleChange = function(property) {
            // If toggling OFF, set flag to apply to all levels
            // Backend will handle turning off child values
            if (!vm.data[property]) {
                vm.data[property + 'ApplyToAll'] = true;
                return;
            }

            // If toggling ON, show confirmation modal
            let settingNames = {
                'isActive': 'Active Setting',
                'view3dFloorPlans': '3D Model Setting',
                'costEstimateEnabled': 'Cost Estimate Setting',
                'designInsightsEnabled': 'Design Insights Setting'
            };

            let modalScope = $scope.$new();
            modalScope.settingName = settingNames[property];
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: true // Don't hide the parent modal
            }).then(function (response) {
                // Store the user's choice to apply to all levels
                vm.data[property + 'ApplyToAll'] = response.applyToAllLevels;
            }, function() {
                // If modal is canceled, revert the toggle
                vm.data[property] = false;
                vm.data[property + 'ApplyToAll'] = false;
            });
        };

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();