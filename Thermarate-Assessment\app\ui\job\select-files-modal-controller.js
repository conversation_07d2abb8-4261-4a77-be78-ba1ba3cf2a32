// FAIRLY CERTAIN THIS IS NOT IN USE...
(function () {
    'use strict';
    var controllerId = 'SelectFilesModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', 'userservice', selectFilesModalController]);
    function selectFilesModalController($scope, $mdDialog, userservice) {
        var vm = this;
        vm.currentAssessorId = $scope.currentAssessorId;
        vm.response = {
            copyDrawings: true,
            assessorId: vm.currentAssessorId,
            purchaseOrder: "",
        };

        vm.assessorList = [];
        var assessorListPromise = userservice.getList()
            .then(function (data) {
                vm.assessorList = data.data;
            });

        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.submitSelection = function () {
            $mdDialog.hide(vm.response);
        };
    }
})();