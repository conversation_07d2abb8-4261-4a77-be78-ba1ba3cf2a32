﻿(function () {
    'use strict';

    var app = angular.module('app');
    /**
     * A generic confirmation for risky actions.
     * Usage: Add attributes:
     * * ng-confirm-message="Are you sure?"  - not used if delete-item is present
     * * ng-confirm-delete-item="description of item"
     * * ng-confirm-delete-reason-required   - will force the user to specify why they are deleting the record.
     * * ng-confirm-click="takeAction(yourlocalitemtodelete, deleteReasonId, deleteNote)" function
     * * ng-confirm-condition="mustBeEvaluatedToTrueForTheConfirmBoxBeShown" expression
    */
    app.directive('ngConfirmClick', ['bootstrap.dialog', function (modalDialog) {
        return {
            restrict: 'A',
            scope: {
                'action': '&ngConfirmClick',
                'delItem': '=ngConfirmDeleteItem'
            },
            link: function (scope, element, attrs) {
                element.bind('click', function (ev) {
                    if (ev != null) {
                        ev.preventDefault();
                        ev.stopPropagation();
                    }
                    var condition = scope.$eval(attrs.ngConfirmCondition);
                    if (condition) {
                        var message = attrs.ngConfirmMessage;
                        var result;
                        if (attrs.ngConfirmDeleteItem) {
                            if (attrs.ngConfirmDeleteReasonRequired != undefined) {
                                result = modalDialog.deleteDialogWithReason(scope.delItem);
                            }
                            else {
                                result = modalDialog.deleteDialog(scope.delItem);
                            }
                        }
                        else {
                            result = modalDialog.confirmationDialog("Confirm", message);
                        }
                        result.then(function (data) {
                            scope.action({ deleteReasonId: data.reasonId, deleteNote: data.note });
                        });
                    }
                    else {
                        scope.action();
                    }
                });
            }
        }
    }]);
})();