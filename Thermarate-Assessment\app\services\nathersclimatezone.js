// Name: nathersclimatezoneservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'nathersclimatezoneservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'compliancemethodservice', nathersclimatezoneservice]);

    function nathersclimatezoneservice(common, config, $http, compliancemethodservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'nathersclimatezone/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getNatHERSClimateZone: getNatHERSClimateZone,
            createNatHERSClimateZone: createNatHERSClimateZone,
            updateNatHERSClimateZone: updateNatHERSClimateZone,
            deleteNatHERSClimateZone:deleteNatHERSClimateZone,
            undoDeleteNatHERSClimateZone:undoDeleteNatHERSClimateZone,
            updateNatHERSData,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    resp.data.data = _.sortBy(resp.data.data, function (val) { return parseInt(val.description); })
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting NatHERSClimateZone list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getNatHERSClimateZone(natHERSClimateZoneCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {natHERSClimateZoneCode: natHERSClimateZoneCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting NatHERSClimateZone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createNatHERSClimateZone(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Nat H E R S Climate Zone Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created NatHERSClimateZone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateNatHERSClimateZone(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Nat H E R S Climate Zone Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating NatHERSClimateZone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteNatHERSClimateZone(natHERSClimateZoneCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { natHERSClimateZoneCode: natHERSClimateZoneCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting NatHERSClimateZone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteNatHERSClimateZone(natHERSClimateZoneCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { natHERSClimateZoneCode: natHERSClimateZoneCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for NatHERSClimateZone: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        /**
         * Re-checks all required compliance options to see if they are still/now compliant. Should run whenever we 
         * change the natHERS climate zone for the assessment.
         */
        function updateNatHERSData(assessment) {
            
            let options = assessment.allComplianceOptions
                .filter(x => x.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" || 
                             x.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" ||
                             x.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER" ||
                             x.complianceMethod.complianceMethodCode === "CMPerfELL");
            
            options?.forEach(option => {
                compliancemethodservice.calculateHERCompliance(assessment, option, true);
            });
        }
    }
})();
