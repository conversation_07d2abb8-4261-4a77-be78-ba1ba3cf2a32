(function () {
    'use strict';
    var controllerId = 'ProjectSelectorModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'projectservice', projectSelectorModalController]);
    function projectSelectorModalController(common, $scope, $mdDialog, projectservice) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;
        vm.isBusy = true;

        vm.title = $scope.title || "Select Project";
        vm.clientId = $scope.clientId;
        vm.excludeProjectId = $scope.excludeProjectId;

        vm.projectList = [];
        vm.selectedProjectId = null;

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        vm.selectProject = function(project) {
            vm.projectList.forEach(p => p.selected = false);
            project.selected = true;
            vm.selectedProjectId = project.projectId;
        }

        vm.confirm = function() {
            $mdDialog.hide(vm.selectedProjectId);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        // - -------- - //
        // - INIT/RUN - //
        // - -------- - //

        function init() {
            // Load projects for the client
            projectservice.getForClient(vm.clientId, false).then(result => {
                if (result && result.data) {
                    vm.projectList = result.data.filter(p => p.projectId !== vm.excludeProjectId);
                }
                vm.isBusy = false;
            });
        }

        init();
    }
})();
