<div data-ng-controller="EnergyLabsConfigureController as vm"
     style="margin-top: -20px; margin-bottom: 300px;"
     class="el-poppins">

  <!-- Header -->
  <div class="el-heading-banner"
       style="height: 315px; padding-top: 65px;">
    <h1 style="margin: 0;">Configure</h1>
    <h3>
      The thermal performance of a home is affected by many different design features.<br/>
      See how these features affect the thermal performance by customising a design.
    </h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>
  </div>
  <hr class="el-title-divider"/>

  <!-- Breadcrumbs -->
  <div class="navigation-text" ng-show="vm.project!=null">
    <div ng-click="vm.backToProjects()" class="clickable">Projects</div>
    <div>></div>
    <div ui-sref="energy-labs({projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}} [{{vm.project.clientName}}]</div>
    <div>></div>
    <b>Configure</b>
  </div>

  <!-- Body -->
  <div class="select-model-main-container">

    <!-- Filters -->
    <standard-model-filters ng-if="vm.project != null && vm.mode === 'select' && vm.initialised"
                            current-list="vm.modelList"
                            applied-filters="vm.filterData"
                            settings="vm.project.energyLabsSettings"
                            filter-count-data="vm.filterCountData"
                            on-filter-changed="vm.updateFilteredList"
                            on-sort-changed="vm.updateSort"
                            total-items-without-filters="vm.totalWithoutFilters"
                            current-total="vm.currentTotal">
    </standard-model-filters>

    <!-- Grid of all available designs -->
    <div ng-if="vm.mode === 'select'" class="el-option-grid">

      <!-- Card -->
      <div ng-repeat="parentModel in vm.modelList"
           class="el-card clickable"
           style="overflow: visible !important;"
           ng-click="vm.select(parentModel)">

        <!-- Header -->
        <div class="el-card-header" style="position:relative;">
            <!-- Title -->
            <div class="el-title">{{parentModel.title}}</div>
            <!-- Compare Icon -->
            <div style="position:absolute; right:12px; top:12px; user-select:none;"
                 ng-style="{
                    'cursor': vm.anyCompareSlotsLeft()||parentModel.selectedForCompare ? 'pointer' : 'default',
                    'opacity': !vm.anyCompareSlotsLeft()&&!parentModel.selectedForCompare ? 0.55 : 1
                 }"
                 ng-click="vm.selectModelForCompare(parentModel); $event.stopPropagation()">
                <div style="position:relative; display:flex;">
                    <!-- Layout Icon -->
                    <img src="/content/images/layout.svg" style="margin:auto; width:28px; height:auto;" />
                    <!-- Tick Icon -->
                    <img ng-if="parentModel.selectedForCompare" src="/content/images/tick-white.svg" style="position:absolute; top:-5px; right:7px; width:10px; height:auto; border-radius:50%; padding:5px 4px; background-color:#848484;" />
                </div>
                <div style="color:#848484;">Compare</div>
                <md-tooltip ng-if="!vm.anyCompareSlotsLeft()&&!parentModel.selectedForCompare" class="dropdown-tooltip" md-direction="top" md-delay="vm.tooltipDelay">
                    You can only compare up to 3 home designs at a time.<br/>Please remove one of your selections to continue. <div class="dropdown-tooltip-triangle" />
                </md-tooltip>
            </div>
        </div>

        <!-- Switcher -->
        <div class="el-card-body padded" style="margin: auto; box-sizing: border-box;">
            <home-plan-options-switcher
                style="width:100%;"
                the-parent-model="parentModel"
                show-label="true"
                view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)">
            </home-plan-options-switcher>
        </div>

      </div>

      <!-- Bottom Tray -->
      <div ng-if="vm.anySelectedForCompare()" class="tray-container">

        <!-- Expand/Collapse Button -->
        <div id="expand-button" class="expand-button" ng-click="vm.trayExpanded = !vm.trayExpanded;">
            <!-- Layout Icon -->
            <img class="layout-icon" src="/content/images/layout.svg" />
            <!-- Text -->
            <div class="expand-button-text">Compare (<span style="width:10px; display:inline-block">{{vm.numSelectedForCompare()}}</span>)</div>
            <!-- Arrow Icon -->
            <img class="arrow-icon" src="/content/images/arrow-right.png" ng-class="{'tray-expanded': vm.trayExpanded}" />
        </div>

        <!-- Tray -->
        <div id="tray" class="tray" ng-class="{'tray-expanded': vm.trayExpanded}">
            <div>
                <!-- Select Text -->
                <div class="select-text">Select up to three designs to compare.</div>
                <!-- Selected -->
                <div class="selected-tiles-container">
                    <div ng-repeat="selectedModel in vm.modelList | filter: { selectedForCompare: true }" class="selected-tile">
                        <div class="selected-tile-title">{{selectedModel.title}}</div>
                        <img class="selected-tile-remove-icon" src="/content/images/cross.png" ng-click="selectedModel.selectedForCompare = false;" />
                    </div>
                </div>
            </div>
            <div class="right-buttons-container">
                <!-- Clear Button -->
                <div class="clear-button"
                     ng-click="vm.clearCompareSelections()">
                    CLEAR
                </div>
                <!-- Compare Button -->
                <div class="compare-button" ng-click="vm.startCompare()">
                    COMPARE
                </div>
            </div>
        </div>

        <!-- White strip at bottom to hide shadow -->
        <div class="white-strip" />

      </div>

    </div>

    <!-- Selected Design + Comparison Cards -->
    <div ng-if="vm.mode === 'configure'">

      <div class="central-grid-container" style="margin-bottom: 85px;">
        <button ng-click="vm.reset()"
                class="el-calculate-button">
          START OVER <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

      <div ng-if="vm.viewingModels.length > 0"
           class="el-option-flex"
           ng-form="vm.energyLabsForm">

        <div ng-repeat="parentModel in vm.viewingModels | filter: { include: true } track by parentModel.uiRenderId"
             style="{{vm.useGridDisplay() === true ? '' : vm.viewingModels.length == 3 ? 'width: 550px;' : 'width: 800px;' }}">

            <!-- Data input card -->
            <div class="el-card {{'model-card-' + $index}}">
              <div class="el-card-header with-actions">
                <span><!-- Empty --></span>
                <div class="el-title">{{parentModel.title}}</div>

                <md-menu ng-show="!vm.disabled"
                         style="justify-self: right;">

                  <!-- Initial '...' button, which launches options -->
                  <img md-menu-origin
                       class="clickable el-menu-launch"
                       ng-click="$mdOpenMenu()"
                       src="/content/feather/more-horizontal.svg"
                       ng-disabled="vm.disabled"/>
                  <md-menu-content>

                    <!-- Compare side by side / Add to comparison -->
                    <md-menu-item ng-show="vm.viewingModels.length < 3">
                      <md-button ng-click="vm.addCompare(vm.viewingModels, $index);vm.clearComparison();">
                        <span>Add to comparison</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Remove comparison -->
                    <md-menu-item ng-show="vm.viewingModels.length > 1">
                      <md-button ng-click="vm.removeCompare(vm.viewingModels, $index);vm.clearComparison();">
                        <span>Remove from comparison</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Copy from/to -->
                    <md-menu-item ng-repeat="option in vm.viewingModels | filter: { include: true } track by $index"
                                  ng-show="option != parentModel"
                                  ng-mouseover="vm.copyFromHighlightEffect($index, true)"
                                  ng-mouseleave="vm.copyFromHighlightEffect($index, false)">
                      <md-button ng-click="vm.copyFromTo(option, parentModel)">
                        <span>Copy from {{option.title}}</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Restore Defaults -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuildingToDefaults(parentModel.selectedVariation);">
                        <span>Restore Defaults</span>
                      </md-button>
                    </md-menu-item>

                    <md-menu-divider></md-menu-divider>

                    <!-- Clear -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuilding(parentModel.selectedVariation);">
                        <span style="color: orangered;">Clear</span>
                      </md-button>
                    </md-menu-item>

                  </md-menu-content>
                </md-menu>
              </div>

              <div class="el-card-body padded">

                <!-- Options + Drawings -->
                <home-plan-options-switcher
                    style="width:100%;"
                    the-parent-model="parentModel"
                    get-full-variation="true"
                    show-label="true"
                    show-north-offset="vm.mode == 'configure' && parentModel.selectedVariation.optionData.northOffset != 'Multi-Orientation'"
                    view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                    on-model-changed="vm.variationChanged(parentModel)"
                    option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)"
                    viewing-models-count="vm.viewingModels.length">
                </home-plan-options-switcher>

                <!-- This is where the actual 'comparison' function exists... Maybe it shouldn't live here but eh -->
                <div ng-if="vm.mode == 'configure'" style="display:block; justify-self:stretch; margin-top:35px;">

                  <!-- Block Data -->
                  <standard-model-block the-model="parentModel.selectedVariation"
                                        variable-options="parentModel.selectedVariation.variableOptions"
                                        configuration="default"
                                        project="vm.project"
                                        required="true"
                                        disabled="true"
                                        on-data-changed="vm.clearComparison()"
                                        copy-across-enabled="vm.viewingModels.length > 1"
                                        copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                        copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)"
                                        orientate-enabled="parentModel.selectedVariation.orientateAvailable"
                                        climate-zone-is-invalid="parentModel.selectedVariation.modelBlockShowError">
                  </standard-model-block>

                  <!-- Specification Data -->
                  <standard-model-specifications the-model="parentModel.selectedVariation"
                                                 variable-options="parentModel.selectedVariation.variableOptions"
                                                 variable-metadata="parentModel.selectedVariation.variableMetadata"
                                                 project="vm.project"
                                                 required="true"
                                                 disabled="false"
                                                 on-data-changed="vm.clearComparison()"
                                                 copy-across-enabled="vm.viewingModels.length > 1"
                                                 copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                                 copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)">
                  </standard-model-specifications>

                  <!-- Assessment -->
                  <standard-model-assessment the-model="parentModel.selectedVariation"
                                             project="vm.project"
                                             variable-options="parentModel.selectedVariation.variableOptions"
                                             on-data-changed="vm.clearComparison()"
                                             copy-across-enabled="vm.viewingModels.length > 1"
                                             copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                             copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)">
                  </standard-model-assessment>

                </div>

              </div>
            </div>

            <!-- Performance Data / Results -->
            <div ng-show="vm.showResults === true" class="el-card v-margin performance-card" ng-style="{ 'height': parentModel.selectedVariation.optionData.northOffset == 'Multi-Orientation' ? (vm.viewingModels.length == 3 ? '640px' : '880px') : 'max-content' }">

                <standard-model-performance ng-if="parentModel.selectedVariation.optionData.northOffset != 'Multi-Orientation'" source="parentModel.selectedVariation.optionPerformance" assessment-method="parentModel.selectedVariation.optionData.assessmentMethod" heating-cooling-load-limits="vm.project.energyLabsSettings.heatingCoolingLoadLimits" target-energy-rating="vm.targetEnergyRating">
                </standard-model-performance>

                <!-- Orientate Chart -->
                <div ng-if="parentModel.selectedVariation.optionData.northOffset == 'Multi-Orientation'" class="el-section-title" style="padding: 2rem 2rem 0 2rem;">
                    <img class="el-section-icon"
                         src="content/images/energy-labs/el-performance-icon.svg"
                         alt="Icon of a home design which is stamped or has awards">
                    Performance
                    <img class="pop-out-icon"
                         src="content/images/pop-out.png"
                         alt="Icon of a home design which is stamped or has awards"
                         ng-click="vm.openOrientateModal(parentModel)">
                </div>
                <div ng-if="parentModel.selectedVariation.optionData.northOffset == 'Multi-Orientation'"
                     style="position:relative; height:100%;"
                     ng-style="{ 'transform': vm.viewingModels.length == 3 ? 'scale(0.6) translateY(-130px)' : 'scale(0.85) translateY(-80px)' }">
                    <!-- Donut Chart -->
                    <div id="central-north-offset-donut-chart-{{$index}}"
                         style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); border-start-start-radius: 60%;">
                    </div>
                    <!-- Floating 'North Offset' arrows -->
                    <div ng-if="parentModel.selectedVariation.orientateResults.results.length > 0"
                         style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%);"
                         class="el-north-offset-web">
                        <div ng-repeat="option in parentModel.selectedVariation.orientateResults.results track by $index"
                             style="position:absolute; transform:rotateZ(calc({{option.northOffset}}deg - 90deg)) translateX(calc(var(--web-distance)/1.6)) rotateZ(90deg)">
                            <img class="el-orientation-icon" src="/content/images/energy-labs/el-north-arrow-icon.svg" alt="North arrow icon">
                        </div>
                    </div>
                </div>

            </div>

            <!-- Design Insights for Option -->
            <div ng-show="vm.showResults === true && parentModel.selectedVariation.optionPerformance != null && vm.showDesignInsights(parentModel.selectedVariation)"
                 class="el-card padded v-margin el-design-card-identifier">
              <standard-model-design-insights source="parentModel.selectedVariation"></standard-model-design-insights>
            </div>

            <!-- Cost Estimate Data for Option -->
            <div ng-show="vm.showResults === true && vm.showCostEstimates(parentModel.selectedVariation, vm.project.energyLabsSettings.properties)"
                 class="el-card padded v-margin">
              <standard-model-cost-estimate source="parentModel.selectedVariation" project="vm.project"></standard-model-cost-estimate>
            </div>

          </div>

      </div>

      <div ng-show="vm.showResults === false"
           class="central-grid-container"
           style="margin-top: 5rem;">
        <button ng-click="vm.runCalc()"
                ng-disabled="vm.energyLabsForm.$invalid || vm.isBusy"
                class="el-calculate-button">
          CALCULATE <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

    </div>

  </div>

</div>

<style>

    /* Tooltip */
    .dropdown-tooltip {
        overflow: visible;
    }
        .dropdown-tooltip > div {
            position: relative;
            width: max-content;
            min-width: 100px;
            height: max-content;
            padding: 5px 14px 4px 14px;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
            box-shadow: 0 0 10px 0 rgba(0,0,0,.25);
            background-color: #fafafa;
            color: black;
            opacity: 1 !important;
            overflow: visible;
        }
        .dropdown-tooltip-triangle {
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translate(-50%, 100%);
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 5px solid #fafafa;
        }

    /* Tray Container */
    .tray-container {
        z-index: 999;
        position: relative;
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 1730px;
        max-width: 100vw;
        box-sizing: border-box;
        pointer-events: none;
    }

    /* Expand/Collapse Button */
    .expand-button {
        z-index: -1;
        position: relative;
        margin-left: 175px;
        width: 220px;
        padding: 10px 0;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        outline: none;
        background-color: #fafafa;
        box-shadow: 0 0 10px 0 rgba(0,0,0,.25);
        cursor: pointer;
        user-select: none;
        pointer-events: all;
    }
    .expand-button.bounce {
        animation: bounce 1.4s ease;
    }
    @keyframes bounce {
        30% { transform:translateY(0%);  }
        40% { transform:translateY(15%); }
        50% { transform:translateY(0%);  }
        60% { transform:translateY(13%); }
        70% { transform:translateY(0%);  }
        80% { transform:translateY(11%); }
        90% { transform:translateY(0);   }
    }

        /* Layout Icon */
        .layout-icon {
            width: 32px;
            height: auto;
        }

        /* Text */
        .expand-button-text {
            margin-top: 2px;
            white-space: nowrap;
            text-align: center;
            font-size: 17px
        }

        /* Arrow Icon */
        .arrow-icon {
            margin-left: 3px;
            width: 18px;
            height: auto;
            transform: rotate(-90deg);
        }
        .arrow-icon.tray-expanded {
            transform: rotate(90deg);
        }

    /* Tray */
    .tray {
        width: 100%;
        height: 0;
        border-top: 1px solid #e0e2e8;
        padding: 0 calc(30px + 4%);
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        background-color: #fafafa;
        pointer-events: all;
        transition: height 0.5s;
    }
    .tray.tray-expanded {
        height: 140px;
    }

        /* Select Text */
        .select-text {
            margin-bottom: 20px;
            font-size: 14px;
        }

        /* Selected Tiles Container */
        .selected-tiles-container {
            display: flex;
            align-items: center;
            column-gap: 20px;
        }

            /* Selected Tile */
            .selected-tile {
                width: max-content;
                padding: 4px 10px;
                display: flex;
                align-items: center;
                column-gap: 10px;
                border-radius: 3px;
                border: 1px solid #e0e2e8;
                font-size: 14px;
            }

                /* Title */
                .selected-tile-title {
                    margin-top: 2px;
                }

                /* Remove Icon */
                .selected-tile-remove-icon {
                    width: 12px;
                    height: auto;
                    cursor: pointer;
                }

        /* Right Buttons Container */
        .right-buttons-container {
            display: flex;
            align-items: center;
            column-gap: 20px;
        }

            /* Clear Button */
            .clear-button {
                padding: 12px 18px;
                border-radius: 10px;
                border: 1px solid #e0e2e8;
                background-color: #EAEAEA;
                font-weight: bold;
                font-size: 13px;
                cursor: pointer;
                user-select: none;
            }
            .clear-button:hover {
                background-color: #D9D9D9;
                color: white;
            }

            /* Compare Button */
            .compare-button {
                padding: 12px 18px;
                border-radius: 10px;
                background-color: rgb(139, 195, 74);
                font-size: 13px;
                font-weight: bold;
                color: white;
                cursor: pointer;
                user-select: none;
            }
            .compare-button:hover {
                background-color: rgb(166 222 100);
            }

    /* White strip at bottom to hide shadow */
    .white-strip {
        z-index: 999;
        position: absolute;
        height: 3px;
        width: 220px;
        top: 51px;
        left: 175px;
        background-color: #fafafa;
    }

    /* Orientate Chart */
    .highcharts-tracker > span {
        overflow: visible !important;
    }

    /* Pop-Out Button */
    .pop-out-icon {
        visibility: hidden;
        z-index: 10;
        position: absolute;
        top: 32px;
        right: 32px;
        width: 22px;
        height: auto;
        cursor: pointer;
    }
    .performance-card:hover .pop-out-icon {
        visibility: visible;
    }

</style>