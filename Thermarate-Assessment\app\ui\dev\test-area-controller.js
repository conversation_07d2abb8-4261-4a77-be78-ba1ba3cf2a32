// Controller specifically for testing functionality which does not require the entire assessment (or other data) to 
// load. This will be a 'living document' so don't expect it to stay the same if you make modifications to it.
(function () {
    // The AssessmentUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'DevTestCtrl';
    angular.module('app')
        .factory(controllerId, ['nemLogging', 'leaflet', 'leafletDrawEvents'])
        .controller(controllerId, ['$rootScope', 'common', '$scope', '$stateParams', '$q',
            'assessmentservice',
            'compliancemethodservice', 
            'nccclimatezonedataservice',
            'certificationservice', 
            'fileservice',
            'assessmentsoftwareservice',
            'compliancestatusservice',
            'coreLoop', devTestController]);
    function devTestController($rootScope, common,  $scope, $stateParams, $q,
        assessmentservice,
        compliancemethodservice, 
        nccclimatezonedataservice,
        certificationservice,
        fileservice,
        assessmentsoftwareservice, 
        compliancestatusservice,
        coreLoop) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.selectedAssessment = {};
        vm.isArchiveTable = false;

        vm.assessmentList = [];
        vm.title = "DEV TEST AREA";
        vm.templateName = null;
        vm.previousRoute = $rootScope.previousState;
        vm.hideActionBar = false;
        vm.assessmentId = $stateParams.assessmentId;
        vm.jobId = null;
        vm.assessment = { };
        vm.isLocked = false;

        vm.buildingToShow = 'proposed'; // Used to switch between showing proposed and reference buildings in building elements tab

        // Get data for object to display on page
        var assessmentIdPromise = null;
        var jobDeferred = $q.defer();

        //resved when either assessment is loaded or when sequential guid is loaded for new assessment
        var assessmentDeferred = $q.defer();

        if (vm.assessmentId != null) {

            assessmentIdPromise = assessmentservice.getAssessment(vm.assessmentId)
            .then(function (data) {
                if (data != null) {
                    vm.assessment = data;
                    vm.minHouseEnergyRating = vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating?.toFixed(1);

                    // This will not run for templates.
                    if (vm.assessment && vm.assessment.assessmentProjectDetail) {

                        // If Alistair ever wants per-compliance option # of floors,
                        // this will have to change.
                        if (vm.assessment.allComplianceOptions[0].proposed.storeys != null)
                            vm.floorsWithGlazing = vm.assessment.allComplianceOptions[0].proposed.storeys.length;
                        else
                            vm.floorsWithGlazing = 0;

                    }

                    vm.isBusy = false;
                    jobDeferred.resolve();
                }
                else {
                    vm.isBusy = false;
                }
                assessmentDeferred.resolve();
            });
        }

        // Get data for any dropdown lists
        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        vm.assessmentSoftwareList = [];
        var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
            .then(function(data){
                vm.assessmentSoftwareList = data;
                vm.allAssessmentSoftwareList = data;
            });

        vm.complianceStatusList = [];
        var complianceStatusPromise = compliancestatusservice.getList()
            .then(function(data){
                vm.complianceStatusList = data.data;
            });

        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
                vm.allComplianceMethodList = data.data;
                vm.complianceOptionsComplianceMethodList = angular.copy(data.data);
            });

        // When we change subtab within the building elements tab, we wish to keep
        // track of what the currently selected tab is, so that if needed we can
        // change whether we show the proposed or reference tab.
        vm.buildingSubtabChanged = function (option) {

            // NOTE: This will not work atm due to the refactor 
            // of the building elements into its own component. 
            if (option.complianceMethod.complianceMethodCode != 'CMPerfSolution' &&
                option.complianceMethod.complianceMethodCode != 'CMPerfSolutionDTS') {
                vm.buildingToShow = 'proposed';
            }
        }

        vm.jobFiles = [];
        jobDeferred.promise.then(function () {
            getJobFileList();
        });

        function getJobFileList() {
            if (vm.assessment.jobId != null && vm.assessment.jobId != undefined && vm.assessment.jobId != "") {
                var filter = [{ field: "JobId", operator: "eq", value: vm.assessment.jobId, valueType: "guid?" }]
                fileservice.getList(null, null, null, null, null, null, filter).then(function (data) {
                    if (data.data != null) {
                        vm.jobFiles = data.data;
                    }
                });
            }
        }

        setInterval(() => {

            if(vm.assessment?.allComplianceOptions?.[0] != null) {
                coreLoop.runCoreLoop(
                    vm.assessment.allComplianceOptions[0].proposed,
                    Number(vm.assessment.nccClimateZone.description),
                    Number(vm.assessment.natHERSClimateZone.description))
            }

        }, 1000);

        vm.save = async function () {

            var saveDeferred = $q.defer();
            vm.isBusy = true;

            // Remove all construction types that are not required, from all options
            vm.assessment.allComplianceOptions.forEach(option => {

                option.tempNotSavedInDb = false;

                // Nullify proposed sealing and ventilation details if they are 
                // not required.
                if (option.servicesRequired != undefined &&
                    option.servicesRequired == true) {

                    option.proposed.services = [];
                    option.reference.services = [];
                }
            });

            //assign current assessor to job as well
            if (vm.assessment.assessorUser) {
                vm.assessment.job.assessorUser = vm.assessment.assessorUser;
            }

            // Convert from DMS input back to decimal if require
            if (vm.useDMS) {
                if (vm.dms.lat && vm.dms.lng) {
                    vm.assessment.assessmentProjectDetail.latitude = convertToDecimal(vm.dms.lat);
                    vm.assessment.assessmentProjectDetail.longitude = convertToDecimal(vm.dms.lng);
                }
            }

            // MAP UPLOAD REMOVED FOR TEST CONTROLLER.

            const isInvalid = true;
            await assessmentservice.updateAssessment(vm.assessment, isInvalid);

            vm.isBusy = false;

            return saveDeferred.promise;
        }

    }
})();
