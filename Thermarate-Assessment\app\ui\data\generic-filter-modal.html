<md-dialog ng-controller="GenericFilterCtrl as vm" class="smallModal">
    <form>
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Active Filter</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="row" layout-padding layout-wrap>
            <span>Select values below to filter the displayed Jobs on the previous list.</span>

            <!-- 
                For each option that is passed in, create the necessary UI element to
                collect the data
                NOTE: Currently on 'select' input types supported - Add more! 
            -->
            <div ng-repeat="option in vm.options"
                 flex="100">
                <div ng-switch="option.elementType"
                     flex="100">

                    <div ng-switch-when="select">
                        <md-input-container class="md-block"
                                            flex="100">
                            <label>{{option.title}}</label>
                            <md-select ng-model="vm.filterData[option.field]"
                                       flex>
                                <md-option ng-repeat="item in option.values"
                                            ng-value="item.value">
                                    {{item.title}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </md-input-container>
                    </div>
                    <div ng-switch-default>
                        <h1>ERROR</h1>
                        <p>option.elementType is of an unknown value and thus cannot be processed!</p>
                    </div>
                </div>

            </div>

        </md-dialog-content>

        <md-dialog-actions layout="row">
            <md-button ng-click="vm.clearAll()"
                       ng-if="vm.clearAllShow()">
                Clear All
            </md-button>
            <span flex></span>
            <md-button ng-click="vm.cancel()">
                Cancel
            </md-button>
            <md-button ng-click="vm.submitSelection()">
                Save
            </md-button>
        </md-dialog-actions>
    </form>
</md-dialog>