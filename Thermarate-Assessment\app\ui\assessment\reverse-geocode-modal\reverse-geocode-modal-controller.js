(function () {

    'use strict';

    let controllerId = 'reverseGeocodeModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 
            reverseGeocodeModalController]);

    function reverseGeocodeModalController($rootScope, $scope, $mdDialog) {

        // The model for this form 
        const vm = this;
        vm.newAddress = $scope.newAddress;

        vm.ok = function(action) {
            $mdDialog.hide(action);
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            $mdDialog.cancel();
        }
    }
})();