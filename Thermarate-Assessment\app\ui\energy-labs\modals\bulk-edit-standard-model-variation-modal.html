<form name="BulkEdiVariationModal"
      data-ng-controller='BulkEdiVariationModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row" ng-model="vm.data.bulkEditAction">
            <md-radio-button ng-value="'EDIT'">
                Edit
            </md-radio-button>
            <md-radio-button ng-value="'COPY'">
                Duplicate
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <fieldset id="edit-inputs"
                  ng-if="vm.data.bulkEditAction == 'EDIT'">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Active -->
                    <tr>
                        <td>
                            Active
                        </td>
                        <td>
                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="vm.data.isActive" ng-disabled="!vm.parentIsActive" ng-change="vm.handleToggleChange('isActive')">
                                </md-switch>
                            </div>
                        </td>
                    </tr>

                    <!-- 3D Model -->
                    <tr>
                        <td>
                            3D Model
                        </td>
                        <td>
                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="vm.data.view3dFloorPlans" ng-disabled="!vm.parent3dModel" ng-change="vm.handleToggleChange('view3dFloorPlans')">
                                </md-switch>
                            </div>
                        </td>
                    </tr>

                    <!-- Cost Estimate -->
                    <tr >
                        <td>
                            Cost Estimate
                        </td>
                        <td>
                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="vm.data.costEstimateEnabled" ng-disabled="!vm.parentCostEstimate" ng-change="vm.handleToggleChange('costEstimateEnabled')">
                                </md-switch>
                            </div>
                        </td>
                    </tr>

                    <!-- Design Insights -->
                    <tr>
                        <td>
                            Design Insights
                        </td>
                        <td>
                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="vm.data.designInsightsEnabled" ng-disabled="!vm.parentDesignInsights" ng-change="vm.handleToggleChange('designInsightsEnabled')">
                                </md-switch>
                            </div>
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Standard Model Variations will be duplicated.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Standard Model Variations will be deleted. </span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>

    .content-container {
        padding: 20px;
        width: 600px;
        box-sizing: border-box;
    }

        .select-text {
            font-size: 14px;
            text-align: left;
        }

    .buttons-container {
        margin-top: 30px;
    }

</style>