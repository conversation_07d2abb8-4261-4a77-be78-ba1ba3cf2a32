<form name="stateform" class="main-content-wrapper" novalidate data-ng-controller='StateUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div flex>
                    <md-card>
                        <md-card-header>
                            State
                        </md-card-header>
                        <md-card-content>

                          <fieldset redi-enable-roles="settings__settings__edit">

                            <!-- ******** State Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>State Code</label>
                              <input type="text" name="stateCode"
                                     ng-disabled="!vm.newRecord"
                                     ng-model="vm.state.stateCode" md-autofocus
                                     md-maxlength="20"
                                     required
                              />
                              <div ng-messages="stateform.stateCode.$error">
                                <div ng-message="required">State Code is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                              </div>
                            </md-input-container>

                            <!-- ******** Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Name</label>
                              <input type="text" name="name"
                                     ng-model="vm.state.name"
                                     md-maxlength="50"
                                     required
                              />
                              <div ng-messages="stateform.name.$error">
                                <div ng-message="required">Name is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 50.</div>
                              </div>
                            </md-input-container>

                            <!-- ******** Country ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Country</label>
                              <md-select name="countryCode"
                                         ng-model="vm.state.countryCode">
                                <md-option ng-value>none</md-option>
                                <md-option ng-value="item.countryCode"
                                           ng-repeat="item in vm.countryList track by item.countryCode">
                                  {{item.description}}
                                </md-option>
                              </md-select>
                            </md-input-container>

                            <!-- Maximum Heating and Cooling -->
                            <md-input-container class="md-block" flex="100"
                                                ng-class="{'vertically-condensed': vm.formStyle == 'CONDENSED'}">
                              <label>Maximum Heating and Cooling</label>
                              <md-select required
                                         name="maximumHeatingAndCoolingRulesetCode"
                                         ng-model="vm.state.heatingAndCoolingRulesetCode">
                                <md-option ng-value="'Inherit'">
                                  As per Certification
                                </md-option>
                                <md-option ng-value="'Disabled'">
                                  Disabled
                                </md-option>
                              </md-select>
                            </md-input-container>

                          </fieldset>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.state"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           ng-disabled="stateform.$invalid || vm.editPermission == false" 
                           ng-show="vm.state.deleted!=true" 
                           ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" 
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.state.stateCode!=null && vm.state.deleted!=true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                  Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.state.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                  Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">
                  Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
