(function () {
    'use strict';
    var serviceId = 'selectvariablelinkservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'zoneservice', 'zonesummaryservice', selectvariablelinkservice]);

    function selectvariablelinkservice(common, zoneservice, zonesummaryservice) {

        /*
            // - Example Var<PERSON><PERSON><PERSON><PERSON> - //
            {
                "variablePathObj": {
                    "sectionValue": "envelopeSummary",
                    "child": {
                        "value": "floorArea",
                        "child": { ... },
                        // OR //
                        "value": "3",
                        "child": { ... },
                        // OR //
                        "type": "pop",
                        "child": { ... },
                        // OR //
                        "type": "whereKeyValue",
                        "key": "description",
                        "value": "Bedroom",
                        "child": { ... },
                    }
                },
                "filters": {...},
                "savedValue": 55.132000000000005
            }
        */

        // Set value on object given variable link
        function setValueFromLink(newData, building, data, referenceJson, parentObject, varName, toDp) {
            let selectableData = angular.copy(data);
            for (const sectionKey in selectableData.zoneSummary) {
                selectableData.zoneSummary[sectionKey] = selectableData.zoneSummary[sectionKey].rows;
                selectableData.zoneSummary[sectionKey].forEach((_, index) => {
                    selectableData.zoneSummary[sectionKey][index] = selectableData.zoneSummary[sectionKey][index].zones;
                });
            }
            selectableData.interiorSummary = selectableData.interiorSummary?.storeys;
            selectableData.interiorSummary?.forEach((_, index) => {
                selectableData.interiorSummary[index] = selectableData.interiorSummary[index].rows;
            });
            let varRefObj = JSON.parse(referenceJson);
            // IF "variablePathString" exists, means is old data from before update which doesn't work, so break link
            if (varRefObj.variablePathString != null) {
                throw "Old data for variable link, so link has been broken!";
            }
            let section = varRefObj.variablePathObj.sectionValue;
            // SPECIAL: EnvelopeSummary, we do not want to process the filters every time to retrieve the value, we just saved the value on select since this data does not change until new Scratch is uploaded anyway
            if (section == "envelopeSummary") {
                if (data.envelopeSummary == null) {
                    parentObject[varName] = null;
                } else {
                    // IF there is new data, or savedValue might be null if the link was inherited from Project using Update All feature
                    if (newData || varRefObj.savedValue == null) {
                        let envelopeSummaryCopy = angular.copy(data.envelopeSummary);
                        envelopeSummaryCopy.exteriorWallAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(building, 'surface', 'ExteriorWall', {sectors: zonesummaryservice.defaultSectors}, [varRefObj.filters]);
                        envelopeSummaryCopy.exteriorGlazingAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(building, 'opening', 'ExteriorGlazing', {sectors: zonesummaryservice.defaultSectors}, [varRefObj.filters]);
                        let theValue = findValueInPath(section, envelopeSummaryCopy, varRefObj.variablePathObj.child);
                        if (theValue == null || theValue == 0) {
                            throw "The new Envelope Summary value could not be found or was 0!";
                        }
                        // IF sector is empty
                        if (!zonesummaryservice.isSectorNotEmpty(varRefObj.variablePathObj.child.child.value, envelopeSummaryCopy)) {
                            throw "The sector is not available for the Envelope Summary variable link with the saved filters!";
                        }
                        varRefObj.savedValue = theValue;
                        parentObject[varName] = Number(Number(theValue).toFixed(toDp??2));
                        // Return new savedValue to be saved
                        if (!newData) {
                            return JSON.stringify(varRefObj);
                        }
                    // ELSE just set from savedValue
                    } else {
                        parentObject[varName] = Number(Number(varRefObj.savedValue ?? 0).toFixed(toDp??2));
                    }
                }
            // Normal
            } else {
                let theObject = section == "drawingAreas"       ? selectableData.drawingAreasData.drawingAreas
                              : section == "drawingAreasTotals" ? selectableData.drawingAreasData.drawingAreasTotals
                              : selectableData[section];
                let theValue = findValueInPath(section, theObject, varRefObj.variablePathObj.child);
                if (theValue == null) {
                    parentObject[varName] = null;
                } else {
                    parentObject[varName] = Number(Number(theValue).toFixed(toDp??2));
                }
            }
        }

        function findValueInPath(section, theObject, thePathObject) {
            let theValue = theObject;
            let nextChild = thePathObject;
            while (nextChild) {
                if (theValue == null) {
                    throw "The linked variable no longer exists!";
                }
                // SPECIAL: ZoneSummary, if floor selected but floor is now last item in list, means floor doesn't exist since last is Whole Building, so break link
                if (section == "zoneSummary" && thePathObject.child.type != "pop" && thePathObject.child.value == theObject[thePathObject.value].length-1) {
                    throw "The Zone Summary storey no longer exists!";
                }
                switch (nextChild.type) {
                    case "pop":
                        theValue = theValue.pop(); break;
                    case "whereKeyValue":
                        theValue = theValue.find(x => x[nextChild.key].toLowerCase() == nextChild.value.toLowerCase()); break;
                    default:
                        theValue = theValue[nextChild.value];
                }
                nextChild = nextChild.child;
            }
            return theValue;
        }

        // PLACEHOLDERS: Drawing Areas
        const placeholder_drawingAreas = {
            "drawingAreas": [
                { "storeyName": "Ground Floor", willDelete: false },
                { "storeyName": "First Floor",  willDelete: false },
                { "storeyName": "Second Floor", willDelete: false },
            ],
            "drawingAreasTotals": {
                "house": "",
                "garage": "",
                "houseGarage": "",
                "alfresco": "",
                "porch": "",
                "housePerimeter": "",
                "houseFacade": "",
                "roofHorizontal": "",
                "roofPitched": ""
            }
        };

        // PLACEHOLDERS: Zone Summary
        const placeholder_zoneSummary = {
            "isPlaceholder": true,
            "interiorZones": {
                "groupName": "Zone Name",
                "descriptionHeading": "Zone Name",
                "rows": []
            },
            "zoneActivity": {
                "groupName": "Zone Activity",
                "descriptionHeading": "Zone Activity",
                "rows": [{
                    "name": "Ground Floor",
                    "zones": [
                        { "description": "Ground Surface" },
                        { "description": "Kitchen / Living" },
                        { "description": "Living" },
                        { "description": "Night Time" },
                        { "description": "Bedroom" },
                        { "description": "Day Time" },
                        { "description": "Garage" },
                        { "description": "Garage Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" },
                    ]
                },{
                    "name": "First Floor",
                    "zones": [
                        { "description": "Kitchen / Living" },
                        { "description": "Living" },
                        { "description": "Night Time" },
                        { "description": "Bedroom" },
                        { "description": "Day Time" },
                        { "description": "Garage" },
                        { "description": "Garage Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" },
                    ]
                },{
                    "name": "Second Floor",
                    "zones": [
                        { "description": "Kitchen / Living" },
                        { "description": "Living" },
                        { "description": "Night Time" },
                        { "description": "Bedroom" },
                        { "description": "Day Time" },
                        { "description": "Garage" },
                        { "description": "Garage Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" },
                    ]
                },{
                    "name": "Whole Building",
                    "zones": [
                        { "description": "Ground Surface" },
                        { "description": "Kitchen / Living" },
                        { "description": "Living" },
                        { "description": "Night Time" },
                        { "description": "Bedroom" },
                        { "description": "Day Time" },
                        { "description": "Garage" },
                        { "description": "Garage Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" },
                    ]
                }]
            },
            "habitable": {
                "groupName": "Zone Type",
                "descriptionHeading": "Habitable",
                "rows": [{
                    "name": "Ground Floor",
                    "zones": [
                        { "description": "Class 10a" },
                        { "description": "Habitable Room" },
                        { "description": "Non-Habitable Room" },
                        { "description": "Interconnecting Space" },
                    ]
                },{
                    "name": "First Floor",
                    "zones": [
                        { "description": "Class 10a" },
                        { "description": "Habitable Room" },
                        { "description": "Non-Habitable Room" },
                        { "description": "Interconnecting Space" },
                    ]
                },{
                    "name": "Second Floor",
                    "zones": [
                        { "description": "Class 10a" },
                        { "description": "Habitable Room" },
                        { "description": "Non-Habitable Room" },
                        { "description": "Interconnecting Space" },
                    ]
                },{
                    "name": "Whole Building",
                    "zones": [
                        { "description": "Class 10a" },
                        { "description": "Habitable Room" },
                        { "description": "Non-Habitable Room" },
                        { "description": "Interconnecting Space" },
                    ]
                }]
            },
            "conditioned": {
                "rows": [{
                    "name": "Ground Floor",
                    "zones": [
                        { "description": "Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "First Floor",
                    "zones": [
                        { "description": "Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "Second Floor",
                    "zones": [
                        { "description": "Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "Whole Building",
                    "zones": [
                        { "description": "Conditioned" },
                        { "description": "Unconditioned" },
                        { "description": "All Zones" }
                    ]
                }],
                "groupName": "Conditioning",
                "descriptionHeading": "Conditioning"
            },
            "nccClassification": {
                "groupName": "NCC Classification",
                "descriptionHeading": "NCC Classification",
                "rows": [{
                    "name": "Ground Floor",
                    "zones": [
                        { "description": "Class 1a" },
                        { "description": "Class 10a" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "First Floor",
                    "zones": [
                        { "description": "Class 1a" },
                        { "description": "Class 10a" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "Second Floor",
                    "zones": [
                        { "description": "Class 1a" },
                        { "description": "Class 10a" },
                        { "description": "All Zones" }
                    ]
                },{
                    "name": "Whole Building",
                    "zones": [
                        { "description": "Class 1a"  },
                        { "description": "Class 10a" },
                        { "description": "All Zones" }
                    ]
                }]
            }
        }

        // PLACEHOLDERS: Envelope Summary
        const placeholder_envelopeSummary = {
            "isPlaceholder": true,
            "sectorKeys": [
                "n",
                "ne",
                "e",
                "se",
                "s",
                "sw",
                "w",
                "nw",
                "total"
            ],
            "exteriorWallAreaTotalsPerSector": {
                "total": {},
                "n": {},
                "ne": {},
                "e": {},
                "se": {},
                "s": {},
                "sw": {},
                "w": {},
                "nw": {}
            },
            "exteriorGlazingAreaTotalsPerSector": {
                "total": {},
                "n": {},
                "ne": {},
                "e": {},
                "se": {},
                "s": {},
                "sw": {},
                "w": {},
                "nw": {}
            },
            "glassExteriorWallRatioPerSector": {
                "total": "",
                "n": "",
                "ne": "",
                "e": "",
                "se": "",
                "s": "",
                "sw": "",
                "w": "",
                "nw": ""
            },
            "averageGlazingUValuePerSector": {
                "n": "",
                "ne": "",
                "e": "",
                "se": "",
                "s": "",
                "sw": "",
                "w": "",
                "nw": "",
                "total": ""
            },
            "averageGlazingSHGCPerSector": {
                "n": "",
                "ne": "",
                "e": "",
                "se": "",
                "s": "",
                "sw": "",
                "w": "",
                "nw": "",
                "total": ""
            }
        }

        // PLACEHOLDERS: Interior Summary
        const placeholder_interiorSummary = {
            storeys: [{
                name: "Ground Floor",
                rows: [
                    { adjacencyName: "Interior Zones" },
                    { adjacencyName: "Garage" },
                    { adjacencyName: "Roof Space" },
                    { adjacencyName: "Subfloor Space" },
                    { adjacencyName: "Neighbour" },
                    { adjacencyName: "All Adjacencies" }
                ]
            },{
                name: "First Floor",
                rows: [
                    { adjacencyName: "Interior Zones" },
                    { adjacencyName: "Garage" },
                    { adjacencyName: "Roof Space" },
                    { adjacencyName: "Subfloor Space" },
                    { adjacencyName: "Neighbour" },
                    { adjacencyName: "All Adjacencies" }
                ]
            },{
                name: "Second Floor",
                rows: [
                    { adjacencyName: "Interior Zones" },
                    { adjacencyName: "Garage" },
                    { adjacencyName: "Roof Space" },
                    { adjacencyName: "Subfloor Space" },
                    { adjacencyName: "Neighbour" },
                    { adjacencyName: "All Adjacencies" }
                ]
            },{
                name: "Whole Building",
                rows: [
                    { adjacencyName: "Interior Zones" },
                    { adjacencyName: "Garage" },
                    { adjacencyName: "Roof Space" },
                    { adjacencyName: "Subfloor Space" },
                    { adjacencyName: "Neighbour" },
                    { adjacencyName: "All Adjacencies" }
                ]
            }]
        }


        // Services
        return {
            setValueFromLink,
            placeholder_drawingAreas,
            placeholder_zoneSummary,
            placeholder_envelopeSummary,
            placeholder_interiorSummary
        };

    }
})();
