<!-- TODO: Optimize display. Seems to be a performance problem. -->
<div ng-form="BuildingServicesOptionForm{{vm.option.optionIndex}}{{vm.buildingType}}">

    <!-- Template Base + Selection + Clear -->
    <md-card layout-margin ng-if="vm.buildingType != 'template'">

        <md-card-header class="layout-column">

            <building-services-template-selector building="vm.building"
                                                 building-type="vm.buildingType"
                                                 option="vm.option"
                                                 disabled="vm.disabled"
                                                 new-job="false"
                                                 show-copy-previous="false">
            </building-services-template-selector>

            <div ng-if="!vm.isTemplate"
                 layout="row"
                 class="md-block"
                 style="margin-left: auto">
                <md-button ng-click="vm.copyServicesToFrom(option[vm.buildingType], vm.baselineOption[vm.buildingType])"
                           class="md-raised"
                           ng-show="!vm.disabledEx() && vm.showCopyBaselineForOption(vm.baselineOption) && vm.comparisonBuilding != null">
                    Copy Baseline
                </md-button>

                <md-button ng-click="vm.copyServicesToFrom(option.reference, option.proposed)"
                           class="md-raised"
                           ng-show="!vm.disabledEx() && vm.buildingType=='reference'">
                    Copy Proposed
                </md-button>

                <!-- Now for every compliance option EXCEPT THIS ONE AND THE BASELINE show a "copy Option X" button -->
                <md-button ng-repeat="opt in vm.optionsNotThisOrBaseline(vm.option)"
                           ng-if="!vm.disabledEx() && vm.option.optionIndex != 0 && vm.showCopyBaselineForOption(opt)"
                           ng-click="vm.copyServicesToFrom(option[vm.buildingType], opt[vm.buildingType])">
                    Copy Option {{opt.optionIndex}}
                </md-button>
            </div>

        </md-card-header>
    </md-card>

    <!-- Loop over all desired categories IN ORDER SET BY CALLING CODE (CONFIRM ???) -->
    <md-card ng-repeat="category in vm.serviceCategoryList track by category.serviceCategoryCode"
             layout-margin
             ng-form="{{category.serviceCategoryCode}}ServiceForm{{option.optionIndex}}">
        <md-card-header layout="row" layout-wrap>
            <span class="md-title clickable"
                  layout="row"
                  ng-click="vm.expandSection(category.serviceCategoryCode)"
                  ng-class="{'card-has-errors' : {{category.serviceCategoryCode}}ServiceForm{{option.optionIndex}}.$invalid==true ||
                                                  ((building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] == false || 
                                                  building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] == null) && 
                                                  !vm.hasElements(category, building)) &&
                                                  !vm.externalDataIsSpecified(category, vm.building) }">

                <span style="margin-right: 15px;"
                      ng-style="{color: building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] ? 'lightgrey' : 'inherit'}">
                    {{category.title}}
                </span>
                <span ng-if="category.hasChildRows === true && !building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()]"
                      style="margin-right: 15px; display: inline-block;"
                      ng-if="!building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()]">
                    <i ng-if="vm.sectionExpansions[category.serviceCategoryCode]"
                       class="fa fa-caret-up" />
                    <i ng-if="(!vm.sectionExpansions[category.serviceCategoryCode])"
                       class="fa fa-caret-down" />
                </span>

                <!-- For certain Categories we have the option that say that external data has been supplied for them, so there is no need for it here. -->
                <md-radio-group ng-if="!vm.disabledEx() &&
                                       ((building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] == false ||
                                         building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] == null ||
                                         building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] == undefined) &&
                                        category.allowExternalData == true)"
                                ng-model="vm.building.categoriesWithExternalData[category.serviceCategoryCode.toLowerCase()]"
                                layout="row"
                                style="margin-left: 5px; transform: scale(0.65); color: #333333;">
                    <md-radio-button ng-value="false">
                        Specified&nbsp;
                    </md-radio-button>
                    <md-radio-button ng-value="true"
                                     ng-click="vm.clearCategory(category, vm.building)">
                        {{category.externalDataSourceTitle}} &nbsp;
                    </md-radio-button>

                </md-radio-group>

            </span>
            <span flex></span>
            <md-checkbox name="{{category.title}}Required"
                         ng-model="building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()]"
                         ng-click="vm.clearCategory(category, vm.building)"
                         ng-disabled="vm.disabledEx()">
                Not Applicable
            </md-checkbox>
        </md-card-header>
        <md-card-content ng-if="building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()] != true">

            <!-- Otherwise, loop over all our grouped headings + expandable buttons-->

            <!-- HEATING SYSTEM -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('SpaceHeatingSystem') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'SpaceHeatingSystem'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="min-height: 70px; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">System Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">
                            {{parent.serviceType.serviceTypeCode == 'HeatPumpDucted' || parent.serviceType.serviceTypeCode == 'HeatPumpNonDucted' 
                                ? 'Energy Rating (GEMS 2019)'
                                : 'Energy Rating'}}
                        </span>
                        <span class="header">Ducted</span>
                        <span class="header">Flued</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-service-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-is-ducted-input'" />
                        <div ng-include="'parent-only-is-flued-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- COOLING SYSTEM -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('SpaceCoolingSystem') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'SpaceCoolingSystem'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;  min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">System Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">
                            {{parent.serviceType.serviceTypeCode == 'HeatPumpDucted' || parent.serviceType.serviceTypeCode == 'HeatPumpNonDucted' 
                                ? 'Energy Rating (GEMS 2019)'
                                : 'Energy Rating'}}
                        </span>
                        <span class="header">Ducted</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-service-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-is-ducted-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- HOT WATER SYSTEM -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('HotWaterSystem') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'HotWaterSystem'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;  min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">System Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">Energy Rating</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-service-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- COOKTOP -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('Cooktop') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'Cooktop'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Cooktop Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">Energy Rating</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-service-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- OVEN -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('Oven') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'Oven'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Oven Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">Energy Rating</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-service-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- ARTIFICIAL LIGHTING -->
            <!-- HAS CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('ArtificialLighting') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'ArtificialLighting'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'service-parent-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr; min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Lamp Type</span>
                        <span class="header">Lamp Power Rating (W)</span>
                        <span class="header">Recessed</span>
                        <span class="header">Sealed</span>
                        <span class="header">IC Rating</span>
                        <span class="header">Cut Out Diameter (mm)</span>
                        <span class="header">Number</span>
                        <span class="header">Cut Out Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'parent-service-type-input'" />
                        <div ng-include="'parent-lamp-power-rating-input'" />
                        <div ng-include="'parent-is-recessed-input'" />
                        <div ng-include="'parent-is-sealed-input'" />
                        <div ng-include="'parent-ic-rating-input'" />
                        <div ng-include="'parent-cut-out-diameter-input'" />
                        <div ng-include="'parent-number-calculation'" />
                        <div ng-include="'parent-cut-out-area-calculation'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'service-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);" class="elements-table-h-elements">Element</th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);" style="min-width: 180px;">Parent Zone</th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);" style="min-width: 180px;">Storey</th>
                                <th ng-if="vm.isTableColumnVisible('serviceControlDevice', parent);" style="min-width: 240px;">Control Device</th>
                                <th ng-if="vm.isTableColumnVisible('adjustmentFactor', parent);">Adjustment Factor</th>
                                <th ng-if="vm.isTableColumnVisible('number', parent);">Number</th>
                                <th ng-if="vm.isTableColumnVisible('cutoutArea', parent);">Cut Out Area (m<sup>2</sup>)</th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        style="margin-right: 5px;"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in parent.elements track by item.serviceTemplateId">
                                <td ng-include="'service-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('serviceControlDevice', parent);"
                                    style="min-width: 240px;"
                                    ng-include="'element-service-control-device-input'" />
                                <td ng-if="vm.isTableColumnVisible('adjustmentFactor', parent);"
                                    ng-include="'element-adjustment-factor-input'" />
                                <td ng-if="vm.isTableColumnVisible('number', parent);"
                                    ng-include="'element-number-input'" />
                                <td ng-if="vm.isTableColumnVisible('cutoutArea', parent);"
                                    ng-include="'element-cut-out-area-calculation'" />
                                <td ng-include="'element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div ng-include="'service-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- EXHAUST FANS -->
            <!-- HAS CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('ExhaustFans') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'ExhaustFans'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'service-parent-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;  min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Lamp Type</span>
                        <span class="header">Lamp Power Rating (W)</span>
                        <span class="header">Recessed</span>
                        <span class="header">Sealed</span>
                        <span class="header">Cut Out Diameter (mm)</span>
                        <span class="header">Number</span>
                        <span class="header">Cut Out Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'parent-service-type-input'" />
                        <div ng-include="'parent-lamp-power-rating-input'" />
                        <div ng-include="'parent-is-recessed-input'" />
                        <div ng-include="'parent-is-sealed-input'" />
                        <div ng-include="'parent-cut-out-diameter-input'" />
                        <div ng-include="'parent-number-calculation'" />
                        <div ng-include="'parent-cut-out-area-calculation'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'service-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);" class="elements-table-h-elements">Element</th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);" style="min-width: 180px;">Parent Zone</th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);" style="min-width: 180px;">Storey</th>
                                <th ng-if="vm.isTableColumnVisible('number', parent);">Number</th>
                                <th ng-if="vm.isTableColumnVisible('cutoutArea', parent);">Cut Out Area (m<sup>2</sup>)</th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        style="margin-right: 5px;"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in parent.elements track by item.serviceTemplateId">
                                <td ng-include="'service-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('number', parent);" ng-include="'element-number-input'" />
                                <td ng-if="vm.isTableColumnVisible('cutoutArea', parent);" ng-include="'element-cut-out-area-calculation'" />
                                <td ng-include="'element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'service-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- CEILING VENTS -->
            <!-- HAS CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('CeilingVents') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'CeilingVents'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'service-parent-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->

                        <span class="header">Recessed</span>
                        <span class="header">Sealed</span>
                        <span class="header">Length (mm)</span>
                        <span class="header">Width (mm)</span>
                        <span class="header">Number</span>
                        <span class="header">Cut Out Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'parent-is-recessed-input'" />
                        <div ng-include="'parent-is-sealed-input'" />
                        <div ng-include="'parent-length-input'" />
                        <div ng-include="'parent-width-input'" />
                        <div ng-include="'parent-number-calculation'" />
                        <div ng-include="'parent-cut-out-area-calculation'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'service-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);" class="elements-table-h-elements">Element</th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);" style="min-width: 180px;">Parent Zone</th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);" style="min-width: 180px;">Storey</th>
                                <th ng-if="vm.isTableColumnVisible('number', parent);">Number</th>
                                <th ng-if="vm.isTableColumnVisible('cutoutArea', parent);">Cut Out Area (m<sup>2</sup>)</th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        style="margin-right: 5px;"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in parent.elements track by item.serviceTemplateId">
                                <td ng-include="'service-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('number', parent);" ng-include="'element-number-input'" />
                                <td ng-if="vm.isTableColumnVisible('cutoutArea', parent);" ng-include="'element-cut-out-area-square-calculation'" />
                                <td ng-include="'element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'service-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- CEILING FANS -->
            <!-- HAS CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('CeilingFans') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'CeilingFans'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'service-parent-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; min-height: 70px;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Lamp Type</span>
                        <span class="header">Lamp Power Rating (W)</span>
                        <span class="header">Blade Diameter (mm)</span>
                        <span class="header">Permanently Installed</span>
                        <span class="header">Speed Controller</span>
                        <span class="header">Number</span>

                        <!-- Data -->
                        <div ng-include="'parent-service-type-input'" />
                        <div ng-include="'parent-lamp-power-rating-input'" />
                        <div ng-include="'parent-blade-diameter-input'" />
                        <div ng-include="'parent-is-permanently-installed-input'" />
                        <div ng-include="'parent-has-speed-controller-input'" />
                        <div ng-include="'parent-number-calculation'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'service-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);" class="elements-table-h-elements">Element</th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);" style="min-width: 180px;">Parent Zone</th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);" style="min-width: 180px;">Storey</th>
                                <th ng-if="vm.isTableColumnVisible('number', parent);">Number</th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        style="margin-right: 5px;"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in parent.elements track by item.serviceTemplateId">
                                <td ng-include="'service-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('number', parent);" ng-include="'element-number-input'" />
                                <td ng-include="'element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'service-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- PHOTOVOLTAIC SYSTEM -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('PhotovoltaicSystem') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'PhotovoltaicSystem'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: min-content min-content min-content min-content min-content min-content min-content min-content; min-height: 70px; justify-content: space-evenly; justify-items: center; align-items: center; ">

                        <!-- Headers -->
                        <span class="header" style="white-space: nowrap;">Array Capacity (kW)</span>
                        <span class="header" style="white-space: nowrap;">Array Area (m<sup>2</sup>)</span>
                        <span class="header" style="white-space: nowrap;">Azimuth (&deg;)</span>
                        <span class="header" style="white-space: nowrap;">Pitch (&deg;)</span>
                        <span class="header" style="white-space: nowrap;">Shade Factor (%)</span>
                        <span class="header" style="white-space: nowrap;">Inverter Capacity (kW)</span>
                        <span class="header" style="white-space: nowrap;">Battery Type</span>
                        <span class="header" style="white-space: nowrap;">Battery Capacity (kWh)</span>

                        <!-- Data -->
                        <div ng-include="'parent-system-capacity-input'" />
                        <div ng-include="'parent-area-input'" />
                        <div ng-include="'parent-azimuth-input'" />
                        <div ng-include="'parent-pitch-input'" />
                        <div ng-include="'parent-shade-factor-input'" />
                        <div ng-include="'parent-inverter-capacity-input'" />
                        <div ng-include="'parent-battery-type-input'" />
                        <div ng-include="'parent-battery-capacity-input'" />
                    </div>

                </div>
            </div>

            <!-- SWIMMING POOL -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('SwimmingPool') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'SwimmingPool'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 0.4fr 0.4fr 0.4fr; min-height: 70px; justify-content: space-evenly; justify-items: center; align-items: center; ">

                        <!-- Headers -->
                        <span class="header">Volume (L)</span>
                        <span class="header">Pump Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">Pump Energy Rating</span>
                        <span class="header">Heating System</span>
                        <span class="header">Cover</span>
                        <span class="header">Time Switch</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-volume-input'" />
                        <div ng-include="'parent-only-pump-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-heating-system-type-input'" />
                        <div ng-include="'parent-only-has-cover-input'" />
                        <div ng-include="'parent-only-has-time-switch-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- SPA -->
            <!-- NO CHILD ROWS -->
            <div ng-repeat="parent in vm.servicesInCategory('Spa') track by parent.serviceTemplateId"
                 ng-if="category.serviceCategoryCode == 'Spa'"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div ng-include="'parent-only-description-input'" style="width: 99%;" />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 0.4fr 0.4fr 0.4fr; min-height: 70px; justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Volume (L)</span>
                        <span class="header">Pump Type</span>
                        <span class="header">Fuel Type</span>
                        <span class="header">Pump Energy Rating</span>
                        <span class="header">Heating System</span>
                        <span class="header">Cover</span>
                        <span class="header">Time Switch</span>
                        <span class="header">Primary</span>

                        <!-- Data -->
                        <div ng-include="'parent-only-volume-input'" />
                        <div ng-include="'parent-only-pump-type-input'" />
                        <div ng-include="'parent-only-fuel-type-input'" />
                        <div ng-include="'parent-only-star-rating-2019-input'" />
                        <div ng-include="'parent-only-heating-system-type-input'" />
                        <div ng-include="'parent-only-has-cover-input'" />
                        <div ng-include="'parent-only-has-time-switch-input'" />
                        <div ng-include="'parent-only-is-primary-input'" />
                    </div>

                </div>
            </div>

            <!-- ADD NEW CONSTRUCTIONS -->
            <div ng-include="'service-template-search'"></div>

        </md-card-content>
    </md-card>

</div>

<!-- SCRIPTS ---------------------------------------------------------------------------------------------------------->
<script type="text/ng-template" id="service-parent-action-buttons">
    <div style="display: flex; justify-content: center;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                 class="clickable"
                 ng-click="$mdOpenMenu()"
                 src="/content/feather/more-horizontal.svg"
                 ng-disabled="vm.disabled"/>

            <md-menu-content>

                <!-- Rename parent item -->
                <md-menu-item>
                    <md-button ng-click="vm.launchParentActionModal('rename', parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Rename
                    </md-button>
                </md-menu-item>

                <!-- Substitube parent item -->
                <md-menu-item>
                    <md-button ng-click="vm.launchParentActionModal('substitute', parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Substitute
                    </md-button>
                </md-menu-item>

                <!-- Duplicate Parent Button -->
                <md-menu-item>
                    <md-button ng-click="vm.duplicateParent(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Show / Hide Button -->
                <md-menu-item>
                    <md-button ng-disabled="vm.disabledEx()"
                               ng-click="parent.showInReport = !parent.showInReport">
                        <span ng-show="parent.showInReport">Hide</span>
                        <span ng-show="!parent.showInReport">Show</span>
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Parent Button -->
                <md-menu-item>
                    <md-button ng-click="vm.removeParentService(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

    </div>
</script>

<!-- Element Action Buttons -->
<script type="text/ng-template" id="element-action-buttons">
    <div style="display: flex; justify-content: center;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                 class="clickable"
                 ng-click="$mdOpenMenu()"
                 src="/content/feather/more-horizontal.svg"
                 ng-disabled="vm.disabled"/>
            <md-menu-content>

                <!-- Duplicate Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.copyElement(parent, item)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Move or copy Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.launchElementActionModal(parent, building, [item])"
                               ng-disabled="vm.disabledEx()">
                        Move or copy
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.deleteElement(parent, item)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

    </div>
</script>

<!-- Expand / Collapse Parent button -->
<script type="text/ng-template" id="expand-collapse-button">
    <span style="margin: 0 10px; display: inline-block;"
          ng-click="parent.isExpanded = !parent.isExpanded;">
        <i ng-if="parent.isExpanded"
           class="fa fa-caret-up" style="font-size: 18px;" />
        <i ng-if="(!parent.isExpanded)"
           class="fa fa-caret-down" style="font-size: 18px;" />
    </span>
</script>

<!-- Add new Construction selector. -->
<script type="text/ng-template" id="service-template-search">

    <!--
        Switch between showing an ADD BUTTON and an autocomplete.
        De-selecting the auto-complete should result in the Add button appearing again
        and vice-versa.
    -->
    <md-button ng-show="!vm.disabledEx() && (category.inAddMode == null || category.inAddMode == false)"
               class="md-primary"
               ng-click="vm.switchToAddMode(category);"
               ng-disabled="vm.disabledEx()">
        ADD SERVICE
    </md-button>
    <div ng-if="!vm.disabledEx() && category.inAddMode == true"
         layout="row">
        <md-autocomplete flex="95"
                         id="Template{{category.serviceCategoryCode}}"
                         md-input-name="template"
                         md-selected-item="tempTemplate"
                         md-selected-item-change="vm.addServiceParentFromTemplate(category, building, tempTemplate)"
                         md-search-text="templateSearchText"
                         md-items="listItem in vm.filteredTemplateList({{category}}) | filter: { description: templateSearchText }"
                         md-item-text="listItem.description"
                         md-min-length="0"
                         ng-required="true"
                         md-select-on-match="true"
                         md-require-match="true"
                         placeholder="Search..."
                         class="glowing vertically-condensed kindly-remove-error-space"
                         style="margin: 10px">
            <md-item-template>
                <span md-highlight-text="templateSearchText"
                      md-highlight-flags="^i">{{listItem.description}}</span>
            </md-item-template>
        </md-autocomplete>

        <md-button class="md-icon-button checkbox-aligner"
                   flex="5"
                   ng-click="category.inAddMode = false;"
                   style="margin: 10px">
            <i class="material-icons">
                clear
            </i>
        </md-button>
    </div>

</script>

<script type="text/ng-template" id="parent-only-description-input">
    <div style="display: grid; grid-template-columns: 1fr 40px; align-items: center; padding-left: 30px;">

        <!-- Construction Description -->
        <span class="lightweight construction-parent-title"
              disabled>

            <!-- Non-clickable description / title -->
            <span>
                {{parent.overrideDisplayDescription || parent.displayDescription || parent.description}}
            </span>
        </span>

        <!-- Parent Actions -->
        <span ng-include="'service-parent-action-buttons'"></span>

    </div>
</script>

<script type="text/ng-template" id="parent-only-is-ducted-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isDucted"
                 ng-model="parent.isDucted"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-only-is-flued-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isFlued"
                 ng-model="parent.isFlued"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-only-is-primary-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isPrimary"
                 ng-model="parent.isPrimary"
                 ng-disabled="vm.disabledEx() || vm.isOnlyChild(parent.serviceCategory)"
                 ng-change="vm.updatePrimaryServiceForCategory(parent.isPrimary, parent.serviceCategory, parent)">
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-only-has-cover-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="hasCover"
                 ng-model="parent.hasCover"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-only-has-time-switch-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="hasTimeSwitch"
                 ng-model="parent.hasTimeSwitch"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-only-service-type-input">
    <div class="lightweight" style="color:grey;">{{vm.getMappedSystemType(parent.serviceType, category.serviceCategoryCode, 'title')}}</div>
</script>

<script type="text/ng-template" id="parent-only-pump-type-input">
    <input ng-if="vm.templateFieldHasValue(parent.derivedFromServiceTemplateId, 'servicePumpType')"
           class="lightweight"
           disabled
           ng-model="parent.servicePumpType.title"/>
    <md-select ng-if="!vm.templateFieldHasValue(parent.derivedFromServiceTemplateId, 'servicePumpType')"
               ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx()"
               ng-model="parent.servicePumpType"
               ng-model-options="{trackBy: '$value.servicePumpTypeCode'}">
        <md-option ng-value="servicePumpType"
                   ng-repeat="servicePumpType in vm.servicePumpTypes">
            {{servicePumpType.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="parent-only-fuel-type-input">
    <input class="lightweight"
           disabled
           ng-model="parent.serviceFuelType.title"/>
</script>

<script type="text/ng-template" id="parent-only-heating-system-type-input">

    <md-select ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx()"
               ng-model="parent.heatingSystemType"
               ng-model-options="{trackBy: '$value.heatingSystemTypeCode'}">

        <md-option ng-value="heatingSystemType"
                   ng-repeat="heatingSystemType in vm.heatingSystemTypesForCategoryCode(category.serviceCategoryCode)">
            {{heatingSystemType.title}}
        </md-option>
    </md-select>

</script>

<script type="text/ng-template" id="parent-only-star-rating-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.starRating"
           formatted-number
           decimals="1" />
</script>

<script type="text/ng-template" id="parent-only-star-rating-2019-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.starRating2019"
           formatted-number
           decimals="1" />
</script>

<script type="text/ng-template" id="parent-only-volume-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.volume"
           formatted-number
           decimals="0"/>
</script>

<!-- FOR CATEGORIES THAT HAVE CHILD ELEMENTS -->
<script type="text/ng-template" id="element-id-input">
    <input disabled
           ng-required="true"
           ng-model="item.elementNumber"
           style="width: 60px;" />
</script>

<script type="text/ng-template" id="element-parent-zone-input">
    <md-select ng-required="true"
               name="ParentZone{{$index}}"
               style="margin: 0;"
               ng-disabled="vm.disabled"
               ng-model="item.parentZoneId">
        <md-option ng-value="x.linkId"
                   ng-repeat="x in vm.availableBuildingZones(item)">
            {{x.zoneDescription}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="element-storey-calc">
    <input class="lightweight"
           style="width: auto;"
           disabled
           ng-value="vm.floorOfZone(item.parentZoneId)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="service-parent-description-input">
    <div style="display: grid; grid-template-columns: 30px 1fr 40px; align-items: center;">

        <!-- Expand/Close Button -->
        <span class="clickable"
              style="margin: 0 10px; display: inline-block;"
              ng-click="parent.isExpanded = !parent.isExpanded">
            <i ng-show="parent.isExpanded"
               class="fa fa-caret-up" style="font-size: 18px;" />
            <i ng-show="(!parent.isExpanded)"
               class="fa fa-caret-down" style="font-size: 18px;" />
        </span>

        <!-- Construction Description -->
        <span class="lightweight construction-parent-title"
              disabled>

            <!-- Non-clickable description / title -->
            <span>
                {{parent.overrideDisplayDescription || parent.displayDescription || parent.description}}
            </span>
        </span>

        <!-- Parent Actions -->
        <span ng-include="'service-parent-action-buttons'"></span>

    </div>
</script>

<script type="text/ng-template" id="parent-service-type-input">
    <input class="lightweight"
           disabled
           ng-model="parent.serviceType.title"/>
</script>

<script type="text/ng-template" id="parent-lamp-power-rating-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx() || parent.serviceType.serviceTypeCode == 'NotApplicable'"
           ng-model="parent.lampPowerRating"
           ng-required="true"
           formatted-number
           decimals="0" />
</script>

<script type="text/ng-template" id="parent-blade-diameter-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.bladeDiameter"
           ng-required="true"
           formatted-number
           decimals="0" />
</script>

<script type="text/ng-template" id="parent-is-recessed-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isRecessed"
                 ng-model="parent.isRecessed"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-is-permanently-installed-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isPermanentlyInstalled"
                 ng-model="parent.isPermanentlyInstalled"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-is-sealed-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="isSealed"
                 ng-model="parent.isSealed"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-has-speed-controller-input">
    <md-checkbox class="checkbox-aligner checkbox-height-fix"
                 style="margin: auto; padding: 0;"
                 name="hasSpeedController"
                 ng-model="parent.hasSpeedController"
                 disabled>
    </md-checkbox>
</script>

<script type="text/ng-template" id="parent-ic-rating-input">
    <md-select ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx()"
               ng-model="parent.icRating"
               ng-model-options="{trackBy: '$value.icRatingCode'}">

        <md-option ng-value="icRating"
                   ng-repeat="icRating in vm.icRatings">
            {{icRating.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="parent-battery-type-input">
    <md-select ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx()"
               ng-model="parent.serviceBatteryType"
               ng-change="vm.batteryTypeChange(parent)"
               ng-model-options="{trackBy: '$value.serviceBatteryTypeCode'}">

        <md-option ng-value="batteryType"
                   ng-repeat="batteryType in vm.batteryTypes">
            {{batteryType.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="parent-width-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.width"
           ng-required="true"
           formatted-number
           decimals="0" />
</script>

<script type="text/ng-template" id="parent-length-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.length"
           ng-required="true"
           formatted-number
           decimals="0" />
</script>

<script type="text/ng-template" id="parent-cut-out-diameter-input">
    <input class="lightweight"
           name="cutOutDiameter"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.cutOutDiameter"
           ng-required="true"
           formatted-number
           decimals="0"/>
</script>

<script type="text/ng-template" id="parent-number-calculation">
    <input class="lightweight"
           name="parentNumber"
           disabled
           ng-value="vm.numberSum(parent).toFixed(0)"/>
</script>

<script type="text/ng-template" id="parent-cut-out-area-calculation">
    <input class="lightweight"
           name="cutOutAreaSum"
           disabled
           ng-value="vm.cutOutAreaSum(parent).toFixed(6)"/>
</script>

<script type="text/ng-template" id="parent-system-capacity-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.systemCapacity"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-area-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.area"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-azimuth-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.azimuth"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-pitch-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.pitch"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-shade-factor-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.shadeFactor"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-inverter-capacity-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="parent.inverterCapacity"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-battery-capacity-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx() || parent.serviceBatteryType.serviceBatteryTypeCode == 'None'"
           ng-model="parent.batteryCapacity"
           ng-required="true"
           formatted-number
           decimals="2"
           style="width: 100px;"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="parent-panel-area-calculation">
    <input class="lightweight"
           name="panelAreaSum"
           disabled
           ng-value="vm.panelAreaSum(parent).toFixed(2)"/>
</script>

<script type="text/ng-template" id="element-panel-area-input">
    <input class="lightweight"
           ng-disabled="vm.disabledEx()"
           ng-model="item.panelArea"
           ng-required="true"
           formatted-number
           decimals="2"
           show-zeros="true" />
</script>

<script type="text/ng-template" id="element-service-control-device-input">
    <md-select ng-required="true"
               class="lightweight service-control-device-style"
               ng-disabled="vm.disabledEx()"
               ng-model="item.serviceControlDevice"
               ng-model-options="{trackBy: '$value.serviceControlDeviceCode'}">

        <md-option ng-value="serviceControlDevice"
                   ng-repeat="serviceControlDevice in vm.serviceControlDevices">
            {{serviceControlDevice.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="element-adjustment-factor-input">
    {{item.serviceControlDevice.adjustmentFactor.toFixed(2)}}
</script>

<script type="text/ng-template" id="element-number-input">
    <md-autocomplete
        class="lightweight small-autocomplete"
        md-menu-class="small-autocomplete-dropdown"
        md-input-name="item-number"
        id="{{item.elementNumber}}-Number"
        md-selected-item="item.number"
        md-selected-item-change="vm.selectedNumberChanged(item, item.selectedNumber)"
        md-search-text="item.numberSearchText"
        md-items="listItem in vm.searchNumbersList(item.numberSearchText)"
        md-item-text="listItem"
        ng-blur="vm.selectSearchedNumber(item)"
        md-min-length="0">
        <md-item-template>
            <span md-highlight-text="item.numberSearchText">
                {{listItem}}
            </span>
        </md-item-template>
    </md-autocomplete>
</script>

<script type="text/ng-template" id="element-cut-out-area-calculation">
    <input class="lightweight"
           disabled
           ng-value="vm.cutOutAreaCalculation(parent, item).toFixed(6);"/>
</script>

<script type="text/ng-template" id="element-cut-out-area-square-calculation">
    <input class="lightweight"
           disabled
           ng-value="vm.cutOutAreaSquareCalculation(parent, item).toFixed(6);"/>
</script>

<script type="text/ng-template" id="element-azimuth-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx()"
           ng-model="item.azimuth"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="service-element-element-sector-input">
    <input class="lightweight"
           name="units"
           disabled
           ng-model="item.sector" />
</script>

<script type="text/ng-template" id="parent-manufacturer-input">
    <input class="lightweight disabled"
           type="text"
           disabled
           ng-required="true"
           ng-model="parent.manufacturer.description" />
</script>

<!-- Inputs with special rules ---------------------------------------------------------------------------------------->
<script type="text/ng-template" id="photovoltaic-element-parent-zone-input">
    <md-select ng-required="true"
               name="ParentZone{{$index}}"
               style="margin: 0;"
               ng-disabled="vm.disabled"
               ng-model="item.parentZoneId"
               ng-change="vm.updateItemToMatchZoneData(item, item.parentZoneId)">
        <md-option ng-value="x.linkId"
                   ng-repeat="x in vm.roofSpaceZones(vm.building.zones)">
            {{x.zoneDescription}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="photovoltaic-element-storey-input">
    <md-select ng-required="true"
               style="margin: 0;"
               name="Storey{{$index}}"
               disabled
               ng-model="item.storey">
        <md-option ng-value="x.floor"
                   ng-repeat="x in vm.building.storeys">
            {{x.name}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="photovoltaic-element-tilt-input">
    <input id="{{item.serviceTemplateId}}Tilt"
           formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx()"
           ng-model="item.tilt"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="service-element-bulk-edit-checkbox-master">
    <md-checkbox id="{{parent.serviceTemplateId}}allCheckbox"
                 ng-disabled="vm.disabledEx()"
                 ng-model="parent.selectAllCheckboxState"
                 md-indeterminate="parent.bulkSelectCheckboxIsIndeterminate || false"
                 ng-click="vm.selectAllElementCheckboxes(parent, parent.selectAllCheckboxState)"
                 style="width: 40px;">
    </md-checkbox>
</script>

<script type="text/ng-template" id="service-element-bulk-edit-checkbox">
    <md-checkbox ng-model="item.checkboxSelected"
                 ng-disabled="vm.disabledEx()"
                 ng-change="vm.updateBulkSelectStatus(parent);"
                 style="width: 40px;">
    </md-checkbox>
</script>

<script type="text/ng-template" id="service-parent-lower-action-buttons">

    <div style="display: flex; justify-content: space-between; margin: 10px 0;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial bulk edit button, which launches options -->
            <md-button class="md-raised md-primary"
                       ng-click="$mdOpenMenu()"
                       ng-disabled="vm.disabledEx() || !(parent.selectAllCheckboxState || parent.bulkSelectCheckboxIsIndeterminate)">
                BULK EDIT
            </md-button>

            <md-menu-content>

                <!-- Duplicate Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.bulkCopyElements(parent)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Move or copy Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.launchElementActionModal(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Move or copy
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.bulkDeleteElements(parent)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

        <add-element-button parent="parent"
                            building="building"
                            elements-added-callback="vm.setElementCodesForCategory(categoryCode)"
                            disabled="vm.disabledEx()"
                            default-control-device="vm.defaultControlDevice"
                            type="'services'"/>

    </div>
</script>
