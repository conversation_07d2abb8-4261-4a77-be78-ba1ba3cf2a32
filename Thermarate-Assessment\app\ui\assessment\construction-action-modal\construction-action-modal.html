<form class="main-content-wrapper"
      novalidate
      data-ng-controller='constructionActionModalController as vm'
      style="min-width:700px; max-width: 700px;">

    <div class="widget widget-header-vertically-expand"
         ng-form="constructionActionModalForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

            <md-card>

                <!--'Rename' body-->
                <md-card-content ng-if="vm.action === 'rename'">

                    <!-- Description (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>Description</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               ng-model="vm.data.overrideDisplayDescription" />
                    </md-input-container>

                    <!-- Original Description (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original Description</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalDescription" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideDisplayDescription = vm.data.originalDescription;">
                            Restore
                        </md-button>
                    </div>

                    <!-- Insulation fields - only show for construction categories -->
                    <div ng-if="vm.isConstructionCategory()">
                        <!-- Insulation (Editable) -->
                        <md-input-container class="md-block" flex="100">
                            <label>Insulation</label>
                            <input ng-disabled="vm.disabled"
                                   ng-model="vm.data.overrideInsulationDescription" />
                        </md-input-container>

                        <!-- Original Insulation (Disabled) -->
                        <div style="display: grid; grid-template-columns: 1fr 100px;">
                            <md-input-container class="md-block vertically-condensed" flex="100">
                                <label>Original Insulation</label>
                                <input ng-disabled="true"
                                       ng-model="vm.data.originalInsulation" />
                            </md-input-container>
                            <md-button class="md-raised"
                                       ng-disabled="vm.disabled"
                                       ng-click="vm.data.overrideInsulationDescription = vm.data.originalInsulation;">
                                Restore
                            </md-button>
                        </div>
                    </div>

                    <!-- Chenath Construction Code -->
                    <div ng-if="vm.parent.category.type === 'surface'"
                            style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Chenath Construction Code</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.chenathConstructionCode" />
                        </md-input-container>

                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.copyCodeToClipboard(vm.data.chenathConstructionCode);">
                            Copy
                        </md-button>
                    </div>

                </md-card-content>

                <md-card-content ng-if="vm.action == 'substitute'">

                    <div style="display: grid; grid-template-columns: 1fr auto auto;">
                        <!-- Template quick search text -->
                        <md-input-container class="md-block" flex="100">
                            <label>Search</label>
                            <input ng-disabled="vm.disabled"
                                   ng-model="vm.templateSearchText"
                                   ng-change="vm.refineConstructionList()"/>
                        </md-input-container>

                        <!-- Construction Type filter - only show for construction type -->
                        <md-input-container style="margin-left: 20px; min-width: 150px;" ng-if="vm.parent.category.type === 'surface'">
                            <label>Construction Type</label>
                            <md-select ng-model="vm.selectedConstructionType"
                                      ng-change="vm.refineConstructionList()"
                                      ng-disabled="vm.disabled">
                                <md-option ng-repeat="type in vm.constructionTypes" ng-value="type">
                                    {{type}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <md-switch ng-model="vm.showDisplayDesc"
                                   style="margin-left: 20px;">Show Display Description</md-switch>
                    </div>

                    <div class="construction-action-modal-search-container">
                        <div class="clickable construction-action-modal-search-item"
                             ng-repeat="template in vm.refinedTemplates track by $index"
                             ng-click="vm.selectedSubstitute = template;"
                             ng-class="{ 'construction-action-search-selected' : vm.selectedSubstitute === template }">
                            <!-- Favourite Star -->
                            <i ng-if="template.isFavourite==true"
                               class="fa fa-star"
                               style="color: gold; margin-right: 5px;"
                               aria-hidden="true"></i>
                            <span>{{vm.showDisplayDesc ? template.displayDescription : template.description}}</span>
                            <span ng-if="vm.parent.category.type === 'glazing' || vm.parent.category.type === 'rooflight'">
                                (U={{template.performance.uValue.toFixed(2)}} and SHGC={{template.performance.shgc.toFixed(2)}})
                            </span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <span ng-show="vm.selectedSubstitute != null">
                            Clicking save will substitute <strong>{{vm.data.overrideDisplayDescription}}</strong>
                            with <strong> {{vm.showDisplayDesc ? vm.selectedSubstitute.displayDescription : vm.selectedSubstitute.description}}</strong>
                        </span>
                    </div>

                </md-card-content>

            </md-card>

            <!-- Add + Cancel Buttons -->
            <div data-cc-widget-button-bar
                 data-is-modal="true">

                <md-button class="md-raised"
                           ng-if="vm.action === 'rename' && vm.isConstructionCategory()"
                           ng-disabled="vm.disabled"
                           ng-click="vm.restoreAll()">
                    RESTORE ALL
                </md-button>
                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_page_(tabs/sub-tabs)__construction__edit', 'assessment_page_(tabs/sub-tabs)__openings__edit']"
                           ng-disabled="constructionActionModalForm.$invalid || vm.disabled"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>