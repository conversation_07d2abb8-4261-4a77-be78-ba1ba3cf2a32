(function () {
    // The userauditDetailCtrl provides the behaviour behind a reusable form to allow viewing/updating.
    // This controller and its template (useraudit-detail.html) are used in a normal view and a modal dialog box.
    'use strict';
    var controllerId = 'UserauditDetailCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', 'userauditservice', '$stateParams', '$state', '$timeout', userauditDetailController]);
function userauditDetailController($rootScope, $scope, userauditservice, $stateParams, $state, $timeout) {
        // The model for this form 
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = false;
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.useraudit = {};
        vm.hideActionBar = false;
        vm.isModal = $scope.modalInstance != null;
        vm.title = "User Audit Details";
        if (vm.viewMode == 'New') {
            vm.title = "New User Audit";
            vm.hideActionBar = true;
        }

        vm.auditRecId = $stateParams.auditRecId;

        // Make sure service has initialised before populating list.
        vm.isBusy = true;
        userauditservice.initialise().then(function () {
            if (vm.auditRecId != null) {
                // Existing Useraudit so go get it from the server (need to make sure we always have the latest record).
                userauditservice.getUserAudit(vm.auditRecId).then(function () {
                    vm.useraudit = userauditservice.useraudit();
                });
            }
            else {
                // New Useraudit - Set any required defaults
            }
            vm.isBusy = false;
        });

        //Save useraudit Information
        vm.saveUseraudit = function () {
            vm.isBusy = true;
            userauditservice.saveUserAudit(vm.useraudit).then(function () {
                vm.useraudit = userauditservice.useraudit();
                vm.isBusy = false;
                if ($scope.modalInstance) {
                    // For a modal we close the modal.
                    $mdDialog.hide(vm.useraudit);
                }
                else {
                    // On a page view - so stay there.
                }
            });

        };

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.cancel();;
            }
            else {
                if (vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("useraudit-list");
                }
            }
        };

        //Delete useraudit Information
        vm.delete = function () {
            vm.isBusy = true;
            var promise = userauditservice.deleteUserAudit(vm.useraudit.auditRecId);
            promise.then(function () {
                vm.isBusy = false;
                // Now exit the current Page/Modal
                if ($scope.modalInstance) {
                    // Close modal if it was a modal.
                    $mdDialog.hide(vm.useraudit);
                }
                else {
                    $timeout(function () {
                        $state.go(vm.previousRoute);
                        }, 1000);
                }
            })
        };

    }
})();
