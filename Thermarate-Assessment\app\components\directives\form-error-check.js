﻿(function () {
    'use strict';

    //Use: add 'form-error-check' to main form and call $scope.checkErrors();


    var app = angular.module('app');
    app.directive('formErrorCheck', ['$interval', function ($interval) {

        //remove error check
        var running = false;
        var invalidTabs = [];

        return {
            restrict: 'A',
            controller: function ($scope, $element, $attrs, $transclude, $timeout) {

                $scope.checkErrors = function () {
                    // find the first invalid $element
                    var invalid = $element[0].querySelectorAll('.ng-invalid');

                    for (var i = 0; i < invalid.length; i++) {
                        checkForMdTab(invalid[i]);
                    }

                    // if we find one, set focus
                    if (invalid[0]) {
                        invalid[0].focus();
                    }
                };

                function getParent($element) {
                    return $element.parentNode;
                }

                function checkForMdTab(invalid) {
                    //check for md tabs and focus that if found.
                    var searching = true;
                    var tab = invalid;
                    var contentTab = null
                    while (searching) {
                        tab = getParent(tab);

                        if (tab != null && tab.tagName == "MD-TAB-CONTENT") {
                            contentTab = tab;
                        }

                        if (tab != null && tab.tagName != "MD-TABS") {
                            searching = true;
                        } else {
                            searching = false;
                        }
                    }

                    if (tab && contentTab) {
                        var rawindex = contentTab.attributes["aria-labelledby"].value;
                        var indexlength = rawindex.length;
                        var index = "";

                        for (var i = indexlength; i > 0; i--) {
                            if (!isNaN(parseInt(rawindex[i - 1]))) {
                                index = rawindex[i - 1] + index;
                            }
                        }

                        var theTab = angular.element($element[0].querySelector('[md-tab-id="' + index + '"]'));

                        //check to see if already has flag
                        var hasFlag = false;
                        for (var i = 0; i < theTab[0].childNodes.length; i++) {
                            if (theTab[0].childNodes[i].tagName == "I") {
                                hasFlag = true;
                                break;
                            }
                        }
                        if (!hasFlag) {
                            theTab[0].innerHTML = theTab[0].innerHTML + '<i class="fa fa-exclamation tab-error-flag flag"></i>';
                        }

                        theTab.addClass("tab-error-flag");

                        if (invalidTabs.filter(function (e) { return e.tab[0].attributes['md-tab-id'].value == index }).length == 0) {
                            invalidTabs.push({ tab: theTab, content: contentTab });
                        }

                        $timeout(function () {
                            theTab.click();
                        });
                    }
                }

                //*****************************

                // Create Element.remove() function if not exist
                // Fix for IE11 troll
                if (!('remove' in Element.prototype)) {
                    Element.prototype.remove = function () {
                        if (this.parentNode) {
                            this.parentNode.removeChild(this);
                        }
                    };
                }

                //check tabs children for errors
                function checkRemoveError(mdTab) {
                    if (mdTab.tab.hasClass("tab-error-flag")) {
                        var invalid = mdTab.content.querySelectorAll('.ng-invalid');

                        if (invalid.length == 0) {
                            var index = invalidTabs.indexOf(mdTab);
                            invalidTabs.splice(index, 1);

                            //remove class and I tag
                            mdTab.tab.removeClass("tab-error-flag");
                            
                            for (var i = mdTab.tab[0].childNodes.length - 1; i >= 0; i--) {
                                if (mdTab.tab[0].childNodes[i].tagName == "I") {
                                    mdTab.tab[0].childNodes[i].remove();
                                }
                            }
                        }
                    }
                    else {
                        var index = invalidTabs.indexOf(mdTab);
                        invalidTabs.splice(index, 1);
                    }
                }


                //timer checks to find errors that have been fixed
                var timer = $interval(function () {
                    for (var i = 0; i < invalidTabs.length; i++) {
                        checkRemoveError(invalidTabs[i]);
                    }
                }, 500);

                function stopTimer() {
                    if (angular.isDefined(timer)) {
                        $interval.cancel(timer);
                        timer = undefined;
                    }
                }

                $scope.$on('$destroy', stopTimer)

            }//end

        };
    }]);
})();