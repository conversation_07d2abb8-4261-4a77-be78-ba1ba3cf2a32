USE [thermarate];

SELECT TOP (100)
       [zone].[ZoneId]
      ,[zone].[AssessmentComplianceBuildingId]
      ,[building].[Description] [__Building]
    --   ,[building].[SurfacesJson] [__Building_SurfacesJson]
    --   ,[building].[OpeningsJson] [__Building_OpeningsJson]
    --   ,[building].[ServicesJson] [__Building_ServicesJson]
      ,[option].[Description] [__Option]
      ,[zone].[CreatedOn]
      ,[zone].[CreatedByName]
      ,[zone].[ModifiedOn]
      ,[zone].[ModifiedByName]
      ,[zone].[Deleted]
      ,[zone].[SortOrder]
      ,[zone].[Storey]
      ,[zone].[ZoneNumber]
      ,[zone].[ZoneNumberSource]
      ,[zone].[ZoneDescription]
      ,[zone].[ZoneTypeCode]
      ,[zone].[FloorArea]
      ,[zone].[RoofLight]
      ,[zone].[CeilingFan]
      ,[zone].[EvaporativeCooler]
      ,[zone].[NaturalLightRequiredPercent]
      ,[zone].[NaturalLightRequiredM2]
      ,[zone].[NaturalLightAchievedM2]
      ,[zone].[VentilationRequiredPercent]
      ,[zone].[VentilationRequiredM2]
      ,[zone].[VentilationAchievedM2]
      ,[zone].[AirMovementRequiredPercent]
      ,[zone].[AirMovementRequiredM2]
      ,[zone].[AirMovementAchievedM2]
      ,[zone].[LampPowerMaximumWM2]
      ,[zone].[LampPowerMaximumW]
      ,[zone].[LampPowerAchievedW]
      ,[zone].[Conditioned]
      ,[zone].[Volume]
      ,[zone].[NccClassificationCode]
      ,[zone].[ZoneActivityCode]
      ,[zone].[StoreyBelow]
      ,[zone].[StoreyAbove]
      ,[zone].[IsReflective]
      ,[zone].[AirCavityCode]
      ,[zone].[NaturallyVentilated]
      ,[zone].[LinkId]
      ,[zone].[CeilingArea]
      ,[zone].[ConditionedFloorArea]
      ,[zone].[Perimeter]
      ,[zone].[WallThickness]
      ,[zone].[Conductivity]
      ,[zone].[Diffusivity]
      ,[zone].[GroundReflectance]
      ,[zone].[EdgeInsulation]
  FROM [dbo].[RSS_Zone] [zone]
  INNER JOIN [dbo].[RSS_AssessmentComplianceBuilding] [building]   ON [zone].[AssessmentComplianceBuildingId] = [building].[AssessmentComplianceBuildingId]
  INNER JOIN [dbo].[RSS_AssessmentComplianceOption]   [option]     ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
  INNER JOIN [dbo].[RSS_Assessment]                   [assessment] ON [option].[AssessmentId] = [assessment].[AssessmentId]
  WHERE 1=1
    AND [zone].[Deleted] = 0 AND [building].[Deleted] = 0 AND [option].[Deleted] = 0
    AND [assessment].[AssessmentId] = '2e2d900e-9954-4751-8738-275c3a396c66'
  ORDER BY [option].[Description], [building].[Description], [zone].[ZoneNumber]