// Name: bushfireproneservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'bushfireproneservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', bushfireproneservice]);

    function bushfireproneservice(common, config, $http) {

        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'bushfireprone/';

        var service = {
            processDataset: processDataset,
            checkIfBushFireProne: checkIfBushFireProne
        };
            
        return service;

        /** 
         * Runs the extraction process for the Bushfire Prone dataset. Returns a string
         * describing the final state of the process (unless it fails, then see error response).
         * 
         * NOTE: Unlike the Tenure Dataset extractions, this process is mostly manual.
         * It does not automatically download the dataset from the internet. Someone
         * must place the CSV files within the needed directory BEFORE calling this.
         */
        function processDataset(datasetUrl) {

            return $http({
                url: baseUrl + 'ProcessBushFireProneDatasets',
                params: { datasetUrl },
                method: 'POST',
                timeout: 5 * 60 * 1000 // 5 Minute timeout, should be more than enough.
            }).then(
                (response) => { return response.data; },
                (error) => {
                    var msg = "Error processing BushFireProne dataset: " + error;
                    log.logError(msg, error, null, true);
                    throw error;
                });
        }

        /** 
         *  Checks to see if the given lat/lng falls within a Bushfire Prone area. 
         *  Return TRUE or FALSE. If the return value is NULL, something is amiss and don't trust anything.
         */
        function checkIfBushFireProne(lat, lng) {

            return $http({
                url: baseUrl + 'CheckIfBushFireProne',
                params: { lat: lat, lng: lng },
                method: 'GET',
            }).then(success, fail);

            function success(resp) {

                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {

                var msg = "Error checking BushFireProne: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }
    }
})();
