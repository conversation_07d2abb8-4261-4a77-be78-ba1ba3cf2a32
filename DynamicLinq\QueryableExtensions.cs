﻿using CsvHelper;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Reflection;
using DynamicExpression = System.Linq.Dynamic.DynamicExpression;

namespace Kendo.DynamicLinq
{
	public static class QueryableExtensions
	{
        public static DataSourceResult<T> ToDataSourceResult<T>(this IQueryable<T> queryable, int pageSize, int skip, List<string> sort, List<string> filter, List<string> aggregates, string exportType = "")
        {
            if (exportType == "csv") {
                skip = 0;
                pageSize = int.MaxValue;
            }
            List<Sort> iSort = null;
            if (sort != null && sort.Any())
            {
                iSort = new List<Sort>();
                foreach (string st in sort)
                {
                    var parts = st.Split(new string[] {"@@"}, StringSplitOptions.None);
                    Sort sortObj = new Sort() { Field = parts[0], Dir = parts.Length>1 ? parts[1].ToLower() : "asc" };
                    iSort.Add(sortObj);
                }
            }

            Filter thisFilter = null;
            if (filter != null && filter.Any())
            {
                thisFilter = new Filter();
                thisFilter = thisFilter.ConvertFilterString(thisFilter, filter, false);
            }

            List<Aggregator> iAggregate = null;
            if (aggregates != null && aggregates.Any())
            {
                iAggregate = new List<Aggregator>();
                foreach (string ag in aggregates)
                {
                    var parts = ag.Split(new string[] { "@@" }, StringSplitOptions.None);
                    Aggregator agObj = new Aggregator() { Field = parts[0], Aggregate = parts.Length > 1 ? parts[1].ToLower() : "sum" };
                    iAggregate.Add(agObj);
                }
            }

            return ToDataSourceResult(queryable, pageSize, skip, iSort, thisFilter, iAggregate, exportType);
        }

        /// <summary>
        /// Applies data processing (paging, sorting, filtering and aggregates) over IQueryable using Dynamic Linq.
        /// </summary>
        /// <typeparam name="T">The type of the IQueryable.</typeparam>
        /// <param name="queryable">The IQueryable which should be processed.</param>
        /// <param name="take">Specifies how many items to take. Configurable via the pageSize setting of the Kendo DataSource.</param>
        /// <param name="skip">Specifies how many items to skip.</param>
        /// <param name="sort">Specifies the current sort order.</param>
        /// <param name="filter">Specifies the current filter.</param>
        /// <param name="aggregates">Specifies the current aggregates.</param>
        /// <returns>A DataSourceResult object populated from the processed IQueryable.</returns>
        public static DataSourceResult<T> ToDataSourceResult<T>(this IQueryable<T> queryable, int pageSize, int skip, IEnumerable<Sort> sort, Filter filter, IEnumerable<Aggregator> aggregates, string exportType = "")
		{
			// Filter the data first
			queryable = Filter(queryable, filter);

			// Calculate the total number of records (needed for paging)
			var total = queryable.Count();

			// Calculate the aggregates
			var aggregate = Aggregate(queryable, aggregates);

			// Sort the data
			queryable = Sort(queryable, sort);

			// Finally page the data
			if (pageSize > 0) 
            {
				queryable = Page(queryable, pageSize, skip);
			}

            DataSourceResult<T> result = new DataSourceResult<T>();
            result.Total = total;
            result.Aggregates = aggregate;
            //if no export type then data is passed back in the object
            if (string.IsNullOrEmpty(exportType))
            {
                result.Data = queryable.ToList();
            }
            //otherwise create and save the file according to requested export type
            else
            {
                result.Query = queryable;
                CreateRequestedExportFile(result, exportType);
                result.Query = null;
            }
            return result;
		}

		/// <summary>
		/// Applies data processing (paging, sorting and filtering) over IQueryable using Dynamic Linq.
		/// </summary>
		/// <typeparam name="T">The type of the IQueryable.</typeparam>
		/// <param name="queryable">The IQueryable which should be processed.</param>
		/// <param name="take">Specifies how many items to take. Configurable via the pageSize setting of the Kendo DataSource.</param>
		/// <param name="skip">Specifies how many items to skip.</param>
		/// <param name="sort">Specifies the current sort order.</param>
		/// <param name="filter">Specifies the current filter.</param>
		/// <returns>A DataSourceResult object populated from the processed IQueryable.</returns>
		public static DataSourceResult<T> ToDataSourceResult<T>(this IQueryable<T> queryable, int take, int skip, IEnumerable<Sort> sort, Filter filter)
		{
			return queryable.ToDataSourceResult(take, skip, sort, filter, null);
		}

        /// <summary>
        ///  Applies data processing (paging, sorting and filtering) over IQueryable using Dynamic Linq.
        /// </summary>
        /// <typeparam name="T">The type of the IQueryable.</typeparam>
        /// <param name="queryable">The IQueryable which should be processed.</param>
        /// <param name="request">The DataSourceRequest object containing take, skip, order, and filter data.</param>
        /// <returns>A DataSourceResult object populated from the processed IQueryable.</returns>
	    public static DataSourceResult<T> ToDataSourceResult<T>(this IQueryable<T> queryable, DataSourceRequest request)
	    {
	        return queryable.ToDataSourceResult(request.Take, request.Skip, request.Sort, request.Filter, null);
	    }

		private static IQueryable<T> Filter<T>(IQueryable<T> queryable, Filter filter)
		{
			if (filter != null )
			{
				// Collect a flat list of all filters
				var filters = filter.All();

                // Get all filter values as array (needed by the Where method of Dynamic Linq)
                //var values = filters.Select(f => f.Value).ToArray();
                List<Object> lValues = new List<object>();
                foreach (Filter fil in filters)
                {
                    lValues.Add(fil.Value);
                    lValues.Add(fil.Value2);
                }
                var values = lValues.ToArray();

				// Create a predicate expression e.g. Field1 = @0 And Field2 > @1
				string predicate = filter.ToExpression(filters);

                predicate = predicate.Replace("or)))",")))or");
                predicate = predicate.Replace("and)))", ")))and");

                predicate = predicate.Replace("or))", "))or");
                predicate = predicate.Replace("and))", "))and");

                predicate = predicate.Replace("or)", ")or");
                predicate = predicate.Replace("and)", ")and");

                // Remove any trailing 'and'/'or's. (Can occur on filter removal).
                predicate = predicate.Trim();
                if (predicate.EndsWith("or"))
                    predicate = predicate.Substring(0, predicate.Length - "or".Length);
                if (predicate.EndsWith("and"))
                    predicate = predicate.Substring(0, predicate.Length - "and".Length);
                predicate = predicate.Replace("and)", ")and");

                // Use the Where method of Dynamic Linq to filter the data
                queryable = queryable.Where(predicate, values);
			}

			return queryable;
		}

		private static object Aggregate<T>(IQueryable<T> queryable, IEnumerable<Aggregator> aggregates)
		{
			if (aggregates != null && aggregates.Any())
			{
				var objProps = new Dictionary<DynamicProperty, object>();
				var groups = aggregates.GroupBy(g => g.Field);
				Type type = null;
				foreach (var group in groups)
				{
					var fieldProps = new Dictionary<DynamicProperty, object>();
					foreach (var aggregate in group)
					{
						var val = DoAggregate(queryable, aggregate.Aggregate, aggregate.Field);

						fieldProps.Add(new DynamicProperty(aggregate.Aggregate, typeof(object)), val);
					}
					type = DynamicExpression.CreateClass(fieldProps.Keys);
					var fieldObj = Activator.CreateInstance(type);
					foreach (var p in fieldProps.Keys)
						type.GetProperty(p.Name).SetValue(fieldObj, fieldProps[p], null);
					objProps.Add(new DynamicProperty(group.Key, fieldObj.GetType()), fieldObj);
				}

				type = DynamicExpression.CreateClass(objProps.Keys);

				var obj = Activator.CreateInstance(type);

				foreach (var p in objProps.Keys)
                {
					type.GetProperty(p.Name).SetValue(obj, objProps[p], null);
                }

				return obj;
			}
            else
            {
                return null;
            }
		}

        /// <summary>
        /// Dynamically runs an aggregate function on the IQueryable.
        /// </summary>
        /// <param name="source">The IQueryable data source.</param>
        /// <param name="function">The name of the function to run. Can be Sum, Average, Min, Max.</param>
        /// <param name="member">The name of the property to aggregate over.</param>
        /// <returns>The value of the aggregate function run over the specified property.</returns>
        public static object DoAggregate(this IQueryable source, string function, string member)
        {
            if (source == null) throw new ArgumentNullException("source");
            if (member == null) throw new ArgumentNullException("member");

            function = CultureInfo.InvariantCulture.TextInfo.ToTitleCase(function);

            // Properties
            PropertyInfo property = source.ElementType.GetProperty(member);
            ParameterExpression parameter = Expression.Parameter(source.ElementType, "s");
            Expression selector = Expression.Lambda(Expression.MakeMemberAccess(parameter, property), parameter);
            // We've tried to find an expression of the type Expression<Func<TSource, TAcc>>,
            // which is expressed as ( (TSource s) => s.Price );

            var methods = typeof(Queryable).GetMethods().Where(x => x.Name == function);

            // Method
            MethodInfo aggregateMethod = typeof(Queryable).GetMethods().FirstOrDefault(
                m => m.Name == function
                    && (m.ReturnType == property.PropertyType
                    || (function == "Average" && m.ReturnType == typeof(Double))
                    || (function == "Average" && m.ReturnType == typeof(Nullable<Double>)))
                    && m.IsGenericMethod);

            // Sum, Average
            if (aggregateMethod != null)
            {
                return source.Provider.Execute(
                    Expression.Call(
                        null,
                        aggregateMethod.MakeGenericMethod(new[] { source.ElementType }),
                        new[] { source.Expression, Expression.Quote(selector) }));
            }
            // Min, Max
            else
            {
                aggregateMethod = typeof(Queryable).GetMethods().FirstOrDefault(
                    m => m.Name == function
                        && m.GetGenericArguments().Length == 2
                        && m.IsGenericMethod);

                return source.Provider.Execute(
                    Expression.Call(
                        null,
                        aggregateMethod.MakeGenericMethod(new[] { source.ElementType, property.PropertyType }),
                        new[] { source.Expression, Expression.Quote(selector) }));
            }
        }


        private static IQueryable<T> Sort<T>(IQueryable<T> queryable, IEnumerable<Sort> sort)
		{
			if (sort != null && sort.Any())
			{
				// Create ordering expression e.g. Field1 asc, Field2 desc
				var ordering = String.Join(",", sort.Select(s => s.ToExpression()));

				// Use the OrderBy method of Dynamic Linq to sort the data
				return queryable.OrderBy(ordering);
			}

			return queryable;
		}

		private static IQueryable<T> Page<T>(IQueryable<T> queryable, int pageSize, int skip)
		{
			return queryable.Skip(skip).Take(pageSize);
		}

        //Makes the fie from requested export type
        private static void CreateRequestedExportFile<T>(DataSourceResult<T> result, string exportType)
        {
            switch (exportType) {
                case "csv":
                    result.FileName = CreateCsv<T>(result);
                    break;
            }
            result.Data = null;
        }

        //creates a csv from the given datasource, returns teh name of the file.
        private static string CreateCsv<T>(DataSourceResult<T> result)
        {
            string fileName = Guid.NewGuid().ToString() + ".csv";
            string filePath = System.Web.Hosting.HostingEnvironment.MapPath("~/Temp/" + fileName).ToString();
            using (StreamWriter writeStream = new StreamWriter(new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite)))
            {
                var csv = new CsvWriter(writeStream);
                while (result.NextChunk())
                {
                    var data = result.Data.ToList();
                    csv.WriteRecords(data);
                }
            }
            return fileName;
        }
    }
}
