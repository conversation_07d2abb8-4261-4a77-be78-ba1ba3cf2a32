/**
* Contains methods required to calculate certain values for the NCC 2019 glazing calculator. This basically
* corresponds to V1 of AB-Sim, with modifications as required to work with the admin portals data structures.
*/
(function () {
    'use strict';
    var serviceId = 'certification2019service';
    angular.module('appservices').factory(serviceId, ['common', 'config', '$http', 'zoneservice', certification2019service]);

    function certification2019service(common, config, $http, zoneservice) {

        var service = {
            calculateFloorAreas: calculateFloorAreas,
            calculateDirectAreaV2: calculateDirectAreaV2,
            calculateSuspendedAreaV2: calculateSuspendedAreaV2,
            determineAvailableInsulationOptions: determineAvailableInsulationOptions,
        };
            
        return service;
        
        
        function calculateFloorAreas(storeys, zones, surfaces) {

            // "Flatten" surfaces into all child elements
            surfaces = surfaces.map(x => x.elements).reduce((a, b) => [...a, ...b]);
                
            // Loop over each storey. For each zone within that storey, calculate the areas.
            // The storeys total will be the total of the zones within the storey. For NCC 2019 we want to keep this
            // information seperate.

            const storeyDetails = [];

            storeys.forEach(storey => {

                // Filter by activity codes we care about (Essentially, habitable ones)
                const zonesInStorey = zones.filter(zone => zone.storey === storey.floor);
                const liveableZones = zonesInStorey.filter(zone => zone.zoneType.zoneTypeCode === "ZTHabitableRoom");
                    
                let directTotal = 0;
                let suspendedTotal = 0;
                let airMovementOutcome = zoneservice.calculateAirMovementOutcomeForZones(liveableZones);
                    
                if(airMovementOutcome.contains("x Std"))
                    airMovementOutcome = airMovementOutcome.slice(0, 4);

                zonesInStorey.forEach(zone => {

                    const constructionsInZone = surfaces.filter(x => x.parentZoneId === zone.linkId);

                    const direct    = this.calculateDirectAreaV2(zone, constructionsInZone);
                    const suspended = this.calculateSuspendedAreaV2(zone, constructionsInZone);

                    directTotal += direct;
                    suspendedTotal += suspended;

                });
                    
                storeyDetails.push({
                    ...storey,
                    direct: directTotal,
                    suspended: suspendedTotal,
                    airMovement: airMovementOutcome,
                })

            });

            return storeyDetails;

        }

        /// <summary>
        /// Direct Contact = sum of area for all Exterior Floor (Connected to Ground)
        /// <summary>
        function calculateDirectAreaV2(zone, constructionsInZone) {
            const sum = constructionsInZone
                ?.filter(x => x.category.constructionCategoryCode === "GroundFloor")
                ?.map(x => x.netArea)
                ?.reduce((a, b) => a + b, 0);

            return sum ?? 0;
        }

        /// <summary>
        /// Suspended = sum of area for all Exterior Floor (Suspended)
        /// </summary>
        function calculateSuspendedAreaV2(zone, constructionsInZone) {

            // a.For every zone with Zone Activity = Kitchen / Living, Living, Day Time, Bedroom, Night Time, Unconditioned, Garage or Garage Conditioned, SUM the area values for ALL of the following:
            //    i.floors with construction code 191 - 240;
            const sum = constructionsInZone
                ?.filter(x => x.category.constructionCategoryCode === "ExteriorFloor" ||
                    x.category.constructionCategoryCode === "ExteriorFloorElevated" ||
                    x.category.constructionCategoryCode === "IntermediateFloor" ||
                    x.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow")
                ?.map(x => x.netArea)
                ?.reduce((a, b) => a + b, 0) ?? 0;

            return sum;
        }

        function determineAvailableInsulationOptions(climateZone) {
                
            const availableOptions = [];
                
            if (climateZone == null)
                return [];

            availableOptions.push("No wall insulation concession used");
        
            if (climateZone.description === "1")
                availableOptions.push("Table 3.12.1.3b Climate zone 1 Option (a)(ii)");
        
            if(climateZone.description === "2")
                availableOptions.push("Table 3.12.1.3b Climate zone 2 Option (a)(ii)");
        
            if(climateZone.description === "3")
                availableOptions.push("Table 3.12.1.3b Climate zone 3 Option (a)(ii)");
        
            if(climateZone.description === "4")
            {
                availableOptions.push("Table 3.12.1.3b Climate zone 4 Option (a)(i)");
                availableOptions.push("Table 3.12.1.3b Climate zone 4 Option (b)");
            }
        
            if(climateZone.description === "5")
            {
                availableOptions.push("Table 3.12.1.3b Climate zone 5 Option (a)(ii)");
                availableOptions.push("Table 3.12.1.3b Climate zone 5 Option (b)(ii)");
            }
        
            if(climateZone.description === "6")
            {
                availableOptions.push("Table 3.12.1.3b Climate zone 6 Option (a)(i)");
                availableOptions.push("Table 3.12.1.3b Climate zone 6 Option (b)");
            }
        
            if(climateZone.description === "7")
            {
                availableOptions.push("Table 3.12.1.3b Climate zone 7 Option (a)(i)");
                availableOptions.push("Table 3.12.1.3b Climate zone 7 Option (a)(i)");
            }
        
            // ClimateZone 8 only has the "no wall insulation concession used" option.
            return availableOptions;
        
        }

    }
})();
