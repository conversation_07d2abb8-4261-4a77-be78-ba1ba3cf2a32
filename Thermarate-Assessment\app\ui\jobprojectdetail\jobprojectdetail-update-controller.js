(function () {
    // The JobprojectdetailUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'JobprojectdetailUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'jobservice', 'lottypeservice', 'projectdescriptionservice', 'jobprojectdetailservice', jobprojectdetailUpdateController]);
function jobprojectdetailUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  jobservice, lottypeservice, projectdescriptionservice, jobprojectdetailservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Job Project Detail';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.jobId = null;
        vm.jobprojectdetail = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Job Project Detail";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.jobId = $scope.jobId;
            }
            vm.hideActionBar = true;
        } else {
            vm.jobId = $stateParams.jobId;
        }

        // Get data for object to display on page
        var jobIdPromise = null;
        if (vm.jobId != null) {
            jobIdPromise = jobprojectdetailservice.getJobProjectDetail(vm.jobId)
            .then(function (data) {
                if (data != null) {
                    vm.jobprojectdetail = data;
                }
                loadMap();
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.lotTypeList = [];
        var lotTypePromise = lottypeservice.getList()
            .then(function(data){
                vm.lotTypeList = data.data;
            });
        vm.projectDescriptionList = [];
        var projectDescriptionPromise = projectdescriptionservice.getList()
            .then(function(data){
                vm.projectDescriptionList = data.data;
            });

        // Functions to get data for Typeahead
        vm.getjobs = function(searchTerm) {
            var filter = [{ field: "clientJobNumber", operator: "startswith", value: searchTerm }];
            return jobservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateJob', function(event){
            event.stopPropagation();
            vm.createJob() // function to launch add modal;
            }));

        vm.createJob = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("jobprojectdetail-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                jobprojectdetailservice.createJobProjectDetail(vm.jobprojectdetail).then(function(data){
                    vm.jobprojectdetail = data;
                    vm.jobId = vm.jobprojectdetail.jobId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                jobprojectdetailservice.updateJobProjectDetail(vm.jobprojectdetail).then(function(data){
                    if (data != null) {
                        vm.jobprojectdetail = data;
                        vm.jobId = vm.jobprojectdetail.jobId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            jobprojectdetailservice.deleteJobProjectDetail(vm.jobId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            jobprojectdetailservice.undoDeleteJobProjectDetail(vm.jobId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        //Setup a Google Map to display items location.
        function loadMap() {
            //create the map and add it to the div
            var latitude = vm.jobprojectdetail.latitude;
            var longitude = vm.jobprojectdetail.longitude;
            if (latitude == null && longitude == null) {
                // No lat and lon so don't try to display map.
                return; 
            }

            if (latitude > 90) {
                // Wrong way around so flip.
                var tempLat = latitude;
                var tempLong = longitude;
                latitude = tempLong;
                longitude = tempLat;
            }
            var coordinates = { lat: latitude, lng: longitude };
            var mapOptions = {
                zoom: 12,
                center: coordinates,
                streetViewControl: true,
            }
            vm.map = new google.maps.Map(document.getElementById('location-map'), mapOptions);

            //Create and add a marker for the location on the map
            var title = "";
            var marker = new google.maps.Marker({
                position: coordinates,
                map: vm.map,
                title: title + ": " + latitude + ", " + longitude,
                animation: google.maps.Animation.DROP,
            });
        }

    }
})();