<form name="WersLinkFilesForm"
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='WholeOfHomeCalculatorCtrl as vm'>

    <div class="widget" ng-cloak>

        <div data-cc-widget-header data-title="{{vm.title}}" />

        <md-card>

            <md-card-content style="padding-top: 22px; padding-bottom: 0;">

                <div>

                    <!--  Project Description  -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Project Description</label>
                        <input type="text" name="projectDescription"
                                ng-model="vm.defaults.projectDescription"
                                ng-maxlength="100"
                        />
                    </md-input-container>

                    <!-- NCC Building Classification -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>NCC Building Classification</label>
                        <md-select ng-model="vm.defaults.nccBuildingClassification">
                            <md-option ng-repeat="v in vm.constants.nccBuildingClassifications"
                                        ng-value="v"
                                        ng-click="vm.recalculateEnergyUsageData()">
                                {{v}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Heating System -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Heating System</label>
                        <md-select md-container-class="md-select-show-all"
                                   ng-model="vm.defaults.spaceHeatingServiceTypeCode">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']"
                                       ng-value="v.serviceTypeCode">
                                {{v.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Energy Rating (GEMS 2019) -->
                    <md-input-container ng-if="vm.showEnergyRating({ serviceType: { serviceTypeCode: vm.defaults.spaceHeatingServiceTypeCode }})"
                                        class="md-block vertically-condensed">
                        <label>
                            {{vm.defaults.spaceHeatingServiceTypeCode == 'HeatPumpDucted' || vm.defaults.spaceHeatingServiceTypeCode == 'HeatPumpNonDucted' 
                                ? 'Heating System Energy Rating (GEMS 2019)'
                                : 'Heating System Energy Rating'}}
                        </label>
                        <input ng-model="vm.defaults.spaceHeatingGems2019Rating"
                               formatted-number
                               decimals="1"/>
                    </md-input-container>

                </div>

                <div>

                    <!-- Cooling System -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Cooling System</label>
                        <md-select ng-model="vm.defaults.spaceCoolingServiceTypeCode"
                                   md-container-class="md-select-show-all">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']"
                                       ng-value="v.serviceTypeCode">
                                {{v.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Energy Rating (GEMS 2019) -->
                    <md-input-container ng-if="vm.showEnergyRating({ serviceType: { serviceTypeCode: vm.defaults.spaceCoolingServiceTypeCode }})"
                                        class="md-block vertically-condensed">
                        <label>
                            {{vm.defaults.spaceCoolingServiceTypeCode == 'HeatPumpDucted' || vm.defaults.spaceCoolingServiceTypeCode == 'HeatPumpNonDucted' 
                                ? 'Cooling System Energy Rating (GEMS 2019)'
                                : 'Cooling System Energy Rating'}}
                        </label>
                        <input ng-model="vm.defaults.spaceCoolingGems2019Rating"
                               formatted-number
                               decimals="1"/>
                    </md-input-container>

                </div>

                <div>

                    <!-- Water Heater Type -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Water Heater Type</label>
                        <md-select ng-model="vm.defaults.waterHeatingServiceTypeCode"
                                   md-container-class="md-select-show-all">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']"
                                       ng-value="v.serviceTypeCode">
                                {{v.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                </div>

                <div>

                    <!-- Swimming Pool -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Swimming Pool</label>
                        <md-select ng-model="vm.defaults.swimmingPoolExists">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Swimming Pool Volume (L) -->
                    <md-input-container ng-if="vm.defaults.swimmingPoolExists === true"
                                        class="md-block vertically-condensed">
                        <label>Swimming Pool Volume (L)</label>
                        <input ng-model="vm.defaults.swimmingPoolVolume"
                               formatted-number
                               decimals="0"/>
                    </md-input-container>

                    <!-- Pool Pump Energy Rating -->
                    <md-input-container ng-if="vm.defaults.swimmingPoolExists === true"
                                        class="md-block vertically-condensed">
                        <label>Pool Pump Energy Rating</label>
                        <input ng-model="vm.defaults.swimmingPoolGems2019Rating"
                               formatted-number
                               decimals="1"/>
                    </md-input-container>

                </div>

                <div>

                    <!-- Spa -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Spa</label>
                        <md-select ng-model="vm.defaults.spaExists">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Spa Volume (L) -->
                    <md-input-container ng-if="vm.defaults.spaExists === true"
                                        class="md-block vertically-condensed">
                        <label>Spa Volume (L)</label>
                        <input ng-model="vm.defaults.spaVolume"
                               formatted-number
                               decimals="0"/>
                    </md-input-container>

                    <!-- Spa Pump Energy Rating -->
                    <md-input-container ng-if="vm.defaults.spaExists === true"
                                        class="md-block vertically-condensed">
                        <label>Spa Pump Energy Rating</label>
                        <input ng-model="vm.defaults.spaGems2019Rating"
                               formatted-number
                               decimals="1"/>
                    </md-input-container>

                </div>

                <div>

                    <!-- Spa -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Photovoltaic (PV) System</label>
                        <md-select ng-model="vm.defaults.photovoltaicExists">
                            <md-option ng-value="null">No Default</md-option>
                            <md-option ng-value="true">
                                Yes
                            </md-option>
                            <md-option ng-value="false">
                                No
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Photovoltaic (PV) System Capacity (kW) -->
                    <md-input-container ng-if="vm.defaults.photovoltaicExists === true"
                                        class="md-block vertically-condensed">
                        <label>Photovoltaic (PV) System Capacity (kW)</label>
                        <input ng-model="vm.defaults.photovoltaicCapacity"
                               formatted-number
                               decimals="2"/>
                    </md-input-container>

                </div>

            </md-card-content>

        </md-card>

        <div data-cc-widget-button-bar
                data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised md-primary"
                        ng-disabled="floorHeightform.$invalid || vm.editPermission == false"
                        ng-show="vm.floorHeight.deleted!=true"
                        ng-click="vm.save()">
                Save
            </md-button>
            <div class="clearfix"></div>
        </div>

    </div>

</form>
<style>
    .wers-link-file-upload {
        max-width: 700px;
    }
</style>