<div class="widget">

    <div class="table-responsive-vertical shadow-z-1">
      <table class="table table-striped table-hover table-very-condensed table-data-centered"
             st-table="vm.modelVariationList"
             st-table-filtered-list="exportList"
             st-global-search="vm.listFilter"
             st-pipe="vm.callServer">

        <!-- Headings -->
        <thead>
            <tr>
              <th style="width: 40px;">
                <div style="display: grid; justify-content: center;">
                <md-checkbox id="sm-allCheckbox"
                             style="margin: auto; text-align: center; width: 0;"
                             ng-model="vm.bulkStatus.selectAllCheckboxState"
                             md-indeterminate="vm.bulkStatus.isIndeterminate"
                             ng-click="vm.selectAllCheckboxes(vm.modelVariationList, vm.bulkStatus.selectAllCheckboxState, vm.bulkStatus)">
                </md-checkbox>
                </div>
              </th>
              <th st-sort="title" class="clickable" style="user-select:none">Description</th>
              <th>Active</th>
              <th>3D Model</th>
              <th>Cost</th>
              <th>Design Insights</th>
              <th ng-if="vm.variationOptionsSettings.floorplanIsActive" st-sort="variationFloorplanName" class="clickable" style="user-select:none">Floor Plan</th>
              <th ng-if="vm.variationOptionsSettings.designOptionIsActive" st-sort="variationDesignOptionName" class="clickable" style="user-select:none">Design Option</th>
              <th ng-if="vm.variationOptionsSettings.facadeIsActive" st-sort="variationFacadeName" class="clickable" style="user-select:none">Facade</th>
              <th ng-if="vm.variationOptionsSettings.specificationIsActive" st-sort="variationSpecificationName" class="clickable" style="user-select:none">Specification</th>
              <th ng-if="vm.variationOptionsSettings.configurationIsActive" st-sort="variationConfigurationName" class="clickable" style="user-select:none">Configuration</th>
              <th st-sort="createdOn" class="clickable" style="user-select:none">Date Created</th>
              <th st-sort="modifiedOn" class="clickable" style="user-select:none">Date Modified</th>
              <th>Default</th>
              <th style="width: 50px;"></th>
            </tr>
        </thead>

        <!-- Data -->
        <tbody>
            <tr ng-repeat="modelVariation in vm.modelVariationList track by $index" class="list-row">

                <!-- Checkbox -->
                <td>
                    <div style="display: grid; justify-content: center;">
                        <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                     ng-model="modelVariation.checkboxSelected"
                                     ng-change="vm.updateBulkSelectStatus(vm.modelVariationList, vm.bulkStatus);">
                        </md-checkbox>
                    </div>
                </td>
                <!-- Description -->
                <td ng-click="vm.rowClickCallback({variationId: modelVariation.standardHomeModelId})">
                    <md-input-container class="md-block" style="display: flex; align-items: center;">
                        <input type="text" ng-model="modelVariation.title" ng-click="$event.stopPropagation()" />
                        <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                    </md-input-container>
                </td>
                <!-- Active -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="modelVariation.isActive" ng-disabled="!vm.parentIsActive">
                        </md-switch>
                    </div>
                </td>
                <!-- 3D Model -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="modelVariation.view3dFloorPlans" ng-disabled="!vm.parent3dModel">
                        </md-switch>
                    </div>
                </td>
                <!-- Cost Estimate -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="modelVariation.costEstimateEnabled" ng-disabled="!vm.parentCostEstimate">
                        </md-switch>
                    </div>
                </td>
                <!-- Design Insights -->
                <td>
                    <div style="display: grid; justify-items: center;">
                        <md-switch ng-model="modelVariation.variableMetadata.designInsightsEnabled" ng-disabled="!vm.parentDesignInsights">
                        </md-switch>
                    </div>
                </td>
                <!-- Floorplan -->
                <td ng-if="vm.variationOptionsSettings.floorplanIsActive">
                    <md-select ng-required="true" style="width: 100%; text-align: left;"
                               class="variation-dropdown md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                               ng-model="modelVariation.variationFloorplanId">
                        <md-option ng-repeat="option in vm.getAvailableFloorplanOptions(modelVariation) track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                    </md-select>
                </td>
                <!-- Design Option -->
                <td ng-if="vm.variationOptionsSettings.designOptionIsActive">
                    <md-select ng-required="true" style="width: 100%; text-align: left;"
                               class="variation-dropdown md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                               ng-model="modelVariation.variationDesignOptionId">
                        <md-option ng-repeat="option in vm.getAvailableDesignOptionOptions(modelVariation) track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                    </md-select>
                </td>
                <!-- Facade -->
                <td ng-if="vm.variationOptionsSettings.facadeIsActive">
                    <md-select ng-required="true" style="width: 100%; text-align: left;"
                               class="variation-dropdown md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                               ng-model="modelVariation.variationFacadeId">
                        <md-option ng-repeat="option in vm.getAvailableFacadeOptions(modelVariation) track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                    </md-select>
                </td>
                <!-- Specification -->
                <td ng-if="vm.variationOptionsSettings.specificationIsActive" style="width:max-content;">
                    <md-select ng-required="true" style="width: 100%; text-align: left;"
                               class="variation-dropdown md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                               ng-model="modelVariation.variationSpecificationId">
                        <md-option ng-repeat="option in vm.getAvailableSpecificationOptions(modelVariation) track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                    </md-select>
                </td>
                <!-- Configuration -->
                <td ng-if="vm.variationOptionsSettings.configurationIsActive">
                    <md-select ng-required="true" style="width: 100%; text-align: left;"
                               class="variation-dropdown md-block vertically-condensed vertically-condensed-ex kindly-remove-error-spacer"
                               ng-model="modelVariation.variationConfigurationId">
                        <md-option ng-repeat="option in vm.getAvailableConfigurationOptions(modelVariation) track by option.standardHomeModelVariationOptionId" ng-value="'{{option.standardHomeModelVariationOptionId}}'">{{option.optionName}}</md-option>
                    </md-select>
                </td>
                <!-- Date Created -->
                <td ng-click="vm.rowClickCallback({variationId: tempId})" class="clickable">{{modelVariation.createdOn != null ? (modelVariation.createdOn | date: 'dd/MM/yyyy') : '-'}}</td>
                <!-- Date Modified -->
                <td ng-click="vm.rowClickCallback({variationId: tempId})" class="clickable">{{modelVariation.modifiedOn != null ? (modelVariation.modifiedOn | date: 'dd/MM/yyyy') : '-'}}</td>
                <!-- Default Checkbox -->
                <td>
                    <div style="display: grid; justify-content: center;">
                        <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                     ng-model="modelVariation.isDefaultVariation"
                                     ng-change="vm.defaultVariationChanged(modelVariation);">
                        </md-checkbox>
                    </div>
                </td>
                <!-- Menu -->
                <td>
                    <md-menu style="margin-top:5px;">
                        <img md-menu-origin
                             class="clickable"
                             ng-click="$mdOpenMenu()"
                             src="/content/feather/more-horizontal.svg"/>
                        <md-menu-content>
                            <!-- Duplicate -->
                            <md-menu-item><md-button ng-click="vm.duplicate(modelVariation)">
                                Duplicate
                            </md-button></md-menu-item>
                            <!-- Move Up -->
                            <md-menu-item ng-show="$index > 0 && vm.sortField == null"><md-button ng-click="vm.moveVariationUp(modelVariation)">
                                Move Up
                            </md-button></md-menu-item>
                            <!-- Move Down -->
                            <md-menu-item ng-show="$index < vm.modelVariationList.length-1 && vm.sortField == null"><md-button ng-click="vm.moveVariationDown(modelVariation)">
                                Move Down
                            </md-button></md-menu-item>
                            <md-menu-divider></md-menu-divider>
                            <!-- Delete -->
                            <md-menu-item><md-button ng-click="vm.delete(modelVariation)">
                                <span style="color: orangered;">Delete</span>
                            </md-button></md-menu-item>
                        </md-menu-content>
                    </md-menu>
                </td>
            </tr>
        </tbody>

        <!-- Buttons -->
        <tfoot>
            <tr>
              <td colspan="9999" class="text-center">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; background-color: rgb(250,250,250);">

                  <!-- Just empty. -->
                  <div></div>

                  <!-- Pagination Display -->
                  <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                </div>
              </td>
            </tr>
        </tfoot>
      </table>
      <div class="widget-pager" style="text-align: center;">
        <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
      </div>

      <div style="display: flex; justify-content: space-between; align-items: center;">

        <md-button class="md-raised md-primary"
                    ng-click="vm.launchBulkEditModal()"
                    ng-disabled="!(vm.bulkStatus.selectAllCheckboxState || vm.bulkStatus.isIndeterminate)">
            BULK EDIT
        </md-button>

        <md-button class="md-raised md-primary"
                   redi-enable-roles="settings__settings__create"
                   ng-show="!vm.disabled"
                   style="margin: 2rem 1.5rem;"
                   ng-click="vm.addVariation()">
          Add VARIATION
        </md-button>
      </div>

      <div>

      </div>
    </div>
    <div class="widget-foot">
      <div class="clearfix"></div>
    </div>
  </div>

<style>

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: relative;
        margin: 0 10px;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }
    .go-to-variation-button:hover {
        background-color: #d1d1d1;
    }
        .go-to-variation-button > img {
            position: absolute;
            top: 50%; left: 54%; transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

    /* Dropdowns */
    .variation-dropdown span:first-child {
        padding-left: 5px;
        text-align: center;
    }
    .variation-dropdown span:last-child {
        width: max-content !important;
        margin-left: 0;
    }

</style>