<div data-ng-controller='AssessmentdrawingStampCtrl as vm' 
     style="transform: scale({{vm.currentPageSize().scale}}) !important; transition: 0.4s;">

    <md-dialog aria-label="Assessment Drawing Stamp"
               style="min-width: max-content;">

        <form name="assessmentdrawingstampform"
              class="main-content-wrapper"
              novalidate >

            <!-- Header -->
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>{{vm.title}}</h2><h2 ng-show="vm.hasList">&nbsp;&ndash; {{vm.slideIndex + 1}}/{{vm.displayList.length}}</h2> <h2 style="padding-left: 10px;" ng-show="vm.drawingDescription!=null">"{{vm.drawingDescription}}"</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="vm.cancel()">
                        <i class="material-icons">clear</i>
                    </md-button>
                </div>
            </md-toolbar>

            <!-- Toolbar -->
            <md-toolbar>
                <div layout="row" style="height: 77px; width:100%; padding-top:30px; padding-left: 20px; background-color:#fff; border-bottom: 1px solid rgba(0,0,0,0.12);"
                     ng-if="vm.hasList" class="stamp-drawing-detail">
                    <md-input-container class="md-block" flex style="margin-right:8px;">
                        <label>Description</label>
                        <input type="text" name="WDDrawingDescription{{vm.slideIndex}}"
                               ng-model="vm.displayList[vm.slideIndex].drawingDescription" />
                        <!--<div ng-messages="assessmentdrawingstampform['WDDrawingDescription'+vm.slideIndex].$error">-->
                        <!--</div>-->
                    </md-input-container>
                    <md-input-container class="md-block" style="max-width: 80px;margin-right:8px;">
                        <label>Size</label>
                        <md-select name="pageSize"
                                   ng-required
                                   ng-model="vm.displayList[vm.slideIndex].pageSize">
                            <md-option ng-value="item.id"
                                       ng-repeat="item in vm.pageSizeList track by item.id">
                                {{item.description}}
                            </md-option>
                        </md-select>
                    </md-input-container>
                    <md-input-container class="md-block" style="max-width: 100px;margin-right:8px;">
                        <label>Sheet No.</label>
                        <input type="text"
                               class="center-input"
                               name="WDSheetNumber{{vm.slideIndex}}"
                               ng-model="vm.displayList[vm.slideIndex].sheetNumber" />
                    </md-input-container>
                    <md-input-container class="md-block" style="max-width: 80px;margin-right:8px;">
                        <label>Revision</label>
                        <input type="text"
                               class="center-input"
                               name="WDRevision{{vm.slideIndex}}"
                               ng-model="vm.displayList[vm.slideIndex].revision" />
                    </md-input-container>
                    <md-input-container class="md-block" style="margin-right:8px;">
                        <label>Revision Date</label>
                        <md-datepicker ng-model="vm.displayList[vm.slideIndex].revisionDate"
                                       md-hide-icons="calendar"
                                       md-placeholder="Enter date"></md-datepicker>
                    </md-input-container>
                    <md-input-container class="md-block" style="max-width: 80px;margin-right:8px;">
                        <label>Stamp</label>
                        <md-select name="pageSize"
                                   ng-required
                                   ng-model="vm.displayList[vm.slideIndex].toStamp"
                                   ng-disabled="!vm.clientOptions.stampDrawings || !vm.displayList[vm.slideIndex].isIncludedInReport">
                            <md-option ng-value="false">No</md-option>
                            <md-option ng-value="true">{{vm.clientOptions.stampDrawings ? 'Yes' : 'No'}}</md-option> <!-- IF 'StampDrawings' is OFF on Client, make appear that 'No' is selected, but we don't want to actually set to 'No' in case the option at Client is turned back on then it can revert to previous selections -->
                        </md-select>
                    </md-input-container>

                    <!-- Include in report toggle -->
                    <div flex layout-align="center center" layout="row" style="max-width: 50px; margin-bottom: 5px;">
                        <button class="feather-icon-button"
                                ng-click="vm.toggleIncludeInReport()">
                            <img ng-show="vm.displayList[vm.slideIndex].isIncludedInReport && vm.clientOptions.includeDrawingsInReport"
                                 src="/content/images/link.png"
                                 style="width: 26px; height: auto;" ng-style="{ 'cursor': vm.clientOptions.includeDrawingsInReport ? 'pointer' : 'default' }" />
                            <img ng-show="vm.displayList[vm.slideIndex].isIncludedInReport == false || !vm.clientOptions.includeDrawingsInReport"
                                 src="/content/images/link-unlink.png"
                                 style="width: 26px; height: auto;" ng-style="{ 'cursor': vm.clientOptions.includeDrawingsInReport ? 'pointer' : 'default' }" />
                        </button>
                    </div>

                    <!-- Include in report toggle -->
                    <div flex layout-align="center center" layout="row" style="max-width: 50px; margin-bottom: 5px;">
                        <button class="feather-icon-button"
                                ng-click="vm.displayList[vm.slideIndex].isShownToClient = !vm.displayList[vm.slideIndex].isShownToClient">
                            <img ng-show="vm.displayList[vm.slideIndex].isShownToClient"
                                 src="/content/images/eye-open.png"
                                 style="width: 26px; height: auto;" />
                            <img ng-show="vm.displayList[vm.slideIndex].isShownToClient == false"
                                 src="/content/images/eye-shut.png"
                                 style="width: 26px; height: auto; transform: scaleX(-1);" />
                        </button>
                    </div>

                    <!-- Delete -->
                    <div flex layout-align="center center" layout="row" style="max-width: 50px; margin-bottom: 5px;">
                        <button class="feather-icon-button"
                                ng-click="vm.deleteItem(vm.slideIndex)">
                            <img src="/content/feather/trash.svg" />
                        </button>
                    </div>

                </div>
                <div style="height: 15px; background-color: #fff;"/>
            </md-toolbar>

            <!-- Body -->
            <md-dialog-content>

                <!-- Processing Overlay -->
                <div ng-if="vm.isBusy== true"
                     class="busy-processing-overlay">
                    <div>
                        <md-progress-circular md-mode="indeterminate"></md-progress-circular>
                    </div>
                </div>

                <!-- Error Display -->
                <div style="padding-top: 10px; padding-bottom: 10px;" ng-if="vm.hasList==false && vm.isBusy == false">
                    <span style="display:table; margin:0 auto;">No stampable drawings included in assessment.</span>
                </div>

                <!-- Drawing + Stamp body image etc -->
                <div ng-style="{ 'width': vm.maxAreaWidth, 'height': vm.maxAreaHeight, 'transform': 'scale(' + vm.currentPageSize().scale - 0.05 + ')' }" class="slideshow-container" style="overflow:hidden;" ng-if="vm.hasList" ng-mouseup="vm.mydragg.stopMoving('container');" >
                    <div class="drawingSlide fade"
                         ng-init="vm.initialiseImage()">
                        <img id="drawnImage"
                             ng-dblclick="vm.setStampToHere($event.offsetX, $event.offsetY)"
                             class="drawingImage" ng-src="{{vm.currentUrl}}" image-on-load="vm.calculateImageDivWidth"/>
                    </div>
                    <div id="container" ng-show="vm.isImageLoaded && vm.displayList[vm.slideIndex].toStamp">
                        <div id="elem" ng-mousedown="vm.mydragg.startMoving('elem', 'container', event);" ng-style="{'width': vm.stampWidth, 'height': vm.stampHeight, 'left': vm.stampX, 'top': vm.stampY}">
                            <img src="/app/img/stamp-example.svg" class="static-non-interactive" />

                            <!-- Inline Scale buttons that are layed over the stamp. -->
                            <md-button class="md-fab resize-button"
                                       ng-click="vm.scale(1 / vm.scaleIncrement)">
                                <i class="fa fa-1x fa-minus"
                                   style="vertical-align:middle;"
                                   aria-hidden="true">
                                </i>
                            </md-button>
                            <md-button class="md-fab resize-button"
                                       ng-click="vm.scale(vm.scaleIncrement)">
                                <i class="fa fa-1x fa-plus"
                                   style="vertical-align:middle;"
                                   aria-hidden="true">
                                </i>
                            </md-button>
                        </div>
                    </div>
                </div>

                <md-button class="prev btn-floating" ng-show="vm.isImageLoaded" ng-click="vm.slideIndexPrev()"><i class="material-icons leftShadow" style="font-size: 60px">skip_previous</i></md-button>
                <md-button class="next btn-floating" ng-show="vm.isImageLoaded" ng-click="vm.slideIndexNext()"><i class="material-icons rightShadow" style="font-size: 60px">skip_next</i></md-button>
            </md-dialog-content>

            <md-dialog-actions layout="row"
                               layout-align="space-between center"
                               style="padding-left: 8px;">

                <div style="margin-right: auto;">
                    <md-button class="md-raised"
                               style="margin-left: 0px;"
                               ng-click="vm.resetToDefault();">
                        Reset Default
                    </md-button>
                    <!--                <md-checkbox class="checkbox-aligner"-->
                    <!--                             style="margin-left: 10px;"-->
                    <!--                             ng-model="vm.autostampUseDefaultStampSize">-->
                    <!--                    Ignore Sheet Size-->
                    <!--                </md-checkbox>-->
                </div>

                <div layout-align="end center" style="margin-left: auto;" layout>
                    <md-button class="md-raised no-line-height" ng-click="vm.rotate('right')"><i class="fa fa-2x fa-undo" aria-hidden="true"></i></md-button>
                    <md-button class="md-raised no-line-height" ng-click="vm.rotate('left')"><i class="fa fa-2x fa-repeat" aria-hidden="true"></i></md-button>
                </div>
                <div layout-align="end center" layout>
                    <md-button class="md-raised md-primary" ng-disabled="assessmentdrawingstampform.$invalid || (vm.hasList!=true&&(!vm.displayList||vm.displayList.length==0))" ng-show="vm.assessment.deleted!=true" ng-click="vm.save()">Save</md-button>
                    <md-button class="md-raised" ng-click="vm.cancel()">Close</md-button>
                </div>
            </md-dialog-actions>
        </form>
    </md-dialog>
</div>

<style>
    .drawingSlides {
        display: block;
        pointer-events: none;
    }

    .slideshow-container {
        position: relative;
        margin: auto;
    }

    .btn-floating {
        display: inline-block;
        text-align: center;
        cursor: pointer;
        width: auto;
        height: auto;
        color: rgb(139, 195, 74);
    }

    .prev {
        position: absolute;
        top: 48%;
        left: 0%;
    }

    .next {
        position: absolute;
        top: 48%;
        right: 0%;
    }

    .prev::after hover, .next::after hover {
        background-color: rgb(139, 195, 74);
    }

    .active {
        background-color: rgb(139, 195, 74);
    }

    .fade {
        -webkit-animation-name: fade;
        -webkit-animation-duration: 1.5s;
        animation-name: fade;
        animation-duration: 1.5s;
    }

    @-webkit-keyframes fade {
        from {
            opacity: .4;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes fade {
        from {
            opacity: .4;
        }
        to {
            opacity: 1;
        }
    }

    @media only screen and (max-width: 300px) {
        .prev, .next, .text {
            font-size: 11px;
        }
    }

    .drawingImage {
        width: auto;
    }

    #container {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        left: 0px;
    }

    #elem {
        position: absolute;
        -webkit-user-select: none;
        -moz-user-select: none;
        -o-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;     
        user-select: none;
    }

    .rightShadow {
        text-shadow: 1px 1px 0 #000000;
    }

    .leftShadow {
        text-shadow: -1px 1px 0 #000000;
    }

    .static-non-interactive {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0px;
        top: 0px;
        pointer-events: none;
    }
    .stamp-drawing-detail md-input-container {
        margin-top:0;
        margin-bottom: 0;
    }
    .stamp-drawing-detail .md-errors-spacer {
        display: none;
    }
    .short-button {
        line-height:15px;
        min-height: 26px;
    }

    .md-button.md-fab.resize-button {
        line-height: 30px;
        width: 30px;
        height: 30px;
        min-height: 30px;
        background-color: darkgrey;
        margin: 3px;
    }

</style>