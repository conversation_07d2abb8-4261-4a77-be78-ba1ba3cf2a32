(function () {

  'use strict';
  angular
    .module('app')
    .component('standardModelVariationOptions', {
      bindings: {
        theParentModel: '<',    // The 'StandardHomeModel' used as the basis of the option data
        onDataChanged: '&',     // Callback fired when any variable is changed.
        copyAcrossEnabled: '<', // Whether to show Copy Across button
        copyAcrossData: '=?',   // Tell parent which option of which field to set in all buildings
        copyAcrossTrigger: '&', // Trigger Copy Across in parent
        required: '<',          // Specify whether all inputs are required or not
        disabled: '<',          // Specify whether inputs should be disabled
      },
      templateUrl: 'app/ui/energy-labs/standard-model-variation-options-form.html',
      controller: StandardModelVariationOptionsController,
      controllerAs: 'vm'
    });

  StandardModelVariationOptionsController.$inject = ['$scope', '$timeout', 'common', 'standardmodelservice'];

  function StandardModelVariationOptionsController($scope, $timeout, common, standardmodelservice) {

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    let vm = this;
    vm.variationCategories = StandardModelConstants.variationCategories;
    vm.allFieldNames = vm.variationCategories.map(cat => common.firstCharLowerCase(cat)).filter(cat => vm.theParentModel.variationOptionsSettings[`${cat}IsActive`]);

    // -------------- //
    // - INITIALISE - //
    // -------------- //

    function initialise() {
        // Get all of these 3 lists as a list
        vm.theList = [vm.firstHalfDropdowns(), vm.middleDropdown(), vm.secondHalfDropdowns()];
    }

    // ----------- //
    // - HANDLES - //
    // ----------- //

        // Get options based on selections
        vm.getOptionsAndVariation = function (optionChangedName) {
            let currentSelections = { parentHomeModelId: vm.theParentModel.standardHomeModelId };
            vm.allFieldNames.forEach(f => {
                currentSelections[`${f}Id`] = vm.theParentModel.dropdownFields[f].selected?.standardHomeModelVariationOptionId;
            });
            let fieldNamesToGet = vm.allFieldNames;
            // IF just changed option, only get options for dropdowns after the changed dropdown
            if (optionChangedName) {
                fieldNamesToGet = fieldNamesToGet.slice(-(fieldNamesToGet.length - fieldNamesToGet.indexOf(optionChangedName) - 1));
            }
            // List of calls
            let allFetches = fieldNamesToGet.map(f => standardmodelservice.getVariationOptionsForStage(
                `variation${common.firstCharUpperCase(f)}`,
                currentSelections
            ));
            Promise.all(allFetches).then(results => {
                // Set new options
                results.forEach((result, i) => {
                    let fieldName = fieldNamesToGet[i];
                    vm.theParentModel.dropdownFields[fieldName].options = vm.variationOptionsList.filter(o => o.variationCategoryCode == common.firstCharUpperCase(fieldName) && result.data.includes(o.standardHomeModelVariationOptionId));
                    // IF has one option, auto-select this option
                    if (vm.theParentModel.dropdownFields[fieldName].options.length == 1) {
                        vm.theParentModel.dropdownFields[fieldName].selected = vm.theParentModel.dropdownFields[fieldName].options[0];
                    }
                });
                // Check if current selections are still acceptable
                fieldNamesToGet.forEach(fieldName => {
                    let thisField = vm.theParentModel.dropdownFields[fieldName];
                    if (!thisField.options.map(o => o.standardHomeModelVariationOptionId).includes(thisField.selected.standardHomeModelVariationOptionId)) {
                        thisField.selected = null;
                    }
                });
                vm.planViewIndexControl = 0;
                // Get new Variation values
                vm.getVariation();
            });
        }

        // Expand field
        vm.expandField = function (expandingFieldName) {
            document.getElementById(`options-switcher-container-${vm.theParentModel.standardHomeModelId}`)?.focus();
            vm.allFieldNames.forEach(name => {
                if (name != expandingFieldName) {
                    vm.theParentModel.dropdownFields[name].expanded = false;
                    vm.theParentModel.dropdownFields[name].justExpanded = false;
                }
            });
            // IF this was already expanded, set to false (do this way because of timing of "vm.switcherBlur()")
            if (vm.theParentModel.dropdownFields[expandingFieldName].justExpanded) {
                vm.theParentModel.dropdownFields[expandingFieldName].expanded = false;
                vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = false;
            // Expand only if has more than 1 option
            } else if (vm.theParentModel.dropdownFields[expandingFieldName].options.length > 1) {
                vm.theParentModel.dropdownFields[expandingFieldName].expanded = true;
                vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = true;
            }
            // Check if now in options selection mode
            vm.theParentModel.inOptionsSelectionsMode = vm.allFieldNames.some(fieldName => vm.theParentModel.dropdownFields[fieldName].expanded);
        }

        // Collapse field
        vm.collapseField = function (expandingFieldName) {
            vm.theParentModel.dropdownFields[expandingFieldName].expanded = false;
            vm.theParentModel.dropdownFields[expandingFieldName].justExpanded = false;
            vm.theParentModel.inOptionsSelectionsMode = false;
        }

        // Click outside
        vm.switcherBlur = function () {
            vm.allFieldNames.forEach(fieldName => vm.theParentModel.dropdownFields[fieldName].expanded = false);
            vm.theParentModel.inOptionsSelectionsMode = false;
            setTimeout(() => {
                vm.allFieldNames.forEach(fieldName => { vm.theParentModel.dropdownFields[fieldName].justExpanded = false });
            }, 100);
        }

        // Check if any dropdown is open
        vm.anyDropdownOpen = function () {
            return vm.allFieldNames.some(f => vm.theParentModel.dropdownFields[f].expanded);
        }

        // Select option
        vm.selectOption = function (fieldName, option) {
            // Select option
            vm.theParentModel.dropdownFields[fieldName].selected = option;
            // Collapse
            vm.collapseField(fieldName);
            // Get options for dropdowns after this dropdown
            vm.getOptionsAndVariation(fieldName);
        }

    vm.selectionChanged = function () {
        vm.getVariation();
    }

    // Get Variation based selections
    vm.getVariation = function () {
        standardmodelservice.getVariationFromSelections(
            vm.theParentModel.standardHomeModelId,
            vm.theParentModel.dropdownFields['floorplan']?.selected.standardHomeModelVariationOptionId,
            vm.theParentModel.dropdownFields['designOption']?.selected.standardHomeModelVariationOptionId,
            vm.theParentModel.dropdownFields['facade']?.selected.standardHomeModelVariationOptionId,
            vm.theParentModel.dropdownFields['specification']?.selected.standardHomeModelVariationOptionId,
            vm.theParentModel.dropdownFields['configuration']?.selected.standardHomeModelVariationOptionId,
            true
        ).then(data => {
            // Set new Variation
            vm.theParentModel.selectedVariation = data;
            // Trigger parent onChange
            vm.onDataChanged();
        });
    }

    // - BUILD - //

    // Get the first half of dropdowns for column 1/3
    vm.firstHalfDropdowns = function () {
        let list = vm.allFieldNames.slice(0, vm.allFieldNames.length/2);
        return list;
    }

    // Get middle item if odd, otherwise return empty, for column 2/3
    vm.middleDropdown = function () {
        if (vm.allFieldNames.length % 2 == 1) {
            let item = vm.allFieldNames[Number((vm.allFieldNames.length/2).toFixed())-1];
            return [item];
        } else {
            return [];
        }
    }

    // Get the second half of dropdowns for column 3/3
    vm.secondHalfDropdowns = function () {
        if (vm.allFieldNames.length % 2 == 1) {
            let list = vm.allFieldNames.slice((vm.allFieldNames.length/2)+1, vm.allFieldNames.length);
            return list;
        } else {
            let list = vm.allFieldNames.slice(vm.allFieldNames.length/2, vm.allFieldNames.length);
            return list;
        }
    }

    // - RUN INITIALISE - //

    initialise();

  }

})();