<form name="maptouserrolesform" class="main-content-wrapper" novalidate data-ng-controller='MaptouserrolesUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <!--Left Side-->
                <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}" >
                    <md-card>
                        <md-card-header>
                            Map To User Roles
                        </md-card-header>
                        <md-card-content>

<!-- ******** Role ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Role</label>
                                <md-select name="roleCode" md-autofocus 
                                        required
                                        ng-model="vm.maptouserroles.roleCode">
                                    <md-option ng-value="item.roleCode" 
                                            ng-repeat="item in vm.employeeRoleList track by item.roleCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="maptouserrolesform.roleCode.$error">
                                    <div ng-message="required">Role is required.</div>
                                </div>
                            </md-input-container>

<!-- ******** aspnet_ Role ******** -->
                            <md-autocomplete md-input-name="aspnet_RoleId"  
                                         required
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.maptouserroles.${fld.ForeignObjectNameJs}"
                                         md-search-text="vm.aspnet_RoleIdSearchText"
                                         md-items="item in vm.getroless(vm.aspnet_RoleIdSearchText)"
                                         md-item-text="item.roleName"
                                         md-require-match
                                         md-floating-label="aspnet_ Role">
                                <md-item-template>
                                    <span md-highlight-text="vm.aspnet_RoleIdSearchText">{{item.roleName}}</span>
                                </md-item-template>
                                <div ng-messages="maptouserrolesform.aspnet_RoleId.$error">
                                    <div ng-message="required">aspnet_ Role is required.</div>
                                </div>
                            </md-autocomplete>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.maptouserroles"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>

<!-- ******** Right Side ******** -->
            <div ng-if="vm.newRecord==false" flex-gt-sm="50">
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="maptouserrolesform.$invalid" ng-show="vm.maptouserroles.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.maptouserroles.mapId>0 && vm.maptouserroles.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.maptouserroles.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
