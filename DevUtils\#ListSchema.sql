SELECT 
    TABLE_NAME AS TableName,
    COLUMN_NAME AS ColumnName,
    DATA_TYPE AS DataType,
    IS_NULLABLE AS IsNullable,
    CHARACTER_MAXIMUM_LENGTH AS MaxLength,
    CONSTRAINT_NAME AS ForeignKeyName,
    REFERENCED_TABLE_NAME AS ReferencedTable,
    REFERENCED_COLUMN_NAME AS ReferencedColumn
FROM (
    -- Columns
    SELECT 
        t.TABLE_NAME, 
        c.COLUMN_NAME, 
        c.DATA_TYPE, 
        c.IS_NULLABLE, 
        c.CHARACTER_MAXIMUM_LENGTH,
        NULL AS CONSTRAINT_NAME,
        NULL AS REFERENCED_TABLE_NAME,
        NULL AS REFERENCED_COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS c
    JOIN INFORMATION_SCHEMA.TABLES t ON c.TABLE_NAME = t.TABLE_NAME
    WHERE t.TABLE_TYPE = 'BASE TABLE'

    UNION ALL

    -- Foreign keys
    SELECT 
        fk.TABLE_NAME, 
        kc.COLUMN_NAME, 
        NULL AS DATA_TYPE, 
        NULL AS IS_NULLABLE, 
        NULL AS CHARACTER_MAXIMUM_LENGTH,
        fk.CONSTRAINT_NAME, 
        pk.TABLE_NAME AS REFERENCED_TABLE_NAME, 
        pk.COLUMN_NAME AS REFERENCED_COLUMN_NAME
    FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
    JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS fk ON rc.CONSTRAINT_NAME = fk.CONSTRAINT_NAME
    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kc ON fk.CONSTRAINT_NAME = kc.CONSTRAINT_NAME
    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE pk ON rc.UNIQUE_CONSTRAINT_NAME = pk.CONSTRAINT_NAME
) AS CombinedData
ORDER BY TableName, ColumnName;