(function () {
    'use strict';
    var controllerId = 'NotificationRuleUpdateCtrl';
    angular.module('app').controller(
        controllerId,
        ['$rootScope', '$scope', '$mdDialog', '$stateParams', 'uuid4', '$state', 'notificationruleservice', 'clientservice', 'statusservice', 'security', notificationRuleUpdateController]);
    function notificationRuleUpdateController(
        $rootScope, $scope, $mdDialog, $stateParams, uuid4, $state, notificationruleservice, clientservice, statusservice, securityservice
    ) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = false;
        vm.templateType = $scope.type ?? $stateParams.type;
        vm.previousRoute = $rootScope.previousState;

        vm.permissions = {
            settingsEdit: securityservice.immediateCheckRoles('settings__settings__edit')
        }
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = vm.isModal;

        vm.newRecord = vm.isModal && $scope.newRecord === true;
        if (!vm.newRecord) {
            vm.notificationRuleId = $stateParams.notificationRuleId;
        }

        vm.variableNotesExpanded = true;

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        vm.initialised = false;
        function initialise() {
            Promise.all([
                clientservice.getList(),
                statusservice.getAssessmentStatuses(),
                notificationruleservice.getRecipientTypes()
            ]).then(([
                clientListData,
                statusListData,
                recipientTypeData
            ]) => {
                vm.clientList = clientListData.data;
                vm.statusList = statusListData.data;
                vm.recipientTypeList = recipientTypeData;
                if (vm.notificationRuleId != null) {
                    Promise.all([
                        notificationruleservice.getNotificationRule(vm.notificationRuleId, $stateParams.type),
                        notificationruleservice.getNotificationTemplates(vm.notificationRuleId, $stateParams.type)
                    ]).then(([
                        notificationRuleData,
                        notificationTemplatesData
                    ]) => {
                        vm.notificationRule = notificationRuleData;
                        vm.notificationTemplates = notificationTemplatesData;
                        vm.initialised = true;
                    });
                } else {
                    vm.notificationRule.notificationRuleId = uuid4.generate();
                    vm.notificationRule.enabled = true;
                    vm.initialised = true;
                }
            });
        }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        function navToNew(id) {
            vm.isBusy = false;
            vm.cancel();
            $state.go("notificationrule-updateform", { notificationRuleId: id });
        }

        vm.addTemplate = function() {
            const newTemplate = {
                notificationRuleId: vm.notificationRule.notificationRuleId,
                notificationTemplateId: uuid4.generate()
            }
            vm.notificationTemplates.push(newTemplate);
        }

        // -------------------------- //
        // - SAVE / CANCEL / DELETE - //
        // -------------------------- //

        vm.save = function () {
            vm.isBusy = true;
            if (vm.newRecord === true) {
                notificationruleservice.createNotificationRule(vm.notificationRule, vm.notificationTemplates).then(() => {
                    vm.isBusy = false;
                    navToNew(vm.notificationRule.notificationRuleId);
                });
            } else {
                notificationruleservice.updateNotificationRule(vm.notificationRule, vm.notificationTemplates).then(() => {
                    vm.isBusy = false;
                });
            }
        }

        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("notificationRule-list");
                }
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            notificationruleservice.deleteNotificationRule(vm.notificationRuleId).then(() => {
                vm.notificationRule.deleted = true;
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            notificationruleservice.undoDeleteNotificationRule(vm.notificationRuleId).then(() => {
                vm.notificationRule.deleted = false;
                vm.isBusy = false;
            });
        }

        vm.deleteTemplate = function(template) {
            vm.notificationTemplates = vm.notificationTemplates.filter(x => x.notificationTemplateId !== template.notificationTemplateId);
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialise();

    }
})();