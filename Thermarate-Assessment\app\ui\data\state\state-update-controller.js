(function () {
    // The StateUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'StateUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'countryservice', 'stateservice', 'security', stateUpdateController]);
function stateUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  countryservice, stateservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit State';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.stateCode = null;
        vm.state = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New State";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.stateCode = $scope.stateCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.stateCode = $stateParams.stateCode;
        }

        // Get data for object to display on page
        var stateCodePromise = null;
        if (vm.stateCode != null) {
            stateCodePromise = stateservice.getState(vm.stateCode)
            .then(function (data) {
                if (data != null) {
                    vm.state = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.countryList = [];
        var countryPromise = countryservice.getList()
            .then(function(data){
                vm.countryList = data.data;
            });

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("state-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                stateservice.createState(vm.state).then(function(data){
                    vm.state = data;
                    vm.stateCode = vm.state.stateCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                stateservice.updateState(vm.state).then(function(data){
                    if (data != null) {
                        vm.state = data;
                        vm.stateCode = vm.state.stateCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            stateservice.deleteState(vm.stateCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            stateservice.undoDeleteState(vm.stateCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();