﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using TenureExtraction;

namespace ExtractionTests
{
    [TestClass]
    public class StarbandExtractionTests
    {


        [TestMethod]
        public  void TestStarbandExtraction()
        {
            string path = Directory.GetCurrentDirectory() + "\\Starbands\\DecimalStarbands.xlsx";
            var x = StarbandExtractor.Extract(path, "NCC 2019");

            ;
        }







    }
}
