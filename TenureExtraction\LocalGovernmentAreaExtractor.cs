﻿using Microsoft.Data.Sqlite;
using System;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.SqlServer.Types;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts 'localities' (e.g. Suburb) information from a Geopackage file and stores in the database.
    /// </summary>
    public class LocalGovernmentAreaExtractor
    {
        private const int CHUNK_SIZE = 500; // Number of rows to return in each sqlite call.
        private const string SQLITE_TABLE = "LGA_Boundaries_LGATE_233";
        private const string MSSQL_TABLE = "RSS_LocalGovernmentArea";

        private SqlConnection sqlConnection;
        private string directory;
        
        private SqliteConnection sqliteConnection;
        
        private Logger logger;
        
        private const string DATASET_ID = "233";
        
        public LocalGovernmentAreaExtractor(string directory, string connectionString, Logger logger)
        {
            try
            {
                this.directory = directory;
                this.logger = logger;
                logger.Log("Attempting to initialize LocalGovernmentAreaExtractor in directory" + directory);
                
                sqlConnection = new SqlConnection(connectionString);
                sqlConnection.Open();
                
                SqlServerTypes.Utilities.LoadNativeAssemblies(AppDomain.CurrentDomain.BaseDirectory);
                SQLitePCL.Batteries_V2.Init();
            }
            catch(Exception e)
            {
                logger.Log("Exception encountered when trying to initialize LocalGovernmentAreaExtractor. Error was: " + e.Message);
                throw;
            }
        }

        public void Dispose()
        {
            if (this.sqlConnection != null && this.sqlConnection.State == ConnectionState.Open)
                this.sqlConnection.Close();

            this.sqlConnection?.Dispose();
            
            if (this.sqliteConnection != null && this.sqliteConnection.State == ConnectionState.Open)
                this.sqliteConnection.Close();

            this.sqliteConnection?.Dispose();
        }

        ~LocalGovernmentAreaExtractor()
        {
            if (this.sqlConnection != null && this.sqlConnection.State == ConnectionState.Open)
                this.sqlConnection.Close();

            this.sqlConnection?.Dispose();
            
            if (this.sqliteConnection != null && this.sqliteConnection.State == ConnectionState.Open)
                this.sqliteConnection.Close();

            this.sqliteConnection?.Dispose();
        }

        private void CreatePostcodeIndex()
        {
            SqliteCommand c = new SqliteCommand(
                $"CREATE INDEX IF NOT EXISTS main.idx_postcode ON {SQLITE_TABLE}(postcode)", 
                this.sqliteConnection);
            
            c.ExecuteNonQuery();
        }
        
        /// <summary>
        /// Extracts boundary data from the Sqlite file and updates our RSS_LocalGovernmentArea table with the boundary data.
        /// </summary>
        public async Task UpdateDataset(int? limitToCount = null)
        {
            // We are only interested in updating the boundary data, as the current RSS_Suburb database has data from 
            // all states, whereas this localities extractor is only for WA.
            // The DataTable class (as used for bulk-insert operations) is unsuitable for bulk-update. Given that this
            // process is not run frequently and is not too huge, we just fall back to inserting 1 by 1.
            
            string[] files = Directory.GetFiles(directory);
            var geopackagePath = files.Where(s => s.Contains(DATASET_ID)).FirstOrDefault();

            if (string.IsNullOrEmpty(geopackagePath))
            {
                logger.Log("No LGA dataset found. Continuing to next process without updating LGA's...");
                return;
            }
            
            logger.Log($"Extraction started for geopackage file: {geopackagePath}");
            
            this.sqliteConnection = new SqliteConnection("Filename=" + geopackagePath);
            this.sqliteConnection.Open();
            CreatePostcodeIndex();
            
            // Delete all data from current table.
            var deleteDataCmd = new SqlCommand(
                $"DELETE FROM dbo.{MSSQL_TABLE} WHERE name != 'DONT_DELETE_ME'",
                sqlConnection);

            deleteDataCmd.ExecuteNonQuery();
            
            var debugCount = 0;

            // Loop over all rows in table and add.
            for(int r = 0; r < int.MaxValue; r++)
            {
                // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                if (limitToCount.HasValue && debugCount >= limitToCount)
                    break;
                
                var selectRowCmd = new SqliteCommand(
                    $"SELECT * FROM {SQLITE_TABLE} ORDER BY name LIMIT {CHUNK_SIZE} OFFSET {r * CHUNK_SIZE}",
                    sqliteConnection);
                
                var reader = selectRowCmd.ExecuteReader();

                logger.Log($"Processing dataset rows {r * CHUNK_SIZE}->{ r * CHUNK_SIZE + CHUNK_SIZE} for {SQLITE_TABLE}");

                if (reader.HasRows == false)
                    break; // Break out of outer loop.

                while (reader.Read())
                {
                    // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                    if (limitToCount.HasValue && debugCount >= limitToCount)
                        break;
                    
                    debugCount++;
                    
                    // Extract postcode (for matching) and boundary data.
                    //int id = Convert.ToInt32(reader["id"].ToString());
                    string name = reader["name"].ToString();
                    string nameTransformed = TransformName(name);
                    
                    SqlGeography boundary = null;
                        
                    try
                    {
                        boundary     = Shared.GetSqliteGeometryAsSqlBoundary(reader, true);
                    }
                    catch (Exception e)
                    {
                        continue; // Invalid data for this row.
                    }

                    // Attempt to insert or update LGA
                    try
                    {
                        // Check to see if a row with the existing name already exists in our DB.
                        var insertCmd = new SqlCommand(
                            $@"INSERT INTO dbo.RSS_LocalGovernmentArea
                                (Name, NameTransformed, Boundary, BoundaryJson)
                            VALUES
                                 (@Name, @NameTransformed, @Boundary, @BoundaryJson);",
                            sqlConnection);
                    
                        //insertCmd.Parameters.AddWithValue("Id", id);
                        insertCmd.Parameters.AddWithValue("Name", name);
                        insertCmd.Parameters.AddWithValue("NameTransformed", nameTransformed);
                       
                         //var geographyParam = new SqlParameter("BoundaryJson", SqlDbType.Udt);
                        // geographyParam.Value = Shared.ConvertSqlGeographyToJson(boundary);
                        // geographyParam.UdtTypeName = "geography";
                        // insertCmd.Parameters.Add(geographyParam);

                        // For some reason, if the list of points forms 2 polygons, we need to unreverse the points
                        // The only way to detect this at the moment is that each list that give 2 polygons have ")" and "(" symbols
                        string boundaryJson = Shared.ConvertSqlGeographyToJson(boundary);
                        if (boundaryJson.Contains(")"))
                        {
                            boundary = boundary.ReorientObject();
                        }
                        
                        insertCmd.Parameters.Add(new SqlParameter("@Boundary", boundary) { UdtTypeName = "Geography" });
                        insertCmd.Parameters.AddWithValue("BoundaryJson", boundaryJson);

                        
                        
                        insertCmd.ExecuteNonQuery();

                    }
                    catch(Exception e)
                    {
                        logger.Log($"Exception encountered while updating boundary information for. " +
                                   $"Range: {r * CHUNK_SIZE} -> { r * CHUNK_SIZE + CHUNK_SIZE}. " +
                                   $"Name: {name}" + 
                                   $"Error: " + e.Message);
                    }
                }
            }
            
        }

        private string TransformName(string name)
        {
            var split = name.Split(',');
            var transform = split[1].Trim() + " " + split[0].Trim();
            transform = Shared.ToTitleCase(transform);
            return transform;
        }
    }
}
