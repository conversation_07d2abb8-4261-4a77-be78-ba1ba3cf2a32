<!-- Icons showing general design data about model -->
<div class="el-data-icon-grid">

    <!-- Grey Line -->
    <hr class="grey-line" />

  <!-- Beds -->
  <div class="el-data-point bed-icon">
    <img class="el-data-icon"
         src="/content/images/energy-labs/el-bed-icon.svg"
         alt="Bed icon">
    <span class="el-data-icon-text">{{vm.theModel.numberOfBedrooms}}</span>
  </div>

  <!-- Baths -->
  <div class="el-data-point">
    <img class="el-data-icon bath-icon"
         style="margin-top:3px;"
         src="/content/images/energy-labs/el-bath-icon.png"
         alt="Bed icon">
    <span class="el-data-icon-text">{{vm.theModel.numberOfBathrooms}}</span>
  </div>

  <!-- Living Areas -->
  <div class="el-data-point">
    <img class="el-data-icon sofa-icon"
         src="/content/images/energy-labs/el-sofa-icon.svg"
         alt="Sofa icon">
    <span class="el-data-icon-text">{{vm.theModel.livingAreas}}</span>
  </div>

  <!-- Parking -->
  <div class="el-data-point">
    <img class="el-data-icon parking-icon"
         style="margin-top:-3px;"
         src="/content/images/energy-labs/el-parking-icon.png"
         alt="Bed icon">
    <span class="el-data-icon-text">{{vm.theModel.numberOfGarageSpots}}</span>
  </div>

  <!-- Width -->
  <div class="el-data-point width">
    <img class="el-data-icon house-width-icon"
         style="opacity:0.55"
         src="/content/images/energy-labs/el-house-width-icon.png"
         alt="Bed icon">
    <span class="el-data-icon-text">{{vm.theModel.width}}m</span>
  </div>

  <!-- House Area -->
  <div class="el-data-point width">
    <img class="el-data-icon area-icon"
         style="opacity:0.55"
         src="/content/images/energy-labs/el-area-icon.png"
         alt="Bed icon">
    <span class="el-data-icon-text">{{vm.theModel.displayFloorArea}}m<sup>2</sup></span>
  </div>

</div>

<style>

    /* Container */
    .el-data-icon-grid {
        position: relative;
        width: 100%;
        height: 55px;
        padding-top: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 50px;
    }

    /* Grey Line */
    .grey-line {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        width: 115%;
        border: none;
        border-top: 1px solid #e3e3e3;
    }

    .el-data-point {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
    }

    .el-data-point.width {
        grid-template-columns: 1fr 3fr;
    }

    .el-data-icon {
        width: 35px;
    }
    .bath-icon { width: 25px; }
    .parking-icon { width: 36px; }
    .house-width-icon { margin-top: 2px; width: 30px; }
    .area-icon { margin-top: 3px; width: 26px; }

    .el-data-icon-text {
        font-weight: bold;
        text-align: center;
        justify-self: start;
    }

</style>