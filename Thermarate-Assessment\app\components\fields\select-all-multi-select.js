﻿(function () {

    'use strict';
    angular.module('app').component('selectAllMultiSelect', {
        bindings: {
            allOptions: '<',
            returnSelections: '=', // Only pass in and return primary key of each item
            primaryKey: '<',
            displayKey: '<',
            isDisabled: '<'
        },
        templateUrl: 'app/components/fields/select-all-multi-select.html',
        controller: SelectAllMultiSelectController,
        controllerAs: 'vm'
    });

    SelectAllMultiSelectController.$inject = ['common'];

    function SelectAllMultiSelectController(common) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //
    
        let vm = this;

        var allObject = {};
        allObject[vm.primaryKey] = 'All';
        allObject[vm.displayKey] = 'All';
        

        // -------------- //
        // - INITIALISE - //
        // -------------- //
        
        vm.UI_list = angular.copy(vm.allOptions);
        vm.UI_selections = angular.copy(vm.returnSelections??[]);

        if (vm.UI_selections.find(s => s == "All") != null) {
            vm.UI_selections = angular.copy(vm.allOptions);
        } else {
            vm.UI_selections = vm.UI_selections.map(s => {
                let newObj = {};
                newObj[vm.primaryKey] = s;
                return newObj;
            });
        }
        vm.UI_selections = common.linkVariablesBetweenArrays(
            vm.UI_selections,
            vm.UI_list,
            vm.primaryKey
        );
        common.initialiseSelectAll(vm.UI_list, vm.UI_selections, vm.primaryKey, vm.displayKey);
        

        // ----------- //
        // - HANDLES - //
        // ----------- //
    
        vm.getMultiSelectDropdownText = common.getMultiSelectDropdownText;

        vm.selectItem = function (item) {
            vm.UI_selections = common.selectAllLogic(vm.UI_list, vm.UI_selections, vm.primaryKey, item, 'All');
            // Now let md-select take it's time to add the selected option
            setTimeout(() => {
                let newSelections = vm.UI_selections.find(i => i[vm.primaryKey] == 'All') != null
                                        ? [allObject]
                                        : angular.copy(vm.UI_selections);
                vm.returnSelections = newSelections.map(s => s[vm.primaryKey]);
            }, 50);
        }
    }

})();