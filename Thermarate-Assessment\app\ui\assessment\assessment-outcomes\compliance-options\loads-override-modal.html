<form class="main-content-wrapper"
      novalidate
      data-ng-controller='loadsOverrideModalController as vm'>

    <div class="widget"
         ng-form="loadsOverrideForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}} Annual Energy Loads ({{vm.energyLoadUnits}}/m<sup>2</sup>)"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

            <div layout="row" layout-sm="column" layout-xs="column" style="min-width:600px;">
                <!--Main Body-->
                <div flex="100">
                    <md-card>
                        <md-card-content>

                            <table style="width: 100%;">
                                <thead>
                                <tr>
                                    <th></th>
                                    <th>Original Value</th>
                                    <th style="display: flex; justify-content: center; align-items: center;">
                                        <span>Manual Override</span>
                                        <md-checkbox ng-model="vm.data.overrideEnergyLoads"
                                                     ng-click="vm.overrideChanged(vm.data.overrideEnergyLoads)"
                                                     ng-disabled="vm.disabled"
                                                     style="margin: 0; margin-left: 20px;">
                                        </md-checkbox>
                                    </th>
                                    <th><!-- reset button --></th>
                                </tr>
                                </thead>
                                <tbody>

                                    <!-- Heating -->
                                    <tr>
                                        <td>Heating</td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input ng-if="vm.data.heatingOriginal > 0 || vm.data.heatingOriginal == null"
                                                       type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.heatingOriginal"
                                                       ng-disabled="true"/>
                                                <input ng-if="vm.data.heatingOriginal == -1"
                                                       type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-value="'N/A'"
                                                       ng-disabled="true"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.heating"
                                                       ng-disabled="vm.data.overrideEnergyLoads !== true || vm.disabled"
                                                       ng-blur="vm.calculateTotal();"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <i class="fa fa-refresh clickable"
                                               ng-click="vm.data.heating = vm.data.heatingOriginal"
                                               ng-disabled="vm.disabled"
                                               ng-show="!vm.disabled"/>
                                        </td>
                                    </tr>

                                    <!-- Cooling -->
                                    <tr>
                                        <td>Cooling</td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input ng-if="vm.data.coolingOriginal >= 0 || vm.data.coolingOriginal == null"
                                                       type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.coolingOriginal"
                                                       ng-disabled="true"/>

                                                <input ng-if="vm.data.coolingOriginal == -1"
                                                       type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-value="'N/A'"
                                                       ng-disabled="true"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.cooling"
                                                       ng-disabled="vm.data.overrideEnergyLoads !== true || vm.disabled === true"
                                                       ng-blur="vm.calculateTotal();"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <i class="fa fa-refresh clickable"
                                               ng-click="vm.data.cooling = vm.data.coolingOriginal"
                                               ng-disabled="vm.disabled"
                                               ng-show="!vm.disabled"/>
                                        </td>
                                    </tr>

                                    <!-- Total -->
                                    <tr>
                                        <td>Total</td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.totalEnergyLoadOriginal"
                                                       ng-disabled="true"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.totalEnergyLoad"
                                                       ng-disabled="vm.data.overrideEnergyLoads !== true || vm.disabled === true"
                                                       ng-blur="vm.calculateOverrideCompliance();"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <i class="fa fa-refresh clickable"
                                               ng-click="vm.data.totalEnergyLoad = vm.data.totalEnergyLoadOriginal"
                                               ng-disabled="vm.disabled"
                                               ng-show="!vm.disabled"/>
                                        </td>
                                    </tr>

                                    <!-- Calculated HER -->
                                    <tr>
                                        <td>{{vm.herTitle()}}</td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.herOriginal"
                                                       ng-disabled="true"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="display: flex;">
                                                <input type="number"
                                                       class="compliance-row-table-input-m"
                                                       ng-model="vm.data.herOverride"
                                                       ng-disabled="vm.data.overrideEnergyLoads !== true || vm.disabled === true"/>
                                            </md-input-container>
                                        </td>
                                        <td>
                                            <i class="fa fa-refresh clickable"
                                               ng-click="vm.data.herOverride = vm.data.herOriginal"
                                               ng-disabled="vm.disabled"
                                               ng-show="!vm.disabled"/>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>

                        </md-card-content>
                    </md-card>
                </div>
            </div>

            <!-- Add + Cancel Buttons -->
            <div data-cc-widget-button-bar
                 data-is-modal="true">

                <md-button class="md-raised md-primary"
                           redi-allow-roles="['assessment_page_(tabs/sub-tabs)__simulation__edit']"
                           ng-disabled="loadsOverrideForm.$invalid || vm.disabled"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>