(function () {
    // The ProjectUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'ProjectUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', 'common', '$scope', '$mdDialog', '$stateParams',
            '$state', '$timeout', 'bootstrap.dialog', 'projectservice', 'projecttypeservice', 'uuid4',
            'clientservice', 'standardmodelservice', 'security', 'servicetemplateservice', 'wadesigncodeservice',
            'addressservice', 'stateservice', 'nathersclimatezoneservice',  'wholeofhomeservice', 'nccclimatezoneservice', 'selectvariablelinkservice',
            projectUpdateController]);
    function projectUpdateController($rootScope, common, $scope, $mdDialog, $stateParams,
        $state, $timeout, modalDialog, projectservice, projecttypeservice, uuid4,
        clientservice, standardmodelservice, securityservice, servicetemplateservice, wadesigncodeservice,
        addressservice, stateservice, nathersclimatezoneservice, wholeofhomeservice, nccclimatezoneservice, selectvariablelinkservice) {

        // The model for this form
        var vm = this;

        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Project';
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.clientId = $scope.clientId;
        vm.hideActionBar = false;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.client = null;
        vm.project = {
            projectId: uuid4.generate(),
            clientId: vm.clientId,
            projectName: "New Project",
            description: "",
            isActive: true,
            standardHomeModelFiles: [],
            variableOptions: {},
            logoFile: {},
            modelsFromModelList: [],
            energyLabsSettings: {}
        };

        // TESTING: Dummy Standard Model
        //vm.project = {
        //    ...vm.project,
        //    ...TestDummyData.NewProject,
        //};

        vm.resetChangeDetection = function () {
            vm.projectOriginal = angular.copy(vm.project);
        }

        vm.hasChanges = function () {
            return angular.toJson(vm.project) != angular.toJson(vm.projectOriginal);
        }

        vm.costEstimateBulkStatus = new MasterBulkStatus();
        vm.costEstimateBulkStatus.checkboxId = "sm-cost-estimate-bulk-checkbox";
        vm.costEstimateBulkStatus.isIndeterminate = false;
        vm.costEstimateBulkStatus.selectAllCheckboxState = false;

        vm.targetEnergyRatingOptions = common.targetEnergyRatingOptions();

        projecttypeservice.getList().then(data => vm.projectTypeList = data.data);

        servicetemplateservice.getServiceTypes().then(data => vm.serviceTypes = data);

        stateservice.getList().then(data => vm.stateList = data.data);

        nathersclimatezoneservice.getList().then(data => vm.natHERSClimateZoneList = data.data);

        nccclimatezoneservice.getList().then(data => vm.nccClimateZoneList = data.data);

        // Services Defaults
        servicetemplateservice.getServiceTypes().then(data => {
            vm.serviceTypes = data;
            vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                'title',
                vm.serviceTypes
            );
        });

        function refreshProject() {
            vm.isBusy = true;
            projectservice.getProject(vm.project.projectId).then(async data => {
                if (data != null) {
                    vm.project = {
                        ...vm.project,
                        ...data,
                    };
                    if (vm.project.logoFile == null) {
                        vm.project.logoFile = {};
                    } else {
                        vm.project.logoFile = { file: { fileName: vm.project.logoFile?.fileName } }; // Get file to show on load
                    }
                    vm.client = await clientservice.getClient(vm.project.clientId);
                    vm.client.clientCostItems.sort((a,b) => a.description > b.description ? 1 : -1);
                    // Get users for this client
                    clientservice.getUsersForClient(vm.project.clientId).then(data => {
                        vm.clientUsers = data;
                        // Get which users are attached to project
                        projectservice.getUsersForProject(vm.project.projectId).then(data => {
                        let userList = data;
                        vm.clientUsers.forEach(user => {
                            if (userList.includes(user.userId)) {
                            user.canViewProject = true;
                            }
                        });
                        });
                    });
                    if (vm.project.energyLabsSettings.toolIdentifyEnabled === null || vm.project.energyLabsSettings.toolIdentifyEnabled === undefined) {
                        vm.project.energyLabsSettings.toolIdentifyEnabled = true;
                        vm.project.energyLabsSettings.toolOptimiseEnabled = true;
                        vm.project.energyLabsSettings.toolConfigureEnabled = true;
                        vm.project.energyLabsSettings.toolWholeOfHomeEnabled = true;
                    }
                    vm.toggleLockWOHDisabled();
                    vm.checkLockWOH();
                    buildVariableOptions(data);
                    vm.updateServDefsVisibility();
                    flattenCostData();
                    vm.resetChangeDetection();
                    vm.updateCostEstimateQuantities();
                }
                vm.isBusy = false;
            });

        }

        vm.latRegex = "[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?)";
        vm.lonRegex = "(^[-+]?(180|([1][0-7][0-9]|[0-9]{1,2})(\\.[0-9]+)?)$)";

        function formatEmptyValues() {
            if (vm.project.stateCode == "")
              vm.project.stateCode = null;
            if (vm.project.natHERSClimateZone == "")
              vm.project.natHERSClimateZone = null;
            if (vm.project.nccClimateZoneCode == "")
              vm.project.nccClimateZoneCode = null;
        }

        vm.toggleLockWOHDisabled = function () {
            if (vm.project.suburbCode == null || vm.project.suburbCode == "" || vm.project.stateCode == null || vm.project.stateCode == "" || vm.project.natHERSClimateZoneCode == null || vm.project.natHERSClimateZoneCode == "" || vm.project.nccClimateZoneCode == null || vm.project.nccClimateZoneCode == "") {
                vm.lockWOHDisabled = true;
                vm.project.lockWOHLocation = false;
                if (!vm.newRecord) {
                    vm.project.energyLabsSettings.properties['natHERSClimateZone'] = true;
                }
            } else {
                vm.lockWOHDisabled = false;
            }
        }

        vm.checkLockWOH = function () {
            if (vm.project.lockWOHLocation && vm.project.energyLabsSettings?.properties != null) {
                vm.project.energyLabsSettings.properties['natHERSClimateZone'] = false;
            }
        }

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;

        if (vm.isModal) {

            if (vm.newRecord === false) {
                vm.project.projectId = $scope.projectId;
            }

            vm.hideActionBar = true;

        } else {
            vm.projectId = $stateParams.projectId;
            vm.project.projectId = $stateParams.projectId;
        }

        if (!vm.newRecord) {

            refreshProject();

        } else {
            vm.title = "New Project";
            vm.toggleLockWOHDisabled();

            clientservice.getClient(vm.clientId).then(data => {
                vm.client = data;
                vm.project.client = data;
                vm.isBusy = false;
            });

        }

        vm.addressChanged = function () {
            vm.toggleLockWOHDisabled();
        }

        vm.suburbChanged = function () {
            addressservice.setSuburbToProject(vm.project, vm.project.suburb, () => vm.toggleLockWOHDisabled());
        }

        // ---------------------------- //
        // - Toggle off on all models - //
        // ---------------------------- //



        vm.toggleModelsIsActive = function () {
            if (!vm.project.isActive) {
                // When toggling OFF, set flag to apply to all levels
                vm.project.toggleChildrenIsActive = true;
                // Immediately update child models in the UI
                if (vm.project.modelsFromModelList) {
                    vm.project.modelsFromModelList.forEach(model => model.isActive = false);
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Active Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.project.toggleChildrenIsActive = response.applyToAllLevels;
                    // If user chose to apply to all levels, update child models in the UI
                    if (response.applyToAllLevels && vm.project.modelsFromModelList) {
                        vm.project.modelsFromModelList.forEach(model => model.isActive = true);
                    }
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.project.isActive = false;
                    vm.project.toggleChildrenIsActive = false;
                });
            }
        }

        vm.toggleModels3dFloorplans = function () {
            if (!vm.project.energyLabsSettings.view3dFloorPlans) {
                // When toggling OFF, set flag to apply to all levels
                vm.project.toggleChildrenView3dFloorPlans = true;
                // Immediately update child models in the UI
                if (vm.project.modelsFromModelList) {
                    vm.project.modelsFromModelList.forEach(model => model.view3dFloorPlans = false);
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "3D Model Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.project.toggleChildrenView3dFloorPlans = response.applyToAllLevels;
                    // If user chose to apply to all levels, update child models in the UI
                    if (response.applyToAllLevels && vm.project.modelsFromModelList) {
                        vm.project.modelsFromModelList.forEach(model => model.view3dFloorPlans = true);
                    }
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.project.energyLabsSettings.view3dFloorPlans = false;
                    vm.toggleSettings.view3dFloorPlans.applyToAllLevels = false;
                });
            }
        }

        // Initialize variation categories
        vm.variationCategories = StandardModelConstants.variationCategories;
        vm.bulkStatus = {};
        vm.variationCategories.forEach(category => {
            vm.bulkStatus[category] = {
                selectAllCheckboxState: false,
                isIndeterminate: false
            };
        });

        vm.toggleModelsVarCategories = function () {
            if (vm.project.modelsFromModelList) {
                vm.project.modelsFromModelList.forEach(m => {
                    if (!vm.project.energyLabsSettings.varCategoryFloorplanActive) {
                        m.variationOptionsSettings.floorplanIsActive = false;
                    }
                    if (!vm.project.energyLabsSettings.varCategoryDesignOptionActive) {
                        m.variationOptionsSettings.designOptionIsActive = false;
                    }
                    if (!vm.project.energyLabsSettings.varCategoryFacadeActive) {
                        m.variationOptionsSettings.facadeIsActive = false;
                    }
                    if (!vm.project.energyLabsSettings.varCategorySpecificationActive) {
                        m.variationOptionsSettings.specificationIsActive = false;
                    }
                    if (!vm.project.energyLabsSettings.varCategoryConfigurationActive) {
                        m.variationOptionsSettings.configurationIsActive = false;
                    }
                });
            }
        }

        // Project Variation Options Functions

        // Get variation options for a specific category
        vm.getProjectVariationOptions = function(category) {
            if (!vm.project.variationOptions) {
                vm.project.variationOptions = [];
            }
            return vm.project.variationOptions.filter(o => o.variationCategoryCode === category && !o.deleted);
        }

        // Add a new variation option to a category
        vm.addProjectVariationOption = function(category) {
            if (!vm.project.variationOptions) {
                vm.project.variationOptions = [];
            }

            let newOptionName = "New Option";
            let counter = 1;
            while (vm.project.variationOptions.filter(o => o.variationCategoryCode === category && !o.deleted).find(o => o.optionName === newOptionName) != null) {
                newOptionName = `New Option ${counter++}`;
            }

            vm.project.variationOptions.push({
                variationCategoryCode: category,
                optionName: newOptionName,
                sortOrder: Math.max(...vm.getProjectVariationOptions(category).map(o => o.sortOrder || 0), 0) + 1,
                deleted: false
            });
        }

        // Duplicate a variation option
        vm.copyProjectVariationOption = function(category, option) {
            let newOptionName = `${option.optionName} (copy)`;
            while (vm.project.variationOptions.filter(o => o.variationCategoryCode === category && !o.deleted).find(o => o.optionName === newOptionName) != null) {
                newOptionName += ' (copy)';
            }

            vm.project.variationOptions.push({
                variationCategoryCode: category,
                optionName: newOptionName,
                sortOrder: Math.max(...vm.getProjectVariationOptions(category).map(o => o.sortOrder || 0), 0) + 1,
                deleted: false
            });
        }

        // Delete a variation option
        vm.deleteProjectVariationOption = function(option) {
            // Find the option by matching all properties since we don't have a unique ID
            const index = vm.project.variationOptions.findIndex(o =>
                o.variationCategoryCode === option.variationCategoryCode &&
                o.optionName === option.optionName &&
                o.sortOrder === option.sortOrder);
            if (index !== -1) {
                vm.project.variationOptions[index].deleted = true;
            }
        }

        // Move a variation option up in the list
        vm.moveProjectVarOptionUp = function(category, option) {
            const options = vm.getProjectVariationOptions(category);
            // Find the option by matching all properties since we don't have a unique ID
            const index = options.findIndex(o =>
                o.variationCategoryCode === option.variationCategoryCode &&
                o.optionName === option.optionName &&
                o.sortOrder === option.sortOrder);
            if (index > 0) {
                // Swap sort orders
                const temp = options[index].sortOrder;
                options[index].sortOrder = options[index - 1].sortOrder;
                options[index - 1].sortOrder = temp;
                // Resort the array
                vm.project.variationOptions.sort((a, b) => a.sortOrder - b.sortOrder);
            }
        }

        // Move a variation option down in the list
        vm.moveProjectVarOptionDown = function(category, option) {
            const options = vm.getProjectVariationOptions(category);
            // Find the option by matching all properties since we don't have a unique ID
            const index = options.findIndex(o =>
                o.variationCategoryCode === option.variationCategoryCode &&
                o.optionName === option.optionName &&
                o.sortOrder === option.sortOrder);
            if (index < options.length - 1) {
                // Swap sort orders
                const temp = options[index].sortOrder;
                options[index].sortOrder = options[index + 1].sortOrder;
                options[index + 1].sortOrder = temp;
                // Resort the array
                vm.project.variationOptions.sort((a, b) => a.sortOrder - b.sortOrder);
            }
        }

        // Update bulk select status
        vm.updateBulkSelectStatus = function(list, bulkStatus) {
            if (!list) return;

            const selectedCount = list.filter(item => item.checkboxSelected).length;
            bulkStatus.selectAllCheckboxState = selectedCount === list.length;
            bulkStatus.isIndeterminate = selectedCount > 0 && selectedCount < list.length;
        }

        // Launch bulk edit modal for project options
        vm.launchProjectOptionsBulkEditModal = function(category) {
            let selectedOptions = vm.project.variationOptions.filter(o =>
                o.variationCategoryCode === category &&
                !o.deleted &&
                o.checkboxSelected
            );

            if (selectedOptions.length === 0) return;

            let modalScope = $rootScope.$new();
            modalScope.thisProjectId = vm.project.projectId;
            modalScope.clientId = vm.project.clientId;
            modalScope.categoryName = common.toSplitTitleCase(category);
            modalScope.options = selectedOptions;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-project-variation-options-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal // Keep parent modal open when this is a modal
            }).then(function(response) {
                if (response.bulkEditAction === 'DELETE') {
                    // Delete selected options
                    selectedOptions.forEach(option => {
                        vm.deleteProjectVariationOption(option);
                    });
                } else if (response.bulkEditAction === 'COPY') {
                    // Duplicate selected options
                    selectedOptions.forEach(option => {
                        vm.copyProjectVariationOption(category, option);
                    });
                    // Clear checkbox selection
                    selectedOptions.forEach(option => option.checkboxSelected = false);
                    // Update bulk selection status
                    vm.updateBulkSelectStatus(vm.project.variationOptions.filter(o => o.variationCategoryCode === category), vm.bulkStatus[category]);
                } else if (response.bulkEditAction === 'COPYTOPROJECT' && response.selectedProjectId) {
                    // Copy options to another project
                    projectservice.getProject(response.selectedProjectId).then(data => {
                        if (data) {
                            // Create copies of the selected options
                            const optionsToCopy = selectedOptions.map(o => ({
                                variationCategoryCode: o.variationCategoryCode,
                                optionName: o.optionName,
                                sortOrder: o.sortOrder,
                                deleted: false
                            }));

                            // Initialize variationOptions array if it doesn't exist
                            if (!data.variationOptions) {
                                data.variationOptions = [];
                            }

                            // Add the copied options to the target project
                            optionsToCopy.forEach(option => {
                                data.variationOptions.push(option);
                            });

                            // Save the updated project
                            projectservice.updateProject(data, false).then(() => {
                                common.logger.logSuccess("Variation options copied successfully to project: " + data.projectName);
                            });

                            // Clear checkbox selection
                            selectedOptions.forEach(option => option.checkboxSelected = false);
                            // Update bulk selection status
                            vm.updateBulkSelectStatus(vm.project.variationOptions.filter(o => o.variationCategoryCode === category), vm.bulkStatus[category]);
                        }
                    });
                }
            });
        }

        // Copy variation options between projects
        vm.copyVariationOptionsProject = function(copyFrom = true) {
            // If copying TO another project, check if we have any options to copy
            if (!copyFrom && (!vm.project.variationOptions || vm.project.variationOptions.filter(o => !o.deleted).length === 0)) {
                common.logger.warning("No variation options to copy. Please add some options first.");
                return;
            }

            let modalScope = $rootScope.$new();
            modalScope.title = `Copy Variation Options ${copyFrom ? 'From' : 'To'} Project`;
            modalScope.clientId = vm.project.clientId;
            modalScope.excludeProjectId = copyFrom ? null : vm.project.projectId; // Exclude current project when copying TO

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/project-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal && vm.newRecord // Keep parent modal open when creating a new record
            }).then(function(selectedProjectId) {
                if (selectedProjectId) {
                    projectservice.getProject(selectedProjectId).then(data => {
                        if (copyFrom) {
                            // COPY FROM: Update current project with selected project's options
                            if (data) {
                                // Copy variation options if available
                                if (data.variationOptions) {
                                    // Create copies of the variation options
                                    const newOptions = data.variationOptions
                                        .filter(o => !o.deleted)
                                        .map(o => ({
                                            variationCategoryCode: o.variationCategoryCode,
                                            optionName: o.optionName,
                                            sortOrder: o.sortOrder,
                                            deleted: false
                                        }));

                                    vm.project.variationOptions = newOptions;
                                } else {
                                    // Only show warning for missing options
                                    common.logger.warning("No variation options found in the selected project.");
                                }

                                // Copy toggle active values for variation categories
                                if (data.energyLabsSettings && vm.project.energyLabsSettings) {
                                    // Copy all variation category active toggles
                                    if (typeof data.energyLabsSettings.varCategoryFloorplanActive === 'boolean')
                                        vm.project.energyLabsSettings.varCategoryFloorplanActive = data.energyLabsSettings.varCategoryFloorplanActive;

                                    if (typeof data.energyLabsSettings.varCategoryDesignOptionActive === 'boolean')
                                        vm.project.energyLabsSettings.varCategoryDesignOptionActive = data.energyLabsSettings.varCategoryDesignOptionActive;

                                    if (typeof data.energyLabsSettings.varCategoryFacadeActive === 'boolean')
                                        vm.project.energyLabsSettings.varCategoryFacadeActive = data.energyLabsSettings.varCategoryFacadeActive;

                                    if (typeof data.energyLabsSettings.varCategorySpecificationActive === 'boolean')
                                        vm.project.energyLabsSettings.varCategorySpecificationActive = data.energyLabsSettings.varCategorySpecificationActive;

                                    if (typeof data.energyLabsSettings.varCategoryConfigurationActive === 'boolean')
                                        vm.project.energyLabsSettings.varCategoryConfigurationActive = data.energyLabsSettings.varCategoryConfigurationActive;

                                    // Update child models if any
                                    vm.toggleModelsVarCategories();
                                }
                            } else {
                                // Only show warning for missing options
                                common.logger.warning("No variation options found in the selected project.");
                            }
                        } else {
                            // COPY TO: Update selected project with current project's options
                            if (data) {
                                // Create copies of the current project's variation options
                                const optionsToCopy = vm.project.variationOptions
                                    .filter(o => !o.deleted)
                                    .map(o => ({
                                        variationCategoryCode: o.variationCategoryCode,
                                        optionName: o.optionName,
                                        sortOrder: o.sortOrder,
                                        deleted: false
                                    }));

                                // Update the target project with the copied options
                                data.variationOptions = optionsToCopy;

                                // Copy toggle active values for variation categories
                                if (vm.project.energyLabsSettings && data.energyLabsSettings) {
                                    // Copy all variation category active toggles with safety checks
                                    if (typeof vm.project.energyLabsSettings.varCategoryFloorplanActive === 'boolean')
                                        data.energyLabsSettings.varCategoryFloorplanActive = vm.project.energyLabsSettings.varCategoryFloorplanActive;

                                    if (typeof vm.project.energyLabsSettings.varCategoryDesignOptionActive === 'boolean')
                                        data.energyLabsSettings.varCategoryDesignOptionActive = vm.project.energyLabsSettings.varCategoryDesignOptionActive;

                                    if (typeof vm.project.energyLabsSettings.varCategoryFacadeActive === 'boolean')
                                        data.energyLabsSettings.varCategoryFacadeActive = vm.project.energyLabsSettings.varCategoryFacadeActive;

                                    if (typeof vm.project.energyLabsSettings.varCategorySpecificationActive === 'boolean')
                                        data.energyLabsSettings.varCategorySpecificationActive = vm.project.energyLabsSettings.varCategorySpecificationActive;

                                    if (typeof vm.project.energyLabsSettings.varCategoryConfigurationActive === 'boolean')
                                        data.energyLabsSettings.varCategoryConfigurationActive = vm.project.energyLabsSettings.varCategoryConfigurationActive;
                                }

                                // Save the updated project
                                projectservice.updateProject(data).then(() => {
                                    // Only show success toast for Copy To operations
                                    common.logger.logSuccess("Variation options and toggle settings copied successfully to project: " + data.projectName);
                                });
                            }
                        }
                    });
                }
            });
        }

        // Clear all project variation options
        vm.clearAllProjectVariationOptions = function() {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Clear All";
            modalScope.confirmationText = "Are you sure you want to clear all project variation options?";

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: vm.isModal // Keep parent modal open when this is a modal
            }).then(function(confirmed) {
                if (confirmed) {
                    if (vm.project.variationOptions) {
                        vm.project.variationOptions.forEach(option => {
                            option.deleted = true;
                        });
                    }
                }
            });
        }

        // Use common service's toSplitTitleCase function
        vm.toSplitTitleCase = common.toSplitTitleCase;

        vm.toggleModelsCostEstimate = function () {
            if (!vm.project.energyLabsSettings.costEstimateEnabledDefault) {
                // When toggling OFF, set flag to apply to all levels
                vm.project.toggleChildrenCostEstimateEnabled = true;
                // Immediately update child models in the UI
                if (vm.project.modelsFromModelList) {
                    vm.project.modelsFromModelList.forEach(model => model.costEstimateEnabled = false);
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Cost Estimate Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.project.toggleChildrenCostEstimateEnabled = response.applyToAllLevels;
                    // If user chose to apply to all levels, update child models in the UI
                    if (response.applyToAllLevels && vm.project.modelsFromModelList) {
                        vm.project.modelsFromModelList.forEach(model => model.costEstimateEnabled = true);
                    }
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.project.energyLabsSettings.costEstimateEnabledDefault = false;
                    vm.project.toggleChildrenCostEstimateEnabled = false;
                });
            }
        }

        vm.toggleModelsDesignInsights = function () {
            if (!vm.project.energyLabsSettings.designInsightsEnabled) {
                // When toggling OFF, set flag to apply to all levels
                vm.project.toggleChildrenDesignInsightsEnabled = true;
                // Immediately update child models in the UI
                if (vm.project.modelsFromModelList) {
                    vm.project.modelsFromModelList.forEach(model => {
                        if (!model.variableMetadata) {
                            model.variableMetadata = {};
                        }
                        model.variableMetadata.designInsightsEnabled = false;
                    });
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Design Insights Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.project.toggleChildrenDesignInsightsEnabled = response.applyToAllLevels;
                    // If user chose to apply to all levels, update child models in the UI
                    if (response.applyToAllLevels && vm.project.modelsFromModelList) {
                        vm.project.modelsFromModelList.forEach(model => {
                            if (!model.variableMetadata) {
                                model.variableMetadata = {};
                            }
                            model.variableMetadata.designInsightsEnabled = true;
                        });
                    }
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.project.energyLabsSettings.designInsightsEnabled = false;
                    vm.toggleSettings.designInsightsEnabled.applyToAllLevels = false;
                });
            }
        }

        // ---------------------------- //

        // List of users attached to project
        vm.userProjectList = [];
        vm.userProjectListChanged = false;
        vm.updateUserProject = function (userId, checked) {
            // Add
            if (checked) {
                vm.userProjectList.push(userId);
            }
            // Remove
            else {
                vm.userProjectList = vm.userProjectList.filter(toKeep => toKeep != userId);
            }
            vm.userProjectListChanged = true;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        vm.newLogoUpload = function (newLogo) {
          vm.showUploadError = false;
          if (newLogo == null) {
            vm.project.logoFileId = null;
            vm.project.logoFile = {};
            vm.project.logoFileUrl = null;
            $scope.$apply();
          }
          else if (['.png', '.jpg', '.jpeg', '.svg'].includes(newLogo.fileName.slice(-4))) {
            vm.project.logoFileId = newLogo.fileId;
            vm.project.logoFileUrl = newLogo.url;
            $scope.$apply();
          }
          else {
            vm.project.logoFile = {};
            vm.showUploadError = true;
            $scope.$apply();
          }
        }

        vm.clearLocationFields = function () {
            vm.project.suburbCode = null;
            vm.project.suburbName = null;
            vm.project.stateCode = null;
            vm.project.latitude = null;
            vm.project.longitude = null;
            vm.project.lga = null;
            vm.project.natHERSClimateZoneCode = null;
            vm.project.nccClimateZoneCode = null;
            vm.toggleLockWOHDisabled();
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                $state.go("client-updateform", { clientId: vm.project.clientId });
            }
        }

        vm.save = function (finishedCallback) {
            vm.isBusy = true;
            formatEmptyValues();
            if (vm.newRecord == true){
                projectservice.createProject(vm.project).then(data => {
                    vm.isBusy = false;
                    // Get the complete project data from the response
                    if (data && data.data) {
                        // Return the complete project data when closing the modal
                        $mdDialog.hide(data.data);
                    } else {
                        // Fallback to the response if data.data is not available
                        $mdDialog.hide(data);
                    }
                });
            } else {
                // Add toggle settings to the project object
                vm.project.toggleSettings = vm.toggleSettings;

                projectservice.updateProject(vm.project).then(async () => {
                    // Update which users can view this project
                    if (vm.userProjectListChanged) {
                        let userIdList = vm.clientUsers.filter(user => user.canViewProject).map(user => user.userId);
                        await projectservice.updateUsersForProject(vm.project.projectId, userIdList);
                    }
                    // Update it's Models
                    await standardmodelservice.updateModelsList(
                        vm.project.modelsFromModelList.map(m => { return {
                            standardHomeModelId: m.standardHomeModelId,
                            isActive: m.isActive,
                            view3dFloorPlans: m.view3dFloorPlans ?? false,
                            costEstimateEnabled: m.costEstimateEnabled ?? false,
                            designInsightsEnabled: m.variableMetadata.designInsightsEnabled ?? false,
                            sortOrder: m.sortOrder,
                            variationOptionsSettings: m.variationOptionsSettings,
                        }})
                    );

                    // Reset toggle settings after save
                    vm.project.toggleChildrenIsActive = false;
                    vm.project.toggleChildrenView3dFloorPlans = false;
                    vm.project.toggleChildrenCostEstimateEnabled = false;
                    vm.project.toggleChildrenDesignInsightsEnabled = false;

                    // For backward compatibility
                    vm.toggleSettings = {
                        isActive: { applyToAllLevels: false },
                        view3dFloorPlans: { applyToAllLevels: false },
                        costEstimateEnabledDefault: { applyToAllLevels: false },
                        designInsightsEnabled: { applyToAllLevels: false }
                    };

                    vm.resetChangeDetection();
                    if (finishedCallback != null) {
                        finishedCallback();
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            projectservice.deleteProject(vm.project.projectId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            projectservice.undoDeleteProject(vm.project.projectId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.getClients = function(searchTerm) {
            var filter = [{ field: "clientName", operator: "startswith", value: searchTerm }];
            var sort = [{ field: "isFavourite", dir: "desc" }, { field: "clientName", dir: "asc" }];

            return clientservice.getList(null, null, null, null, null, sort, filter)
              .then(function (data) {
                  var list = data.data;
                  return list;
              });
        }

        vm.clientChanged = function() {

            if (vm.project.client == null)
                return;

            vm.project.clientId = vm.project.client.clientId;
        }

        vm.uploadExcelFile = function(file)
        {
          if (file == null)
              return;

          projectservice
            .processSpreadsheet(file, vm.project.projectId)
            .then((data) => {
                setTimeout(refreshProject, 250);
            });
        }

        vm.onModelDelete = function () {
            refreshProject();
        }

        /**
         * Sets sort order of standard home model files when no 'sort order' is already supplied. This is only
         * required to fix up old data which was created WITHOUT the sort order in the first place.
         * */
        function setImageSortOrder(standardHomeModelFiles) {

            let order = 0;
            standardHomeModelFiles.forEach(x => {

                if (x.deleted)
                    return;

                x.sortOrder = order;
                order++;
            });

            // standardHomeModelFiles.sort((a, b) => a.sortOrder - b.sortOrder);

        }
        vm.setImageSortOrder = setImageSortOrder;

        vm.variableOptions = [];
        function buildVariableOptions(project) {
            // Build variable options
            const vars = [];
            for (const key in project.variableOptions) {
                vars.push({
                    title: key,
                    options: project.variableOptions[key]
                });
            }
            vm.variableOptions = vars;

            // Initialize variation options if needed
            if (project.variationOptions && !Array.isArray(project.variationOptions)) {
                // If it's not an array, it might be coming from JSON deserialization
                try {
                    project.variationOptions = JSON.parse(project.variationOptions);
                } catch (e) {
                    // If parsing fails, initialize as empty array
                    project.variationOptions = [];
                }
            } else if (!project.variationOptions) {
                project.variationOptions = [];
            }
        }

        vm.toggleCostEstimate = function toggleCostEstimate(variable, option) {

            let options = vm.project.variableMetadata.generalOptionData[variable.title];

            if (options == null) {
                vm.project.variableMetadata.generalOptionData[variable.title] = [];
                options = vm.project.variableMetadata.generalOptionData[variable.title];
            }

            let match = options?.find(x => x.optionValue === option);

            if (match == null) {
                options.push({ optionValue: option, costEstimateData: { description: option, category: variable.title } });
                match = options?.find(x => x.optionValue === option);
            }

            if (match.costEstimateData == null)
                match.costEstimateData = { description: option, category: variable.title, enabled: false };

            match.costEstimateEnabled = !match.costEstimateEnabled;
            match.costEstimateData.category = variable.title;

            flattenCostData();

        }

        vm.toggleVariableDefaultOption = function toggleVariableDefaultOption(variable, option) {

            let options = vm.project.variableMetadata.generalOptionData[variable.title];

            if (options == null) {
                vm.project.variableMetadata.generalOptionData[variable.title] = [];
                options = vm.project.variableMetadata.generalOptionData[variable.title];
            }

            let match = options?.find(x => x.optionValue === option);

            if (match == null) {
                options.push({ optionValue: option, costEstimateData: { description: option, category: variable.title, enabled: false } });
                match = options?.find(x => x.optionValue === option);
            }

            if (match.costEstimateData == null)
                match.costEstimateData = { description: option, category: variable.title, enabled: false };

            match.costEstimateData.category = variable.title;

            if (match.isDefaultForVariable)
                match.isDefaultForVariable = false;
            else {

                options.forEach(option => option.isDefaultForVariable = false);
                match.isDefaultForVariable = true;
            }
        }

        vm.costEstimateEnabledFor = function (variable, option) {

            const matches = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable]?.filter(x => x.optionValue === option);

            if (matches == null || matches.length === 0)
                return false;

            const match = matches[0];
            return match.costEstimateEnabled;
        }

        vm.isDefaultForVariable = (variable, option) => vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable]?.filter(x => x.optionValue === option)[0]?.isDefaultForVariable;

        vm.toggleCostEstimate = function (variable, option) {

            let options = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];

            if (options == null) {
                vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable] = [];
                options = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];
            }

            let match = options.find(x => x.optionValue === option);

            if (match == null) {
                options.push({ optionValue: option, costEstimateData: { description: option, category: variable } });
                match = options.find(x => x.optionValue === option);
            }

            if (match.costEstimateData == null)
                match.costEstimateData = { description: option, category: variable.title, enabled: false };

            match.costEstimateEnabled = !match.costEstimateEnabled;
            match.costEstimateData.category = variable;

            flattenCostData();
        }

        /** Flatten cost data for UI & sort on category and option desc. */
        function flattenCostData() {

            vm.flattenedCostData = [];
            for (var categoryKey in vm.project.energyLabsSettings.properties) {

                if (!vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData.hasOwnProperty(categoryKey))
                    continue;

                const ordered = vm.ordered(vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[categoryKey]);
                ordered?.forEach(option => {

                    if (option.costEstimateEnabled && option.costEstimateData != null) {
                        // Cost Item
                        if (option.costEstimateData?.costItemId != null) {
                            vm.costItemChanged(option, vm.client.clientCostItems.find(i => i.clientCostItemId == option.costEstimateData.costItemId));
                        }
                        vm.flattenedCostData.push(option)
                    }

                });
            }

            vm.flattenedCostData.sort((a, b) => {

                let ac = a.costEstimateData?.category;
                let bc = b.costEstimateData?.category;

                if (ac === bc) {
                    ac = a.sortOrder;
                    bc = b.sortOrder;
                }

                if (typeof ac == 'string' && bc != null) {
                    return ac.localeCompare(bc);
                } else {
                    return (ac || 0) - (bc || 0);
                }
            });
        }

        vm.toggleVariableDefaultOption = function (variable, option) {

            let options = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];

            if (options == null) {
                vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable] = [];
                options = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];
            }

            let match = options.find(x => x.optionValue === option);

            if (match == null) {
                options.push({ optionValue: option });
                match = options.find(x => x.optionValue === option);
            }

            if (match.isDefaultForVariable)
                match.isDefaultForVariable = false;
            else {

                options.forEach(option => option.isDefaultForVariable = false);

                match.isDefaultForVariable = true;
            }

            flattenCostData();
        }

        vm.restoreVariableDefaults = function () {

            const model = vm.project;
            const defaults = vm.project.energyLabsSettings.variableOptionDefaults;

            for(const key in vm.project.energyLabsSettings.properties) {

                const values = defaults.generalOptionData[key];

                // If there are no values present for this key, it means that
                // NO DEFAULTS have been set and therefore they should rever to blank.
                if (values == null || values.length === 0) {
                    model.variableMetadata.generalOptionData[key] = [];
                }

                defaults.generalOptionData[key]?.forEach(def => {

                    // Ensure this option value is even valid for this specific home design.
                    const valid = model.variableOptions[key].includes(def.optionValue);
                    if (!valid)
                        return;

                    const option = model.variableMetadata.generalOptionData[key]?.find(x => x.optionValue === def.optionValue);

                    if (option != null) {
                        option.isDefaultForVariable = def.isDefaultForVariable;
                        option.costEstimateEnabled = def.costEstimateEnabled;
                        option.sortOrder = def.sortOrder;
                    } else {

                        if (model.variableMetadata.generalOptionData[key] == null)
                            model.variableMetadata.generalOptionData[key] = [];

                        model.variableMetadata.generalOptionData[key].push(angular.copy(def));

                    }
                });

                // ... we also need to loop the _other_ way. We are looking for values which exist on the model
                // but NOT in the defaults. This implies the value in the model needs to be 'nullified'.
                model.variableMetadata.generalOptionData[key]?.forEach(optX => {

                    const def = defaults.generalOptionData[key]?.find(x => x.optionValue === optX.optionValue);

                    if (def == null) {
                        optX.isDefaultForVariable = null;
                        optX.costEstimateEnabled = null;
                        optX.sortOrder = null;
                    }
                });
            }

            flattenCostData();
        }

        vm.propertyGroups = standardmodelservice.specGridOrder;

        vm.ordered = function (options) {
            return options.sort((a, b) => a.sortOrder - b.sortOrder);
        }

        vm.reorderOptions = function(variable, optionsInOrder) {

            // options will be in the order we want so simply loop over and apply
            const allOptions = vm.project.energyLabsSettings.variableOptionDefaults.generalOptionData[variable];
            optionsInOrder.forEach((opt, i) => {
                let match = allOptions.find(x => x.optionValue === opt);

                if (match == null && match !== 0) {
                    allOptions.push({ optionValue: opt });
                    match = allOptions.find(x => x.optionValue === opt);
                }

                match.sortOrder = i;
            });

            flattenCostData();
        }

        vm.toggleBulkEditAll = toggleBulkEditAll;
        function toggleBulkEditAll(toggleState) {
            vm.project.variableMetadata.designInsights.forEach(insight => {
                insight.selectedForBulkEdit = toggleState;
            });
        }

        vm.addDesignInsight = function() {
            if (vm.project.variableMetadata.designInsights == null)
                vm.project.variableMetadata.designInsights = [];

            vm.project.variableMetadata.designInsights.push({});
        }

        vm.ensureInsightsAreUnique = function(insight, option, currentVariable){

            // For each insight in our list (excluding the current one) we check
            // to ensure all other insights do not contain the same combination
            // or options across both available variables (climateZone and
            // northOffset). If it's not unique, we pop the last option of the
            // list.
            setTimeout(() => {
                vm.project.variableMetadata.designInsights.forEach(x => {

                    if (x === insight)
                        return;

                    let hasDuplicateClimateZone = false;
                    let hasDuplicateNorthOffset = false;

                    x.climateZones.forEach(cz => {
                        const match = insight.climateZones?.find(czb => czb === cz);
                        if (match != null)
                            hasDuplicateClimateZone = true;
                    });

                    x.northOffsets.forEach(cz => {
                        const match = insight.northOffsets?.find(czb => czb === cz);
                        if (match != null)
                            hasDuplicateNorthOffset = true;
                    });

                    if (hasDuplicateClimateZone && hasDuplicateNorthOffset){
                        common.logger.logWarning("Cannot feature design insights with duplicate data. Removing last input.", null, null, true);

                        const index = insight[currentVariable].indexOf(option);
                        insight[currentVariable].splice(index, 1);
                    }

                    // We should only need to compare it with the current insight.
                    // Others should all be unique as they are checked on every
                    // change also.

                });
            }, 100);
        }

        vm.roundBathroomsToNearestHalf = function() {
            setTimeout(() => {
                vm.project.numberOfBathrooms = common.roundToStep(vm.project.numberOfBathrooms, 0.5)
            }, 50);
        }

        vm.removeDesignInsight = function(insight) {
            const index = vm.project.variableMetadata.designInsights.indexOf(insight);
            vm.project.variableMetadata.designInsights.splice(index, 1);
        }

        vm.selectAllCheckboxes = (a, b, c) => {

            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                safeApply();
            }, 25);
        }

        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        vm.updateBulkSelectStatus = updateBulkSelectStatus;

        vm.launchCostEstimateBulkEdit = async function() {

            let modalScope = $rootScope.$new();

            // Modal Inputs
            modalScope.type = "project";
            let selectedItems = vm.flattenedCostData.filter(x => x.checkboxSelected);
            // IF all selected items have same Category, add this Category's Cost Item options
            if (selectedItems.every(x => x.costEstimateData.category == selectedItems[0].costEstimateData.category)) {
                modalScope.clientCostItems = vm.itemCodesForCategory(selectedItems[0].costEstimateData.category);
            }
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-cost-estimate-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {

                selectedItems.forEach(x => {

                    if (response.costItemCustomSelected) {
                        x.costEstimateData.costItemId = null;
                    }
                    else if (response.costItemId) {
                        x.costEstimateData.costItemId = response.costItemId;
                    }

                    if (response.quantityVarRefJson) {
                        x.costEstimateData.quantity = null;
                        x.costEstimateData.quantityVarRefJson = response.quantityVarRefJson;
                    } else if (response.quantity) {
                        x.costEstimateData.quantity = response.quantity;
                        x.costEstimateData.quantityVarRefJson = null;
                    }

                    // Only if Item Code was before or is now 'Custom', OR a new Item Code was just selected
                    if (x.costEstimateData.costItemId == null || response.costItemId) {
                        if (response.unitOfMeasure)
                            x.costEstimateData.unitOfMeasure = response.unitOfMeasure;

                        if (response.ratePerUnit)
                            x.costEstimateData.ratePerUnit = Number(response.ratePerUnit);

                        if (response.margin)
                            x.costEstimateData.margin = response.margin;

                        if (response.rounding)
                            x.costEstimateData.rounding = response.rounding;

                        if (response.notes)
                            x.costEstimateData.notes = response.notes;
                    }

                    x.checkboxSelected = false;

                });

                vm.costEstimateBulkStatus.isIndeterminate = false;
                vm.costEstimateBulkStatus.selectAllCheckboxState = false;

            });
        }

        vm.launchVariableSettingsUpdateAllConfrimation = function () {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Update All";
            modalScope.confirmationText = "Are you sure you want to update all Home Design's Variable Settings?";
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (confirmed) {
                if (confirmed) {
                    standardmodelservice.updateVariableSettingsInAllModels(vm.projectId);
                }
            });
        }

        // - Services Defaults - //

        vm.allServDefsSelected = false;
        vm.anyServDefsSelected = false;

        vm.updateServDefsVisibility = function () { $timeout(() => {
            vm.spaceHeatingRatingVisible = vm.showEnergyRatingForCode(vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating?.serviceTypeCode, vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceHeating);
            vm.spaceCoolingRatingVisible = vm.showEnergyRatingForCode(vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling?.serviceTypeCode, vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spaceCooling);
            vm.swimmingPoolVolumeVisible = vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.swimmingPool?.exists === true;
            vm.swimmingPoolRatingVisible = vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.swimmingPool?.exists === true;
            vm.spaVolumeVisible = vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spa?.exists === true;
            vm.spaRatingVisible = vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.spa?.exists === true;
            vm.photovoltaicCapacityVisible = vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData.photovoltaic?.exists === true;
            vm.updateServDefSelectAllState();
        });}

        vm.updateServDefSelectAllState = function () { $timeout(() => {
            if (vm.spaceHeatingTypeSelected && (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected) && vm.spaceCoolingTypeSelected && (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                && vm.waterHeaterTypeSelected && vm.swimmingPoolExistsSelected && (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                && (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected) && vm.spaExistsSelected && (vm.spaVolumeVisible && vm.spaVolumeSelected) && (vm.spaRatingVisible && vm.spaRatingSelected)
                && vm.photovoltaicExistsSelected && (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)) {

                vm.allServDefsSelected = true;
                vm.anyServDefsSelected = true;

            } else if (vm.spaceHeatingTypeSelected || (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected) || vm.spaceCoolingTypeSelected || (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                || vm.waterHeaterTypeSelected || vm.swimmingPoolExistsSelected || (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                || (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected) || vm.spaExistsSelected || (vm.spaVolumeVisible && vm.spaVolumeSelected) || (vm.spaRatingVisible && vm.spaRatingSelected)
                || vm.photovoltaicExistsSelected || (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)) {

                vm.allServDefsSelected = false;
                vm.anyServDefsSelected = true;

            } else {
                vm.allServDefsSelected = false;
                vm.anyServDefsSelected = false;
            }
        });}

        vm.selectAllServiceDefaultCheckboxes = function () {
            let toggle = !vm.allServDefsSelected;
            vm.anyServDefsSelected = toggle;
            vm.spaceHeatingTypeSelected = toggle;
            vm.spaceHeatingRatingSelected = toggle;
            vm.spaceCoolingTypeSelected = toggle;
            vm.spaceCoolingRatingSelected = toggle;
            vm.waterHeaterTypeSelected = toggle;
            vm.swimmingPoolExistsSelected = toggle;
            vm.swimmingPoolVolumeSelected = toggle;
            vm.swimmingPoolRatingSelected = toggle;
            vm.spaExistsSelected = toggle;
            vm.spaVolumeSelected = toggle;
            vm.spaRatingSelected = toggle;
            vm.photovoltaicExistsSelected = toggle;
            vm.photovoltaicCapacitySelected = toggle;
        }

        vm.launchServDefsUpdateAllConfrimation = function () {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Update All";
            modalScope.confirmationText = "Updating the Services Defaults Settings will overwrite the existing data for all Home Designs and Variations. Are you sure you want to proceed?";
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (confirmed) {
                let fieldsToCopy = [];
                if (confirmed) {
                    if (vm.spaceHeatingTypeSelected)
                        fieldsToCopy.push("SpaceHeating.ServiceTypeCode");
                    if (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected)
                        fieldsToCopy.push("SpaceHeating.Gems2019Rating");
                    if (vm.spaceCoolingTypeSelected)
                        fieldsToCopy.push("SpaceCooling.ServiceTypeCode");
                    if (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                        fieldsToCopy.push("SpaceCooling.Gems2019Rating");
                    if (vm.waterHeaterTypeSelected)
                        fieldsToCopy.push("WaterHeating.ServiceTypeCode");
                    if (vm.swimmingPoolExistsSelected)
                        fieldsToCopy.push("SwimmingPool.Exists");
                    if (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                        fieldsToCopy.push("SwimmingPool.Volume");
                    if (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected)
                        fieldsToCopy.push("SwimmingPool.Gems2019Rating");
                    if (vm.spaExistsSelected)
                        fieldsToCopy.push("Spa.Exists");
                    if (vm.spaVolumeVisible && vm.spaVolumeSelected)
                        fieldsToCopy.push("Spa.Volume");
                    if (vm.spaRatingVisible && vm.spaRatingSelected)
                        fieldsToCopy.push("Spa.Gems2019Rating");
                    if (vm.photovoltaicExistsSelected)
                        fieldsToCopy.push("Photovoltaic.Exists");
                    if (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)
                        fieldsToCopy.push("Photovoltaic.Capacity");
                    standardmodelservice.updateServicesDefaultsInAllModels(vm.projectId, null, fieldsToCopy, vm.project.energyLabsSettings.variableOptionDefaults.wholeOfHomeDefaultData);
                    // Deselect all checkboxes
                    vm.allServDefsSelected = true;
                    vm.selectAllServiceDefaultCheckboxes();
                    vm.allServDefsSelected = false;
                }
            });
        }

        // Services Defaults
        servicetemplateservice.getServiceTypes().then(data => {
            vm.serviceTypes = data;
            vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                'title',
                vm.serviceTypes
            );
        });

        // - ---------------- - //

        // - ------------------------------------ - //

        // Cost Estimates
        vm.itemCodesForCategory = function (category) {
            return vm.client.clientCostItems.filter(i => i.category == category);
        }
        vm.openVariableSelectModal = function (option) {
            let modalScope = $rootScope.$new();
            modalScope.varRefObj = option.costEstimateData.quantityVarRefJson != null ? JSON.parse(option.costEstimateData.quantityVarRefJson) : null;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/zone-summary-variable-select-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(newVarRefObj => {
                option.costEstimateData.quantityVarRefJson = JSON.stringify(newVarRefObj);
                vm.updateCostEstimateQuantities();
            });
        }
        vm.launchCostEstimatesUpdateAllConfrimation = function () {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Update All";
            modalScope.confirmationText = "Updating the Cost Estimate Settings will overwrite the existing data for all Home Designs and Variations. Are you sure you want to proceed?";
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (confirmed) {
                if (confirmed) {
                    const bulkSelected = vm.flattenedCostData.filter(x => x.checkboxSelected);
                    standardmodelservice.updateCostEstimatesInAllModels(vm.projectId, bulkSelected);
                    bulkSelected.forEach(x => x.checkboxSelected = false );
                    vm.costEstimateBulkStatus.isIndeterminate = false;
                    vm.costEstimateBulkStatus.selectAllCheckboxState = false;
                }
            });
        }
        vm.costItemChanged = function (row, costItem) {
            // Link may be broken when come back to page if the parent Cost Item has changed category
            if (costItem == null || costItem.category != row.costEstimateData.category) {
                row.costEstimateData.costItemId = null;
            }
            row.costEstimateData.unitOfMeasure = costItem.unitOfMeasure;
            row.costEstimateData.ratePerUnit = costItem.ratePerUnit;
            row.costEstimateData.margin = costItem.margin;
            row.costEstimateData.rounding = costItem.rounding;
            row.costEstimateData.notes = costItem.notes;
        }

        // Update Quantity values from variable references
        vm.updateCostEstimateQuantities = function () {
            vm.flattenedCostData.forEach(option => {
                if (option.costEstimateData.quantityVarRefJson != null && option.costEstimateData.quantityVarRefJson != "") {
                    option.costEstimateData.quantity = null;
                }
            });
        }

        vm.cloneDesignInsight = function(insight) {
            // Legit just copying the notes.
            const index =  vm.project.variableMetadata.designInsights.indexOf(insight);
            vm.project.variableMetadata.designInsights.splice(index + 1, 0,{ notes: insight.notes });
        }

        vm.launchDesignInsightBulkEdit = async function() {

            let modalScope = $rootScope.$new();

            // Modal Inputs
            modalScope.options = vm.project.variableOptions;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-design-insight-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {

                const bulkSelected = vm.project.variableMetadata.designInsights.filter(x => x.checkboxSelected);

                bulkSelected.forEach(x => {

                    x.checkboxSelected = false;

                    if (response.bulkEditAction === "DELETE") {
                        vm.removeDesignInsight(x);
                        return;
                    }

                    if (response.bulkEditAction === "COPY") {
                        vm.cloneDesignInsight(x);
                        return;
                    }

                    if (response.climateZones)
                        x.climateZones = response.climateZones;

                    if (response.northOffsets)
                        x.northOffsets = response.northOffsets;

                    if (response.notes)
                        x.notes = response.notes;

                });

                vm.designInsightsBulkStatus.isIndeterminate = false;
                vm.designInsightsBulkStatus.selectAllCheckboxState = false;

            });
        }

        // Click on Model row
        vm.modelRowClick = function (modelId) {
            vm.navigateAttempt('standard-model-updateform', { standardHomeModelId: modelId });
        }

        // Attempt to navigate
        vm.navigateAttempt = async function (page, params) {
            if (vm.hasChanges()) {
                let modalScope = $rootScope.$new();
                modalScope.title = vm.project.projectName;
                modalScope.bodyText = `You have unsaved changes for "${vm.project.projectName}". Would you like to save before navigating?`;
                modalScope.buttons = [{
                    isPrimary: true,
                    text: "Save",
                    onClick: () => vm.save(() => $state.go(page, params))
                },{
                    text: "Don't Save",
                    onClick: () => $state.go(page, params)
                },{
                    isCancel: true,
                    text: "Cancel"
                }];
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                });
            } else {
                $state.go(page, params);
            }
        }

        vm.serviceTypesForCategoryCode = (code) => servicetemplateservice.serviceTypesForCategoryCode(code, vm.serviceTypes);

        vm.heatingSystemTypesForCategoryCode = (code) => servicetemplateservice
          .heatingSystemTypesForCategoryCode(code, vm.heatingSystemTypes);

        vm.showEnergyRatingForCode = wholeofhomeservice.showEnergyRatingForCode;

        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.roundUpInt = common.roundUpInt;

        vm.keyToName = standardmodelservice.keyToName;
    }
})();