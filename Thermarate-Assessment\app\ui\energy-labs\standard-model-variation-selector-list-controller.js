(function () {

    'use strict';
    angular.module('app').component('standardModelVariationSelectorList', {
        bindings: {
            variationOfId: '<',
            excludeVariationId: '<',
            variationOptionsList: '<',
            variationOptionsSettings: '<',
            selectedVariationId: '=' // So parent get the Variation selected
        },
        templateUrl: 'app/ui/energy-labs/standard-model-variation-selector-list.html',
        controller: standardModelListController,
        controllerAs: 'vm'
    });

    standardModelListController.$inject = ['$rootScope', '$scope', '$mdDialog', 'bootstrap.dialog', 'standardmodelservice', 'projectservice', 'daterangehelper', 'common'];

    function standardModelListController($rootScope, $scope, $mdDialog, modalDialog, standardmodelservice, projectservice, daterangehelper, common) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;
        vm.isBusy = true;
        vm.modelVariationList = [];
        vm.listFilter = "";

        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;

        var saveTableState = null;
        var persistRangeName = "standardModelList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Refresh List
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        // Call Server
        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            vm.isBusy = true;

            if (tableState != null) {
                saveTableState = tableState;
            }

            let pagination = saveTableState?.pagination;
            let start = pagination.start || 0;       // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            let pageSize = pagination.number || 100; // Number of entries showed per page.
            let pageIndex = (start / pageSize) + 1;

            let sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }

            let filter = null;

            standardmodelservice.getListCancel();
            standardmodelservice.getModelVariationsQuery(
                vm.variationOfId,
                vm.listFilter,
                pageSize,
                pageIndex,
                sort,
                filter
            ).then(
                result => {
                    if (result != null) {
                        vm.currentFilter = standardmodelservice.currentFilter();
                        vm.modelVariationList = result.data.filter(m => m.standardHomeModelId != vm.excludeVariationId);
                        vm.passedUpModels = vm.modelVariationList;
                        vm.totalRecords = result.total-1; // Account for excluded Variation
                        saveTableState.pagination.numberOfPages = Math.ceil((result.total-1) / pageSize); // Set the number of pages so the pagination can updateA
                        vm.showingFromCnt = vm.modelVariationList.length > 0 ? start + 1 : 0;
                        vm.showingToCnt = start + vm.modelVariationList.length;
                        vm.isBusy = false;
                    }
                },
                error => vm.isBusy = false
            );
        };

        // Select Model
        vm.selectHomeModel = function (variation) {
            vm.modelVariationList.forEach(x => x.selected = false);
            variation.selected = true;
            vm.selectedVariationId = variation.standardHomeModelId;
        }
    }
  })();