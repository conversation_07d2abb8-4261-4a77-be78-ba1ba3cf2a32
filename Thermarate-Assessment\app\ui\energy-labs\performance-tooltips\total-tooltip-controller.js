(function () {

  'use strict';
  angular
    .module('app')
    .component('elTotalTooltip', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
      },
      templateUrl: 'app/ui/energy-labs/performance-tooltips/total-tooltip.html',
      controller: ElTotalTooltipController,
      controllerAs: 'vm'
    });

  ElTotalTooltipController.$inject = ['common'];

  function ElTotalTooltipController(common) {

    let vm = this;

    vm.lessThan = common.lessThanOrEqualish;
  }

})();