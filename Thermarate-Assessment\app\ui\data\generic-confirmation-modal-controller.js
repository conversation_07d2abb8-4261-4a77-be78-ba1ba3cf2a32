
(function () {
    'use strict';
    var controllerId = 'ConfirmationModalCtrl';
    angular.module('app')
    .controller(controllerId, ['$scope', '$mdDialog', genericFilterModalController]);
    function genericFilterModalController($scope, $mdDialog) {
        var vm = this;

        vm.confirmationHeader = $scope.confirmationHeader;
        vm.confirmationText = $scope.confirmationText;

        vm.confirm = function () {
            $mdDialog.hide(true);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }
    }
})();