<section id="assessmentdrawing-list-view" class="main-content-wrapper" data-ng-controller="AssessmentdrawingListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Assessmentdrawing"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.assessmentdrawingList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="assessmentdrawingList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="assessmentSealedExhaustFansDetails" class="can-sort text-left">Assessment</th>
                        <th st-sort="drawingNumber" class="can-sort text-right">Drawing Number</th>
                        <th st-sort="drawingDescription" class="can-sort text-left">Drawing Description</th>
                        <th st-sort="attachmentDisplayName" class="can-sort text-left">Attachment</th>
                        <th st-sort="sheetNumber" class="can-sort text-right">Sheet Number</th>
                        <th st-sort="pageSize" class="can-sort text-right">Size</th>
                        <th st-sort="revision" class="can-sort text-left">Revision</th>
                        <th st-sort="revisionDate" class="can-sort text-left">Revision Date</th>
                        <th st-sort="isIncludedInReport" class="can-sort text-center">Included In Report</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.assessmentdrawingList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="assessmentdrawing-updateform({ assessmentDrawingId: row.assessmentDrawingId})">Select</md-button>  </td>
                        <td data-title="Assessment" class="text-left">{{::row.assessmentSealedExhaustFansDetails }}</td>
                        <td data-title="Drawing Number" class="text-right">{{::row.drawingNumber | number }}</td>
                        <td data-title="Drawing Description" class="text-left">{{::row.drawingDescription }}</td>
                        <td data-title="Attachment" class="text-left">{{::row.attachmentDisplayName }}</td>
                        <td data-title="Sheet Number" class="text-right">{{::row.sheetNumber | number }}</td>
                        <td data-title="Size" class="text-right">{{row.pageSize!= null?'A'+row.pageSize:''}}</td>
                        <td data-title="Revision" class="text-left">{{::row.revision }}</td>
                        <td data-title="Revision Date" class="text-left">{{::row.revisionDate | date: 'dd/MM/yyyy' }}</td>
                        <td data-title="Included In Report" class="text-center"><span ng-bind-html='row.isIncludedInReport | tickCross'></span></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="9" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
