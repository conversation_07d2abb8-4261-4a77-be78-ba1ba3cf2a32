﻿
using NetTopologySuite.IO;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.SqlServer.Types;
using System.Data.SqlTypes;
using System.Text;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts data from .dbf and .shp files within a given folder and inserts them into the desired Database.
    /// </summary>
    public class NccClimateZoneExtractorGeoJson
    {
        private SqlConnection connection;
        private string directory;
        private Logger logger;

        // To prevent data lose in the case of extraction failure, we switch between table A and B
        private const int SPATIAL_REFERENCE_IDENTIFIER = 4326;

        private const string DATA_TABLE = "RSS_NccClimateZoneData";

        public NccClimateZoneExtractorGeoJson(string directory, string connectionString, LogCallback logCallback = null)
        {
            this.directory = directory;

            connection = new SqlConnection(connectionString);
            connection.Open();

            logger = new Logger(logCallback);

            SqlServerTypes.Utilities.LoadNativeAssemblies(AppDomain.CurrentDomain.BaseDirectory);
        }

        public void Dispose()
        {
            if(connection != null)
            {
                connection.Close();
                connection.Dispose();
            }
        }

        /// <summary>
        /// Run the entire dbf -> SQL extraction process. Handles dropping and creating tables, and deleting duplicate entries.
        /// 
        /// Caller must still dispose of the NccClimateZoneExtractor.
        /// </summary>
        public string RunFullProcess()
        {
            try
            {
                if (this.NewDatasetIsAvailable())
                {
                    this.DropExtractionTable();
                    this.CreateExtractionTable();
                    this.Extract();
                    logger.Log("Full extraction of new NCC Climate Zone dataset completed without errors.");
                    return "NCC Climate Zone data extracted successfully.";
                }
                else
                {
                    logger.Log("Failed to Extract Dataset! Reason: No new Dataset available!");
                    return "Failed to Extract Dataset! Reason: No new Dataset available!";
                }
            }
            catch (Exception e)
            {
                throw e;
            }      
        }

        public bool NewDatasetIsAvailable()
        {
            string[] files = Directory.GetFiles(directory);
            var csvFiles = new List<string>();

            foreach (string f in files)
            {
                if (f.EndsWith(".geojson"))
                    csvFiles.Add(f);
            }

            return csvFiles.Count > 0;
        }

        /// <summary>
        /// Drops the extraction table if it already exists. Otherwise does nothing.
        /// </summary>
        public void DropExtractionTable()
        {
            // Note: For now I am just dropping the entire table - this makes it easier in future if Alistair asks for more 
            // info to be extracted or w/e from the DB we don't have to worry about schema updates and so on - we simply modify
            // the creation method and voila.
            try
            {
                SqlCommand cmd = new SqlCommand($"DROP TABLE dbo.{DATA_TABLE};", connection);
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                logger.Log($"DROP TABLE dbo.{DATA_TABLE} failed with {ex}");
            }
        }

        /// <summary>
        /// Creates the initial table within the database.
        /// </summary>
        public void CreateExtractionTable()
        {
            // Create address table (Derived from .dbf file)
            SqlCommand cmd = new SqlCommand($@"
                CREATE TABLE dbo.{DATA_TABLE}(

                    -- Main data inc geometry
	                NccClimateZoneDataId int IDENTITY(1, 1) PRIMARY KEY NOT NULL,	-- This is our internal ID
	
	                Boundary			geography,               -- Special geography column
                    BoundaryString		AS Boundary.STAsText(),

                    -- Misc. data that might be useful at some point
	                ObjectId		int,
	                Climate			int,
	                LgaCode20		nvarchar(100) NULL,
	                LgaName20		nvarchar(100) NULL,
	                SteCode16		nvarchar(100) NULL,
	                SteName16		nvarchar(100) NULL,
	                Elevation	    decimal,
                    FeatRel			DATETIME NULL,
	                AttrRel			DATETIME NULL,
	                PlanAcc			int,
	                [Source]		nvarchar(200) NULL
                );", connection);

            cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Extract and parse the given dbfFiles. Can optionally only parse a given number (Used for testing only);
        /// </summary>
        /// <param name="totalCount">OPTIONAL: Limit total rows to count for testing. Mainly for debugging.</param>
        public void Extract()
        {
            logger.Log($"Ncc Climate Zone Data Extraction started to table: {DATA_TABLE}");

            // Define our bulk insert data
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("Boundary", typeof(SqlGeography));
            dataTable.Columns.Add("ObjectId", typeof(int));
            dataTable.Columns.Add("Climate", typeof(int));
            dataTable.Columns.Add("LgaCode20", typeof(string));
            dataTable.Columns.Add("LgaName20", typeof(string));
            dataTable.Columns.Add("SteCode16", typeof(string));
            dataTable.Columns.Add("SteName16", typeof(string));
            dataTable.Columns.Add("Elevation", typeof(double));
            dataTable.Columns.Add("FeatRel", typeof(DateTime));
            dataTable.Columns.Add("AttrRel", typeof(DateTime));
            dataTable.Columns.Add("PlanAcc", typeof(int));
            dataTable.Columns.Add("Source", typeof(string));

            // Create object of SqlBulkCopy which help to insert  
            SqlBulkCopy bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.BulkCopyTimeout = 180; // 3 Minutes

            // Assign and map our data table to our DB table
            bulkCopy.DestinationTableName = $"dbo.{DATA_TABLE}";
            bulkCopy.ColumnMappings.Add("Boundary",     "Boundary");
            bulkCopy.ColumnMappings.Add("ObjectId",     "ObjectId");
            bulkCopy.ColumnMappings.Add("Climate",      "Climate");
            bulkCopy.ColumnMappings.Add("LgaCode20",    "LgaCode20");
            bulkCopy.ColumnMappings.Add("LgaName20",    "LgaName20");
            bulkCopy.ColumnMappings.Add("SteCode16",    "SteCode16");
            bulkCopy.ColumnMappings.Add("SteName16",    "SteName16");
            bulkCopy.ColumnMappings.Add("Elevation",    "Elevation");
            bulkCopy.ColumnMappings.Add("FeatRel",      "FeatRel"); 
            bulkCopy.ColumnMappings.Add("AttrRel",      "AttrRel");
            bulkCopy.ColumnMappings.Add("PlanAcc",      "PlanAcc");
            bulkCopy.ColumnMappings.Add("Source",       "Source");

            var geojsonFiles = new List<string>();

            // Find all related .dbf and .shp files within the given folder.
            string[] files = Directory.GetFiles(directory);

            foreach (string f in files)
            {
                if (f.EndsWith(".geojson"))
                    geojsonFiles.Add(f);
            }

            // Certain indices in this dataset are "corrupt" in that the order that the "handedness"
            // of the data within these polygons is the reverse of every other index. Basically, for
            // these indexes, we do NOT wish to reverse the polygon geometry.
            // We have to rely on our own index as their is no uniquely identify property within the dataset...
            var corruptIndexes = new List<int> { 4425, 4482, 4683, 4788, 4895, 4904, 4908, 4934, 5056 };

            // Loop through each shapefile and retreive the data from them and the corresponding .dbf as needed.
            foreach (string file in geojsonFiles)
            {
                logger.Log($"Extraction started for dbf file: {file}");

                var rawJson = File.ReadAllText(file);
                var geoJsonRoot = JsonConvert.DeserializeObject<GeoJsonRoot>(rawJson);

                dataTable.Clear(); // Clear previous geojson rows in table.

                int jsonIndex = 0; // DO NOT CHANGE THIS

                foreach (Feature f in geoJsonRoot.Features)
                {
                    jsonIndex++; // NOTE: Do not move this without updating which indices will be corrupt. First entry is always 1

                    StringBuilder rawGeometry = new StringBuilder();

                    if (f.Geometry.Coordinates.Count == 1)
                    {
                        // There is only a single polygon boundary
                        // Example format:
                        // STGeomFromText('POLYGON ((0 0, 150 0, 150 150, 0 150, 0 0))'_
                        rawGeometry.Append("POLYGON ((");

                        if(!corruptIndexes.Contains(jsonIndex))
                            f.Geometry.Coordinates[0].Reverse();

                        foreach (var p in f.Geometry.Coordinates[0])
                        {
                            rawGeometry.Append($"{p[0]} {p[1]},");
                        }

                        rawGeometry = rawGeometry.Remove(rawGeometry.Length - 1, 1); // Remove trailing ','
                        rawGeometry.Append("))");

                    } 
                    else
                    {
                        // There are possibly intersecting or 'hollow' boundary shapes.
                        // Example format:
                        // MULTIPOLYGON(((2 0, 3 1, 2 2, 1.5 1.5, 2 1, 1.5 0.5, 2 0)),
                        //             ((1 0, 1.5 0.5, 1 1, 1.5 1.5, 1 2, 0 1, 1 0)))
                        rawGeometry.Append("MULTIPOLYGON(");

                        foreach (var boundary in f.Geometry.Coordinates)
                        {
                            rawGeometry.Append("((");
                            boundary.Reverse();
                            foreach (var p in boundary)
                            {
                                rawGeometry.Append($"{p[0]} {p[1]},");
                            }

                            rawGeometry = rawGeometry.Remove(rawGeometry.Length - 1, 1); // Remove inner trailing ','
                            rawGeometry.Append(")),");
                        }

                        rawGeometry = rawGeometry.Remove(rawGeometry.Length - 1, 1); // Remove outer trailing ','
                        rawGeometry.Append(")");
                    }
                    

                    SqlChars chars = new SqlChars(new SqlString(rawGeometry.ToString()));

                    var sqlgeo = SqlGeography.STGeomFromText(chars, SPATIAL_REFERENCE_IDENTIFIER);
                    // Close any open polygons - No need to RE-reorient.
                    sqlgeo = sqlgeo.MakeValid();        

                    DataRow dr = dataTable.NewRow();
                    dr["Boundary"] = sqlgeo;
                    dr["ObjectId"] = int.Parse(f.Properties.ObjectID);
                    dr["Climate"] = int.Parse(f.Properties.Climate);
                    dr["LgaCode20"] = f.Properties.LgaCode20;
                    dr["LgaName20"] = f.Properties.LgaName20;
                    dr["SteCode16"] = f.Properties.SteCode16;
                    dr["SteName16"] = f.Properties.SteName16;
                    dr["Elevation"] = double.Parse(f.Properties.Elevation);
                    dr["FeatRel"] = DBNull.Value; // DateTime.Parse(f.Properties.FeatRel);
                    dr["AttrRel"] = DBNull.Value; // DateTime.Parse(f.Properties.AttrRel);
                    dr["PlanAcc"] = int.Parse(f.Properties.PlanAcc);
                    dr["Source"] = f.Properties.Source;

                    dataTable.Rows.Add(dr);

                    Debug.WriteLine("Processed index:" + jsonIndex);     
                }

                // Bulk write data to sql server.
                try
                {
                    bulkCopy.WriteToServer(dataTable);
                } 
                catch (Exception e)
                {
                    logger.Log($"Exception occured while writing to SQL table. Exception: {e.Message}. Stack: {e.StackTrace}");                 
                }
            }

            // Apply custom indexes to address database.
            // Insert the given row into our DB.
            logger.Log($"Finished row extraction and insertion. Creating indexes.");

            // Create custom spatial index. I think it uses a quadtree behind the scenes.
            var createIndexCmd = new SqlCommand(
                $@" CREATE SPATIAL INDEX si_RSS_NccClimateZoneData_Boundary
                    ON dbo.{DATA_TABLE}(Boundary);",
                connection);

            createIndexCmd.CommandTimeout = 120; // 2 Minute timeout, should take ~20 seconds.
            createIndexCmd.ExecuteNonQuery();

            logger.Log($"Finished creating indexes.");
        }
    }
}
