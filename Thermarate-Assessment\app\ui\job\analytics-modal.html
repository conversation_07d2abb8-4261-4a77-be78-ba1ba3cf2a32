<md-dialog ng-controller="AnalyticsCtrl as vm"
           style="min-width: 800px; width: 800px;">
    <form name="newclientform">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Thermal Performance Data Analytics</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancel()">
                    <i class="material-icons">clear</i>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content layout="row" layout-padding layout-wrap>
            <md-card flex="100">
                <md-card-content>
                    <div layout-xs="row" layout-wrap>

                        <!-- Certification -->
                        <md-input-container class="md-block vertically-condensed analytics-dropdown"
                                            flex-gt-sm>
                            <label>Certification</label>
                            <md-select name="certification"
                                       ng-model="vm.certification"
                                       ng-change="vm.refresh()"
                                       style="margin-bottom:30px;">
                                <md-option ng-value="item"
                                           ng-repeat="item in vm.certificationList track by item.id">
                                    {{item.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Assessment Method -->
                        <md-input-container class="md-block vertically-condensed analytics-dropdown"
                                            flex-gt-sm>
                            <label>Assessment Method</label>
                            <md-select name="preliminaryComplianceMethodCode"
                                       ng-model="vm.assessmentMethod"
                                       ng-change="vm.refresh()"
                                       style="margin-bottom:5px;">
                                <md-option ng-value="item"
                                           ng-repeat="item in vm.availableComplianceMethods track by item.code">
                                    {{item.description}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Include version 1 assessments only -->
                        <div style="display:flex; justify-content:space-between; margin-bottom:-20px;">
                            <label style="margin:auto 0">Include version 1 assessments only</label>
                            <md-switch ng-model="vm.version1Only"
                                       ng-change="vm.refresh()">
                            </md-switch>
                        </div>

                        <!-- Include virtual simulations -->
                        <div style="display:flex; justify-content:space-between; margin-bottom:-20px;">
                            <label style="margin:auto 0">Include virtual simulations</label>
                            <md-switch ng-model="vm.includeVirtualSims"
                                       ng-change="vm.refresh()">
                            </md-switch>
                        </div>

                    </div>
                </md-card-content>
            </md-card>
            <md-card flex="100">
                <md-card-content>
                    <div class="heading">Summary</div>
                    <table class="table table-striped table-hover table-condensed analytics-table">
                        <thead>
                            <tr>
                                <th width="135px"></th>
                                <th width="100px" class="text-center">Heating (MJ/m<sup>2</sup>)</th>
                                <th width="100px" class="text-center">Cooling (MJ/m<sup>2</sup>)</th>
                                <th width="100px" class="text-center">{{vm.assessmentMethod.code == "CMPerfELL" ? "Thermal" : "Total"}} (MJ/m<sup>2</sup>)</th>
                                <th width="100px" class="text-center" ng-if="vm.showHER">Energy Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-left">Mean</td>
                                <td class="text-center">{{vm.calcs.heatingMean.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingMean.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalMean.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingMean != null ? vm.calcs.energyRatingMean.toFixed(2) : '0.00'}}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Median</td>
                                <td class="text-center">{{vm.calcs.heatingMedian.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingMedian.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalMedian.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingMedian != null ? vm.calcs.energyRatingMedian.toFixed(2) : '0.00'}}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Standard Deviation</td>
                                <td class="text-center">{{vm.calcs.heatingStandardDeviation.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingStandardDeviation.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalStandardDeviation.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingStandardDeviation != null ? vm.calcs.energyRatingStandardDeviation.toFixed(2) : '0.00'}}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Worst Performing</td>
                                <td class="text-center">{{vm.calcs.heatingWorstPerforming.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingWorstPerforming.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalWorstPerforming.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingWorstPerforming != null ? vm.calcs.energyRatingWorstPerforming.toFixed(2) : '0.00'}}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Best Performing</td>
                                <td class="text-center">{{vm.calcs.heatingBestPerforming.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingBestPerforming.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalBestPerforming.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingBestPerforming != null ? vm.calcs.energyRatingBestPerforming.toFixed(2) : '0.00'}}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Range</td>
                                <td class="text-center">{{vm.calcs.heatingRange.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.coolingRange.toFixed(2)}}</td>
                                <td class="text-center">{{vm.calcs.totalRange.toFixed(2)}}</td>
                                <td class="text-center" ng-if="vm.showHER">{{vm.calcs.energyRatingRange != null ? vm.calcs.energyRatingRange.toFixed(2) : '0.00'}}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="footer-size-text" ng-show="vm.calcs != null">Sample size = {{vm.calcs.listTotal}} {{vm.calcs.listTotal == 1 ? 'job.' : 'jobs.'}}</div>
                </md-card-content>
            </md-card>
            <md-card style="width:100%;">
                <md-card-content>
                    <div class="heading">Assessment Data</div>
                    <div class="jobs-table-container" ng-class="{'account-for-scrollbar': vm.calcs.listTotal > 4}">
                        <table class="table table-striped table-hover table-condensed analytics-table"
                                st-table="vm.calcs.jobsList"
                                st-pipe="vm.refresh">
                            <thead>
                                <tr>
                                    <th st-sort="clientJobNumber"             width="135px" class="text-left can-sort">Job Number</th>
                                    <th st-sort="heating"                     width="100px" class="text-center can-sort">Heating (MJ/m<sup>2</sup>)</th>
                                    <th st-sort="cooling"                     width="100px" class="text-center can-sort">Cooling (MJ/m<sup>2</sup>)</th>
                                    <th st-sort="totalEnergyLoad"             width="100px" class="text-center can-sort">{{vm.assessmentMethod.code == "CMPerfELL" ? "Thermal" : "Total"}} (MJ/m<sup>2</sup>)</th>
                                    <th st-sort="calculatedHouseEnergyRating" width="100px" class="text-center can-sort" ng-if="vm.showHER">Energy Rating</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr ng-repeat="row in vm.calcs.jobsList">
                                    <td class="text-left job-reference-cell">
                                        {{::row.clientJobNumber }}
                                        <md-tooltip md-direction="right" class="solid-popup" style="border: 1px solid #b6b6b6; font-size: 1.2rem;">
                                            <table>
                                                <tr> <td>House Type            </td><td style="padding-left: 16px"><b>{{::row.assessmentDesign != null ? row.assessmentDesign : 'Not Specified'}}    </b></td></tr>
                                                <tr> <td>Building Description  </td><td style="padding-left: 16px"><b>{{::row.projectDescriptionDescription != null ? row.projectDescriptionDescription : 'Not Specified'}}    </b></td></tr>
                                                <tr> <td>Lot Width (m)         </td><td style="padding-left: 16px"><b>{{::row.assessmentProjectDetailLotWidth != null ? row.assessmentProjectDetailLotWidth : 'Not Specified'}}</b></td></tr>
                                                <tr> <td>Garage                </td><td style="padding-left: 16px"><b>{{::row.garageLocation != null ? row.garageLocation : 'Not Specified'}}                                  </b></td></tr>
                                                <tr> <td>Covered Outdoor Living</td><td style="padding-left: 16px"><b>{{::row.outdoorLivingLocation != null ? row.outdoorLivingLocation : 'Not Specified'}}                    </b></td></tr>
                                                <tr> <td>NatHERS Climate Zone  </td><td style="padding-left: 16px"><b>{{::row.natHersClimateZone != null ? row.natHersClimateZone : 'Not Specified'}}                          </b></td></tr>
                                                <tr> <td>North Offset (&deg;)  </td><td style="padding-left: 16px"><b>{{::row.northOffset != null ? row.northOffset : 'Not Specified'}}                                        </b></td></tr>
                                            </table>
                                        </md-tooltip>
                                    </td>
                                    <td class="text-center">{{::row.heating.toFixed(2) }}</td>
                                    <td class="text-center">{{::row.cooling.toFixed(2) }}</td>
                                    <td class="text-center">{{::row.totalEnergyLoad.toFixed(2) }}</td>
                                    <td class="text-center" ng-if="vm.showHER">{{::row.calculatedHouseEnergyRating.toFixed(2) }}</td>
                                </tr>
                                <tr ng-hide="vm.calcs.jobsList.length || vm.isBusy">
                                    <td colspan="{{4 + (vm.showHER ? 1 : 0)}}" class="empty-list-message">No jobs found</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div ng-show="vm.showingToCnt < vm.calcs.listTotal"
                         style="width:max-content; margin:auto; margin-top: 10px; margin-bottom: 10px;"
                         class="clickable show-more-text"
                         ng-click="vm.getMoreResults()">
                        Show more
                    </div>
                    <span ng-if="vm.calcs.jobsList.length > 0" class="footer-size-text add-margin">Showing 1 - {{vm.calcs.listTotal > vm.showingToCnt ? vm.showingToCnt : vm.calcs.listTotal}} of {{vm.calcs.listTotal}}</span>
                </md-card-content>
            </md-card>
        </md-dialog-content>
    </form>
</md-dialog>
<style>

    .analytics-dropdown md-select {
        border-bottom: 1px solid grey;
    }

    .heading {
        font-size: 14px;
        font-weight: bold;
    }

    .jobs-table-container {
        height: 265px;
        width: 100%;
        overflow-y: auto;
        margin-bottom: 15px;
    }

    .jobs-table-container.account-for-scrollbar {
        width: 103%;
    }

    .analytics-table .text-center {
        text-align: center !important;
    }

    .analytics-table th {
        position: sticky;
        top: 0;
        background-color: white;
        border: none !important;
        white-space: nowrap;
    }

    .analytics-table th.can-sort {
        cursor: pointer;
    }

    .analytics-table td {
        padding: 11px 20px !important;
    }

    .job-reference-cell {
        font-weight: bold;
    }

    .footer-size-text {
        margin: 30px 0px 10px 21px;
        color: rgba(0,0,0,0.87);
        font-size: 1.2rem;
    }

    .footer-size-text.add-margin {
        margin-top: 10px;
    }

</style>