// Check ui code version matches latest server version available.
// Check is done every 10 minutes, and a warning toast displayed if a newer version is available.
(function () {
    'use strict';
    var serviceId = 'versioncheckerservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', '$interval', versioncheckerservice]);

    function versioncheckerservice(common, config, $http, $interval) {
        var $q = common.$q;
        var log = common.logger;
        var currentVersion = null;

        var service = {
            /* These are the operations that are available from this service. */
            initialise: initialise,
        };

        return service;

        //#region main application operations
        // ----------------------------------

        function initialise() {

            var intervalMinutes = 10 * 60 * 1000; // Check every 10 minutes
            $interval(function () {
                checkVersion();
            }, intervalMinutes);
            
        } // end initialise()


        function checkVersion() {
            var wkUrl = config.servicesUrlPrefix + 'SystemConfig/GetInfo';
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
                var systemInfo = resp.data;
                if (currentVersion == null) {
                    currentVersion = systemInfo.version;
                }

                if (currentVersion != systemInfo.version) {
                    log.logWarning("A newer version of this app is now available.  Please refresh your browser.", null, null, true);
                }

            }
            function fail(error) {
                var msg = "Error getting system information: " + error;
                log.logError(msg, error, null, false); // only writes to log - no toast
            }
        }
    }



})();