USE [thermarate];

SELECT [userRole].[UserId]
      ,[user].[UserName] [__User]
      ,[userRole].[RoleId]
      ,[role].[Name] [__Role]
FROM [dbo].[AspNetUserRoles] [userRole]
INNER JOIN [dbo].[AspNetUsers] [user] ON [userRole].[UserId] = [user].[Id]
INNER JOIN [dbo].[AspNetRoles] [role] ON [userRole].[RoleId] = [role].[Id]
WHERE 1=1
	AND [user].[Id] IN (
        '6BD08875-CA99-4952-9FA3-6B47F9D530BE'
        -- 'ac3d9346-0882-4545-9ed7-37d381c23b2e',
        -- 'e3be5247-40ad-4a92-9a16-3851fa789ae9',
        -- 'FDC51DAD-554C-4455-AF14-0AB53F4B20DC'
    )
ORDER BY [role].[Name]

-- Give User all Roles
-- INSERT INTO [dbo].[AspNetUserRoles]
-- ([UserId], [RoleId])
-- SELECT '6BD08875-CA99-4952-9FA3-6B47F9D530BE', [role].[Id]
-- FROM [dbo].[AspNetRoles] [role]
-- WHERE NOT EXISTS (
--     SELECT 1 
--     FROM [dbo].[AspNetUserRoles] [userRole]
--     WHERE [userRole].[UserId] = '6BD08875-CA99-4952-9FA3-6B47F9D530BE'
--       AND [userRole].[RoleId] = [role].[Id]
-- );