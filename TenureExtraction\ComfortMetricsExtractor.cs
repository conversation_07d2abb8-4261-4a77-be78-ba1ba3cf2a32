﻿using System;
using System.Collections.Generic;
using System.IO;

using OfficeOpenXml;

namespace TenureExtraction
{
    public class ComfortMetricExtractor
    {

        // NON-0 Indexed values
        private const int CLIMATE_ZONE_COLUMN = 1;
        private const int COOLING_SET_POINT_COLUMN = 2;
        private const int COOLING_HDH_COLUMN = 3;
        private const int COOLING_CDH_COLUMN = 4;
        private const int COOLING_DGH_COLUMN = 5;
        private const int COOLING_TEMP_RANGE_COLUMN = 6;
        
        private const int START_ROW = 2;
        private const int END_ROW = 70;
        
        public static Dictionary<int, ComfortMetricDto> Extract(string pathToExcelFile, string sheetName)
        {
            var matrix = new Dictionary<int, ComfortMetricDto>();

            FileInfo existingFile = new FileInfo(pathToExcelFile);
            using (ExcelPackage package = new ExcelPackage(existingFile))
            {
                ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];

                for (int row = START_ROW; row <= END_ROW; row++)
                {

                    var climateZone = Convert.ToInt32(worksheet.Cells[row, CLIMATE_ZONE_COLUMN].Value);
                    var coolingSetPoint = Convert.ToDecimal(worksheet.Cells[row, COOLING_SET_POINT_COLUMN].Value);
                    var hdh = Convert.ToDecimal(worksheet.Cells[row, COOLING_HDH_COLUMN].Value);
                    var cdh = Convert.ToDecimal(worksheet.Cells[row,COOLING_CDH_COLUMN].Value);
                    var dgh = Convert.ToDecimal(worksheet.Cells[row, COOLING_DGH_COLUMN].Value);
                    var coolingTempRange = Convert.ToDecimal(worksheet.Cells[row, COOLING_TEMP_RANGE_COLUMN].Value);
                    
                    ComfortMetricDto comfortMetricsDtoForClimateZone = new ComfortMetricDto(
                        climateZone,
                        coolingSetPoint,
                        hdh,
                        cdh,
                        dgh,
                        coolingTempRange);

                    matrix.Add(climateZone, comfortMetricsDtoForClimateZone);
                }
            }

            return matrix;
        }
    }



    public class ComfortMetricDto
    {
        /// <summary>
        /// The Corresponding NatHERS climate zone.
        /// </summary>
        public int ClimateZone { get; }
        
        public decimal AssumedCoolingThermostatSetPoint { get; }
        
        /// <summary>
        /// Heating Degree Hours
        /// </summary>
        public decimal Hdh { get; }
        
        /// <summary>
        /// Cooling Degree Hours
        /// </summary>
        public decimal Cdh { get; }
        
        /// <summary>
        /// Dehumidification Gram Hours
        /// </summary>
        public decimal Dgh { get; }
        
        public decimal AnnualAverageDailyOutdoorTemperatureRange { get; }

        public ComfortMetricDto(int climateZone, decimal assumedCoolingThermostatSetPoint, decimal hdh, decimal cdh, decimal dgh, decimal annualAverageDailyOutdoorTemperatureRange)
        {
            ClimateZone = climateZone;
            AssumedCoolingThermostatSetPoint = assumedCoolingThermostatSetPoint;
            Hdh = hdh;
            Cdh = cdh;
            Dgh = dgh;
            AnnualAverageDailyOutdoorTemperatureRange = annualAverageDailyOutdoorTemperatureRange;
        }
        
    }

}
