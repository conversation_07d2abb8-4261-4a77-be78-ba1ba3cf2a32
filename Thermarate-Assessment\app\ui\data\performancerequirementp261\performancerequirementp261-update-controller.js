(function () {
    // The Performancerequirementp261UpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'Performancerequirementp261UpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'performancerequirementp261service', performancerequirementp261UpdateController]);
function performancerequirementp261UpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  performancerequirementp261service) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Performance Requirement P261';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.performanceRequirementP261Code = null;
        vm.performancerequirementp261 = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Performance Requirement P261";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.performanceRequirementP261Code = $scope.performanceRequirementP261Code;
            }
            vm.hideActionBar = true;
        } else {
            vm.performanceRequirementP261Code = $stateParams.performanceRequirementP261Code;
        }

        // Get data for object to display on page
        var performanceRequirementP261CodePromise = null;
        if (vm.performanceRequirementP261Code != null) {
            performanceRequirementP261CodePromise = performancerequirementp261service.getPerformanceRequirementP261(vm.performanceRequirementP261Code)
            .then(function (data) {
                if (data != null) {
                    vm.performancerequirementp261 = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("performancerequirementp261-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                performancerequirementp261service.createPerformanceRequirementP261(vm.performancerequirementp261).then(function(data){
                    vm.performancerequirementp261 = data;
                    vm.performanceRequirementP261Code = vm.performancerequirementp261.performanceRequirementP261Code;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                performancerequirementp261service.updatePerformanceRequirementP261(vm.performancerequirementp261).then(function(data){
                    if (data != null) {
                        vm.performancerequirementp261 = data;
                        vm.performanceRequirementP261Code = vm.performancerequirementp261.performanceRequirementP261Code;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            performancerequirementp261service.deletePerformanceRequirementP261(vm.performanceRequirementP261Code).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            performancerequirementp261service.undoDeletePerformanceRequirementP261(vm.performanceRequirementP261Code).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();