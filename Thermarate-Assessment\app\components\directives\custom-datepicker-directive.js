﻿(function () {
    'use strict';

    var app = angular.module('app');
    ////Date validation directive
    app.directive('customDatepicker', function ($compile) {
        return {
            replace: true,
            scope: {
                ngModel: '=',
                dateOptions: '='
            },
            link: function ($scope, $element, $attrs, $controller) {
                var $button = $element.find('button');
                var $input = $element.find('input');
                $button.on('click', function () {
                    if ($input.is(':focus')) {
                        $input.trigger('blur');
                    } else {
                        $input.trigger('focus');
                    }
                });
            }
        };
    });
})();