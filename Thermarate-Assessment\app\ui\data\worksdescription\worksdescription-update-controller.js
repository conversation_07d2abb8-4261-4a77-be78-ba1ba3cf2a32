(function () {
    // The WorksDescriptionUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'WorksDescriptionUpdateCtrl';
    angular.module('app')
        .controller(controllerId,
            ['$rootScope', '$scope', '$mdDialog',
                '$stateParams', '$state', 'worksdescriptionservice', 'security',
                worksDescriptionUpdateController]);

    function worksDescriptionUpdateController($rootScope, $scope, $mdDialog, $stateParams,
            $state, worksdescriptionservice, securityservice) {

        // The model for this form
        var vm = this;

        vm.spinnerOptions = {};
        vm.isBusy = true;

        vm.title = 'Edit Works Description';
        vm.previousRoute = $rootScope.previousState;

        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Works Description";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.worksDescriptionCode = $scope.worksDescriptionCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.worksDescriptionCode = $stateParams.worksDescriptionCode;
        }

        // Get data for object to display on page
        var worksDescriptionCodePromise = null;
        if (vm.worksDescriptionCode != null) {

            worksDescriptionCodePromise = worksdescriptionservice.getWorksDescription(vm.worksDescriptionCode)
            .then(function (data) {
                if (data != null) {
                    vm.worksDescription = data;

                    console.log(data);
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("worksdescription-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                worksdescriptionservice.createWorksDescription(vm.worksDescription).then(function(data){
                    vm.worksDescription = data;
                    vm.worksDescriptionCode = vm.worksDescription.worksDescriptionCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                worksdescriptionservice.updateWorksDescription(vm.worksDescription).then(function(data){
                    if (data != null) {
                        vm.worksDescription = data;
                        vm.worksDescriptionCode = vm.worksDescription.worksDescriptionCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            worksdescriptionservice.deleteWorksDescription(vm.worksDescriptionCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            worksdescriptionservice.undoDeleteWorksDescription(vm.worksDescriptionCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();