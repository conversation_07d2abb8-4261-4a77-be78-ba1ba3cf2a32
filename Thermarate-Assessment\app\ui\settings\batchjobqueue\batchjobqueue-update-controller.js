(function () {
    // The BatchjobqueueUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'BatchjobqueueUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$timeout', '$state',  'frequencyservice', 'batchjobqueueservice', batchjobqueueUpdateController]);
function batchjobqueueUpdateController($rootScope, $scope, $mdDialog, $stateParams, $timeout, $state,  frequencyservice, batchjobqueueservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Batch Job';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.recId = null;
        vm.batchjobqueue = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Batch Job";
            vm.batchjobqueue.requestedDateTimeUtc = new Date();
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.recId = $scope.recId;
            }
            vm.hideActionBar = true;
        } else {
            vm.recId = $stateParams.recId;
        }

        // Get data for object to display on page
        var recIdPromise = null;
        if (vm.recId != null) {
            recIdPromise = batchjobqueueservice.getBatchJobQueue(vm.recId)
            .then(function (data) {
                if (data != null) {
                    vm.batchjobqueue = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.frequencyList = [];
        var frequencyPromise = frequencyservice.getList()
            .then(function (data) {
                vm.frequencyList = data.data;
            });

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("batchjobqueue-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                batchjobqueueservice.createBatchJobQueue(vm.batchjobqueue).then(function(data){
                    vm.batchjobqueue = data;
                    vm.recId = vm.batchjobqueue.recId;
                    vm.cancel();
                });
            }else{
                batchjobqueueservice.updateBatchJobQueue(vm.batchjobqueue).then(function(data){
                    if (data != null) {
                        vm.batchjobqueue = data;
                        vm.recId = vm.batchjobqueue.recId;
                    }
                });
            }
        }

        vm.delete = function () {
            batchjobqueueservice.deleteBatchJobQueue(vm.recId).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            batchjobqueueservice.undoDeleteBatchJobQueue(vm.recId).then(function () {
                vm.cancel();
            });
        }

    }
})();