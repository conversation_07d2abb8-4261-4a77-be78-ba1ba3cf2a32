
<!-- ******** Compliance Cost ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100">
    <label>Compliance Cost</label>
    <div layout="row" layout-wrap class="spacing-above">

        <md-radio-group ng-model="vm.options.complianceCostEnabled"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">
                Yes
            </md-radio-button>
            <md-radio-button ng-value="false">
                No
            </md-radio-button>
        </md-radio-group>

    </div>
</md-input-container>

<br/>

<!-- ******** Energy Explorer ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100">
    <label>Energy Explorer</label>
    <div layout="row" layout-wrap class="spacing-above">

        <md-radio-group ng-model="vm.options.ncc2022ThermPerfEnabled"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">
                Yes
            </md-radio-button>
            <md-radio-button ng-value="false">
                No
            </md-radio-button>
        </md-radio-group>

    </div>
</md-input-container>

<br/>

<!-- ******** Whole-of-Home Explorer ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100">
    <label>Whole-of-Home Explorer</label>
    <div layout="row" layout-wrap class="spacing-above">

        <md-radio-group ng-model="vm.options.ncc2022WohEnabled"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">
                Yes
            </md-radio-button>
            <md-radio-button ng-value="false">
                No
            </md-radio-button>
        </md-radio-group>

    </div>
</md-input-container>

<br/>

<!-- ******** Thermal Performance Data Analytics ******** -->
<md-input-container class="md-block md-input-has-value"
                    flex="100">
    <label>Thermal Performance Data Analytics</label>
    <div layout="row" layout-wrap class="spacing-above">

        <md-radio-group ng-model="vm.options.analyticsEnabled"
                        layout="row"
                        ng-disabled="vm.disabled === true"
                        class="checkbox-radio-padding">
            <md-radio-button ng-value="true">
                Yes
            </md-radio-button>
            <md-radio-button ng-value="false">
                No
            </md-radio-button>
        </md-radio-group>

    </div>
</md-input-container>