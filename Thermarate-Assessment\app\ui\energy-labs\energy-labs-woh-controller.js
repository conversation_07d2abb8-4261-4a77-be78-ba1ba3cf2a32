(function () {

    'use strict';
    var controllerId = 'EnergyLabsWohController';
    angular.module('app').controller(controllerId, ['$stateParams', '$state', '$rootScope', '$timeout', 'common', 'uuid4', 'standardmodelservice', 'projectservice', 'servicetemplateservice', 'nccclimatezoneservice', 'nccclimatezonedataservice', 'addressservice', 'wholeofhomeservice', 'energylabsservice', energyLabsWohController]);

    function energyLabsWohController($stateParams, $state, $rootScope, $timeout, common, uuid4, standardmodelservice, projectservice, servicetemplateservice, nccclimatezoneservice, nccclimatezonedataservice, addressservice, wholeofhomeservice, energylabsservice) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;

        vm.projectId = $stateParams.projectId;
        vm.project = null;

        vm.modelList = null;

        vm.filterData = {};
        vm.viewingModels = null;

        vm.isBusy = false;
        vm.mode = null; // 'select' | 'configure'
        vm.showResults = false;

        vm.addCompare = standardmodelservice.addCompare;
        vm.removeCompare = function (modelsList, cardClickedIndex) {
            standardmodelservice.removeCompare(modelsList, cardClickedIndex);
            setTimeout(() => $rootScope.$apply(), 10);
        }

        vm.copyFromHighlightEffect = standardmodelservice.copyFromHighlightEffect;

        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.showEnergyRating = wholeofhomeservice.showEnergyRating;

        // Breadcrumbs
        vm.backToProjects = function () {
            $state.go('energy-labs-parent-menu');
        }

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        async function initialize() {
            vm.trayExpanded = false;
            projectservice.getProject(vm.projectId).then(async data => {
                vm.project = data;
                vm.updateFilteredList({});
                servicetemplateservice.getServiceTypes().then(data => {
                    vm.serviceTypes = data;
                    vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                        ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                        'title',
                        vm.serviceTypes
                    );
                });
                nccclimatezoneservice.getList().then(data => vm.nccClimateZoneList = data.data);
                if ($stateParams.standardHomeModelId) {
                    var model = await standardmodelservice.getStandardModel($stateParams.standardHomeModelId);
                    model.selectedVariation = { standardHomeModelId: $stateParams.variationId };
                    let prefilledData = {
                        suburb: $stateParams.suburb,
                        stateCode: $stateParams.stateCode,
                        optionData: { nccClimateZone: ($stateParams.nccClimateZone != null && $stateParams.nccClimateZone != "") ? Number($stateParams.nccClimateZone) : null }
                    };
                    vm.select(model, prefilledData, true);
                } else {
                    vm.mode = "select";
                }
            });
        }

        // ----------------------------------- //
        // - HANDLES (Select Design Process) - //
        // ----------------------------------- //

        // Call backend to update filter options, filter count data, models list and selected Variations if Variation needs to change
        vm.updateFilteredList = function({ filterData }) {
            vm.currentTotal = null;
            // Get list of variations
            let selectedVariationsForModels = {};
            vm.modelList?.forEach(m => {
                selectedVariationsForModels[m.standardHomeModelId] = m.selectedVariation.standardHomeModelId;
            });
            let designOptions = vm.project.energyLabsSettings.distinctDesignOptions;
            designOptions.features = standardmodelservice.allFeatures;
            designOptions.categories = standardmodelservice.categories;
            vm.modelList = null;
            var phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
            standardmodelservice.getForProjectMultiFiltered(
                vm.projectId,
                {
                    fields: standardmodelservice.multiFiltersFields,
                    distinctDesignOptions: vm.project.energyLabsSettings.distinctDesignOptions,
                    appliedFilters: filterData,
                    selectedVariationsForModels,
                    countParentModelsOnly: true
                }
            ).then(response => {
                vm.modelList = response.parentModelList;
                vm.modelList.forEach(m => {
                    m.selectedForCompare = false;
                    m.defaultVariation = m.selectedVariation;
                });
                vm.filterCountData = response.filterCountData;
                if (filterData == null) {
                    vm.totalWithoutFilters = response.totalItems;
                } else {
                    vm.currentTotal = response.totalItems;
                }
                vm.anyFiltersApplied = standardmodelservice.anyFiltersApplied(filterData);
                vm.initialised = true;
            });
        }

        // Sort
        vm.updateSort = function (sortBy) {
            vm.modelList = standardmodelservice.applySort(sortBy, vm.modelList);
        }

        // Switcher button click, close dropdowns on all other cards
        vm.switcherOptionButtonClicked = function (model) {
            vm.modelList.filter(m => m.standardHomeModelId != model.standardHomeModelId).forEach(m => {
                Object.keys(m.dropdownFields ?? []).forEach(key => {
                    m.dropdownFields[key].expanded = false;
                    m.dropdownFields[key].justExpanded = false;
                });
                m.inOptionsSelectionsMode = false;
            });
        }

        // Select
        vm.select = function (building, prefilledData = null, autoRunCalc = false) {
            standardmodelservice.getStandardModel(building.selectedVariation.standardHomeModelId).then(model => {
                vm.mode = 'configure';
                vm.viewingModels = [ building ];
                building.selectedVariation = model;
                building.include = true;
                vm.resetBuilding(building.selectedVariation);
                vm.resetBuildingToDefaults(building.selectedVariation);
                if (prefilledData != null) {
                    let optionData = {
                        ...building.selectedVariation.optionData,
                        ...prefilledData.optionData
                    }
                    building.selectedVariation = {
                        ...building.selectedVariation,
                        ...prefilledData,
                        optionData: optionData
                    };
                } else {
                    standardmodelservice.assignDefaults([building.selectedVariation]);
                }
                if (autoRunCalc && building.selectedVariation.suburb != null && building.selectedVariation.suburb != "" && building.selectedVariation.stateCode != null && building.selectedVariation.stateCode != "") {
                    vm.runCalc();
                }
            });
        }

        // Compare Selecting
        vm.anySelectedForCompare = function () {
            return vm.modelList?.some(m => m.selectedForCompare);
        }
        vm.numSelectedForCompare = function () {
            return vm.modelList?.filter(m => m.selectedForCompare).length;
        }
        vm.anyCompareSlotsLeft = function () {
            return vm.numSelectedForCompare() < 3;
        }
        vm.selectModelForCompare = function (parentModel) {
            if (vm.anyCompareSlotsLeft() || parentModel.selectedForCompare == true) {
                parentModel.selectedForCompare = !parentModel.selectedForCompare;
                // IF now none left, set vm.trayExpanded to false
                if (vm.numSelectedForCompare() == 0) {
                    vm.trayExpanded = false;
                }
                // ELSE IF just added and only 1 selected, run extend height animation
                else if (parentModel.selectedForCompare && vm.numSelectedForCompare() == 1) {
                    setTimeout(() => document.getElementById("tray").classList.add("tray-expanded"), 600);
                    setTimeout(() => {
                        if (!vm.trayExpanded) {
                            document.getElementById("tray")?.classList.remove("tray-expanded");
                        }
                    }, 2500);
                }
                // ELSE IF just added, run bounce animcation
                else if (parentModel.selectedForCompare) {
                    document.getElementById("expand-button").classList.remove("bounce");
                    setTimeout(() => document.getElementById("expand-button").classList.add("bounce"), 0);
                }
            }
        }
        vm.clearCompareSelections = function () {
            vm.modelList.forEach(m => m.selectedForCompare = false);
        }
        vm.startCompare = function() {
            let comparingModels = vm.modelList?.filter(m => m.selectedForCompare);
            let getModelCalls = comparingModels.map(m => standardmodelservice.getStandardModel(m.selectedVariation.standardHomeModelId));
            Promise.all(getModelCalls).then(results => {
                for (let [i, modelResult] of results.entries()) {
                    vm.mode = 'configure';
                    comparingModels[i].selectedVariation = modelResult;
                    comparingModels[i].include = true;
                    vm.resetBuilding(comparingModels[i].selectedVariation);
                    vm.resetBuildingToDefaults(comparingModels[i].selectedVariation);
                    standardmodelservice.assignDefaults([comparingModels[i].selectedVariation]);
                    comparingModels[i].selectedVariation.copyAcrossData = {};
                    comparingModels[i].uiRenderId = uuid4.generate();
                }
                vm.viewingModels = comparingModels;
                // Set whether "multi-orientate" option should be available
                let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
                vm.viewingModels.forEach(m => {
                    if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                        m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                    }
                });
            });
        }

        // ---------------------------- //
        // - HANDLES (Configure Page) - //
        // ---------------------------- //

        // Go back to selecting Model
        vm.reset = function () {
            vm.mode = 'select';
            vm.showResults = false;
            vm.compare = false;
            vm.trayExpanded = false;

            vm.modelList.forEach(model => {
                vm.resetBuilding(model);
                model.selectedForCompare = false;
                model.selectedVariation = model.defaultVariation;
            });

            vm.viewingModels = null;
            vm.filterData = {};
            vm.updateFilteredList({filterData: vm.filterData});
        }

        // Reset to defaults
        vm.resetBuildingToDefaults = function (variation, ignoreSuburbData = false) {
            let origNorthOffset = variation.optionData.northOffset;
            let origNatHers = variation.optionData.natHERSClimateZone;
            applyDefaults(variation.variableMetadata.wholeOfHomeDefaultData, variation);
            if (ignoreSuburbData) {
                variation.optionData.northOffset = origNorthOffset;
                variation.optionData.natHERSClimateZone = origNatHers;
            } else {
                // Suburb
                if (vm.project.lockWOHLocation && vm.project.suburbCode) {
                    variation.suburb = vm.project.suburbName;
                    nccclimatezonedataservice.getClimateZone(vm.project.suburb.latitude, vm.project.suburb.longitude).then(data => {
                        if (variation.optionData == null) {
                            variation.optionData = {};
                        }
                        variation.optionData.nccClimateZone = data;
                    });
                } else {
                    variation.suburb = null;
                    variation.suburbObject = null;
                    variation.optionData.nccClimateZone = null;
                }
                // State
                if (vm.project.lockWOHLocation && vm.project.stateCode) {
                    variation.stateCode = vm.project.stateCode;
                } else {
                    variation.stateCode = null;
                }
                variation.modelBlockShowError = false;
                if (vm.energyLabsForm != null) {
                    vm.energyLabsForm.$setPristine();
                    vm.energyLabsForm.$setUntouched();
                }
            }
            vm.showResults = false;
        }

        // Clear
        vm.resetBuilding = function (variation, ignoreSuburbData = false) {
            if (!ignoreSuburbData) {
                // Only clear nccClimateZone, suburb and state if lockWOHLocation toggled off
                if (!vm.project.lockWOHLocation) {
                    variation.optionData = {};
                    variation.suburb = null;
                    variation.suburbObject = null;
                    variation.stateCode = null;
                } else {
                    variation.optionData = { nccClimateZone: variation.optionData?.nccClimateZone };
                }
            }
            variation.swimmingPool = {};
            variation.spa = {};
            variation.photovoltaic = {};
            variation.spaceHeating = {};
            variation.spaceCooling = {};
            variation.waterHeating = {};

            vm.showResults = false;
            variation.modelBlockShowError = false;
            if (vm.energyLabsForm != null) {
                vm.energyLabsForm.$setPristine();
                vm.energyLabsForm.$setUntouched();
            }
            vm.showResults = false;
        }

        // Run
        vm.runCalc = async function () {

            if (vm.isBusy)
                return;

            try {

                vm.isBusy = true;

                for(let i = 0; i < vm.viewingModels.length; i++) {

                    const building = vm.viewingModels[i];
                    building.selectedVariation.calculations = {};

                    const { allowance, achieved } = await wholeofhomeservice.calculate({
                        swimmingPool: building.selectedVariation.swimmingPool,
                        spa: building.selectedVariation.spa,
                        photovoltaic: building.selectedVariation.photovoltaic,
                        spaceHeating: building.selectedVariation.spaceHeating,
                        spaceCooling: building.selectedVariation.spaceCooling,
                        waterHeating: building.selectedVariation.waterHeating,

                        projectDetails: {
                            stateCode: building.selectedVariation.stateCode,
                            nccClimateZone: building.selectedVariation.optionData.nccClimateZone,
                            wohFloorArea: building.selectedVariation.wohFloorArea,
                            nccBuildingClassification: building.selectedVariation.nccBuildingClassification
                        },
                    });

                    if (allowance == null || achieved == null)
                        return;

                    building.selectedVariation.calculations = { allowance, achieved };
                    building.selectedVariation.calculations.difference = allowance - achieved;

                    building.selectedVariation.passed = building.selectedVariation.calculations.achieved <= building.selectedVariation.calculations.allowance;

                    if (building.selectedVariation.calculations.achieved <= building.selectedVariation.calculations.allowance && building.selectedVariation.calculations.achieved <= 10) {
                        building.selectedVariation.calculations.outcome = "The net equivalent energy usage of the home complies with the Whole-of-Home energy usage requirements.";
                    } else {
                        building.selectedVariation.calculations.outcome =
                            "The net equivalent energy usage of the home does not comply with the Whole-of-Home energy usage requirements.\n\n" +
                            "Compliance may be achieved by incorporating one or more of the following:\n\n"

                        building.selectedVariation.calculations.outcome += "1. install a more efficient heating system; or \n";
                        building.selectedVariation.calculations.outcome += "2. install a more efficient cooling system; or \n";
                        building.selectedVariation.calculations.outcome += "3. install a more efficient water heating system; or \n";

                        let pvNoteNumber = "5";
                        if (building.selectedVariation.swimmingPool.exists)
                            building.selectedVariation.calculations.outcome += "4. install a more efficient swimming pool pump; or \n";
                        else
                            pvNoteNumber = "4";

                        let pvCapacityReq = 0;

                        if (!building.selectedVariation.photovoltaic.exists)
                            pvCapacityReq = (common.roundUp(building.selectedVariation.calculations.achieved - building.selectedVariation.calculations.allowance, 2)).toFixed(2);
                        else
                            pvCapacityReq = (common.roundUp(building.selectedVariation.calculations.achieved - building.selectedVariation.calculations.allowance + building.selectedVariation.photovoltaic.capacity, 2)).toFixed(2);

                        building.selectedVariation.calculations.outcome += `${pvNoteNumber}. install a photovoltaic (PV) system with a minimum system capacity of ${pvCapacityReq} (kW).`;
                    }

                }

                vm.showResults = true;
                vm.isBusy = false;

            } catch (e) {
                throw e;
            } finally {
                vm.isBusy = false;
            }

        }

        // Apply defaults
        function applyDefaults(defaults, toBuilding) {
            toBuilding.spaceHeating = {};
            toBuilding.spaceHeating.gems2019Rating = defaults.spaceHeating?.gems2019Rating;
            if (defaults.spaceHeating?.serviceTypeCode) {
                toBuilding.spaceHeating.serviceType = vm.serviceTypes.find(x => x.serviceTypeCode === defaults.spaceHeating?.serviceTypeCode);
            }
            toBuilding.spaceCooling = {};
            toBuilding.spaceCooling.gems2019Rating = defaults.spaceCooling?.gems2019Rating;
            if (defaults.spaceCooling?.serviceTypeCode) {
                toBuilding.spaceCooling.serviceType = vm.serviceTypes.find(x => x.serviceTypeCode === defaults.spaceCooling?.serviceTypeCode);
            }
            toBuilding.waterHeating = {};
            toBuilding.waterHeating.gems2019Rating = defaults.waterHeating?.gems2019Rating;
            if (defaults.waterHeating?.serviceTypeCode) {
                toBuilding.waterHeating.serviceType = vm.serviceTypes.find(x => x.serviceTypeCode === defaults.waterHeating?.serviceTypeCode);
            }
            toBuilding.swimmingPool = {
                exists: defaults.swimmingPool?.exists,
                volume: defaults.swimmingPool?.volume,
                gems2019Rating: defaults.swimmingPool?.gems2019Rating
            }
            toBuilding.spa = {
                exists: defaults.spa?.exists,
                volume: defaults.spa?.volume,
                gems2019Rating: defaults.spa?.gems2019Rating
            }
            toBuilding.photovoltaic = {
                exists: defaults.photovoltaic?.exists,
                capacity: defaults.photovoltaic?.capacity,
            }
        }

        // Clear results
        vm.clearComparison = function() {
            vm.showResults = false;
        }

        // Variation changes
        vm.variationChanged = function (parentModel) {
            vm.clearComparison();
            vm.resetBuilding(parentModel.selectedVariation, true);
            vm.resetBuildingToDefaults(parentModel.selectedVariation, true);
            // Set whether "multi-orientate" option should be available
            let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
            vm.viewingModels.forEach(m => {
                if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                    m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                }
            });
        }

        // Copy single option across
        vm.copyOptionAcross = function (copyAcrossData) {
            // IF copying suburb
            if (copyAcrossData.field == 'suburb') {
                vm.viewingModels.forEach(building => {
                    building.selectedVariation.suburbObject = copyAcrossData.option;
                    energylabsservice.setBlockDataFromSuburb(
                        building.selectedVariation,
                        copyAcrossData.option,
                        building.selectedVariation.variableOptions,
                        () => vm.clearResults()
                    );
                });
            }
            // ELSE do normal copy
            else {
                vm.viewingModels.forEach(building => {
                    if (building.selectedVariation.variableOptions[copyAcrossData.field]?.includes(copyAcrossData.option)) {
                        building.selectedVariation.optionData[copyAcrossData.field] = copyAcrossData.option;
                    }
                });
            }
        }

        // Copy single option across
        vm.copyServiceOptionAcross = function (event, field, subField, option) {
            vm.viewingModels.forEach(building => {
                building.selectedVariation[field][subField] = option;
            });
            $timeout(() => {
                // Make sure option still clicked so dropdown collapses
                event.currentTarget.previousElementSibling.click();
            });
        }

        // Copy all data to another building
        vm.copyFromTo = function(fromBuilding, toBuilding) {
            toBuilding.selectedVariation.optionData = angular.copy(fromBuilding.selectedVariation.optionData);
            toBuilding.selectedVariation.suburb = fromBuilding.selectedVariation.suburb;
            toBuilding.selectedVariation.suburbObject = fromBuilding.selectedVariation.suburbObject;
            toBuilding.selectedVariation.swimmingPool = angular.copy(fromBuilding.selectedVariation.swimmingPool);
            toBuilding.selectedVariation.spa = angular.copy(fromBuilding.selectedVariation.spa);
            toBuilding.selectedVariation.photovoltaic = angular.copy(fromBuilding.selectedVariation.photovoltaic);
            toBuilding.selectedVariation.spaceHeating = angular.copy(fromBuilding.selectedVariation.spaceHeating);
            toBuilding.selectedVariation.spaceCooling = angular.copy(fromBuilding.selectedVariation.spaceCooling);
            toBuilding.selectedVariation.waterHeating = angular.copy(fromBuilding.selectedVariation.waterHeating);
            vm.clearResults();
        }

        // Clear
        vm.clearResults = function () {
            vm.showResults = false;
            vm.anySuburbsInvalid = vm.viewingModels.some(x => x.selectedVariation.climateZoneIsInvalid);
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialize();
  }
})();