(function () {
    // The slipAddressUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'slipAddressDataUpdateController';
    angular.module('app')
        .controller(controllerId, ['common', '$rootScope', '$scope', '$mdDialog', 'slipaddress', 'daterangehelper', slipAddressDataUpdateController]);

    function slipAddressDataUpdateController(common, $rootScope, $scope, $mdDialog, slipaddress, daterangehelper) {

        var vm = this;

        const log = common.logger;

        vm.state = "";
        vm.processing = true;
        vm.lastProcessDate = "";

        vm.updateStreetAddress = true;
        vm.updateSuburbBoundaries = true;
        vm.updateLga = true;

        function initialize() {

            slipaddress.getLastProcessDate().then(data => {
                console.log(data);
                vm.lastProcessDate = new Date(data);
            });

            slipaddress.getCurrentProcessingState().then(data => {

                vm.processing = data === "STARTED";

                if (data === "STARTED")
                    vm.state = "Currently processing a new dataset. This takes roughly 30-45 minutes.";
                else if (data === "FAILED") 
                    log.logError("An error was encountered while processing the dataset. If this error persists, please contact support. The underlying datasets may have changed.");
                else
                    vm.state = "";

            });
        }

        vm.processNewDataset = function () {

            vm.processing = true;

            try {

                slipaddress.processNewDataset(
                    vm.updateStreetAddress, 
                    vm.updateSuburbBoundaries,
                    vm.updateLga)
                .then(data => {
                    console.log(data);
                    vm.state = data;
                    vm.processing = false;

                    if(data === true)
                        log.logSuccess("Dataset/s updated successfully.");
                });

            } catch (e) {
                vm.state = e;
                vm.processing = false;
                log.logError("An error was encountered while processing the dataset. If this error persists, please contact support. The underlying datasets may have changed.");
            }
        }

        initialize();

    }
})();