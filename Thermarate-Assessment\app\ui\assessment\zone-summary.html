<style>
    .totals-row {
        color: rgba(0, 0, 0, 0.43);
        font-weight: 400;
    }

    .border-last:last-child > td {
        border-bottom: 2px solid grey;
    }

    .border-cell {
        border-bottom: 2px solid grey;
    }

    /* 
        Dummy class applied to inputs so we can target in on the javascript side.
    */
    .dummy-format-class {

    }
</style>

<!-- Zone Summary -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span style="margin-right: 15px;"
              ng-click="vm.expand('buildingSummary');">
            <span style="margin-right: 15px;" 
                  ng-style="{ 'color' : vm.source.zones == null || vm.source.zones.length == 0 ? 'grey' : 'black' }">
                Zone Summary
            </span>
            <i ng-if="vm.sectionExpansions['buildingSummary']" class="fa fa-caret-up" />
            <i ng-if="(!vm.sectionExpansions['buildingSummary']) && vm.source.zones.length > 0" 
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <!-- Rows in Section -->
    <md-card-content ng-show="vm.sectionExpansions['buildingSummary']"
                        style="margin: 0px; padding: 0px;">

        <md-tabs>
            <md-tab ng-repeat="summaryGroup in vm.knownBuildingSummaryGroups">
                <md-tab-label>
                    <span>{{vm.buildingSummaryGroups[summaryGroup].groupName}}</span>
                </md-tab-label>
                <md-tab-body>
                    <table class="table table-striped table-hover table-condensed">

                        <!-- Grouped Header-->
                        <thead>

                            <tr>
                                <th class="text-left"
                                    colspan="{{summaryGroup == 'interiorZones' ? 4 : 3}}">
                                </th>

                                <th style="text-align:center"
                                    colspan="2">
                                    Floor Area
                                </th>

                                <th colspan="2" style="text-align:center">Volume</th>

                                <th colspan="2" style="text-align:center">Exterior Wall Area</th>

                                <th colspan="2" style="text-align:center">Exterior Glazing Area</th>

                                <th colspan="2" style="text-align:center"></th>

                            </tr>

                            <!-- 'Subheader' Row -->
                            <tr style="font-weight: bold;">
                                <td class="text-center" style="min-width: 110px; width: 110px;">Storey</td>

                                <td ng-if="summaryGroup == 'interiorZones'" 
                                    class="text-center" 
                                    style="min-width: 110px; width: 110px;">Zone Number</td>

                                <td class="text-center" style="min-width: 180px; width: 180px;">
                                    {{vm.buildingSummaryGroups[summaryGroup].descriptionHeading}}
                                </td>

                                <td style="text-align: center; min-width: 90px; width: 90px;">Zone Count</td>

                                <td style="text-align: right;">(m<sup>2</sup>)</td>
                                <td style="text-align: left;">(%)</td>

                                <!-- Volume m3 & % -->
                                <td style="text-align: right;">(m<sup>3</sup>)</td>
                                <td style="text-align:  left;">(%)</td>

                                <!-- Exterior wall area m2 & % -->
                                <td style="text-align: right;">(m<sup>2</sup>)</td>
                                <td style="text-align: left;">(%)</td>

                                <!-- Exterior glazing area m2 & % -->
                                <td style="text-align: right;">(m<sup>2</sup>)</td>
                                <td style="text-align: left;">(%)</td>

                                <!-- Exterior glazing area m2 & % -->
                                <td style="text-align: center; width: 180px;">Glass-Exterior Wall (%)</td>
                                <td style="text-align: center; width: 180px;">Glass-Floor Area (%)</td>
                            </tr>

                        </thead>

                        <tbody ng-repeat="storeyGroup in vm.buildingSummaryGroups[summaryGroup].rows track by $index"
                               class="border-last">

                            <!-- Groups -->
                            <tr ng-repeat="row in storeyGroup.zones track by $index"
                                class="border-last">

                                <!-- Group -->
                                <td data-title="Description"
                                    ng-if="$index == 0"
                                    rowspan="{{storeyGroup.zones.length}}"
                                    class="text-center border-cell">
                                    <span>{{storeyGroup.name}}</span>
                                </td>

                                <!-- Zone Number -->
                                <td ng-if="summaryGroup == 'interiorZones'">
                                    {{row.zoneNumber}}
                                </td>

                                <!-- Description (Varies) -->
                                <td>
                                    {{row.description}}
                                </td>

                                <td style="text-align: center;">
                                    {{row.zoneCount}}
                                </td>

                                <!-- Floor Area -->
                                <td data-title="Floor Area"
                                    style="text-align: right;">
                                    {{row.floorArea.toFixed(2);}}
                                </td>

                                <!-- Floor Area % -->
                                <td data-title="Floor Area"
                                    style="text-align: left;">
                                    {{row.floorAreaPercent.toFixed(2);}}
                                </td>

                                <!-- Volume m2 & % -->
                                <td style="text-align: right;">{{row.volume.toFixed(2);}}</td>
                                <td style="text-align: left;">{{row.volumePercent.toFixed(2);}}</td>

                                <!-- Exterior wall area m2 & % -->
                                <td style="text-align: right;">{{row.exteriorWallArea.toFixed(2);}}</td>
                                <td style="text-align: left;">{{row.exteriorWallAreaPercent.toFixed(2);}}</td>

                                <!-- Exterior Glazing area m2 & % -->
                                <td style="text-align: right;">{{row.exteriorGlazingArea.toFixed(2);}}</td>
                                <td style="text-align: left;">{{row.exteriorGlazingAreaPercent.toFixed(2);}}</td>

                                <!-- Glass vs X ratio %'s -->
                                <td style="text-align: center;">{{row.glassExteriorWallAreaPercent.toFixed(2);}}</td>
                                <td style="text-align:  center;">{{row.glassFloorAreaPercent.toFixed(2);}}</td>

                            </tr>

                        </tbody>

                    </table>
                </md-tab-body>
            </md-tab>
        </md-tabs>
    </md-card-content>

</md-card>

<!-- Envelope Summary -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span style="margin-right: 15px;"
              ng-click="vm.expand('envelopeSummary');">
            <span style="margin-right: 15px;"
                  ng-style="{ 'color' : vm.source.zones == null || vm.source.zones.length == 0 ? 'grey' : 'black' }">
                  Envelope Summary
            </span>
            <i ng-if="vm.sectionExpansions['envelopeSummary']" class="fa fa-caret-up" />
            <i ng-if="(!vm.sectionExpansions['envelopeSummary']) && vm.source.zones.length > 0"
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <!-- Body -->
    <md-card-content ng-show="vm.sectionExpansions['envelopeSummary']"
                     style="margin: 0px; padding: 0px;">

        <div class="graph-grid">

            <!-- Table & Filters -->
            <div class="filter-grid-item">
                <div>
                    <!-- Table -->
                    <table class="table table-striped table-hover table-condensed"
                            style="margin-bottom: 5rem;">
                        <thead>

                        <tr style="font-weight: bold; text-align: center;">
                            <!-- Title -->
                            <th></th>
                            <!-- Only show each Sector if at least 1 row exists for column -->
                            <th ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys" 
                                style="text-transform: capitalize;  text-align: center;">
                                {{ vm.sectorFromLabel(sector, vm.sectorDetermination.sectors).description || 'Total'}} 
                            </th>
                        </tr>
                        </thead>

                        <tbody>
                        <tr style="text-align: center;">
                            <td style="text-align: center;">Exterior Wall Area (m<sup>2</sup>)</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].area.toFixed(2)}}
                            </td>
                        </tr>
                        <tr style="text-align: center;">
                            <td style="text-align: center;">Exterior Wall Area (%)</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].percentage.toFixed(2)}}
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">Exterior Glazing Area (m<sup>2</sup>)</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].area.toFixed(2)}}
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">Exterior Glazing Area (%)</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].percentage.toFixed(2)}}
                            </td>
                        </tr>
                        <tr style="text-align: center;">
                            <td style="text-align: center;">Glass-Exterior Wall Ratio (%)</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.glassExteriorWallRatioPerSector[sector].toFixed(2)}}
                            </td>
                        </tr>
                        <tr style="text-align: center;">
                            <td style="text-align: center;">Average Glazing U-Value</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.averageGlazingUValuePerSector[sector].toFixed(2)}}
                            </td>
                        </tr>
                        <tr style="text-align: center;">
                            <td style="text-align: center;">Average Glazing SHGC</td>
                            <td style="text-align: center;" 
                                ng-repeat="sector in vm.envelopeSummaryData.sectorKeys"
                                ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummaryData)">
                                {{vm.envelopeSummaryData.averageGlazingSHGCPerSector[sector].toFixed(2)}}
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <!-- Filters -->
                    <h3 style="margin-top: 16px;">Filter</h3>
                    <div class="filter-grid" ng-repeat="filter in vm.filters track by filter.id">

                        <!-- Row 1 -->
                        <div>
                            <label class="graph-option-select-label">Storey</label>
                            <md-select name="storeyFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.storey"
                                        ng-required="true">
                                <md-option ng-value="storey"
                                           ng-click="vm.applyStoreySelectLogic(vm.source, filter, vm.calculateEnvelopeSummaryData)"
                                           ng-repeat="storey in vm.selectedStoreys">
                                    {{storey.description}}
                                </md-option>
                                <md-option ng-value="'ALL'"
                                           ng-click="vm.applyStoreySelectLogic(vm.source, filter, vm.calculateEnvelopeSummaryData)">
                                    Whole Building
                                </md-option>
                            </md-select>
                        </div>
                        <div>
                            <label class="graph-option-select-label">Horizontal Shading (Exterior Glazing)</label>
                            <md-select name="glazingHorizontalShadingFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.glazingHorizontalShading.selection"
                                        multiple>
                                <md-option ng-value="shading"
                                           ng-click="vm.applySelectionLogic(filter.glazingHorizontalShading, shading)"
                                           ng-repeat="shading in filter.glazingHorizontalShading.selectableArray track by $index">
                                    {{shading.title}}
                                </md-option>
                            </md-select>
                        </div>
                        <div>
                            <label class="graph-option-select-label">Horizontal Shading (Exterior Walls)</label>
                            <md-select name="wallHorizontalShadingFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.wallHorizontalShading.selection"
                                        multiple>
                                <md-option ng-value="shading"
                                           ng-click="vm.applySelectionLogic(filter.wallHorizontalShading, shading)"
                                           ng-repeat="shading in filter.wallHorizontalShading.selectableArray track by $index">
                                    {{shading.title}}
                                </md-option>
                            </md-select>
                        </div>

                        <!-- Row 2 -->                            
                        <div>
                            <label class="graph-option-select-label">Group</label>
                            <md-select name="groupFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.group"
                                        ng-required="true"
                                        ng-change="filter.selection = []">
                                <md-option ng-value="group"
                                           ng-click="vm.applyGroupSelectLogic(vm.source, filter, group, vm.calculateEnvelopeSummaryData)"
                                           ng-repeat="group in vm.GROUP_OPTIONS">
                                    {{group}}
                                </md-option>
                            </md-select>
                        </div>
                        <div>
                            <label class="graph-option-select-label">Vertical Shading (Exterior Glazing)</label>
                            <md-select name="glazingVerticalShadingFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.glazingVerticalShading.selection"
                                        multiple>
                                <md-option ng-value="shading"
                                           ng-click="vm.applySelectionLogic(filter.glazingVerticalShading, shading)"
                                           ng-repeat="shading in filter.glazingVerticalShading.selectableArray track by $index">
                                    {{shading.title}}
                                </md-option>
                            </md-select>
                        </div>
                        <div>
                            <label class="graph-option-select-label">Vertical Shading (Exterior Walls)</label>
                            <md-select name="wallVerticalShadingFilter"
                                       class="lightweight vertically-condensed"
                                       ng-model="filter.wallVerticalShading.selection"
                                       multiple>
                                <md-option ng-value="shading"
                                           ng-click="vm.applySelectionLogic(filter.wallVerticalShading, shading)"
                                           ng-repeat="shading in filter.wallVerticalShading.selectableArray track by $index">
                                    {{shading.title}}
                                </md-option>
                            </md-select>
                        </div>

                        <!-- Row 3 - how to stop making row expand -->
                        <div>
                            <label class="graph-option-select-label">Selection</label>
                            <md-select  name="selectionFilter"
                                        class="lightweight vertically-condensed"
                                        ng-model="filter.selection"
                                        multiple
                                        ng-required="true">
                                <md-option ng-value="selection"
                                           ng-repeat="selection in filter.selectableArray track by $index"
                                           ng-click="vm.applySelectionLogic(filter, selection)">
                                    {{ selection.title || selection }}
                                </md-option>
                            </md-select>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Graph -->
            <div class="filter-grid-item">
                <!-- Chart -->
                <div id={{vm.windRoseChartId}}></div>

                <!-- View Dropdown -->
                <div class="chart-view-container">
                    <label class="graph-option-select-label">View</label>
                    <md-select  name="view"
                                class="lightweight vertically-condensed"
                                ng-model="vm.windRoseChartView">
                        <md-option value="ExteriorWall" ng-click="vm.windRoseChartViewChange()">
                            Exterior Wall (%)
                        </md-option>
                        <md-option value="ExteriorGlazing" ng-click="vm.windRoseChartViewChange()">
                            Exterior Glazing (%)
                        </md-option>
                        <md-option value="GlassExteriorWallRatio" ng-click="vm.windRoseChartViewChange()">
                            Glass-Exterior Wall Ratio (%)
                        </md-option>
                    </md-select>
                </div>
            </div>

        </div>

    </md-card-content>

</md-card>

<!-- Floor Plan Areas -->
<floor-plan-areas source="vm.source"
                  storeys="vm.storeys"
                  ncc-climate-zone="vm.assessment.nccClimateZone.description"
                  nathers-climate-zone="vm.assessment.natHERSClimateZone.description">
</floor-plan-areas>

<!-- Area Correction Factors -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span style="margin-right: 15px;"
              ng-click="vm.expand('areaCorrectionFactors');">
            <span style="margin-right: 15px;"
                  ng-style="{ 'color' : vm.source.zones == null || vm.source.zones.length == 0 ? 'grey' : 'black' }">
                Area Correction Factors
            </span>
            <i ng-if="vm.sectionExpansions['areaCorrectionFactors']" class="fa fa-caret-up" />
            <i ng-if="(!vm.sectionExpansions['areaCorrectionFactors']) && vm.source.zones.length > 0"
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <!-- Body -->
    <md-card-content ng-show="vm.sectionExpansions['areaCorrectionFactors']"
                     style="margin: 0px; padding: 0px;">
        <table class="table table-striped table-hover table-condensed">
            <thead>
                <tr style="font-weight: bold;">
                    <th>Description</th>
                    <th>Area Correction Factor</th>
                </tr>
            </thead>

            <tbody>

            <tr ng-if="vm.certification.glazingCalculatorRulesetCode === 'NCC 2019'">
                <td>CHENATH (NCC 2019)</td>
                <td>{{vm.source.ncc2019AreaCorrectionFactor.toFixed(8)}}</td>
            </tr>

            <tr ng-if="vm.certification.glazingCalculatorRulesetCode === 'NCC 2022'">
                <td>CHENATH (NCC 2022)</td>
                <td>{{vm.source.ncc2022AreaCorrectionFactor.toFixed(8)}}</td>
            </tr>

            <tr>
                <td>NCC Whole of Home</td>
                <td>{{vm.nccWholeOfHomeCorrectionFactor.toFixed(4)}}</td>
            </tr>

            <tr>
                <!-- No rounding for now -->
                <td>Heating Load Limit (Specification 44)</td>
                <td>{{vm.source.heatingLoadLimitCorrectionFactor}}</td>
            </tr>

            <tr>
                <!-- No rounding for now -->
                <td>Cooling Load Limit (Specification 44)</td>
                <td>{{vm.source.coolingLoadLimitCorrectionFactor}}</td>
            </tr>

            </tbody>
        </table>
    </md-card-content>

</md-card>

<!-- Artificial Lighting -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span style="margin-right: 15px;"
              ng-click="vm.expand('artificialLighting')">
            <span style="margin-right: 15px;"
                  ng-style="{ 'color' : vm.source.zones == null || vm.source.zones.length == 0 ? 'grey' : 'black' }">
                Artificial Lighting
            </span>
            <i ng-if="vm.sectionExpansions['artificialLighting']" class="fa fa-caret-up" />
            <i ng-if="!vm.sectionExpansions['artificialLighting'] && vm.source.zones.length > 0"
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <!-- Rows in Section -->
    <md-card-content ng-show="vm.sectionExpansions['artificialLighting']"
                     style="margin: 0px; padding: 0px;">

        <table class="table table-striped table-hover table-condensed">

            <!-- Headers-->
            <thead>

                <!-- Grouped Headers -->
                <tr>
                    <th class="text-left"
                        colspan="4" />
                    <th style="text-align:center"
                        colspan="3">
                        Artificial Lighting Lamp Power
                    </th>
                </tr>

                <!-- 'Subheader' Row -->
                <tr style="font-weight: bold;">

                    <td class="text-center" style="min-width: 110px; width: 110px;">Zone Number</td>
                    <td class="text-center" style="min-width: 110px; width: 110px;">Zone Name</td>
                    <td class="text-center" style="min-width: 80px; width: 80px;">Zone Type</td>
                    <td class="text-center" style="width: 133px;">Floor Area (m<sup>2</sup>)</td>
                    <td class="text-center" style="width: 133px;">Maximum (W)</td>
                    <td class="text-center" style="width: 133px;">Achieved (W)</td>
                    <td class="text-center" style="width: 133px;">Outcome</td>
                </tr>
            </thead>

            <tbody>

                <!-- Interior Zones AND Spaces LOL -->
                <tr ng-repeat="zone in vm.artificialLightingZones() track by $index">

                    <!-- Zone Number -->
                    <td data-title="Zone Number">
                        {{zone.zoneNumber || zone.spaceNumber}}
                    </td>

                    <!-- Zone Name -->
                    <td data-title="Zone Name">
                        {{zone.zoneDescription || zone.spaceDescription}}
                    </td>

                    <!-- Zone Type -->
                    <td data-title="Zone Type">
                        {{zone.zoneType.description}}
                    </td>

                    <!-- Floor Area -->
                    <td data-title="Floor Area"
                        class="text-right">
                        {{zone.floorArea.toFixed(2);}}
                    </td>

                    <!-- Maximum W -->
                    <td data-title="Floor Area"
                        class="text-right">
                        {{zone.lampPowerMaximumW.toFixed(2)}}
                    </td>

                    <!-- Achieved W -->
                    <td data-title="Achieved W"
                        class="text-right">
                        {{zone.lampPowerAchievedW == null || zone.lampPowerAchievedW == 0 ?  "Not Specified" : zone.lampPowerAchievedW.toFixed(2)  }}
                    </td>

                    <td data-title="Outcome"
                        class="text-right">
                        {{ (zone.lampPowerAchievedW == null || zone.lampPowerAchievedW == "") ? "Installed Lamp Power (W) not to exceed maximum" : +zone.lampPowerMaximumW.toFixed(2) >= +zone.lampPowerAchievedW.toFixed(2) ? "Pass" : "Fail"}}
                    </td>
                </tr>

            </tbody>

        </table>
    </md-card-content>

</md-card>

<!-- Light & Ventilation -->
<md-card>

    <md-card-header style="font-size: 18px; margin-bottom: 10px;">
        <span ng-click="vm.expand('lightAndVentilation');">
            <span style="margin-right: 15px;"
                    ng-style="{ 'color' : vm.source.zones == null || vm.source.zones.length == 0 ? 'grey' : 'black' }">
                Light & Ventilation
            </span>
            <i ng-if="vm.sectionExpansions['lightAndVentilation']" class="fa fa-caret-up" />
            <i ng-if="!vm.sectionExpansions['lightAndVentilation'] && vm.source.zones.length > 0" 
               class="fa fa-caret-down"></i>
        </span>

    </md-card-header>

    <md-card-content ng-show="vm.sectionExpansions['lightAndVentilation']"
                        style="margin: 0px; padding: 0px;">
        <table class="table table-striped table-hover table-condensed">

            <!-- Headers-->
            <thead>

                <!-- Grouped Headers -->
                <tr>
                    <th class="text-left"
                        colspan="3" />
                    <th style="text-align:center"
                        colspan="3">
                        Light
                    </th>
                    <th style="text-align:center"
                        colspan="3">
                        Ventilation
                    </th>
                    <th style="text-align:center"
                        colspan="3">
                        Air Movement
                    </th>
                </tr>

                <!-- 'Subheader' Row -->
                <tr style="font-weight: bold;">

                    <td class="text-center" style="min-width: 110px; width: 110px;">Zone Number</td>
                    <td class="text-center" style="min-width: 110px; width: 110px;">Zone Name</td>
                    <td class="text-center" style="min-width: 80px; width: 80px;">Storey</td>

                    <!-- Natural Light -->
                    <td class="text-center"
                        style="width: 80px;">
                        Required (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Achieved (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Outcome
                    </td>

                    <!-- Ventilation -->
                    <td class="text-center"
                        style="width: 80px;">
                        Required (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Achieved (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Outcome
                    </td>

                    <!-- Air Movement -->
                    <td class="text-center"
                        style="width: 80px;">
                        Required (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Achieved (m<sup>2</sup>)
                    </td>
                    <td class="text-center"
                        style="width: 85px;">
                        Outcome
                    </td>

                </tr>
            </thead>

            <!-- Habitable Zones -->
            <tbody>

                <!-- Interior Zones -->
                <tr ng-repeat="zone in vm.habitableZones() track by $index">

                    <!-- Zone Number -->
                    <td data-title="Zone Number">
                        {{zone.zoneNumber}}
                    </td>

                    <!-- Zone Name -->
                    <td data-title="Zone Name">
                        {{zone.zoneDescription}}
                    </td>

                    <!-- Storey -->
                    <td data-title="Storey">
                        {{vm.storeys[zone.storey].description}}
                    </td>

                    <!-- Natural Light Required M2 -->
                    <td>
                        {{zone.naturalLightRequiredM2.toFixed(2)}}
                    </td>

                    <!-- Natural Light Achieved -->
                    <td>
                        {{zone.naturalLightAchievedM2.toFixed(2)}}
                    </td>

                    <!-- Light Outcome -->
                    <td data-title="Outcome"
                        class="text-right">
                        {{zone.naturalLightAchievedM2 == null ? "Insufficient Input" : zone.naturalLightAchievedM2 >= zone.naturalLightRequiredM2 ? "Pass" : "Fail"}}
                    </td>

                    <!-- Ventilation Required M2 -->
                    <td>
                        {{zone.ventilationRequiredM2.toFixed(2)}}
                    </td>

                    <!-- Ventilation Achieved -->
                    <td>
                        {{zone.ventilationAchievedM2.toFixed(2)}}
                    </td>

                    <!-- Ventilation Outcome-->
                    <td data-title="Outcome"
                        class="text-right">
                        {{ zone.ventilationRequiredM2 == null || zone.ventilationRequiredM2 == 0 ? "N/A" : zone.ventilationAchievedM2 == null ? "Insufficient Input" : zone.ventilationAchievedM2 >= zone.ventilationRequiredM2 ? "Pass" : "Fail"}}
                    </td>

                    <!-- Air Movement Required M2 -->
                    <td>
                        {{zone.airMovementRequiredM2.toFixed(2)}}
                    </td>

                    <!-- Air Movement Achieved -->
                    <td>
                        {{zone.airMovementAchievedM2.toFixed(2)}}
                    </td>

                    <!-- Air Movement Outcome -->
                    <td data-title="Outcome"
                        class="text-right">
                        {{vm.airMovementOutcome(zone.airMovementRequiredM2, zone.airMovementAchievedM2)}}
                    </td>

                </tr>

            </tbody>

            <!-- Storey SUM rows-->
            <tbody>

                <!-- Interior Zones -->
                <tr ng-repeat="row in vm.lightAndVentilationStoreyRows track by $index">

                    <!-- Blank -->
                    <td></td>

                    <td data-title="Zone Number"
                        style="font-weight: bold;">
                        {{row.description}} Total
                    </td>

                    <td data-title="Zone Name"
                        style="font-weight: bold;">
                        {{row.description}}
                    </td>

                    <!-- Natural Light Required M2 -->
                    <td>
                        {{row.naturalLight.required.toFixed(2)}}
                    </td>

                    <!-- Natural Light Achieved -->
                    <td>
                        {{row.naturalLight.achieved.toFixed(2)}}
                    </td>

                    <!-- Light Outcome -->
                    <td data-title="Outcome"
                        class="text-right">
                        {{row.naturalLight.outcome}}
                    </td>

                    <!-- Ventilation Required M2 -->
                    <td>
                        {{row.ventilation.required.toFixed(2)}}
                    </td>

                    <!-- Ventilation Achieved -->
                    <td>
                        {{row.ventilation.achieved.toFixed(2)}}
                    </td>

                    <!-- Ventilation Outcome-->
                    <td data-title="Outcome"
                        class="text-right">
                        {{row.ventilation.outcome}}
                    </td>

                    <!-- Air Movement Required M2 -->
                    <td>
                        {{row.airMovement.required.toFixed(2)}}
                    </td>

                    <!-- Air Movement Achieved -->
                    <td>
                        {{row.airMovement.achieved.toFixed(2)}}
                    </td>

                    <!-- Air Movement Outcome -->
                    <td data-title="Outcome"
                        class="text-right">
                        {{row.airMovement.outcome}}
                    </td>
                </tr>
            </tbody>

        </table>
    </md-card-content>

</md-card>

<style>
    .filter-grid {
        display: grid;
        /* 1fr causes child multi-select to stretch column when it becomes bigger */
        grid-template-columns: repeat(3, 33%);
        gap: 12px;
        justify-content: space-around;
        justify-items: center;
    }

    .filter-grid > div {
        width: 100%;
    }

    .filter-grid-item {
        padding: 16px;
    }

    .chart-view-container {
        width: 33%;
        margin: 0 auto;
    }
</style>