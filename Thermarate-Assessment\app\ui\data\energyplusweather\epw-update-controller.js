(function () {
    'use strict';

    const CONTROLLER_ID = 'epwUpdateController';

    angular.module('app')
        .controller(CONTROLLER_ID,
            ['energyplusweatherdata', epwUpdateController]);

function epwUpdateController(energyplusweatherdata) {

    var vm = this;

    vm.processing = false;

    vm.processNewDataset = function (file) {

        if (file == null)
            return;

        vm.processing = true;
        console.log("Processing? ", vm.processing);

        try {
            energyplusweatherdata.processZip(file).then(data => {
                vm.processing = false;
            });

        } catch (e) {
            console.log(e);
            vm.processing = false;
        }

    }

    vm.excelArchiveUrl = () => energyplusweatherdata.excelArchiveUrl();

}
})();