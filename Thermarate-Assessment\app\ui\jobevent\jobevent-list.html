<section id="jobevent-list-view" class="main-content-wrapper" data-ng-controller="JobeventListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Jobevent"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.jobeventList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="jobeventList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="jobClientJobNumber" class="can-sort text-left">Job</th>
                        <th st-sort="eventTypeDescription" class="can-sort text-left">Event Type</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.jobeventList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="jobevent-updateform({ jobEventId: row.jobEventId})">Select</md-button>  </td>
                        <td data-title="Job" class="text-left">{{::row.jobClientJobNumber }}</td>
                        <td data-title="Event Type" class="text-left">{{::row.eventTypeDescription }}</td>
                        <td data-title="Description" class="text-left">{{::row.description }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
