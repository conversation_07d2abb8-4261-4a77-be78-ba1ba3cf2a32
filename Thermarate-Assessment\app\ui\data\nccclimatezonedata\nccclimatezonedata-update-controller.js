(function () {
    // The nccClimateZoneUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'nccClimateZoneUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['nccclimatezonedataservice', nccClimateZoneUpdateController]);
function nccClimateZoneUpdateController(nccclimatezonedataservice) {

    var vm = this;

    vm.state = "";
    vm.processing = false;

    vm.processNewDataset = function () {

        vm.processing = true;
        vm.state = "Attempting to process new dataset...";

        try {

            nccclimatezonedataservice.processDataset().then(data => {
                console.log(data);
                vm.state = data;
                vm.processing = false;
            });

        } catch (e) {
            vm.state = e;
            vm.processing = false;
        }

    }

}
})();