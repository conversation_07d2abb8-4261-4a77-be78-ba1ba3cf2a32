(function () {
    // The CompliancestatusUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'CompliancestatusUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'compliancestatusservice', 'security', compliancestatusUpdateController]);
function compliancestatusUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  compliancestatusservice, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Compliance Status';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.complianceStatusCode = null;
        vm.compliancestatus = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Compliance Status";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.complianceStatusCode = $scope.complianceStatusCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.complianceStatusCode = $stateParams.complianceStatusCode;
        }

        // Get data for object to display on page
        var complianceStatusCodePromise = null;
        if (vm.complianceStatusCode != null) {
            complianceStatusCodePromise = compliancestatusservice.getComplianceStatus(vm.complianceStatusCode)
            .then(function (data) {
                if (data != null) {
                    vm.compliancestatus = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("compliancestatus-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                compliancestatusservice.createComplianceStatus(vm.compliancestatus).then(function(data){
                    vm.compliancestatus = data;
                    vm.complianceStatusCode = vm.compliancestatus.complianceStatusCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                compliancestatusservice.updateComplianceStatus(vm.compliancestatus).then(function(data){
                    if (data != null) {
                        vm.compliancestatus = data;
                        vm.complianceStatusCode = vm.compliancestatus.complianceStatusCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            compliancestatusservice.deleteComplianceStatus(vm.complianceStatusCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            compliancestatusservice.undoDeleteComplianceStatus(vm.complianceStatusCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();