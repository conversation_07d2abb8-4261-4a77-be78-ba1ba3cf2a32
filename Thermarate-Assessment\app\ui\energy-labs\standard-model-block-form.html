<!-- "Block" -->
<div ng-if="vm.showAnyInputs() || vm.configuration === 'woh'">

    <div class="el-section-title">
        <img class="el-section-icon"
             src="content/images/energy-labs/el-block-icon.svg"
             alt="Icon of cartoon houses divided into their land blocks">
        Block
        <img ng-if="vm.showNorthOffset"
                src="/content/images/energy-labs/el-north-arrow-icon.svg"
                style="position: absolute; top: 0; right: 50px; transform: rotate({{vm.theModel.optionData.northOffset || 0 }}deg); max-height: 50px;"
                ng-style="{ 'opacity': vm.theModel.optionData.northOffset != null || vm.theModel.optionData.northOffset === 0 ? '100%' : '0%' }"
                alt="North offset arrow" />
    </div>

    <!-- Suburb select. This autofills the rest of the 'block' information -->
    <div ng-if="vm.showInputFor('natHERSClimateZone') || vm.configuration === 'woh';">
        <search-suburb class="force-text-to-be-black"
                       initial-suburb-text="vm.theModel.suburb"
                       selected-suburb="vm.theModel.suburbObject"
                       suburb-changed="vm.suburbChanged();"
                       copy-across-enabled="vm.copyAcrossEnabled"
                       copy-across-data="vm.theModel.copyAcrossData"
                       copy-across-trigger="vm.copySuburbAcross()"
                       condensed="true"
                       required="false"
                       ng-disabled="false">
        </search-suburb>

        <div style="color: rgb(221,44,0); margin: 0;">
            <div ng-if="vm.theModel.climateZoneIsInvalid">There are no home design configurations in the specified suburbs climate zone.</div>
        </div>

        <md-input-container ng-if="vm.configuration === 'default' || vm.configuration === 'identify' || vm.configuration === 'orientate'"
                            class="md-block"
                            flex="100">
            <label>NatHERS Climate Zone</label>
            <input ng-model="vm.theModel.optionData.natHERSClimateZone"
                   type="text"
                   ng-disabled="true"
                   ng-required="true" />
        </md-input-container>

        <md-input-container ng-if="vm.configuration === 'woh'"
                            class="md-block"
                            flex="100">
            <label>State</label>
            <input ng-model="vm.theModel.stateCode"
                   type="text"
                   ng-disabled="true"
                   ng-required="true" />
        </md-input-container>

        <md-input-container ng-if="vm.configuration === 'woh'"
                            class="md-block"
                            flex="100">
            <label>NCC Climate Zone</label>
            <input ng-model="vm.theModel.optionData.nccClimateZone"
                   type="text"
                   ng-disabled="true"
                   ng-required="true" />
        </md-input-container>

    </div>

    <!-- North Offset -->
    <md-input-container ng-if="vm.showInputFor('northOffset') && vm.configuration !== 'woh' && vm.configuration !== 'orientate'"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex">
        <label>North Offset</label>
        <md-select name="northOffset"
                   md-container-class="md-select-show-all"
                   ng-required="true"
                   ng-model="vm.theModel.optionData.northOffset"
                   ng-change="vm.onDataChanged()">
            <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions.northOffset track by $index">
                <md-option ng-value="option">
                    {{option}}
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, 'northOffset', option);"><img src="/content/images/share.png" /></div>
            </div>
        </md-select>
    </md-input-container>

    <!-- Site Exposure -->
    <md-input-container ng-if="vm.showInputFor('siteExposure') && vm.configuration !== 'woh'"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex">
        <label>Site Exposure</label>
        <md-select md-container-class="md-select-show-all"
                   ng-required="true"
                   ng-model="vm.theModel.optionData.siteExposure"
                   ng-change="vm.onDataChanged()">
            <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions.siteExposure track by $index">
                <md-option ng-value="option">
                    {{option}}
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, 'siteExposure', option);"><img src="/content/images/share.png" /></div>
            </div>
        </md-select>
    </md-input-container>

    <!-- Floor Height -->
    <md-input-container ng-if="vm.showInputFor('floorHeight') && vm.configuration !== 'woh'"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex">
        <label>Floor Height</label>
        <md-select md-container-class="md-select-show-all"
                   ng-required="true"
                   ng-model="vm.theModel.optionData.floorHeight"
                   ng-change="vm.onDataChanged()">
            <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions.floorHeight track by $index">
                <md-option ng-value="option">
                    {{option}}
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, 'floorHeight', option);"><img src="/content/images/share.png" /></div>
            </div>
        </md-select>
    </md-input-container>

    <!-- Block Type -->
    <md-input-container ng-if="vm.showInputFor('blockType');"
                        class="md-block kindly-remove-error-spacer vertically-condensed-ex">
        <label>Block Type</label>
        <md-select name="blockType"
                   ng-required="true"
                   ng-model="vm.theModel.optionData.blockType"
                   ng-change="vm.onDataChanged()">
            <div class="custom-dropdown-option" ng-repeat="option in vm.variableOptions.blockType track by $index">
                <md-option ng-value="option">
                    {{option}}
                </md-option>
                <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, 'blockType', option);"><img src="/content/images/share.png" /></div>
            </div>
        </md-select>
    </md-input-container>

</div>

<style>
    .force-text-to-be-black > md-autocomplete > md-autocomplete-wrap > md-input-container > input {
        color: black !important;
    }
</style>