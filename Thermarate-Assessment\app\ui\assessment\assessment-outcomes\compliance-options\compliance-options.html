<fieldset ng-disabled="vm.shouldInputBeDisabled() || (vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded')"
          style="margin: 0px; padding: 0px;">
    <md-card layout-margin>
        <md-card-header>
            <span class="md-title" ng-class="{'card-has-errors' : complianceOptionsForm.$invalid}">
                Compliance Options
            </span>
        </md-card-header>

        <md-card-content ng-form="complianceOptionsForm" 
                         ng-if="vm.assessment.allComplianceOptions.length <= 1">
            <h2 style="text-align: center; color: darkgrey; font-weight:300;">Add an Option to begin</h2>
        </md-card-content>

        <md-card-content ng-form="complianceOptionsForm"
                         ng-if="vm.assessment.allComplianceOptions.length > 0"
                         style="padding: 10px 0px;">

            <!-- All compliance options in a row + drag and drop -->
            <div ng-repeat="option in vm.assessment.allComplianceOptions track by $index"
                 ng-if="$index > 0"
                 lr-drag-src="complianceOptionsList"
                 lr-drop-target="complianceOptionsList"
                 lr-drag-data="vm.assessment.allComplianceOptions"
                 lr-drop-success="vm.reorderComplianceOptions(item)"
                 lr-match-property="optionIndex"
                 lr-match-value="{{option.optionIndex}}"
                 lr-index="vm.assessment.allComplianceOptions.indexOf(option)"
                 style="margin-bottom: 20px;">

                <md-card class="compliance-option"
                         ng-class="{
                     'compliance-option-hover': vm.isOnHoverEnabled(option) && option.isCompliant,
                     'compliance-option-uncompliant-hover': vm.isOnHoverEnabled(option) && !option.isCompliant,
                     'compliance-option-selected' : option.isSelected == true,
                     'compliance-option-fade' : vm.shouldOptionFade(option) }"
                         flex="100">

                    <div class="md-title"
                         ng-class="{'card-has-errors' : !option.isCompliant}"
                         style="margin-bottom: 8px;">
                        <span>Option {{option.optionIndex}}</span>
                    </div>

                    <div ng-switch="option.complianceMethod.complianceMethodCode" lr-no-drag>

                        <!-- HER -->
                        <div layout ng-switch-when="CMHouseEnergyRating">
                            <compliance-her compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">

                            </compliance-her>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- HER -->
                        <div layout ng-switch-when="CMPerfWAProtocolHER">
                            <compliance-her compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">

                            </compliance-her>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- HER-DTS -->
                        <div layout ng-switch-when="CMPerfSolutionHER">
                            <compliance-her compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">

                            </compliance-her>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- HER-DTS -->
                        <div layout ng-switch-when="CMPerfELL">
                            <compliance-her compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">

                            </compliance-her>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- VURB -->
                        <div layout ng-switch-when="CMPerfSolution">
                            <compliance-perf compliance-data="option"
                                             assessment="vm.assessment"
                                             is-option="true"
                                             flex="100"
                                             compliance-method-list="vm.complianceMethodList"
                                             compliance-type-changed="vm.complianceTypeChanged(option);"
                                             assessment-software-list="vm.assessmentSoftwareList"
                                             on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                             purchase-order-default="vm.purchaseOrderDefault"
                                             is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                             job-files="vm.jobFiles">
                            </compliance-perf>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- DTS -->
                        <div layout ng-switch-when="CMPerfSolutionDTS">
                            <compliance-perf compliance-data="option"
                                             assessment="vm.assessment"
                                             is-option="true"
                                             flex="100"
                                             compliance-method-list="vm.complianceMethodList"
                                             compliance-type-changed="vm.complianceTypeChanged(option);"
                                             assessment-software-list="vm.assessmentSoftwareList"
                                             on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                             purchase-order-default="vm.purchaseOrderDefault"
                                             is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                             job-files="vm.jobFiles">
                            </compliance-perf>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <!-- ELE -->
                        <div layout ng-switch-when="CMElementalProv">
                            <compliance-ele compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">
                            </compliance-ele>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                        <div layout ng-switch-when="CMPerfWAProtocolEP">
                            <compliance-ele compliance-data="option"
                                            assessment="vm.assessment"
                                            is-option="true"
                                            flex="100"
                                            compliance-method-list="vm.complianceMethodList"
                                            assessment-software-list="vm.assessmentSoftwareList"
                                            on-compliance-changed="vm.checkCompliance(option.complianceOptionsId)"
                                            compliance-type-changed="vm.complianceTypeChanged(option);"
                                            is-locked="vm.isLocked || vm.shouldInputBeDisabled()"
                                            purchase-order-default="vm.purchaseOrderDefault"
                                            job-files="vm.jobFiles">
                            </compliance-ele>
                            <div ng-include="'select-and-delete-comp-opt'"></div>
                        </div>

                    </div>
                </md-card>

            </div>
        </md-card-content>

        <!-- Automatically add same type of compliance option as the Baseline run - user can change it to something else after -->
        <div style="margin-right: auto;"
             ng-if="!(vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded')">
            <md-button class="md-raised md-primary"
                       style="height: 30px; vertical-align: central;"
                       ng-click="vm.addOption()"
                       ng-show="!vm.isLocked"
                       redi-enable-roles="assessment_actions__addcompliance"
                       ng-disabled="(vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded') || vm.shouldInputBeDisabled()">
                ADD OPTION
            </md-button>

        </div>

    </md-card>
</fieldset>

<!-- Selection and Erasure Buttons -->
<script type="text/ng-template" id="select-and-delete-comp-opt">
    <div style="padding: 5px; margin-bottom: 5px; height: 100%; display: flex; flex-direction: column;">

        <md-button ng-if="item.isLocked!=true"
                   redi-enable-roles="assessment_actions__deletecompliance"
                   style="margin-bottom: 10px;"
                   class="md-raised md-icon-button md-warn"
                   title="Delete Option"
                   ng-click="vm.removeOption(option.complianceOptionsId)"
                   ng-show="!vm.isLocked"
                   ng-disabled="vm.deleteInProgress === true || vm.shouldInputBeDisabled()">
            <i class="fa fa-eraser fa-lg"></i>
        </md-button>

        <!-- THR-281: 1) Remove the Copy Option AND Clear Option buttons
        <md-button ng-if="item.isLocked!=true"
                   style="margin-bottom: 10px;"
                   class="md-raised md-icon-button"
                   title="Copy Option"
                   ng-click="vm.cloneOption(option)"
                   ng-show="!vm.isLocked"
                   ng-disabled="vm.shouldInputBeDisabled()">
            <i class="fa fa-clone fa-lg"></i>
        </md-button>

        <md-button ng-if="item.isLocked!=true"
                   style="margin-bottom: 10px;"
                   class="md-raised md-icon-button"
                   title="Clear Option"
                   ng-click="vm.clearOption(option)"
                   ng-show="!vm.isLocked"
                   ng-disabled="vm.shouldInputBeDisabled()">
            <i class="fa fa-file-o fa-lg"></i>
        </md-button>
        -->

        <md-input-container class="md-block"
                            style="margin-top: auto; margin-right: 0px; padding-right: 0px;">
            <md-checkbox ng-model="option.isSelected"
                         style="transform: scale(1.7); margin-right: 0px; padding-right: 0px;"
                         ng-change="vm.onSelect(option);"
                         ng-disabled="vm.shouldSelectBeDisabled(option) || vm.selectPermissionEnabled == false">
            </md-checkbox>

            <md-tooltip ng-if="option.tempNotSavedInDb === true"">
                Assessment must be saved before new compliance option can be selected
            </md-tooltip>

        </md-input-container>

    </div>
</script>