﻿(function () {
	'use strict';

	angular
		.module('appservices')
		.factory('energyloadlimitservice', [
			'common',
			'config',
			'$http',
			EnergyLoadLimitService
		]);

	function EnergyLoadLimitService(common, config, $http) {
		
		const log = common.logger;
		const baseUrl = config.servicesUrlPrefix + 'energyloadlimit/';

		const service = {
			getCombinedEnergyData: getCombinedEnergyData,
		};
		return service;
		
		// Returns a bunch of different info so we don't have to chain so
		// many function.then(function.then(function.then...))...
		function getCombinedEnergyData(climateZone, requiredRating, floorType, proposedEnergyUsage, ruleset, state = "WA") {

			if (proposedEnergyUsage == null || isNaN(proposedEnergyUsage))
				proposedEnergyUsage = 0;
			
			floorType = floorType?.replace('&', '_') || null;
			let params = {
				climateZone,
				requiredRating,
				
				proposedEnergyUsage,
				ruleset: ruleset,
				state
			}
			
			if(floorType != null && floorType !== "")
				params.floorType = floorType;
			
			return $http({
				url: baseUrl + 'GetCombinedEnergyData',
				params: params,
				method: 'GET',
			}).then(success, fail)
			
			function success(resp) {
				if (resp != null && resp.data != undefined && resp.data != null) {
					return resp.data;
				}
				else {
					return null;
				}
			}
			
			function fail(error) {
				var msg = "Error getting combined energy data: " + error;

				log.logError(msg, error, null, true);
				throw error; // so caller can see it
			}
		}
		
		

	}
})();