// Name: worksdescriptionservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'worksdescriptionservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', worksdescriptionservice]);

    function worksdescriptionservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'worksdescription/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getFiltered: getFiltered,
            getFilteredCancel: getFilteredCancel,
            currentFilter: () => currentFilter,
            getWorksDescription: getWorksDescription,
            createWorksDescription: createWorksDescription,
            updateWorksDescription: updateWorksDescription,
            deleteWorksDescription:deleteWorksDescription,
            undoDeleteWorksDescription:undoDeleteWorksDescription,
        };
            
        return service;

        /** No Need for filtering or pagination on this list as it should only ever be tiny. */
        function getList() {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetAll';

            //Get error List from the Server 
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Works Description list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** Get's a filtered and paginated list */
        function getFiltered(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, clientId, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetFiltered';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate, clientId: clientId };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Works Description list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getFilteredCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        /** Returns the full DTO for the corresponding code */
        function getWorksDescription(worksDescriptionCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {worksDescriptionCode: worksDescriptionCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting worksDescription: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createWorksDescription(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Description Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error creating Building Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateWorksDescription(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Building Description Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Building Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteWorksDescription(worksDescriptionCode) {

            return $http({
                url: baseUrl + 'Delete',
                params: { worksDescriptionCode: worksDescriptionCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Works Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteWorksDescription(worksDescriptionCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { worksDescriptionCode: worksDescriptionCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Works Description: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
