// Name: notificationservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'notificationruleservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', notificationruleservice]);

    function notificationruleservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'notificationrule/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getFiltered: getFiltered,
            getFilteredCancel: getFilteredCancel,
            currentFilter: () => currentFilter,
            getNotificationRule,
            createNotificationRule,
            updateNotificationRule,
            copyNotificationRule,
            deleteNotificationRule,
            undoDeleteNotificationRule,
            getRecipientTypes,
            getNotificationTemplates,
            updateEnabled
        };
            
        return service;

        /** No Need for filtering or pagination on this list as it should only ever be tiny. */
        function getList() {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetAll';

            //Get error List from the Server 
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true,
                cache: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Notification Rule list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** Get's a filtered and paginated list */
        function getFiltered(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, clientId, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetFiltered';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate, clientId: clientId };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                console.log(resp);
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Notification Rule list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getFilteredCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        /** Returns the full DTO for the corresponding code */
        function getNotificationRule(notificationRuleId) {
            return $http({
                url: baseUrl + 'Get',
                params: {notificationRuleId: notificationRuleId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting data from NotificationRule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** Returns the full DTO for the corresponding code */
        function getNotificationTemplates(notificationRuleId) {
            return $http({
                url: baseUrl + 'GetNotificationTemplates',
                params: {notificationRuleId: notificationRuleId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting data from NotificationRule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createNotificationRule(notificationRule, notificationTemplates) {
            var url = baseUrl + 'Create';
            return $http.post(url, { notificationRule, notificationTemplates }).then(success, fail)
            function success(resp) {
                log.logSuccess("Notification Rule Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error creating Notification Rule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateNotificationRule(notificationRule, notificationTemplates) {
            var url = baseUrl + 'Update';
            return $http.post(url, { notificationRule, notificationTemplates }).then(success, fail)
            function success(resp) {
                log.logSuccess("Notification Rule Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Notification Rule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        function copyNotificationRule(notificationRuleId) {
            var url = baseUrl + 'Copy';
            return $http({
                url: url,
                params: { notificationRuleId: notificationRuleId } ,
                method: 'GET' 
            }).then(success, fail);
            
            function success(resp) {
                log.logSuccess("Notification Rule Copied");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Notification Rule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteNotificationRule(notificationRuleId) {

            return $http({
                url: baseUrl + 'Delete',
                params: { notificationRuleId: notificationRuleId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Notification Rule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteNotificationRule(notificationRuleId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { notificationRuleId: notificationRuleId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Notification Rule: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /** Returns the full DTO for the corresponding code */
        function getRecipientTypes() {
            return $http({
                url: baseUrl + 'GetRecipientTypes',
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                
                if (resp != null && resp.data !== undefined && resp.data != null)
                    return resp.data;
   
                return null;
                
            }
            function fail(error) {
                var msg = "Error getting data from NotificationRule: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateEnabled(notificationRuleId, isEnabled) {
            const url = baseUrl + 'UpdateEnabled';
            return $http({
                url: url,
                params: { notificationRuleId, isEnabled },
                method: 'POST',
            })
              .then(success, fail);

            function success(resp) { return resp?.data; }
            function fail(error) {
                var msg = "Failed to update 'enabled': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }
        
    }
})();
