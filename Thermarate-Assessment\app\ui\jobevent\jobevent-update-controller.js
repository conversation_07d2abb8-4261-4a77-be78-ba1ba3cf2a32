(function () {
    // The JobeventUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'JobeventUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'jobservice', 'eventtypeservice', 'jobeventservice', jobeventUpdateController]);
function jobeventUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  jobservice, eventtypeservice, jobeventservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Job Event';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.jobEventId = null;
        vm.jobevent = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Job Event";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.jobEventId = $scope.jobEventId;
            }
            vm.hideActionBar = true;
        } else {
            vm.jobEventId = $stateParams.jobEventId;
        }

        // Get data for object to display on page
        var jobEventIdPromise = null;
        if (vm.jobEventId != null) {
            jobEventIdPromise = jobeventservice.getJobEvent(vm.jobEventId)
            .then(function (data) {
                if (data != null) {
                    vm.jobevent = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.eventTypeList = [];
        var eventTypePromise = eventtypeservice.getList()
            .then(function(data){
                vm.eventTypeList = data.data;
            });

        // Functions to get data for Typeahead
        vm.getjobs = function(searchTerm) {
            var filter = [{ field: "clientJobNumber", operator: "startswith", value: searchTerm }];
            return jobservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateJob', function(event){
            event.stopPropagation();
            vm.createJob() // function to launch add modal;
            }));

        vm.createJob = function() {
            // Add logic to display create modal form.
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("jobevent-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                jobeventservice.createJobEvent(vm.jobevent).then(function(data){
                    vm.jobevent = data;
                    vm.jobEventId = vm.jobevent.jobEventId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                jobeventservice.updateJobEvent(vm.jobevent).then(function(data){
                    if (data != null) {
                        vm.jobevent = data;
                        vm.jobEventId = vm.jobevent.jobEventId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            jobeventservice.deleteJobEvent(vm.jobEventId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            jobeventservice.undoDeleteJobEvent(vm.jobEventId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();