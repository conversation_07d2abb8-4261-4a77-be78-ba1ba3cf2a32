<form name="glazingExportForm" 
      class="main-content-wrapper"
      novalidate 
      data-ng-controller='exportGlazingCalcModalController as vm'>

    <div ng-if="vm.processing == true"
            class="export-processing-overlay">
        <div>
            <h2>Generating glazing calculator, please wait...</h2>
        </div>
    </div>

    <div class="widget" 
         ng-cloak>
        <div class="glazing-calc-modal-header">
            <h4 class="md-title">{{vm.title()}}</h4>

            <!-- Simplified / Advanced selector -->
            <div><!--empty--></div>
<!--            <div ng-if="vm.source === 'assessment'"-->
<!--                 style="justify-self: end;">-->
<!--                <span class="switch-selector clickable" -->
<!--                      ng-class="{'switch-selected' : vm.mode == 'simplified'}"-->
<!--                      ng-click="vm.mode = 'simplified'">-->
<!--                    Simplified-->
<!--                </span> -->
<!--                <span>|</span> -->
<!--                <span class="switch-selector clickable"-->
<!--                      ng-class="{'switch-selected' : vm.mode == 'advanced'}"-->
<!--                      ng-click="vm.mode = 'advanced'">-->
<!--                    Advanced-->
<!--                </span>-->
<!--            </div>-->

            <!-- Close button -->
            <button class="md-icon-button md-button md-ink-ripple" 
                    type="button" 
                    style="margin-right: 10px;"
                    ng-click="vm.cancel()">
                <md-icon class="icon_close material-icons" 
                         aria-label="Close dialog">close</md-icon>
            </button>
        </div>
        <div style="width: 1140px;">

            <!-- Non-table data -->
            <md-card>
                <md-card-content>

                    <!-- Simulation / Option Selection -->
                    <md-input-container ng-if="vm.source === 'assessment'"
                                        class="md-block vertically-condensed">
                        <label>Simulation</label>
                        <md-select ng-model="vm.building"
                                   ng-model-options="{trackBy: '$value.assessmentComplianceBuildingId'}">
                            <md-option ng-value="simulationOption.building"
                                       ng-repeat="simulationOption in vm.simulationOptions"
                                       ng-click="vm.recalculateSimulationData(simulationOption)">
                                {{simulationOption.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Certification -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Certification</label>
                        <md-select name="certification"
                                   ng-required="true"
                                   ng-model="vm.selectedCertification"
                                   ng-model-options="{trackBy: '$value.certificationId'}">
                            <md-option ng-value="item"
                                       ng-repeat="item in vm.certificationList track by item.certificationId"
                                       ng-click="vm.updateDataLinkedToCertification(item); vm.recalculateSimulationData(null, item)">
                                {{item.title}}
                            </md-option>
                        </md-select>
                        <div ng-messages="glazingExportForm.certification.$error">
                            <div ng-message="required">Certification is required.</div>
                        </div>
                    </md-input-container>

                    <!-- ******** Sector Determination ******** -->
                    <md-input-container class="md-block">
                        <label>Sector Determination</label>
                        <md-select name="sectorDetermination"
                                   ng-required="true"
                                   ng-model="vm.selectedSectorDetermination"
                                   ng-model-options="{trackBy: '$value.sectorDeterminationCode'}"
                                   ng-style="{'color': vm.selectedSectorDetermination.sectorDeterminationCode == 'NatHERS' ? 'red' : 'black'}">
                            <md-option ng-value="sector"
                                       ng-repeat="sector in vm.sectorDeterminationList track by sector.sectorDeterminationCode">
                                {{sector.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Building Description -->
                    <md-input-container class="md-block vertically-condensed">
                        <label>Building Description</label>
                        <input type="text"
                               name="buildingDescription"
                               ng-model="vm.buildingDescription"
                               required/>
                        <div ng-messages="glazingExportForm.buildingDescription.$error">
                            <div ng-message="required">Building Description is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Glazing Description Format -->
                    <md-input-container class="md-block vertically-condensed" ng-click="vm.glazingFormatsOnChange">
                        <label>Glazing Description Format</label>
                        <md-select ng-model="vm.selectedGlazingFormat"
                                   ng-model-options="{ trackby: '$value.id' }"
                                   md-selected-text="vm.getGlazingFormatSelectedText()"
                                   multiple>
                            <md-option ng-value="glazingFormat"
                                       ng-repeat="glazingFormat in vm.glazingFormats"
                                       ng-click="vm.glazingFormatsOptionClick(glazingFormat)">
                                {{glazingFormat.title}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- ******** Assessment Software ******** -->
                    <md-input-container ng-if="vm.mode === 'advanced' && vm.source === 'none'"
                                        class="md-block vertically-condensed">
                        <label>Assessment Software</label>
                        <md-select name="assessmentSoftwareCode"
                                   ng-model="vm.selectedAssessmentSoftware"
                                   ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}"
                                   ng-required>
                            <md-option ng-value="item"
                                       ng-repeat="item in vm.availableAssessmentSoftwareList track by item.assessmentSoftwareCode">
                                {{item.description}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- NCC Climate Zone -->
                    <md-input-container ng-if="vm.mode === 'advanced'"
                                        class="md-block vertically-condensed">
                        <label>NCC Climate Zone</label>
                        <md-select ng-model="vm.selectedNccClimateZone"
                                   ng-model-options="{ trackBy: '$value.nccClimateZoneCode' }"
                                   ng-required="true">
                            <md-option ng-value="climateZone"
                                       ng-repeat="climateZone in vm.nccClimateZoneList"
                                       ng-click="vm.nccClimateZoneChanged(climateZone)">
                                {{climateZone.description}}
                            </md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Multi-Orientation -->
                    <md-input-container ng-if="vm.mode === 'advanced'"
                                        class="md-block vertically-condensed"
                                        style="display:flex; align-items:center; column-gap:4px;">
                        <md-switch ng-model="vm.multiOrientation"
                                   style="margin: 0;"
                                   class="ng-pristine ng-valid ng-not-empty md-checked ng-touched">
                        </md-switch>
                        <div>Multi-Orientation</div>
                    </md-input-container>

                    <!-- Full Masonry-->
<!--                    <md-input-container ng-if="vm.certification2022() && vm.mode === 'advanced'"-->
<!--                                        class="md-block vertically-condensed">-->
<!--                        <label>Full Masonry</label>-->
<!--                        <md-select ng-required="true"-->
<!--                                   name="isFullMasonry"-->
<!--                                   ng-model="vm.selectedIsFullMasonry">-->
<!--                            <md-option ng-value="true">-->
<!--                                Yes-->
<!--                            </md-option>-->
<!--                            <md-option ng-value="false">-->
<!--                                No-->
<!--                            </md-option>-->
<!--                        </md-select>-->
<!--                    </md-input-container>-->

                    <!-- Scratch file upload + Process button -->
                    <div ng-if="vm.source === 'none'">

                        <div ng-if="vm.processingScratch == true"
                             class="export-processing-overlay">
                            <div>
                                <h2>Processing scratch file, please wait...</h2>
                            </div>
                        </div>

                        <h3>Scratch Data</h3>
                        <div style="display: grid; grid-template-columns: 6fr 4fr; align-items: center; margin-bottom: 12px;">

                            <div ng-if="vm.selectedAssessmentSoftware == null">
                                Please select an Assessment Software to be shown upload options.
                            </div>

                            <!-- Upload Table -->
                            <table ng-if="vm.selectedAssessmentSoftware != null">
                                <tbody>
                                <!-- Loop over all required software files, excluding 'fileA' which is generally the 'assessment file' which
                                     we have no interest in now -->
                                <tr ng-repeat="fileType in vm.softwareFileTypes"
                                    ng-if="vm.selectedAssessmentSoftware[fileType.prop  + 'Required'] === true && fileType.prop !== 'fileA'"
                                    ngf-drop="vm.selectScratchFile($file, fileType.prop)"
                                    ngf-drag-over-class="'file-dragover'">

                                    <!-- Software file display name -->
                                    <td style="width: 170px;">
                                        {{vm.selectedAssessmentSoftware[fileType.prop + 'Name']}}
                                    </td>

                                    <!-- Actual upload button -->
                                    <td style="display: grid; grid-template-columns: 300px 60px; align-items: center;">

                                        <!-- File name -->
                                        <md-input-container class="md-block vertically-condenxed kindly-remove-error-spacer">
                                            <input ng-disabled="true"
                                                   ng-value="vm.scratchData[fileType.prop].name || 'Upload a File'"/>
                                        </md-input-container>

                                        <!-- Upload Button -->
                                        <md-button ng-if="vm.scratchData[fileType.prop] == null"
                                                   class="md-raised md-icon-button small-icon-button"
                                                   ngf-select="vm.selectScratchFile($file, fileType.prop)"
                                                   ngf-accept="vm.selectedAssessmentSoftware[fileType.prop + 'Extension']"
                                                   style="justify-self: end;">
                                            <i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>
                                        </md-button>

                                        <!-- Delete Button -->
                                        <button ng-if="vm.scratchData[fileType.prop] != null"
                                                class="feather-icon-button"
                                                ng-click="vm.scratchData[fileType.prop] = null; vm.building = null;"
                                                style="justify-self: end;">
                                            <img src="/content/feather/trash.svg" />
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <!-- Process button -->
                            <md-button class="md-raised md-primary"
                                       ng-disabled="!vm.scratchFilesAreValid(vm.scratchData, vm.softwareFileTypes)"
                                       ng-click="vm.processScratchData()">
                                PROCESS
                            </md-button>
                        </div>
                    </div>

                    <!-- Number of Storeys (2019) -->
                    <md-input-container ng-if="false && vm.certification2019() && vm.mode === 'advanced' && vm.building != null && vm.source === 'none'"
                                        class="md-block vertically-condensed">
                        <label>Number of Storeys</label>
                        <md-select ng-model="vm.selectedNumberOfStoreys">
                            <md-option ng-value="1" ng-click="vm.updateStoreys(1)">1</md-option>
                            <md-option ng-value="2" ng-click="vm.updateStoreys(2)">2</md-option>
                            <md-option ng-value="3" ng-click="vm.updateStoreys(3)">3</md-option>
                            <md-option ng-value="4" ng-click="vm.updateStoreys(4)">4</md-option>
                            <md-option ng-value="5" ng-click="vm.updateStoreys(5)">5</md-option>
                            <md-option ng-value="6" ng-click="vm.updateStoreys(6)">6</md-option>
                            <md-option ng-value="7" ng-click="vm.updateStoreys(7)">7</md-option>
                            <md-option ng-value="8" ng-click="vm.updateStoreys(8)">8</md-option>
                            <md-option ng-value="9" ng-click="vm.updateStoreys(9)">9</md-option>
                            <md-option ng-value="10" ng-click="vm.updateStoreys(10)">10</md-option>
                        </md-select>
                    </md-input-container>

                    <!-- Number of Storeys (2022) -->
                    <md-input-container ng-if="false && vm.certification2022() && vm.mode === 'advanced' && vm.building != null && vm.source === 'none'"
                                        class="md-block vertically-condensed">
                        <label>Number of Storeys</label>
                        <md-select ng-model="vm.selectedNumberOfStoreys">
                            <md-option ng-value="1" ng-click="vm.updateStoreys(1)">One</md-option>
                            <md-option ng-value="2" ng-click="vm.updateStoreys(2)">Two or more</md-option>
                        </md-select>
                    </md-input-container>

                </md-card-content>
            </md-card>

            <md-card ng-if="vm.building != null">
                <md-card-content>

                    <!-- 2022 Certification Details -->
                    <div ng-if="vm.certification2022()">

                        <!-- Building Floor Area -->
                        <h2>Building Floor Area</h2>

                        <!-- Area table. 2022 Certification just shows data grouped up.-->
                        <table class="table table-striped table-hover table-condensed">
                            <thead>
                            <tr>
                                <th>Direct Contact (m<sup>2</sup>)</th>
                                <th>Suspended (m<sup>2</sup>)</th>
                                <th>Over Rooms (m<sup>2</sup>)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <input class="lightweight"
                                           ng-model="vm.buildingDetails.area.direct"
                                           formatted-number
                                           decimals="2"/>
                                </td>
                                <td>
                                    <input class="lightweight"
                                           ng-model="vm.buildingDetails.area.suspended"
                                           formatted-number
                                           decimals="2"/>
                                </td>
                                <td>
                                    <input class="lightweight"
                                           ng-model="vm.buildingDetails.area.overRooms"
                                           formatted-number
                                           decimals="2"/>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <!-- 2019 Certification Details -->
                    <div ng-if="vm.certification2019()">

                        <!-- Building Details -->
                        <h2>Building Details</h2>

                        <!-- Overrides table (multiple rows - 1 per storey) -->
                        <table class="table table-striped table-hover table-condensed">
                            <thead>

                            <!-- Grouped Headers -->
                            <tr>
                                <th colspan="2"></th>
                                <th colspan="2">Building Floor Area (m2)</th>
                                <th></th>
                            </tr>

                            <!-- 'Subheaders -->
                            <tr>
                                <th style="width: 160px;">Storey</th>
                                <th style="width: 90px;">Air Movement</th>
                                <th style="width: 90px;">Direct Contact</th>
                                <th style="width: 90px;">Suspended</th>
                                <th>Wall Insulation Option</th>
                            </tr>
                            </thead>
                            <tbody>

                            <!-- TODO: Alistair will probably want the title to match up at all times 
                                 (e.g. even after changing num storeys...) -->
                            <tr ng-repeat="storey in vm.buildingDetails.storeys track by $index">

                                <td>
                                    <input type="text"
                                           class="lightweight"
                                           ng-model="storey.name"/>
                                </td>

                               <td>
                                    <input class="lightweight"
                                           ng-model="storey.airMovement"
                                           ng-style="{'color' : vm.airMovementIsValid(storey) ? 'black' : 'red'}"
                                           ng-blur="vm.validateAirMovement(storey)"/>
                                </td>

                                <td>
                                    <input class="lightweight"
                                           ng-model="storey.direct"
                                           formatted-number
                                           decimals="2"/>
                                </td>
                                <td>
                                    <input class="lightweight"
                                           ng-model="storey.suspended"
                                           formatted-number
                                           decimals="2"/>
                                </td>
                                <td>
                                    <md-select name="wallInsulation"
                                               class="lightweight vertically-condensed kindly-remove-error-spacer"
                                               ng-model="storey.wallInsulation"
                                               ng-required="true">
                                        <md-option ng-value="insulation"
                                                   ng-repeat="insulation in vm.availableWallInsulationOptions track by $index">
                                            {{insulation}}
                                        </md-option>
                                    </md-select>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </md-card-content>
            </md-card>

            <!-- Construction -->
            <md-card ng-if="vm.certification2022() && vm.building.masonryWalls != null">
                <md-card-content>

                    <h2>Construction</h2>

                    <table>
                        <tbody>
                        <tr>
                            <!-- Full Masonry Exterior & Interior Walls -->
                            <td style="width: 360px; vertical-align: text-top;padding-top:10px;">
                                Full Masonry Exterior & Interior Walls
                            </td>
                            <td style="width: 360px;">
                                <md-input-container class="md-block vertically-condensed">
                                    <md-select ng-required="true"
                                               name="isFullMasonry"
                                               ng-model="vm.building.masonryWalls">
                                        <md-option ng-value="true">
                                            Yes
                                        </md-option>
                                        <md-option ng-value="false">
                                            No
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                </md-card-content>
            </md-card>

            <!-- Manual Overrides -->
            <md-card ng-if="vm.building != null">
                <md-card-content>

                    <!-- Note: Currently the same between certificatons, would not be surprised if this changes -->
                    <h2>Manual Overrides</h2>
                    <table class="table table-striped table-hover table-condensed"
                           style="table-layout: fixed;">
                        <thead>

                        <!-- Grouped Headers -->
                        <!-- Note: Having these headers seems to absolutely mess up our custom sizing for the subheaders.
                             Have not figured out how to get around this yet, so leaving commented out for now. -->
                        <!--                            <tr>-->
                        <!--                                <th colspan="3" style="text-align: center; width: 235px;">Application</th>-->
                        <!--                                <th colspan="2" style="text-align: center; width: 60px;">Glazing Performance</th>-->
                        <!--                                <th colspan="2" style="text-align: center; width: 120px;">Horizontal Projection</th>-->
                        <!--                                <th colspan="2" style="text-align: center; width: 40px;">Vertical Offset</th>-->
                        <!--                            </tr>-->

                        <!-- Subheaders -->
                        <tr>
                            <td style="width: 10px"></td>
                            <td style="width: 65px; text-align: center;">Storey</td>
                            <td style="width: 70px; text-align: center;">Group</td>
                            <td style="width: 120px; text-align: center;">Selection</td>
                            <td style="width: 30px; text-align: center;">U-Value</td>
                            <td style="width: 30px; text-align: center;">SHGC</td>
                            <td style="width: 80px; text-align: center;">Horizontal Projection</td>
                            <td style="width: 20px; text-align: center;">P</td>
                            <td style="width: 20px; text-align: center;">G</td>
                            <td style="width: 20px; text-align: center;">H Max</td>
                            <td style="width: 30px;"></td>
                        </tr>
                        </thead>

                        <tbody>
                        <tr ng-repeat="override in vm.manualOverrides track by override.id"
                            lr-drag-src="manualOverrides"
                            lr-drop-target="manualOverrides"
                            lr-drop-success="vm.doNothing()"
                            lr-match-property="id"
                            lr-drag-data="vm.manualOverrides"
                            lr-match-value="{{override.id}}"
                            lr-index="vm.manualOverrides.indexOf(override)">
                            <td>
                                <img src="/content/feather/menu.svg"
                                     class="feather-icon-button sm"/>
                            </td>
                            <td lr-no-drag="true">
                                <md-select name="storeyOverride"
                                           class="lightweight"
                                           ng-model="override.storey"
                                           ng-required="true">
                                    <md-option ng-value="null"
                                               ng-click="vm.applyStoreySelectLogic(override, null, vm.recalculateSimulationData)"></md-option>
                                    <md-option ng-value="'ALL'"
                                               ng-click="vm.applyStoreySelectLogic(override, 'ALL', vm.recalculateSimulationData)">
                                        All Storeys
                                    </md-option>
                                    <md-option ng-value="storey"
                                               ng-click="vm.applyStoreySelectLogic(override, storey, vm.recalculateSimulationData)"
                                               ng-repeat="storey in vm.selectedStoreys">
                                        {{storey.description}}
                                    </md-option>
                                </md-select>
                            </td>
                            <td lr-no-drag="true">
                                <md-select name="groupOverride"
                                           class="lightweight"
                                           ng-model="override.group"
                                           ng-required="true"
                                           ng-change="override.selection = []">
                                    <md-option ng-value="null"
                                               ng-click="vm.applyGroupSelectLogic(override, null)">
                                    </md-option>
                                    <md-option ng-value="group"
                                               ng-click="vm.applyGroupSelectLogic(override, group)"
                                               ng-repeat="group in vm.GROUP_OPTIONS">
                                        {{group}}
                                    </md-option>
                                </md-select>
                            </td>
                            <td lr-no-drag="true">
                                <!-- The values in this control change based on which 'grouping' we are overriding -->
                                <md-select name="selectionverride"
                                           class="lightweight"
                                           ng-model="override.selection"
                                           multiple
                                           ng-required="true">
                                    <md-option ng-value="selection"
                                               ng-repeat="selection in override.selectableArray track by $index"
                                               ng-click="vm.applySelectionLogic(override, selection)">
                                        {{ selection.title || selection }}
                                    </md-option>
                                </md-select>
                            </td>
                            <td lr-no-drag="true">
                                <input formatted-number
                                       decimals="2"
                                       class="lightweight center-input"
                                       style="width: 40px;"
                                       ng-model="override.uValue"/>
                            </td>
                            <td lr-no-drag="true">
                                <input formatted-number
                                       decimals="2"
                                       class="lightweight center-input"
                                       style="width: 40px;"
                                       ng-model="override.shgc"/>
                            </td>
                            <td lr-no-drag="true">
                                <md-select ng-model="override.pOperation"
                                           class="lightweight">
                                    <md-option ng-value="null" ng-click="override.p = null;"></md-option>
                                    <md-option ng-value="'asModelled'" ng-click="override.p = null;">As modelled</md-option>
                                    <md-option ng-value="'equalTo'">P Is equal to</md-option>
                                    <md-option ng-value="'minimumIs'">Minimum P value is</md-option>
                                    <md-option ng-value="'maximumIs'">Maximum P value is</md-option>
                                    <md-option ng-value="'noShading'" ng-click="override.p = null; override.g = null; override.hMax = null;">No Shading</md-option>
                                </md-select>
                            </td>
                            <td lr-no-drag="true">
                                <input formatted-number
                                       decimals="2"
                                       ng-disabled="override.pOperation == null || override.pOperation == 'asModelled' || override.pOperation == 'noShading'"
                                       class="lightweight center-input"
                                       style="width: 40px;"
                                       ng-model="override.p"/>
                            </td>
                            <td lr-no-drag="true">
                                <input formatted-number
                                       decimals="2"
                                       ng-disabled="override.pOperation == 'noShading'"
                                       class="lightweight center-input"
                                       style="width: 40px;"
                                       ng-model="override.g"/>
                            </td>
                            <td lr-no-drag="true">
                                <input formatted-number
                                       decimals="2"
                                       ng-disabled="override.pOperation == 'noShading'"
                                       class="lightweight center-input"
                                       style="width: 40px;"
                                       ng-model="override.hMax"/>
                            </td>
                            <td  lr-no-drag="true"
                                 style="text-align: center;">
                                <button class="feather-icon-button sm"
                                        title="Delete row"
                                        ng-click="vm.deleteOverride(override)">
                                    <img src="/content/feather/trash.svg" />
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <md-button ng-click="vm.addOverride()"
                               class="md-primary md-raised">
                        ADD
                    </md-button>
                </md-card-content>
            </md-card>

            <md-button class="md-primary md-raised"
                       ng-click="vm.export();"
                       ng-disabled="!vm.dataIsValid() || vm.processing == true;"
                       style="margin-left: auto; float: right;">
                Export glazing calc
            </md-button>
        </div>
    </div>

</form>

<style>

    .export-processing-overlay {
        position: absolute; 
        top: 0; 
        bottom: 0; 
        left: 0; 
        right: 0; 

        display: grid; 
        align-items: center; 
        justify-items: center;
        z-index: 9999;

        background-color: rgba(255, 255, 255, 0.75);
    }

    .glazing-calc-modal-header {
        display: grid;
        grid-template-columns: 1fr 200px 30px;
        align-items: center;
        background-color: var(--thermarate-grey);
        padding: 0 24px 0 16px;
        height: 64px;
        max-height: 64px;
    }

    .glazing-calc-modal-header > h4 {
        margin: 0;
    }

</style>