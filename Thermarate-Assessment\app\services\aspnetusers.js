// Name: aspnetusersservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'aspnetusersservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', aspnetusersservice]);

    function aspnetusersservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var initPromise,
            initFailed;
        var canceller = null;
        var useListCache = false;
        var usersData = {};
        var currentFilter = "";
        var baseUrl = config.servicesUrlPrefix + 'aspnetusers/';
        usersData.usersList = [];
        usersData.roles = [];
        usersData.errorMessage = "";

        var service = {
            /* These are the operations that are available from this service. */
            currentFilter: function () { return currentFilter },
            getList: getList,
            getListCancel: getListCancel,
            getRoles: getRoles,
            users: function () { return usersData.users },
            roles: function () { return usersData.roles },
            error: function () { return usersData.errorMessage },
            getUsers: function (userId) { return getUsers(userId) },
            saveUsers: function (users) { return saveUsers(users) },
            resetPassword: function (users) { return resetPassword(users) },
            unlockUser: function (users) { return unlockUser(users) },
            sendWelcomeEmail: function (users) { return sendWelcomeEmail(users) },
            deleteUsers: function (userId) { return deleteUsers(userId) },
            restoreUsers: function (userId) { return restoreUsers(userId) },
            checkUserNameIsAvailable: checkUserNameIsAvailable,
        };

        return service;

        //#region main application operations
        // ----------------------------------

        function getList(forFilter, pageSize, pageIndex, sort, filter, aggregate, filterByCode) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = {};
            if (filterByCode) {
                params.filterByCode = filterByCode;
            }
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = false;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Users list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Check if the passed in userid is available for use (ie. doesn't exist in the system yet)
        function checkUserNameIsAvailable(userName) {
            var wkUrl = '../api/aspnetusers/UserNameIsAvailable?userName=' + userName;

            var deferred = $q.defer();
            $http.get(wkUrl).then(success, fail);
            return deferred.promise;

            function success(resp) {
                deferred.resolve(
                    resp.data
                );
            }
            function fail(error) {
                var msg = "Error checking if username is available: " + error;
                log.logError(msg, error, null, true);
                deferred.reject(msg);
                throw error; // so caller can see it
            }
        }

        //Fetch Role List from server
        function getRoles() {
            var wkUrl = '../api/aspnetusers/GetRoles';  // Gets All Roles for application
            //Get users List from the Server
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {
                usersData.roles = resp.data; //Assign data to roles list
            }
            function fail(error) {
                var msg = "Error getting roles: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Fetch the requested userId from the server
        function getUsers(userId) {
            //Get users from the Server
            return $http({
                url: '../api/aspnetusers/Get/' + userId,
                method: 'GET',
                isArray: true
            }).then(success, fail)
            function success(resp) {

                usersData.users = resp.data; //Assign data to users
            }
            function fail(error) {
                var msg = "Error getting users list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Save users Detail on Server Call
        function saveUsers(users) {
            var isNew = users.userId == null ? true : false;
            usersData.errorMessage = "";
            users.errorMessage = "";
            //Save Users Detail on the Server
            return $http.post('../api/aspnetusers/Post', users)
                .then(success, fail);

            function success(resp) {
                usersData.users = resp.data;
                if (usersData.users.errorMessage != '') {
                    usersData.errorMessage = usersData.users.errorMessage;
                    log.logError(usersData.errorMessage, "", null, true);
                }
                else {
                    log.logSuccess('User Saved.');
                    useListCache = false;
                }
            }
            function fail(error) {
                var msg = "Error saving users detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Unlock a locked out user
        function unlockUser(users) {
            //Unlock user on the server
            var unlockuser = angular.copy(users)
            unlockuser.isLockedOut = false;
            return $http.post('../api/aspnetusers/Post', unlockuser)
                .then(success, fail);

            function success(resp) {
                usersData.users = resp.data;
                log.logSuccess('User unlocked.');
                useListCache = false;
            }
            function fail(error) {
                var msg = "Error unlock user account detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Send welcome email to external user
        function sendWelcomeEmail(users) {
            //Send welcome email on the server
            return $http.post('../api/aspnetusers/SendWelcomeEmailToNewUser?id=' + users.id)
                .then(success, fail);

            function success(resp) {
                log.logSuccess('Welcome email sent.');
                useListCache = false;
            }
            function fail(error) {
                var msg = "Error sending welcome email: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Reset user password
        function resetPassword(users) {
            //Reset user password (to new specified password on the server
            users.isLockedOut = false;
            return $http.post('../api/aspnetusers/ResetPassword', users)
                .then(success, fail);

            function success(resp) {
                usersData.users = resp.data;
                log.logSuccess('Password changed.');
                useListCache = false;
            }
            function fail(error) {
                var msg = "Error changeing password: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        //Delete users Detail on Server Call
        function deleteUsers(userId) {
            initPromise = null;
            // Delete users Detail from the server
            initPromise = $http.delete('../api/aspnetusers/Delete/' + userId).then(success, fail);

            function success(resp) {
                log.logSuccess('User Deleted.');
                useListCache = false;
            }
            function fail(error) {
                var msg = "Error deleting users detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }

        //Restore users Detail on Server Call
        function restoreUsers(userId) {
            initPromise = null;
            // Restore users Detail from the server
            initPromise = $http.delete('../api/aspnetusers/Restore/' + userId).then(success, fail);

            function success(resp) {
                log.logSuccess('User Restored.');
                useListCache = false;
            }
            function fail(error) {
                var msg = "Error restoring users detail: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
            return initPromise;
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

    }

})();
