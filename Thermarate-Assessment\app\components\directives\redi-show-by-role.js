﻿(function () {
    'use strict';

    var app = angular.module('app');
    // Keep a bootstrap downdown open when clicking within the dropdown itself.
    // Useful for forms, etc within a dropdown.
    app.directive('rediShowByRoles', ['security', function (securityservice) {
        return {
            transclude: true,
            restrict: 'A',
            scope: {
                roles: "=rediShowByRoles"
            },
            link: function (scope, elem, attrs, ctrl, transclude) {
                var validated = false;
                var requiredRoles = scope.roles;
                if (requiredRoles && securityservice.currentUser.userRoles) {
                    for (var i = 0; i < securityservice.currentUser.userRoles.length; i++) {
                        if (requiredRoles.indexOf(securityservice.currentUser.userRoles[i].toLowerCase()) > -1) {
                            validated = true;
                            break;
                        }
                    }
                }

                if (validated) {
                    transclude(function (clone, scope) {
                        elem.append(clone);
                    });

                } else {
                    elem.remove();
                }
            }
        };
    }]);
})();