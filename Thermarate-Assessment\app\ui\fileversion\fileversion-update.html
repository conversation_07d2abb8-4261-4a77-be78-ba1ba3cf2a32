<form name="fileversionform" class="main-content-wrapper" novalidate data-ng-controller='FileversionUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <!--Left Side-->
                <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}" >
                    <md-card>
                        <md-card-header>
                            File Version
                        </md-card-header>
                        <md-card-content>

<!-- ******** File ******** -->
                            <md-autocomplete md-input-name="fileId" md-autofocus 
                                         required
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.fileversion.file"
                                         md-search-text="vm.fileIdSearchText"
                                         md-items="item in vm.getfiles(vm.fileIdSearchText)"
                                         md-item-text="item.displayName"
                                         md-require-match
                                         md-floating-label="File">
                                <md-item-template>
                                    <span md-highlight-text="vm.fileIdSearchText">{{item.displayName}}</span>
                                </md-item-template>
                                <div ng-messages="fileversionform.fileId.$error">
                                    <div ng-message="required">File is required.</div>
                                </div>
                            </md-autocomplete>

<!-- ******** U R L ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>U R L</label>
                                <input type="text" name="uRL" 
                                        ng-model="vm.fileversion.uRL"  
                                    />
                                <div ng-messages="fileversionform.uRL.$error">
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.fileversion"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>

<!-- ******** Right Side ******** -->
            <div ng-if="vm.newRecord==false" flex-gt-sm="50">
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="fileversionform.$invalid" ng-show="vm.fileversion.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.fileversion.versionNo>0 && vm.fileversion.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.fileversion.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
