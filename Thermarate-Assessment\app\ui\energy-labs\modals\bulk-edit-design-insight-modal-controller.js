(function () {
    'use strict';
    var controllerId = 'BulkEditDesignInsightModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'standardmodelservice', bulkEditDesignInsightModalController]);
    function bulkEditDesignInsightModalController(common, $scope, $mdDialog, standardmodelservice) {

        // - VARIABLES - //

        let vm = this;
        vm.isBusy = true;

        vm.data = {
            bulkEditAction: "COPYTOMODEL",
            selectedVariationId: null
        };

        vm.options = $scope.options;
        vm.thisHomeModelId = $scope.thisHomeModelId;
        vm.variationOfId = $scope.variationOfId;
        vm.variationOptionsList = $scope.variationOptionsList;
        vm.variationOptionsSettings = $scope.variationOptionsSettings;

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Confirm
        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        // Cancel
        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();