(function () {
    // The AdjacentFloorCoveringUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'FloorCoveringUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'adjacentfloorcoveringservice', 'manufacturerservice', 'uuid4', 'security', floorCoveringUpdateController]);
    function floorCoveringUpdateController($rootScope, $scope, $mdDialog, $stateParams,
        $state, adjacentfloorcoveringservice, manufacturerservice, uuid4, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Floor Covering';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.floorCovering = {
            adjacentFloorCoveringCode: uuid4.generate(),
            title: null,
            solarAbsorptance: null,
            manufacturer: null
        };

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Floor Covering";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.adjacentFloorCoveringCode = $scope.floorCoveringCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.adjacentFloorCoveringCode = $stateParams.floorCoveringCode;
        }

        // Get data for object to display on page
        var floorCoveringCodePromise = null;
        if (vm.adjacentFloorCoveringCode != null) {

            floorCoveringCodePromise = adjacentfloorcoveringservice.getAdjacentFloorCovering(vm.adjacentFloorCoveringCode)
            .then(function (data) {
                if (data != null) {
                    vm.floorCovering = data;
                    console.log("Floor is: ", vm.floorCovering);
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("floorCovering-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                adjacentfloorcoveringservice.createAdjacentFloorCovering(vm.floorCovering).then(function(data){
                    vm.floorCovering = data;
                    vm.adjacentFloorCoveringCode = vm.floorCovering.adjacentFloorCoveringCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                adjacentfloorcoveringservice.updateAdjacentFloorCovering(vm.floorCovering).then(function(data){
                    if (data != null) {
                        vm.floorCovering = data;
                    vm.adjacentFloorCoveringCode = vm.floorCovering.adjacentFloorCoveringCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            adjacentfloorcoveringservice.deleteAdjacentFloorCovering(vm.adjacentFloorCoveringCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            adjacentfloorcoveringservice.undoDeleteAdjacentFloorCovering(vm.adjacentFloorCoveringCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.removeMaterialCode = function(index) {
            vm.floorCovering.materialCodesArray.splice(index, 1);
        }

    }
})();