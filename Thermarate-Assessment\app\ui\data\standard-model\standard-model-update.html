<form name="standardModelform"
      class="main-content-wrapper"
      style="min-width:600px;"
      novalidate data-ng-controller='StandardModelUpdateCtrl as vm'>

    <!-- Close button for modal -->
    <md-toolbar ng-if="vm.isModal">
        <div class="md-toolbar-tools">
            <h2>{{vm.title}}</h2>
            <span flex></span>
            <md-button class="md-icon-button" ng-click="vm.cancel()">
                <md-icon>close</md-icon>
            </md-button>
        </div>
    </md-toolbar>

    <div class="widget home-design-body" ng-cloak>

        <!-- Breadcrumbs -->
        <div
            ng-if="!vm.newRecord"
            ng-style="{'visibility': vm.project.clientName == null || vm.project.projectName == null ? 'hidden' : 'visible'}"
            class="navigation-text"
            style="margin-left:0 !important"
        >
            <div ng-click="vm.navigateAttempt('client-listform', null)" class="clickable">Clients</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId})" class="clickable">{{vm.project.clientName}}</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('client-updateform', {clientId: vm.project.clientId, tabIndex: 7})" class="clickable">EnergyLab</div>
            <div>></div>
            <div ng-click="vm.navigateAttempt('project-updateform', {projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}}</div>
            <div>></div>
            <b>{{vm.standardModel.title}}</b>
        </div>

        <!-- The Basics -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header ng-if="!vm.newRecord">
                <span class="md-title">Home Design</span>
            </md-card-header>
            <md-card-content>

                <fieldset redi-enable-roles="settings__settings__edit">

                    <!--  Home Design  -->
                    <md-input-container class="md-block" flex="100">
                        <label>Home Design</label>
                        <input type="text"
                               name="title"
                               ng-model="vm.standardModel.title"
                               md-autofocus
                               md-maxlength="100"
                               ng-required />
                        <div ng-messages="standardModelform.title.$error">
                            <div ng-message="required">Title is required.</div>
                            <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                        </div>
                    </md-input-container>

                    <!-- Description -->
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Description</label>
                        <input type="text" name="description"
                               ng-model="vm.standardModel.description"
                               md-maxlength="500"/>
                        <div ng-messages="standardModel.description.$error">
                            <div ng-message="md-maxlength">Too many characters entered, max length is 500.</div>
                        </div>
                    </md-input-container>

                    <!--  Project  -->
                    <md-autocomplete md-input-name="projectId"
                                     required
                                     ng-disabled="true"
                                     md-input-minlength="2"
                                     md-min-length="0"
                                     md-selected-item="vm.standardModel.project"
                                     md-search-text="vm.projectIdSearchText"
                                     md-items="item in vm.getProjects(vm.projectIdSearchText)"
                                     md-item-text="item.projectName"
                                     md-require-match
                                     md-floating-label="Project"
                                     md-selected-item-change="vm.projectChanged();">
                        <md-item-template>
                            <span md-highlight-text="vm.projectIdSearchText">{{item.projectName}}</span>
                        </md-item-template>
                        <div ng-messages="standardModelform.projectId.$error">
                            <div ng-message="required">Project is required.</div>
                        </div>
                    </md-autocomplete>

                    <!-- Active -->
                    <div>
                        <label style="font-size: 9px;">Active</label>
                        <md-switch style="width: fit-content;" ng-model="vm.standardModel.isActive" ng-disabled="!vm.project.isActive" ng-change="vm.toggleVariationsIsActive()">
                                <span ng-show="vm.standardModel.isActive === false">The model and all options will <b>not</b> appear in the EnergyLab.</span>
                                <span ng-show="vm.standardModel.isActive === true">The model and all options will appear in the EnergyLab.</span>
                        </md-switch>
                    </div>

                    <!-- 3D Model -->
                    <div style="margin-top: 18px">
                        <label style="font-size: 9px">3D Model</label>
                        <md-switch style="width: fit-content; margin-top: 5px;" ng-model="vm.standardModel.view3dFloorPlans" ng-disabled="!vm.project.energyLabsSettings.view3dFloorPlans" ng-change="vm.toggleVariations3dModel()">
                        </md-switch>
                    </div>

                    <!-- Cost Estimate -->
                    <div style="margin-top: 18px">
                        <label style="font-size: 9px">Cost Estimate</label>
                        <md-switch style="width: fit-content; margin-top: 5px;" ng-model="vm.standardModel.costEstimateEnabled" ng-disabled="!vm.project.energyLabsSettings.costEstimateEnabledDefault" ng-change="vm.toggleVariationsCostEstimate()">
                        </md-switch>
                    </div>

                    <!-- Design Insights -->
                    <div style="margin-top: 18px">
                        <label style="font-size: 9px">Design Insights</label>
                        <md-switch style="width: fit-content; margin-top: 5px;" ng-model="vm.standardModel.variableMetadata.designInsightsEnabled" ng-change="vm.toggleVariationsDesignInsights()">
                        </md-switch>
                    </div>
                </fieldset>

                <div class="col-md-12" ng-if="vm.newRecord==false">
                    <div rd-display-created-modified ng-model="vm.standardModel"></div>
                </div>

            </md-card-content>
        </md-card>

        <!-- Variation Options -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}">

            <md-card-header>
                <span class="md-title">Home Design Variation Options</span>
                <!-- Menu -->
                <md-menu style="margin-top:5px;">
                    <img md-menu-origin
                            class="clickable"
                            ng-click="$mdOpenMenu()"
                            src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Copy From Home Design -->
                        <md-menu-item><md-button ng-click="vm.copyVariationOptionsToModel(true)">
                            Copy From Home Design
                        </md-button></md-menu-item>
                        <!-- Copy From Project -->
                        <md-menu-item><md-button ng-click="vm.copyVariationOptionsToModel('project')">
                            Copy From Project
                        </md-button></md-menu-item>
                        <!-- Copy To Home Design -->
                        <md-menu-item ng-show="!vm.newRecord"><md-button ng-click="vm.copyVariationOptionsToModel(false)">
                            Copy To Home Design
                        </md-button></md-menu-item>
                        <md-menu-divider></md-menu-divider>
                        <!-- Delete -->
                        <md-menu-item><md-button style="color: orangered;" ng-click="vm.deleteAllVariationOptions()">
                            Clear All
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>

            <md-card-content style="padding-bottom:35px;">

                <div style="display: flex; flex-direction: column; gap: 50px;">

                    <!-- FOR EACH -->
                    <div ng-repeat="category in vm.variationCategories track by $index">

                        <!-- Label -->
                        <div style="font-size:18px; font-weight:bold;">{{vm.toSplitTitleCase(category)}}</div>

                        <!-- Design Insights Toggle -->
                        <div>
                            <md-switch
                                style="width: fit-content;"
                                ng-model="vm.standardModel.variationOptionsSettings[vm.firstCharLowerCase(category) + 'IsActive']"
                                ng-disabled="!vm.standardModel.project.energyLabsSettings['varCategory' + category + 'Active']"
                            >
                                <span>Active</span>
                            </md-switch>
                        </div>

                        <!-- List -->
                        <div ng-repeat="option in vm.getVariationOptions(category) track by option.standardHomeModelVariationOptionId"
                             style="display:flex; column-gap:10px;"
                        >
                            <!-- Checkbox -->
                            <md-input-container class="md-block" style="margin-top: 6px; width:max-content;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="option.checkboxSelected"
                                             ng-change="vm.updateBulkSelectStatus(vm.standardModel.variationOptionsList, vm.bulkStatus[category]);">
                                </md-checkbox>
                            </md-input-container>
                            <!-- Input -->
                            <md-input-container class="md-block" style="margin:0; flex-grow:1">
                                <input id="option-input-{{option.standardHomeModelVariationOptionId}}" style="width:100%" type="text" name="option-{{option.optionName}}" ng-model="option.optionName" />
                            </md-input-container>
                            <!-- Menu -->
                            <md-menu style="margin-top:5px;">
                                <img md-menu-origin
                                        class="clickable"
                                        ng-click="$mdOpenMenu()"
                                        src="/content/feather/more-horizontal.svg"/>
                                <md-menu-content>
                                    <!-- Copy to Home Design -->
                                    <md-menu-item ng-if="!vm.newRecord"><md-button ng-click="vm.copy(category, option)">
                                        Copy to Home Design
                                    </md-button></md-menu-item>
                                    <!-- Duplicate -->
                                    <md-menu-item><md-button ng-click="vm.copyVariationOption(category, option)">
                                        Duplicate
                                    </md-button></md-menu-item>
                                    <!-- Move Up -->
                                    <md-menu-item ng-show="$index > 0"><md-button ng-click="vm.moveVarOptionUp(category, option)">
                                        Move Up
                                    </md-button></md-menu-item>
                                    <!-- Move Down -->
                                    <md-menu-item ng-show="$index < vm.getVariationOptions(category).length-1"><md-button ng-click="vm.moveVarOptionDown(category, option)">
                                        Move Down
                                    </md-button></md-menu-item>
                                    <md-menu-divider></md-menu-divider>
                                    <!-- Delete -->
                                    <md-menu-item><md-button style="color: orangered;" ng-click="vm.deleteVariationOption(option)">
                                        Delete
                                    </md-button></md-menu-item>
                                </md-menu-content>
                            </md-menu>
                        </div>

                        <!-- Buttons -->
                        <div style="width:100%; display:flex; justify-content:space-between;">
                            <!-- Bulk Edit -->
                            <md-button style="margin-right:-10px"
                                       class="md-raised md-primary"
                                       ng-click="vm.launchOptionsBulkEditModal(category)"
                                       ng-disabled="!(vm.bulkStatus[category].selectAllCheckboxState || vm.bulkStatus[category].isIndeterminate)">
                                BULK EDIT
                            </md-button>
                            <!-- Add Option -->
                            <md-button class="md-raised md-primary"
                                       ng-click="vm.addVariationOption(category)">
                                Add Option
                            </md-button>
                        </div>

                    </div>

                </div>

            </md-card-content>

        </md-card>

        <!-- Services Defaults -->
        <md-card ng-if="!vm.newRecord" flex-gt-lg="{{vm.isModal ? null : 50}}">
            <md-card-header>
                <!-- Title -->
                <span class="md-headline">Services Defaults</span>
                <!-- Menu -->
                <md-menu class="three-dot-menu">
                    <img md-menu-origin
                         class="clickable"
                         ng-click="$mdOpenMenu()"
                         src="/content/feather/more-horizontal.svg"/>
                    <md-menu-content>
                        <!-- Restore Defaults -->
                        <md-menu-item><md-button ng-click="vm.restoreServiceDefaults()">
                            <span>Restore Defaults</span>
                        </md-button></md-menu-item>
                        <!-- Clear All -->
                        <md-menu-item><md-button ng-click="vm.clearAllServiceDefaults()">
                            <span style="color: orangered;">Clear All</span>
                        </md-button></md-menu-item>
                    </md-menu-content>
                </md-menu>
            </md-card-header>
            <md-card-content>

                <div>

                    <!-- Select All -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.allServDefsSelected"
                                         ng-click="vm.selectAllServiceDefaultCheckboxes()">
                            </md-checkbox>
                        </md-input-container>
                    </div>

                    <!-- Heating System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceHeatingTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label class="">Heating System</label>
                            <md-select md-container-class="md-select-show-all"
                                       ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.spaceHeatingRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceHeatingRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>{{vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpDucted' || vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Heating System Energy Rating (GEMS 2019)'
                                        : 'Heating System Energy Rating'}}</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Cooling System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceCoolingTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Cooling System</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode"
                                       md-container-class="md-select-show-all"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Energy Rating (GEMS 2019) -->
                        <md-input-container ng-if="vm.spaceCoolingRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaceCoolingRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>{{vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpDucted' || vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.serviceTypeCode == 'HeatPumpNonDucted'
                                        ? 'Cooling System Energy Rating (GEMS 2019)'
                                        : 'Cooling System Energy Rating'}}</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Water Heater Type -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.waterHeaterTypeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Water Heater Type</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.waterHeating.serviceTypeCode"
                                       md-container-class="md-select-show-all">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']"
                                           ng-value="v.serviceTypeCode"
                                           ng-click="vm.clearResults()">
                                    {{v.title}}
                                </md-option>
                            </md-select>
                        </md-input-container>

                    </div>

                    <!-- Swimming Pool -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Swimming Pool</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.exists"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Swimming Pool Volume (L) -->
                        <md-input-container ng-if="vm.swimmingPoolVolumeVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolVolumeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Swimming Pool Volume (L)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.volume"
                                   formatted-number
                                   decimals="0" />
                        </md-input-container>

                        <!-- Pool Pump Energy Rating -->
                        <md-input-container ng-if="vm.swimmingPoolRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.swimmingPoolRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Pool Pump Energy Rating</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Spa -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.exists"
                                       ng-change="vm.updateServDefsVisibility();">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Spa Volume (L) -->
                        <md-input-container ng-if="vm.spaVolumeVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaVolumeSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa Volume (L)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.volume"
                                   formatted-number
                                   decimals="0" />
                        </md-input-container>

                        <!-- Spa Pump Energy Rating -->
                        <md-input-container ng-if="vm.spaRatingVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.spaRatingSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Spa Pump Energy Rating</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa.gems2019Rating"
                                   formatted-number
                                   decimals="1" />
                        </md-input-container>

                    </div>

                    <!-- Photovoltaic (PV) System -->
                    <div>
                        <md-input-container class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.photovoltaicExistsSelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Photovoltaic (PV) System</label>
                            <md-select ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic.exists"
                                       ng-change="vm.updateServDefsVisibility();"
                                       ng-required="true">
                                <md-option ng-value="null">No Default</md-option>
                                <md-option ng-value="true"
                                           ng-click="vm.clearResults()">
                                    Yes
                                </md-option>
                                <md-option ng-value="false"
                                           ng-click="vm.clearResults()">
                                    No
                                </md-option>
                            </md-select>
                        </md-input-container>

                        <!-- Photovoltaic (PV) System Capacity (kW) -->
                        <md-input-container ng-if="vm.photovoltaicCapacityVisible"
                                            class="md-block vertically-condensed service-defaults-field">
                            <md-checkbox class="service-defaults-checkbox"
                                         ng-model="vm.photovoltaicCapacitySelected"
                                         ng-click="vm.updateServDefSelectAllState()">
                            </md-checkbox>
                            <label>Photovoltaic (PV) System Capacity (kW)</label>
                            <input ng-model="vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic.capacity"
                                   formatted-number
                                   decimals="2" />
                        </md-input-container>

                    </div>
                </div>

                <!-- Update All -->
                <md-button class="md-raised md-primary"
                            ng-click="vm.launchServDefsUpdateAllConfrimation()"
                            ng-disabled="!vm.anyServDefsSelected">
                    Update All
                </md-button>

            </md-card-content>
        </md-card>

        <!-- Variations List -->
        <md-card flex-gt-lg="{{vm.isModal ? null : 50}}" ng-if="!vm.newRecord" style="min-width: max-content;">

            <md-card-header>
                <span class="md-title">Home Design Variations</span>
            </md-card-header>

            <md-card-content style="padding-bottom: 35px;">
                <standard-model-variation-list ng-if="vm.standardModel.variationOptionsList != null"
                                               project-id="vm.standardModel.projectId"
                                               variation-of-id="vm.standardModel.standardHomeModelId"
                                               variation-options-list="vm.standardModel.variationOptionsList"
                                               variation-options-settings="vm.standardModel.variationOptionsSettings"
                                               parent-is-active="vm.standardModel.isActive"
                                               parent-3d-model="vm.standardModel.view3dFloorPlans"
                                               parent-cost-estimate="vm.standardModel.costEstimateEnabled"
                                               parent-design-insights="vm.standardModel.variableMetadata.designInsightsEnabled"
                                               on-initialise-complete="vm.resetChangeDetection()"
                                               on-delete="vm.onModelDelete()"
                                               passed-up-models="vm.standardModel.variationList"
                                               row-click-callback="vm.variationRowClick(variationId)">
                </standard-model-variation-list>
            </md-card-content>

        </md-card>

        <!-- Buttons -->
        <div data-cc-widget-button-bar
             data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised md-primary"
                       ng-disabled="standardModelform.$invalid || vm.editPermission == false || vm.floorplannerLinkEditing || vm.floorplannerLinkWrongSite || vm.floorplannerLinkInvalid || (!vm.newRecord && !vm.hasChanges())"
                       ng-show="vm.standardModel.deleted!=true"
                       ng-click="vm.save()">
                Save
            </md-button>
            <md-button class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="!vm.newRecord && vm.standardModel.title != null && vm.standardModel.deleted != true"
                       ng-confirm-click="vm.delete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to delete this record.">
                Delete
            </md-button>
            <md-button class="md-raised"
                       redi-enable-roles="settings__settings__delete"
                       ng-show="vm.standardModel.deleted==true"
                       ng-confirm-click="vm.undoDelete()"
                       ng-confirm-condition="true"
                       ng-confirm-message="Please confirm you want to RESTORE this record.">
                Restore
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>
    </div>
</form>

<style>

    .home-design-body md-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .home-design-body .three-dot-menu {
    }

    .plan-images-container {
        display: flex;
        flex-wrap: wrap;
        column-gap: 30px;
        max-width: 95vw;
    }

    @media screen and (max-width: 1300px) {
        .plan-images-container {
            grid-template-columns: 400px;
        }
    }

    .variation-option {
        transition: 300ms;
    }

    .variation-option:hover {
        background-color: #f1f1f1;
    }

    .featureGroupHeading {
        border-bottom: solid 1px #8d8d8d;
        font-weight: bold;
        padding-bottom: 6px;
        margin-top: 22px;
        margin-bottom: 18px;
    }

    .service-defaults-field {
        display: flex !important;
        min-height: 50px;
    }

        .service-defaults-field > label {
            margin-left: 35px;
        }

        .service-defaults-field > md-select {
            flex: 1;
        }

    .service-defaults-checkbox {
        margin-right: 5px !important;
    }

</style>