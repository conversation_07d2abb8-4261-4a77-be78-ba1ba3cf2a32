﻿<md-select style="margin:0;"
           ng-model="vm.UI_selections"
           md-selected-text="vm.getMultiSelectDropdownText(vm.UI_selections, vm.displayKey)"
           multiple>
    <md-option ng-repeat="item in vm.UI_list"
               ng-value="item"
               ng-click="vm.isDisabled ? null : vm.selectItem(item)"
               ng-disabled="vm.isDisabled">
        {{item[vm.displayKey]}}
    </md-option>
</md-select>