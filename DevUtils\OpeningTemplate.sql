USE [thermarate];

SELECT [ConstructionId]
      ,[Deleted]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[Description]
      ,[DisplayDescription]
      ,[ManufacturerId]
      ,[ExternalConstructionId]
      ,[AdjacencyCode]
      ,[UnitOfMeasureCode]
      ,[Comments]
      ,[LifeCycleDataJson]
      ,[ChenathDataJson]
      ,[FR5DataJson]
      ,[HeroDataJson]
      ,[EPDataJson]
      ,[ConstructionCategoryCode]
      ,[FrameMaterialCode]
      ,[IsThermalBreak]
      ,[Tilt]
      ,[OpeningStyleCode]
      ,[HasWeatherStrip]
      ,[HasInsectScreen]
      ,[GlassDataJson]
      ,[PerformanceJson]
      ,[ShaftReflectance]
      ,[ShaftWallRValue]
      ,[ShaftLength]
      ,[HasInteriorShades]
      ,[ExteriorShadeFactor]
      ,[HasDiffuserShades]
      ,[VisualisationDataJson]
      ,[FrameSolarAbsorptance]
      ,[AllowEditingFrameSolarAbsorptance]
      ,[FrameColourId]
      ,[AllowEditingFrameColour]
      ,[NccOpeningStyleCode]
      ,[Azimuth]
      ,[ShadeProjection]
      ,[ShadeOffset]
      ,[GrossArea]
      ,[NetArea]
      ,[Height]
      ,[Width]
      ,[HasExteriorShades]
      ,[AllowEditingOpeningStyle]
      ,[ShowInReport]
      ,[GrossAreaIsManuallyOverridden]
      ,[GrossAreaOriginalValue]
      ,[OpenabilityIsManuallyOverridden]
      ,[OpenabilityOriginalValue]
      ,[ConstructionSubCategoryCode]
  FROM [dbo].[RSS_OpeningTemplate]
