USE [thermarate];

SELECT [event].[JobEventId]
      ,[event].[JobId]
	  ,[job].[JobReference] [__Job]
	  ,[job].[StatusCode] [__JobStatus]
      ,[event].[EventTypeCode]
      ,[event].[Description]
      ,[event].[CreatedOn]
      ,[event].[CreatedByName]
      ,[event].[ModifiedOn]
      ,[event].[ModifiedByName]
      ,[event].[Deleted]
  FROM [dbo].[RSS_JobEvent] [event]
  INNER JOIN [dbo].[RSS_Job] [job] ON [event].[JobId] = [job].[JobId]
