(function () {

    'use strict';
    var controllerId = 'EnergyLabsConfigureController';
    angular.module('app')
        .controller(controllerId, ['$stateParams', '$state', '$mdDialog', '$rootScope', 'common', 'uuid4', 'standardmodelservice', 'projectservice', 'addressservice', 'energylabsservice', energyLabsConfigureController]);

    function energyLabsConfigureController($stateParams, $state, $mdDialog, $rootScope, common, uuid4, standardmodelservice, projectservice, addressservice, energylabsservice) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;

        vm.projectId = $stateParams.projectId;
        vm.project = null;

        vm.modelList = null;

        vm.filterData = {};
        vm.viewingModels = null;

        vm.isBusy = false;
        vm.mode = $stateParams.standardHomeModelId != null ? 'configure' : 'select'; // 'select' | 'configure'
        vm.showResults = false;

        vm.addCompare = standardmodelservice.addCompare;
        vm.removeCompare = function (modelsList, cardClickedIndex) {
            standardmodelservice.removeCompare(modelsList, cardClickedIndex);
            setTimeout(() => $rootScope.$apply(), 10);
        }

        vm.copyFromHighlightEffect = standardmodelservice.copyFromHighlightEffect;

        vm.showDesignInsights = standardmodelservice.showDesignInsights;
        vm.showCostEstimates = standardmodelservice.showCostEstimates;
        vm.toSplitTitleCase = common.toSplitTitleCase;

        // Breadcrumbs
        vm.backToProjects = function () {
            $state.go('energy-labs-parent-menu');
        }

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        async function initialize() {
            vm.trayExpanded = false;
            projectservice.getProject(vm.projectId).then(data => {
                vm.project = data;
                vm.updateFilteredList({});
            });
            if ($stateParams.standardHomeModelId) {
                var model = await standardmodelservice.getStandardModel($stateParams.standardHomeModelId);
                model.selectedVariation = { standardHomeModelId: $stateParams.variationId };
                let prefilledData = {
                    suburb: $stateParams.suburb,
                    optionData: await standardmodelservice.getStandardModelOption($stateParams.standardHomeModelOptionId)
                };
                // Delete these values so doesn't cause issue where same results show even when changing Variation.
                delete prefilledData.optionData.standardHomeModelId;
                delete prefilledData.optionData.standardHomeModelOptionId;
                vm.select(model, prefilledData, true);
            }
        }

        // ----------------------------------- //
        // - HANDLES (Select Design Process) - //
        // ----------------------------------- //

        // Sort
        vm.updateSort = function (sortBy) {
            vm.modelList = standardmodelservice.applySort(sortBy, vm.modelList);
        }

        // Call backend to update filter options, filter count data, models list and selected Variations if Variation needs to change
        vm.updateFilteredList = function({ filterData }) {
            vm.currentTotal = null;
            // Get list of variations
            let selectedVariationsForModels = {};
            vm.modelList?.forEach(m => {
                selectedVariationsForModels[m.standardHomeModelId] = m.selectedVariation.standardHomeModelId;
            });
            let designOptions = vm.project.energyLabsSettings.distinctDesignOptions;
            designOptions.features = standardmodelservice.allFeatures;
            designOptions.categories = standardmodelservice.categories;
            vm.modelList = null;
            var phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
            standardmodelservice.getForProjectMultiFiltered(
                vm.projectId,
                {
                    fields: standardmodelservice.multiFiltersFields,
                    distinctDesignOptions: vm.project.energyLabsSettings.distinctDesignOptions,
                    appliedFilters: filterData,
                    selectedVariationsForModels,
                    countParentModelsOnly: true
                }
            ).then(response => {
                vm.modelList = response.parentModelList;
                vm.modelList.forEach(m => {
                    m.selectedForCompare = false;
                    m.defaultVariation = m.selectedVariation;
                });
                vm.filterCountData = response.filterCountData;
                if (filterData == null) {
                    vm.totalWithoutFilters = response.totalItems;
                } else {
                    vm.currentTotal = response.totalItems;
                }
                vm.anyFiltersApplied = standardmodelservice.anyFiltersApplied(filterData);
                vm.initialised = true;
            });
        }

        // Switcher button click, close dropdowns on all other cards
        vm.switcherOptionButtonClicked = function (model) {
            vm.modelList.filter(m => m.standardHomeModelId != model.standardHomeModelId).forEach(m => {
                Object.keys(m.dropdownFields ?? []).forEach(key => {
                    m.dropdownFields[key].expanded = false;
                    m.dropdownFields[key].justExpanded = false;
                });
                m.inOptionsSelectionsMode = false;
            });
        }

        // Select
        vm.select = function (building, prefilledData = null, autoRunCalc = false) {
            standardmodelservice.getStandardModel(building.selectedVariation.standardHomeModelId).then(model => {
                vm.mode = 'configure';
                building.selectedVariation = model;
                building.include = true;
                building.uiRenderId = uuid4.generate();
                vm.viewingModels = [ building ];
                vm.resetBuilding(building.selectedVariation);
                vm.resetBuildingToDefaults(building.selectedVariation);
                if (prefilledData != null) {
                    let optionData = {
                        ...building.selectedVariation.optionData,
                        ...prefilledData.optionData
                    }
                    building.selectedVariation = {
                        ...building.selectedVariation,
                        ...prefilledData,
                        optionData: optionData
                    };
                } else {
                    standardmodelservice.assignDefaults([building.selectedVariation]);
                }
                // Set whether "multi-orientate" option should be available
                let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
                vm.viewingModels.forEach(m => {
                    if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                        m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                    }
                });
                if (autoRunCalc) {
                    vm.runCalc();
                }
            });
        }

        // Compare Selecting
        vm.anySelectedForCompare = function () {
            return vm.modelList?.some(m => m.selectedForCompare);
        }
        vm.numSelectedForCompare = function () {
            return vm.modelList?.filter(m => m.selectedForCompare).length;
        }
        vm.anyCompareSlotsLeft = function () {
            return vm.numSelectedForCompare() < 3;
        }
        vm.selectModelForCompare = function (parentModel) {
            if (vm.anyCompareSlotsLeft() || parentModel.selectedForCompare == true) {
                parentModel.selectedForCompare = !parentModel.selectedForCompare;
                // IF now none left, set vm.trayExpanded to false
                if (vm.numSelectedForCompare() == 0) {
                    vm.trayExpanded = false;
                }
                // ELSE IF just added and only 1 selected, run extend height animation
                else if (parentModel.selectedForCompare && vm.numSelectedForCompare() == 1) {
                    setTimeout(() => document.getElementById("tray").classList.add("tray-expanded"), 600);
                    setTimeout(() => {
                        if (!vm.trayExpanded) {
                            document.getElementById("tray")?.classList.remove("tray-expanded");
                        }
                    }, 2500);
                }
                // ELSE IF just added, run bounce animcation
                else if (parentModel.selectedForCompare) {
                    document.getElementById("expand-button").classList.remove("bounce");
                    setTimeout(() => document.getElementById("expand-button").classList.add("bounce"), 0);
                }
            }
        }
        vm.clearCompareSelections = function () {
            vm.modelList.forEach(m => m.selectedForCompare = false);
        }
        vm.startCompare = function() {
            let comparingModels = vm.modelList?.filter(m => m.selectedForCompare);
            let getModelCalls = comparingModels.map(m => standardmodelservice.getStandardModel(m.selectedVariation.standardHomeModelId));
            Promise.all(getModelCalls).then(results => {
                for (let [i, modelResult] of results.entries()) {
                    vm.mode = 'configure';
                    comparingModels[i].selectedVariation = modelResult;
                    comparingModels[i].include = true;
                    vm.resetBuilding(comparingModels[i].selectedVariation);
                    vm.resetBuildingToDefaults(comparingModels[i].selectedVariation);
                    standardmodelservice.assignDefaults([comparingModels[i].selectedVariation]);
                    comparingModels[i].selectedVariation.copyAcrossData = {};
                    comparingModels[i].uiRenderId = uuid4.generate();
                }
                vm.viewingModels = comparingModels;
                // Set whether "multi-orientate" option should be available
                let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
                vm.viewingModels.forEach(m => {
                    if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                        m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                    }
                });
            });
        }

        // ---------------------------- //
        // - HANDLES (Configure Page) - //
        // ---------------------------- //

        // Go back to selecting Model
        vm.reset = function () {
            vm.mode = 'select';
            vm.showResults = false;
            vm.compare = false;
            vm.trayExpanded = false;

            vm.modelList.forEach(model => {
                model.selected = false;
                model.optionData = {};
                model.selectedForCompare = false;
                model.selectedVariation = model.defaultVariation;
            });

            vm.viewingModels = null;
            vm.filterData = {};
            vm.updateFilteredList({ filterData: vm.filterData });
        }

        // Reset to defaults
        vm.resetBuildingToDefaults = function(variation, ignoreSuburbData = false) {
            let origNorthOffset = variation.optionData.northOffset;
            let origNatHers = variation.optionData.natHERSClimateZone;
            standardmodelservice.assignDefaults([variation]);
            if (ignoreSuburbData) {
                variation.optionData.northOffset = origNorthOffset;
                variation.optionData.natHERSClimateZone = origNatHers;
            } else {
                // Suburb
                if (vm.project.lockWOHLocation && vm.project.suburb != null) {
                    variation.suburb = vm.project.suburbName;
                    // Get nathers zone from suburb
                    addressservice.climateZoneCodeByPostCode(vm.project.suburb.postcode).then(climateZone => {
                        variation.optionData.natHERSClimateZone = climateZone.slice(3);
                    });
                } else {
                    variation.suburb = null;
                    variation.suburbObject = null;
                }
                variation.modelBlockShowError = false;
                // State
                if (vm.project.lockWOHLocation && vm.project.stateCode != null) {
                    variation.stateCode = vm.project.stateCode;
                } else {
                    variation.stateCode = null;
                }
                if (vm.energyLabsForm != null) {
                    vm.energyLabsForm.$setPristine();
                    vm.energyLabsForm.$setUntouched();
                }
            }
            vm.showResults = false;
        }

        // Clear
        vm.resetBuilding = function (variation, ignoreSuburbData = false) {
            if (!ignoreSuburbData) {
                // Only clear natHERSCliamteZone, suburb and state if lockWOHLocation toggled off
                if (!vm.project.lockWOHLocation) {
                    variation.optionData = {};
                    variation.suburb = null;
                    variation.suburbObject = null;
                    variation.stateCode = null;
                } else if (!ignoreSuburbData) {
                    variation.optionData = { natHERSClimateZone: variation.optionData?.natHERSClimateZone };
                }
            }
            if (vm.energyLabsForm != null) {
              vm.energyLabsForm.$setPristine();
              vm.energyLabsForm.$setUntouched();
            }
            variation.modelBlockShowError = false;
            vm.showResults = false;
        }

        // Run
        vm.runCalc = async function () {

            if (vm.isBusy)
                return;

            try {

                vm.isBusy = true;

                vm.targetEnergyRating = vm.project.energyLabsSettings.heatingCoolingLoadLimits ? vm.project.energyLabsSettings.targetEnergyRating : null;

                // Orientate
                vm.viewingModels.forEach((m, i) => {
                    m.selectedVariation.modelIndex = i;
                });
                vm.orientateVariations = vm.viewingModels.filter(m => m.selectedVariation.optionData.northOffset == 'Multi-Orientation').map(m => m.selectedVariation);
                if (vm.orientateVariations.length > 0) {
                    vm.orientateVariations.forEach(model => model.optionPerformance = null);
                    var optionData = vm.orientateVariations.map(x => ({
                        standardHomeModelId: x.standardHomeModelId,
                        stateCode: x.stateCode,
                        ...x.optionData
                    }));
                    vm.targetEnergyRating = vm.project.energyLabsSettings.heatingCoolingLoadLimits ? vm.project.energyLabsSettings.targetEnergyRating : null;
                    let matchingOptions = await standardmodelservice.orientate(vm.project.projectId, optionData.map(d => ({ ...d, northOffset: null })), vm.targetEnergyRating);
                    matchingOptions.forEach((o, i) => {
                        vm.orientateVariations[i].orientateResults = matchingOptions[i];
                    })
                    vm.orientateVariations.forEach(v => {
                        v.orientateResults.modelIndex = v.modelIndex;
                    });
                    energylabsservice.renderDonutChart(matchingOptions, vm.targetEnergyRating, vm.project.energyLabsSettings.heatingCoolingLoadLimits);
                }
                // Normal
                vm.configureVariations = vm.viewingModels.filter(m => m.selectedVariation.optionData.northOffset != 'Multi-Orientation').map(m => m.selectedVariation);
                if (vm.configureVariations.length > 0) {
                    // Clear old results
                    vm.configureVariations.forEach(model => model.optionPerformance = null);
                    var optionData = vm.configureVariations.map(x => ({
                        standardHomeModelId: x.standardHomeModelId,
                        stateCode: x.stateCode,
                        ...x.optionData
                    }));
                    var results = await standardmodelservice.compare(vm.project.projectId, optionData, vm.targetEnergyRating);
                    await Promise.all(vm.viewingModels.map(v => {
                        return standardmodelservice.getStandardModel(v.selectedVariation.standardHomeModelId).then(data => {
                            Object.keys(v.selectedVariation.variableMetadata.generalOptionData).forEach(key => {
                                v.selectedVariation.variableMetadata.generalOptionData[key].forEach(o => {
                                    o.costEstimateData = data.variableMetadata.generalOptionData[key].find(dOption => dOption.optionValue == o.optionValue).costEstimateData;
                                });
                            })
                        })
                    }));
                    for (let i = 0; i < vm.configureVariations.length; i++) {
                        vm.configureVariations[i].optionPerformance = results[i];
                    }
                    // Wait, then ensure 'design insight' cards are the same height...
                    setTimeout(() => {
                        const cards = Array.from(document.getElementsByClassName("el-design-card-identifier"));
                        cards.forEach(card => card.style.height = 'auto');
                        const heights = cards.map(x => x.offsetHeight);
                        const maxHeight = Math.max(...heights);
                        cards.forEach(card => card.style.height = maxHeight + 'px');
                    }, 300);
                }
                vm.showResults = true;
                vm.isBusy = false;

            } catch(e) {
                throw e;
            } finally {
                vm.isBusy = false;
            }

        }

        // Clear results
        vm.clearComparison = function() {
            vm.showResults = false;
        }

        // Variation changes
        vm.variationChanged = function (parentModel) {
            vm.clearComparison();
            vm.resetBuilding(parentModel.selectedVariation, true);
            vm.resetBuildingToDefaults(parentModel.selectedVariation, true);
            // Set whether "multi-orientate" option should be available
            let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
            vm.viewingModels.forEach(m => {
                if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                    m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                }
            });
        }

        // Copy single option across
        vm.copyOptionAcross = function (copyAcrossData) {
            // IF copying suburb
            if (copyAcrossData.field == 'suburb') {
                vm.viewingModels.forEach(building => {
                    building.selectedVariation.suburbObject = copyAcrossData.option;
                    energylabsservice.setBlockDataFromSuburb(
                        building.selectedVariation,
                        copyAcrossData.option,
                        building.selectedVariation.variableOptions,
                        () => vm.clearComparison()
                    );
                });
            }
            // ELSE do normal copy
            else {
                vm.viewingModels.forEach(building => {
                    if (building.selectedVariation.variableOptions[copyAcrossData.field]?.includes(copyAcrossData.option)) {
                        building.selectedVariation.optionData[copyAcrossData.field] = copyAcrossData.option;
                    }
                });
            }
        }

        // Copy all data to another building
        vm.copyFromTo = function(fromBuilding, toBuilding) {
            toBuilding.selectedVariation.optionData = angular.copy(fromBuilding.selectedVariation.optionData);
            toBuilding.selectedVariation.stateCode = fromBuilding.selectedVariation.stateCode;
            toBuilding.selectedVariation.suburb = fromBuilding.selectedVariation.suburb;
            toBuilding.selectedVariation.suburbObject = fromBuilding.selectedVariation.suburbObject;
            vm.clearComparison();
        }

        // Open Orientate modal
        vm.openOrientateModal = function (model) {
            var modalScope = $rootScope.$new();
            modalScope.resultsList = vm.viewingModels.filter(m => m.selectedVariation.optionData.northOffset == 'Multi-Orientation').map(m => ({
                label1: `${m.title}`,
                label2: `(${standardmodelservice.getSelectedVariationOptionNames(m.selectedVariation, m.variationOptionsList).join(" | ")})`,
                results: m.selectedVariation.orientateResults.results,
                homeModelFiles: m.selectedVariation.standardHomeModelFiles,
                view3dFloorPlans: m.selectedVariation.view3dFloorPlans,
                floorplannerLink: m.selectedVariation.floorplannerLink
            }));
            modalScope.currentIndex = vm.viewingModels.filter(m => m.selectedVariation.optionData.northOffset == 'Multi-Orientation').findIndex(m => m.uiRenderId == model.uiRenderId);
            modalScope.energyLabsSettings = vm.project.energyLabsSettings;
            var modalOptions = {
                templateUrl: 'app/ui/energy-labs/modals/orientate-chart-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialize();

    }
})();