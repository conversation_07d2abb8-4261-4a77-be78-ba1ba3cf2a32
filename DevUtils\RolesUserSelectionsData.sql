USE [thermarate];

SELECT [UserId]
      ,[user].[UserName] [__User]
      ,[RoleId]
      ,[role].[Name] [__Role]
      ,[SelectionsJson]
  FROM [dbo].[RSS_RolesUserSelectionsData] [selectionsData]
INNER JOIN [dbo].[AspNetUsers] [user] ON [selectionsData].[UserId] = [user].[Id]
INNER JOIN [dbo].[AspNetRoles] [role] ON [selectionsData].[RoleId] = [role].[Id]
ORDER BY [user].[UserName],
         [role].[Name]