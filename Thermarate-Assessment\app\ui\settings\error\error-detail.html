<form name="errorform" class="main-content-wrapper" novalidate data-ng-controller='ErrorDetailCtrl as vm' rc-submit="vm.saveError()">

    <div class="widget">
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div flex style="padding-top:20px;">
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Message</label>
                        <span class="">{{vm.error.message}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Type</label>
                        <span class="">{{vm.error.type}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>User</label>
                        <span class="">{{vm.error.user}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Time</label>
                        <span class="">{{vm.error.timeLocal| date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Application</label>
                        <span class="">{{vm.error.application}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Host</label>
                        <span class="">{{vm.error.host}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Status</label>
                        <span class="">{{vm.error.statusCode}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Source</label>
                        <span class="">{{vm.error.source}}</span>
                    </md-input-container>
                    <md-input-container class="md-block readonly-data" flex-gt-sm>
                        <label>Sequence</label>
                        <span class="">{{vm.error.sequence}}</span>
                    </md-input-container>
                    <md-card>
                        <md-card-header>
                            Raw Log Entry
                        </md-card-header>
                        <md-card-content>
                            <div class="readonly-data " style="white-space:pre; overflow-x:scroll;">
                                {{vm.error.allXml}}
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
                <div layout="row">
                    <div rd-display-created-modified ng-model="vm.error"></div>
                </div>
            </div>
        </div>
        <div data-cc-widget-button-bar
                data-is-modal="vm.isModal">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
            <div class="clearfix"></div>
        </div>

    </div>

</form>
