<!-- Cost Estimate -->
<div>

  <div class="el-section-title">
    <img class="el-section-icon"
         src="content/images/energy-labs/el-cost-icon.svg"
         alt="Icon of a price tag">
    Cost Estimate
  </div>

  <table class="table table-condensed table-bordered">
    <tbody>
    <tr ng-repeat="estimate in vm.linkOptionsToEstimates() track by $index">
      <td class="maximize-cell">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">{{vm.keyToName(estimate.category)}}</div>
        <div>{{estimate.description}}</div>
      </td>
      <td class="minimize-cell" style="text-align: right;">
        <span ng-if="estimate.rounding == null">{{(estimate.quantity * estimate.ratePerUnit * (1 + (estimate.margin / 100))) | currency : '$' : 2 }}</span>
        <span ng-if="estimate.rounding != null">{{vm.roundUpInt((estimate.quantity * estimate.ratePerUnit * (1 + (estimate.margin / 100))), estimate.rounding) | currency : '$' : vm.zeroOrTwo()}}</span>
      </td>
    </tr>

    <!-- Totals Row -->
    <tr style="height: 55px;">
      <td style="font-weight: bold; text-align: right;">
        Total
      </td>
      <td style="text-align: right;">
        <span style="margin-left: 3.5rem;">{{vm.totalCost | currency : '$' : vm.zeroOrTwo()}}</span>
      </td>
    </tr>

    </tbody>
  </table>

</div>

<style>

</style>