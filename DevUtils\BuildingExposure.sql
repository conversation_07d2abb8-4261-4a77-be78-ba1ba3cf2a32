USE [thermarate];

SELECT [buildingExposure].[BuildingExposureCode]
      ,[buildingExposure].[Description]
      ,[buildingExposure].[CreatedOn]
      ,[buildingExposure].[CreatedByName]
      ,[buildingExposure].[ModifiedOn]
      ,[buildingExposure].[ModifiedByName]
      ,[buildingExposure].[Deleted]
  FROM [dbo].[RSS_BuildingExposure] [buildingExposure]
  WHERE 1=1
    --AND [buildingExposure].[Deleted] = 0
    --AND [buildingExposure].[BuildingExposureCode] = 'ExposureCode'
  ORDER BY [buildingExposure].[Description]

-- Update a building exposure
-- UPDATE [dbo].[RSS_BuildingExposure]
-- SET [Description] = 'New Description'
-- WHERE [BuildingExposureCode] = 'ExposureCode'

-- Add a new building exposure
-- INSERT INTO [dbo].[RSS_BuildingExposure]
-- ([BuildingExposureCode], [Description], [CreatedOn], [CreatedByName], [Deleted])
-- VALUES
-- ('NewCode', 'New Building Exposure', GETDATE(), 'System', 0)
