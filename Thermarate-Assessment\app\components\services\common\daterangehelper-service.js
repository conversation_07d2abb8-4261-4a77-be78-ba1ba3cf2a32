(function () {
    'use strict';

    // Must configure the common service and set its 
    // events via the commonConfigProvider

    angular.module('common')
        .factory('daterangehelper', ['common', 'commonConfig', daterangehelper]);

    function daterangehelper(common, commonConfig) {

        var availableRanges = {};

        var service = {
            getRanges: getRanges,
            getDefaultRange: getDefaultRange,
            correctRangeDates: correctRangeDates,
        };

        return service;

        function getRanges() {
            refreshRanges();
            var returnRanges = {};
            if (arguments == null || arguments.length == 0) {
                angular.copy(availableRanges, returnRanges);
            }
            else {
                for (var i = 0; i < arguments.length; i++) {
                    var lbl = arguments[i];
                    returnRanges[lbl] = availableRanges[lbl];
                }
            }

            return returnRanges;
        }

        function getDefaultRange(defRangeLabel, persistRangeName) {
            refreshRanges();
            var defObj = {
                startDate: availableRanges[defRangeLabel][0],
                endDate: availableRanges[defRangeLabel][1],
                label: defRangeLabel
            }

            if (persistRangeName != null) {
                var persistRange = localStorage.getItem(persistRangeName);
                if (persistRange && persistRange != "undefined") {
                    var savedState = JSON.parse(localStorage.getItem(persistRangeName));
                    defObj = savedState;
                    if (availableRanges[defObj.label] != null) {
                        defObj.startDate = availableRanges[defObj.label][0];
                        defObj.endDate = availableRanges[defObj.label][1];
                    }
                    else {
                        defObj.startDate = moment(defObj.startDate);
                        defObj.endDate = moment(defObj.endDate);
                    }
                }
            }

            return defObj;
        }

        // Ensure the range dates are correct as at now.
        function correctRangeDates(rangeObj) {
            if (rangeObj == null) {
                return null;
            }
            if (availableRanges[rangeObj.label] != null) {
                refreshRanges();
                rangeObj.startDate = availableRanges[rangeObj.label][0];
                rangeObj.endDate = availableRanges[rangeObj.label][1];
            }
        }

        function refreshRanges() {
            availableRanges = {
                'Today': [moment().startOf('day'), moment().endOf('day')],
                'Yesterday': [moment().subtract('day', 1).startOf('day'), moment().subtract('day', 1).endOf('day')],
                'This Week': [moment().startOf('week'), moment().endOf('week')],
                'Last Week': [moment().subtract('day', 7).startOf('week'), moment().subtract('day', 7).endOf('week')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract('month', 1).startOf('month'), moment().subtract('month', 1).endOf('month')],
                'This Quarter': [moment().startOf('quarter'), moment().endOf('quarter')],
                'Last Quarter': [moment().subtract('month', 3).startOf('quarter'), moment().subtract('month', 3).endOf('quarter')],
                'Current Year': [moment().startOf('year'), moment().endOf('year')],
                'Current Financial Year': [(moment().month() > 5 ? moment().month('July').startOf('month') : moment().month('July').subtract('year', 1).startOf('month')), (moment().month() > 5 ? moment().add(1, 'year').month('June').endOf('month') : moment().month('June').endOf('month'))],
                'Last Financial Year': [(moment().month() > 5 ? moment().month('July').subtract('year', 1).startOf('month') : moment().month('July').subtract('year', 2).startOf('month')), (moment().month() > 5 ? moment().month('June').endOf('month') : moment().subtract('year', 1).month('June').endOf('month'))],
                'Last Year': [moment().subtract('year', 1).startOf('year'), moment().subtract('year', 1).endOf('year')],
                '12 Months': [moment().subtract('year', 1).startOf('day'), moment().endOf('day')],
                'All Time': [moment('2008-01-01'), moment().add('year',7)],
            };
        }

    }
})();