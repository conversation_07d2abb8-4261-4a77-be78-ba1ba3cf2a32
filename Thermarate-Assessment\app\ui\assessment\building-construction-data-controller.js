(function () {

    'use strict';
    angular
        .module('app')
        .component('buildingConstructionData', {
            bindings: {
                // An array of all construction categories you wish to be displayed
                // AND AFFECTED BY TEMPLATES here.
                constructionCategoryList: '<',
                generalSectionDisplay: '<',    // use 'construction' or 'opening'
                building: '<',           // The specific building (proposed or reference)
                buildingType: '<',
                comparisonBuilding: '<', // Building to compare against (usually the baseline)
                baselineOption: '<',
                option: '<',             // The compliance option
                disabled: '<',           // Disable all inputs?
                clientId: '<',           // Needed for template selection.
                isTemplate: '<',         // BOOL,
                complianceOptions: '<',  // Full list of all compliance options for copying from,
                launchGlazingCalcCallback: '&', // Fired when glazing calc button clicked lol
                sectorDetermination: '<'
            },
            templateUrl: 'app/ui/assessment/building-construction-data.html',
            controller: BuildingConstructionData,
            controllerAs: 'vm'
        });

    BuildingConstructionData.$inject = ['$rootScope', '$scope', '$mdDialog', 'coreLoop',
        'constructionservice', 'manufacturerservice', 'colourservice', 'zoneservice',
        'uuid4', 'constants', '$anchorScroll', 'common', 'buildingconstructiontemplateservice', 'columnvisibilityservice'];

    function BuildingConstructionData($rootScope, $scope, $mdDialog, coreLoop,
        constructionservice, manufacturerservice, colourservice, zoneservice,
        uuid4, constants, $anchorScroll, common, buildingconstructiontemplateservice, columnvisibilityservice) {

        var vm = this;

        // List of functions we want to expose.
        vm.copyBuildingElementsToFrom = copyBuildingElementsToFrom;
        vm.searchChanged = searchChanged;
        vm.addConstructionParentFromTemplate = addConstructionParentFromTemplate;
        vm.substituteParent = substituteParent;
        vm.duplicateParent = duplicateParent;
        vm.removeConstructionParent = removeConstructionParent;
        vm.clearConstructionItem = clearConstructionItem;
        vm.copyElement = copyElement;
        vm.bulkCopyElements = bulkCopyElements;
        vm.hasConstructionItems = hasConstructionItems;
        vm.switchToAddMode = switchToAddMode;
        vm.filteredConstructionTemplateList = filteredConstructionTemplateList;
        vm.openingsOverrideModal = openingsOverrideModal;
        vm.openShadingModal = openShadingModal;
        vm.launchOpeningsRenameModal = launchOpeningsRenameModal;
        vm.isOpeningRenameApplicable = isOpeningRenameApplicable;
        vm.openSolarAbsorptanceOverrideModal = openSolarAbsorptanceOverrideModal;
        vm.launchParentActionModal = launchParentActionModal;
        vm.launchElementActionModal = launchElementActionModal;

        $scope.building = this.building; // this allows 'building' to be accessed directly in the HTML template.
        $scope.option = this.option;

        vm.buildingType = this.buildingType;
        vm.comparisonBuilding = this.comparisonBuilding;

        vm.buildingConstructionTemplates = [];
        vm.openingStyleList = [];
        vm.frameMaterialList = [];
        vm.airCavityList = [];

        let intervalTimer = null;

        vm.sortBy = function(column, parent, transformCallback = null) {

            if(parent.sortInfo == null)
                parent.sortInfo = new SortInfo();

            common.setSort(column, parent.sortInfo, transformCallback);
            parent.sortedElements = common.applySort(parent.elements, parent.sortInfo);
        }

        vm.sortHorizontalShading = function(parent) {

            if(parent.sortInfo == null)
                parent.sortInfo = new SortInfo();

            common.setSort('horizontalShading', parent.sortInfo);

            let sortedData = [...parent.elements];

            if(parent.sortInfo.column === null) {
                parent.sortedElements = null;
                return;
            }

            if (parent.sortInfo.column != null && parent.sortInfo.direction != null) {

                if (parent.sortInfo.direction === "ASC")
                    sortedData.sort((a, b) => {
                        const aS = constructionservice.greaterShading(a.horizontalProjection1?.shading, a.horizontalProjection2?.shading);
                        const bS = constructionservice.greaterShading(b.horizontalProjection1?.shading, b.horizontalProjection2?.shading);

                        let ac = constructionservice.shadingOutcomeMap[aS];
                        let bc = constructionservice.shadingOutcomeMap[bS];
                        return ac - bc
                    });
                else if (parent.sortInfo.direction === "DESC")
                    sortedData.sort((a, b) => {
                        const aS = constructionservice.greaterShading(a.horizontalProjection1?.shading, a.horizontalProjection2?.shading);
                        const bS = constructionservice.greaterShading(b.horizontalProjection1?.shading, b.horizontalProjection2?.shading);

                        let ac = constructionservice.shadingOutcomeMap[aS];
                        let bc = constructionservice.shadingOutcomeMap[bS];
                        return bc - ac
                    });

                parent.sortedElements = sortedData;
                return;

            } else {

                // Sort by nothing then - null indicates caller should use 'natural' order.
                parent.sortedElements = null;
                return;
            }
        }

        vm.sortVerticalShading = function(parent) {

            if(parent.sortInfo == null)
                parent.sortInfo = new SortInfo();

            common.setSort('verticalShading', parent.sortInfo);

            let sortedData = [...parent.elements];

            if(parent.sortInfo.column === null) {
                parent.sortedElements = null;
                return;
            }

            if (parent.sortInfo.column != null && parent.sortInfo.direction != null) {

                if (parent.sortInfo.direction === "ASC")
                    sortedData.sort((a, b) => {
                        const aS = constructionservice.greaterShading(a.verticalScreen1?.shading, a.verticalScreen2?.shading, a.verticalScreen3?.shading);
                        const bS = constructionservice.greaterShading(b.verticalScreen1?.shading, b.verticalScreen2?.shading, b.verticalScreen3?.shading);

                        let ac = constructionservice.shadingOutcomeMap[aS];
                        let bc = constructionservice.shadingOutcomeMap[bS];
                        return ac - bc
                    });
                else if (parent.sortInfo.direction === "DESC")
                    sortedData.sort((a, b) => {
                        const aS = constructionservice.greaterShading(a.verticalScreen1?.shading, a.verticalScreen2?.shading, a.verticalScreen3?.shading);
                        const bS = constructionservice.greaterShading(b.verticalScreen1?.shading, b.verticalScreen2?.shading, b.verticalScreen3?.shading);

                        let ac = constructionservice.shadingOutcomeMap[aS];
                        let bc = constructionservice.shadingOutcomeMap[bS];
                        return bc - ac
                    });

                parent.sortedElements = sortedData;
                return;

            } else {

                // Sort by nothing then - null indicates caller should use 'natural' order.
                parent.sortedElements = null;
                return;
            }
        }

        vm.sortElements = function(parent) {

            if(parent.sortInfo == null)
                parent.sortInfo = new SortInfo();

            parent.sortedElements = common.applySort(parent.elements, parent.sortInfo);
        }

        vm.elementsInOrder = function(parent) {

            if(parent.sortedElements == null)
                return parent.elements;

            return parent.sortedElements;

        }

        function initialize() {

            // Start timer that computes differences where applicable.
            if (vm.buildingType == "reference")
                vm.differenceTimer = setInterval(() => computeDifferences($scope.option.reference, $scope.option.proposed), 1000);

            buildingconstructiontemplateservice.getAll(vm.generalSectionDisplay).then(function (data) {
                vm.buildingConstructionTemplates = data;
            });

            constructionservice.getOpeningStyleList()
                .then(data => { vm.openingStyleList = data; });

            constructionservice.getFrameMaterialList()
                .then(data => { vm.frameMaterialList = data; });

            constructionservice.getAirCavityList()
                .then(data => { vm.airCavityList = data; });

            // Brrrrrrr.
            intervalTimer = setInterval(
                () => {
                    // Brrrrrrrr.
                    // TODO: I actually think this should be pulled out of here, and into the assessment page itself.
                    // That way it just loops over ALL options/compliance data etc in one fell swoop.
                    coreLoop.computeAreas(vm.building);
                    coreLoop.computeOpenability(vm.building);
                    coreLoop.computeLowestLivingAreaFloorType(vm.building);
                    coreLoop.computeFullMasonryExteriorAndInteriorWalls(vm.building);
                    coreLoop.clampAzimuths(vm.building);
                    coreLoop.computeDynamicSectors(vm.building, vm.sectorDetermination);
                    sortParentsByArea();
                },
                1000);
        }

        initialize();

        $scope.$on("$destroy", function () {
            // Destroy our difference timer.
            if (vm.differenceTimer) {
                clearInterval(vm.differenceTimer);
            }

            if (intervalTimer)
                clearInterval(intervalTimer);
        });

        function filteredConstructionTemplateList(category) {
            return vm.constructionTemplateList.filter(x => x.constructionCategoryCode == category.constructionCategoryCode);
        }

        function switchToAddMode(category) {
            category.inAddMode = true;

            // Set a small delay and then focus on our template input
            setTimeout(() => {
                let ele = document.getElementById("Template" + category.constructionCategoryCode);
                ele = ele.childNodes[1]; // Work our way through the DOM
                ele = ele.childNodes[2]; // until we get to the input
                //ele = ele.childNodes[3]; // we actually want to focus on...
                ele.focus();

            }, 50);
        }

        vm.disabledEx = function () {
            return vm.disabled;
        }

        vm.symbol = function(symbolName) {
            return common.symbol(symbolName);
        }

        // If we're said glazing will be manually specified using a glazing calculator,
        // then we ignore empty lists for glazing categories.
        vm.externalDataIsSpecified = function(category, building) {

            if (building.categoriesWithExternalData == null)
                return false;

            return building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] === true;
        }

        /** Copies the given data to the given option.building from the given building */
        function copyBuildingElementsToFrom(toOption, toBuilding, fromBuilding, fromOption) {

            // USE TIMEOUTS TO AVOID WEIRD UI DISPLAY BUGS.
            setTimeout(async () => {

                // Temporarily assign a dummy 'template type'.
                fromBuilding.templateType = vm.generalSectionDisplay;
                await constructionservice.applyTemplate(null, toBuilding, fromBuilding, null);
                delete fromBuilding.templateType;

            }, 100);

        }

        vm.optionsNotThisOrBaseline = function (option) {
            return vm.complianceOptions
                ?.filter(x => x.optionIndex != 0 && x.optionIndex != option.optionIndex);
        }

        /**
         * We only show the 'copy baseline' button for options if we are currently viewing
         * the 'proposed' building OR we are viewing the 'reference building' AND their is
         * a reference building available to copy from in the baseline option.
         *
         * We determine if a reference building is 'available' based upon its compliance method.
         */
        vm.showCopyBaselineForOption = function (option) {
            return vm.buildingType === 'proposed' ||
                   (vm.buildingType === 'reference' && hasReferenceBuilding(option.complianceMethod.complianceMethodCode));
        }

        vm.enableCopyToFrom = function (copyToOption, copyFromOption) {
            return copyToOption.buildingZonesTemplateId === copyFromOption.assessmentComplianceBuildingId ||
                   copyToOption.buildingZonesTemplateId === copyFromOption.buildingZonesTemplateId;
        }

        function hasReferenceBuilding(complianceMethodCode) {
            return complianceMethodCode === "CMPerfSolutionDTS" ||
                   complianceMethodCode === "CMPerfSolution";
        }

        function clearCategory(category, building) {

            let list = constructionservice.getAllConstructionsForCategoryType(category, building);

            if(list == null || list.length === 0)
                return;

            for (let i = list.length - 1; i >= 0; i--) {
                if (list[i].category.constructionCategoryCode === category.constructionCategoryCode) {
                    list.splice(i, 1);
                }
            }
        }
        vm.clearCategory = clearCategory;

        //save searchtext so it can be set to the otehr field if user selects other from the dropdown.
        function searchChanged(constructionItem, searchText, variable) {
            constructionItem[variable + 'PreviousSearchText'] = searchText;
        }

        /**
         * Adds a new construction item PARENT to the list (either a surface or opening) based on the given template.
         * Existing constructions with the same derived template can still exist when a new one is added (as it may
         * for instance be a different color).
         *
         * @param {any} category
         * @param {any} building
         * @param {any} templateView
         */
        async function addConstructionParentFromTemplate(category, building, templateView) {

            category.inAddMode = false;

            // Ok so we got the generic item 'view', now retreive the full template.
            let template = await constructionservice.getConstruction(templateView.constructionId, templateView.type);

            // Merge our item with the template data but make sure we
            // retain our NEW id and also a ref to the template/'derived from' ID.
            let newItem = angular.copy(template);
            newItem.derivedFromConstructionId = template.constructionId;
            newItem.constructionId = uuid4.generate();
            newItem.source = "manual";

            // Insert first first element which is a clone of itself its parent...
            newItem.elements = [{ ...newItem, constructionId: uuid4.generate() }];
            newItem.isExpanded = true;

            if(category.constructionCategoryCode === "SubfloorWall")
                newItem.showInReport = false;

            const list = constructionservice.getAllConstructionsForCategoryType(category, building);
            list.push(newItem);

            setElementCodesForCategory(category.constructionCategoryCode);

        }

        vm.deleteElement = function (parent, element) {

            parent.elements = parent.elements
                .filter(x => x.constructionId !== element.constructionId);

            setElementCodesForCategory(parent.category.constructionCategoryCode);

        }

        vm.bulkDeleteElements = function (parent) {
            parent.elements = parent.elements.filter(x => x.checkboxSelected !== true);
            setElementCodesForCategory(parent.category.constructionCategoryCode);
        }

        function copyElement(parent, item) {

            const index = parent.elements.indexOf(item);
            let clone = {
                ...parent.elements[index],
            };
            clone.constructionId = uuid4.generate();
            clone.source = "manual";

            // Insert below cloned item.
            parent.elements.splice(index + 1, 0, clone);

            setElementCodesForCategory(parent.category.constructionCategoryCode);

        }

        function bulkCopyElements(parent) {

            const elementsToCopy = parent.elements.filter(x => x.checkboxSelected === true);

            elementsToCopy.forEach(item => copyElement(parent, item));

            parent.elements.forEach(x => x.checkboxSelected = false);

        }

        /**
         * Sets all element codes in order of UI. Element codes are NOT preserved when adding new elements, removing,
         * or re-arranging parents. They should be seen as ephemeral and only used as a reference in PDF generation.
         *
         * @param  {string} categoryCode The category code to run this process on.
         */
        function setElementCodesForCategory(categoryCode) {

            const surfaces = vm.surfacesInCategory(categoryCode);
            const openings = vm.openingsInCategory(categoryCode);
            const groups = [...surfaces, ...openings]; // Only one will ever have data.

            let elem = 1;
            for (let x = 0; x < groups.length; x++) {

                const elements = groups[x].elements;
                const code = vm.constructionCategoryList
                    .filter(x => x.constructionCategoryCode === categoryCode)[0].elementCode;

                for (let i = 0; i < elements.length; i++) {
                    elements[i].elementNumber = getElementCode(code, elem)
                    elem++;
                }
            }
        }
        vm.setElementCodesForCategory = setElementCodesForCategory;

        function setAllElementCodes() {
            vm.constructionCategoryList.forEach(x => setElementCodesForCategory(x.constructionCategoryCode));
        }

        function getElementCode(code, number) {
            return code + '-' + common.addLeadingZero(number, 3);
        }

        async function substituteParent(parent, template, category, building) {

            if (parent == null || template == null || template === "")
                return;

            parent.isInChangeMode = false;

            // Ok so we got the generic item, now retrieve the proper one..
            let t = await constructionservice.getConstruction(template.constructionId, template.type);

            // Merge our item with the template data but make sure we
            // retain our NEW id and also a ref to the template/'derived from' ID.
            let allConstructionsInCategory = constructionservice.getAllConstructionsForCategoryType(category, building);

            let oldGuid = parent.constructionId;
            const backupElements = parent.elements;
            let newParent = common.nullAwareMerge(parent, t);

            newParent.derivedFromConstructionId = t.constructionId;
            newParent.constructionId = oldGuid; // We want to keep our original ID
            newParent.elements = backupElements;
            newParent.isExpanded = parent.isExpanded;
            newParent.overrideDisplayDescription = null;

            if(category.constructionCategoryCode === "SubfloorWall")
                newParent.showInReport = false;

            // nullAwareMerge replaced our reference, so we need to set what's in our
            // master list to the new values.
            let match = allConstructionsInCategory.filter(x => x.constructionId === newParent.constructionId);

            if (match == null || match.length === 0)
                return;

            let index = allConstructionsInCategory.indexOf(match[0]);
            allConstructionsInCategory[index] = newParent;

            for (let i = 0; i < newParent.elements.length; i++) {
                // Not much has to be updated on the child.
                const child = newParent.elements[i];
                child.derivedFromConstructionId = t.constructionId;
            }

            forceBlurParentInputsWithDecimals(newParent);
        }

        function duplicateParent(parent, building) {

            const clone = angular.copy(parent);
            clone.constructionId = uuid4.generate();

            if(parent.category.type === "surface" || parent.category.type === "permanentopening") {
                const originalIndex = building.surfaces.indexOf(parent);
                building.surfaces.splice(originalIndex + 1, 0, clone);
            }
            else {
                const originalIndex = building.openings.indexOf(parent);
                building.openings.splice(originalIndex + 1, 0, clone);
            }

            setElementCodesForCategory(parent.category.constructionCategoryCode);
        }

        /** Removes the parent and all elements */
        function removeConstructionParent(parent, building) {

            let list;
            if (parent.category.type === "surface" || parent.category.type === "permanentopening")
                list = building.surfaces;
            else
                list = building.openings;

            for (let i = list.length - 1; i >= 0; i--) {
                if (list[i].constructionId === parent.constructionId) {
                    list.splice(i, 1);
                }
            }

            setElementCodesForCategory(parent.category.constructionCategoryCode);
        }

        function clearConstructionItem(item, building) {

            let list = constructionservice.getAllConstructionsForCategoryType(item.category, building);

            var index = list.indexOf(item);
            list[index] = {
                ...list[index],
                units: null,
                comments: null,
                zoneId: null,
            };
        }

        /** Returns true if there are any construction items present of the given code. */
        function hasConstructionItems(category, building) {
            return constructionservice.checkBuildingContainsConstructionOfCategory(category, building);
        }

        /** Simply returns the descriptive name of the floor this zone is on/above/below */
        vm.floorOfZone = function(zoneLinkId) {

            const zone = vm.building?.zones?.find(x => x.linkId === zoneLinkId);

            if(zone == null)
                return "";

            const storey = zone.storey ?? zone.storeyBelow ?? zone.storeyAbove;
            return vm.building.storeys[storey].name;
        }

        vm.elementZoneChanged = function(element, zone) {
            element.storey = zone.storey;
        }

        vm.updateOpeningStyle =  function(parent, style) {
            parent.openability = style.defaultOpenability;
            parent.openingStyle = style;
            parent.nccOpeningStyle = style.nccOpeningStyle;
        }

        // WIP: Not used for now!
        /** Check for changes against the given building and the baseline if applicable */
        function computeDifferences(building, comparator) {

            // TODO: The functionality of this has to be updated. I am not sure now if we should
            // only be comparing against the 'derivedFromConstrucitonId' prop and then compare
            // units/comments or...? Or do we also look for items that are not present in the base building?

            //vm.differences = [];

            //// Check all construction items.
            //building.surfaces.forEach(item => {

            //    let comparison = comparator.constructionItems.find(x => x.assessmentConstructionItemId == item.assessmentConstructionItemId);

            //    if (comparison) {

            //        // Check and store if the constrution items still match.
            //        let matchReflectiveNA = (comparison.insulation == null || !comparison.airSpace);
            //        let itemReflectiveNA = (item.insulation == null || !item.airSpace);

            //        vm.differences[item.assessmentConstructionItemId] = {
            //            construction: comparison.construction?.constructionCode != item.construction?.constructionCode,
            //            insulation: comparison.insulation?.insulationCode != item.insulation?.insulationCode,
            //            airSpace: comparison.airSpace != item.airSpace,
            //            reflective: comparison.isReflective != item.isReflective || matchReflectiveNA != itemReflectiveNA,
            //            colour: comparison.colour?.colourCode != item.colour?.colourCode,
            //            // Checks to make sure the lengths are the same and checks match to make sure that for every
            //            // ventilation construction in our comparison, we a matching ventilation in our item...
            //            ventilation: (comparison.assessmentConstructionItemRoofVentilations?.length != item.assessmentConstructionItemRoofVentilations?.length) ||
            //                !comparison.assessmentConstructionItemRoofVentilations
            //                    ?.every(sa => item.assessmentConstructionItemRoofVentilations
            //                        ?.some(sb => sa.roofVentilationCode == sb.roofVentilationCode)),
            //            area: comparison.area != item.area,
            //            details: comparison.details != item.details,
            //            externalFinish: comparison.externalFinish?.externalFinishCode != item.externalFinish?.externalFinishCode,
            //            material: comparison.material?.materialCode != item.material?.materialCode,
            //            subFloor: comparison.subFloor?.subFloorCode != item.subFloor?.subFloorCode,
            //            ceilingConstruction: comparison.ceilingConstruction?.ceilingConstructionCode != item.ceilingConstruction?.ceilingConstructionCode,
            //        }

            //    } else {
            //        // If there is no corresponding row, it means the entire row is new, so we can mark it as such.
            //        vm.differences[item.assessmentConstructionItemId] = {
            //            construction: true,
            //            insulation: true,
            //            airSpace: true,
            //            reflective: true,
            //            colour: true,
            //            ventilation: true,
            //            area: true,
            //            details: true,
            //            ventilation: true,
            //            material: true,
            //            subFloor: true,
            //            ceilingConstruction: true,
            //        }
            //    }

            //});

            //// Compare external glazings.
            //vm.glazingDifferences = [];
            //building.externalGlazing.forEach(glazing => {

            //    let comparison = comparator.externalGlazing.find(x => x.assessmentExternalGlazingId == glazing.assessmentExternalGlazingId);

            //    if (comparison) {

            //        vm.glazingDifferences[glazing.assessmentExternalGlazingId] = {

            //            manufacturer: comparison.manufacturer?.manufacturerId != glazing.manufacturer?.manufacturerId,
            //            description: comparison.description != glazing.description,
            //            window: comparison.windowId != glazing.windowId,
            //            glassType: comparison.glassType != glazing.glassType,
            //            frame: comparison.frame != glazing.frame,
            //            uValue: comparison.uValue != glazing.uValue,
            //            shgc: comparison.shgcValue != glazing.shgcValue,
            //            area: comparison.area != glazing.area,
            //            details: comparison.details != glazing.details
            //        }

            //    } else {
            //        // If there is no corresponding row, it means the entire row is new, so we can mark it as such.
            //        vm.glazingDifferences[glazing.assessmentExternalGlazingId] = {
            //            manufacturer: true,
            //            description: true,
            //            window: true,
            //            glassType: true,
            //            frame: true,
            //            uValue: true,
            //            shgc: true,
            //            area: true,
            //            details: true
            //        }
            //    }

            //});

        }
        vm.computeDifferences = computeDifferences;

        vm.toTitleCase = function(str) {
            return common.toSplitTitleCase(str);
        }

        vm.constructionTemplateList = [];
        let constructionTemplatePromise = constructionservice.getAll()
            .then(data => {
                vm.constructionTemplateList = data;
            });

        vm.colourList = null;
        colourservice.getAll()
            .then(data => { vm.colourList = data; });

        vm.sectionExpansions = {}; // Used to keep track of which sections are expanded.
        vm.expand = function (section) {
            vm.sectionExpansions[section] = !vm.sectionExpansions[section];
        }

        /**
         * @param {string} categoryCode
         * @returns {T[]}
         */
        vm.surfacesInCategory = function(categoryCode) {
            return vm.building.surfaces.filter(x => x.category.constructionCategoryCode === categoryCode);
        }

        /**
         * @param {string} categoryCode
         * @returns {T[]}
         */
        vm.openingsInCategory = function(categoryCode) {
            return vm.building.openings.filter(x => x.category?.constructionCategoryCode === categoryCode);
        }

        vm.openingListForCategory = function (category) {

            let list = [];
            let exclusionFilter = null;

            // Exclude windows if this is a door. Paradoxically, doors can be selected if this is a window.
            if (category?.title?.toLowerCase().includes("door"))
                exclusionFilter = "window";

            if (category.constructionCategoryCode == 'Skylight' ||
                category.constructionCategoryCode == "RoofWindow") {
                list = vm.openingStyleList?.filter(x => x.type == 'roof' || x.type == 'all')
            } else {
                list = vm.openingStyleList?.filter(x => x.type == 'wall' || x.type == 'all')
            }

            // Instead of filter by INCLUSION, we filter by EXCLUSION so that, for instance,
            // things like 'Fixed' and 'Other' are still included
            if (exclusionFilter != null) {
                exclusionFilter = exclusionFilter.toLowerCase()
                list = list?.filter(x => !x.openingStyleCode.toLowerCase().includes(exclusionFilter));
            }

            return list;
        }

        vm.availableParentZonesForCategory = function(category) {

            const code = category.constructionCategoryCode;
            const zones = vm.building.zones;

            if(code === "Roof")
                return [...zoneservice.interiorZones(zones), ...zoneservice.roofSpaceZones(zones)];
            else if(code === "SubfloorWall")
                return zoneservice.subfloorSpaceZones(zones);
            else
                return zoneservice.interiorZones(zones);

        }

        vm.availableAdjacentZonesForCategory = function(category) {

            const code = category.constructionCategoryCode;
            const zones = vm.building.zones;

            if(category.type === "surface") {
                if(code === "CeilingRoofAbove" || code === "InteriorWallAdjacentToRoofSpace")
                    return zoneservice.roofSpaceZones(zones);
                else if(code === "InteriorWallAdjacentToSubfloorSpace" || code === "ExteriorFloor")
                    return zoneservice.subfloorSpaceZones(zones);
                else if(code === "IntermediateFloor" || code === "InteriorWall" || code === "InteriorDoor")
                    return zoneservice.interiorZones(zones);
                else if(code === "GroundFloor")
                    return zoneservice.groundSurfaceZones(zones);
                else
                    return [];
            } else {
                return zoneservice.interiorZones(zones);
            }

        }

        /**
         * Sorts all Parent rows AND their elements by area.
         */
        function sortParentsByArea() {

            vm.building.surfaces.forEach(parent => {
                parent.elements?.sort((a, b) => b.netArea - a.netArea);
            });

            vm.building.openings.forEach(parent => {
                parent.elements?.sort((a, b) => b.grossArea - a.grossArea);
            });

            vm.building.surfaces.sort((a, b) => b.netArea - a.netArea);
            vm.building.openings.sort((a, b) => b.netArea - a.netArea);

            setAllElementCodes();
        }

        /**
         * Used by the 'sortBy' method when the sort column is the parent zone. Since the parent zone property is just a
         * GUID (useless for sorting), we use this callback to return the actual description for the sort.
         *
         * @param value The value to transform
         * @return {string} Zone Description
         */
        vm.transfromZoneIdToDescription = (value) => vm.building.zones
            ?.find(x => x.linkId === value)?.zoneDescription;

        vm.transformZoneNumberToDescription = (value) => vm.building.zones
            ?.find(x => x.zoneNumber === value)?.zoneDescription;

        /** Fired on blur for manually-added elements */
        vm.elementDimensionChanged = function(element) {

            if(element.source === "manual") {

                setTimeout(() => {

                    // Don't alter a grossArea that has been overridden.
                    if ((element.grossAreaIsManuallyOverridden === false || element.grossAreaIsManuallyOverridden === undefined) && element.width != null && element.height != null) {
                        element.grossArea = element.netArea = element.width * element.height;
                        forceBlurNetArea(element);
                    }

                }, 50);
            }
        }

        /** Ensure net area is not larger than gross area */
        vm.elementNetAreaChanged = function(element) {

            if(element.source === "manual") {

                const oldGrossArea = element.grossArea;
                setTimeout(() => {

                    if(element.netArea > oldGrossArea) {
                        element.netArea = oldGrossArea;
                        forceBlurNetArea(element);
                    }

                }, 50);
            }
        }

        /** Reset net area to equal gross area */
        vm.elementGrossAreaChanged = function(element) {

            if(element.source === "manual") {

                setTimeout(() => {

                    element.netArea = element.grossArea;
                    forceBlurNetArea(element);

                }, 50);
            }
        }

        function forceBlurNetArea(element) {
            common.forceBlurInputWithId(element.constructionId + "NetAreaInput");
        }

        function forceBlurParentInputsWithDecimals(parent) {
            common.forceBlurInputWithId(parent.constructionId + "SHGCInput");
            common.forceBlurInputWithId(parent.constructionId + "UValueInput");
            common.forceBlurInputWithId(parent.constructionId + "FrameSolarInput");
        }

        vm.subfloorZones = () =>
            vm.building.zones?.filter(x => x.zoneActivity?.zoneActivityCode === "ZASubfloorSpace");

        /**
         * Opens the override modal for non calculated area fields and openability fields on the Openings tab
         *
         * @param {ConstructionTemplateDto} item
         * Flag to tell modal what field to display and override
         * @param {string} property
        **/
        function openingsOverrideModal(item, property) {
            var modalScope = $rootScope.$new();
            modalScope.item = item;
            modalScope.property = property;
            modalScope.disabled = vm.disabledEx();
            var modalOptions = {
                templateUrl: 'app/ui/assessment/openings-override-modal/openings-override-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(
                // Submitted
                function (result) {
                    // Update item here
                    item[property + 'OriginalValue'] = result.originalValue;
                    item[property + 'IsManuallyOverridden'] = result.isOverridden;
                    item[property] = Number(result.overrideValue);
                },
                // Cancelled
                function () {}
            );
        }

        /**
         * Opens the shading modal for to display more fields (Horizontal, Vertical, Left Wing, Right Wing)
         *
         * @param {ConstructionTemplateDto} item
         * Flag to tell modal what shading field to display info for
         * @param {string} property
        **/
        function openShadingModal(item, property) {

            let modalScope = $rootScope.$new();

            // Take a copy of the construction element so we don't update the original
            // until saving dialog.
            modalScope.disabled = vm.disabledEx();
            modalScope.item = angular.copy(item);
            modalScope.property = property;
            let modalOptions = {
                templateUrl: 'app/ui/assessment/shading-info-modal/shading-info-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(
                // Submitted
                function (result) {

                    if(property === "horizontalShading") {
                        item.horizontalProjection1 = common.nullAwareMerge(item.horizontalProjection1, result.horizontalProjection1);
                        item.horizontalProjection2 = common.nullAwareMerge(item.horizontalProjection2, result.horizontalProjection2);
                    }

                    if(property === "verticalShading") {
                        item.verticalScreen1 = common.nullAwareMerge(item.verticalScreen1, result.verticalScreen1);
                        item.verticalScreen2 = common.nullAwareMerge(item.verticalScreen2, result.verticalScreen2);
                        item.verticalScreen3 = common.nullAwareMerge(item.verticalScreen3, result.verticalScreen3);
                    }

                    if(property === "leftWingWall")
                        item.leftWingWall = common.nullAwareMerge(item.leftWingWall, result.leftWingWall);

                    if(property === "rightWingWall")
                        item.rightWingWall = common.nullAwareMerge(item.rightWingWall, result.rightWingWall);

                },
                // Cancelled
                function () {}
            );
        }

        vm.greaterShading = constructionservice.greaterShading;

        vm.elementWingShading = function(wing) {

             if(wing == null)
                 return "";

             return wing.shading;
        }

        vm.wallOutcomeTransform = function(a) {
            return constructionservice.shadingOutcomeMap[a];
        }

        function openSolarAbsorptanceOverrideModal(item, solarAbsorptanceProperty, colourProperty) {
            let modalScope = $rootScope.$new();
            modalScope.disabled = vm.disabledEx();
            modalScope.item = item;
            modalScope.solarAbsorptanceProperty = solarAbsorptanceProperty;
            modalScope.colourProperty = colourProperty;
            modalScope.colourList = vm.colourList;
            let modalOptions = {
                templateUrl: 'app/ui/assessment/colour-override-modal/colour-override-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(
                // Submitted
                function (result) {

                    //item[solarAbsorptanceProperty + 'OriginalValue'] = result[solarAbsorptanceProperty + 'OriginalValue'];
                    //item[solarAbsorptanceProperty + 'IsManuallyOverridden'] = result[solarAbsorptanceProperty + 'IsManuallyOverridden']
                    item[solarAbsorptanceProperty] = result[solarAbsorptanceProperty + "OverrideValue"]

                    //item[colourProperty + 'OriginalValue'] = result[colourProperty + 'OriginalValue'];
                    //item[colourProperty + 'IsManuallyOverridden'] = result[colourProperty + 'IsManuallyOverridden']
                    item[colourProperty] = result[colourProperty + "OverrideValue"];

                    // Apply final values to sub elements.
                    item.elements?.forEach(e => {
                        e[solarAbsorptanceProperty] = item[solarAbsorptanceProperty];
                        e[colourProperty] = item[colourProperty];
                    });

                },
                // Cancelled
                function () {}
            );
        }

        vm.openVisibilityModal = function(parent) {
            const modalSuccessCallback = (response) => parent.hiddenTableColumns = response;
            columnvisibilityservice.openVisibilityModal(parent, modalSuccessCallback);
        }

        vm.isTableColumnVisible = function(columnProperty, parent) {
            return columnvisibilityservice.isTableColumnVisible(columnProperty, parent);
        }

        // Find start position. (count columns before shaing section)
        vm.getShadowHeaderStartPosition = function(parent) {

            const categoryCode = parent.category.constructionCategoryCode;
            let startPosition = 0;

            // Config (list of columns that appear before the shading section per costruction category)
            let columnPropertiesBeforeShading = [];

            // 3 Different construction categories have the shading header
            if (categoryCode === 'ExteriorWall')
                columnPropertiesBeforeShading = ['checkbox', 'elementNumber', 'parentZoneId', 'storey', 'azimuth', 'sector', 'height', 'width'];
            else if (categoryCode === 'ExteriorGlazing' || categoryCode === 'ExteriorDoor')
                columnPropertiesBeforeShading = ['checkbox', 'elementNumber', 'parentZoneId', 'storey', 'azimuth', 'sector', 'height', 'width', 'openability'];

            if (columnPropertiesBeforeShading.length > 0 && parent.hiddenTableColumns) {
                columnPropertiesBeforeShading.forEach(columnProperty => {
                    // False or undefined means hidden
                    if (parent.hiddenTableColumns[columnProperty] !== true)
                        startPosition++;
                });
            }
            else {
                // If no hiddenTableColumns, assume all are visible.
                startPosition = columnPropertiesBeforeShading.length;
            }

            return startPosition;
        }

        // Find Shading header's width. (ie. Count how many of the shadow columns are hidden)
        vm.getShadowHeaderWidth = function(parent) {

            let shadowColumnCount = 0;

            // Shading properties are the same for all construction categories
            const shadingColumnProperties = ['horizontalShading', 'verticalShading', 'leftWingWall.shading', 'rightWingWall.shading'];

            if (shadingColumnProperties.length > 0 && parent.hiddenTableColumns) {
                shadingColumnProperties.forEach(shadingProperty => {
                    // False or undefined means hidden
                    if (parent.hiddenTableColumns[shadingProperty] !== true)
                        shadowColumnCount++;
                });
            }
            else {
                // If no hiddenTableColumns, assume all are visible.
                shadowColumnCount = shadingColumnProperties.length;
            }

            return shadowColumnCount;
        }

        vm.expandSection = function(constructionCategoryCode) {

            vm.sectionExpansions[constructionCategoryCode] = !vm.sectionExpansions[constructionCategoryCode]

            const s = vm.surfacesInCategory(constructionCategoryCode);
            const o = vm.openingsInCategory(constructionCategoryCode);

            const a = [...s, ...o];

            a.forEach(parent => parent.isExpanded = vm.sectionExpansions[constructionCategoryCode]);

        }

        // AngularJS Filter
        // must be $scope and not vm
        // ---
        // Some Subfloor Ventilation types need to be filtered beyond their type.
        // Exterior Floor (Connected to Ground) - Subfloor Ventilation
        //     Connected to Ground
        // Exterior Floor (Suspended) - Subfloor Ventilation
        //     Enclosed
        //     Enclosed Disconnected
        //     Open
        //     Very Open
        // Exterior Floor (Elevated) - Subfloor Ventilation
        //     Elevated
        $scope.subfloorVentilationFilter = function(category) {
            return function(item) {

                let result = false;

                switch (category.constructionCategoryCode) {
                    case 'GroundFloor':
                        result = item.airCavityCode === 'SubfloorConnectedToGround'
                        break;
                    case 'ExteriorFloor':
                        result = item.type === category.ventilationType &&
                            item.airCavityCode != 'SubfloorConnectedToGround' &&
                            item.airCavityCode != 'SubfloorElevated';
                        break;
                    case 'ExteriorFloorElevated':
                        result = item.airCavityCode === 'SubfloorElevated'
                        break;
                    default:
                        result = item.type === category.ventilationType;
                        break;
                }

                return result;
            }
        }

        /** Opens popup modal to rename or substitute building construction. **/
        function launchParentActionModal(action, parent, building) {

            var modalScope = $rootScope.$new();
            modalScope.action = action;
            modalScope.type = "construction"; // Base type... "services" for services modal
            modalScope.generalSectionDisplay = vm.generalSectionDisplay; // Pass the section display to distinguish Construction vs Openings
            modalScope.parent = parent;
            modalScope.building = building;
            modalScope.option = vm.option;
            modalScope.disabled = vm.disabledEx();

            var modalOptions = {
                templateUrl: 'app/ui/assessment/construction-action-modal/construction-action-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);

            modalScope.modalInstance.then(
                // Submitted
                async function (data) {

                    console.log("Returned: ", data);

                    if(action === 'rename') {
                        parent.overrideDisplayDescription = data.overrideDisplayDescription;
                        parent.overrideInsulationDescription = data.overrideInsulationDescription;
                    }

                    if(action === 'substitute') {
                        await substituteParent(parent, data, parent.category, building);
                    }

                },
                // Cancelled
                function () {}
            );
        }

        /** Opens popup modal to rename openings with specific fields. **/
        function launchOpeningsRenameModal(parent, building) {

            var modalScope = $rootScope.$new();
            modalScope.parent = parent;
            modalScope.building = building;
            modalScope.option = vm.option;
            modalScope.disabled = vm.disabledEx();

            var modalOptions = {
                templateUrl: 'app/ui/assessment/openings-rename-modal/openings-rename-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);

            modalScope.modalInstance.then(
                // Submitted
                async function (data) {

                    console.log("Openings Rename Returned: ", data);

                    // Update the parent with the new override values
                    parent.overrideDisplayDescription = data.overrideDisplayDescription;
                    parent.overrideOpeningStyle = data.overrideOpeningStyle;

                    // Update glass override in the glassData object
                    if (!parent.glassData) {
                        parent.glassData = {};
                    }
                    parent.glassData.overrideDescription = data.overrideGlass;

                    // Update performance overrides in the performance object
                    if (!parent.performance) {
                        parent.performance = {};
                    }
                    parent.performance.overrideUValue = data.overrideUValue;
                    parent.performance.overrideSHGC = data.overrideSHGC;

                    // Copy override values to all elements
                    if (parent.elements && parent.elements.length > 0) {
                        parent.elements.forEach(element => {
                            // Update element's override values to match parent
                            element.overrideDisplayDescription = data.overrideDisplayDescription;
                            element.overrideOpeningStyle = data.overrideOpeningStyle;

                            // Update glass override in the element's glassData object
                            if (!element.glassData) {
                                element.glassData = {};
                            }
                            element.glassData.overrideDescription = data.overrideGlass;

                            // Update performance overrides in the element's performance object
                            if (!element.performance) {
                                element.performance = {};
                            }
                            element.performance.overrideUValue = data.overrideUValue;
                            element.performance.overrideSHGC = data.overrideSHGC;
                        });
                    }

                },
                // Cancelled
                function () {}
            );
        }

        /** Checks if the opening category is applicable for the new rename modal. **/
        function isOpeningRenameApplicable(parent) {
            return parent.category.constructionCategoryCode === 'ExteriorGlazing' ||
                   parent.category.constructionCategoryCode === 'InteriorGlazing' ||
                   parent.category.constructionCategoryCode === 'Skylight' ||
                   parent.category.constructionCategoryCode === 'RoofWindow';
        }

        /** Opens popup modal to mvoe or copy element/s. **/
        function launchElementActionModal(parent, building, elements = null) {

            let fromBulk = false;
            if(elements === null) {
                fromBulk = true;
                elements = parent.elements.filter(x => x.checkboxSelected === true);
            }

            var modalScope = $rootScope.$new();
            modalScope.type = "construction"; // Base type... "services" for services modal
            modalScope.parent = parent;
            modalScope.elements = elements;

            modalScope.building = building;
            modalScope.option = vm.option;
            modalScope.disabled = vm.disabledEx();

            // set available "destinations" for the move/copy based on parent category.
            modalScope.availableDestinations = [...building.surfaces, ...building.openings]
                .filter(x => x.category.constructionCategoryCode === parent.category.constructionCategoryCode);

            var modalOptions = {
                templateUrl: 'app/ui/assessment/construction-element-action-modal/construction-element-action-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);

            modalScope.modalInstance.then(
                // Submitted
                async function (data) {

                    if(data.action === 'move')
                        elements.forEach(ele => {
                            if(fromBulk) ele.checkboxSelected = false;
                            moveElementTo(ele, parent, data.destination);
                        });

                    if(data.action === 'copy')
                        elements.forEach(ele => {
                            if(fromBulk) ele.checkboxSelected = false;
                            copyElementTo(ele, data.destination);
                        });

                    if(fromBulk) {
                        elements.forEach(x => x.checkboxSelected = false);

                        parent.selectAllCheckboxState = false;
                        parent.bulkSelectCheckboxIsIndeterminate = false;
                    }

                },
                // Cancelled
                function () {}
            );
        }

        function moveElementTo(element, parent, destination) {
            copyElementTo(element, destination);
            parent.elements = parent.elements.filter(x => x.constructionId !== element.constructionId);
        }

        function copyElementTo(element, destination) {
            const copy = angular.copy(element);
            copy.constructionId = uuid4.generate();
            destination.elements.push(copy);
        }

        vm.selectAllElementCheckboxes = function (parent, state) {

            setTimeout(() => {

                let wantedResult = false;
                if (parent.bulkSelectCheckboxIsIndeterminate === true)
                    wantedResult = true;
                else if (state !== true)
                    wantedResult = true;

                parent.elements.forEach(x => x.checkboxSelected = wantedResult);

                parent.selectAllCheckboxState = wantedResult;
                vm.updateBulkSelectStatus(parent);

            }, 25);
        }

        vm.updateBulkSelectStatus = function (parent) {

            const elements = parent.elements;

            const allChecked = elements.every(x => x.checkboxSelected === true);
            const someChecked = elements.some(x => x.checkboxSelected === true);

            if(allChecked) {
                parent.bulkSelectCheckboxIsIndeterminate = false;
                parent.selectAllCheckboxState = true;

                // Have to manually apply this class (Probably due to clashes with the indeterminate value)
                const checkBoxId = parent.constructionId + "allCheckbox";
                setTimeout(() => {
                    let currentClass = document.getElementById(checkBoxId).className;
                    if (currentClass.indexOf("ng-empty-add md-checked") === -1) {
                        document.getElementById(checkBoxId).className += " ng-empty-add md-checked";
                    }
                }, 25);

            } else if(someChecked) {
                parent.selectAllCheckboxState = false;
                parent.bulkSelectCheckboxIsIndeterminate = true;
            } else {
                parent.selectAllCheckboxState = false;
                parent.bulkSelectCheckboxIsIndeterminate = false;
            }

            safeApply();

            function safeApply() {
                const phase = $rootScope.$$phase;
                if (!phase) {
                    $rootScope.$apply();
                }
            }
        }

    }
})();