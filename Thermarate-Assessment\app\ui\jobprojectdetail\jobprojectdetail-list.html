<section id="jobprojectdetail-list-view" class="main-content-wrapper" data-ng-controller="JobprojectdetailListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Jobprojectdetail"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.jobprojectdetailList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="jobprojectdetailList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="jobClientJobNumber" class="can-sort text-left">Job</th>
                        <th st-sort="lotTypeDescription" class="can-sort text-left">Plan Type</th>
                        <th st-sort="prefix" class="can-sort text-left">Prefix</th>
                        <th st-sort="strataLotNumber" class="can-sort text-right">Strata Lot Number</th>
                        <th st-sort="surveyStrataLotNumber" class="can-sort text-right">Survey Strata Lot Number</th>
                        <th st-sort="originalLotNumber" class="can-sort text-right">Original Lot Number</th>
                        <th st-sort="lotNumber" class="can-sort text-right">Lot Number</th>
                        <th st-sort="depositedPlanNumber" class="can-sort text-right">Deposited Plan Number</th>
                        <th st-sort="originalDepositedPlanNumber" class="can-sort text-right">Original Deposited Plan Number</th>
                        <th st-sort="volume" class="can-sort text-left">Volume</th>
                        <th st-sort="folio" class="can-sort text-left">Folio</th>
                        <th st-sort="houseNumber" class="can-sort text-left">House Number</th>
                        <th st-sort="streetName" class="can-sort text-left">Street Name</th>
                        <th st-sort="streetType" class="can-sort text-left">Street Type</th>
                        <th st-sort="suburb" class="can-sort text-left">Suburb</th>
                        <th st-sort="stateCode" class="can-sort text-left">State Code</th>
                        <th st-sort="postcode" class="can-sort text-left">Postcode</th>
                        <th st-sort="localGovernmentAuthority" class="can-sort text-left">Local Government Authority</th>
                        <th st-sort="projectDescriptionDescription" class="can-sort text-left">Project</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.jobprojectdetailList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="jobprojectdetail-updateform({ jobId: row.jobId})">Select</md-button>  </td>
                        <td data-title="Job" class="text-left">{{::row.jobClientJobNumber }}</td>
                        <td data-title="Plan Type" class="text-left">{{::row.lotTypeDescription }}</td>
                        <td data-title="Prefix" class="text-left">{{::row.prefix }}</td>
                        <td data-title="Strata Lot Number" class="text-right">{{::row.strataLotNumber | number }}</td>
                        <td data-title="Survey Strata Lot Number" class="text-right">{{::row.surveyStrataLotNumber | number }}</td>
                        <td data-title="Original Lot Number" class="text-right">{{::row.originalLotNumber | number }}</td>
                        <td data-title="Lot Number" class="text-right">{{::row.lotNumber | number }}</td>
                        <td data-title="Deposited Plan Number" class="text-right">{{::row.depositedPlanNumber | number }}</td>
                        <td data-title="Original Deposited Plan Number" class="text-right">{{::row.originalDepositedPlanNumber | number }}</td>
                        <td data-title="Volume" class="text-left">{{::row.volume }}</td>
                        <td data-title="Folio" class="text-left">{{::row.folio }}</td>
                        <td data-title="House Number" class="text-left">{{::row.houseNumber }}</td>
                        <td data-title="Street Name" class="text-left">{{::row.streetName }}</td>
                        <td data-title="Street Type" class="text-left">{{::row.streetType }}</td>
                        <td data-title="Suburb" class="text-left">{{::row.suburb }}</td>
                        <td data-title="State Code" class="text-left">{{::row.stateCode }}</td>
                        <td data-title="Postcode" class="text-left">{{::row.postcode }}</td>
                        <td data-title="Local Government Authority" class="text-left">{{::row.localGovernmentAuthority }}</td>
                        <td data-title="Project" class="text-left">{{::row.projectDescriptionDescription }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="20" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
