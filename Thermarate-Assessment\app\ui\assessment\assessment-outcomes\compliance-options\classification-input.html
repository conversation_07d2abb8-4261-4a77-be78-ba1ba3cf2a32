<!-- NOTE: Probably no longer in use. Used to display 5 circles that you could click. Has been changed elsewhere to just be a simple dropbox...-->
<span ng-repeat="i in [1, 2, 3, 4, 5]"
      style="font-size: 20px;">
    <i ng-if="vm.classification >= i"
       class="fa fa-circle"
       ng-click="vm.clicked(true, i)">
    </i>
    <i ng-if="vm.classification == null || vm.classification < i"
       class="fa fa-circle-o"
       ng-click="vm.clicked(true, i)">
    </i>
</span>