<form id="loginForm" name="form" novalidate class="login-form validated-form form-horizontal" data-ng-controller="LoginFormController as vm">
    <md-dialog aria-label="Sign In" flex-gt-sm="100">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>Sign In</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="vm.cancelLogin()">
                    <md-icon class="icon_close" aria-label="Close dialog">close</md-icon>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content layout-padding>
            <div >
                <!--Company logo image-->
                <img src="content/images/login-logo.png" width="80" style="padding-bottom: 0px; padding-top: 0px; " />
            </div>
            <div class="" style="font-size: 20px; color: #085589; padding-top: 0px; ">
                <strong>Thermarate Assessment System</strong> Login
            </div>
            <div class="text-info" style="padding-top:8px;">Please enter your login details.</div>
            <br />
            <md-input-container class="md-block" flex-gt-sm>
                <label>Email Address</label>
                <input name="login" type="text" ng-model="vm.user.email" required md-autofocus md-minlength="3" md-maxlength="60" ng-keyup="$event.keyCode == 13 && vm.enterLogin()">
                <div ng-messages="loginForm.login.$error" >
                    <div ng-message="required">Email Address is required.</div>
                    <div ng-message="md-maxlength">Email Address has to be less than 60 characters long.</div>
                    <div ng-message="md-minlength">Email Address must be at least 3 characters long.</div>
                </div>
            </md-input-container>
            <md-input-container class="md-block" flex-gt-sm style="height: 44px;">
                <label>Password</label>
                <input name="pass" type="password" ng-model="vm.user.password" required ng-minlength="5" ng-keyup="$event.keyCode == 13 && vm.enterLogin()">
            </md-input-container>
            <div class="error">
                <div ng-if="!vm.authReason">{{vm.authReason}}</div>
                <div ng-if="vm.authError">{{vm.authError}}</div>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-hue-1" ng-click="vm.forgotLogin()" title="click to retrieve a new password" ng-disabled='vm.user.email == null'>Forgot Password</md-button>
            <md-button class="md-primary md-raised" ng-click="vm.login()" ng-disabled='form.$invalid'>Sign in</md-button>
            <md-button class="md-raised" ng-click="vm.cancelLogin()">Cancel</md-button>
        </md-dialog-actions>
    </md-dialog>
</form>
