<form name="BuildingConstructionTemplateForm" 
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='BuildingConstructionTemplateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>

        <md-card layout-margin ng-form="InfoForm">
            <md-card-header>
                <span class="md-title">Template Information</span>
            </md-card-header>
            <md-card-content>

                <fieldset redi-enable-roles="admin__template__edit">

                    <!-- ******** Template name ******** -->
                    <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="40">
                        <label>Template Name</label>
                        <input type="text" name="templatename"
                               ng-model="vm.template.templateName"
                               maxlength="1000"
                               required />
                        <div ng-messages="templateinfoform.templatename.$error">
                        </div>
                    </md-input-container>

                    <!-- ******** Template Notes ******** -->
                    <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="40">
                        <label>Template Notes</label>
                        <textarea type="text" name="templatenotes"
                                  ng-model="vm.template.notes"
                                  rows="4" />
                        <div ng-messages="assessmentform.templatenotes.$error">
                        </div>
                    </md-input-container>

                </fieldset>

            </md-card-content>
        </md-card>

        <md-card layout-margin ng-form="ConstructionForm">
            <md-card-header>
                <span class="md-title">{{vm.subheading}}</span>
            </md-card-header>
            <md-card-content>

                <!-- The NG-IF is a hack here to not load this until we have our template data -->
                <building-construction-data ng-if="vm.template != null"
                                            option="vm.template"
                                            building="vm.template"
                                            building-type="'template'"
                                            construction-category-list="vm.shownConstructionCategories"
                                            general-section-display="vm.template.templateType"
                                            disabled="vm.editPermission == false"
                                            is-template="true">
                </building-construction-data>

            </md-card-content>
        </md-card>

        <div class="fixed-action-bar">
            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="vm.isBusy || vm.editPermission == false"
                           type="button" 
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           ng-disabled="vm.isBusy || vm.deletePermission == false"
                           type="button" 
                           ng-click="vm.delete()">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>
        </div>

    </div>
</form>       
