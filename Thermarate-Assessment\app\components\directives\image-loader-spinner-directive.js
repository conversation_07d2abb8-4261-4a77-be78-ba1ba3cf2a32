﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('spinnerLoad', ["$rootScope", function spinnerLoad($rootScope) {
        return {
            scope: {
                ngSrc: '@'
            },
            link: function (scope, element) {
                element.on('load', function () {
                    element.addClass('in');
                    element.prev('i.fa-spinner').remove();
                });
                element.on('error', function(){
                    element.prev('i.fa-spinner').remove();
                    if (scope.ngSrc.indexOf("https://go.westernpower") > -1) {
                        $rootScope.$broadcast('WP-Login-Required');
                    }
                    element.replaceWith('<div class="red text-center"><br/><br/><i class="fa fa-image fa-2x"></i><br/><br/>Failed To Load Image</div>');
                });
                scope.$watch('ngSrc', function () {
                    element.removeClass('in');
                    element.addClass('fade');
                    element.before('<i class="fa fa-spinner fa-lg fa-spin"></i>');
                });
            }
        }
    }]);
})();