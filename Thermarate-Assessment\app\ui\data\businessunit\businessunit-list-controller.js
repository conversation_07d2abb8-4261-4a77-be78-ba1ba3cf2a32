(function () {
    // The BusinessunitListCtrl supports a list page.
    'use strict';
    var controllerId = 'BusinessunitListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$state', '$mdDialog', 'businessunitservice', 'daterangehelper', businessunitListController]);
function businessunitListController($rootScope, $state, $mdDialog, businessunitservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Business Units';
        vm.businessunitList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'name',
                    description: 'Name',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'businessUnitType',
                    description: 'Business Unit Type',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'managerUserId',
                    description: 'Manager',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'businessUnitPrefix',
                    description: 'Business Unit Prefix',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'emailAddress',
                    description: 'Email Address',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'phone',
                    description: 'Phone',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'mobile',
                    description: 'Mobile',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'fax',
                    description: 'Fax',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressLine1',
                    description: 'Address Line1',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressLine2',
                    description: 'Address Line2',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressSuburb',
                    description: 'Address Suburb',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressStateCode',
                    description: 'Address State',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressPostCode',
                    description: 'Address Post Code',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'addressFormatted',
                    description: 'Address Formatted',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        var persistRangeName = "businessunitList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        vm.goToBusinessUnit = function (businessUnitId) {
            $state.go("businessunit-updateform", { businessUnitId: businessUnitId });
        }

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createBusinessunit = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/businessunit/businessunit-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "name", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            businessunitservice.getListCancel();
            businessunitservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = businessunitservice.currentFilter();
                    vm.businessunitList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.businessunitList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createBusinessunit,
                name: 'Add Business Unit',
                desc: 'Add Business Unit',
                roles: ['admin__businessunit__create'],
            });
        }

        setActionButtons();
    }
})();