(function () {
    // The LottypeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'LottypeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'lottypeservice', lottypeUpdateController]);
function lottypeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  lottypeservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Plan Type';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.lotTypeCode = null; //planTypeCode
        vm.lottype = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Plan Type";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.lotTypeCode = $scope.lotTypeCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.lotTypeCode = $stateParams.lotTypeCode;
        }

        // Get data for object to display on page
        var lotTypeCodePromise = null;
        if (vm.lotTypeCode != null) {
            lotTypeCodePromise = lottypeservice.getLotType(vm.lotTypeCode)
            .then(function (data) {
                if (data != null) {
                    vm.lottype = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("lottype-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                lottypeservice.createLotType(vm.lottype).then(function(data){
                    vm.lottype = data;
                    vm.lotTypeCode = vm.lottype.lotTypeCode; //planTypeCode
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                lottypeservice.updateLotType(vm.lottype).then(function(data){
                    if (data != null) {
                        vm.lottype = data;
                        vm.lotTypeCode = vm.lottype.lotTypeCode; //planTypeCode
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            lottypeservice.deleteLotType(vm.lotTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            lottypeservice.undoDeleteLotType(vm.lotTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();