 (function () {
    // The JobUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'JobUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'constants', 'Upload', 'bootstrap.dialog', 'uuid4', 
            'projectdescriptionservice', 'lottypeservice', 'suburbservice', 'addressservice', 'stateservice',
            'streettypeservice', 'clientservice',
            'compliancemethodservice', 
            'userservice', 'statusservice', 'jobservice', 'assessmentservice',
            'fileservice', 'sequentialguidservice', 'priorityservice', 'clientdefaultservice',
            'assessmentsoftwareservice', 'designchangeservice',
            'assessmentdrawingservice', 'metromapservice', 'worksdescriptionservice', 'zonetypeservice',
            'zoneservice', 'bushfireproneservice', 'nccclimatezonedataservice', 'nccclimatezoneservice',
            'nominatedbuildingsurveyorservice', 'buildingconstructiontemplateservice', 'assessmentcomplianceoptionservice',
            'buildingdesigntemplateservice', 'bushfireattacklevelservice', 'bushfirestatedataservice', 'certificationservice', 'constructionservice', 
            'buildingservicestemplateservice', 'security',
             jobUpdateController]);
    function jobUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
        constants, Upload, modalDialog, uuid4, 
        projectdescriptionservice, lottypeservice, suburbservice, addressservice, stateservice,
        streettypeservice, clientservice,
        compliancemethodservice, 
        userservice, statusservice, jobservice, assessmentservice,
        fileservice, sequentialguidservice, priorityservice, clientdefaultservice,
        assessmentsoftwareservice,  designchangeservice,
        assessmentdrawingservice, metromapservice, worksdescriptionservice, zonetypeservice,
        zoneservice, bushfireproneservice, nccclimatezonedataservice, nccclimatezoneservice,
        nominatedbuildingsurveyorservice, buildingconstructiontemplateservice, assessmentcomplianceoptionservice,
        buildingdesigntemplateservice, bushfireattacklevelservice, bushfirestatedataservice, certificationservice, constructionservice, 
        buildingservicestemplateservice, security) {
        // The model for this form 
        var vm = this;

        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Job';
        vm.subtitle = "";
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.hideActionBar = false;
        vm.jobId = null;
        vm.uploadBusy = false;
        vm.progressMode = "determinate";
        vm.tempStreetTypeSearchQuery = "";
        vm.assessmentVersionRegex = "\\d(\\.\\d{1,2})?";
        //var myRegex = new RegExp('^[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?),\\s*[-+]?(180(\\.0+)?|((1[0-7]\\d)|([1-9]?\\d))(\\.\\d+)?)$');
        vm.latRegex = "[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?)";
        vm.lonRegex = "[-+]?(180(\\.0+)?|((1[0-7]\\d)|([1-9]?\d))(\\.\\d+)?)";
        vm.degreesDMSRegex = "[0-9]{1,3}";
        vm.minutesDMSRegex = "[0-9]{1,2}";
        vm.secondsDMSRegex = "[0-9]{1,2}(?:\\.[0-9]{1,2})?";
        vm.directionLatRegex = "[N|S]{1}";
        vm.directionLonRegex = "[E|W]{1}";
        vm.job = {};

        vm.job.currentAssessment = {
            assessmentId: uuid4.generate(),
            assessmentProjectDetail: {}
        };
        vm.useDMS = false;
        vm.dms = {};
        vm.attachedDrawings = [];
        vm.showLotNumberInput = false; // We actually want this hidden until they attempt a search.

        vm.showprojectDescriptions = false;

        vm.pendingFileList = {};
        var pendingPdfConverRecordsTimeout = null;

        // Flags to switch between 'New Job' and 'Recertification'-style dialogs.
        const TYPE_NEWJOB = "NEW_JOB";
        const TYPE_RECERTIFY = "RECERTIFY";
        const TYPE_COPY = "COPY_ASSESSMENT";

        vm.TYPE_NEWJOB = TYPE_NEWJOB;
        vm.TYPE_RECERTIFY = TYPE_RECERTIFY;
        vm.TYPE_COPY = TYPE_COPY;
        vm.type = $scope.type;

        vm.newRecord = vm.type === TYPE_NEWJOB;

        vm.blankConstructionTemplate = {

            categoriesNotRequired: {},
            openings: [],
            surfaces: [],
            categoriesWithExternalData: {},
            classification: null

        };

        vm.blankDesignTemplate = {
            buildingDesignTemplateId: null,

            projectDescription: null,
            projectDescriptionOther: null,
            projectClassification: null,
            designFeatures: {},
            design: null,
            storeys: null,
            zones: null,
            buildingOrientation: null,
            zoneTypesNotApplicable: { floorPlanSpaces: true, generalroofs: true } ,
        };

        vm.blankServicesTemplate = {
            categoriesNotRequired: {},
            services: [],
        };

        vm.modifiedDesignTemplate = {
            // This is ridiculous.
        };

        // -----------------------------------------------------------------------------------
        // NEW RECORD-ONLY INITIALIZATION 
        if (vm.type == TYPE_NEWJOB) {
            vm.isModal = true;
            vm.hideActionBar = true;
            vm.title = "New Job";
            // Set any default values required for a new record.
            sequentialguidservice.getGuid().then(function (data) {
                vm.job.jobId = data;
                vm.jobId = data;
                vm.isBusy = false;
            });
        }
        // END NEW RECORD-ONLY INITIALIZATION 
        // -----------------------------------------------------------------------------------

        vm.buildingSurveyorList = [];
        var buildingSurveyorPromise = nominatedbuildingsurveyorservice.getList()
            .then(function (data) {
                vm.allBuildingSurveyorList = data;
                vm.buildingSurveyorList = vm.allBuildingSurveyorList;
            });

        // -----------------------------------------------------------------------------------
        // RECERTIFICATION + COPY INITIALIZATION 

        // This first bunch is actually shared between the copy & recert functions.
        if (vm.type === TYPE_RECERTIFY || vm.type === TYPE_COPY) {

            vm.isModal = true;
            vm.hideActionBar = true;

            vm.job = {};
            vm.job.currentAssessment = {};

            let clientOptions = $scope.clientOptions;

            // Load the base assessment up - this is the basis for all fields.
            vm.baseAssessment = $scope.baseAssessment;

            if (vm.baseAssessment) {
                angular.copy(vm.baseAssessment.job, vm.job);
                vm.job.currentAssessment = angular.copy(vm.baseAssessment);
                vm.job.currentAssessment.assessmentProjectDetail.orderDate = new Date();

                // Use the currently logged in user as the default 'creator' value
                userservice.getUser(security.currentUser.rssUserId).then(function (data) {
                    vm.job.currentAssessment.assessmentProjectDetail.creator = data;
                });

                if (vm.type === TYPE_COPY) {
                    vm.job.jobId = uuid4.generate(); // Jobs need a new UUID also when copying.
                    vm.job.currentAssessment.assessmentProjectDetail.clientJobNumber = null;
                    vm.job.currentAssessment.recertificationDesignChanges = [];
                }

                vm.jobId = vm.job.jobId;
                vm.job.currentAssessment.assessmentId = uuid4.generate(); // New Assessment Guid

                setMinHouseEnergyRating();

                // Also don't want to copy prior recertifications reasons across.
                vm.job.currentAssessment.notes = null;

                vm.floors = vm.job.currentAssessment.allComplianceOptions[0].proposed.storeys.length;

                // Copy all options by default.
                if(vm.type === TYPE_COPY) {
                    vm.job.currentAssessment.allComplianceOptions.forEach(opt => {
                        opt.doCopy = true;
                        optionCopyClicked(opt);
                        opt.copyAnnualEnergyLoads = true;
                        opt.copyAssessmentFiles = true;
                        opt.copyMultiSim = true;
                        opt.copyDrawings = true;
                        opt.isSelected = false;
                    });
                }

                // Only bring across the selected option and make it un-de-selectable?
                if(vm.type === TYPE_RECERTIFY) {

                    let selected = vm.job.currentAssessment.allComplianceOptions.find(x => x.isSelected === true);

                    if(selected == null)
                        selected = vm.job.currentAssessment.allComplianceOptions[0];

                    selected.doCopy = true;

                    applyClientTemplateDefaultsForRecert(selected);

                    selected.copyAnnualEnergyLoads = false;
                    selected.copyAssessmentFiles = false;
                    selected.copyMultiSim = false;
                    selected.copyDrawings = false;
                    selected.isBaselineSimulation = true; // New baseline.

                    // Discard the rest.
                    vm.job.currentAssessment.allComplianceOptions = [selected];

                }

                // We don't want to copy the purchase order data across, so set to null
                vm.job.currentAssessment.allComplianceOptions[0].purchaseOrder = null;
                vm.job.currentAssessment.allComplianceOptions[0].purchaseOrderFile = null;
                vm.job.currentAssessment.allComplianceOptions[0].isExpanded = true;
                vm.job.currentAssessment.allComplianceOptions[0].isSelected = false;

                // Back this up in case we want to pull building construction data of it in the future.
                vm.backupBaselineSimulation = angular.copy(vm.job.currentAssessment.allComplianceOptions[0]);
                vm.backupProposedTemplate = {   // Some duplicate data is ok.
                    ...vm.backupBaselineSimulation,
                    ...vm.backupBaselineSimulation.proposed
                }
                vm.backupReferenceTemplate = {
                    ...vm.backupBaselineSimulation,
                    ...vm.backupBaselineSimulation.proposed
                }

                vm.backupZonesTemplate = {
                    buildingDesignTemplateId: vm.job.currentAssessment.allComplianceOptions[0].proposed.buildingZonesTemplateId,
                    zones: vm.job.currentAssessment.allComplianceOptions[0].proposed.zones
                };

            }

            //Wait for the required dropdown lists to set so the client options can filter them
            $q.all([projectDescriptionPromise, complianceMethodPromise, certificationPromise,
                 assessmentSoftwarePromise, buildingSurveyorPromise, worksDescriptionPromise])
                 .then(function () {
                if (!_.isEmpty(clientOptions)) {
                    //Set client options
                    restrictAvailableOptions(clientOptions);
                }
            });

            clientdefaultservice.getClientDefault(vm.job.client.clientId)
                .then(function (data) {
                    if (data != null) {

                        //Set client defaults
                        vm.purchaseOrderCode = data.purchaseOrderCode;

                        // Actually we don't _want_ to fire this immediately here otherwise a lot of
                        // values will be overridden... and we don't want that.
                        // vm.clientChanged(); // Fire this immediately since our client will be filled in for recert & copy.
                    }
                });

            if (vm.type == TYPE_RECERTIFY) {
                vm.title = 'Recertification';
            }

            if (vm.type == TYPE_COPY) {
                vm.title = 'Copy Assessment';
            }

            vm.isBusy = false;

        }

        // END RECERTIFICATION ONLY INITIALIZATION AND FUNCTIONS
        // -----------------------------------------------------------------------------------

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.jobId = $scope.jobId;
            }
            vm.hideActionBar = true;
        } else {
            vm.jobId = $stateParams.jobId;
        }

        // -------------------------------------------------------------------------------------------------------------------------------------------
        // METRO MAP STUFF

        // DOM Elements we will need to interact with
        const remoteMapElem = 'remote_map';
        const remoteSiteMapElem = 'remote_site_map';

        vm.selectedMapRawImageBuffer = null;        // Aerial (Metro) map
        vm.selectedSiteMapRawImageBuffer = null;    // Geometry (OpenStreetMaps/GMaps) map

        // Completely random coordinates to start the map on (should never be seen here)!
        const initialLat = -32;
        const initialLng = 151;
        const siteInfoZoomLevel = 18;
        const aerialMapZoomLevel = 21;

        // END METRO MAP STUFF
        // -------------------------------------------------------------------------------------------------------------------------------------------

        vm.floorsChanged = function (count, building, floorOverrides, forceRename) {

            vm.floors = count;

            let existingFloorCount = building?.storeys?.length;

            if(forceRename)
                existingFloorCount = null;

            let newStoreys = assessmentcomplianceoptionservice
                .generateStoreys(count, building?.storeys);

            // The client defaults are only used for the VERY INITIAL client selection here, afterwards
            // the building description or hard-coded defaults apply.
            newStoreys = assessmentcomplianceoptionservice.setStoreyNames(newStoreys, floorOverrides, existingFloorCount)

            building.storeys = newStoreys;

        }

        function setMinHouseEnergyRating() {
            vm.tempMinHouseEnergyRating = vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating?.toFixed(1);
        }

        vm.clearMinHouseEnergyRating = function () {
            vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating = null;
            vm.tempMinHouseEnergyRating = null;
            vm.minHouseEnergyRatingSearchText = '';
        }

        // Get data for object to display on page
        var jobIdPromise = null;
        if (vm.jobId != null) {
            jobIdPromise = jobservice.getJob(vm.jobId)
            .then(function (data) {
                if (data != null) {
                    vm.job = data;
                    vm.subtitle = vm.job.jobReference + " | " + vm.job.clientName + " | " + vm.job.projectOwner;
                    setMinHouseEnergyRating();
                }
                vm.isBusy = false;
            });
        }
        else if (vm.type == TYPE_NEWJOB) {

            // New Job
            vm.isBusy = false;

            // Add Baseline Compliance option
            vm.job.currentAssessment.allComplianceOptions = [defaultBaselineRun()];

            // Fill EP floors and glazing calcs for initial baseline run.
            //vm.floorsChanged(1, vm.job.currentAssessment.allComplianceOptions);

            vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating = null;
            vm.job.currentAssessment.assessmentProjectDetail.orderDate = new Date();
            vm.job.currentAssessment.performanceRequirementP262Code = "P262Satisfied";
            vm.job.currentAssessment.complianceStatusCode = "CSNotAchieved";
            vm.job.currentAssessment.statusCode = "AInProgress";
            $scope.originalModel = angular.copy(vm.job);

        }

        vm.searchChanged = function (streetTypeItem, searchText) {
            vm.tempStreetTypeSearchQuery = searchText;
        }

        vm.streetTypeItemChanged = function (streetTypeItem, newStreetType, searchText) {
            if (newStreetType == "Other") {
                streetTypeItem["streetType"] = vm.tempStreetTypeSearchQuery;
            } else if (newStreetType != undefined) {
                streetTypeItem["streetType"] = newStreetType;
            } 
        }

        vm.suburbItemChanged = function (suburbItem) {
            if (suburbItem != undefined && typeof suburbItem == 'object') {
                vm.job.currentAssessment.assessmentProjectDetail.suburb = suburbItem.name;
                vm.job.currentAssessment.assessmentProjectDetail.stateCode = suburbItem.stateCode;
                vm.job.currentAssessment.assessmentProjectDetail.postcode = suburbItem.postcode;
                vm.job.currentAssessment.assessmentProjectDetail.localGovernmentAuthority = suburbItem.localGovernmentArea;
            }
        }

        vm.assessmentSoftwareOtherChanged = function () {

            if (vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode == 'Other' && vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareOther == null) {
                vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode = '';
            }
        }

        vm.clearAssessmentSoftwareDescription = function (item) {
            item.assessmentSoftwareCode = null;
            item.assessmentSoftwareOther = null;
        }

        // Get data for any dropdown lists
        vm.getstreettypes = function (searchTerm) {
            var filter = [{ field: "description", operator: "startswith", value: searchTerm }];
            return streettypeservice.getList(null, null, null, null, null, null, filter)
            .then(function (data) {
                data.data.push({ description: "Other" });
                return data.data;
            });
        }

        vm.getsuburbs = function (searchTerm) {
            var filter = [{ field: "name", operator: "startswith", value: searchTerm }];
            return suburbservice.getList(null, null, null, null, null, null, filter)
            .then(function (data) {
                return data.data;
            });
        }

        vm.assessmentSoftwareList = [];
        var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
            .then(function (data) {
                vm.assessmentSoftwareList = data;
                vm.allAssessmentSoftwareList = data;
            });

        vm.stateList = [];
        var statePromise = stateservice.getList()
            .then(function(data){
                vm.stateList = data.data;
            });

        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function(data){
                vm.complianceMethodList = data.data;
                vm.allComplianceMethodList = vm.complianceMethodList;
            });
        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.certificationList = data.data;
                vm.allCertificationList = vm.certificationList;
            });

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.priorityList = [];
        var priorityPromise = priorityservice.getList()
            .then(function (data) {
                vm.priorityList = data.data;
            });

        vm.designChangeList = [];
        var designChangePromise = designchangeservice.getList()
            .then(function (data) {
                vm.designChangeList = data.data;
            });

        vm.allWorksDescriptionList = [];
        vm.worksDescriptionList = [];
        var worksDescriptionPromise = worksdescriptionservice.getList()
            .then(data => { vm.worksDescriptionList = vm.allWorksDescriptionList = data; })

        // Job Project Details Dropdowns
        vm.lotTypeList = [];
        var lotTypePromise = lottypeservice.getList()
            .then(function (data) {
                vm.lotTypeList = data.data;
            });
        vm.projectDescriptionList = [];
        var projectDescriptionPromise = projectdescriptionservice.getList()
            .then(function (data) {
                vm.projectDescriptionList = data.data;
            });

        vm.nccClimateZoneList = [];
        let nccClimateZonePromise = nccclimatezoneservice.getList()
            .then(function(data){
                vm.nccClimateZoneList = data.data;
            });

        //list for filtered templates.
        vm.buildingConstructionTemplates = [];
        buildingconstructiontemplateservice
            .getAll('construction')
            .then((list) => vm.buildingConstructionTemplates = list);

        vm.buildingOpeningTemplates = [];
        buildingconstructiontemplateservice
            .getAll('opening')
            .then((list) => vm.buildingOpeningTemplates = list);

        vm.buildingServicesTemplates = [];
        buildingservicestemplateservice
            .getAll()
            .then((list) => vm.buildingServicesTemplates = list);

        vm.buildingDesignTemplates = [];
        let designTemplatePromise = buildingdesigntemplateservice
            .getAll()
            .then((list) => vm.buildingDesignTemplates = list);

        function updateAvailableSoftware() {

            var currentSoftwareCode = vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode;
            var currentComplianceCode = vm.job.currentAssessment.allComplianceOptions[0].complianceMethod?.complianceMethodCode;

            for (var ii = 0; ii < vm.assessmentSoftwareList.length; ii++) {
                var softwareFound = false;
                for (var jj = 0; jj < vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods.length; jj++) {
                    if (vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].complianceMethodCode == currentComplianceCode && 
                        vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].isAvailable) {
                        vm.assessmentSoftwareList[ii].isAvailable = true;
                        softwareFound = true;
                        break;
                    }
                }
                if (!softwareFound) {
                    vm.assessmentSoftwareList[ii].isAvailable = false;
                }

                if (currentSoftwareCode == vm.assessmentSoftwareList[ii].assessmentSoftwareCode && !vm.assessmentSoftwareList[ii].isAvailable) {
                    vm.job.currentAssessment.assessmentSoftwareCode = null; // clear selected option because software is invalid.
                }
            }
        }
        vm.updateAvailableSoftware = updateAvailableSoftware;

        $q.all([complianceMethodPromise, assessmentSoftwarePromise]).then(function () {
            updateAvailableSoftware();
        });

        // Functions to get data for Typeahead
        vm.getcontacts = function(searchTerm) {
            if (vm.job.client == null) {
                return [];
            } else {
                var filter = [{ field: "fullName", operator: "startswith", value: searchTerm }];
                if (vm.job.client && vm.job.client.clientId != null) {
                    filter[0].logic = "and";
                    filter.push({ field: "clientId", operator: "eq", value: vm.job.client.clientId, valueType: "guid?" });
                }
                return userservice.getList(null, null, null, null, null, null, filter, null, vm.job.client.clientId ).then(data => {
                    return data.data;
                });
            }
        }

        vm.minHouseEnergyRatingSearchText = "";

        eventListenerList.push($scope.$on('CreateContact', function(event){
            event.stopPropagation();
            vm.createContact() // function to launch add modal;
            }));

        vm.createContact = function() {
            // Add logic to display create modal form.
        }
        vm.fakeClientId = 'FAKE_ID_FOR_FAKE_CLIENT';
        vm.getclients = function(searchTerm) {
            var filter = [{ field: "clientName", operator: "startswith", value: searchTerm }];
            var sort = [{ field: "isFavourite", dir: "desc" }, { field: "clientName", dir: "asc" }];

            return clientservice.getList(null, null, null, null, null, sort, filter)
            .then(function (data) {
                var list = data.data;

                list.push({
                    accountsEmail:"",
                    accountsFirstName: "",
                    accountsLastName: "",
                    accountsNote:"",
                    accountsPhone:"",
                    clientId: vm.fakeClientId,
                    clientName:"ADD A NEW CLIENT",
                    createdByName:"admin",
                    createdOn:Date.now(),
                    defaultClientAssigneeUserId:"",
                    deleted:false,
                    modifiedByName:"admin",
                    modifiedOn:Date.now()
                });

                return list;
            });
        }

        function clearFields() {
            vm.job.currentAssessment.assessmentProjectDetail.clientJobNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.projectOwner = null;
            vm.job.currentAssessment.allComplianceOptions[0].purchaseOrder = null;
            vm.job.currentAssessment.nccClimateZoneCode = null;
            vm.job.assessorUserId = null;
            vm.job.currentAssessment.notes = null;
            vm.purchaseOrderCode = null;
        }

        vm.constructionCategoryList = []
        var constructionCategoryPromise = constructionservice.getConstructionCategoryList()
            .then((data) => {
                vm.constructionCategoryList = data;
                vm.constructionTabCategories = constructionservice.constructionCategories();
                vm.openingTabCategories = constructionservice.openingCategories();
            });

        vm.clientChanged = function () {

            clearFields();

            if (vm.job.client && vm.job.client.clientId == vm.fakeClientId) {
                vm.isBusy = true;
                var newScope = $rootScope.$new(true);
                $mdDialog.show({
                    templateUrl: 'app/ui/job/new-client-contact-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: true,
                    scope: newScope,
                    skipHide: true, // DON'T HIDE THE MODAL
                })
                .then(function (response) {
                    // make a new client
                    var clientDto = {
                        clientName: response.clientName,
                        accountsFirstName: response.accountsFirstName,
                        accountsLastName: response.accountsLastName,
                        accountsPhone: response.accountsPhone,
                        accountsEmail: response.accountsEmailAddress,
                        accountsNote: response.accountsNote,
                        accountsSameAsContact: response.accountsSameAsContact,
                        clientOptions: response.clientOptions
                    };
                    clientservice.createClient(clientDto)
                    .then(function (data) {
                        var newClientDto = data.data;

                        // Assign newly created client before creating user.
                        response.user.client = newClientDto;
                        userservice.createUser(response.user)
                            .then(function (data) {

                            var newContactDto = data.data;
                            newClientDto.defaultClientAssigneeUserId = newContactDto.contactId;
                            vm.job.client = newClientDto;
                            vm.clientIdSearchText = newClientDto.clientName;

                            vm.clientChanged();

                            vm.job.currentAssessment.assessmentProjectDetail.clientAssignee = newContactDto;
                            vm.contactIdSearchText = newContactDto.fullName;

                            vm.isBusy = false;
                        });
                    });
                }, function () {
                    //cancelled
                    vm.job.client = undefined;
                    vm.job.currentAssessment.assessmentProjectDetail.clientAssignee = undefined;
                    vm.clientIdSearchText = "";
                    vm.contactIdSearchText = "";
                });

                return;
            }

            if (vm.job.client && vm.job.client.defaultClientAssigneeUserId) {
                userservice.getUser(vm.job.client.defaultClientAssigneeUserId).then(function (data) {
                    vm.job.currentAssessment.assessmentProjectDetail.clientAssignee = data;
                });
            }

            if (vm.job.currentAssessment != null) {

                // TODO: what is this doing / why?????
                if (vm.job.currentAssessment.assessmentProjectDetail.creator && vm.job.client && vm.job.currentAssessment.assessmentProjectDetail.creator.clientId != vm.job.client.clientId) {
                    vm.job.currentAssessment.assessmentProjectDetail.creator = null;
                }

                // Use the currently logged in user as the default 'creator' value, rather than the client default.
                userservice.getUser(security.currentUser.rssUserId).then(function (data) {
                    vm.job.currentAssessment.assessmentProjectDetail.creator = data;
                });

                if (vm.job.client != undefined) {
                    vm.isBusy = true;
                    clientdefaultservice.getClientDefault(vm.job.client.clientId)
                        .then(function (data) {

                            if (!data) {
                                data = {};
                            }

                            if (data != null) {

                                vm.currentClientDefault = data;

                                //Set client options
                                if (vm.job.client.clientOptions) {
                                    restrictAvailableOptions(vm.job.client.clientOptions);
                                } else {
                                    //No options so reset dropdown lists and fields
                                    resetOptions();
                                }

                                //Set client defaults
                                vm.job.currentAssessment.priorityCode = data.priorityCode;

                                if (vm.type == TYPE_NEWJOB)
                                    vm.job.currentAssessment.assessmentProjectDetail.emailAssessor = data.emailAssessorNewJob;
                                else if (vm.type == TYPE_RECERTIFY)
                                    vm.job.currentAssessment.assessmentProjectDetail.emailAssessor = data.emailAssessorRecertification;
                                else if (vm.type == TYPE_COPY)
                                    vm.job.currentAssessment.assessmentProjectDetail.emailAssessor = data.emailAssessorCopyAssessment;

                                vm.purchaseOrderCode = data.purchaseOrderCode;

                                vm.projectDescriptionChanged(vm.job.currentAssessment.allComplianceOptions[0].proposed, data.projectDescription, "");

                                vm.job.currentAssessment.allComplianceOptions[0].complianceMethodCode = data.preliminaryComplianceMethodCode;
                                vm.job.currentAssessment.allComplianceOptions[0].complianceMethod = vm.allComplianceMethodList
                                    .find(x => x.complianceMethodCode === data.preliminaryComplianceMethodCode);

                                vm.job.currentAssessment.assessmentProjectDetail.nominatedBuildingSurveyorId = data.nominatedBuildingSurveyorId;
                                vm.job.currentAssessment.prioritySode = data.priorityCode;

                                complianceMethodChanged();

                                vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftware = vm.allAssessmentSoftwareList.find(x => x.assessmentSoftwareCode ===  data.assessmentSoftwareCode);
                                vm.assessmentSoftwareOtherChanged();

                                vm.job.currentAssessment.allComplianceOptions[0].certification
                                    = vm.allCertificationList.find(x => x.certificationId == data.certificationId);

                                if(data.worksDescription != null)
                                    vm.job.currentAssessment.worksDescription = vm.allWorksDescriptionList
                                        .find(x => x.worksDescriptionCode == data.worksDescription.worksDescriptionCode)

                                vm.job.currentAssessment.buildingExposureCode = data.buildingExposureCode;

                                // Apply default assessment notes if applicable.
                                vm.job.currentAssessment.assessorNotesNotApplicable = data.assessorNotesNotApplicable;
                                if(!data.assessorNotesNotApplicable)
                                    vm.job.currentAssessment.assessorNotes = data.assessorNotes;

                                vm.job.currentAssessment.qrCodeEnabled = data.qrCodeEnabled;

                                //Assign assessor
                                if (data.assessorUserId != undefined) {
                                    userservice.getUser(data.assessorUserId)
                                        .then(function (result) {
                                            vm.job.assessorUser = result;
                                            vm.isBusy = false;
                                        });
                                }

                                vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating = data.minHouseEnergyRating;
                                setMinHouseEnergyRating();

                                vm.job.currentAssessment.allComplianceOptions[0].proposed.lowestLivingAreaFloorType = data.lowestLivingAreaFloorType;

                                // Assign building construction templates if applicable
                                vm.applyClientDefaultTemplatesToBuilding(vm.job.currentAssessment.allComplianceOptions[0].proposed, 'proposed', data);
                                vm.applyClientDefaultTemplatesToBuilding(vm.job.currentAssessment.allComplianceOptions[0].reference, 'reference', data);

                            }
                        });

                    vm.isBusy = false;
                } else {
                    //No options so reset dropdown lists and options
                    resetOptions();
                }
            }
        }

        //Initial set up for options modifying the field display on the UI
        vm.complianceOptions = [];

        vm.applyClientDefaultTemplatesToBuilding = function(building, buildingString, clientDefaults) {

            if(building == null || clientDefaults == null)
                throw 'building or clientDefaults cannot be null.';

            // Apply construction (surfaces) template.
            if (clientDefaults[buildingString + "ConstructionTemplateId"] != null) {
                building.constructionTemplateId = clientDefaults[buildingString + "ConstructionTemplateId"];
                if (clientDefaults[buildingString + "ConstructionTemplateId"] !== "BLANK_TEMPLATE") {
                    const t = vm.buildingConstructionTemplates
                        .find(x => x.buildingConstructionTemplateId === clientDefaults[buildingString + "ConstructionTemplateId"]);

                    applyConstructionTemplate(
                        vm.job.currentAssessment.allComplianceOptions[0],
                        building,
                        t,
                        'proposed'
                    );
                }
            }

            // Apply openings template.
            if (clientDefaults[buildingString + "OpeningTemplateId"] != null) {
                building.openingTemplateId = clientDefaults[buildingString + "OpeningTemplateId"];
                if (clientDefaults[buildingString + "OpeningTemplateId"] !== "BLANK_TEMPLATE") {

                    const t = vm.buildingOpeningTemplates
                        .find(x => x.buildingConstructionTemplateId === clientDefaults[buildingString + "OpeningTemplateId"]);

                    applyConstructionTemplate(
                        vm.job.currentAssessment.allComplianceOptions[0],
                        building,
                        t,
                        'proposed'
                    );
                }
            }

            // Apply services template.
            if (clientDefaults[buildingString + "ServicesTemplateId"] != null) {
                building.servicesTemplateId = clientDefaults[buildingString + "ServicesTemplateId"];
                if (clientDefaults[buildingString + "ServicesTemplateId"] !== "BLANK_TEMPLATE") {

                    const t = vm.buildingServicesTemplates
                        .find(x => x.buildingServicesTemplateId === clientDefaults[buildingString + "ServicesTemplateId"]);

                    buildingservicestemplateservice.applyTemplate(
                        vm.job.currentAssessment.allComplianceOptions[0],
                        building,
                        t);
                }
            }

            // Apply zone template (note naming is not regular on this, unlike above, hence the ternary op here.
            building.buildingZonesTemplateId = buildingString === "proposed"
                ? clientDefaults.buildingZonesTemplateId
                : clientDefaults.referenceBuildingZonesTemplateId;

            if (clientDefaults.buildingZonesTemplateId != null) {

                let templ = vm.buildingDesignTemplates
                    .find(x => x.buildingDesignTemplateId === clientDefaults.buildingZonesTemplateId);

                if (templ != null) {
                    vm.applyDesignTemplate(templ, building);
                }
            }
        }

        /**
         * Restricts the available inputs based upon the clientOptions argument.
         * @param {any} options
         */
        function restrictAvailableOptions(options) {

            // Restrict building surveyors.
            vm.buildingSurveyorList = vm.allBuildingSurveyorList
                ?.filter(x => options.availableBuildingSurveyorIds?.includes(x.buildingSurveyorId));

            // Don't allow a state where we can't pick ANYTHING - will mean we
            // couldn't continue.
            if (vm.buildingSurveyorList == null || vm.buildingSurveyorList.length == 0)
                vm.buildingSurveyorList = vm.allBuildingSurveyorList;

            // Restrict Compliance Methods
            vm.complianceMethodList = vm.allComplianceMethodList
                ?.filter(x => options.availableComplianceMethodCodes?.includes(x.complianceMethodCode));

            // Restrict Compliance Methods
            vm.certificationList = vm.allCertificationList
                ?.filter(x => options.availableCertifications?.includes(x.certificationId));

            vm.worksDescriptionList = vm.allWorksDescriptionList
                ?.filter(x => options.availableWorksDescriptions?.includes(x.worksDescriptionCode));

            vm.assessmentSoftwareList = vm.allAssessmentSoftwareList;

            // We don't show the QR code selection anymore so we actually do NOT set this here (It will have been set by
            // the clientDefaults earlier)
            // if (vm.type == TYPE_NEWJOB) {
            //    vm.job.currentAssessment.qrCodeEnabled = options.isQRCodeAvailable;
            // }

            // Min house energy rating above doesn't work.
            vm.availableHouseEnergyRatings = options.availableHouseEnergyRatings;
            vm.availableHouseEnergyRatings?.sort((a, b) => a - b);

            // Convert to and from set to remove duplicates if present.
            vm.availableHouseEnergyRatings = [...new Set(vm.availableHouseEnergyRatings)];

            vm.clientOptionsConfigured = true;
        }

        // Note: Technically do not have to do, as selecting a client is mandatory and will set the below values.
        // However, it looks better on the eyes when all fields are shown and dropdown lists are reset when a client is removed
        function resetOptions() {
            //Reset options modifying the field display on the UI
            vm.complianceOptions = [];

            //Reset dropdown lists
            vm.complianceMethodList = vm.allComplianceMethodList;
            vm.assessmentSoftwareList = vm.allAssessmentSoftwareList;
        }

        eventListenerList.push($scope.$on('CreateClient', function(event){
            event.stopPropagation();
            vm.createClient() // function to launch add modal;
            }));

        vm.createClient = function() {
            // Add logic to display create modal form.
        }

        vm.getAssessors = function(searchTerm) {
            var filter = [
                { field: "fullName", operator: "startswith", value: searchTerm, logic: "and" },
                { field: "isExternal", operator: "eq", value: false, valueType: "boolean"}
            ];
            return userservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        vm.projectDescriptionChanged = function (building, newprojectDescription) {

            if (newprojectDescription?.projectDescriptionCode == 'PDOther') {
                building.projectDescriptionOther = building.projectDescriptionPreviousSearchText;
            }

            // Update # storeys with glazing
            if (newprojectDescription != null) {
                vm.floorsChanged(newprojectDescription.numberOfStoreysWithGlazing, building, newprojectDescription.storeys, true)
            }

        }

        //save searchtext so it can be set to the other field if user selects other from the dropdown.
        vm.projectDescriptionSearchChanged = function (item, searchText) {
            item["projectDescriptionPreviousSearchText"] = searchText;
        }

        // Functions to get data for Typeahead
        vm.getProjectDescriptionList = function (searchText, includeOther) {
            return vm.projectDescriptionList;
        }

        vm.clearProjectDescription = function (item) {
            item.projectDescriptionSearchText = "";
            item.projectDescriptionPreviousSearchText = "";
            item.projectDescription = null;
            item.projectDescriptionOther = null;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];

            clearAllTimeouts();
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {

            // Currently these are always modal...
            if (vm.isModal) {
                $mdDialog.cancel();
            }
        }

        vm.save = function (addAnotherJob) {

            vm.isBusy = true;

            if (vm.useDMS) {
                vm.job.jobProjectDetail.latitude = ConvertDMSToDD(vm.dms.latDegrees, vm.dms.latMinutes, vm.dms.latSeconds, vm.dms.latDirection);
                vm.job.jobProjectDetail.longitude = ConvertDMSToDD(vm.dms.lonDegrees, vm.dms.lonMinutes, vm.dms.lonSeconds, vm.dms.lonDirection);
            }

            // Assign assessor to current assessment, as well as job
            if (vm.job.assessorUser) {
                vm.job.currentAssessment.assessorUser = vm.job.assessorUser;
            }

            // Attach any new files to the current job
            vm.job.filesInOrder = vm.attachedDrawings?.map(x => x.attachment);

            metromapservice.uploadMapImageAndStore(vm.selectedMapRawImageBuffer, vm.job.currentAssessment, "mapImageFile", vm.job.jobId)
            .then(() => {
                metromapservice.uploadMapImageAndStore(vm.selectedSiteMapRawImageBuffer, vm.job.currentAssessment, "siteMapImageFile", vm.job.jobId)
                .then(async () => {

                    if (vm.type == TYPE_NEWJOB) {
                         // [THR-498] Set Spaces and Roofs based on selected Building Description
                         vm.job.currentAssessment.allComplianceOptions[0].proposed = {
                             ...vm.job.currentAssessment.allComplianceOptions[0].proposed,
                             ...( await zoneservice.defaultSpacesRoofsForBuilding(vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescriptionCode, vm.job.currentAssessment.allComplianceOptions[0].proposed.storeys) )
                         };
                        saveAsNewJob(addAnotherJob);
                    } else if (vm.type == TYPE_RECERTIFY) {

                        // Save as a recertification
                        await submitForRecertification();

                    } else if (vm.type == TYPE_COPY) {
                        await submitForCopy();
                    }
                });
            });
        }

        function saveAsNewJob(addAnotherJob) {

            if (vm.newRecord == true) {

                vm.job.currentAssessment.allComplianceOptions[0].heatingAndCoolingRulesetCode = 
                    assessmentcomplianceoptionservice.determineInitialHeatingAndCoolingRuleset(
                        vm.job.currentAssessment.allComplianceOptions[0].certification,
                        vm.stateList,
                        vm.job.currentAssessment.assessmentProjectDetail.stateCode,
                        vm.job.client
                    );

                vm.job.currentAssessment.allComplianceOptions[0].sectorDetermination = vm.job.currentAssessment.allComplianceOptions[0].certification.sectorDetermination;

                sequentialguidservice.getGuid().then((guid) => {

                    vm.job.jobId = guid;

                    jobservice.createJob(vm.job).then(function (result) {

                        var assessmentId = result.data;
                        vm.isBusy = false;
                        if (!addAnotherJob) {

                            if (assessmentId != null) {
                                $rootScope.$broadcast('newJob', null);
                                vm.cancel();
                                $state.go("assessment-updateform", { assessmentId: assessmentId });
                            }
                        }
                        else {
                            vm.job = {};
                            resetForm();
                            $scope.jobform.$setPristine();
                            $scope.jobform.$setUntouched();
                            var element = angular.element(document.querySelector("#clientInput"));
                            element.focus();
                        }
                    });
                });
            } else {
                jobservice.updateJob(vm.job).then(function(data){
                    if (data != null) {
                        //vm.job = data;
                        //vm.jobId = vm.job.jobId;
                        //vm.useDMS = false;
                        vm.isBusy = false;
                    }
                });
            }

            $scope.jobform.$setPristine();
        }

        async function submitForRecertification() {

            vm.job.currentAssessment.allComplianceOptions = await stripDataNotRequired(vm.job.currentAssessment.allComplianceOptions);

            // Re-attach drawings in case the above removed.
            if(vm.job.currentAssessment.allComplianceOptions[0].copyDrawings !== true)
                vm.job.filesInOrder = vm.attachedDrawings?.map(x => x.attachment);

            console.log("Option copy data to send to server: ", vm.job.currentAssessment.allComplianceOptions);

            assessmentservice.recertify(vm.job)
                .then(function (data) {
                    vm.isBusy = false;
                    $state.go("assessment-updateform", { assessmentId: data.data });
                    $mdDialog.hide();
                });
        }

        async function submitForCopy() {

            vm.job.currentAssessment.allComplianceOptions = await stripDataNotRequired(vm.job.currentAssessment.allComplianceOptions);

            // Re-attach drawings in case the above removed.
            if(vm.job.currentAssessment.allComplianceOptions[0].copyDrawings !== true)
                vm.job.filesInOrder = vm.attachedDrawings?.map(x => x.attachment);

            console.log(vm.job.currentAssessment.allComplianceOptions[0]);

            jobservice.copyAssessment(vm.job)
                .then(function (data) {
                    vm.isBusy = false;
                    $state.go("assessment-updateform", { assessmentId: data.data });
                    $mdDialog.hide();
                });
        }

        /** 
         * Looks at which data the user wants to copy over from the previous assessment (in terms of compliance options)
         * and strips away anything not wanting to be copied. Note we do NOT need to re-asign GUIDS here. The back end will do that.
         */
        async function stripDataNotRequired(options) {

            // 1. Filter by option wanting to be copied.
            let filtered = options.filter(x => x.doCopy === true);

            // 1. Reassign indexes + which is the baseline.
            for (let i = 0; i < filtered.length; i++) {

                let option = filtered[i];

                option.optionIndex = i;
                option.isBaselineSimulation = i === 0;
                option.isSelected = false;

                // Mmmmkay now we need to apply the required Zone and Construction templates
                // as specified... 

                applyCopyOptionZoneTemplates(option, option.proposed);
                applyCopyOptionZoneTemplates(option, option.reference);

                await applyCopyOptionConstructionTemplate(option, 'proposed', 'construction');
                await applyCopyOptionConstructionTemplate(option, 'reference', 'construction');

                await applyCopyOptionConstructionTemplate(option, 'proposed', 'opening');
                await applyCopyOptionConstructionTemplate(option, 'reference', 'opening');

                await applyCopyOptionServicesTemplate(option, 'proposed');
                await applyCopyOptionServicesTemplate(option, 'reference');

                if (option.copyDrawings === false)
                    option.assessmentDrawings = [];

                if (option.copyAnnualEnergyLoads === false) {

                    option.proposed.heating = null;
                    option.proposed.cooling = null;
                    option.proposed.totalEnergyLoad = null;
                    option.proposed.houseEnergyRating = null;
                    option.proposed.energyUsageSummary = null;
                    option.proposed.heatingOriginal = null;
                    option.proposed.coolingOriginal = null;
                    option.proposed.totalEnergyLoadOriginal = null;
                    option.proposed.houseEnergyRatingOverride = null;
                    option.proposed.overrideEnergyLoads = false;

                    option.reference.heating = null;
                    option.reference.cooling = null;
                    option.reference.totalEnergyLoad = null;
                    option.reference.houseEnergyRating = null;
                    option.reference.energyUsageSummary = null;
                    option.reference.heatingOriginal = null;
                    option.reference.coolingOriginal = null;
                    option.reference.totalEnergyLoadOriginal = null;
                    option.reference.houseEnergyRatingOverride = null;
                    option.reference.overrideEnergyLoads = false;

                }

                if (option.copyAssessmentFiles === false) {

                    option.proposed.fileA        = null;
                    option.proposed.fileB        = null;
                    option.proposed.fileC        = null;
                    option.proposed.fileD        = null;
                    option.proposed.fileE        = null;
                    option.proposed.markupFile   = null;
                    option.proposed.fileAId      = null;
                    option.proposed.fileBId      = null;
                    option.proposed.fileCId      = null;
                    option.proposed.fileDId      = null;
                    option.proposed.fileEId      = null;
                    option.proposed.markupFileId = null;

                    option.reference.fileA        = null;
                    option.reference.fileB        = null;
                    option.reference.fileC        = null;
                    option.reference.fileD        = null;
                    option.reference.fileE        = null;
                    option.reference.markupFile   = null;
                    option.reference.fileAId      = null;
                    option.reference.fileBId      = null;
                    option.reference.fileCId      = null;
                    option.reference.fileDId      = null;
                    option.reference.fileEId      = null;
                    option.reference.markupFileId = null;

                }

                if (option.copyMultiSim === false) {
                    option.ncc2022 = null;
                }

            }

            return filtered;
        }

        function applyCopyOptionZoneTemplates(option, building) {

            const templateId = building.newBuildingZoneTemplateId;

            // If copy, do nothing! Ok as-is
            if (templateId == "COPY" ||
                templateId == null)
                return;
            else if (templateId == "BLANK_TEMPLATE")
                vm.nullifyZoneDefaults(option, building);
            else {

                // Assume we have a guid here.
                const template = vm.buildingDesignTemplates
                    .find(x => x.buildingDesignTemplateId == templateId);

                vm.applyDesignTemplate(template, building);
            }
        }

        async function applyCopyOptionConstructionTemplate(option, building = '', templateType) {

            let templateId = null;

            if (templateType == 'construction')
                templateId = option[building].newConstructionTemplateId;
            else if(templateType == 'opening')
                templateId = option[building].newOpeningTemplateId;

            // If copy, do nothing! Ok as-is
            if (templateId == null || templateId == "COPY")
                return;
            else if (templateId == "BLANK_TEMPLATE")
                await vm.applyConstructionTemplate(
                    option,
                    option[building],
                    { ...vm.blankConstructionTemplate, templateType },
                    building
                );
            else {

                // Assume we have a guid here.
                let template = null;

                if (templateType == 'construction')
                    template = vm.buildingConstructionTemplates.find(x => x.buildingConstructionTemplateId == templateId);
                else if (templateType == 'opening')
                    template = vm.buildingOpeningTemplates.find(x => x.buildingConstructionTemplateId == templateId);

                // sigh FFS. Have to upate this to apply both the construction
                // and opening templates here depending on selections...
                await vm.applyConstructionTemplate(
                      option,
                      option[building],
                      template,
                      building);
            }
        }

        async function applyCopyOptionServicesTemplate(option, building = '') {

            let templateId = option[building].newServicesTemplateId;

            // If copy, do nothing! Ok as-is
            if (templateId == null || templateId === "COPY")
                return;
            else if (templateId === "BLANK_TEMPLATE") {

                await buildingservicestemplateservice.applyTemplate(
                    option,
                    option[building],
                    { ...vm.blankConstructionTemplate }
                );

            } else {

                // Assume we have a guid here.
                let template = null;
                template = vm.buildingServicesTemplates.find(x => x.buildingServicesTemplateId === templateId);

                await buildingservicestemplateservice.applyTemplate(
                    option,
                    option[building],
                    template
                );

            }
        }

        function resetForm() {
            vm.job = angular.copy($scope.originalModel);
            vm.title = "New Job";
            vm.job.currentAssessment.assessmentId = uuid4.generate();
            vm.job.currentAssessment.allComplianceOptions = [defaultBaselineRun()];

            // Set any default values required for a new record.
            sequentialguidservice.getGuid().then(function (data) {
                vm.job.jobId = data;
                vm.jobId = data;
                vm.isBusy = false;
            });

            vm.attachedDrawings = [];

            vm.clearAddress();
        };

        vm.clearAddress = function () {

            vm.job.currentAssessment.assessmentProjectDetail.postcode = null;
            vm.job.currentAssessment.assessmentProjectDetail.suburb = null;
            vm.job.currentAssessment.assessmentProjectDetail.streetName = null;
            vm.job.currentAssessment.assessmentProjectDetail.prefix = null;
            vm.job.currentAssessment.assessmentProjectDetail.houseNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.lotTypeCode = null;
            vm.job.currentAssessment.assessmentProjectDetail.lotType = null;
            vm.job.currentAssessment.assessmentProjectDetail.stateCode = null;
            vm.job.currentAssessment.assessmentProjectDetail.streetType = null;
            vm.job.currentAssessment.assessmentProjectDetail.projectOwner = null;
            vm.job.currentAssessment.assessmentProjectDetail.planType = null;
            vm.job.currentAssessment.assessmentProjectDetail.depositedPlanNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.originalDepositedPlanNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.strataLotNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.surveyStrataLotNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.lotNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.originalLotNumber = null;
            vm.job.currentAssessment.assessmentProjectDetail.volume = null;
            vm.job.currentAssessment.assessmentProjectDetail.folio = null;
            vm.job.currentAssessment.natHERSClimateZoneCode = null;
            vm.job.currentAssessment.natHERSClimateZone = null;
            vm.job.currentAssessment.nccClimateZoneCode = null;
            vm.job.currentAssessment.nccClimateZone = null;
            vm.job.currentAssessment.buildingExposureCode = null;
            vm.job.currentAssessment.bushfireAttackLevelCode = null;
            vm.job.currentAssessment.assessmentProjectDetail.localGovernmentAuthority = null;
            vm.job.currentAssessment.assessmentProjectDetail.latitude = null;
            vm.job.currentAssessment.assessmentProjectDetail.longitude = null;
            vm.job.currentAssessment.isBushFireProne = null;
            vm.job.currentAssessment.bushFireProneUnknown = null;
            vm.job.currentAssessment.bushfireAttackLevelCode = null;
            vm.job.currentAssessment.nccClimateZoneCode = null;
            vm.job.currentAssessment.assessmentProjectDetail.nccClimateZoneCode = null;
            vm.job.currentAssessment.assessmentProjectDetail.boundaryGeometry = null;
            vm.job.currentAssessment.assessmentProjectDetail.boundarySides = null;
            vm.job.currentAssessment.assessmentProjectDetail.useCustomAddress = false;

            vm.job.currentAssessment.assessmentProjectDetail.certificateOfTitle = null;
            vm.job.currentAssessment.assessmentProjectDetail.parcelArea = null;

            vm.job.currentAssessment.assessmentProjectDetail.lotShape = null;
            vm.job.currentAssessment.assessmentProjectDetail.northSideFacing = null;
            vm.job.currentAssessment.assessmentProjectDetail.facingDescription = null;
            vm.job.currentAssessment.assessmentProjectDetail.facingWithinXMetres = null;
            vm.job.currentAssessment.assessmentProjectDetail.cornerBlock = null;

            vm.searchAddressText = '';

            vm.addressChanged();
        }

        vm.resetAssessmentProjectDetailAddress = function (apd) {
            return {
                ...apd,
                postcode: null,
                suburb: null,
                streetName: null,
                prefix: null,
                houseNumber: null,
                lotTypeCode: null,
                stateCode: null,
                streetType: null,
                depositedPlanNumber: null,
                strataLotNumber: null,
                surveyStrataLotNumber: null,
                lotNumber: null,
                originalLotNumber: null,
                volume: null
            };
        }

        vm.delete = function () {
            vm.isBusy = true;
            jobservice.deleteJob(vm.jobId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            jobservice.undoDeleteJob(vm.jobId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        function ConvertDMSToDD(degrees, minutes, seconds, direction) {
            degrees = Number(degrees);
            minutes = Number(minutes);
            seconds = Number(seconds);

            var dd = degrees + minutes / 60 + seconds / (60 * 60);

            if (direction == "S" || direction == "W") {
                dd = dd * -1;
            } // Don't do anything for N or E
            return dd;
        }

        vm.uploadFile = function (files, field, index) {

            for (var i = 0; i < files.length; i++) {
                var $file = files[i];
                uploadFile($file, field)
            }

        }

        function uploadFile($file, field) {
            vm.uploadBusy = true;
            var target = { id: Math.random() };

            vm.attachedDrawings.push(target);
            if ($file == undefined || $file == null || $file.name.split('.').pop() != "pdf") 
                return;

            target[field + "UploadProgress"] = null;

            var url = "../api/Assessment/UploadFile?assessmentId=" + vm.job.currentAssessment.assessmentId;
            url += "&jobId=" + vm.job.jobId;
            url += `&category=Assessment Drawing`
            url += `&classification=Generic`

            var promise = Upload.upload({
                url: url,
                method: "POST",
                file: $file
            });

            promise.progress(function (evt) {
                target[field + "UploadProgress"] = 80 * (evt.loaded / evt.total); // By 80 to allow server time to do stuff.
            }).success(function (data, status, headers, config) {

                target[field] = data;
                target[field + "UploadProgress"] = null;
                vm.uploadBusy = false;

            }).error(function (data, status, headers, config) {
                vm.uploadBusy = false;
            });
        }

        vm.downloadFile = function (fileDto) {
            var a = document.createElement('a');
            a.href = fileDto.url;
            a.setAttribute('download', fileDto.fileName);
            a.target = '_blank';
            a.click();
        }

        vm.deleteFile = async function (item) {

            if (item?.attachment == null)
                return;

            // Delete file on server and remove from shown drawings list.
            fileservice.deleteFile(item.attachment.fileId).then(() => {
                const indexOfDeleted = vm.attachedDrawings.indexOf(item);
                vm.attachedDrawings.splice(indexOfDeleted, 1);
             });
        }

        vm.cancelJob = function () {
            modalDialog.confirmationDialog("Confirm", "Please confirm you want to Cancel this job.", null, null, null, null, null, true).then(function() {
                vm.isBusy = true;
                jobservice.cancelJob(vm.jobId).then(function (result) {
                    vm.isBusy = false;
                    if (result != null && result.data.statusCode == "JCancelled") {
                        vm.cancel();
                    }
                });
            });
        }

        vm.handleAttachmentReorder = function() {
            // Do nothing...
        }

        function complianceMethodChanged() {
            updateAvailableSoftware();
        }
        vm.complianceMethodChanged = complianceMethodChanged;

        async function applyConstructionTemplate(option, building, template, buildingType) {

            if(template == null)
                return;

            await constructionservice.applyTemplate(option, building, template, buildingType);
        }
        vm.applyConstructionTemplate = applyConstructionTemplate;

        vm.clearConstructionTemplate = function (building) {

            if (vm.job.currentAssessment.allComplianceOptions[0][building] == null)
                vm.job.currentAssessment.allComplianceOptions[0][building] = {};

            let bld = vm.job.currentAssessment.allComplianceOptions[0][building];

            vm.job.currentAssessment.allComplianceOptions[0][building].constructionTemplateId = null;
            vm.job.currentAssessment.allComplianceOptions[0][building].constructionTemplateTitle = null;
            bld.surfaces = null;
            bld.openings = null;
            bld.services = null;
        }

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.cancelJob,
                name: 'Cancel',
                desc: 'Cancel',
                icon: 'fa fa-ban',
                roles: ['actions__assessment_cancel'],
            });

        }

        setActionButtons();

        vm.clientJobNumberChanged = async function () {

            // Ensure client job number is unique.
            var available = await assessmentservice
                .checkJobNumberIsAvailable(
                    vm.job.currentAssessment.assessmentProjectDetail.clientJobNumber, 
                    vm.job.currentAssessment.assessmentProjectDetail.customClientName, 
                    vm.job.client.clientId);

            if(!available) {
                modalDialog.infoDialog(
                    "Client Job Number already exists. ", 
                    "If you require recertification of an existing assessment, please order the recertification via the Jobs menu. If you are ordering a new assessment, please enter a unique Client Job Number.",
                    "",
                    "Ok", 
                    null);

                vm.job.currentAssessment.assessmentProjectDetail.clientJobNumber = null;
            }
        }

        // Fired when user makes custom additions to the address - so we don't want to do a search or anything, just update
        // our display address with the details we have.
        vm.addressChanged = function () {
            if (vm.job.currentAssessment && vm.job.currentAssessment.assessmentProjectDetail) {
                vm.job.currentAssessment.assessmentProjectDetail.fullAddress = assessmentservice.getFullAddress(vm.job.currentAssessment.assessmentProjectDetail);
                vm.job.currentAssessment.assessmentProjectDetail.originalDisplayAddress = vm.job.currentAssessment.assessmentProjectDetail.fullAddress;
            }
        }

        // Select suburb
        vm.selectedSuburb = null;
        vm.suburbChanged = function () {
            addressservice.setSuburbToAssessment(
                vm.job.currentAssessment,
                vm.selectedSuburb,
                () => vm.captureMapImagesAtCurrentAddress(true)
            );
        }

        // Address updates via search address should trigger capturing map data among other things.
        vm.captureMapImagesAtCurrentAddress = async function (isSuburbSearch = false) {

            vm.showLotNumberInput = vm.job.currentAssessment.assessmentProjectDetail.lotNumber == null;

            if (vm.job.currentAssessment && vm.job.currentAssessment.assessmentProjectDetail) {
                vm.job.currentAssessment.assessmentProjectDetail.fullAddress = assessmentservice.getFullAddress(vm.job.currentAssessment.assessmentProjectDetail);
                vm.job.currentAssessment.assessmentProjectDetail.originalDisplayAddress = vm.job.currentAssessment.assessmentProjectDetail.fullAddress.replace(', ', '\n');
            }

            // Don't check bushfire prone if gps coords are null.
            if(vm.job.currentAssessment.assessmentProjectDetail.latitude != null && 
                vm.job.currentAssessment.assessmentProjectDetail.longitude != null) {

                bushfireproneservice.checkIfBushFireProne(vm.job.currentAssessment.assessmentProjectDetail.latitude, vm.job.currentAssessment.assessmentProjectDetail.longitude)
                    .then(async isProne => {
                        let stateHasData = await bushfirestatedataservice.stateHasBushfireData(vm.job.currentAssessment.assessmentProjectDetail.stateCode);
                        // IF is prone, means we do have data and 'IsProne' was found
                        if (isProne) {
                            vm.job.currentAssessment.bushFireProneUnknown = false;
                            vm.job.currentAssessment.isBushFireProne = true;
                        // ELSE if have data, means 'IsProne' is false
                        } else if (stateHasData) {
                            vm.job.currentAssessment.bushFireProneUnknown = false;
                            vm.job.currentAssessment.isBushFireProne = false;
                            vm.job.currentAssessment.bushfireAttackLevelCode = "BAL-NotApp";
                        // ELSE we don't have data for this state, so trigger 'unknown' state
                        } else {
                            vm.job.currentAssessment.bushFireProneUnknown = true;
                            vm.job.currentAssessment.isBushFireProne = null;
                            vm.job.currentAssessment.bushfireAttackLevelCode = null;
                        }
                    });

                nccclimatezonedataservice.getClimateZone(
                        vm.job.currentAssessment.assessmentProjectDetail.latitude,
                        vm.job.currentAssessment.assessmentProjectDetail.longitude)
                    .then((climateZone) => {
                        vm.job.currentAssessment.nccClimateZoneCode = `NCC${climateZone}`
                        vm.job.currentAssessment.nccClimateZone = vm.nccClimateZoneList.find(x => x.nccClimateZoneCode === vm.job.currentAssessment.nccClimateZoneCode);
                    });
            }

            vm.job.currentAssessment.assessmentProjectDetail.coordinatesAreConfirmed = true;
            vm.job.currentAssessment.includeMapInExportedPDF = true;

            releaseMapImage();

            let hasMetroMapData = await metromapservice.isDataAvailableAtCoordinates({
                lat: vm.job.currentAssessment.assessmentProjectDetail.latitude,
                lng: vm.job.currentAssessment.assessmentProjectDetail.longitude
            });

            let captureMap = hasMetroMapData
                ? "metro"
                : "satellite";

            let maxZoom = hasMetroMapData && vm.job.currentAssessment.assessmentProjectDetail.boundaryGeometry
                ? null
                : 18;

            // Take a full-sized snapshot for maximum fidelity booyah
            vm.selectedMapRawImageBuffer = await metromapservice.takeFullscreenSnapshot(
                document.getElementById(remoteMapElem),
                {
                    lat: vm.job.currentAssessment.assessmentProjectDetail.latitude,
                    lng: vm.job.currentAssessment.assessmentProjectDetail.longitude
                },
                vm.job.currentAssessment.assessmentProjectDetail.boundaryGeometry,
                captureMap, 
                true, 
                maxZoom
            );

            vm.selectedSiteMapRawImageBuffer = await metromapservice.takeFullscreenSnapshot(
                document.getElementById(remoteSiteMapElem),
                {
                    lat: vm.job.currentAssessment.assessmentProjectDetail.latitude,
                    lng: vm.job.currentAssessment.assessmentProjectDetail.longitude
                },
                vm.job.currentAssessment.assessmentProjectDetail.boundaryGeometry,
                'street',
                isSuburbSearch, 
                siteInfoZoomLevel
            );

        }

        vm.isBushFireProneChange = function () {
            if (!vm.job.currentAssessment.isBushFireProne) {
                vm.job.currentAssessment.bushfireAttackLevelCode = "BAL-NotApp";
            }
        }

        // Removes any references to previously held map imagery.
        function releaseMapImage() {
            vm.selectedMapRawImageBuffer = null;
            vm.selectedSiteMapRawImageBuffer = null;
            vm.job.currentAssessment.assessmentProjectDetail.mapImageFileId = null;
            vm.job.currentAssessment.assessmentProjectDetail.mapImageFile = null;
        }

        vm.useCustomClientNameChanged = function (useCustomClientName) {
            if (useCustomClientName === false) {
                vm.job.currentAssessment.assessmentProjectDetail.customClientName = null;
                vm.clientJobNumberChanged();
            }
        }

        var mapImageBackup = null;
        vm.useCustomAddressChanged = function () {

            if(vm.job.currentAssessment.assessmentProjectDetail.useCustomAddress)
                vm.showLotNumberInput = true;
            else
                vm.showLotNumberInput = vm.job.currentAssessment.assessmentProjectDetail.lotNumber == null;

            // NOTE: At this point we are not discarding images or coordinates or anything when custom address is ticked
            // as the custom address is, AFAIK, more about modifying formatting of the address rather than actually changing the address.
            if (vm.job.currentAssessment.assessmentProjectDetail.useCustomAddress) {
                vm.job.currentAssessment.assessmentProjectDetail.customDisplayAddress = vm.job.currentAssessment.assessmentProjectDetail.originalDisplayAddress;

                // mapImageBackup = vm.selectedMapRawImageBuffer;
                // vm.selectedMapRawImageBuffer = null;

            } else {
                vm.job.currentAssessment.assessmentProjectDetail.customDisplayAddress = null;
                // vm.selectedMapRawImageBuffer = mapImageBackup;
            }
        }

        // To prevent logic conflicts certain items in the 'select fields to copy' need to be disabled based on
        // information entered prior in the form.
        vm.checkIfDisabled = function (item) {

            if (vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode 
                != vm.baseAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode) {

                if (item.id == "COPY_BASELINE_OUTCOMES" || item.id == "COPY_SIM_OPTIONS")
                    return true;
            }

            return false;
        }

        function clearAllTimeouts() {
            if (pendingPdfConverRecordsTimeout) {
                clearTimeout(pendingPdfConverRecordsTimeout);
                pendingPdfConverRecordsTimeout = null;
            }
        }

        vm.formatActualMinHouseEnergyRating = function () {
            vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating = parseFloat(vm.tempMinHouseEnergyRating);
        }

        /** Returns true/false depending on whether or not we should allow inputs for design fields. */
        vm.disableDesignInputs = function () {
            return vm.job.currentAssessment.allComplianceOptions[0].proposed.buildingZonesTemplateId != null ||
                vm.job.currentAssessment.allComplianceOptions[0].proposed.buildingZonesTemplateId == "BLANK_TEMPLATE"; // ||
        }

        /**
         *  Applies the given Zone Template. If no building is passed, it will default
         *  to the baseline proposed building.
         */
        vm.applyDesignTemplate = function (template, building = null) {

            // This may be hit if e.g. client default is set to a now-deleted template.
            if(template == null)
                return; 

            if (building == null) {
                throw "No, you need to set this now!!!";
            }

            building.buildingZonesTemplateId = template.buildingDesignTemplateId;
            building.zones = angular.copy(template.zones);
            // Make sure new job has set defaults, otherwise use template values
            if (vm.type != TYPE_NEWJOB) {
                building.spaces = angular.copy(template.spaces);
            }

            // This has to be set explicitly as only some come from templates now.
            building.zoneTypesNotApplicable.interior = template.zoneTypesNotApplicable.interior;
            building.zoneTypesNotApplicable.roofspace = template.zoneTypesNotApplicable.roofspace;
            building.zoneTypesNotApplicable.subfloorspace = template.zoneTypesNotApplicable.subfloorspace;
            building.zoneTypesNotApplicable.groundsurface = template.zoneTypesNotApplicable.groundsurface;
            // building.zoneTypesNotApplicable.floorPlanSpaces = true; // These 2 not affected by templates.
            // building.zoneTypesNotApplicable.generalroofs = true;

            building.buildingOrientation = template.buildingOrientation;

            building.projectClassification = template.projectClassification;
            // building.projectDescription = template.projectDescription;
            // building.projectDescriptionOther = template.projectDescriptionOther;

            building.designFeatures = template.designFeatures;
            building.design = template.design;
            building.designWasBlankFromTemplate = template.design == null;

            building.garageLocation = template.garageLocation;
            building.buildingWidth = template.buildingWidth;
            building.buildingLength = template.buildingLength;
            building.buildingPerimeter = template.buildingPerimeter;
            building.roofArea = template.roofArea;

            building.storeys = template.storeys;

            // Re-assign GUIDs
            building.zones.forEach(x => {
                x.zoneId = uuid4.generate();
                x.createdOn = new Date().toUTCString();
                x.assessmentComplianceBuildingId = building.assessmentComplianceBuildingId
            });
        }

        /** Nullify Zone data, generally used when selecting the "BLANK_TEMPLATE" */
        vm.nullifyZoneDefaults = function (option = null, building = null, applyDefaults = false) {

            if (option == null)
                option = vm.job.currentAssessment.allComplianceOptions[0];

            if (building == null)
                building = option.proposed;

            // Instead of just nullifying these, reset to client default if they exist.
            if (vm.currentClientDefault != null && applyDefaults) {

                const data = vm.currentClientDefault;

                building.buildingZonesTemplateId = data.buildingZonesTemplateId;
                building.zones = data.zones;

                // building.projectDescription = data.projectDescription;
                // building.projectDescriptionOther = data.projectDescriptionOther;
                building.projectClassification = data.projectClassification;
                building.designFeatures = data.designFeatures;
                building.design = data.design;
                building.designWasBlankFromTemplate = data.design == null;

                building.lowestLivingAreaFloorType = data.lowestLivingAreaFloorType;
                building.storeys = data.storeys;
                vm.floors = data.storeys?.length;

            } else {

                building.buildingZonesTemplateId = null;
                building.zones = [];

                // building.projectClassification = null;
                // building.projectDescription = null;
                building.projectDescriptionOther = null;
                building.designFeatures = null;
                //building.design = null; // [THR-509] Want to keep House Type in Recertification.
                building.storeys = null;
                vm.floors = 1;
            }

        }

        vm.bushfireAttackLevelList = [];
        var bushfireAttackLevelPromise = bushfireattacklevelservice.getList()
            .then(function (data) {
                vm.bushfireAttackLevelList = data.data;
            });

        function optionCopyClicked(option) {
            setTimeout(() => { // garbage wait so doCopy value is correct.

                // TODO: Alistair may provide "default" values for these fields
                // which change depending on whether this is a recertification
                // or a copy etc.
                if (option.doCopy) {

                    option.proposed.newBuildingZoneTemplateId = "COPY";
                    option.reference.newBuildingZoneTemplateId = "COPY";

                    option.proposed.newConstructionTemplateId = "COPY";
                    option.reference.newConstructionTemplateId = "COPY";

                    option.proposed.newOpeningTemplateId = "COPY";
                    option.reference.newOpeningTemplateId = "COPY";

                    option.proposed.newServicesTemplateId = "COPY";
                    option.reference.newServicesTemplateId = "COPY";

                    option.copyDrawings = true;
                    option.copySimulationResults = true;
                } else {
                    //delete option.optionBackup;
                    //delete option.proposedBuildingBackup;
                    //delete option.referenceBuildingBackup;
                }

            }, 20);
        }
        vm.optionCopyClicked = optionCopyClicked;

        async function applyClientTemplateDefaultsForRecert(option) {

            const defaults = await clientdefaultservice.getClientDefault(vm.job.client.clientId);

            option.proposed.newBuildingZoneTemplateId = defaults.buildingZonesTemplateId;
            option.reference.newBuildingZoneTemplateId = defaults.referenceBuildingZonesTemplateId;

            option.proposed.newConstructionTemplateId = defaults.proposedConstructionTemplateId;
            option.reference.newConstructionTemplateId = defaults.referenceConstructionTemplateId;

            option.proposed.newOpeningTemplateId = defaults.proposedOpeningTemplateId;
            option.reference.newOpeningTemplateId = defaults.referenceOpeningTemplateId;

            option.proposed.newServicesTemplateId = defaults.proposedServicesTemplateId;
            option.reference.newServicesTemplateId = defaults.referenceServicesTemplateId;

        }

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.job?.currentAssessment?.worksDescription?.worksDescriptionCode);
        }

        /** 
         * Returns a compliance option which is suitable as a default baseline run for new jobs
         * as well as copy/recerts where the user does not wish to copy anything.
         * */
        function defaultBaselineRun(complianceMethodCode = null) {

            // // TODO: I think we need to be able to override storeys here too...? What else??
            // if (complianceMethodCode == null)
            //     "CMPerfSolution"

            const complianceMethod = vm.allComplianceMethodList?.find(x => x.complianceMethodCode === complianceMethodCode);

            return {

                description: "Baseline specifications",
                assessmentId: vm.job.currentAssessment.assessmentId,
                complianceOptionsId: uuid4.generate(),
                complianceMethodCode: complianceMethodCode,
                complianceMethod: complianceMethod,
                optionIndex: 0,
                isBaselineSimulation: true,
                isSelected: true,
                isCompliant: false,
                isComplianceValid: true,
                isLocked: false,
                updatedDrawingsRequired: null,
                newPurchaseOrderRequired: false,
                markupFileRequired: false,

                assessmentDrawings: [],

                proposed: {
                    services: [],
                    openings: [],
                    surfaces: [],
                    categoriesWithExternalData: {},
                    classification: null,
                    zones: [],
                    storeys: [],
                    categoriesNotRequired: {},
                    zoneTypesNotApplicable: { floorPlanSpaces: true, generalroofs: true }
                },

                reference: {
                    services: [],
                    openings: [],
                    surfaces: [],
                    categoriesWithExternalData: {},
                    classification: null,
                    zones: [],
                    storeys: [],
                    categoriesNotRequired: {},
                    zoneTypesNotApplicable: { floorPlanSpaces: true, generalroofs: true },
                }
            }
        }
    }
})();