(function () {
    // The bushFireProneUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'bushFireProneUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'bushfireproneservice', 'daterangehelper', bushFireProneUpdateController]);
function bushFireProneUpdateController($rootScope, $scope, $mdDialog, bushfireproneservice, daterangehelper) {

    var vm = this;

    vm.state = "";
    vm.processing = false;

    vm.datasetUrl = "https://direct-download.slip.wa.gov.au/datadownload/Bush_Fire_Prone_Areas/Bush_Fire_Prone_Areas_2021_OBRM_019_WA_GDA94_Public_Geopackage.zip";

    vm.processNewDataset = function () {

        if(vm.datasetUrl == null || vm.datasetUrl == "")
            return;

        vm.processing = true;
        vm.state = "Attempting to process new dataset...";

        try {

            bushfireproneservice.processDataset(vm.datasetUrl).then(data => {
                console.log(data);
                vm.state = data;
                vm.processing = false;
            });

        } catch (e) {
            vm.state = e;
            vm.processing = false;
        }

    }

}
})();