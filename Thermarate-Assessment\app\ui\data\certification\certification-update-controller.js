(function () {
    // The CertificationUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'CertificationUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams',
            '$state', 'certificationservice', 'manufacturerservice', 'uuid4', 'security', certificationUpdateController]);
    function certificationUpdateController($rootScope, $scope, $mdDialog, $stateParams,
        $state, certificationservice, manufacturerservice, uuid4, securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Certification';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;

        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        vm.certification = {
            certificationId: uuid4.generate(),
            title: null,
            description: null
        };

        vm.manufacturers = [];
        let manufacturerPromise = manufacturerservice.getList()
            .then(data => { vm.manufacturers = data.data; });

        vm.rulesets = [];
        let rulesetPromise = certificationservice.getRulesets()
            .then(data => { 
                vm.rulesets = data; 
                console.log(data);
            });

        vm.enabledRulesets = () => vm.rulesets?.filter(x => x.rulesetCode !== "Disabled" && 
                                                            x.rulesetCode !== "Inherit" &&
                                                            x.rulesetCode !== "Enabled");

        vm.sectors = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => { 
                vm.sectors = data;
                console.log(data);
            });

        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Certification";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.certificationId = $scope.certificationId;
            }
            vm.hideActionBar = true;
        } else {
            vm.certificationId = $stateParams.certificationId;
        }

        // Get data for object to display on page
        var certificationCodePromise = null;
        if (vm.certificationId != null) {
            certificationCodePromise = certificationservice.getCertification(vm.certificationId)
            .then(function (data) {
                if (data != null) {
                    vm.certification = data;
                    console.log('vm.certification:', vm.certification);
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("certification-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                certificationservice.createCertification(vm.certification).then(function(data){
                    vm.certification = data;
                    vm.certificationId = vm.certification.certificationId;
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                certificationservice.updateCertification(vm.certification).then(function(data){
                    if (data != null) {
                        vm.certification = data;
                        vm.certificationId = vm.certification.certificationId;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            certificationservice.deleteCertification(vm.certificationId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            certificationservice.undoDeleteCertification(vm.certificationId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();