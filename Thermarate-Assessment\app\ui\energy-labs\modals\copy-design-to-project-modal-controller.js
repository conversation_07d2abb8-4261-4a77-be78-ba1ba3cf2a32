(function () {
    'use strict';
    var controllerId = 'CopyDesignToProjectModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'projectservice', copyDesignToProjectModalController]);
    function copyDesignToProjectModalController(common, $scope, $mdDialog, projectservice) {

        let vm = this;
        vm.isBusy = true;

        var thisProjectId = $scope.thisProjectId;
        var clientId = $scope.clientId;

        projectservice.getForClient(clientId, false).then(
            result => {
                if (result == null)
                    return;
                vm.projectList = result;
                vm.projectList = vm.projectList.filter(x => x.projectId != thisProjectId);
                vm.isBusy = false;
            },
            error => {
                console.log(error);
                vm.isBusy = false
            }
        );

        vm.selectProject = function (project) {
            vm.projectList.forEach(x => x.selected = false);
            project.selected = true;
            vm.selectedProjectId = project.projectId;
        }

        vm.confirm = function () {
            $mdDialog.hide(vm.selectedProjectId);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();