// Name: bushfireattacklevelservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'bushfireattacklevelservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', bushfireattacklevelservice]);

    function bushfireattacklevelservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'bushfireattacklevel/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getBushfireAttackLevel: getBushfireAttackLevel,
            createBushfireAttackLevel: createBushfireAttackLevel,
            updateBushfireAttackLevel: updateBushfireAttackLevel,
            deleteBushfireAttackLevel:deleteBushfireAttackLevel,
            undoDeleteBushfireAttackLevel:undoDeleteBushfireAttackLevel,
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting BushfireAttackLevel list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getBushfireAttackLevel(bushfireAttackLevelCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {bushfireAttackLevelCode: bushfireAttackLevelCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting BushfireAttackLevel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createBushfireAttackLevel(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Bushfire Attack Level Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created BushfireAttackLevel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateBushfireAttackLevel(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Bushfire Attack Level Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating BushfireAttackLevel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteBushfireAttackLevel(bushfireAttackLevelCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { bushfireAttackLevelCode: bushfireAttackLevelCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting BushfireAttackLevel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteBushfireAttackLevel(bushfireAttackLevelCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { bushfireAttackLevelCode: bushfireAttackLevelCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for BushfireAttackLevel: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
