<form id="bushfireprone-form"
         class="main-content-wrapper"
         data-ng-controller="bushFireProneUpdateCtrl as vm">

    <md-card style="margin: 30px 0; padding: 20px 20px;">
        <h1>
            Bushfire Prone Dataset
        </h1>
        <div style="width: 1000px">

            <div>
                The current "Bushfire Prone" dataset is obtained through <a href="https://catalogue.data.wa.gov.au/dataset/?q=Bush+Fire+Prone">LandGate.</a>
            </div>
            </br>
            <div>
                New versions are released sporadically (every year or 2+). As the resource for the Bushfire Prone dataset is not at a fixed
                location (unlike the Address datasets, for instance), the process cannot yet be fully automated. 
            </div>
            </br>
            <div>
                To update the dataset, enter the required url to the new Geopackage URL (GDA94) here and click the 
                process button. For reference, the 2021 URL was </br> 
                <span style="color: orange;">
                    'https://direct-download.slip.wa.gov.au/datadownload/Bush_Fire_Prone_Areas/Bush_Fire_Prone_Areas_2021_OBRM_019_WA_GDA94_Public_Geopackage.zip'
                </span>
            </div>
            </br>
            <div>
                <strong>This can take up to 30 minutes. Do not run this at the same time as updating the address dataset!</strong>
            </div>
            </br>

            <div>

                <input type="text"
                       style="display: block; width: 99%; margin: 16px 0; padding: 6px;"
                       ng-model="vm.datasetUrl"/>
                <md-button ng-click="vm.processNewDataset()"
                           class="md-raised md-warn"
                           ng-disabled="vm.processing || vm.datasetUrl == null">
                    Process New Dataset
                </md-button>
            </div>
            </br>
            <div>
                <span style="font-weight: bold;">
                    {{vm.state}}
                </span>
            </div>

        </div>
    </md-card>

</form>
