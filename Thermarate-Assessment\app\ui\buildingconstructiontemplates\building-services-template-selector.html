<!-- Services Template Selection -->
<!-- Reuseable template selection controller with required logic. Probably going to require a LOT of ng-ifs... -->
<md-input-container class="md-block vertically-condensed kindly-remove-error-spacer "
                    flex="100">
    <label>{{vm.label()}}</label>
    <div layout="row">
        <md-select name="template"
                   flex="100"
                   ng-model="vm.building.servicesTemplateId"
                   ng-disabled="vm.disabled"
                   ng-required="vm.required">
            <md-option ng-value="template.buildingServicesTemplateId"
                       ng-repeat="template in vm.templateList"
                       ng-click="vm.applyTemplate(vm.option, vm.building, template);">
                {{template.templateName}}
            </md-option>
            <md-option ng-value="'NO_DEFAULT'"
                       ng-click="vm.nullifyTemplateData(vm.option, vm.building, 'NO_DEFAULT')"
                       ng-if="vm.isTemplate">
                No Default
            </md-option>
            <md-option ng-value="'COPY'"
                       ng-show="vm.showCopyPrevious">
                Copy from Previous Assessment
            </md-option>
            <!-- 
                This is the BLANK option which is shown from the new job window when we WANT to show this
                as the selection even AFTER it has been clicked.
            -->
            <md-option ng-if="vm.newJob == true"
                       ng-value="'BLANK_TEMPLATE'"
                       ng-click="vm.nullifyTemplateData(vm.option, vm.building, 'BLANK_TEMPLATE')">
                Blank Services Template
            </md-option>
            <!-- 
                This is the BLANK option which is shown from the ASSESSMENT and all other windows when
                we just want to selection to show as 'null' / 'empty' after selecting this.
            -->
            <md-option ng-if="vm.newJob == false"
                       ng-value="'NOT_IN_USE'"
                       ng-click="vm.nullifyTemplateData(vm.option, vm.building, null)">
                Blank Services Template
            </md-option>
            <md-option ng-value="'MODIFIED'"
                       ng-show="false">
                Modified Template
            </md-option>
        </md-select>

    </div>
</md-input-container>
