 <!-- Storey List -->
<div class="shadow-z-1" ng-form="storeyListForm"
     style="display: inline-block;">
    <table class="table table-striped table-hover table-condensed">
        <thead>
            <tr>
                <th class="text-left" style="width: 80px; white-space:nowrap;">
                    Floor
                </th>
                <th class="text-left" style="width: 200px;white-space: nowrap;">
                    Storey Name
                </th>
                <th class="text-right" style="width: 90px; white-space:nowrap;">
                    Height Above Ground (m)
                </th>
                <th class="text-right" style="width: 90px; white-space:nowrap;">
                    Floor Area (m&sup2;)
                </th>
                <th class="text-right" style="width: 90px; white-space:nowrap;">
                    Ceiling Area (m&sup2;)
                </th>
                <th class="more-actions-icon-column" style="white-space:nowrap;">
                </th>
            </tr>

        </thead>
        <tbody>

            <tr ng-repeat="storey in vm.source.storeys track by storey.name">

                <!-- Floor -->
                <td data-title="Floor">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer"
                                        ng-class="{'input-black' : !vm.disabled }">
                        <input type="text"
                               name="floor{{$index}}"
                               ng-model="storey.floor"
                               disabled
                               required />
                    </md-input-container>
                </td>

                <!-- Storey Name -->
                <td data-title="Storey Name">
                    <md-input-container flex="100"
                                        class="md-block vertically-condensed kindly-remove-error-spacer"
                                        ng-class="{'input-black' : !vm.disabled }">
                        <input type="text"
                               auto-grow
                               name="StoreyName{{$index}}"
                               ng-model="storey.name"
                               required
                               ng-disabled="vm.disabled" />
                    </md-input-container>
                </td>

                <!-- Height Above Ground -->
                <td data-title="Height Above Ground"
                    class="text-right">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer"
                                        ng-class="{'input-black' : !vm.disabled}">
                        <input type="text"
                               name="HeightOffGround{{$index}}"
                               ng-model="storey.heightOffGround"
                               ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                               formatted-number
                               decimals="2"
                               ng-disabled="vm.disabled" />
                    </md-input-container>
                </td>

                <!-- Floor Area -->
                <td data-title="Floor Area"
                    class="text-right">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                        <input type="text"
                               name="FloorArea{{$index}}"
                               ng-value="storey.floorArea.toFixed(2)"
                               disabled />
                    </md-input-container>
                </td>

                <!-- Ceiling Area -->
                <td data-title="Ceiling Area"
                    class="text-right">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                        <input type="text"
                               name="CeilingArea{{$index}}"
                               ng-value="storey.ceilingArea.toFixed(2)"
                               disabled />
                    </md-input-container>
                </td>

                <!-- Actions -->
                <td>
                    <div layout="row"
                         layout-align="center center">

                        <!-- 'More' button w/ Popup -->
                        <md-menu ng-show="!vm.disabled">

                            <!-- Initial '...' button, which launches options -->
                            <img md-menu-origin
                                 class="clickable"
                                 ng-click="$mdOpenMenu()"
                                 src="/content/feather/more-horizontal.svg"
                                 ng-disabled="vm.disabled"/>
                            <md-menu-content>

                                <md-menu-item>
                                    <md-button ng-click="vm.copyStorey(storey)"
                                               ng-disabled="vm.disabled">
                                        Duplicate
                                    </md-button>
                                </md-menu-item>
                                <md-menu-divider></md-menu-divider>
                                <md-menu-item>
                                    <md-button ng-click="vm.deleteStorey(storey)"
                                               ng-disabled="vm.disabled">
                                        <span style="color: orangered;">Delete</span>
                                    </md-button>
                                </md-menu-item>

                            </md-menu-content>
                        </md-menu>
                    </div>
                </td>
            </tr>

        </tbody>
    </table>

    <div layout="row"
         style="padding: 10px 2px;">

        <md-button class="md-raised md-primary"
                   ng-click="vm.addStorey()"
                   ng-show="!vm.disabledEx()"
                   ng-disabled="vm.disabled">
            Add
        </md-button>
    </div>

</div>
