<form id="epw-form"
         class="main-content-wrapper"
         data-ng-controller="epwUpdateController as vm">

    <md-card style="padding: 20px 20px;">
        <h1>
            Energy Plus Weather Update
        </h1>
        <div>

            <div flex="50">
                <div style="margin: 20px 0;">
                    To process a new .EPW dataset click the button below and select a .ZIP file containing all .EPW files for ALL climate zones.
                    All previous data is erased and replaced with the new dataset, so it is important to ensure you have data for all climate zones
                    within the .ZIP file you upload.
                </div>

                <div style="margin: 20px 0;">
                    After processing, you may download the dataset as a set of Excel files using the 'Download Archive' button. 
                    Downloading the excel archive could take quite some time where it will appear like nothing is happening (~2-5 minutes).
                    Do not navigate away from this page after clicking the download button.
                </div>
            </div>

            <div style="margin: 20px 0;">
                <md-button class="md-raised md-warn"
                           ngf-select="vm.processNewDataset($file)"
                           redi-allow-roles="['settings__uploaddatasets__view']"
                           ng-disabled="vm.processing">
                    Upload Zip
                </md-button>

                <!--
                    NOTE: I was unable to find a way to display the 'save file' dialogue
                    using a function and regular $http request, so this frankenstein is
                    in place for the moment, but it's not ideal (esp. since we can't
                    disable and re-enable the button).
                -->
                <a ng-if="!vm.processing"
                   href="{{vm.excelArchiveUrl()}}"
                   download="EPWExcelFiles.zip">
                    <md-button class="md-raised md-warn"
                               ng-disabled="vm.processing"
                               ng-click="vm.processing = true;">
                        Download Archive
                    </md-button>
                </a>
                <md-button ng-if="vm.processing"
                           class="md-raised md-warn"
                           disabled>
                    Download Archive
                </md-button>
            </div>
        </div>
    </md-card>

</form>
