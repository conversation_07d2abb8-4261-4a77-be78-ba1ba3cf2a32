<form name="notificationRuleForm"
      class="main-content-wrapper"
      style="width: 600px;"
      ng-style="{ 'width' : vm.isModal == true ? '900px' : ''}"
      novalidate
      data-ng-controller='NotificationRuleUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.newRecord ? 'New Notification Rule' : 'Edit Notification Rule'}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
    </div>

    <!-- Rules -->
    <md-card ng-if="vm.initialised">
        <md-card-header>
            <h2 style="margin: 0px;">Rules</h2>
        </md-card-header>
        <md-card-content>

            <fieldset redi-enable-roles="settings__settings__edit">

                <!-- Title -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Title</label>
                    <input type="text" 
                           name="title"
                           ng-model="vm.notificationRule.title"
                           md-maxlength="100"
                           ng-required="true" />
                    <div ng-messages="notificationRuleForm.title.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                    </div>
                </md-input-container>

                <!-- Description -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Description</label>
                    <input type="text" name="description"
                           ng-model="vm.notificationRule.description"
                           md-maxlength="500"/>
                    <div ng-messages="notificationRuleForm.description.$error">
                        <div ng-message="md-maxlength">Too many characters entered, max length is 500.</div>
                    </div>
                </md-input-container>

                <!-- Initial Status -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>Initial Status</label>
                    <md-select ng-required="false"
                               name="initialStatus"
                               ng-model="vm.notificationRule.initialStatus"
                               ng-model-options="{trackBy: '$value.statusCode'}">
                        <md-option ng-value="null">
                            Any
                        </md-option>
                        <md-option ng-value="status"
                                   ng-repeat="status in vm.statusList">
                            {{status.description}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- New Status -->
                <md-input-container class="md-block" flex-gt-sm>
                    <label>New Status</label>
                    <md-select ng-required="true"
                               name="newStatus"
                               ng-model="vm.notificationRule.newStatus"
                               ng-model-options="{trackBy: '$value.statusCode'}">
                        <md-option ng-value="status"
                                   ng-repeat="status in vm.statusList">
                            {{status.description}}
                        </md-option>
                    </md-select>
                </md-input-container>

                <!-- Clients -->
                <md-input-container class="md-block" flex-gt-sm style="margin-top:-16px;">
                    <span style="font-size:9px; color:rgba(0,0,0,0.54);">Clients</span>
                    <select-all-multi-select
                        all-options="vm.clientList"
                        return-selections="vm.notificationRule.clientSelectionsList"
                        primary-key="'clientId'"
                        display-key="'clientName'">
                    </select-all-multi-select>
                </md-input-container>

                <!-- Enabled -->
                <div>
                    <label style="font-size: 9px;">Enabled</label>
                    <md-switch ng-model="vm.notificationRule.enabled"
                               style="width: fit-content;">
                        <span ng-show="vm.notificationRule.enabled === false">Notification is disabled.</span>
                        <span ng-show="vm.notificationRule.enabled === true">Notification is enabled.</span>
                    </md-switch>
                </div>

            </fieldset>

            <div class="col-md-12" ng-if="vm.newRecord==false">
                <div rd-display-created-modified ng-model="vm.notificationRule"></div>
            </div>
        </md-card-content>
    </md-card>

    <!-- Recipients -->
    <md-card ng-if="vm.initialised">
        <md-card-header>
            <h2 style="margin: 0px;">Recipients and Templates</h2>
        </md-card-header>

        <!-- Properties for Recipients -->
        <md-card-content >

            <fieldset redi-enable-roles="settings__settings__edit">

                <div style="border-radius: 3px; border: 2px solid #59814923; padding: 1.5rem; margin: 0 0.6rem 2rem 0.6rem;" class="clickable"
                     ng-click="vm.variableNotesExpanded = !vm.variableNotesExpanded">
                    <span style="font-size: 16px; font-weight: bold;">Notes&nbsp;</span>
                    <i ng-if="vm.variableNotesExpanded === false"
                       class="fa fa-caret-up" />
                    <i ng-if="vm.variableNotesExpanded === true"
                       class="fa fa-caret-down" />

                    <div ng-show="vm.variableNotesExpanded === true" style="margin-top: 1rem;">

                        The following variables are available to use within emails. They must be encased in double { brackets.
                        Full details of the scripting language can be found at <a href="https://github.com/scriban/scriban">Github</a>.
                        <ul>
                            <li><b>admin_name</b> - Simply "Admin".</li>
                            <li><b>recipient_name</b> - The name of the person receiving this email.</li>
                            <li><b>assessor_name</b> - The name of the assessor assigned to the job/assessment.</li>
                            <li><b>assessor_last_name</b> - The last name of the assessor assigned to the job/assessment.</li>
                            <li><b>assignee_name</b> - The name of the client assignee.</li>
                            <li><b>assignee_last_name</b> - The last name of the client assignee.</li>
                            <li><b>client_name</b> - The business name of the client.</li>
                            <li><b>project_address</b> - The physical address of the job/assessment.</li>
                            <li><b>project_owner</b> - The full name/s of the owner/s of the job/assessment.</li>
                            <li><b>assessment_url</b> - The URL of the assessment on the ADMIN site. <b>Do not send to clients.</b></li>
                            <li><b>assessment_client_url</b> - the URL of the assessment on the CLIENT site. Safe to send to internal accounts if required.</li>
                            <li><b>client_job_number</b> - The client job number (As shown on client portal). </li>
                            <li><b>works_description</b> - The works description </li>
                            <li><b>assessment_version</b> - The current assessment version. </li>
                            <li><b>priority</b> - The priority assigned to the assessment. </li>
                            <li><b>local_government_authority</b> - Local government authority that the address falls under. </li>
                            <li><b>google_maps_url</b> - The raw url of the assessments location. Must be wrapped in an <a> tag to appear in the email as a link. </li>
                            <li><b>building_description</b> - The building description of the selected options proposed building. </li>
                            <li><b>project_classification</b> - The project classification of the selected options proposed building.</li>
                        </ul>

                        The following variables are an an object or an array of objects containing compliance option data.
                        <ul>
                            <li><b>options</b> -  List of ALL compliance options (including the baseline).</li>
                            <li><b>valid_options</b> - List of all VALID compliance options.</li>
                            <li><b>baseline_option</b> - The baseline compliance.</li>
                            <li><b>selected_option</b> - The currently selected compliance option.</li>
                        </ul>

                        The following variables are available on the <b>compliance options</b> (obtained through the above properties).
                        <ul>
                            <li><b>option_index</b> - The 'number' of the option.</li>
                            <li><b>assessment_method</b> - Assessment method for the specific option.</li>
                            <li><b>description</b> - Description of the option.</li>
                            <li><b>updated_drawings_required</b> - True/false value indicating whether updated drawings are required.</li>
                            <li><b>valid</b> - True/false value indicating whether the compliance option is valid.</li>
                            <li><b>is_baseline</b> - True/false value indicating whether this is the baseline.</li>
                            <li><b>is_selected</b> - True/false value indicating whether this compliance option is selected.</li>
                        </ul>
                    </div>
                </div>

                <div ng-repeat="template in vm.notificationTemplates track by $index">

                    <md-card>
                        <md-card-header style="margin-top: 5px;">

                            <!-- Recipient Type -->
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed"
                                                style="margin: 0; width: 300px;">
                                <label>Recipient Type</label>
                                <md-select ng-required="true"
                                           class="vertically-condensed-ex kindly-remove-error-spacer"
                                           style="font-size: 18px; margin: 0;"
                                           name="newStatus"
                                           ng-model="template.notificationRecipientTypeCode">
                                    <md-option ng-value="recipientType.notificationRecipientTypeCode"
                                               ng-repeat="recipientType in vm.recipientTypeList">
                                        {{recipientType.notificationRecipientTypeCode}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <span style="margin-left:auto; display: inline-block;" class="clickable"
                                  ng-click="template.isExpanded = !(template.isExpanded !== false)">
                                <i ng-if="template.isExpanded === false"
                                   class="fa fa-caret-up" />
                                <i ng-if="template.isExpanded !== false"
                                   class="fa fa-caret-down" />
                            </span>

                        </md-card-header>
                        <md-card-content ng-show="template.isExpanded !== false">

                            <h3 style="margin-top: 0;">Email Template</h3>

                            <!-- Email Subject -->
                            <md-input-container style="width: 100%;">
                                <label>Subject</label>
                                <input type="text" 
                                       style="width: 100%;"
                                       ng-model="template.subject"
                                       ng-required="true" />
                            </md-input-container>

                            <!-- HTML Template -->
                            <div>
                                <label>HTML Template</label>
                                <textarea class="assessment-notes-input"
                                          style="width: 99%; min-height: 200px;"
                                          ng-model="template.emailHtml"
                                          ng-required="true"></textarea>
                            </div>

                            <!-- Plain Text Template -->
                            <div style="margin-top: 1rem;">
                                <label>Plain Text Template</label>
                                <textarea class="assessment-notes-input"
                                          style="width: 99%; min-height: 200px;"
                                          ng-model="template.emailPlainText"
                                          ng-required="true"></textarea>
                            </div>

                            <md-button class="md-raised md-warn"
                                       style="margin: 1.5rem 0 0.5rem 1rem; margin-left: auto; float: right;"
                                       ng-click="vm.deleteTemplate(template)">
                                DELETE TEMPLATE
                            </md-button>

                        </md-card-content>
                    </md-card>

                </div>

                <md-button class="md-raised md-primary"
                           ng-click="vm.addTemplate()">
                    ADD TEMPLATE
                </md-button>

            </fieldset>

        </md-card-content>

    </md-card>

    <div ng-if="vm.initialised"
         data-cc-widget-button-bar
         data-is-modal="vm.isModal">
        <div data-ng-show="vm.isBusy"
             data-cc-spinner="vm.spinnerOptions"></div>
        <md-button class="md-raised md-primary"
                   ng-disabled="notificationRuleForm.$invalid || vm.permissions.settingsEdit == false"
                   ng-show="vm.notificationRule.deleted != true"
                   ng-click="vm.save()">
            Save
        </md-button>
        <md-button class="md-raised md-warn"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.notificationRule.deleted!=true"
                   ng-confirm-click="vm.delete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to delete this record.">
            Delete
        </md-button>
        <md-button class="md-raised"
                   redi-enable-roles="settings__settings__delete"
                   ng-show="vm.notificationRule.deleted == true"
                   ng-confirm-click="vm.undoDelete()"
                   ng-confirm-condition="true"
                   ng-confirm-message="Please confirm you want to RESTORE this record.">
            Restore
        </md-button>
        <md-button class="md-raised"
                   ng-click="vm.cancel()">
            Cancel
        </md-button>
        <div class="clearfix"></div>
    </div>

</form>       
