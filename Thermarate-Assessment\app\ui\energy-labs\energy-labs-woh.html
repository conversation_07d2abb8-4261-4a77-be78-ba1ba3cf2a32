<div data-ng-controller="EnergyLabsWohController as vm"
     style="margin-top: -20px;"
     class="el-poppins">

  <div class="el-heading-banner"
       style="height: 350px; padding-top: 30px;">
    <h1 style="margin: 0;">Whole-of-Home</h1>
    <h3>
      Measure the energy usage of the home's services and appliances.
    </h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>
  </div>
  <hr class="el-title-divider"/>

  <div class="navigation-text" ng-show="vm.project!=null">
    <div ng-click="vm.backToProjects()" class="clickable">Projects</div>
    <div>></div>
    <div ui-sref="energy-labs({projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}} [{{vm.project.clientName}}]</div>
    <div>></div>
    <b>Whole-of-Home</b>
  </div>

  <div class="select-model-main-container">

    <standard-model-filters ng-if="vm.project != null && vm.mode === 'select' && vm.initialised"
                            current-list="vm.modelList"
                            applied-filters="vm.filterData"
                            settings="vm.project.energyLabsSettings"
                            filter-count-data="vm.filterCountData"
                            on-filter-changed="vm.updateFilteredList"
                            on-sort-changed="vm.updateSort"
                            total-items-without-filters="vm.totalWithoutFilters"
                            current-total="vm.currentTotal">
    </standard-model-filters>

    <!-- Grid of all available designs -->
    <div ng-if="vm.mode === 'select'"
         class="el-option-grid">

      <!-- Card -->
      <div ng-repeat="parentModel in vm.modelList"
           class="el-card clickable"
           ng-click="vm.select(parentModel)">

        <!-- Header -->
        <div class="el-card-header" style="position:relative;">
            <!-- Title -->
            <div class="el-title">{{parentModel.title}}</div>
            <!-- Compare Icon -->
            <div style="position:absolute; right:12px; top:12px; user-select:none;"
                 ng-style="{
                    'cursor': vm.anyCompareSlotsLeft()||parentModel.selectedForCompare ? 'pointer' : 'default',
                    'opacity': !vm.anyCompareSlotsLeft()&&!parentModel.selectedForCompare ? 0.55 : 1
                 }"
                 ng-click="vm.selectModelForCompare(parentModel); $event.stopPropagation()">
                <div style="position:relative; display:flex;">
                    <!-- Layout Icon -->
                    <img src="/content/images/layout.svg" style="margin:auto; width:28px; height:auto;" />
                    <!-- Tick Icon -->
                    <img ng-if="parentModel.selectedForCompare" src="/content/images/tick-white.svg" style="position:absolute; top:-5px; right:7px; width:10px; height:auto; border-radius:50%; padding:5px 4px; background-color:#848484;" />
                </div>
                <div style="color:#848484;">Compare</div>
                <md-tooltip ng-if="!vm.anyCompareSlotsLeft()&&!parentModel.selectedForCompare" class="dropdown-tooltip" md-direction="top" md-delay="vm.tooltipDelay">
                    You can only compare up to 3 home designs at a time.<br/>Please remove one of your selections to continue. <div class="dropdown-tooltip-triangle" />
                </md-tooltip>
            </div>
        </div>

        <!-- Switcher -->
        <div class="el-card-body padded">
            <home-plan-options-switcher
                style="width:100%;"
                the-parent-model="parentModel"
                show-label="true"
                view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)">
            </home-plan-options-switcher>
        </div>

      </div>

      <!-- Bottom Tray -->
      <div ng-if="vm.anySelectedForCompare()" class="tray-container">

        <!-- Expand/Collapse Button -->
        <div id="expand-button" class="expand-button" ng-click="vm.trayExpanded = !vm.trayExpanded;">
            <!-- Layout Icon -->
            <img class="layout-icon" src="/content/images/layout.svg" />
            <!-- Text -->
            <div class="expand-button-text">Compare (<span style="width:10px; display:inline-block">{{vm.numSelectedForCompare()}}</span>)</div>
            <!-- Arrow Icon -->
            <img class="arrow-icon" src="/content/images/arrow-right.png" ng-class="{'tray-expanded': vm.trayExpanded}" />
        </div>

        <!-- Tray -->
        <div id="tray" class="tray" ng-class="{'tray-expanded': vm.trayExpanded}">
            <div>
                <!-- Select Text -->
                <div class="select-text">Select up to three designs to compare.</div>
                <!-- Selected -->
                <div class="selected-tiles-container">
                    <div ng-repeat="selectedModel in vm.modelList | filter: { selectedForCompare: true }" class="selected-tile">
                        <div class="selected-tile-title">{{selectedModel.title}}</div>
                        <img class="selected-tile-remove-icon" src="/content/images/cross.png" ng-click="selectedModel.selectedForCompare = false;" />
                    </div>
                </div>
            </div>
            <div class="right-buttons-container">
                <!-- Clear Button -->
                <div class="clear-button"
                     ng-click="vm.clearCompareSelections()">
                    CLEAR
                </div>
                <!-- Compare Button -->
                <div class="compare-button" ng-click="vm.startCompare()">
                    COMPARE
                </div>
            </div>
        </div>

        <!-- White strip at bottom to hide shadow -->
        <div class="white-strip" />

      </div>

    </div>

    <!-- Selected Design + Comparison Cards -->
    <div ng-if="vm.mode === 'configure'">

      <div class="central-grid-container" style="margin-bottom: 85px;">
        <button ng-click="vm.reset()"
                class="el-calculate-button">
          START OVER <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

      <div ng-if="vm.viewingModels.length > 0"
           class="el-option-flex"
           ng-form="vm.energyLabsForm">

        <div ng-repeat="parentModel in vm.viewingModels track by parentModel.uiRenderId"
             style="{{vm.useGridDisplay() === true ? '' : vm.viewingModels.length == 3 ? 'width: 550px;' : 'width: 800px;' }}">

          <div>

            <!-- Data Input Card -->
            <div class="el-card {{'model-card-' + $index}}">
              <div class="el-card-header with-actions">
                <span><!-- Empty --></span>
                <div class="el-title">{{parentModel.title}}</div>

                <md-menu ng-show="!vm.disabled"
                         style="justify-self: right;">

                  <!-- Initial '...' button, which launches options -->
                  <img md-menu-origin
                       class="clickable el-menu-launch"
                       ng-click="$mdOpenMenu()"
                       src="/content/feather/more-horizontal.svg"
                       ng-disabled="vm.disabled"/>
                  <md-menu-content>

                    <!-- Compare side by side / Add Comparison -->
                    <md-menu-item ng-show="vm.viewingModels.length < 3">
                      <md-button ng-click="vm.addCompare(vm.viewingModels, $index);vm.clearResults();">
                        <span>Add to comparison</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Remove comparison -->
                    <md-menu-item ng-show="vm.viewingModels.length > 1">
                      <md-button ng-click="vm.removeCompare(vm.viewingModels, $index);vm.clearResults();">
                        <span>Remove from comparison</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Copy from/to -->
                    <md-menu-item ng-repeat="option in vm.viewingModels | filter: { include: true } track by $index"
                                  ng-show="option != parentModel"
                                  ng-mouseover="vm.copyFromHighlightEffect($index, true)"
                                  ng-mouseleave="vm.copyFromHighlightEffect($index, false)">
                      <md-button ng-click="vm.copyFromTo(option, parentModel)">
                        <span>Copy from {{option.title}}</span>
                      </md-button>
                    </md-menu-item>

                    <!-- Restore Defaults -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuildingToDefaults(parentModel.selectedVariation);">
                        <span>Restore Defaults</span>
                      </md-button>
                    </md-menu-item>

                    <md-menu-divider></md-menu-divider>

                    <!-- Clear -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuilding(parentModel.selectedVariation);">
                        <span style="color: orangered;">Clear</span>
                      </md-button>
                    </md-menu-item>

                  </md-menu-content>
                </md-menu>
              </div>

              <div class="el-card-body padded">

                <!-- Options + Drawings -->
                <home-plan-options-switcher
                    style="width:100%;"
                    the-parent-model="parentModel"
                    get-full-variation="true"
                    show-label="true"
                    view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                    on-model-changed="vm.variationChanged(parentModel)"
                    option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)"
                    viewing-models-count="vm.viewingModels.length">
                </home-plan-options-switcher>

                <div ng-if="vm.mode == 'configure'" style="display:block; justify-self:stretch; margin-top:38px;">

                  <!-- Block Data -->
                  <standard-model-block ng-if="!vm.project.lockWOHLocation"
                                        the-model="parentModel.selectedVariation"
                                        variable-options="parentModel.selectedVariation.variableOptions"
                                        configuration="woh"
                                        project="vm.project"
                                        required="true"
                                        disabled="true"
                                        on-data-changed="vm.clearResults()"
                                        copy-across-enabled="vm.viewingModels.length > 1"
                                        copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                        copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)"
                                        climate-zone-is-invalid="parentModel.selectedVariation.modelBlockShowError">
                  </standard-model-block>

                  <!-- Very custom WoH inputs -->

                  <div class="el-section-title">
                    <img class="el-section-icon"
                         src="content/images/energy-labs/el-plug-icon.svg"
                         alt="Icon of cartoon houses divided into their land blocks">
                    Services
                  </div>

                  <div>
                    <!-- Heating System -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Heating System</label>
                        <md-select md-container-class="md-select-show-all"
                                   ng-required="true"
                                   ng-model="parentModel.selectedVariation.spaceHeating.serviceType"
                                   ng-model-options="{trackBy: '$value.serviceTypeCode'}">
                          <div class="custom-dropdown-option" ng-repeat="v in vm.serviceTypesGrouped['SpaceHeatingSystem']">
                              <md-option ng-value="v"
                                         ng-click="vm.clearResults()">
                                  {{v.title}}
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'spaceHeating', 'serviceType', v);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                      <!-- Energy Rating (GEMS 2019) -->
                      <md-input-container ng-if="vm.showEnergyRating(parentModel.selectedVariation.spaceHeating)"
                                          class="md-block vertically-condensed">
                        <label>{{parentModel.selectedVariation.spaceHeating.serviceType.serviceTypeCode == 'HeatPumpDucted' || parentModel.selectedVariation.spaceHeating.serviceType.serviceTypeCode == 'HeatPumpNonDucted'
                                    ? 'Heating System Energy Rating (GEMS 2019)'
                                    : 'Heating System Energy Rating'}}</label>
                        <input ng-model="parentModel.selectedVariation.spaceHeating.gems2019Rating"
                               formatted-number
                               decimals="1"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                    </div>

                    <!-- Cooling System -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Cooling System</label>
                        <md-select ng-model="parentModel.selectedVariation.spaceCooling.serviceType"
                                   ng-required="true"
                                   ng-model-options="{trackBy: '$value.serviceTypeCode'}"
                                   md-container-class="md-select-show-all">
                          <div class="custom-dropdown-option" ng-repeat="v in vm.serviceTypesGrouped['SpaceCoolingSystem']">
                              <md-option ng-value="v"
                                         ng-click="vm.clearResults()">
                                  {{v.title}}
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'spaceCooling', 'serviceType', v);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                      <!-- Energy Rating (GEMS 2019) -->
                      <md-input-container ng-if="vm.showEnergyRating(parentModel.selectedVariation.spaceCooling)"
                                          class="md-block vertically-condensed">
                        <label>{{parentModel.selectedVariation.spaceCooling.serviceType.serviceTypeCode == 'HeatPumpDucted' || parentModel.selectedVariation.spaceCooling.serviceType.serviceTypeCode == 'HeatPumpNonDucted'
                                    ? 'Cooling System Energy Rating (GEMS 2019)'
                                    : 'Cooling System Energy Rating'}}</label>
                        <input ng-model="parentModel.selectedVariation.spaceCooling.gems2019Rating"
                               formatted-number
                               decimals="1"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                    </div>

                    <!-- Water Heater Type -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Water Heater Type</label>
                        <md-select ng-model="parentModel.selectedVariation.waterHeating.serviceType"
                                   ng-model-options="{trackBy: '$value.serviceTypeCode'}"
                                   ng-required="true"
                                   md-container-class="md-select-show-all">
                          <div class="custom-dropdown-option" ng-repeat="v in vm.serviceTypesGrouped['HotWaterSystem']">
                              <md-option ng-value="v"
                                         ng-click="vm.clearResults()">
                                  {{v.title}}
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'waterHeating', 'serviceType', v);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                    </div>

                    <!-- Swimming Pool -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Swimming Pool</label>
                        <md-select ng-model="parentModel.selectedVariation.swimmingPool.exists"
                                   ng-required="true">
                          <div class="custom-dropdown-option">
                              <md-option ng-value="true"
                                         ng-click="vm.clearResults()">
                                Yes
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'swimmingPool', 'exists', true);"><img src="/content/images/share.png"/></div>
                          </div>
                          <div class="custom-dropdown-option">
                              <md-option ng-value="false"
                                         ng-click="vm.clearResults()">
                                No
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'swimmingPool', 'exists', false);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                      <!-- Swimming Pool Volume (L) -->
                      <md-input-container ng-if="parentModel.selectedVariation.swimmingPool.exists === true"
                                          class="md-block vertically-condensed">
                        <label>Swimming Pool Volume (L)</label>
                        <input ng-model="parentModel.selectedVariation.swimmingPool.volume"
                               formatted-number
                               decimals="0"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                      <!-- Pool Pump Energy Rating -->
                      <md-input-container ng-if="parentModel.selectedVariation.swimmingPool.exists === true"
                                          class="md-block vertically-condensed">
                        <label>Pool Pump Energy Rating</label>
                        <input ng-model="parentModel.selectedVariation.swimmingPool.gems2019Rating"
                               formatted-number
                               decimals="1"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                    </div>

                    <!-- Spa -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Spa</label>
                        <md-select ng-model="parentModel.selectedVariation.spa.exists"
                                   ng-required="true">
                          <div class="custom-dropdown-option">
                              <md-option ng-value="true"
                                         ng-click="vm.clearResults()">
                                Yes
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'spa', 'exists', true);"><img src="/content/images/share.png"/></div>
                          </div>
                          <div class="custom-dropdown-option">
                              <md-option ng-value="false"
                                         ng-click="vm.clearResults()">
                                No
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'spa', 'exists', false);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                      <!-- Spa Volume (L) -->
                      <md-input-container ng-if="parentModel.selectedVariation.spa.exists === true"
                                          class="md-block vertically-condensed">
                        <label>Spa Volume (L)</label>
                        <input ng-model="parentModel.selectedVariation.spa.volume"
                               formatted-number
                               decimals="0"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                      <!-- Spa Pump Energy Rating -->
                      <md-input-container ng-if="parentModel.selectedVariation.spa.exists === true"
                                          class="md-block vertically-condensed">
                        <label>Spa Pump Energy Rating</label>
                        <input ng-model="parentModel.selectedVariation.spa.gems2019Rating"
                               formatted-number
                               decimals="1"
                               ng-required="true"
                               ng-blur="vm.clearResults()" />
                      </md-input-container>

                    </div>

                    <!-- Photovoltaic (PV) System -->
                    <div>
                      <md-input-container class="md-block vertically-condensed">
                        <label>Photovoltaic (PV) System</label>
                        <md-select ng-model="parentModel.selectedVariation.photovoltaic.exists"
                                   ng-required="true">
                          <div class="custom-dropdown-option">
                              <md-option ng-value="true"
                                         ng-click="vm.clearResults()">
                                Yes
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'photovoltaic', 'exists', true);"><img src="/content/images/share.png"/></div>
                          </div>
                          <div class="custom-dropdown-option">
                              <md-option ng-value="false"
                                         ng-click="vm.clearResults()">
                                No
                              </md-option>
                              <div ng-if="vm.viewingModels.length > 1" class="copy-across-button" ng-click="vm.copyServiceOptionAcross($event, 'photovoltaic', 'exists', false);"><img src="/content/images/share.png"/></div>
                          </div>
                        </md-select>
                      </md-input-container>

                      <!-- Photovoltaic (PV) System Capacity (kW) -->
                      <md-input-container ng-if="parentModel.selectedVariation.photovoltaic.exists === true"
                                          class="md-block vertically-condensed">
                        <label>Photovoltaic (PV) System Capacity (kW)</label>
                        <input ng-model="parentModel.selectedVariation.photovoltaic.capacity"
                               formatted-number
                               decimals="2"
                               ng-required="true"
                               ng-blur="vm.clearResults()"/>
                      </md-input-container>

                    </div>
                  </div>
                </div>

              </div>
            </div>

            <div ng-show="vm.showResults"
                 class="el-card v-margin">

              <div style="margin: 2rem 2rem 2rem 2rem;">

                <!-- Performance Heading -->
                <div class="el-section-title">
                  <img class="el-section-icon"
                       src="content/images/energy-labs/el-performance-icon.svg"
                       alt="Icon of a home design which is stamped or has awards">
                  Performance
                </div>

                <div style="padding: 0 2rem 2rem 2rem; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; justify-content: space-between; justify-items: center;">

                  <!-- Energy Usage (kW) -->
                  <div class="el-performance-result {{vm.viewingModels.length == 3 ? 'smaller-text' : ''}}">
                    <label>Energy Usage (kW)</label>
                    <span>{{parentModel.selectedVariation.calculations.achieved.toFixed(1)}}</span>
                  </div>

                  <!-- Allowance -->
                  <div class="el-performance-result">
                    <label>Allowance (kW)</label>
                    <span>{{parentModel.selectedVariation.calculations.allowance.toFixed(1)}}</span>
                  </div>

                  <!-- Difference -->
                  <div class="el-performance-result">
                    <label>Difference (kW)</label>
                    <span>{{parentModel.selectedVariation.calculations.difference.toFixed(1)}}</span>
                  </div>

                  <!-- Rating -->
                  <div class="el-performance-result">
                    <label>Outcome</label>
                    <span ng-style="{'color': parentModel.selectedVariation.calculations.achieved <= parentModel.selectedVariation.calculations.allowance ? 'var(--thermarate-green)' : 'red'}">
                      {{parentModel.selectedVariation.passed ? "Pass" : "Fail"}}
                    </span>
                  </div>

                </div>

                <div ng-show="parentModel.selectedVariation.calculations.outcome != null && !parentModel.selectedVariation.passed"
                     id="el-woh-outcome-performance_{{$index}}"
                     class="el-woh-outcome-bg">

                  <!-- Final Outcome -->
                  <md-input-container class="md-block vertically-condensed"
                                      style="grid-area: center; width: 100%; justify-self: start; align-self: start;">
                              <textarea type="text"
                                        class="el-woh-outcomes-text"
                                        ng-style="{ 'min-height' : vm.passed ? '100px' : '178px' }"
                                        ng-model="parentModel.selectedVariation.calculations.outcome"
                                        disabled>
                              </textarea>
                  </md-input-container>

                </div>

              </div>

            </div>

          </div>

        </div>
      </div>

      <div ng-show="vm.showResults === false"
           class="central-grid-container"
           style="margin-top: 5rem;">
        <button ng-click="vm.runCalc()"
                ng-disabled="vm.energyLabsForm.$invalid || vm.isBusy || vm.anySuburbsInvalid"
                class="el-calculate-button">
          CALCULATE <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

    </div>

  </div>

</div>

<style>
    /* Tooltip */
    .dropdown-tooltip {
        overflow: visible;
    }

        .dropdown-tooltip > div {
            position: relative;
            width: max-content;
            min-width: 100px;
            height: max-content;
            padding: 5px 14px 4px 14px;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
            box-shadow: 0 0 10px 0 rgba(0,0,0,.25);
            background-color: #fafafa;
            color: black;
            opacity: 1 !important;
            overflow: visible;
        }

    .dropdown-tooltip-triangle {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translate(-50%, 100%);
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 5px solid #fafafa;
    }

    /* Tray Container */
    .tray-container {
        z-index: 999;
        position: relative;
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 1730px;
        max-width: 100vw;
        box-sizing: border-box;
        pointer-events: none;
    }

    /* Expand/Collapse Button */
    .expand-button {
        z-index: -1;
        position: relative;
        margin-left: 175px;
        width: 220px;
        padding: 10px 0;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        outline: none;
        background-color: #fafafa;
        box-shadow: 0 0 10px 0 rgba(0,0,0,.25);
        cursor: pointer;
        user-select: none;
        pointer-events: all;
    }

        .expand-button.bounce {
            animation: bounce 1.4s ease;
        }

    @keyframes bounce {
        30% {
            transform: translateY(0%);
        }

        40% {
            transform: translateY(15%);
        }

        50% {
            transform: translateY(0%);
        }

        60% {
            transform: translateY(13%);
        }

        70% {
            transform: translateY(0%);
        }

        80% {
            transform: translateY(11%);
        }

        90% {
            transform: translateY(0);
        }
    }

    /* Layout Icon */
    .layout-icon {
        width: 32px;
        height: auto;
    }

    /* Text */
    .expand-button-text {
        margin-top: 2px;
        white-space: nowrap;
        text-align: center;
        font-size: 17px
    }

    /* Arrow Icon */
    .arrow-icon {
        margin-left: 3px;
        width: 18px;
        height: auto;
        transform: rotate(-90deg);
    }

        .arrow-icon.tray-expanded {
            transform: rotate(90deg);
        }

    /* Tray */
    .tray {
        width: 100%;
        height: 0;
        border-top: 1px solid #e0e2e8;
        padding: 0 calc(30px + 4%);
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        background-color: #fafafa;
        pointer-events: all;
        transition: height 0.5s;
    }

        .tray.tray-expanded {
            height: 140px;
        }

    /* Select Text */
    .select-text {
        margin-bottom: 20px;
        font-size: 14px;
    }

    /* Selected Tiles Container */
    .selected-tiles-container {
        display: flex;
        align-items: center;
        column-gap: 20px;
    }

    /* Selected Tile */
    .selected-tile {
        width: max-content;
        padding: 4px 10px;
        display: flex;
        align-items: center;
        column-gap: 10px;
        border-radius: 3px;
        border: 1px solid #e0e2e8;
        font-size: 14px;
    }

    /* Title */
    .selected-tile-title {
        margin-top: 2px;
    }

    /* Remove Icon */
    .selected-tile-remove-icon {
        width: 12px;
        height: auto;
        cursor: pointer;
    }

    /* Right Buttons Container */
    .right-buttons-container {
        display: flex;
        align-items: center;
        column-gap: 20px;
    }

    /* Clear Button */
    .clear-button {
        padding: 12px 18px;
        border-radius: 10px;
        border: 1px solid #e0e2e8;
        background-color: #EAEAEA;
        font-weight: bold;
        font-size: 13px;
        cursor: pointer;
        user-select: none;
    }

        .clear-button:hover {
            background-color: #D9D9D9;
            color: white;
        }

    /* Compare Button */
    .compare-button {
        padding: 12px 18px;
        border-radius: 10px;
        background-color: rgb(139, 195, 74);
        font-size: 13px;
        font-weight: bold;
        color: white;
        cursor: pointer;
        user-select: none;
    }

        .compare-button:hover {
            background-color: rgb(166 222 100);
        }

    /* White strip at bottom to hide shadow */
    .white-strip {
        z-index: 999;
        position: absolute;
        height: 3px;
        width: 220px;
        top: 51px;
        left: 175px;
        background-color: #fafafa;
    }

    .el-woh-outcome-bg {

        --woh-green-background: rgba(133,186,56, 0.2);
        --woh-red-background: rgba(225, 94, 37, 0.2);
        --woh-grey-background: rgba(180, 180, 180, 0.1);

        padding: 15px 20px 0 20px;
        background-color: var(--woh-grey-background);
        border-radius: 10px;

        display: grid;
        grid-template-areas: "center";
        align-items: center;
        justify-items: center;
    }

    .el-woh-outcomes-text {

        font-size: 14px;
        /*min-height: 250px !important; */
        color: black !important;
        background-color: transparent;
        border: none !important;
        outline: none;
        line-height: 125% !important;

        grid-area: center;
    }
</style>
