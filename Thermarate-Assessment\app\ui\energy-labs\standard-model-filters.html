<div class="el-filter-grid">

    <!-- Filters -->
    <div ng-repeat="filter in vm.filters track by filter.field"
         class="el-filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">
        <!-- Label -->
        <label class="el-filter-label">{{filter.name}}</label>
        <!-- Clear Button -->
        <img src="/content/images/cross.png"
             class="el-filter-clear-button"
             ng-click="vm.clearFilter(filter);$event.stopPropagation()"
        />
        <!-- Normal Field -->
        <md-input-container ng-if="!['features', 'categories'].includes(filter.field)"
                            class="el-filter-dropdown md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex md-input-focused">
            <md-select name="{{filter.field}}"
                       class="vertically-condensed vertically-condensed-ex"
                       style="margin-bottom: 6px"
                       ng-required="false"
                       multiple="true"
                       md-selected-text="vm.filterText(filter.field)"
                       ng-change="vm.filterChanged()"
                       ng-model="vm.appliedFilters[filter.field]">
                <md-option>
                    Any
                </md-option>
                <md-option ng-show="vm.filterCountData[filter.field][option] > 0"
                           ng-repeat="option in vm.settings.distinctDesignOptions[filter.field] track by $index"
                           ng-value="option">
                    {{option}} ({{vm.filterCountData[filter.field][option]}})
                </md-option>
            </md-select>
        </md-input-container>
        <!-- Features -->
        <md-input-container ng-if="filter.field == 'features'"
                            class="el-filter-dropdown md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex md-input-focused">
            <md-select name="features"
                       style="margin-bottom: 6px"
                       ng-required="false"
                       multiple="true"
                       md-selected-text="vm.filterText('features')"
                       ng-change="vm.filterChanged()"
                       ng-model="vm.appliedFilters['features']">
                <md-option>
                    Any
                </md-option>
                <div ng-repeat="section in vm.featuresSections" ng-show="section.features.length > 0">
                    <hr ng-if="vm.featureSectionHasItems(section)" class="md-select-divider" />
                    <md-option ng-repeat="option in section.features track by $index"
                               ng-show="vm.filterCountData['features'][option] > 0"
                               ng-value="option">
                        {{vm.featureName(option)}} ({{vm.filterCountData['features'][option]}})
                    </md-option>
                </div>
            </md-select>
        </md-input-container>
        <!-- Categories -->
        <md-input-container ng-if="filter.field == 'categories'"
                            class="el-filter-dropdown md-block kindly-remove-error-spacer vertically-condensed vertically-condensed-ex md-input-focused">
            <md-select name="categories"
                        style="margin-bottom: 6px"
                        ng-required="false"
                        multiple="true"
                        md-selected-text="vm.filterText('categories')"
                        ng-change="vm.filterChanged()"
                        ng-model="vm.appliedFilters['categories']">
                <md-option>
                    Any
                </md-option>
                <hr class="md-select-divider" ng-show="vm.categories.length > 0" />
                <md-option ng-show="vm.filterCountData['categories'][option] > 0"
                           ng-repeat="option in vm.categories track by $index"
                           ng-value="option">
                    {{vm.featureName(option)}} ({{vm.filterCountData['categories'][option]}})
                </md-option>
            </md-select>
        </md-input-container>
    </div>

</div>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: -8px; color: #7b7b7b; font-size: 12px;">
    <!-- Number of Items -->
    <div style="margin-left: 20px; margin-top: -2px; display: flex;" ng-show="!vm.filtersApplied">
        Showing {{vm.totalItemsWithoutFilters}} home designs
    </div>
    <div style="margin-left: 20px; margin-top: -2px; display: flex;" ng-show="vm.filtersApplied">
        {{vm.currentTotal != null ? vm.currentTotal : '-'}} out of {{vm.totalItemsWithoutFilters}} home designs match your filters (<div ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</div>)
    </div>
    <!-- Sort -->
    <sort-list-dropdown class="energy-labs-sort"
                        sort-by-options="vm.sortByOptions"
                        sort-by-selection="vm.sort"
                        default-sort="vm.defaultSort"
                        apply-sort="vm.applySort()"></sort-list-dropdown>
</div>

<style>

    .el-filter-grid {
        width: 1691px;
        margin: 6px;
        margin-bottom: 20px;
        margin-left: 18px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        column-gap: 2rem;
        row-gap: 2rem;
    }

    .el-filter {
        position: relative;
        border-radius: 10px;
        padding: 12px 10px;
    }

    .el-filter-label {
        margin-left: 12px !important;
        font-size: 12px;
    }

    .el-filter-dropdown {
        margin-top: 10px !important;
        background-color: #eeeeee;
        padding: 10px 10px 6px 10px;
    }

    .md-select-placeholder {
        color: black !important;
    }

    .el-filter-label {
        margin-left: 15px;
    }

    .md-select-value {
        border-bottom: none !important;
    }

    .el-filter-clear-button {
        display: none;
        position: absolute;
        right: 15px;
        bottom: 30px;
        z-index: 50;
        width: 9px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

        .el-filter-clear-button:hover {
            background-color: #f5f5f5;
        }

        .el-filter.options-selected > .el-filter-clear-button {
            display: inherit !important;
        }

        .el-filter.options-selected .md-select-icon {
            margin-left: -36px;
        }

    .el-filter-array .el-filter-label {
        color: dimgrey;
    }

    md-select-menu,
    md-content {
        max-height: 600px !important;
    }

    hr.md-select-divider {
        border: 0;
        height: 1px;
        background: var(--thermarate-grey);
    }

</style>