(function () {
    // The JobListExportCtrl supports a list page.
    'use strict';
    var controllerId = 'JobListExportCtrl';
    angular.module('app')
        .controller(controllerId, ['$q', '$rootScope', '$scope', 'config', 'countryservice', 'nathersclimatezoneservice', 'nccclimatezoneservice', 'clientservice', 'compliancemethodservice', 'userservice', 'statusservice', 'projectdescriptionservice', 'joblistexportservice', 'daterangehelper', jobListExportController]);
    function jobListExportController($q, $rootScope, $scope, config, countryservice, nathersclimatezoneservice, nccclimatezoneservice, clientservice, compliancemethodservice, userservice, statusservice, projectdescriptionservice, joblistexportservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'CSV Export';
        vm.filter = {};

        vm.localGovernmentAuthorities = [];
        var localGovernmentAuthorityPromise = countryservice.getLocalGovernmentAuthorities()
            .then(function (data) {
                vm.localGovernmentAuthorities = data;
            });

        vm.nccClimateZoneCode = [];
        var nCCClimateZonePromise = nccclimatezoneservice.getList()
            .then(function (data) {
                vm.nccClimateZoneCode = data.data;
            });
        vm.natHERSClimateZoneList = [];
        var natHERSClimateZonePromise = nathersclimatezoneservice.getList()
            .then(function (data) {
                vm.natHERSClimateZoneList = data.data;
            });

        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
            });

        vm.statusList = [];
        var statusPromise = statusservice.getList()
            .then(function (data) {
                //Only fetch Job Status Codes
                vm.statusList = _.filter(data.data, function (rec) {
                    return rec.statusCode.startsWith('J');
                });
            });

        vm.projectDescriptionList = [];
        var projectDescriptionPromise = projectdescriptionservice.getList()
            .then(function (data) {
                vm.allProjectDescriptionList = data.data;
                vm.projectDescriptionList = vm.allProjectDescriptionList;
            });

        $q.all([complianceMethodPromise,
            statusPromise, projectDescriptionPromise, natHERSClimateZonePromise,
            nCCClimateZonePromise, localGovernmentAuthorityPromise]).then(function () {
                vm.isBusy = false;
        });

        //Project Description Typeahead dropdown List and Select
        vm.getProjectDescriptionList = function (searchText) {
            var list = [];
            var typeOther = null;
            if (searchText == undefined || searchText == null) { searchText = ""; }
            searchText = searchText.toLowerCase();
            for (var ii = 0; ii < vm.projectDescriptionList.length; ii++) {
                if (vm.projectDescriptionList[ii].projectDescriptionCode == 'PDOther') {
                    typeOther = vm.projectDescriptionList[ii];
                    continue;
                }
                if (vm.projectDescriptionList[ii].description.toLowerCase().includes(searchText)) {
                    list.push(vm.projectDescriptionList[ii]);
                }
            }
            //Always have Other at the bottom of the list before 'No Default'
            if (typeOther != null) {
                list.push(typeOther);
            }
            return list;
        }

        vm.clearProjectDescription = function () {
            vm.projectDescriptionSearchText = "";
            vm.filter.projectDescriptionCode = null;
            vm.filter.projectDescriptionOther = null;
        }

        //Project Description typeahead select
        vm.projectDescriptionSelect = function (projectDescription) {
            vm.filter.projectDescriptionCode = projectDescription ? projectDescription.projectDescriptionCode : null;
        }

        //Assessor Typeahead dropdown List and Select
        vm.getemployees = function (searchTerm) {
            var filter = [
                { field: "fullName", operator: "startswith", value: searchTerm, logic: "and" },
                { field: "isExternal", operator: "eq", value: false, valueType: "boolean" }
            ];

            return userservice.getList(null, null, null, null, null, null, filter)
                .then(function (data) {
                    return data.data;
                });
        }

        //Assessor typeahead select
        vm.assessorSelect = function (assessor) {
            vm.filter.assessorId = assessor ? assessor.userId : null;
        }

        //Client Typeahead dropdown List and Select
        vm.getclients = function (searchTerm) {
            var filter = [{ field: "clientName", operator: "startswith", value: searchTerm }];
            return clientservice.getList(null, null, null, null, null, null, filter)
                .then(function (data) {
                    var list = data.data;
                    return list;
                });
        }

        //Client typeahead select
        vm.clientSelect = function (client) {
            vm.filter.clientId = client ? client.clientId : null;
        }

        vm.designChanged = function () {
            vm.filter.design = vm.design;
        }

        //Clear the appropriate filter
        vm.clear = function (propertyName) {
            vm.filter[propertyName] = null;
            if (propertyName === "clientId") {
                vm.client = null;
            }
            if (propertyName === "assessorId") {
                vm.assignedToAssessorEmployee = null;
            }
        }

        //Submit Function Call
        vm.submit = function () {
            vm.isBusy = true;
            joblistexportservice.exportList(vm.filter).then(function (data) {
                var urlPrefix = config.servicesUrlPrefix.replace("api", "Temp");
                var url = urlPrefix + data;
                window.location.href = url;
                vm.isBusy = false;
            });
        }

        //Return to home page
        vm.cancel = function () {
            $state.go("/");
        }
    }
})();