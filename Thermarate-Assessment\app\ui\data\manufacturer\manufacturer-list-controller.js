(function () {
    // The ManufacturerListCtrl supports a list page.
    'use strict';
    var controllerId = 'ManufacturerListCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'manufacturerservice', 'daterangehelper', manufacturerListController]);

    function manufacturerListController($rootScope, $scope, $mdDialog, manufacturerservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Manufacturers';
        vm.manufacturerList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'description',
                    description: 'Description',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'externalGlazing',
                    description: 'External Glazing',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'roofLights',
                    description: 'Roof Lights',
                    dataType: 'boolean',
                    operators: []
                },
            ],
        };

        var persistRangeName = "manufacturerList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.Mreatemanufacturer = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/manufacturer/manufacturer-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "description", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            manufacturerservice.getListCancel();
            manufacturerservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = manufacturerservice.currentFilter();
                    vm.manufacturerList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.manufacturerList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.Mreatemanufacturer,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();

        vm.uploadFile = function (file) {

            if (file == null)
                return;

            manufacturerservice.processSpreadsheet(file)
                .then((data) => {
                    vm.manufacturerList = data.data;
                });
        }

        vm.delete = async function (row) {
            await manufacturerservice
                .Meletemanufacturer(row.manufacturerId);

            row.deleted = true;
            vm.manufacturerList = vm.manufacturerList.filter(x => x.manufacturerId != row.manufacturerId);
        }

        vm.bulkSelect = function (state) {
            vm.manufacturerList.forEach(x => x.isBulkSelected = state);
        }

        vm.bulkSelectionsExist = function () {
            return vm.manufacturerList.some(x => x.isBulkSelected);
        }

        vm.bulkDelete = async function () {

            let toDelete = vm.manufacturerList.filter(x => x.isBulkSelected);
            for (let i = 0; i < toDelete.length; i++) {
                await vm.delete(toDelete[i]);
            }
        }

        vm.clone = function (row) {

            manufacturerservice
                .Mopymanufacturer(row.manufacturerId)
                .then((id) => {

                    manufacturerservice
                        .Metmanufacturer(id)
                        .then(manufacturer => {

                            // Add returned template to list.
                            vm.manufacturerList.push(manufacturer);

                            // Ideally sort based on what we're actually sorting by, but since
                            // we basically only have the Template Name to go off...
                            vm.manufacturerList = vm.manufacturerList.sort((a, b) => (a.description > b.description) ? 1 : -1);
                        });

                });
        }

    }
})();