(function () {
    'use strict';
    var controllerId = 'BulkEditCostItemsModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$rootScope', '$mdDialog', 'standardmodelservice', bulkEditCostItemsModalController]);
    function bulkEditCostItemsModalController(common, $scope, $rootScope, $mdDialog, standardmodelservice) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;
        vm.type = $scope.type;
        vm.allCostItemCategories = $scope.allCostItemCategories;

        vm.data = { clientPortalEnabled: null }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        vm.confirm = function () {
            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

        vm.roundUpInt = common.roundUpInt;
        vm.keyToName = standardmodelservice.keyToName;
    }
})();