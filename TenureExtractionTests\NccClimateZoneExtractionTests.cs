﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;

namespace ExtractionTests
{
    [TestClass]
    public class NccClimateZoneExtractionTests
    {


        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public void TestFullProcess()
        {
            var directory = Directory.GetCurrentDirectory() + "\\DownloadTest/";

            // Process the Dataset into our DB.
            TenureExtraction.NccClimateZoneExtractorGeoJson ex = new TenureExtraction.NccClimateZoneExtractorGeoJson(
                directory,
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            //ex.Extract();
            ex.RunFullProcess();
            ex.Dispose();
        }
    }
}
