<section id="batchjobqueue-list-view" class="main-content-wrapper" data-ng-controller="BatchjobqueueListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Batchjobqueue"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.batchjobqueueList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="batchjobqueueList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="requestName" class="can-sort text-left">Request Name</th>
                        <th st-sort="requestData" class="can-sort text-left">Request Data</th>
                        <th st-sort="requestedDateTimeUtc" class="can-sort text-left">Requested Date Time </th>
                        <th st-sort="submitted" class="can-sort text-center">Submitted</th>
                        <th st-sort="dueDateTimeUtc" class="can-sort text-left">Due Date Time </th>
                        <th st-sort="runFrequencyCodeDescription" class="can-sort text-left">Run Frequency</th>
                        <th st-sort="runAtTime" class="can-sort text-left">Run At Time</th>
                        <th st-sort="lastRunDateTimeUtc" class="can-sort text-left">Last Run Date Time </th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.batchjobqueueList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="batchjobqueue-updateform({ recId: row.recId})">Select</md-button>  </td>
                        <td data-title="Request Name" class="text-left">{{::row.requestName }}</td>
                        <td data-title="Request Data" class="text-left">{{::row.requestData }}</td>
                        <td data-title="Requested Date Time " class="text-left">{{::row.requestedDateTimeUtc | date: 'dd/MM/yyyy' }}</td>
                        <td data-title="Submitted" class="text-center"><span ng-bind-html='row.submitted | tickCross'></span></td>
                        <td data-title="Due Date Time " class="text-left">{{::row.dueDateTimeUtc | date: 'dd/MM/yyyy' }}</td>
                        <td data-title="Run Frequency" class="text-left">{{::row.runFrequencyCodeDescription }}</td>
                        <td data-title="Run At Time" class="text-left">{{::row.runAtTime }}</td>
                        <td data-title="Last Run Date Time " class="text-left">{{::row.lastRunDateTimeUtc | date: 'dd/MM/yyyy' }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="9" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
