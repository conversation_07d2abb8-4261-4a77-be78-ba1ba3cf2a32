(function () {
    // The ClientListCtrl supports a list page.
    'use strict';
    var controllerId = 'ClientListCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$state', 'clientservice', 'daterangehelper', 'userservice', clientListController]);
    function clientListController($rootScope, $scope, $mdDialog, $state, clientservice, daterangehelper, userservice) {
        // The model for this form 
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Clients';
        vm.clientList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};

        var persistRangeName = "clientList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.goToClient = function (clientId) {
            $state.go("client-updateform", { clientId: clientId });
        }

        vm.createClient = function () {
            var newScope = $rootScope.$new(true);
            $mdDialog.show({
                templateUrl: 'app/ui/job/new-client-contact-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                scope: newScope,
                skipHide: true, // DON'T HIDE THE MODAL
            }).then(response => {
                var clientDto = {
                    clientName: response.clientName,
                    accountsFirstName: response.accountsFirstName,
                    accountsLastName: response.accountsLastName,
                    accountsPhone: response.accountsPhone,
                    accountsEmail: response.userName,
                    accountsNote: response.accountsNote,
                    accountsSameAsContact: response.accountsSameAsContact,
                    clientOptions: response.clientOptions,
                    clientDefault: {
                        purchaseOrderCode: response.purchaseOrderCode
                    }
                };
                clientservice.createClient(clientDto).then(data => {
                    var newClientDto = data.data;
                    // Assign newly created client before creating user.
                    response.user.client = newClientDto;
                    userservice.createUser(response.user).then(() => {
                        vm.isBusy = false;
                        vm.refreshList();
                    });
                });
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            let sort = [{ field: "isFavourite", dir: "desc" }];

            if (saveTableState.sort != null && saveTableState.sort.predicate != null) {

                let field = saveTableState.sort.predicate;
                let dir = saveTableState.sort.reverse ? "desc" : "asc";
                sort.push({ field, dir });
            }

            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "clientName", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            clientservice.getListCancel();
            clientservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = clientservice.currentFilter();
                    vm.clientList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.clientList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createClient,
                name: 'Add Client',
                desc: 'Add Client',
                roles: ['admin__client__create'],
            });
        }

        vm.setFavouriteStatus = function (clientId, isFavourite) {
            clientservice.setIsFavourite(clientId, isFavourite);
        }

        setActionButtons();
    }
})();