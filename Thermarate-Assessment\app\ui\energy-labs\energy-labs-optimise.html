<div data-ng-controller="EnergyLabsOptimiseController as vm"
     style="margin-top: -20px; margin-bottom: 300px;"
     class="el-poppins">

  <div class="el-heading-banner"
       style="height: 350px; padding-top: 30px;">

    <h1 style="margin: 0;">Optimise</h1>
    <h3 style="margin: 0;">Identify the most cost effective compliance solution for any design.</h3>
    <div class="white-circle"></div>
    <div class="dark-circle"></div>

  </div>
  <hr class="el-title-divider"/>

  <div class="navigation-text" ng-show="vm.project!=null">
    <div ng-click="vm.backToProjects()" class="clickable">Projects</div>
    <div>></div>
    <div ui-sref="energy-labs({projectId: vm.project.projectId})" class="clickable">{{vm.project.projectName}} [{{vm.project.clientName}}]</div>
    <div>></div>
    <b>Optimise</b>
  </div>

  <div class="select-model-main-container">

    <standard-model-filters ng-if="vm.project != null && vm.mode === 'select' && vm.initialised"
                            current-list="vm.modelList"
                            applied-filters="vm.filterData"
                            settings="vm.project.energyLabsSettings"
                            filter-count-data="vm.filterCountData"
                            on-filter-changed="vm.updateFilteredList"
                            on-sort-changed="vm.updateSort"
                            total-items-without-filters="vm.totalWithoutFilters"
                            current-total="vm.currentTotal">
    </standard-model-filters>

    <!-- Grid of all available designs -->
    <div ng-if="vm.mode === 'select'"
         class="el-option-grid">
      <div ng-repeat="parentModel in vm.modelList"
           class="el-card clickable"
           ng-click="vm.select(parentModel)">

        <div class="el-card-header">
          <div class="el-title">{{parentModel.title}}</div>
        </div>

        <div class="el-card-body padded">
            <!-- Options + Drawings -->
            <home-plan-options-switcher
                style="width:100%;"
                the-parent-model="parentModel"
                show-label="true"
                view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)">
            </home-plan-options-switcher>
        </div>

      </div>
    </div>

    <!-- Selected Design + Comparison Cards -->
    <div ng-if="vm.mode === 'optimise'">

      <div class="central-grid-container" style="margin-bottom: 85px;">
        <button ng-click="vm.reset()"
                class="el-calculate-button">
          START OVER <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

      <div ng-if="vm.viewingModels.length > 0"
           class="el-option-flex"
           ng-form="vm.energyLabsForm">

        <div ng-repeat="parentModel in vm.viewingModels track by $index"
             style="{{ vm.useGridDisplay() === true ? '' : vm.modelsToShow().length == 3 ? 'width: 600px;' : 'width: 800px;' }}"
             class="optimise-column"
             ng-show="$index === 0 || $index === 1 && vm.compare === true">

            <!-- Data input card -->
            <div class="el-card">
              <div class="el-card-header with-actions">
                <span><!-- Empty --></span>
                <div class="el-title">{{parentModel.title}}</div>

                <md-menu ng-show="!vm.disabled"
                         style="justify-self: right;">

                  <!-- Initial '...' button, which launches options -->
                  <img md-menu-origin
                       class="clickable el-menu-launch"
                       ng-click="$mdOpenMenu()"
                       src="/content/feather/more-horizontal.svg"
                       ng-disabled="vm.disabled"/>
                  <md-menu-content>

                    <!-- Restore Defaults -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuildingToDefaults(parentModel.selectedVariation);">
                        <span>Restore Defaults</span>
                      </md-button>
                    </md-menu-item>

                    <md-menu-divider></md-menu-divider>

                    <!-- Clear -->
                    <md-menu-item>
                      <md-button ng-click="vm.resetBuilding(parentModel.selectedVariation);">
                        <span style="color: orangered;">Clear</span>
                      </md-button>
                    </md-menu-item>

                  </md-menu-content>
                </md-menu>
              </div>

              <div class="el-card-body padded">

                <!-- Options + Drawings -->
                <home-plan-options-switcher
                    style="width:100%;"
                    the-parent-model="parentModel"
                    get-full-variation="true"
                    show-label="true"
                    show-north-offset="vm.mode == 'optimise' && parentModel.selectedVariation.optionData.northOffset != 'Multi-Orientation'"
                    view-3d-floor-plans="vm.project.energyLabsSettings.view3dFloorPlans"
                    on-model-changed="vm.variationChanged(parentModel)"
                    option-button-clicked-callback="vm.switcherOptionButtonClicked(parentModel)"
                    viewing-models-count="vm.viewingModels.length">
                </home-plan-options-switcher>

                <!-- This is where the actual 'comparison' function exists... Maybe it shouldn't live here but eh -->
                <div ng-if="vm.mode == 'optimise'" style="display:block; justify-self:stretch; margin-top:38px;">

                  <!-- Block Data -->
                  <standard-model-block the-model="parentModel.selectedVariation"
                                        variable-options="parentModel.selectedVariation.variableOptions"
                                        configuration="default"
                                        project="vm.project"
                                        required="true"
                                        disabled="true"
                                        on-data-changed="vm.clearComparison()"
                                        copy-across-enabled="vm.viewingModels.length > 1"
                                        copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                        copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)"
                                        climate-zone-is-invalid="parentModel.selectedVariation.modelBlockShowError">
                  </standard-model-block>

                  <!-- Specification Data -->
                  <standard-model-specifications the-model="parentModel.selectedVariation"
                                                 variable-options="parentModel.selectedVariation.variableOptions"
                                                 variable-metadata="parentModel.selectedVariation.variableMetadata"
                                                 project="vm.project"
                                                 required="true"
                                                 disabled="false"
                                                 on-data-changed="vm.clearComparison()"
                                                 copy-across-enabled="vm.viewingModels.length > 1"
                                                 copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                                 copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)"
                                                 multi-select="true"
                                                 initialise-options="vm.initialiseOptions">
                  </standard-model-specifications>

                  <!-- Assessment -->
                  <standard-model-assessment the-model="parentModel.selectedVariation"
                                             project="vm.project"
                                             variable-options="parentModel.selectedVariation.variableOptions"
                                             on-data-changed="vm.clearComparison()"
                                             copy-across-enabled="vm.viewingModels.length > 1"
                                             copy-across-data="parentModel.selectedVariation.copyAcrossData"
                                             copy-across-trigger="vm.copyOptionAcross(parentModel.selectedVariation.copyAcrossData)"
                                             target-energy-rating-enabled="true"
                                             cost-estimate-enabled="parentModel.selectedVariation.optionData.costEstimateEnabledDefault">
                  </standard-model-assessment>

                </div>

              </div>
            </div>

            <!-- Performance Data / Results -->
            <div ng-show="vm.showResults"
                 class="el-card padded el-card-body v-margin"
                 style="justify-items: normal; overflow: visible; padding-bottom: 0 !important;">
              <div>
                <div class="el-section-title">
                  <img class="el-section-icon"
                       src="content/images/energy-labs/el-performance-icon.svg"
                       alt="Icon of a home design which is stamped or has awards">
                  Performance
                </div>

                <!-- Empty div that fills screen for click logic -->
                <div class="div-fill-screen"
                     ng-if="vm.anyLaunchDropdownsExpanded()"
                     ng-click="vm.collapseAllLaunchDropdowns(); $event.stopPropagation();"
                />

                <!-- Table if results found -->
                <table ng-if="parentModel.selectedVariation.optionPerformance.resultsPaged.length > 0"
                       class="el-optimise-performance-table table table-condensed table-data-centered table-hover"
                       cellspacing="0">
                  <thead>
                  <tr>
                    <th style="border-bottom: none;"></th>
                    <th style="border-bottom: none;"></th>
                    <th>Heating (MJ/m<sup>2</sup>)</th>
                    <th>Cooling (MJ/m<sup>2</sup>)</th>
                    <th>Total (MJ/m<sup>2</sup>)</th>
                    <th ng-if="parentModel.selectedVariation.optionData.assessmentMethod == 'House Energy Rating (HER)'">Energy Rating</th>
                    <th ng-if="parentModel.selectedVariation.optionData.costEstimateEnabledDefault">Total Cost</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr ng-repeat="option in parentModel.selectedVariation.optionPerformance.resultsPaged track by $index"
                      class="clickable highlight-on-hover {{option.uiExpanded ? 'row-highlighted' : ''}}"
                      ng-click="vm.expandResult(parentModel.selectedVariation, option)"
                      style="position: relative !important">
                    <td>
                      <div style="display: grid; justify-items: center; align-items: center; background-color: var(--thermarate-green); height: 30px; width: 30px;">
                        <span style="color: white;">{{$index + 1}}</span>
                      </div>
                      <div class="expanded-result-gap" ng-style="{ 'height': !option.uiExpanded ? 0 : 100 + (vm.propertiesInOrder().length * 52) + (parentModel.selectedVariation.optionData.costEstimateEnabledDefault ? 52 : 0) + 'px' }"/>
                      <div class="expanded-result"
                           ng-style="{
                              'opacity': !option.uiExpanded ? '0' : '1',
                              'height': !option.uiExpanded ? '0' : !option.uiExpanded ? 0 : 80 + (vm.propertiesInOrder().length * 52) + (parentModel.selectedVariation.optionData.costEstimateEnabledDefault ? 52 : 0) + 'px',
                              'padding-top': !option.uiExpanded ? 0 : '12px',
                              'padding-bottom': !option.uiExpanded ? 0 : '10px',
                              'overflow': !option.uiExpanded ? 'hidden' : 'visible'
                           }">

                        <div ng-repeat="key in vm.propertiesInOrder()" class="expanded-result-item" ng-style="{ 'pointer-events': option.uiExpanded ? 'initial' : 'none' }">
                            <div class="expanded-result-label">{{vm.keyToName(key)}}</div>
                            <div class="expanded-result-value">{{option[key]}}</div>
                            <div ng-if="parentModel.selectedVariation.optionData.costEstimateEnabledDefault" class="expanded-result-cost">${{option.costEstimateData[key].toLocaleString()}}</div>
                        </div>
                        <div class="expanded-result-item" ng-if="parentModel.selectedVariation.optionData.costEstimateEnabledDefault" ng-style="{ 'pointer-events': option.uiExpanded ? 'initial' : 'none' }">
                            <div class="expanded-result-value expanded-result-total-cost-label">Total Cost</div>
                            <div ng-if="parentModel.selectedVariation.optionData.costEstimateEnabledDefault" class="expanded-result-cost expanded-result-total-cost">${{option.costEstimateData.totalCost.toLocaleString()}}</div>
                        </div>
                        <!-- Configure Button -->
                        <div class="expanded-result-item launch-button-container">
                            <!-- Button -->
                            <div class="launch-button" ng-click="option.launchExpanded = !option.launchExpanded; $event.stopPropagation();">
                                <div class="launch-button-text">LAUNCH</div>
                                <img src="/content/images/energy-labs/el-launch-arrow-icon.svg" style="width:16px; height:auto;" />
                                <!-- Expanded -->
                                <div class="launch-button-options" ng-class="{ 'expanded': option.launchExpanded }">
                                    <div ng-if="option.view3dFloorPlans" ng-click="vm.open3dViewerModal(option, $event);$event.stopPropagation();">3D Model</div>
                                    <div ng-click="vm.goToOtherTool('configure', option);$event.stopPropagation();">Configure</div>
                                    <div ng-click="vm.openSwitcherModal(option, $event);$event.stopPropagation();">Floor Plan</div>
                                    <div ng-click="vm.goToOtherTool('woh', option);$event.stopPropagation();">Whole-of-Home</div>
                                </div>
                            </div>
                        </div>

                      </div>
                    </td>

                    <!-- Name -->
                    <td class="performance-result performance-result-name" style="text-align: start; position: relative;">
                      <span style="text-align: start">
                        {{option.standardHomeModelTitle}} (Configuration {{$index + 1}})
                      </span>
                      <img class="expand-icon" ng-style="{ transform: !option.uiExpanded ? 'rotateX(180deg)' : none }" src="../../../content/images/arrow-up-skinny.png" />
                    </td>

                    <!-- Heating Load -->
                    <td class="performance-result">
                      <span ng-style="{ color: vm.determineHeatCoolResultColour(vm.project.energyLabsSettings.heatingCoolingLoadLimits, option, 'heatingLoad', 'heatingLoadLimit') }">
                        {{option.heatingLoad.toFixed(1)}}
                        <!-- Popup with Additional Data -->
                        <md-tooltip class="solid-popup optimise-tooltip"
                                    md-direction="bottom">
                            <el-heating-tooltip source="option" assessment-method="option.assessmentMethod" heating-cooling-load-limits="vm.project.energyLabsSettings.heatingCoolingLoadLimits"></el-heating-tooltip>
                        </md-tooltip>
                      </span>
                    </td>

                    <!-- Cooling Load -->
                    <td class="performance-result">
                      <span ng-style="{ color: vm.determineHeatCoolResultColour(vm.project.energyLabsSettings.heatingCoolingLoadLimits, option, 'coolingLoad', 'coolingLoadLimit') }">
                        {{option.coolingLoad.toFixed(1)}}
                        <!-- Popup with Additional Data -->
                        <md-tooltip class="solid-popup optimise-tooltip"
                                    md-direction="bottom">
                            <el-cooling-tooltip source="option" assessment-method="option.assessmentMethod" heating-cooling-load-limits="vm.project.energyLabsSettings.heatingCoolingLoadLimits"></el-cooling-tooltip>
                        </md-tooltip>
                      </span>
                    </td>

                    <!-- Total Energy -->
                    <td class="performance-result">

                      <span ng-style="{ color: option.totalEnergyLoad <= option.energyLoadLimits.calculatedMaxEnergy ? 'var(--thermarate-green)' : 'var(--warning)' }">
                        {{option.totalEnergyLoad.toFixed(1)}}
                        <!-- Popup with Additional Data -->
                        <md-tooltip class="solid-popup optimise-tooltip"
                                    md-direction="bottom">
                        <el-total-tooltip source="option"></el-total-tooltip>
                        </md-tooltip>
                      </span>

                    </td>

                    <!-- Calculated Energy Rating -->
                    <td class="performance-result energy-rating"
                        ng-if="parentModel.selectedVariation.optionData.assessmentMethod == 'House Energy Rating (HER)'">

                      <span ng-style="{ color: option.energyRating >= 7 ? 'var(--thermarate-green)' : 'var(--warning)' }">
                        {{option.energyRating.toFixed(1)}}
                        <!-- Popup with Additional Data -->
                        <md-tooltip class="solid-popup optimise-tooltip"
                                    md-direction="bottom">
                        <el-rating-tooltip source="option" target-energy-rating="parentModel.selectedVariation.optionData.targetEnergyRating"></el-rating-tooltip>
                        </md-tooltip>
                      </span>

                    </td>

                    <!-- Total Cost -->
                    <td class="performance-result energy-rating"
                        ng-if="parentModel.selectedVariation.optionData.costEstimateEnabledDefault">
                      <span>
                        ${{option.costEstimateData.totalCost.toLocaleString()}}
                      </span>
                    </td>

                  </tr>
                  </tbody>
                </table>

                <!-- Info if no results found -->
                <div ng-if="parentModel.selectedVariation.optionPerformance.resultsPaged === null || parentModel.selectedVariation.optionPerformance.resultsPaged.length === 0">
                  <div class="el-category" style="text-align: center; color: orangered; margin: 2rem;">
                    No matches found for given configuration.
                  </div>
                </div>

                <div ng-if="parentModel.selectedVariation.optionPerformance.resultsPaged < parentModel.selectedVariation.optionPerformance.resultsFull"
                     style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                     class="clickable"
                     ng-click="vm.getMoreResults()">
                    Show more
                </div>

              </div>
            </div>

          </div>

      </div>

      <div ng-show="vm.showResults === false"
           class="central-grid-container"
           style="margin-top: 5rem;">
        <button ng-click="vm.runCalc()"
                ng-disabled="vm.energyLabsForm.$invalid || vm.isBusy"
                class="el-calculate-button">
          CALCULATE <img class="el-launch-mode-icon" src="content/images/energy-labs/el-launch-arrow-icon.svg" alt="Arrow pointing right">
        </button>
      </div>

    </div>

  </div>

</div>
<style>

    .optimise-column {
        width: 1240px;
        min-width: 1240px;
    }

    /* Row */
    .el-optimise-performance-table {
        overflow: hidden;
    }
    .el-optimise-performance-table tr:hover.highlight-on-hover {
        background-color: rgba(0, 0, 0, 0.12);
    }

    /* td's */
    .el-optimise-performance-table td {
        vertical-align: top !important;
    }

    /* Arrow */
    .el-optimise-performance-table .result-arrow-icon {
        position: absolute;
        right: 10px;
        top: 16px;
        width: 14px;
        cursor: pointer;
    }

    /* performance-result td's */
    .el-optimise-performance-table .performance-result {
        padding-top: 14px;
        text-shadow: 0px 0px 1px black;
    }

    /* Name */
    .el-optimise-performance-table .performance-result-name {
        display: flex;
        column-gap: 12px;
        text-shadow: none;
    }
    .el-optimise-performance-table tr:hover .performance-result-name,
    .row-highlighted .performance-result-name {
        text-shadow: 0px 0px 1px black;
    }
    .el-optimise-performance-table .performance-result-name .expand-icon {
        visibility: hidden;
        margin-top: 3px;
        width: 13px;
        height: 8px;
    }
    .el-optimise-performance-table tr:hover .expand-icon {
        visibility: visible;
    }

    .el-optimise-performance-table tr:hover > td,
    .el-optimise-performance-table tr:hover > th {
        background-color: transparent !important;
    }

/*    .row-highlighted {
         background-color: rgba(0, 0, 0, 0.12);
    }*/

    .expanded-result {
        z-index: 50;
        width: 700px;
        transform: translateX(-50%);
        padding: 30px 250px 0px 250px;
        position: absolute !important;
        top: 46px !important;
        left: 50% !important;
        background-color: white;
        transition: height 0.3s ease-out, opacity 0.3s ease-out;
    }
    .expanded-result-gap {
        transition: height 0.3s ease-out;
    }
        .expanded-result-item {
            padding: 8px;
            display: grid;
            grid-template-columns: 5fr 1fr;
            border-bottom: var(--thermarate-grey) 1px solid;
        }
            .expanded-result-label {
                margin-bottom: 5px;
                font-size: 9px;
                grid-column: 1 / 3;
                text-align: left;
            }
            .expanded-result-value {
                text-align: left;
            }
            .expanded-result-cost {
                text-align: right;
            }
            .expanded-result-total-cost-label, .expanded-result-total-cost {
                text-shadow: 0px 0px 1px black;
                padding: 8px 0px !important;
            }
            .optimise-tooltip {
                margin-top: 14px;
            }

    /* Emptry div that fills screen for click logic */
    .div-fill-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10;
        background-color: transparent;
        cursor: default;
    }
    /* Launch Container */
    .launch-button-container {
        margin-top: 10px;
        display: block;
        border-bottom: none;
    }
    /* Launch Button */
    .launch-button {
        position: relative;
        float: right;
        width: max-content;
        padding: 6px 15px;
        display: flex;
        column-gap: 10px;
        border-radius: 8px;
        background-color: #cccccc;
        color: white;
        transition: background-color 0.2s;
    }
    /* Launch Button (Hover) */
    .launch-button:hover {
        background-color: var(--thermarate-green);
    }
    /* Launch Button Text */
    .launch-button-text {
        margin-top: 2px;
        font-weight: bold;
    }
    /* Launch Button Options */
    .launch-button-options {
        z-index: 70;
        position: absolute;
        bottom: -10px;
        left: 0;
        transform: scaleY(0) translateY(100%);
        width: max-content;
        border-radius: 8px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 2px 1px -1px rgba(0, 0, 0, 0.12);
        opacity: 0;
        transition: all 0.3s;
        background-color: white;
    }
    /* Launch Button Options Expanded */
    .launch-button-options.expanded {
        transform: scaleY(1) translateY(100%) !important;
        opacity: 1;
    }
    /* Launch Button Options Option */
    .launch-button-options > div {
        padding: 5px 15px;
        color: black;
    }
    /* Launch Button Options Option (Hover) */
    .launch-button-options > div:hover {
        background-color: var(--thermarate-grey);
    }

    .el-optimise-performance-table tr:hover td {
        filter: none !important; /* This fixes issue where hovering over row would cause expanded results div to shift to left */
        background-color: transparent !important;
    }

</style>
