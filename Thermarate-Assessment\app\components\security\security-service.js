/*
* Security Service Module
*
* The security service module manages login, logout, authorising access and providing user conext.
*
*/

// Based loosely around work by <PERSON><PERSON><PERSON> - https://github.com/witoldsz/angular-http-auth
angular.module('security.service', [
  'security.retryQueue',    // Keeps track of failed requests that need to be retried once the user logs in
  'security.login',         // Contains the login form template and controller
  'ngMaterial',           // Used to display the login form as a modal dialog.
  'common',                 // Common items such as logging
  'common.bootstrap'        // Common bootstrap services including confirmation dialog
])

.factory('security', ['$http', '$q', '$location', 'securityRetryQueue', '$mdDialog', 'common', '$rootScope',
    function ($http, $q, $location, queue, $mdDialog, common, $rootScope) {

    // Redirect to the given url (defaults to '/')
    function redirect(url) {
        url = url || '/';

        if ($location.path() == '/login' && url == '/login')
            return; // Dont redirect back to self - if login page.

        $location.path(url);
    }

    var logSuccess = common.logger.getLogFn('security', 'success');
    var logError = common.logger.getLogFn('security', 'error');

    var checkedForCurrentUser = false;

    // Login form dialog stuff
    var loginDialog = null;
    function openLoginDialog() {
        if ( loginDialog ) {
            // Already open 
            return;
        }
        var modalOptions = {
            templateUrl: 'app/components/security/login/form.tpl.html',
            controller: 'LoginFormController',
            fullscreen: true,
        };
        loginDialog = $mdDialog.show(modalOptions);
        loginDialog.then(onLoginDialogClose);
    }
    function closeLoginDialog(success) {
        if (loginDialog) {
            $mdDialog.hide(success);
            loginDialog = null;
        }
    }
    function onLoginDialogClose(success) {
        if ( success ) {
            queue.retryAll();
            loginDialog = null;
        } else {
          queue.cancelAll();
          redirect('/login');
        }
    }

    var twofacauthDialog = null;
    function openSetup2faDialog() {
        var modalOptions = {
            templateUrl: 'app/components/security/account/twofacauthform.html',
            controller: 'TwoFacAuthController',
            keyboard: true,
            backdrop: 'static',
            windowClass: 'thin1-modal',
        };
        twofacauthDialog = $mdDialog.show(modalOptions).then(onTwoFacAuthDialogClose);
    };

    function onTwoFacAuthDialogClose(success) {
        twofacauthDialog = null;
        if (success) {
            queue.retryAll();
        } else {
            queue.cancelAll();
            redirect();
        }
    }

    function closeSetup2faDialog(success) {
        if (twofacauthDialog) {
            twofacauthDialog.close(success);
        }
    }

    var accountDialog = null;
    function openAccountDialog() {
        if (loginDialog) {
            throw new Error('Trying to open a dialog that is already open!');
        }
        var modalOptions = {
            templateUrl: 'app/components/security/account/form.tpl.html',
            controller: 'AccountFormController',
            fullscreen: true,
        };
        accountDialog = $mdDialog.show(modalOptions);
        accountDialog.then(onAccountDialogClose);
    }
    function closeAccountDialog(success) {
        if (accountDialog) {
            $mdDialog.hide(success);
            accountDialog = null;
        }
    }
    function onAccountDialogClose(success) {
        if (success) {
            queue.retryAll();
            accountDialog = null;
        } else {
            queue.cancelAll();
            redirect();
        }
    }

    // Check if the user has the requested role.
    function authorize(requiredRole) {
        if (service.currentUser.userRoles.indexOf(requiredRole.toLowerCase()) > -1) return true;
        return false;
    }

    // Check if the user has one of the required roles.
    function checkRoles(requiredRoles) {

        // TODO-341: This legit just checks a string, so if a substring is found, it matches.
        //  Replace with the below at some point.
        // if (requiredRoles == null || requiredRoles.length === 0)
        //     return true;
        //
        // if(service.currentUser.userRoles == null || service.currentUser.userRoles.length === 0)
        //     return false;
        //
        // for (var i = 0; i < service.currentUser.userRoles.length; i++) {
        //     if (requiredRoles.some(x => x === service.currentUser.userRoles[i].toLowerCase())) {
        //         return true;
        //     }
        // }
        
        if (requiredRoles == null || requiredRoles.length === 0)
            return true;
        if (requiredRoles && service.currentUser.userRoles) {
            for (var i = 0; i < service.currentUser.userRoles.length; i++) {
                if (requiredRoles.indexOf(service.currentUser.userRoles[i].toLowerCase()) > -1) {
                    return true;
                }
            }
        }

        return false;
    }

    // Register a handler for when an item is added to the retry queue
    queue.onItemAddedCallbacks.push(function(retryItem) {
        if ( queue.hasMore() ) {
            service.showLogin();
        }
    });

    // The public API of the service
    var service = {

        // Get the first reason for needing a login
        getLoginReason: function() {
            return queue.retryReason();
        },

        // Show the modal login dialog
        showLogin: function() {
            openLoginDialog();
        },
        show2faSetup: function () {
            openSetup2faDialog();
        },
        // 2FA - verifyAuthenticator
        verifyAuthenticator: function (verificationCode) {
            var promise = $http.get('/api/Security/VerifyAuthenticator', { params: { "verificationCode": verificationCode } })
                .then(function (response) {                    
                    if (response.data === 'true') {
                        closeSetup2faDialog(true);
                        logSuccess('You have successfully verify  2FA', null, true);
                    }
                    return response.data === true;
                })
            return promise;
        },

        closeSetup2faDialog: closeSetup2faDialog,

        // 2FA - IsTwoFacAuthEnabled
        isTwoFacAuthEnabled: function (userName) {
            var promise = $http.get('/api/Security/IsTwoFacAuthEnabled', { params: { "userName": userName } })
                .then(function (response) {
                    return response.data === true;
                })
            return promise;
        },

        // 2FA - IsTwoFacAuthRequire
        isTwoFacAuthRequire: function (userName) {
            var promise = $http.get('/api/Security/IsTwoFacAuthRequire', { params: { "userName": userName } })
                .then(function (response) {
                    return response.data === true;
                })
            return promise;
        },

        // 2FA - getAuthenticatorDetails
        getAuthenticatorDetails: function (verificationCode) {
            var promise = $http.get('/api/Security/GetAuthenticatorDetails')
                .then(function (response) {
                    return response.data;
                })
            return promise;
        },

        // 2FA - Disable2FA
        disable2fa: function () {
            var promise = $http.get('/api/Security/Disable2FA')
                .then(function (response) {
                   
                    localStorage.removeItem("2fa_expire_" + service.currentUser.identityUser.userName);

                    return response;
                })
            return promise;
        },

        // Attempt to authenticate a user by the given email and password
        login: function (email, password, verificationCode, isTwoFacAuthRequire) {
            var promise = $http.post('/api/Security/Login', { email: email, password: password, twoFacAuthVerificationCode: verificationCode, isTwoFacAuthRequire: isTwoFacAuthRequire })
                          .then(function(response) {
                              angular.copy(response.data, service.currentUser);
                              if (service.isAuthenticated()) {

                                  if (verificationCode && service.currentUser.twoFacAuthEnabled) {
                                      const expire = new Date()
                                      expire.setDate(expire.getDate() + 30)
                                      localStorage.setItem("2fa_expire_" + email, '' + expire.getTime());
                                  }

                                  closeLoginDialog(true);
                                  if ($location.path() == '/login')
                                      $location.path('/home');
                                  logSuccess('You have successfully logged in', null, true);
                              }
                              var isAuthenticated = service.isAuthenticated();
                              if (isAuthenticated) {
                                  return true;
                              }
                              else {
                                  var hasErrorMsg = response.data && response.data.error;
                                  if (hasErrorMsg) {
                                      return response.data.error;
                                  }
                              }
                          })
          return promise;
        },

        // Give up trying to login and clear the retry queue
        cancelLogin: function() {
            closeLoginDialog(false);
            if ($location.path() != '/login')
                redirect('/login');
        },

        // Send request to the sever to reset the password.
        forgotPassword: function(email) {
            var promise = $mdDialog.show($mdDialog.confirm().title('Reset Password')
                                            .textContent('Please confirm you want a new password to be emailed.')
                                            .ok('Reset Password')
                                            .cancel('Cancel'))
                .then(function () {
                    var promise = $http.post('/api/Security/ResetPassword', { email: email })
                        .then(function (response) {
                            if (response.data == true) {
                                closeLoginDialog(false);
                                logSuccess('A new password has been emailed.', null, true);
                                return true;
                            }
                            else {
                                return false;
                            }
                            });
                    return promise;
                });

            return promise;
        },

        microsoftLogin: function(code) {
             return $http.post('/api/ExternalLogin/MicrosoftLogin', { code: code })
                    .then(response => {
                        console.log(response);
                        return response;
                    })
                    .catch(error => {
                        logError(error?.data?.exceptionMessage);
                        console.log(error);
                        return error?.data?.exceptionMessage;
                    });
        },

        googleLogin: function(code) {
             return $http.post('/api/ExternalLogin/GoogleLogin', { code: code })
                    .then(response => {
                        console.log(response);
                        return response;
                    })
                    .catch(error => {
                        logError(error?.data?.exceptionMessage);
                        console.log(error);
                        return error?.data?.exceptionMessage;
                    });
        },

        // Logout the current user and redirect to the login page.
        logout: function() {
            $http.post('/api/Security/Logout')
            .then(function () {
                service.currentUser.userId = '';
                service.currentUser.userRoles = ['public'];
                service.currentUser.userFullName = '';
                redirect('/login');
                });
        },

        // Ask the backend to see if a user is already authenticated - this may be from a previous session.
        requestCurrentUser: function (q) {
            if (service.isAuthenticated()) {
                checkedForCurrentUser = true;
                q.resolve();
                return $q.when(service.currentUser);
            } else {
                return $http.get('/api/Security/CurrentUser').then(function (response) {
                if (response.data != null && response.data != 'null')  
                    angular.copy(response.data, service.currentUser);
                checkedForCurrentUser = true;
                q.resolve();
                return service.currentUser;
              }, function (error) {
                  checkedForCurrentUser = true;
                  q.resolve();
                  return null;
              });
          } 
        },

        // Authorize current user based on required role (user must belong to 1 of the required roles)
        // Returns a Promise.
        authorizeRoles: function(requiredRoles) {
            var defer = $q.defer();
            var promise = defer.promise;

            // Make sure current user has been loaded (if authenticated).
            // Pass defer in, to ensure we wait if the user details are loaded from the server.
            service.requestCurrentUser(defer);

            return promise.then(function () {
                
                if( service.currentUser.userId == null || service.currentUser.userId === "") {
                    console.warn("[security] current user is not authenticated.");
                    return false;
                }
                
                return checkRoles(requiredRoles)
            })
        },

        // Authorize current user based on required role (user must belong to 1 of the required roles)
        // Checks roles immediately - no promise.
        immediateCheckRoles: function (requiredRoles) {
            return checkRoles(requiredRoles)
        },

        // Show the modal account dialog
        showAccount: function () {
            openAccountDialog();
        },

        closeAccount: function (success) {
            closeAccountDialog(success);
        },

        // Attempt to change account password
        changePassword: function (email, currentPassword, newPassword) {
            var promise = $http.post('/api/Security/ChangePassword', { password: currentPassword, newPassword: newPassword })
                          .then(function (response) {
                              if(response.data.errorMessage != '') {
                                  logError('Password changed failed', null, true);
                                  return false;
                              }
                              else {
                                  closeAccountDialog(true);
                                  logSuccess('Password changed.', null, true);
                                  return true;
                              }
                              
                          });
            return promise;
        },

        // Cancel the change password dialog.
        cancelChangePassword: function () {
            closeAccountDialog(true);
            //redirect();
        },

        // Information about the current user
        currentUser: {
            userId: '',
            userFullName: '',
            userFirstName: '',
            userLastName: '',
            userEmailAddress: '',
            userBusinessUnit: null,
            managedUserId: null,
            userRoles: ['public'],
            loggedInAt: null,
            userJobTitle: '',
            isLocked: false
        },

        // Is the current user authenticated?
        isAuthenticated: function () {
            if (service.currentUser == undefined || service.currentUser == null || service.currentUser == 'null' || service.currentUser.userId == '' || service.currentUser.userId == undefined)
                return false;
            return true;
        },
        
    };

    return service;

}]);
