﻿<ui-select name="stateCode"
            ng-model="$parent.model"
            theme="bootstrap"
            reset-search-input="false"
            class=""
           ng-required="$parent.isrequired"
            focus-on="stateCode" >
    <ui-select-match placeholder="state" title="state">
        <abbr ng-if="!$select.isEmpty()" class="search-choice-clear" ng-click="$select.select(undefined)"></abbr>
        {{$select.selected.name}}
    </ui-select-match>
    <ui-select-choices repeat="srow.stateCode as srow in states | filter: $select.search"
                        refresh-delay="0">
        <div ng-bind-html="srow.name | highlight: $select.search">
        </div>
    </ui-select-choices>
</ui-select>
