<section id="error-list-view" class="main-content-wrapper" data-ng-controller="ErrorListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Error Log"
                data-query-builder-current="vm.currentQuery"
                data-export-list="exportList"
                data-default-start="vm.rptDateRange"
                data-date-ranges="vm.ranges"
                data-export-filename="Error">
        </div>
        <div class="table-responsive-vertical shadow-z-1" >
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.errorList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="errorList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="timeUtc" class="can-sort text-left" style="min-width:90px;">Time</th>
                        <th st-sort="message" class="can-sort text-left" >Message</th>
                        <th st-sort="application" class="can-sort text-left" style="min-width:80px;">Application</th>
                        <th st-sort="host" class="can-sort text-left" style="min-width:90px;">Host</th>
                        <th st-sort="source" class="can-sort text-left" style="min-width:80px;">Source</th>

                        <th st-sort="user" class="can-sort text-left" style="min-width:90px;">User</th>

                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.errorList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="settings-system-log-detail({ errorId: row.errorId, description: row.application })">Select</md-button>  </td>
                        <td data-title="Date UTC" class="text-left">{{::row.timeLocal| date: 'dd/MM/yyyy HH:mm:ss'}}</td>
                        <td data-title="Message" class="text-left" flex-gt-sm="20" >{{::row.message}}</td>
                        <td data-title="App" class="text-left" rd-show-is-modified="row.unknown">{{::row.application}}</td>
                        <td data-title="Host" class="text-left">{{::row.host}}</td>
                        <td data-title="Source" class="text-left">{{::row.source}}</td>
                        <td data-title="User" class="text-left">{{::row.user}}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="7" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="7"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
