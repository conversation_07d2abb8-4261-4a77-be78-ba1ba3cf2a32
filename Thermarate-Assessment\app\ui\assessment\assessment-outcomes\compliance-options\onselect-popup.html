<form name="SimulationOptionsForm"
      data-ng-controller='OnselectPopupCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Select Compliance Option"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="min-width:600px; padding: 10px 20px;">

        <!-- Processing Overlay -->
        <div ng-if="vm.isBusy == true"
             class="busy-processing-overlay">
            <div>
                <md-progress-circular md-mode="indeterminate"></md-progress-circular>
            </div>
        </div>

        <div style="text-align:left; margin: 30px 0px; ">
            The assessment will be finalised with the specifications detailed in this compliance option.
        </div>

        <!-- Purchase Order Section -->
        <div flex="100"
             ng-if="option.newPurchaseOrderRequired == true">

            <h1 class="md-headline"
                style="margin-bottom: 20px;">
                Purchase Order
            </h1>

            <!-- ******** Purchase Order & File ******** -->
            <md-input-container class="md-block vertically-condensed"
                                flex-gt-sm
                                ng-if="vm.purchaseOrderCode  == 'Number' || vm.purchaseOrderCode =='NumberAndFile'">
                <label>Purchase Order</label>
                <input type="text"
                       name="purchaseOrder"
                       ng-required="vm.purchaseOrderCode !='File'"
                       ng-model="option.purchaseOrder" />
                <div ng-messages="jobform.purchaseOrder.$error">
                    <div ng-message="required">Purchase order is required.</div>
                </div>
            </md-input-container>

            <div class="md-block"
                 style="margin: 0px 2px;"
                 flex-gt-sm
                 ng-if="vm.purchaseOrderCode =='NumberAndFile' || vm.purchaseOrderCode =='File'">
                <label style="font-size: 9px; color: gray;">Purchase Order File</label>
                <file-upload class="vertically-condensed"
                             assessment="vm.assessment"
                             job="vm.assessment.job"
                             required-message="''"
                             force-edit="true"
                             file-object="option"
                             prop-name="purchaseOrderFile"
                             category="'Invoicing'"
                             classification="'Purchase Order File'"
                             is-required="true"></file-upload>
            </div>
        </div>

        <!-- Attach Files -->
        <div ng-if="vm.option.updatedDrawingsRequired == true">

            <h1 class="md-headline"
                style="margin-bottom: 10px;">
                Updated Drawings
            </h1>

            <!-- If drawings are already attached to this option, ask if you wish to clear-->
            <div ng-if="vm.drawingsPresent()"
                 layout="row">

                <span style="margin-bottom: auto; margin-top: auto;">
                    There are already drawings attached to this option.
                </span>

                <md-button class="md-raised md-warn"
                            style="margin-left: auto;"
                            ng-click="vm.deleteAllDrawings();">
                    Clear Drawings
                </md-button>

            </div>

            <md-card>
                <table style="border-collapse: collapse; margin-top:10px">
                    <tbody>
                        <tr ng-repeat="item in vm.attachedDrawings track by $index"
                            lr-drag-data="vm.attachedDrawings"
                            lr-match-property="id"
                            lr-index="vm.attachedDrawings.indexOf(item)"
                            lr-match-value="{{item.id}}"
                            lr-drag-src="newFile"
                            lr-drop-target="newFile">

                            <!-- File Row -->
                            <td class="file-row" ng-show="item.attachmentUploadProgress != null || item.attachment != null">
                                <!-- Document Icon -->
                                <img src="../../../content/images/file.png"
                                        style="opacity: 0.35; margin-right: 3px; width: 20px; height: auto; cursor: pointer;"
                                        ng-show="item.attachment!=null" 
                                        ng-click="vm.downloadFile(item.attachment)"/>
                                <!-- Name -->
                                <button class="file-name-button" 
                                        ng-show="(item.attachmentUploadProgress != null || item.attachment != null) && item.attachment!=null"
                                        ng-click="vm.downloadFile(item.attachment)">
                                    {{item.attachment.fileName}}
                                </button>
                                <!-- Menu -->
                                <md-menu>
                                    <img ng-show="(item.attachmentUploadProgress != null || item.attachment != null) && item.attachment!=null"
                                            class="file-arrow-button"
                                            md-menu-origin
                                            ng-click="$mdOpenMenu()"
                                            src="/content/images/arrow-up-skinny.png"/>
                                    <md-menu-content>
                                        <!-- Download Attachment -->
                                        <md-menu-item><md-button ng-click="vm.downloadFile(item.attachment)">
                                            Download Attachment
                                        </md-button></md-menu-item>
                                        <md-menu-divider></md-menu-divider>
                                        <!-- Delete button -->
                                        <md-menu-item><md-button ng-click="vm.deleteFile(item)">
                                            <span style="color: orangered">Delete</span>
                                        </md-button></md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                                <!-- Progress Indicator -->
                                <md-progress-circular style="display:inline-block;"
                                                        md-diameter="20"
                                                        md-mode="{{vm.progressMode}}"
                                                        value="{{item.attachmentUploadProgress}}">
                                </md-progress-circular>
                            </td>

                        </tr>
                    </tbody>
                </table>
            </md-card>

            <!-- If no drawings are attached to this option, show the upload field.-->
            <md-card ng-if="vm.drawingsPresent() === false"
                     style="outline: 1px dashed lightgrey; outline-offset: -10px; background-color: white;"
                     data-title="Attachment" class="text-left"
                     ngf-drop="vm.uploadFile($files, 'attachment')"
                     ngf-multiple="true"
                     ngf-drag-over-class="'file-dragover'">
                <label style="text-align: center; padding-top: 20px;">
                    Attach Files
                </label>
                <md-card-content layout="row" layout-align="center">
                    <div>
                        <md-button class="md-raised"
                                   ngf-select="vm.uploadFile($files, 'attachment')"
                                   ngf-multiple="true">
                            <i class="material-icons" style="text-align: center; vertical-align: middle">
                                file_upload
                            </i>
                            Upload
                        </md-button>
                        <label style="display:inline-block;color: darkgrey; font-family: Roboto, 'Helvetica Neue', sans-serif; font-size: 1.6rem;">
                            or drag here.
                        </label>
                    </div>
                </md-card-content>
            </md-card>

        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       redi-allow-roles="['assessment_actions__selectcompliance']"
                       ng-disabled="SimulationOptionsForm.$invalid || (vm.option.updatedDrawingsRequired == true && vm.option.assessmentDrawings.length == 0 && vm.attachedDrawings.length == 0)"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

            <div class="clearfix"></div>
        </div>

    </div>

</form>

<style>

    md-card {
        box-shadow: none;
    }

    .file-row {
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }

        .file-row:hover .file-arrow-button {
            visibility: visible;
        }
        .file-row:hover .file-name-button {
            text-decoration: underline
        }

        .file-name-button {
            background: none;
            border: none;
            text-decoration: none;
            font-size: 16px;
            color: var(--thermarate-green);
        }

        .file-arrow-button {
            visibility: hidden;
            opacity: 0.8;
            margin-left: 4px;
            margin-bottom: -4px;
            width: 12px;
            height: auto;
            padding: 7px 4px;
            border-radius: 4px;
            cursor: pointer;
            transform: rotate(180deg);
        }
        .file-arrow-button:hover {
            background-color: #d1d1d1;
        }

</style>