<form name="jobeventform" class="main-content-wrapper" novalidate data-ng-controller='JobeventUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div>
                    <md-card>
                        <md-card-header>
                            Job Event
                        </md-card-header>
                        <md-card-content>

<!-- ******** Job ******** -->
                            <md-autocomplete md-input-name="jobId" md-autofocus 
                                         required
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.jobevent.job"
                                         md-search-text="vm.jobIdSearchText"
                                         md-items="item in vm.getjobs(vm.jobIdSearchText)"
                                         md-item-text="item.clientJobNumber"
                                         md-require-match
                                         md-floating-label="Job">
                                <md-item-template>
                                    <span md-highlight-text="vm.jobIdSearchText">{{item.clientJobNumber}}</span>
                                </md-item-template>
                                <div ng-messages="jobeventform.jobId.$error">
                                    <div ng-message="required">Job is required.</div>
                                </div>
                            </md-autocomplete>

<!-- ******** Event Type ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Event Type</label>
                                <md-select name="eventTypeCode"  
                                        ng-model="vm.jobevent.eventTypeCode">
                                    <md-option ng-value>none</md-option>
                                    <md-option ng-value="item.eventTypeCode" 
                                            ng-repeat="item in vm.eventTypeList track by item.eventTypeCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

<!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Description</label>
                                <input type="text" name="description" 
                                        ng-model="vm.jobevent.description"  
                                    />
                                <div ng-messages="jobeventform.description.$error">
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.jobevent"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="jobeventform.$invalid" ng-show="vm.jobevent.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.jobevent.jobEventId!=null && vm.jobevent.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.jobevent.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
