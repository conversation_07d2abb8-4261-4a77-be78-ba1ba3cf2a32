(function () {
    'use strict';

    var app = angular.module('app');
    // Directive to display Created By and Modified By line.
    // Requires consistent naming of created and modified values.
    app.directive('rdDisplayCreatedModified', ['$window', function ($window) {
        //Usage:
        //<div data-rd-display-created-modified ng-model="vm.MyModel"></div>
        var directive = {
            link: link,
            restrict: 'AE',
            require: 'ngModel',
            scope: {
                ngModel: '='
            }
        };
        return directive;

        function link(scope, element, attrs, ngModel) {
            scope.$watch('ngModel', function (newModel) {
                if (newModel != undefined) {
                    var wkText = "";
                    var moment = $window.moment;
                    var dateFormat = "DD/MM/YYYY h:mm a";
                    var crDt = newModel.createdOn;
                    var mdDt = newModel.modifiedOn;

                    if (newModel.createdByName != undefined) {
                        wkText = "Created by " + newModel.createdByName + " on " + moment(crDt).format(dateFormat) + ". ";
                    }
                    if (newModel.modifiedByName != undefined) {
                        wkText = wkText + "Last modified by " + newModel.modifiedByName + " on " + moment(mdDt).format(dateFormat) + ".";
                    }
                    element.html(wkText);
                    attrs.$set('class', 'rd-display-created-modified');
                }
            });
        }
    }]);
})();