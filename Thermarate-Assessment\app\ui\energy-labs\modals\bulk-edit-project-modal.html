<form name="BulkEditProjectModal"
      data-ng-controller='BulkEditProjectModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row"
                        ng-model="vm.data.bulkEditAction">
            <md-radio-button ng-value="'EDIT'">
                Edit
            </md-radio-button>
            <md-radio-button ng-value="'COPY'">
                Duplicate
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <fieldset id="edit-inputs"
                  ng-if="vm.data.bulkEditAction == 'EDIT'">
            <table class="bulk-edit-table-consistent-heights table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Active -->
                    <tr>
                        <td>
                            Active
                        </td>
                        <td>
                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="vm.data.isActive" ng-change="vm.handleIsActiveChange()">
                                </md-switch>
                            </div>
                        </td>
                    </tr>


                </tbody>
            </table>

        </fieldset>

        <!-- Apply to all levels options - only shown when toggle is ON -->
        <div ng-if="vm.data.isActive" style="margin: auto; width: max-content; margin-bottom: 5px;">
            <div style="font-weight: bold; margin-bottom: 15px;">Apply to:</div>
            <md-radio-group ng-model="vm.data.toggleChildrenIsActive">
                <md-radio-button ng-value="false" class="md-primary">
                    This level only
                </md-radio-button>
                <md-radio-button ng-value="true" class="md-primary">
                    All levels
                </md-radio-button>
            </md-radio-group>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected projects will be duplicated.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected projects will be deleted. </span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

        </div>

    </div>

</form>

<style>
</style>