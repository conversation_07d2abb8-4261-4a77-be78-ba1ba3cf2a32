(function () {
    'use strict';
    var controllerId = 'HomePlanSwitcherModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', homePlanSwitcherModalController]);
    function homePlanSwitcherModalController(common, $scope, $mdDialog) {

        let vm = this;

        vm.planViews = $scope.planViews;
        vm.planViewIndex = $scope.currentIndex;

        vm.prevImage = function($event) {
            stopPropagation($event);
            vm.runAnimationLeft = false;
            vm.runAnimationRight = false;
            setTimeout(() => {
                if (vm.planViewIndex !== 0) {
                    vm.planViewIndex--;
                }
                vm.runAnimationRight = true;
                $scope.$apply();
            }, 0);
        }

        vm.nextImage = function($event) {
            stopPropagation($event);
            vm.runAnimationLeft = false;
            vm.runAnimationRight = false;
            setTimeout(() => {
                if (vm.planViewIndex < vm.planViews.length - 1) {
                    vm.planViewIndex++;
                }
                vm.runAnimationLeft = true;
                $scope.$apply();
            }, 0);
        }

        function stopPropagation(event) {
          if(event && event.stopPropagation)
            event.stopPropagation();
        }

        vm.cancel = function() {
            console.log("TEST");
            $mdDialog.cancel();
        }

    }
})();