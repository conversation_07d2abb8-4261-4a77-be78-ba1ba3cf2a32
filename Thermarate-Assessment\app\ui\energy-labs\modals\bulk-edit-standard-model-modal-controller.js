(function () {
    'use strict';
    var controllerId = 'BulkEditStandardModelModalCtrl';
    angular.module('app')
    .controller(controllerId, ['common', '$scope', '$mdDialog', 'projectservice', bulkEditStandardModelModalController]);
    function bulkEditStandardModelModalController(common, $scope, $mdDialog, projectservice) {

        let vm = this;

        var thisProjectId = $scope.thisProjectId;
        var clientId = $scope.clientId;
        vm.projectEnergyLabsSettings = $scope.projectEnergyLabsSettings;

        // Get parent settings from scope
        vm.parentIsActive = $scope.parentIsActive;
        vm.parent3dModel = $scope.parent3dModel;
        vm.parentCostEstimate = $scope.parentCostEstimate;
        vm.parentDesignInsights = $scope.parentDesignInsights;

        // Get default values based on selected models
        vm.defaultIsActive = $scope.defaultIsActive;
        vm.defaultView3dFloorPlans = $scope.defaultView3dFloorPlans;
        vm.defaultCostEstimateEnabled = $scope.defaultCostEstimateEnabled;
        vm.defaultDesignInsightsEnabled = $scope.defaultDesignInsightsEnabled;

        projectservice.getForClient(clientId, false).then(
            result => {
                if (result == null)
                    return;
                vm.projectList = result.data;
                vm.projectList = vm.projectList.filter(x => x.projectId != thisProjectId);
                vm.isBusy = false;
            },
            error => {
                console.log(error);
                vm.isBusy = false
            }
        );

        vm.selectProject = function (project) {
            vm.projectList.forEach(x => x.selected = false);
            project.selected = true;
            vm.data.selectedProjectId = project.projectId;
        }

        vm.data = {
            bulkEditAction: "EDIT",
            // Use default values from selected models, but respect parent settings
            isActive: vm.parentIsActive ? (vm.defaultIsActive !== undefined ? vm.defaultIsActive : true) : false,
            view3dFloorPlans: vm.parent3dModel ? (vm.defaultView3dFloorPlans !== undefined ? vm.defaultView3dFloorPlans : true) : false,
            costEstimateEnabled: vm.parentCostEstimate ? (vm.defaultCostEstimateEnabled !== undefined ? vm.defaultCostEstimateEnabled : true) : false,
            designInsightsEnabled: vm.parentDesignInsights ? (vm.defaultDesignInsightsEnabled !== undefined ? vm.defaultDesignInsightsEnabled : true) : false,
            floorplanIsActive: vm.projectEnergyLabsSettings.varCategoryFloorplanActive,
            designOptionIsActive: vm.projectEnergyLabsSettings.varCategoryDesignOptionActive,
            facadeIsActive: vm.projectEnergyLabsSettings.varCategoryFacadeActive,
            specificationIsActive: vm.projectEnergyLabsSettings.varCategorySpecificationActive,
            configurationIsActive: vm.projectEnergyLabsSettings.varCategoryConfigurationActive,
            selectedProjectId: null
        };

        // Track original values to detect changes
        vm.originalValues = {
            isActive: vm.data.isActive,
            view3dFloorPlans: vm.data.view3dFloorPlans,
            costEstimateEnabled: vm.data.costEstimateEnabled,
            designInsightsEnabled: vm.data.designInsightsEnabled
        };

        // Handle toggle changes
        vm.handleToggleChange = function(property) {
            // If toggling OFF, set flag to apply to all levels
            // Backend will handle turning off child values
            if (!vm.data[property]) {
                vm.data[property + 'ApplyToAll'] = true;
                return;
            }

            // If toggling ON, show confirmation modal
            let settingNames = {
                'isActive': 'Active Setting',
                'view3dFloorPlans': '3D Model Setting',
                'costEstimateEnabled': 'Cost Estimate Setting',
                'designInsightsEnabled': 'Design Insights Setting'
            };

            let modalScope = $scope.$new();
            modalScope.settingName = settingNames[property];
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: true // Don't hide the parent modal
            }).then(function (response) {
                // Store the user's choice to apply to all levels
                vm.data[property + 'ApplyToAll'] = response.applyToAllLevels;
            }, function() {
                // If modal is canceled, revert the toggle
                vm.data[property] = false;
                vm.data[property + 'ApplyToAll'] = false;
            });
        };

        vm.confirm = function () {
            // Add flags for each toggle to indicate whether to apply to all levels
            if (vm.data.isActive && vm.data.isActive !== vm.originalValues.isActive && vm.data.isActiveApplyToAll === undefined) {
                vm.data.isActiveApplyToAll = true; // Default to apply to all
            }

            if (vm.data.view3dFloorPlans && vm.data.view3dFloorPlans !== vm.originalValues.view3dFloorPlans && vm.data.view3dFloorPlansApplyToAll === undefined) {
                vm.data.view3dFloorPlansApplyToAll = true; // Default to apply to all
            }

            if (vm.data.costEstimateEnabled && vm.data.costEstimateEnabled !== vm.originalValues.costEstimateEnabled && vm.data.costEstimateEnabledApplyToAll === undefined) {
                vm.data.costEstimateEnabledApplyToAll = true; // Default to apply to all
            }

            if (vm.data.designInsightsEnabled && vm.data.designInsightsEnabled !== vm.originalValues.designInsightsEnabled && vm.data.designInsightsEnabledApplyToAll === undefined) {
                vm.data.designInsightsEnabledApplyToAll = true; // Default to apply to all
            }

            $mdDialog.hide(vm.data);
        }

        vm.cancel = function() {
            $mdDialog.cancel();
        }

    }
})();