<section id="template-list-view" class="main-content-wrapper" data-ng-controller="TemplateListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Template"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.templateList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="templateList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="templateName" class="can-sort text-left">Template Name</th>
                        <th st-sort="emailSubject" class="can-sort text-left">Email Subject</th>
                        <th st-sort="categoryDescription" class="can-sort text-left">Category</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.templateList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="template-updateform({ templateId: row.templateId})">Select</md-button>  </td>
                        <td data-title="Template Name" class="text-left">{{::row.templateName }}</td>
                        <td data-title="Email Subject" class="text-left">{{::row.emailSubject }}</td>
                        <td data-title="Category" class="text-left">{{::row.categoryDescription }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
