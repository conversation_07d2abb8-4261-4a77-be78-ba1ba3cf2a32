// Name: certificationservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'certificationservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', 'coreLoop', 'compliancemethodservice', certificationservice]);

    function certificationservice(common, config, $http, Upload, coreLoop, compliancemethodservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'certification/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getCertification: getCertification,
            createCertification: createCertification,
            updateCertification: updateCertification,
            deleteCertification:deleteCertification,
            undoDeleteCertification: undoDeleteCertification,
            getAll,
            getRulesets,
            getSectorDeterminations,
            updateDataLinkedToCertification,
        };
            
        return service;

        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Certification list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting Certification List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }
        
        function getCertification(id) {
            return $http({
                url: baseUrl + 'Get',
                params: { id },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Certification: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createCertification(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Certification Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created Certification: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateCertification(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Certification Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Certification: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteCertification(id) {
            return $http({
                url: baseUrl + 'Delete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Certification: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteCertification(id) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { id },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Certification: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getRulesets() {

            return $http({
                url: baseUrl + 'GetRulesets',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Rulesets."));
        }

        function getSectorDeterminations() {

            return $http({
                url: baseUrl + 'getSectorDeterminations',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Sector Determinations."));
        }

        function updateDataLinkedToCertification(assessment, option) {

            if(option.certification.sectorDetermination != null)
                option.sectorDetermination = option.certification.sectorDetermination;
            
            if(option.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" ||
               option.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfELL") {
               compliancemethodservice.calculateHERCompliance(assessment, option);
            }

            coreLoop.computeEnergyUsage(option.proposed, option);
            if(option.complianceMethod.complianceMethodCode === 'CMPerfSolution' ||
                option.complianceMethod.complianceMethodCode === 'CMPerfSolutionDTS') {
                coreLoop.computeEnergyUsage(option.reference, option);
            }
        }

    }
})();
