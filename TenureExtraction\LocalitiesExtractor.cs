﻿using Microsoft.Data.Sqlite;
using System;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.SqlServer.Types;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts 'localities' (e.g. Suburb) information from a Geopackage file and stores in the database.
    /// https://public-services.slip.wa.gov.au/public/rest/services/SLIP_Public_Services/Boundaries/MapServer/14
    /// </summary>
    public class LocalitiesExtractor
    {
        private const int CHUNK_SIZE = 500; // Number of rows to return in each sqlite call.

        private SqlConnection sqlConnection;
        private string directory;
        
        private SqliteConnection sqliteConnection;
        
        private Logger logger;
        
        private const string LOCALITIES_ID = "234";
        
        public LocalitiesExtractor(string directory, string connectionString, Logger logger)
        {
            try
            {
                this.directory = directory;
                this.logger = logger;
                logger.Log("Attempting to initialize LocalitiesExtractor in directory" + directory);
                
                sqlConnection = new SqlConnection(connectionString);
                sqlConnection.Open();
                
                SqlServerTypes.Utilities.LoadNativeAssemblies(AppDomain.CurrentDomain.BaseDirectory);
                SQLitePCL.Batteries_V2.Init();
            }
            catch(Exception e)
            {
                logger.Log("Exception encountered when trying to initialize LocalitiesExtractor. Error was: " + e.Message);
                throw;
            }
        }

        public void Dispose()
        {
            if (this.sqlConnection != null && this.sqlConnection.State == ConnectionState.Open)
                this.sqlConnection.Close();

            this.sqlConnection?.Dispose();
            
            if (this.sqliteConnection != null && this.sqliteConnection.State == ConnectionState.Open)
                this.sqliteConnection.Close();

            this.sqliteConnection?.Dispose();
        }

        ~LocalitiesExtractor()
        {
            if (this.sqlConnection != null && this.sqlConnection.State == ConnectionState.Open)
                this.sqlConnection.Close();

            this.sqlConnection?.Dispose();
            
            if (this.sqliteConnection != null && this.sqliteConnection.State == ConnectionState.Open)
                this.sqliteConnection.Close();

            this.sqliteConnection?.Dispose();
        }

        private void CreatePostcodeIndex()
        {
            SqliteCommand c = new SqliteCommand("CREATE INDEX IF NOT EXISTS main.idx_postcode ON Localities_LGATE_234(postcode)", this.sqliteConnection);
            c.ExecuteNonQuery();
        }
        
        /// <summary>
        /// Extracts boundary data from the Sqlite file and updates our RSS_Suburb table with the boundary data.
        /// </summary>
        public async Task UpdateLocalitiesDataset(int? limitToCount = null)
        {
            // We are only interested in updating the boundary data, as the current RSS_Suburb database has data from 
            // all states, whereas this localities extractor is only for WA.
            // The DataTable class (as used for bulk-insert operations) is unsuitable for bulk-update. Given that this
            // process is not run frequently and is not too huge, we just fall back to inserting 1 by 1.
            
            
            string[] files = Directory.GetFiles(directory);
            var geopackagePath = files.Where(s => s.Contains(LOCALITIES_ID)).FirstOrDefault();

            if (string.IsNullOrEmpty(geopackagePath))
            {
                logger.Log("No localities dataset found. Continuing to next process without updating suburbs...");
                return;
            }
            
            logger.Log($"Extraction started for geopackage file: {geopackagePath}");
            
            this.sqliteConnection = new SqliteConnection("Filename=" + geopackagePath);
            this.sqliteConnection.Open();
            CreatePostcodeIndex();

            var debugCount = 0;

            // Loop over all rows in table and add.
            for(int r = 0; r < int.MaxValue; r++)
            {
                // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                if (limitToCount.HasValue && debugCount >= limitToCount)
                    break;
                
                var selectRowCmd = new SqliteCommand(
                    $"SELECT * FROM Localities_LGATE_234 ORDER BY postcode LIMIT {CHUNK_SIZE} OFFSET {r * CHUNK_SIZE}",
                    sqliteConnection);
                
                var reader = selectRowCmd.ExecuteReader();

                logger.Log($"Processing localities dataset rows {r * CHUNK_SIZE}->{ r * CHUNK_SIZE + CHUNK_SIZE} for Localities_LGATE_234");

                if (reader.HasRows == false)
                    break; // Break out of outer loop.

                while (reader.Read())
                {
                    // FOR DEBUGGING ONLY: Break out of loop early if necessary.
                    if (limitToCount.HasValue && debugCount >= limitToCount)
                        break;
                    
                    debugCount++;
                    
                    // Extract postcode (for matching) and boundary data.
                    string postcode = reader["postcode"].ToString();
                    string name = reader["name"].ToString();
                    
                    SqlGeography boundary = null;
                        
                    try
                    {
                        boundary     = Shared.GetSqliteGeometryAsSqlBoundary(reader);
                    }
                    catch (Exception e)
                    {
                        continue; // Invalid data for this row.
                    }

                    // Attempt to update suburb
                    try
                    {
                        var updateCmd = new SqlCommand(
                            "UPDATE dbo.RSS_Suburb " +
                            "SET BoundaryJson = @BoundaryJson " +
                            "WHERE PostCode = @PostCode AND name LIKE @Name",
                            sqlConnection);
                        
                        updateCmd.Parameters.AddWithValue("BoundaryJson", Shared.ConvertSqlGeographyToJson(boundary));
                        updateCmd.Parameters.AddWithValue("PostCode", postcode);
                        updateCmd.Parameters.AddWithValue("Name", "%" + name + "%");

                        updateCmd.ExecuteNonQuery();
                    }
                    catch(Exception e)
                    {
                        logger.Log($"Exception encountered while updating boundary information for. " +
                                   $"Range: {r * CHUNK_SIZE} -> { r * CHUNK_SIZE + CHUNK_SIZE}. " +
                                   $"Postcode: {postcode}" + 
                                   $"Error: " + e.Message);
                    }
                }
            }
        }
    }
}
