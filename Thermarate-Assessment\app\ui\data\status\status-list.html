<section id="status-list-view" class="main-content-wrapper" data-ng-controller="StatusListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Status"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.statusList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="statusList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="statusCode" class="can-sort text-center">Status Code</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="hidden" class="can-sort text-center">Hidden</th>
                        <th st-sort="statusTypeCode" class="can-sort text-left">Status Type Code</th>
                        <th st-sort="sortOrder" class="can-sort text-right">Sort Order</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.statusList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="status-updateform({ statusCode: row.statusCode})">Select</md-button>  </td>
                        <td data-title="Status Code" class="text-center">{{::row.statusCode }}</td>
                        <td data-title="Description" class="text-left">{{::row.description }}</td>
                        <td data-title="Hidden" class="text-center"><span ng-bind-html='row.hidden | tickCross'></span></td>
                        <td data-title="Status Type Code" class="text-left">{{::row.statusTypeCode }}</td>
                        <td data-title="Sort Order" class="text-right">{{::row.sortOrder | number }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
