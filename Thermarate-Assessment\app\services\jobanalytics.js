// Name: jobanalyticsservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'jobanalyticsservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', jobanalyticsservice]);

    function jobanalyticsservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var canceller = null;
        const baseUrl = config.servicesUrlPrefix + 'jobanalytics/';

        var service = {
            getAnalytics: getAnalytics,
        };
            
        return service;
        
        function getAnalytics(listSize, certificationId, complianceMethodCode, includeVirtualSims, version1Only, fields, filterOptions, appliedFilters, searchFilter, sort) {
            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetAnalytics';

            let filterData = {
                fields,
                filterOptions,
                appliedFilters: {
                    ...appliedFilters,
                    assessmentVersion: version1Only ? [1] : appliedFilters.assessmentVersion
                }
            };
            filterData.paging = common.buildqueryparameters.build(null, 10000, 1, sort?.field != null ? [sort] : null, searchFilter);

            return $http({
                url: wkUrl,
                params: { listSize: listSize, certificationId: certificationId, complianceMethodCode: complianceMethodCode, includeVirtualSims: includeVirtualSims },
                data: filterData,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error retrieving analytics: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
