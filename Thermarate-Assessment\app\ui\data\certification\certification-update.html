<form name="certificationform" class="main-content-wrapper" novalidate data-ng-controller='CertificationUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="column">
                <div flex="100">
                    <md-card>
                        <md-card-header>
                            Certification
                        </md-card-header>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- ******** Title ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Title</label>
                                    <input type="text" name="certificationTitle"
                                           ng-model="vm.certification.title"
                                           md-autofocus
                                           ng-disabled="!vm.newRecord"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="certificationform.certificationCode.$error">
                                        <div ng-message="required">Title is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- ******** Description ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Description</label>
                                    <input type="text" name="certificationDescription"
                                           ng-model="vm.certification.description"
                                           md-maxlength="500"/>
                                </md-input-container>

                                <!-- Chenath Area Correction Factor -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Chenath Area Correction Factor</label>
                                    <md-select required
                                               name="chenathRulesetCode"
                                               ng-model="vm.certification.chenathRulesetCode">
                                        <md-option ng-value="ruleset.rulesetCode"
                                                   ng-repeat="ruleset in vm.enabledRulesets()">
                                            CHEANATH ({{ruleset.title}})
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Decimal Star Bands Ruleset -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Decimal Star Bands</label>
                                    <md-select required
                                               name="decimalStarbandsRulesetCode"
                                               ng-model="vm.certification.decimalStarbandRulesetCode">
                                        <md-option ng-value="ruleset.rulesetCode"
                                                   ng-repeat="ruleset in vm.enabledRulesets()">
                                            {{ruleset.title}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Maximum Heating & Cooling Ruleset -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Maximum Heating and Maximum Cooling</label>
                                    <md-select required
                                               name="heatingCoolingRulesetCode"
                                               ng-model="vm.certification.heatingAndCoolingRulesetCode">
                                        <md-option ng-value="ruleset.rulesetCode"
                                                   ng-repeat="ruleset in vm.enabledRulesets()">
                                            {{ruleset.title}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Glazing Calculator Ruleset -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Glazing Calculator</label>
                                    <md-select required
                                               name="glazingCalculatorRulesetCode"
                                               ng-model="vm.certification.glazingCalculatorRulesetCode">
                                        <md-option ng-value="ruleset.rulesetCode"
                                                   ng-repeat="ruleset in vm.enabledRulesets()">
                                            {{ruleset.title}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Sector Determination -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Sector Determination</label>
                                    <md-select required
                                               name="sectorDeterminationCode"
                                               ng-model="vm.certification.sectorDeterminationCode">
                                        <md-option ng-value="sector.sectorDeterminationCode"
                                                   ng-repeat="sector in vm.sectors">
                                            {{sector.title}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                            </fieldset>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.certification"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>

            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="certificationform.$invalid || vm.editPermission == false"
                           ng-show="vm.certification.deleted!=true"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.certification.title != null && vm.certification.deleted != true"
                           ng-confirm-click="vm.delete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.certification.deleted==true"
                           ng-confirm-click="vm.undoDelete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
