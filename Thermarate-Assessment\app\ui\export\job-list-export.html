<form id="job-list-export-view" name="jobListExportForm" class="main-content-wrapper" data-ng-controller="JobListExportCtrl as vm">
    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-subtitle="{{vm.subtitle}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div flex="100">
                    <md-card>
                        <md-card-header>
                            <span class="md-headline">Specify CSV Export Filters</span>
                        </md-card-header>
                        <md-card-content>

                            <!-- ******** From Date ******** -->
                            <md-input-container class="md-block" flex-gt-sm="40">
                                <label>From Date</label>
                                <md-datepicker ng-model="vm.filter.fromDate"
                                               name="fromDate"
                                               required
                                               md-placeholder="From date">
                                </md-datepicker>
                                <div class="validation-messages" ng-messages="jobListExportForm.fromDate.$error">
                                    <div ng-message="valid">The entered value is not a date!</div>
                                    <div ng-message="required">This date is required!</div>
                                    <div ng-message="mindate">Date is too early!</div>
                                    <div ng-message="maxdate">Date is too late!</div>
                                    <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                                </div>
                            </md-input-container>

                            <!-- ******** To Date ******** -->
                            <md-input-container class="md-block" flex-gt-sm="40">
                                <label>To Date</label>
                                <md-datepicker ng-model="vm.filter.toDate"
                                               name="toDate"
                                               required
                                               md-placeholder="To date">
                                </md-datepicker>
                                <div class="validation-messages" ng-messages="jobListExportForm.toDate.$error">
                                    <div ng-message="valid">The entered value is not a date!</div>
                                    <div ng-message="required">This date is required!</div>
                                    <div ng-message="mindate">Date is too early!</div>
                                    <div ng-message="maxdate">Date is too late!</div>
                                    <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Client ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-autocomplete md-input-name="clientId" md-autofocus
                                                 flex="95"
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.client"
                                                 md-search-text="vm.clientIdSearchText"
                                                 md-items="item in vm.getclients(vm.clientIdSearchText)"
                                                 md-item-text="item.clientName"
                                                 md-floating-label="Client"
                                                 md-selected-item-change="vm.clientSelect(item)">
                                    <md-item-template>
                                        <i class="fa fa-address-book-o" aria-hidden="true" ng-if="item.clientId=='FAKE_ID_FOR_FAKE_CLIENT'">&nbsp;</i><span md-highlight-text="vm.clientIdSearchText">{{item.clientName}}</span>
                                    </md-item-template>
                                </md-autocomplete>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('clientId')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Building Description ******** -->
                            <div layout="row" flex="100" flex-gt-sm="50" flex-gt-md="40" layout-wrap>
                                <md-autocomplete md-input-name="projectDescriptionCode"
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.projectDescription"
                                                 md-search-text="vm.projectDescriptionSearchText"
                                                 md-items="item in vm.getProjectDescriptionList(vm.projectDescriptionSearchText)"
                                                 md-item-text="item.description"
                                                 md-floating-label="Building Description"
                                                 md-selected-item-change="vm.projectDescriptionSelect(item)"
                                                 flex="95">
                                    <md-item-template>
                                        <span md-highlight-text="vm.projectDescriptionSearchText" md-highlight-flags="^i">{{item.description}}</span>
                                    </md-item-template>
                                </md-autocomplete>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clearProjectDescription()"><i class="material-icons">clear</i></md-button>
                                </div>
                                <md-input-container ng-if="vm.filter.projectDescriptionCode=='PDOther'" class="md-block" flex="100">
                                    <div layout="row" layout-wrap>
                                        <div flex="90">
                                            <label>Building Description Other</label>
                                            <input type="text" name="projectDescriptionCodeOther"
                                                   ng-model="vm.filter.projectDescriptionOther" />
                                        </div>
                                        <div flex="5">
                                            <md-button class="md-icon-button" ng-click="vm.clearProjectDescription()"><i class="material-icons">clear</i></md-button>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Design Field (Optional) -->
                            <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <label>Design</label>
                                <input ng-change="vm.designChanged()"
                                       type="text"
                                       name="design"
                                       ng-model="vm.design" />
                            </md-input-container>

                            <!-- ******** Status ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>Status</label>
                                    <md-select name="statusCode"
                                               ng-model="vm.filter.statusCode">
                                        <md-option ng-value="item.statusCode"
                                                   ng-repeat="item in vm.statusList track by item.statusCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('statusCode')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Baseline Assessment Method ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>Baseline Assessment Method</label>
                                    <md-select name="preliminaryComplianceMethodCode"
                                               ng-model="vm.filter.preliminaryComplianceMethodCode">
                                        <md-option ng-value="item.complianceMethodCode"
                                                   ng-repeat="item in vm.complianceMethodList track by item.complianceMethodCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('preliminaryComplianceMethodCode')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Compliance Option Required ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>Compliance Option Required</label>
                                    <md-select name="complianceOptionRequired"
                                               ng-model="vm.filter.complianceOptionRequired">
                                        <md-option value="Yes">Yes</md-option>
                                        <md-option value="No">No</md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('complianceOptionRequired')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Final Assessment Method ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>Final Assessment Method</label>
                                    <md-select name="complianceMethodCode"
                                               ng-model="vm.filter.finalComplianceMethod">
                                        <md-option ng-value="item.complianceMethodCode"
                                                   ng-repeat="item in vm.complianceMethodList track by item.complianceMethodCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('finalComplianceMethod')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Assessor Id ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-autocomplete md-input-name="assignedToAssessorUserId"
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.assignedToAssessorUser"
                                                 md-search-text="vm.assignedToAssessorUserIdSearchText"
                                                 md-items="item in vm.getemployees(vm.assignedToAssessorUserIdSearchText)"
                                                 md-item-text="item.fullName"
                                                 md-floating-label="Assigned Assessor"
                                                 md-selected-item-change="vm.assessorSelect(item)"
                                                 flex="95">
                                    <md-item-template>
                                        <span md-highlight-text="vm.assignedToAssessorUserIdSearchText">{{item.fullName}}</span>
                                    </md-item-template>
                                </md-autocomplete>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('assessorId')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Local Government Authority ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>Local Government Authority</label>
                                    <md-select name="localGovernmentAuthority"
                                               ng-model="vm.filter.localGovernmentAuthority">
                                        <md-option ng-value="item"
                                                   ng-repeat="item in vm.localGovernmentAuthorities">
                                            {{item}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('localGovernmentAuthority')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** Nat HERS Climate Zone ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>NatHERS Climate Zone</label>
                                    <md-select name="natHERSClimateZoneCode"
                                               ng-model="vm.filter.natHERSClimateZoneCode">
                                        <md-option ng-value="item.natHERSClimateZoneCode"
                                                   ng-repeat="item in vm.natHERSClimateZoneList track by item.natHERSClimateZoneCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('natHERSClimateZoneCode')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                            <!-- ******** NCC Climate Zone ******** -->
                            <div layout-gt-sm="row" flex="100" flex-gt-sm="50" flex-gt-md="40">
                                <md-input-container class="md-block" flex="95">
                                    <label>NCC Climate Zone</label>
                                    <md-select name="nccClimateZoneCode"
                                               ng-model="vm.filter.nccClimateZoneCode">
                                        <md-option ng-value="item.nccClimateZoneCode"
                                                   ng-repeat="item in vm.nccClimateZoneCode track by item.nccClimateZoneCode">
                                            {{item.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                                <div flex="5">
                                    <md-button class="md-icon-button" ng-click="vm.clear('nccClimateZoneCode')"><i class="material-icons">clear</i></md-button>
                                </div>
                            </div>

                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           redi-allow-roles="['naviation_menu__energylab']" 
                           ng-disabled="jobListExportForm.$invalid" ng-click="vm.submit()">Export</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
</form>
