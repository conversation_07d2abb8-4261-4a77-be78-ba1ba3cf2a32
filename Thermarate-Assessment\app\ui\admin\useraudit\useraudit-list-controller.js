(function () {
    // The UserauditListCtrl provides the behaviour for User Audit information.
    'use strict';
    var controllerId = 'UserauditListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'userauditservice', userauditListController]);
function userauditListController($rootScope, $scope, $mdDialog, userauditservice) {
    // The model for this form 
    var vm = this;
    var modalInstance = {};
    vm.spinnerOptions = {};
    vm.isBusy = false;
    vm.title = "User Audit List";
    vm.userauditList = [];
    vm.listFilter = "";
    vm.actionButtons = [];
    vm.totalRecords = 0;
    vm.showingFromCnt = 0;
    vm.showingToCnt = 0;
    vm.filterOptions = [{ code: 'All', name: 'All' }, 
                        ];
    vm.currentFilter = "All";
    vm.currentQuery = {};

    //Repopulate the List after Refresh Page
    vm.refreshList = function (filter) {
        vm.callServer(null);
    };

    var saveTableState = null;
    vm.callServer = function callServer(tableState) {
        if (tableState != null) {
            saveTableState = tableState;
        }
        if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
            return;
        }

        var pagination = saveTableState.pagination;

        var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
        var pageSize = pagination.number || 100;  // Number of entries showed per page.
        var pageIndex = (start / pageSize) + 1;

        vm.isBusy = true;
        var sort = {};
        if (saveTableState.sort != null) {
            sort.field = saveTableState.sort.predicate;
            sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
        }
        var filter = null;
        if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
            var val = saveTableState.search.predicateObject.$;
            // Adjust here for the columns quick search will search.
            filter = [{ field: "userName", operator: "startswith", value: val, logic: "or" }, { field: "eventType", operator: "startswith", value: val, logic: "or" },
                      { field: "eventDescription", operator: "contains", value: val }];
        }
        if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
            filter = vm.currentQuery.filter;
        }

        userauditservice.getListCancel();
        userauditservice.getList(vm.textSearch, pageSize, pageIndex, sort, filter)
            .then(function (result) {
                if (result == undefined || result == null) {
                    // Its been cancelled so get out of here.
                    return;
                }
                vm.currentFilter = userauditservice.currentFilter();
                vm.userauditList = result.data;
                vm.totalRecords = result.total;
                saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                vm.showingFromCnt = vm.userauditList.length > 0 ? start + 1 : 0;
                vm.showingToCnt = start + result.data.length;
                vm.isBusy = false;
            },
            function (error) {
                vm.isBusy = false;
            });
    };

    /* Show Modal for a New Useraudit */
    vm.newRecord = function () {
        var modalScope = $rootScope.$new();
        modalScope.viewMode = "New";
        var modalOptions = {
            templateUrl: 'app/ui/admin/useraudit/useraudit-detail.html',
            controller: 'UserauditDetailCtrl as vm',
            scope: modalScope,
            resolve: {
                viewMode: function () {
                    return 'New';
                }
            }
        };
        modalScope.modalInstance = $mdDialog.show(modalOptions);
        modalScope.modalInstance.then(function (data) {
            // Returned from modal, so refresh list.
            vm.refreshList();
        }, function () {
            // Cancelled.
        })['finally'](function () {
            modalScope.modalInstance = undefined  // <--- This fixes
        });
    }

}
})();
