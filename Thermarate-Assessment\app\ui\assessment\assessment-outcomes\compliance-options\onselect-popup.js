(function () {
    'use strict';
    let controllerId = 'OnselectPopupCtrl';
    angular
        .module('app')
        .controller(controllerId, ['common', '$scope', 'fileservice', '$mdDialog', 
            'Upload', 'assessmentdrawingservice', 'assessmentcomplianceoptionservice',
            onselectPopupController])

    function onselectPopupController(common, $scope,  fileservice, $mdDialog, 
         Upload, assessmentdrawingservice, assessmentcomplianceoptionservice) {

        var $q = common.$q;
        let vm = this;
        vm.progressMode = "determinate";

        vm.assessment = $scope.assessment;
        vm.option = $scope.option;
        vm.option.assessmentDrawings = vm.option.assessmentDrawings.filter(d => d.isShownToClient);
        vm.purchaseOrderCode = $scope.purchaseOrderCode;

        // This is the target list of drawings which will be uploaded
        // to this option (Erasing any others...?).
        vm.attachedDrawings = [];
        vm.isBusy = false;

        vm.confirm = async function () {

            try {
                vm.isBusy = true;

                // Attach new files to upload 
                vm.option.filesInOrder = vm.attachedDrawings?.map(x => x.attachment);

                // Update compliance option. This also contains the returned 'filesInOrder' prop used to kickoff
                // the PDF processing (similar to how it works for the new-job window).
                await assessmentcomplianceoptionservice.updateAssessmentComplianceOption(vm.option);

                vm.isBusy = false;

                $mdDialog.hide();

            } catch (e) {
                vm.isBusy = false;
                throw 'Error updating compliance option. Please try again.';
            }
        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        /** Deletes any drawings already attached to the current simulation. */
        vm.deleteAllDrawings = function () {

            let drawings = vm.option.assessmentDrawings;

            for (let i = 0; i < drawings.length; i++) {

                let drawing = drawings[i];
                assessmentdrawingservice.deleteAssessmentDrawing(drawing.assessmentDrawingId);
            }

            // Set to empty list ready to accept new drawings...
            vm.option.assessmentDrawings = [];
        }

        vm.drawingsPresent = function() {
            const drawings = vm.option.assessmentDrawings?.filter(x => x.deleted == false && x.archived == false && x.isShownToClient);
            return drawings?.length > 0;
        }

        // FILE UPLOAD CODE ------------------------------------------------------------------------------------
        // COPY PASTE FROM job-update controller.
        // Should really be in a service IMO.

        vm.uploadFile = function (files, field) {

            if (vm.option.assessmentDrawings == null) {
                vm.option.assessmentDrawings = [];
            }

            files.forEach(file => uploadFile(file, field));
        }

        function uploadFile($file, field) {
            vm.uploadBusy = true;
            let target = { id: Math.random() };

            vm.attachedDrawings.push(target);
            if ($file == undefined || $file == null) {
                return;
            }
            target[field + "UploadProgress"] = null;
            console.log(target);

            var url = "../api/Assessment/UploadFile?assessmentId=" + vm.assessment.assessmentId;
            url += "&jobId=" + vm.assessment.jobId;
            url += `&category=Assessment Drawing`
            url += `&classification=Generic`

            var promise = Upload.upload({
                url: url,
                method: "POST",
                file: $file
            });

            promise.progress(function (evt) {
                target[field + "UploadProgress"] = 80 * (evt.loaded / evt.total); // By 80 to allow server time to do stuff.
            }).success(async function (data) {

                target[field] = data;
                target[field + "UploadProgress"] = null;

                // Drawings will begin processing when/if this is vm.confirmed

            }).error(function () {
                vm.uploadBusy = false;
            });
        }

        vm.downloadFile = function (fileDto) {
            var a = document.createElement('a');
            a.href = fileDto.url;
            a.setAttribute('download', fileDto.fileName);
            a.target = '_blank';
            a.click();
        }

        function getFilesList() {
            if (vm.jobId != null && vm.jobId != undefined) {
                var filter = [{ field: "JobId", operator: "eq", value: vm.jobId, valueType: "guid?" }]
                fileservice.getList(null, null, null, null, null, null, filter).then(function (data) {
                    if (data.data != null) {
                        vm.filesList = data.data;
                    }
                });
            }
        }

        vm.fileDownload = function (rowdata) {
            var link = document.createElement("a");
            link.download = rowdata.displayName;
            link.href = rowdata.url;
            link.click();
        }

        vm.fileRemove = function (rowdata) {
            fileservice.deleteFile(rowdata.fileId);
            getFilesList();
        }

        vm.deleteFile = function (item) {
            if (!item.attachment) { return; }
            var promise = null;
            var file = null;

            //Find file in Target list
            if (vm.attachedDrawings != undefined && vm.attachedDrawings != null) {
                for (var ii = 0; ii < vm.attachedDrawings.length; ii++) {
                    if (vm.attachedDrawings[ii].attachment && vm.attachedDrawings[ii].attachment.fileId == item.attachment.fileId) {
                        file = vm.attachedDrawings[ii];
                        break;
                    }
                }
            }

            if (file && file.splitFiles && file.splitFiles.length > 0) {
                //Remove parent and split files
                var fileIds = _.map(file.splitFiles, function (rec) { return rec.fileId });
                promise = fileservice.deleteSplitFiles(item.attachment.fileId, fileIds);
            } else {
                //Remove file
                promise = fileservice.deleteFile(item.attachment.fileId);
            }

            //Remove file from Target list
            promise.then(function () {
                vm.attachedDrawings = _.without(vm.attachedDrawings, _.findWhere(vm.attachedDrawings, {
                    id: file.id
                }));
            });
        }

        // END FILE UPLOAD CODE --------------------------------------------------------------------------------
    }
    // END
})();