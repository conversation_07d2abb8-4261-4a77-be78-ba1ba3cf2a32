(function () {
    'use strict';
    var controllerId = 'ExternalLoginVerifyCtrl';

    angular.module('app').controller(
        controllerId, 
        ['$location', 'security', externalLogin]
    );

    function externalLogin($location, security) {

        var vm = this;

        let urlArgs = new URLSearchParams(window.location.href.substring(window.location.href.indexOf('?')+1, window.location.href.length));

        let ssoType = urlArgs.get("type");
        let ssoCode = urlArgs.get('code');

        if (ssoCode != null && ssoCode != '') {
            let ssoLoginFun = ssoType == "google" ? security.googleLogin : security.microsoftLogin;
            ssoLoginFun(ssoCode).then(response => {
                if (response?.status == 200) {
                    $location.path('/home');
                } else {
                    $location.path('/login');
                }
            });
        } else {
            $location.path('/login');
        }

    }

})();