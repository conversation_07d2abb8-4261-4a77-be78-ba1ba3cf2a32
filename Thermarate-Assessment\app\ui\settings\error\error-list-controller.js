(function () {
    // The ErrorListCtrl provides the behaviour for Error information.
    'use strict';
    var controllerId = 'ErrorListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'errorservice', 'daterangehelper', errorListController]);
function errorListController($rootScope, $scope, $mdDialog, errorservice, daterangehelper) {
    // The model for this form 
    var vm = this;
    var modalInstance = {};
    vm.spinnerOptions = {};
    vm.isBusy = true;
    vm.title = "System Log";
    vm.errorList = [];
    vm.listFilter = "";
    vm.actionButtons = [];
    vm.filterOptions = [{ code: 'All', name: 'All' }];
    vm.currentFilter = "All";
    vm.totalRecords = 0;
    vm.showingFromCnt = 0;
    vm.showingToCnt = 0;
    vm.currentQuery = {};
    vm.queryModel = {
        canSave: false,
        fields: [
            {
                name: 'message',
                description: 'message',
                dataType: 'string',
                operators: []
            },
            {
                name: 'type',
                description: 'type',
                dataType: 'string',
                operators: []
            },
            {
                name: 'source',
                description: 'source',
                dataType: 'string',
                operators: []
            },
            {
                name: 'user',
                description: 'user',
                dataType: 'string',
                operators: []
            },
            {
                name: 'timeUtc',
                description: 'Time Utc',
                dataType: 'date',
                operators: []
            },
            {
                name: 'application',
                description: 'application',
                dataType: 'string',
                operators: []
            },
        ],
    };

    var persistRangeName = "errorList-DtRange";
    vm.rptDateRange = daterangehelper.getDefaultRange('Today', persistRangeName);
    vm.ranges = daterangehelper.getRanges();

    //Repopulate the Error List after Refresh Page
    vm.refreshList = function (filter) {
        vm.callServer(null ,filter);
        localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
    };

    var saveTableState = null;
    vm.callServer = function callServer(tableState) {
        if (tableState != null) {
            saveTableState = tableState;
        }
        if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
            return;
        }

        var pagination = saveTableState.pagination;

        var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
        var pageSize = pagination.number || 10;  // Number of entries showed per page.
        var pageIndex = (start / pageSize) + 1;

        vm.isBusy = true;
        var sort = {};
        if (saveTableState.sort != null) {
            sort.field = saveTableState.sort.predicate;
            sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
        }
        var filter = null;
        if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
            var val =  saveTableState.search.predicateObject.$;
            filter = [{ field: "message", operator: "contains", value: val, logic:"or" }, 
                        { field: "user", operator: "contains", value: val, logic: "or" },
                        { field: "source", operator: "contains", value: val }];
        }
        if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
            filter = vm.currentQuery.filter;
        }
        daterangehelper.correctRangeDates(vm.rptDateRange);
        errorservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
            .then(function (result) {
                vm.currentFilter = errorservice.currentFilter();
                vm.errorList = result.data;
                vm.totalRecords = result.total;
                saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                vm.showingFromCnt = vm.errorList.length > 0 ? start + 1 : 0;
                vm.showingToCnt = start + result.data.length;
                vm.isBusy = false;
            });
    };

   //refreshList();

    /* Show Modal for a New Error */
    vm.newRecord = function () {
        var modalScope = $rootScope.$new();
        modalScope.viewMode = "New";
        var modalOptions = {
            templateUrl: 'app/ui/settings/error/error-detail.html',
            scope: modalScope,
            resolve: {
                viewMode: function () {
                    return 'New';
                }
            }
        };
        modalScope.modalInstance = $mdDialog.show(modalOptions);
        modalScope.modalInstance.then(function (data) {
            // Returned from modal, so refresh list.
            vm.refreshList();
        }, function () {
            // Cancelled.
        })['finally'](function () {
            modalScope.modalInstance = undefined  // <--- This fixes
        });
    }

}
})();
