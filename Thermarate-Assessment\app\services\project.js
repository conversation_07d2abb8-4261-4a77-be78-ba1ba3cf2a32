// Name: projectservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'projectservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'Upload', projectservice]);

    function projectservice(common, config, $http, Upload) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'project/';


        function getList() {
            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';

            //Get error List from the Server
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Project list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: false,
            }).then(success, fail)

            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {
                var msg = "Error getting Project List: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }

        function getForClient(clientId, forFilter, pageSize, pageIndex, sort, filter, onlyActive = true) {
            var params = { clientId: clientId, onlyActive: onlyActive };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, null);
            return $http({
                url: baseUrl + 'GetForClient',
                method: 'GET',
                params: params,
                cache: false,
            }).then(success, fail)

            function success(resp) { return resp?.data }
            function fail(error) {
                var msg = "Error getting Project List: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }

        }

        function getProject(id) {
            return $http({
                url: baseUrl + 'Get',
                params: { id },
                method: 'GET',
                cache: false,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createProject(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Project Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created Project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateProject(data, showSuccess = true) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                if (showSuccess) {
                    log.logSuccess("Project Changes Saved");
                }
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteProject(projectId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { projectId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteProject(projectId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { projectId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function copyProject(projectId) {
            return $http({
                url: baseUrl + 'Copy',
                params: { projectId },
                method: 'POST',
            }).then(success, fail)

            function success(resp) {
                log.logSuccess("Home Design copied successfully.");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error copying Home Design: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateIsActive(standardHomeModelId, isActive, toggleChildren = true) {
            const url = baseUrl + 'UpdateIsActive';
            return $http({
                url: url,
                params: { standardHomeModelId, isActive, toggleChildren },
                method: 'POST',
            })
              .then(success, fail);

            function success(resp) {
                return resp?.data;
            }
            function fail(error) {
                var msg = "Failed to update 'isActive': " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }

        function getUsersForProject(projectId) {
            return $http({
                url: baseUrl + 'GetUsersForProject',
                params: { projectId: projectId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getProjectsForUser(userId) {
            return $http({
                url: baseUrl + 'GetProjectsForUser',
                params: { userId: userId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateUsersForProject(projectId, userIdList) {
            return $http({
                url: baseUrl + 'UpdateUsersForProject',
                params: { projectId: projectId },
                data: userIdList,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error updating users for project: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateProjectsForUser(userId, projectIdList) {
            return $http({
                url: baseUrl + 'UpdateProjectsForUser',
                params: { userId: userId },
                data: projectIdList,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error updating projects for user: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateProjectsList(projectList) {
            return $http({
                url: baseUrl + 'UpdateProjectsList',
                data: projectList,
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error updating projects' list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        return {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getProject,
            createProject: createProject,
            updateProject,
            deleteProject,
            undoDeleteProject,
            copyProject,
            getAll,
            getForClient,
            updateIsActive,
            getUsersForProject: getUsersForProject,
            getProjectsForUser: getProjectsForUser,
            updateUsersForProject: updateUsersForProject,
            updateProjectsForUser: updateProjectsForUser,
            updateProjectsList: updateProjectsList,
        };
    }
})();
