SELECT 
    [JobId],
    [CurrentAssessmentId],
    [JobReference],
    [ClientId],
    [ClientName],
    [StatusCode],
    [JobStatusDescription],
    [JobCreatedOn],
    [JobModifiedOn],
    [JobDeleted],
    [AssessorUserId],
    [AssessorFullName],
    [AssessmentPriorityCode],
    [AssessmentDesign],
    [AssessmentStatusCode],
    [AssessmentCreatedOn],
    [AssessmentCerficateDate],
    [AssessmentDeleted],
    [ClientJobNumber],
    [ProjectOwner],
    [OrderDate],
    [Address],
    [OrderTypeDescription],
    [CreatorFullName],
    [ClientAssigneeFullName],
    [ProjectDescriptionCode],
    [ProjectDescriptionDescription],
    [ComplianceMethodCode],
    [ComplianceMethodDescription],
    [AssessmentVersion],
    [AssessmentProjectDetailCreatedOn],
    [AssessmentProjectDetailDeleted],
    [AssessmentPriorityDescription],
    [AssessmentNatHERSClimateZone],
    [AssessmentNCCClimateZone],
    [NorthOffset],
    [Heating],
    [Cooling],
    [TotalEnergyLoad],
    [CalculatedHouseEnergyRating],
    [AssessmentProjectDetailLotDescription],
    [AssessmentProjectDetailLotWidth],
    [AssessmentProjectDetailLotLength],
    [AssessmentProjectDetailParcelArea],
    [AssessmentProjectDetailCornerBlock],
    [AssessmentProjectDetailRearLaneway],
    [AssessmentProjectDetailRuralLot],
    [CertificationId],
    [CertificationTitle],
    [GarageLocation],
    [OutdoorLivingLocation],
    [AssessmentProjectDetailComplianceCost],
    [AssessmentProjectDetailSuburb],
    [AssessmentProjectDetailLGA],
    [AssessmentProjectDetailLGAShort],
    CASE WHEN [AssessmentProjectDetailLGAShort] IS NOT NULL THEN [AssessmentProjectDetailLGAShort] ELSE REPLACE([AssessmentProjectDetailLGA], 'city of ', '') END
  FROM [dbo].[RSS_JobAssessmentProjectDetail_View]
  ORDER BY [JobReference]