angular.module('security.account.form', ['services.localizedMessages'])

// The AccountFormController provides the behaviour behind a reusable form to allow users to change their account details (such as password).
// This controller and its template (account/form.tpl.html) are used in a modal dialog box by the security service.
    .controller('AccountFormController', ['$scope', 'common', 'security', 'localizedMessages', '$sce', 'userservice', function ($scope, common, security, localizedMessages, $sce, userservice) {
        
    var log = common.logger;

    // The model for this form 
    $scope.user = {};
    $scope.user.email = security.currentUser.userId;
    $scope.user.currentPassword = null;
    $scope.user.newPassword = null;
    $scope.user.confirmNewPassword = null;

    $scope.signaturePicture = null;
    // Get user data
    $scope.user = null;
    if (security.currentUser.userId != null) {
        userIdPromise = userservice.getUserByAspNetId(security.currentUser.userId)
        .then(function (data) {
            if (data != null) {
                $scope.user = data;
                console.log(data);
                console.log($scope.user);
            }
        });
    }

    $scope.signatureUpdate = false;
    $scope.updateSignature = function () {
        $scope.signatureUpdate = true;
    }

    $scope.sanitizeSignature = function () {
        // IF has SVG image, use SVG version
        if ($scope.user.signatureSVGImage) {
            $scope.signaturePicture = $sce.trustAsHtml($scope.user.signatureSVGImage);
        }
        // ELSE IF has Base64 version, user Base64 version
        else if ($scope.user.signatureBase64Image) {
            $scope.signaturePicture = "<img src=\"" + vm.user.signatureBase64Image + "\" width=\"250\">";
        }
    }

    // Call the server to save Signature
    $scope.saveSignature = function () {
        userservice.updateUser($scope.user)
            .then(function (data) {
            if (data != null) {
                $scope.user = data;
            }
        });
    }

    // Any error message from failing to login
    $scope.authError = null;

    // Attempt to save the user changes specified in the form's model
    $scope.save = function() {
    // Clear any previous security errors
        $scope.authError = null;

        if ($scope.newPassword != $scope.confirmPassword)
        {
            $scope.authError = localizedMessages.get('password.change.passwordMismatch');
            return;
        }

        // Call the server to change the password
        security.changePassword($scope.user.email, $scope.user.currentPassword, $scope.user.newPassword).then(function(loggedIn) {
            if ( !loggedIn ) {
                // If we get here then the password change failed
                $scope.authError = localizedMessages.get('password.change.error');
            }
        }, function(x) {
            // If we get here then there was a problem with the password change request to the server
            $scope.authError = localizedMessages.get('password.change.serverError', { exception: x });
        });
    };

    $scope.cancel = function () {
        security.cancelChangePassword();
    };

    $scope.clearForm = function() {
        $scope.user = {};
    };

    $scope.twoFacAuthEnabled = security.currentUser.twoFacAuthEnabled;

    $scope.show2faSetup = function () {
        security.show2faSetup();
    };

    $scope.disable2fa = function () {
        const response = confirm("Are you sure you want disable 2FA?");
        if (response !== true) {
            return;
        }
        security.disable2fa().then(function (response) {
            security.currentUser.isTwoFacAuthRequire = false;
            security.currentUser.twoFacAuthEnabled = false;
            security.currentUser.twoFacAuthVerified = false;
            $scope.twoFacAuthEnabled = false;
            log.logSuccess("2FA on your account has been disabled");
        }, function (x) {
            // If we get here then there was a problem with the password change request to the server
            $scope.authError = '2FA  Code verification Error';
        });
    };

}]);
