<md-card layout-margin redi-show-by-roles="['assessment_page_(tabs/sub-tabs)__simulation__view']">
    <md-card-header>
        <span class="md-title">Compliance Summary</span>
    </md-card-header>
    <md-card-content>

        <!--On template, for some reason, we need to show these trhee fields but have them disabled and unset. For non-templates it should be as usual-->
        <!--This one is the non-template-->
        <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="25">
            <label>Final Assessment Method</label>
            <md-select name="finalComplianceMethod"
                       ng-model="vm.selectedComplianceOption().complianceMethodCode"
                       disabled>
                <md-option ng-value="item.complianceMethodCode"
                           ng-repeat="item in vm.complianceMethodList">
                    {{item.description}}
                </md-option>
                <md-option ng-value="null">Compliance Method Not Selected</md-option>
            </md-select>
        </md-input-container>

        <!-- ******** Performance Requirement P261 ******** -->
        <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="25">
            <label>Performance Requirement P261</label>
            <input ng-model="vm.assessment.p261Description"
                   disabled />
        </md-input-container>

        <!-- ******** Performance Requirement P262 ******** -->
        <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="25">
            <label>Performance Requirement P262</label>
            <md-select name="performanceRequirementP262Code"
                       required
                       ng-model="vm.assessment.performanceRequirementP262Code"
                       ng-change="vm.setFinalComplianceMethod()"
                       ng-disabled="vm.isLocked || (vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='AComplete' || vm.assessment.statusCode=='ASuperseded')">
                <md-option ng-value="item.performanceRequirementP262Code"
                           ng-repeat="item in vm.performanceRequirementP262List track by item.performanceRequirementP262Code">
                    {{item.description}}
                </md-option>
            </md-select>
            <div ng-messages="assessmentform.performanceRequirementP262Code.$error">
                <div ng-message="required">Performance Requirement P262 is required.</div>
            </div>
        </md-input-container>

        <!-- ******** Compliance Status ******** -->
        <md-input-container class="md-block" flex="100" flex-gt-sm="50" flex-gt-md="25" ng-class="{'compliance-achieved':vm.assessment.complianceStatusCode == 'CSAchieved'}">
            <label>Compliance Status</label>
            <md-select name="complianceStatusCode"
                       ng-model="vm.assessment.complianceStatusCode"
                       ng-change="vm.setFinalComplianceMethod()"
                       ng-disabled="true">
                <md-option ng-value="item.complianceStatusCode"
                           ng-repeat="item in vm.complianceStatusList track by item.complianceStatusCode">
                    {{item.description}}
                </md-option>
            </md-select>
            <div ng-messages="assessmentform.complianceStatusCode.$error">
            </div>
        </md-input-container>

    </md-card-content>

</md-card>