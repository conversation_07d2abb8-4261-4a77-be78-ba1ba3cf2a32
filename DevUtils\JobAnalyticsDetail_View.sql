SELECT TOP (1000) [JobId]
      ,[CurrentAssessmentId]
      ,[JobReference]
      ,[ClientId]
      ,[ClientJobNumber]
      ,[StatusCode]
      ,[JobCreatedOn]
      ,[AssessmentCerficateDate]
      ,[JobModifiedOn]
      ,[JobDeleted]
      ,[AssessmentDeleted]
      ,[ClientName]
      ,[ProjectDescriptionDescription]
      ,[AssessmentDesign]
      ,[AssessmentPriorityDescription]
      ,[AssessmentProjectDetailSuburb]
      ,[AssessmentProjectDetailLGA]
      ,[JobStatusDescription]
      ,[ClientAssigneeFullName]
      ,[AssessorFullName]
      ,[AssessmentVersion]
      ,[AssessmentProjectDetailLotDescription]
      ,[AssessmentProjectDetailLotWidth]
      ,[AssessmentProjectDetailLotLength]
      ,[AssessmentProjectDetailParcelArea]
      ,[AssessmentProjectDetailCornerBlock]
      ,[AssessmentProjectDetailRearLaneway]
      ,[AssessmentProjectDetailRuralLot]
      ,[NorthOffset]
      ,[CertificationId]
      ,[CertificationTitle]
      ,[GarageLocation]
      ,[OutdoorLivingLocation]
      ,[ComplianceMethodCode]
      ,[ComplianceMethodDescription]
      ,[NatHersClimateZone]
      ,[Heating]
      ,[Cooling]
      ,[TotalEnergyLoad]
      ,[CalculatedHouseEnergyRating]
      ,[IsActive]
      ,[IsVirtualSim]
  FROM [dbo].[RSS_JobAnalyticsDetail_View]
 WHERE 1=1
    -- AND [ClientId] = 'c2d280bc-4274-c026-6c65-3a0b53d165e5'
    -- AND [ComplianceMethodCode] = 'CMPerfSolution'
    -- AND [CertificationId] = '9ef094ee-0725-4f94-9499-2c11b10fa649'
    -- AND [CurrentAssessmentId] = '6a200958-333b-4af6-af53-8056e665eb3a'