(function () {

  'use strict';
  angular
    .module('app')
    .component('elHomePlanTooltip', {
      bindings: {
        planViews: '<',
        currentIndex: '<',
        view3dFloorPlans: '<',
        projectView3dFloorPlans: '<',
        parentModelView3dFloorPlans: '<',
        floorplannerLink: '<',
        onClose: '&'
      },
      templateUrl: 'app/ui/energy-labs/performance-tooltips/home-plan-tooltip.html',
      controller: ElHomePlanTooltipController,
      controllerAs: 'vm'
    });

  ElHomePlanTooltipController.$inject = ['$rootScope', '$mdDialog'];

  function ElHomePlanTooltipController($rootScope, $mdDialog) {

    let vm = this;

    vm.showLabel = vm.planViews.length >= 2;

    // Check if 3D floor plans should be enabled by checking all three levels:
    // 1. Project level (projectView3dFloorPlans)
    // 2. Home Design level (parentModelView3dFloorPlans)
    // 3. Variation level (view3dFloorPlans)
    vm.show3dFloorPlans = vm.projectView3dFloorPlans &&
                         vm.parentModelView3dFloorPlans &&
                         vm.view3dFloorPlans;

    function stopPropagation(event) {
      if(event && event.stopPropagation)
        event.stopPropagation();
    }

    vm.prevImage = function($event) {
      stopPropagation($event);
      if(vm.currentIndex !== 0)
        vm.currentIndex--;
    }

    vm.nextImage = function($event) {
      stopPropagation($event);
      if(vm.currentIndex < vm.planViews.length - 1)
        vm.currentIndex++;
    }

        vm.open3dViewerModal = function (event) {
            event.stopPropagation();
            var modalScope = $rootScope.$new();
            modalScope.floorplannerLink = vm.floorplannerLink;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-3d-viewer-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
            vm.onClose != null && vm.onClose();
        }

        vm.openSwitcherModal = function (event) {
            event.stopPropagation();
            var modalScope = $rootScope.$new();
            modalScope.planViews = vm.planViews;
            modalScope.currentIndex = vm.currentIndex;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-switcher-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
            vm.onClose != null && vm.onClose();
        }

  }

})();