<form name="floorCoveringform" class="main-content-wrapper" novalidate data-ng-controller='FloorCoveringUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="column">
                <div flex="100">
                    <md-card>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- ******** Description ******** -->
                                <md-input-container class="md-block" flex="100">
                                    <label>Description</label>
                                    <input type="text"
                                           name="floorCoveringDescription"
                                           ng-model="vm.floorCovering.description"
                                           md-autofocus
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="floorCoveringform.floorCoveringDescription.$error">
                                        <div ng-message="required">Description is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <md-input-container class="md-block" flex="100">
                                    <label>Classification</label>
                                    <input type="text"
                                           name="floorCoveringClassification"
                                           ng-model="vm.floorCovering.classification"
                                           md-maxlength="4"
                                           required />
                                    <div ng-messages="floorCoveringform.floorCoveringClassification.$error">
                                        <div ng-message="required">Classification is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 4.</div>
                                    </div>
                                </md-input-container>

                                <span>Material Codes</span>
                                <div>
                                    <div ng-repeat="code in vm.floorCovering.materialCodesArray track by $index"
                                         class="material-code-row">
                                        <input ng-model="vm.floorCovering.materialCodesArray[$index]"
                                               class="hide-updown"
                                               type="number"
                                               style="height: 20px; text-align: right;"/>
                                        <md-button ng-click="vm.removeMaterialCode($index);"
                                                   class="md-warn">
                                            Delete
                                        </md-button>
                                    </div>

                                </div>

                                <md-button ng-click="vm.floorCovering.materialCodesArray.push(null);"
                                           class="md-primary">
                                    Add Code
                                </md-button>

                            </fieldset>

                            <div class="col-md-12" 
                                 ng-if="vm.newRecord==false"
                                 style="margin-top: 20px;">
                                <div rd-display-created-modified ng-model="vm.floorCovering"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>

            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary"
                           ng-disabled="floorCoveringform.$invalid || vm.editPermission == false"
                           ng-show="vm.floorCovering.deleted!=true"
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.floorCovering.deleted != true"
                           ng-confirm-click="vm.delete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.floorCovering.deleted==true"
                           ng-confirm-click="vm.undoDelete()"
                           ng-confirm-condition="true"
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>   

<style>
    .material-code-row {
        display: grid;
        grid-template-columns: 50px 50px;
        align-items: center;
    }

    /* 
      This hides the (rather annoying imo) UP / DOWN buttons that automatically get added to type="number" input fields.
    */
    .hide-updown::-webkit-inner-spin-button,
    .hide-updown::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        margin: 0;
    }
</style>
