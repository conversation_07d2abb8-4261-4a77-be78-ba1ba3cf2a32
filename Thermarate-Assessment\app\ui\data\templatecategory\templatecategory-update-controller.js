(function () {
    // The TemplatecategoryUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'TemplatecategoryUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'templatecategoryservice', templatecategoryUpdateController]);
function templatecategoryUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  templatecategoryservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Template Category';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.code = null;
        vm.templatecategory = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Template Category";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.code = $scope.code;
            }
            vm.hideActionBar = true;
        } else {
            vm.code = $stateParams.code;
        }

        // Get data for object to display on page
        var codePromise = null;
        if (vm.code != null) {
            codePromise = templatecategoryservice.getTemplateCategory(vm.code)
            .then(function (data) {
                if (data != null) {
                    vm.templatecategory = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("templatecategory-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                templatecategoryservice.createTemplateCategory(vm.templatecategory).then(function(data){
                    vm.templatecategory = data;
                    vm.code = vm.templatecategory.code;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                templatecategoryservice.updateTemplateCategory(vm.templatecategory).then(function(data){
                    if (data != null) {
                        vm.templatecategory = data;
                        vm.code = vm.templatecategory.code;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            templatecategoryservice.deleteTemplateCategory(vm.code).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            templatecategoryservice.undoDeleteTemplateCategory(vm.code).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();