(function () {
    'use strict';
    var controllerId = 'LoginCtrl';
    angular.module('app').controller(
        controllerId, ['common', '$rootScope', '$scope', '$mdDialog', '$window', '$location', 'config', 'localizedMessages', 'jobservice', 'priorityservice', 'security', 'daterangehelper', '$timeout', 'standardmodelservice', login]);
    function login(common, $rootScope, $scope, $mdDialog, $window, $location, config, localizedMessages, jobservice, priorityservice, security, daterangehelper, $timeout, standardmodelservice) {

        var vm = this;

        vm.loginForm = {};
        vm.loginForm.stage = null;

        vm.showPassword = false;

        vm.focusDefaultField = function () {
            $timeout(() => {
                switch(vm.loginForm.stage) {
                    case null:                document.getElementById("emailAddressInput").select(); break;
                    case "enterYourPassword": document.getElementById("passwordInput").select(); break;
                    case "twoFacConfirm":     document.getElementById("twoFacCodeInput").select(); break;
                }
            });
        }

        vm.focusDefaultField();

        vm.microsoftLogin = function () {
            window.location.href = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize`
                                        + `?client_id=${config.azureClientId}`
                                        + `&response_type=code`
                                        + `&redirect_uri=${(new $window.URL($location.absUrl())).origin}`
                                        + `&response_mode=query`
                                        + `&scope=offline_access%20user.read%20mail.read`;
        }

        vm.googleLogin = function () {
            window.location.href = `https://accounts.google.com/o/oauth2/auth`
                                        + `?client_id=${config.googleClientId}`
                                        + `&response_type=code`
                                        + `&redirect_uri=${(new $window.URL($location.absUrl())).origin}`
                                        + `&scope=profile%20email`;
        }

        vm.goToWelcomeBack = function () {
            vm.loginForm.stage = null;
            vm.loginForm.username = null;
            vm.loginForm.password = null;
            vm.authError = null;
            vm.focusDefaultField();
        }

        vm.welcomBackContinue = function () {
            if (vm.loginForm.username != null && vm.loginForm.username != '') {
                vm.loginForm.stage = "enterYourPassword";
                vm.focusDefaultField();
                $timeout(() => vm.toggleShowHidePassword(false));
            }
        }

        vm.toggleShowHidePassword = function (toggle) {
            vm.showPassword = toggle;
            vm.passwordShowHideIcon = vm.showPassword ? "eye-visible" : "eye-hide";
            let showHideButton = document.getElementById("passwordInput");
            showHideButton.type = vm.showPassword ? "text" : "password";
        }

        vm.forgotPassword = function () {
            // Do stuff to send a new password.
            vm.isBusy = true;
            security.forgotPassword(vm.loginForm.username).then(
                success => {
                    console.log("forgot login func success = " + success);
                    vm.isBusy = false;
                    if (success == false) {
                        vm.authError = localizedMessages.get('password.reset.error');
                    }
                },
                x => {
                    // If we get here then there was a problem with the password reset request to the server
                    if (x?.length > 0)
                        vm.authError = localizedMessages.get('login.error.serverError', { exception: x });
                    vm.isBusy = false;
                }
            );
        }

        vm.loginAttempt = function () {
            var twofacexpDate = localStorage.getItem("2fa_expire_" + vm.loginForm.username);
            var expire = new Date(parseInt(twofacexpDate, 10));

            // Clear any previous security errors
            vm.authError = null;
            vm.isBusy = true;

            security.isTwoFacAuthEnabled(vm.loginForm.username).then(enabled => {

                // [THR-575] Force 2FA every time
                vm.twofaRequire = enabled;
                //if (enabled) {
                //    var now = new Date();
                //    vm.twofaRequire = !twofacexpDate || expire === null || expire === undefined || now > expire;
                //}

                // IF 2FA required, go to 2FA code form
                if (vm.twofaRequire) {
                    vm.loginForm.stage = "twoFacConfirm";
                    vm.focusDefaultField();
                // ELSE try login
                } else {
                    vm.login();
                }
            });
        }

        vm.login = function () {
            security.login(vm.loginForm.username, vm.loginForm.password, vm.loginForm.twoFacCode, vm.twofaRequire).then(
                result => {
                    vm.isBusy = false;
                    var loggedIn = result === true;
                    if (!loggedIn) {
                        // If we get here then the login failed due to bad credentials
                        vm.authError = localizedMessages.get('login.error.invalidCredentials');
                    } else {
                        vm.authError = null;
                    }
                },
                error => {
                    // If we get here then there was a problem with the login request to the server
                    vm.authError = localizedMessages.get('login.error.serverError', { exception: error });
                    vm.isBusy = false;
                }
            );
        }

    }

})();