<md-dialog ng-controller="OpeningsOverrideModalCtrl as vm">
  <ng-form name="openingsoverridemodalform" novalidate ng-submit="vm.save()">
    <md-toolbar>
      <div class="md-toolbar-tools">
        <h2 style="text-transform: capitalize">{{vm.title}} ({{vm.item.elementNumber}})</h2>
        <span flex></span>
        <md-button class="md-icon-button" ng-click="vm.cancel()">
          <i class="material-icons">clear</i>
        </md-button>
      </div>
    </md-toolbar>

    <md-dialog-content layout="column" layout-margin layout-padding>

      <table class="table" style="margin: 0;">
        <thead>
        <tr>
          <!-- Title of the property we're editing again (for good measure) -->
          <th>
            <div style="margin-bottom: 4px; text-align: center;">{{vm.originalValueText}}</div>
          </th>

          <!-- Description + Override checkbox -->
          <th style="text-align: center;">
            {{vm.manualOverrideSubtext}}
            <md-input-container style="margin: 0 0 0 10px;">
              <md-checkbox style="margin: 0;"
                           class="compliance-row-table-input-m"
                           name="isOverriden"
                           ng-disabled="vm.disabled"
                           ng-model="vm.isOverridden"
                           ng-change="vm.toggleOverride()">
              </md-checkbox>
            </md-input-container>
          </th>
        </tr>
        </thead>
        <tbody>
          <tr>

            <!-- Original value (disabled) -->
            <td>
              <md-input-container class="vertically-condensed kindly-remove-error-spacer">
                <input class="compliance-row-table-input-m"
                       disabled
                       ng-value="vm.originalValue.toFixed(2)"/>
              </md-input-container>
            </td>

            <!-- Overridden value with reset button-->
            <td>
              <div style="display: grid; grid-template-columns: 8fr 2fr;
                          align-content: center; align-items: center;">
                <md-input-container class="vertically-condensed kindly-remove-error-spacer">
                  <input formatted-number
                         id="override-value-focus-node"
                         class="compliance-row-table-input-m"
                         decimals="2"
                         name="test"
                         ng-disabled="!vm.isOverridden || vm.disabled"
                         ng-required="vm.isOverridden"
                         ng-model="vm.overrideValue"/>
                </md-input-container>
                <button ng-disabled="!vm.isOverridden || vm.disabled"
                        class="feather-icon-button"
                        ng-click="vm.reset()">
                  <img src="/content/feather/refresh-ccw.svg" />
                  <md-tooltip>
                    Reset to original value
                  </md-tooltip>
                </button>
              </div>

            </td>
          </tr>
        </tbody>
      </table>

    </md-dialog-content>

    <md-dialog-actions layout="row">
      <md-button class="md-raised md-primary"
                 ng-click="vm.save()"
                 ng-disabled="vm.disabled">
        Save
      </md-button>
      <md-button class="md-raised" ng-click="vm.cancel()"> Cancel </md-button>
    </md-dialog-actions>

  </ng-form>

</md-dialog>

<style>
  md-dialog-content * {
    font-size: 16px;
  }
  md-input-container label:not(.md-container-ignore) {
    left: auto;
  }
</style>
