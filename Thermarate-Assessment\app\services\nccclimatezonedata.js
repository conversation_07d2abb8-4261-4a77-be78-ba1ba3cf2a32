// Name: nccclimatezonedataservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'nccclimatezonedataservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', nccclimatezonedataservice]);

    function nccclimatezonedataservice(common, config, $http) {

        var log = common.logger;
        var baseUrl = config.servicesUrlPrefix + 'nccclimatezonedata/';

        var service = {
            processDataset: processDataset,
            getClimateZone: getClimateZone
        };
            
        return service;

        /** 
         * Runs the extraction process for the NCC Climate Zone dataset. Returns a string
         * describing the final state of the process (unless it fails, then see error response).
         * 
         * NOTE: This dataset was provided by an external consultant to <PERSON>, NOT Landgate!
         * 
         * NOTE: Unlike the Tenure Dataset extractions, this process is mostly manual.
         */
        function processDataset() {

            return $http({
                url: baseUrl + 'ProcessDataset',
                method: 'POST',
                timeout: 5 * 60 * 1000 // 5 Minute timeout, should be more than enough.
            }).then(
                (response) => { return response.data; },
                (error) => {
                    var msg = "Error processing NCC Climate Zone dataset: " + error;
                    log.logError(msg, error, null, true);
                    throw error;
                });
        }

        /** 
         *  Checks the climate zone code for the given lat/lng
         */
        function getClimateZone(lat, lng) {

            return $http({
                url: baseUrl + 'getClimateZone',
                params: { lat: lat, lng: lng },
                method: 'GET',
            }).then(success, fail);

            function success(resp) {

                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }

            function fail(error) {

                var msg = "Error checking NCC Climate Zone dataset: " + error;
                log.logError(msg, error, null, true);
                throw error;
            }
        }
    }
})();
