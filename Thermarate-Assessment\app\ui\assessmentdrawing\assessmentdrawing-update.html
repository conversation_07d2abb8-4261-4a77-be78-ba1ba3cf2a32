<form name="assessmentdrawingform" class="main-content-wrapper" novalidate data-ng-controller='AssessmentdrawingUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <!--Left Side-->
                <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}" >
                    <md-card>
                        <md-card-header>
                            Assessment Drawing
                        </md-card-header>
                        <md-card-content>

<!-- ******** Assessment ******** -->
                            <md-autocomplete md-input-name="assessmentId" md-autofocus 
                                         required
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.assessmentdrawing.assessment"
                                         md-search-text="vm.assessmentIdSearchText"
                                         md-items="item in vm.getassessments(vm.assessmentIdSearchText)"
                                         md-item-text="item.sealedExhaustFansDetails"
                                         md-require-match
                                         md-floating-label="Assessment">
                                <md-item-template>
                                    <span md-highlight-text="vm.assessmentIdSearchText">{{item.sealedExhaustFansDetails}}</span>
                                </md-item-template>
                                <div ng-messages="assessmentdrawingform.assessmentId.$error">
                                    <div ng-message="required">Assessment is required.</div>
                                </div>
                            </md-autocomplete>

<!-- ******** Drawing Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Drawing Number</label>
                                <input type="text" name="drawingNumber" 
                                        ng-model="vm.assessmentdrawing.drawingNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="assessmentdrawingform.drawingNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Drawing Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Drawing Description</label>
                                <input type="text" name="drawingDescription" 
                                        ng-model="vm.assessmentdrawing.drawingDescription"  
                                        md-maxlength="100"
                                    />
                                <div ng-messages="assessmentdrawingform.drawingDescription.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

<!-- ******** Attachment ******** -->
                            <md-autocomplete md-input-name="attachment"  
                                         md-input-minlength="2"
                                         md-min-length="0"
                                         md-selected-item="vm.assessmentdrawing.attachment"
                                         md-search-text="vm.attachmentSearchText"
                                         md-items="item in vm.getfiles(vm.attachmentSearchText)"
                                         md-item-text="item.displayName"
                                         md-require-match
                                         md-floating-label="Attachment">
                                <md-item-template>
                                    <span md-highlight-text="vm.attachmentSearchText">{{item.displayName}}</span>
                                </md-item-template>
                            </md-autocomplete>

<!-- ******** Sheet Number ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Sheet Number</label>
                                <input type="text" name="sheetNumber" 
                                        ng-model="vm.assessmentdrawing.sheetNumber"  
                                        only-numeric
                                    />
                                <div ng-messages="assessmentdrawingform.sheetNumber.$error">
                                </div>
                            </md-input-container>

<!-- ******** Revision ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Revision</label>
                                <input type="text" name="revision" 
                                        ng-model="vm.assessmentdrawing.revision"  
                                        md-maxlength="20"
                                    />
                                <div ng-messages="assessmentdrawingform.revision.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                                </div>
                            </md-input-container>

<!-- ******** Revision Date ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Revision Date</label>
                                <md-datepicker ng-model="vm.assessmentdrawing.revisionDate" 
                                               name="revisionDate"  
                                            md-placeholder="Enter date">
                                </md-datepicker>
                                <div class="validation-messages" ng-messages="assessmentdrawingform.revisionDate.$error">
                                    <div ng-message="valid">The entered value is not a date!</div>
                                    <div ng-message="required">This date is required!</div>
                                    <div ng-message="mindate">Date is too early!</div>
                                    <div ng-message="maxdate">Date is too late!</div>
                                    <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                                </div>
                            </md-input-container>

<!-- ******** Included In Report ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <md-checkbox name="isIncludedInReport"
                                        ng-model="vm.assessmentdrawing.isIncludedInReport"  >
                                    Included In Report
                                </md-checkbox>
                                <div ng-messages="assessmentdrawingform.isIncludedInReport.$error">
                                    <div ng-message="required">Included In Report is required.</div>
                                </div>
                            </md-input-container>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.assessmentdrawing"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>

<!-- ******** Right Side ******** -->
            <div ng-if="vm.newRecord==false" flex-gt-sm="50">
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="assessmentdrawingform.$invalid" ng-show="vm.assessmentdrawing.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.assessmentdrawing.assessmentDrawingId!=null && vm.assessmentdrawing.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.assessmentdrawing.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
