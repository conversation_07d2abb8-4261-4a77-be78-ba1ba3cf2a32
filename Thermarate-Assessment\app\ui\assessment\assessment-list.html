<!-- OBSOLETE - NO LONGER IN USE -->
<section id="assessment-list-view" class="main-content-wrapper" data-ng-controller="AssessmentListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="Assessment"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.assessmentList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="assessmentList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left">Action</th>
                        <th st-sort="jobClientJobNumber" class="can-sort text-left">Job</th>
                        <th st-sort="statusDescription" class="can-sort text-left">Status</th>
                        <th st-sort="assessmentSoftwareDescription" class="can-sort text-left">Assessment Software</th>
                        <th st-sort="nCCClimateZoneDescription" class="can-sort text-left">N C C Climate Zone</th>
                        <th st-sort="natHERSClimateZoneDescription" class="can-sort text-left">Nat H E R S Climate Zone</th>
<!--                        <th st-sort="buildingOrientation" class="can-sort text-right">Building Orientation</th>-->
                        <th st-sort="buildingExposureDescription" class="can-sort text-left">Building Exposure</th>
                        <th st-sort="proposedConditionedFloorArea" class="can-sort text-right">Conditioned Floor Area</th>
                        <th st-sort="proposedUnconditionedFloorArea" class="can-sort text-right">Unconditioned Floor Area</th>
                        <th st-sort="proposedGarageFloorArea" class="can-sort text-right">Attached Garage Floor Area</th>
                        <th st-sort="proposedHouseEnergyRating" class="can-sort text-right">Proposed House Energy Rating</th>
                        <th st-sort="referenceHeating" class="can-sort text-right">Reference Heating</th>
                        <th st-sort="referenceCooling" class="can-sort text-right">Reference Cooling</th>
                        <th st-sort="referenceHouseEnergyRating" class="can-sort text-right">Reference House Energy Rating</th>
                        <th st-sort="proposedHeating" class="can-sort text-right">Proposed Heating</th>
                        <th st-sort="proposedCooling" class="can-sort text-right">Proposed Cooling</th>
                        <th st-sort="proposedHouseEnergyRating" class="can-sort text-right">Proposed House Energy Rating</th>
                        <th st-sort="softwareFileDisplayName" class="can-sort text-left">Software File</th>
                        <th st-sort="performanceRequirementP261Description" class="can-sort text-left">Performance Requirement P261</th>
                        <th st-sort="performanceRequirementP262Description" class="can-sort text-left">Performance Requirement P262</th>
                        <th st-sort="complianceStatusDescription" class="can-sort text-left">Compliance Status</th>
                        <th st-sort="sealedExhaustFansSpecified" class="can-sort text-center">Sealed Exhaust Fans Specified</th>
                        <th st-sort="sealedExhaustFansDetails" class="can-sort text-left">Sealed Exhaust Fans Details</th>
                        <th st-sort="unsealedExhaustFansSpecified" class="can-sort text-center">Unsealed Exhaust Fans Specified</th>
                        <th st-sort="unsealedExhaustFansDetails" class="can-sort text-left">Unsealed Exhaust Fans Details</th>
                        <th st-sort="ceilingVentsSpecified" class="can-sort text-center">Ceiling Vents Specified</th>
                        <th st-sort="ceilingVentsDetails" class="can-sort text-left">Ceiling Vents Details</th>
                        <th st-sort="wallVentsSpecified" class="can-sort text-center">Wall Vents Specified</th>
                        <th st-sort="wallVentsDetails" class="can-sort text-left">Wall Vents Details</th>
                        <th st-sort="unfluedGasHeatersSpecified" class="can-sort text-center">Unflued Gas Heaters Specified</th>
                        <th st-sort="unfluedGasHeatersDetails" class="can-sort text-left">Unflued Gas Heaters Details</th>
                        <th st-sort="fluedGasHeatersSpecified" class="can-sort text-center">Flued Gas Heaters Specified</th>
                        <th st-sort="fluedGasHeatersDetails" class="can-sort text-left">Flued Gas Heaters Details</th>
                        <th st-sort="sealedChimneySpecified" class="can-sort text-center">Sealed Chimney Specified</th>
                        <th st-sort="sealedChimneyDetails" class="can-sort text-left">Sealed Chimney Details</th>
                        <th st-sort="unsealedChimneySpecified" class="can-sort text-center">Unsealed Chimney Specified</th>
                        <th st-sort="unsealedChimneyDetails" class="can-sort text-left">Unsealed Chimney Details</th>
                        <th st-sort="sealedRecessedLightFittingsSpecified" class="can-sort text-center">Sealed Recessed Lights Specified</th>
                        <th st-sort="sealedRecessedLightFittingsDetails" class="can-sort text-left">Sealed Recessed Lights Details</th>
                        <th st-sort="unsealedRecessedLightFittingsSpecified" class="can-sort text-center">Unsealed Recessed Lights Specified</th>
                        <th st-sort="unsealedRecessedLightFittingsDetails" class="can-sort text-left">Unsealed Recessed Lights Details</th>
                        <th st-sort="ceilingFansSpecified" class="can-sort text-center">Ceiling Fans Specified</th>
                        <th st-sort="ceilingFansDetails" class="can-sort text-left">Ceiling Fans Details</th>
                        <th st-sort="evaporativeCoolingSystemSpecified" class="can-sort text-center">Evaporative Cooling System Specified</th>
                        <th st-sort="evaporativeCoolingSystemDetails" class="can-sort text-left">Evaporative Cooling System Details</th>
                        <th st-sort="certificateNumber" class="can-sort text-left">Certificate Number</th>
                        <th st-sort="cerficateDate" class="can-sort text-left">Cerficate Date</th>
                        <th st-sort="assessorUser" class="can-sort text-left">Assessor</th>
                        <th st-sort="assessorSignatureFileDisplayName" class="can-sort text-left">Assessor Signature File</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.assessmentList">
                        <td data-title="Action"><md-button class="md-primary list-select" ui-sref="assessment-updateform({ assessmentId: row.assessmentId})">Select</md-button>  </td>
                        <td data-title="Job" class="text-left">{{::row.jobClientJobNumber }}</td>
                        <td data-title="Status" class="text-left">{{::row.statusDescription }}</td>
                        <td data-title="Assessment Software" class="text-left">{{::row.assessmentSoftwareDescription }}</td>
                        <td data-title="N C C Climate Zone" class="text-left">{{::row.nCCClimateZoneDescription }}</td>
                        <td data-title="Nat H E R S Climate Zone" class="text-left">{{::row.natHERSClimateZoneDescription }}</td>
<!--                        <td data-title="Building Orientation" class="text-right">{{::row.buildingOrientation | number }}</td>-->
                        <td data-title="Building Exposure" class="text-left">{{::row.buildingExposureDescription }}</td>
                        <td data-title="Conditioned Floor Area" class="text-right">{{::row.proposedConditionedFloorArea | number }}</td>
                        <td data-title="Unconditioned Floor Area" class="text-right">{{::row.proposedUnconditionedFloorArea | number }}</td>
                        <td data-title="Attached Garage Floor Area" class="text-right">{{::row.proposedGarageFloorArea | number }}</td>
                        <td data-title="Proposed Heating" class="text-right">{{::row.proposedHeating | number }}</td>
                        <td data-title="Proposed Cooling" class="text-right">{{::row.proposedCooling | number }}</td>
                        <td data-title="Proposed House Energy Rating" class="text-right">{{::row.proposedHouseEnergyRating | number }}</td>
                        <td data-title="Reference Heating" class="text-right">{{::row.referenceHeating | number }}</td>
                        <td data-title="Reference Cooling" class="text-right">{{::row.referenceCooling | number }}</td>
                        <td data-title="Reference House Energy Rating" class="text-right">{{::row.referenceHouseEnergyRating | number }}</td>
                        <td data-title="Proposed Heating" class="text-right">{{::row.proposedHeating | number }}</td>
                        <td data-title="Proposed Cooling" class="text-right">{{::row.proposedCooling | number }}</td>
                        <td data-title="Proposed House Energy Rating" class="text-right">{{::row.proposedHouseEnergyRating | number }}</td>
                        <td data-title="Software File" class="text-left">{{::row.softwareFileDisplayName }}</td>
                        <td data-title="Performance Requirement P261" class="text-left">{{::row.performanceRequirementP261Description }}</td>
                        <td data-title="Performance Requirement P262" class="text-left">{{::row.performanceRequirementP262Description }}</td>
                        <td data-title="Compliance Status" class="text-left">{{::row.complianceStatusDescription }}</td>
                        <td data-title="Sealed Exhaust Fans Specified" class="text-center"><span ng-bind-html='row.sealedExhaustFansSpecified | tickCross'></span></td>
                        <td data-title="Sealed Exhaust Fans Details" class="text-left">{{::row.sealedExhaustFansDetails }}</td>
                        <td data-title="Unsealed Exhaust Fans Specified" class="text-center"><span ng-bind-html='row.unsealedExhaustFansSpecified | tickCross'></span></td>
                        <td data-title="Unsealed Exhaust Fans Details" class="text-left">{{::row.unsealedExhaustFansDetails }}</td>
                        <td data-title="Ceiling Vents Specified" class="text-center"><span ng-bind-html='row.ceilingVentsSpecified | tickCross'></span></td>
                        <td data-title="Ceiling Vents Details" class="text-left">{{::row.ceilingVentsDetails }}</td>
                        <td data-title="Wall Vents Specified" class="text-center"><span ng-bind-html='row.wallVentsSpecified | tickCross'></span></td>
                        <td data-title="Wall Vents Details" class="text-left">{{::row.wallVentsDetails }}</td>
                        <td data-title="Unflued Gas Heaters Specified" class="text-center"><span ng-bind-html='row.unfluedGasHeatersSpecified | tickCross'></span></td>
                        <td data-title="Unflued Gas Heaters Details" class="text-left">{{::row.unfluedGasHeatersDetails }}</td>
                        <td data-title="Flued Gas Heaters Specified" class="text-center"><span ng-bind-html='row.fluedGasHeatersSpecified | tickCross'></span></td>
                        <td data-title="Flued Gas Heaters Details" class="text-left">{{::row.fluedGasHeatersDetails }}</td>
                        <td data-title="Sealed Chimney Specified" class="text-center"><span ng-bind-html='row.sealedChimneySpecified | tickCross'></span></td>
                        <td data-title="Sealed Chimney Details" class="text-left">{{::row.sealedChimneyDetails }}</td>
                        <td data-title="Unsealed Chimney Specified" class="text-center"><span ng-bind-html='row.unsealedChimneySpecified | tickCross'></span></td>
                        <td data-title="Unsealed Chimney Details" class="text-left">{{::row.unsealedChimneyDetails }}</td>
                        <td data-title="Sealed Recessed Lights Specified" class="text-center"><span ng-bind-html='row.sealedRecessedLightFittingsSpecified | tickCross'></span></td>
                        <td data-title="Sealed Recessed Lights Details" class="text-left">{{::row.sealedRecessedLightFittingsDetails }}</td>
                        <td data-title="Unsealed Recessed Lights Specified" class="text-center"><span ng-bind-html='row.unsealedRecessedLightFittingsSpecified | tickCross'></span></td>
                        <td data-title="Unsealed Recessed Lights Details" class="text-left">{{::row.unsealedRecessedLightFittingsDetails }}</td>
                        <td data-title="Ceiling Fans Specified" class="text-center"><span ng-bind-html='row.ceilingFansSpecified | tickCross'></span></td>
                        <td data-title="Ceiling Fans Details" class="text-left">{{::row.ceilingFansDetails }}</td>
                        <td data-title="Evaporative Cooling System Specified" class="text-center"><span ng-bind-html='row.evaporativeCoolingSystemSpecified | tickCross'></span></td>
                        <td data-title="Evaporative Cooling System Details" class="text-left">{{::row.evaporativeCoolingSystemDetails }}</td>
                        <td data-title="Certificate Number" class="text-left">{{::row.certificateNumber }}</td>
                        <td data-title="Cerficate Date" class="text-left">{{::row.cerficateDate | date: 'dd/MM/yyyy' }}</td>
                        <td data-title="Assessor User" class="text-right">{{::row.assessorUser.fullName | number }}</td>
                        <td data-title="Assessor Signature File" class="text-left">{{::row.assessorSignatureFileDisplayName }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="54" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
