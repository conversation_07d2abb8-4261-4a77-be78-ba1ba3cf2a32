
-- Generated by Augment on 10/04/25

-- <PERSON><PERSON><PERSON> to update SortOrder of RSS_StandardHomeModel records
-- Groups records by ProjectId and IsVariationOfHomeModelId
-- Ensures no duplicate SortOrder values within each group

-- First, let's create a temporary table to hold our new sort orders
DECLARE @SortOrderUpdates TABLE (
    StandardHomeModelId UNIQUEIDENTIFIER,
    NewSortOrder INT
);

-- Insert records into our temporary table with new sort orders
-- We use ROW_NUMBER() to generate sequential sort orders within each group
WITH SortedModels AS (
    SELECT 
        StandardHomeModelId,
        ProjectId,
        IsVariationOfHomeModelId,
        ROW_NUMBER() OVER (
            PARTITION BY 
                ProjectId, 
                ISNULL(IsVariationOfHomeModelId, CAST('00000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER))
            ORDER BY 
                Title, -- Primary sort by Title (can be changed to any other field)
                CreatedOn -- Secondary sort by CreatedOn
        ) AS NewSortOrder
    FROM 
        dbo.RSS_StandardHomeModel
    WHERE 
        Deleted = 0 -- Only consider non-deleted records
)
INSERT INTO @SortOrderUpdates (StandardHomeModelId, NewSortOrder)
SELECT StandardHomeModelId, NewSortOrder
FROM SortedModels;

-- Print a summary of what will be updated
SELECT 
    COUNT(*) AS TotalRecordsToUpdate,
    COUNT(DISTINCT ProjectId) AS ProjectsAffected,
    COUNT(DISTINCT ISNULL(IsVariationOfHomeModelId, CAST('00000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER))) AS VariationGroupsAffected
FROM 
    dbo.RSS_StandardHomeModel
WHERE 
    Deleted = 0;

-- Update the actual records with new sort orders
UPDATE m
SET m.SortOrder = u.NewSortOrder
FROM dbo.RSS_StandardHomeModel m
INNER JOIN @SortOrderUpdates u ON m.StandardHomeModelId = u.StandardHomeModelId;

-- Verify the results - check for any duplicate sort orders within groups
SELECT 
    ProjectId,
    IsVariationOfHomeModelId,
    SortOrder,
    COUNT(*) AS RecordCount
FROM 
    dbo.RSS_StandardHomeModel
WHERE 
    Deleted = 0
GROUP BY 
    ProjectId, 
    IsVariationOfHomeModelId, 
    SortOrder
HAVING 
    COUNT(*) > 1
ORDER BY 
    ProjectId, 
    IsVariationOfHomeModelId, 
    SortOrder;

-- If the above query returns no rows, then there are no duplicate sort orders within groups

-- Show the updated sort orders for verification
SELECT 
    StandardHomeModelId,
    ProjectId,
    IsVariationOfHomeModelId,
    Title,
    SortOrder
FROM 
    dbo.RSS_StandardHomeModel
WHERE 
    Deleted = 0
ORDER BY 
    ProjectId, 
    ISNULL(IsVariationOfHomeModelId, CAST('00000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER)), 
    SortOrder;
