(function () {
    'use strict';
    var controllerId = 'VariationScratchImportModalCtrl';
    angular.module('app')
    .controller(controllerId, [
        'common', '$scope', '$mdDialog', 'coreLoop', 'certificationservice', 'uuid4',
        'assessmentsoftwareservice', 'nccclimatezoneservice', 'assessmentcomplianceoptionservice', 'standardmodelservice',
        'zoneservice', 'zonetypeservice', 'zonesummaryservice',
        variationScratchImportModalController
    ]);
    function variationScratchImportModalController(
        common, $scope, $mdDialog, coreLoop, certificationservice, uuid4,
        assessmentsoftwareservice, nccclimatezoneservice, assessmentcomplianceoptionservice, standardmodelservice,
        zoneservice, zonetypeservice, zonesummaryservice
    ) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;
        vm.processingScratch = false;
        vm.showZoneResults = false;

        vm.softwareFileTypes = assessmentsoftwareservice.primarySoftwareFileTypes;
        vm.selectedAssessmentSoftware = null;
        vm.scratchData = {};

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        function initialise() {
            let certificationListPromise = certificationservice.getList().then(data => {
                vm.certificationList = data.data;
            });
            let softwareListPromise = assessmentsoftwareservice.getAll().then(data => {
                vm.assessmentSoftwareList = data;
            });
            let nccListPromise = nccclimatezoneservice.getList().then(data => {
                vm.nccClimateZoneList = data.data;
            });
            let activityListPromise = zoneservice.getZoneActivityList().then(data => {
                vm.zoneActivityList = data;
            });
            let zoneTypeListPromise = zonetypeservice.getList().then(data => {
                vm.zoneTypeList = data.data;
            });
            let classificationListPromise = zoneservice.getNccClassificationList().then(data => {
                vm.nccClassificationList = data;
            });
            Promise.all([certificationListPromise, softwareListPromise, nccListPromise, activityListPromise, zoneTypeListPromise, classificationListPromise]).then(() => {
                // ----------- //
                // - TESTING - //
                // ----------- //
                //vm.selectedAssessmentSoftware = TestDummyData.HomeDesignVariationScratchImport.selectedAssessmentSoftware;
                //vm.building = TestDummyData.HomeDesignVariationScratchImport_BuildingResult;
                //vm.processingScratch = false;
                //vm.showZoneResults = true;
                // ----------- //
                vm.initialised = true;
            });
        }
        initialise();

        // --------------------- //
        // - HANDLES (General) - //
        // --------------------- //

        // Cancel
        vm.cancel = function() {
            $mdDialog.cancel();
        }

        // --------------------- //
        // - HANDLES (Scratch) - //
        // --------------------- //

        // Select scratch file
        vm.selectScratchFile = function($file, prop) {
            vm.scratchData[prop] = {
                file: $file,
                name: $file.name
            };
        }

        // Scratch files valid
        vm.scratchFilesAreValid = function(data, fileTypes) {
            if (vm.selectedAssessmentSoftware == null)
                return false;
            // Note: Start on 1 to skip 'fileA' (assessment file)
            for (let i = 1; i < fileTypes.length; i++) {
                const fileType = fileTypes[i];
                if (vm.selectedAssessmentSoftware[fileType.prop + 'Required'] === true && data[fileType.prop] == null)
                    return false;
            }
            return true;
        }

        // Process
        vm.process = async function () {
            try {
                vm.processingScratch = true;

                const files = [];
                const b = await vm.scratchData.fileB.file.text();
                files.push(b);
                if (vm.scratchData.fileC?.file != null) {
                    const c = await vm.scratchData.fileC.file.text();
                    files.push(c);
                }

                const data = await assessmentcomplianceoptionservice.processScratchFiles(
                    vm.selectedAssessmentSoftware.assessmentSoftwareCode,
                    files
                );

                vm.building = {};
                await assessmentcomplianceoptionservice.applyScratchData(vm.building, data);

                vm.building.openings.forEach(o => {
                    o.elements.forEach(e => {
                        e.sector = common.determineSectorType(e.azimuth, zonesummaryservice.defaultSectors);
                    });
                });
                vm.building.surfaces.forEach(s => {
                    s.elements.forEach(e => {
                        e.sector = common.determineSectorType(e.azimuth, zonesummaryservice.defaultSectors);
                    });
                });

                // Run our core loop functions twice (this is in case some calcs at the end of the first loop affect
                // the start of the second loop and so on). More than twice shouldn't be needed...
                coreLoop.runCoreLoop(vm.building, 4); // Just use '4' since nccClimateZone calcs not required for this feature anyway
                coreLoop.runCoreLoop(vm.building, 4);

                // Only take storeys that have glazing when determining this value.
                var all = vm.building.openings.map(x => x.elements)
                                              .reduce((a, b) => [...a, ...b])
                                              .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing" || x.category.constructionCategoryCode === "InteriorGlazing")
                                              .map(x => x.storey);
                const storeysWithGlazing = [...new Set(all)];
                vm.selectedNumberOfStoreys = storeysWithGlazing.length;
                vm.selectedStoreys = vm.building.storeys.filter(x => storeysWithGlazing.some(y => y === x.floor));

                vm.processingScratch = false;
                vm.showZoneResults = true;

            } catch(e) {
                vm.processingScratch = false;
                throw e;
            }
        }

        // ------------------------ //
        // - HANDLES (Zones List) - //
        // ------------------------ //

        // Zone Number
        vm.updateSource = function (item, value, leadChar) {
            // If the value has been nullified, we wish to set the source back to 'AUTO'
            // and find the next available name for it.
            if (value == null || value == "" && item.zoneNumberSource != "AUTO") {
                item.zoneNumberSource = "AUTO";
                item.zoneNumber = vm.determineNextFreeZoneNumber(leadChar);
            } else if (item.zoneNumberSource == "AUTO") {
                // If the value is NOT null, we set the source as 'MANUAL' which ensures
                // it will not be re-calculated.
                item.zoneNumberSource = "MANUAL";
                // I don't think it's appropriate to rename other zones here (even ones which have not
                // been set manually...)
            }
        }

        // Zone Activity
        vm.interiorZoneActivityList = () => vm.zoneActivityList?.filter(x => x.availableFor === 'interior');
        vm.zoneActivityChanged = function (zoneActivity, zone) {
            if (zoneActivity == null || zone == null)
                return;
            let foundZones = vm.zoneTypeList?.filter(s => s.zoneTypeCode == zoneActivity.defaultZoneTypeCode);
            if (foundZones != null && foundZones.length > 0) {
                let foundZoneType = foundZones[0];
                zone.zoneType = foundZoneType;
                let foundNccClassifications = vm.nccClassificationList?.filter(s => s.nccClassificationCode == foundZoneType.defaultNccClassificationCode);
                if (foundNccClassifications != null && foundNccClassifications.length > 0)
                    zone.nccClassification = foundNccClassifications[0];
            }
            zone.conditioned = zoneActivity.defaultConditioned;
            zoneTypeChanged(zone.zoneType, zone);
        }

        // Interior NCC Classification List
        vm.interiorNccClassificationList = () => vm.nccClassificationList?.filter(x => x.availableFor !== 'exterior');

        // Zone Type
        vm.interiorZoneTypeList = () => vm.zoneTypeList?.filter(x => x.availableFor === 'interior' || x.availableFor === 'all');
        function zoneTypeChanged(zoneType, zone, autoselectNccAndConditioned) {
            if (zoneType) {
                // Assigned zonetype to actual lighting row.
                zone.zoneType = zoneType;
                zone.zoneTypeCode = zoneType.zoneTypeCode;
                zone.lampPowerMaximumWM2 = zoneType.lampPowerMaximumWM2;
                if (autoselectNccAndConditioned === true) {
                    let foundNccClassifications = vm.nccClassificationList?.filter(s => s.nccClassificationCode == zoneType.defaultNccClassificationCode);
                    if (foundNccClassifications != null && foundNccClassifications.length > 0)
                        zone.nccClassification = foundNccClassifications[0];
                    zone.conditioned = false;
                }
            } else {
                zone.lampPowerMaximumWM2 = null;
                // Nullify zonetype.
                zone.zoneType = null;
                zone.zoneTypeCode = null;
            }
            vm.calculateAllowance(zone);
            setTimeout(() => {
                vm.building.projectClassification = zonetypeservice
                    .determineProjectClassificationBasedOnZones(vm.building.zones);
            }, 100);
        }

        // Allowance
        vm.calculateAllowance = function (item) {
            if (item.floorArea == undefined || item.floorArea == null || item.lampPowerMaximumWM2 == undefined || item.lampPowerMaximumWM2 == null) {
                // We could enter this state if items were loaded via scratch file. So in this case, we want to
                // actually determine the lampPowerMaximumWM2 if possible (it's based on the zoneType)
                if (item.zoneType == null || item.zoneType.lampPowerMaximumWM2 == null)
                    return;
                else
                    item.lampPowerMaximumWM2 = item.zoneType.lampPowerMaximumWM2;
            }
                        item.lampPowerMaximumW = item.floorArea * item.lampPowerMaximumWM2;
            // Truncate to 1 decimal place
            var maxAllowance = String(item.lampPowerMaximumW);
            var split = maxAllowance.split('.');
            if (split.length == 2) {
                var decimal = split[1];
                if (decimal.length > 1) {
                    decimal = decimal.substring(0, 1);
                    maxAllowance = split[0] + "." + decimal;
                    item.lampPowerMaximumW = Number(maxAllowance);
                }
            }
        }

        // NCC Classification
        vm.availableInteriorNccClassificationList = function () {
            let interiorZones = vm.interiorZones();
            let allClassifications = vm.interiorNccClassificationList();
            if (interiorZones == null || interiorZones.length === 0) {
                return allClassifications;
            }
            let class10A = allClassifications?.find(x => x.nccClassificationCode === 'Class10A');
            if (class10A == null)
                return allClassifications;
            // Find the first interior zone which is NOT Class10a, check its NCC classification type, and limit based on that.
            let limiting = interiorZones.filter(x => x.nccClassification?.nccClassificationCode !== class10A.nccClassificationCode);
            // If we found no limiting zones, return all options
            if (limiting == null || limiting.length === 0) {
                return allClassifications;
            }
            return [limiting[0].nccClassification, class10A];
        }
        vm.onNccClassificationUpdate = function (item) {
            setTimeout(() => {
                vm.calculateAllowance(item);
                vm.building.projectClassification = zonetypeservice
                    .determineProjectClassificationBasedOnZones(vm.building.zones);
            }, 100);
        }

        // Floor Area
        vm.matchCeilingAreaToFloorArea = function (item) {
            item.ceilingArea = item.floorArea;
            common.forceBlurInputWithId("CeilingAreaInput" + item.zoneId)
        }

        // List zones
        vm.interiorZones = () => {
            const sortOn = vm.sortedInteriorZones != null
                            ? vm.sortedInteriorZones
                            : vm.building.zones;
            return zoneservice.interiorZones(sortOn);
        }

        // Re-number
        vm.renumberZones = function () {
            if (vm.sortCol == null && vm.sortDir == null) {
                for (let i = 0; i < vm.building.zones.length; i++) {
                    vm.building.zones[i].sortOrder = i;
                }
            }
        }

        // Sort
        vm.sortBy = function (zoneList, column) {
            if(vm[zoneList + "SortInfo"] == null)
                vm[zoneList + "SortInfo"]  = new SortInfo();
            const matchingZones = zoneList === 'Interior'
                ? zoneservice.interiorZones(vm.building.zones)
                : vm.building.zones.filter(x => x.zoneActivity != null &&
                                              x.zoneActivity.zoneActivityCode === `ZA${zoneList}`);
            common.setSort(column, vm[zoneList + "SortInfo"]);
            vm['sorted' + zoneList + 'Zones'] = common.applySort(matchingZones, vm[zoneList + "SortInfo"]);
        }

        // Add zone
        vm.addZone = function (zones, activityCode, leadChar) {
            if (vm.building.zones == null) {
                vm.building.zones = [];
            }
            let zx = vm.zoneActivityList?.filter(x => x.zoneActivityCode === activityCode)[0];
            vm.building.zones.push({
                zoneId: uuid4.generate(),
                linkId: uuid4.generate(),
                floorArea: null,
                zoneNumber: vm.determineNextFreeZoneNumber(leadChar),
                zoneNumberSource: "AUTO",
                zoneActivity: zx,
                naturallyVentilated: null
            });
            vm.renumberZones();
            vm.sortedInteriorZones = null;
            vm.sortedInteriorZones = common.applySort(vm.interiorZones(), vm.InteriorSortInfo);
        }

        // Determine next zone number
        vm.determineNextFreeZoneNumber = function (leadChar) {
            leadChar = leadChar.toUpperCase();
            // Get all existing zones that match the format Z### (Regardless of SOURCE).
            let pattern = `${leadChar}(\\d{3})`
            let regEx = new RegExp(pattern);
            let matches = [];
            let existingZones = vm.building?.zones;
            existingZones.forEach(zone => {
                let matching = regEx.exec(zone.zoneNumber);
                // We add a check for length so that things like "ZZ0044", "Z002z" etc don't match.
                if (matching != null && matching.input.length == 4)
                    matches.push(zone.zoneNumber);
            });
            // Convert to actual numbers (I.e. Z001 => 1 etc);
            let converted = [];
            matches.forEach(zn => {
                converted.push(parseInt(zn.slice(1)));
            });
            // Loop over until we find a free slot.
            for (let i = 1; i < 10000; i++) {
                if (converted.some(c => c == i))
                    continue;
                else {
                    return leadChar + common.addLeadingZero(i, 3);
                }
            }
            // Failure state.
            return "!###";
        }

        // Remove zone
        vm.removeZone = function (row) {
            for (var ii = 0, ilen = vm.building.zones.length; ii < ilen; ii++) {
                if (vm.building.zones[ii].zoneId == row.zoneId) {
                    vm.building.zones.splice(ii, 1);
                    //vm.renumberZones();
                    //vm.sortedInteriorZones = null;
                    //vm.sortedInteriorZones = common.applySort(vm.interiorZones(), vm.InteriorSortInfo);
                    return;
                }
            }
        }
        vm.removeZones = function (zones) {
            zones.forEach(zone => {
                removeZone(zone);
            });
        }

        // Clone zone
        vm.cloneZone = function (row) {
            if (row == null)
                return;
            const leadChar = determineLeadCharFromZone(row);
            let index = vm.building.zones.findIndex(x => x === row); // Original index
            let n = angular.copy(row);
            n.zoneId = uuid4.generate();
            n.zoneNumber = vm.determineNextFreeZoneNumber(leadChar);
            n.zoneNumberSource = "AUTO";
            n.zoneDescription = (row.zoneDescription ?? "") + " - Copy";
            vm.building.zones.splice(index + 1, 0, n);
            vm.renumberZones();
        }

        // None selected
        vm.noneSelected = function (code) {
            return !vm.building?.zones?.some(x => x[code]);
        }

        function determineLeadCharFromZone(z) {
            if (zoneservice.isInterior(z))
                return "Z";
            if (z.zoneActivity?.zoneActivityCode === "ZARoofSpace")
                return "R";
            if (z.zoneActivity?.zoneActivityCode === "ZASubfloorSpace")
                return "S";
            if (z.zoneActivity?.zoneActivityCode === "ZAGroundSurface")
                return "G"
            return "Z";
        }

        // Save
        vm.saveResults = async function () {

            // Only save required data
            // Filter out storeys that are no longer associated with any zones
            const usedStoreys = vm.building.zones
                .filter(z => z.storey !== null && z.storey !== undefined)
                .map(z => z.storey);

            // Get unique storey values
            const uniqueUsedStoreys = [...new Set(usedStoreys)];

            // Filter the storeys to only include those that are still used by zones
            const filteredStoreys = vm.building.storeys.filter(s =>
                uniqueUsedStoreys.includes(s.floor));

            let summaryBuilding = {
                storeys: filteredStoreys,
                surfaces: vm.building.surfaces.map(s => ({
                    category: s.category,
                    sector: s.sector,
                    performance: s.performance,
                    elements: s.elements.map(e => ({
                        adjacentZoneNumber: e.adjacentZoneNumber,
                        adjacentZoneDescription: vm.building.zones.find(z => z.zoneNumber == e.adjacentZoneNumber)?.zoneDescription,
                        parentZoneId: e.parentZoneId,
                        category: e.category,
                        netArea: e.netArea,
                        grossArea: e.grossArea,
                        sector: e.sector,
                        horizontalProjection1: e.horizontalProjection1,
                        horizontalProjection2: e.horizontalProjection2,
                        verticalScreen1: e.verticalScreen1,
                        verticalScreen2: e.verticalScreen2,
                        verticalScreen3: e.verticalScreen3,
                        storey: e.storey
                    }))
                })),
                openings: vm.building.openings.map(o => ({
                    category: o.category,
                    sector: o.sector,
                    performance: o.performance,
                    elements: o.elements.map(e => ({
                        parentZoneId: e.parentZoneId,
                        category: e.category,
                        grossArea: e.grossArea,
                        sector: e.sector,
                        horizontalProjection1: e.horizontalProjection1,
                        horizontalProjection2: e.horizontalProjection2,
                        verticalScreen1: e.verticalScreen1,
                        verticalScreen2: e.verticalScreen2,
                        verticalScreen3: e.verticalScreen3,
                        storey: e.storey
                    }))
                })),
                zones: vm.building.zones.map(z => ({
                    zoneId: z.zoneId,
                    zoneNumber: z.zoneNumber,
                    zoneDescription: z.zoneDescription,
                    zoneType: z.zoneType,
                    zoneActivity: z.zoneActivity,
                    storey: z.storey,
                    linkId: z.linkId,
                    nccClassification: z.nccClassification,
                    floorArea: z.floorArea,
                    volume: z.volume,
                    conditioned: z.conditioned,
                    naturalLightRequiredPercent: z.naturalLightRequiredPercent,
                    naturalLightRequiredM2: z.naturalLightRequiredM2,
                    naturalLightAchievedM2: z.naturalLightAchievedM2,
                    ventilationRequiredPercent: z.ventilationRequiredPercent,
                    ventilationRequiredM2: z.ventilationRequiredM2,
                    ventilationAchievedM2: z.ventilationAchievedM2,
                    airMovementRequiredPercent: z.airMovementRequiredPercent,
                    airMovementRequiredM2: z.airMovementRequiredM2,
                    airMovementAchievedM2: z.airMovementAchievedM2,
                    exteriorWallArea: z.exteriorWallArea,
                    exteriorGlazingArea: z.exteriorGlazingArea,
                }))
            };

            // Close and pass back summary data
            $mdDialog.hide(summaryBuilding);
        }

    }
})();