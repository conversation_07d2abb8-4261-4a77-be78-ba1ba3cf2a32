(function () {
    // The BuildingexposureUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'BuildingexposureUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'buildingexposureservice', 
      'security', buildingexposureUpdateController]);
function buildingexposureUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  buildingexposureservice, 
      securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Building Exposure';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.buildingExposureCode = null;
        vm.buildingexposure = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');  

        if (vm.newRecord) {
            vm.title = "New Building Exposure";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.buildingExposureCode = $scope.buildingExposureCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.buildingExposureCode = $stateParams.buildingExposureCode;
        }

        // Get data for object to display on page
        var buildingExposureCodePromise = null;
        if (vm.buildingExposureCode != null) {
            buildingExposureCodePromise = buildingexposureservice.getBuildingExposure(vm.buildingExposureCode)
            .then(function (data) {
                if (data != null) {
                    vm.buildingexposure = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("buildingexposure-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                buildingexposureservice.createBuildingExposure(vm.buildingexposure).then(function(data){
                    vm.buildingexposure = data;
                    vm.buildingExposureCode = vm.buildingexposure.buildingExposureCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                buildingexposureservice.updateBuildingExposure(vm.buildingexposure).then(function(data){
                    if (data != null) {
                        vm.buildingexposure = data;
                        vm.buildingExposureCode = vm.buildingexposure.buildingExposureCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            buildingexposureservice.deleteBuildingExposure(vm.buildingExposureCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            buildingexposureservice.undoDeleteBuildingExposure(vm.buildingExposureCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();