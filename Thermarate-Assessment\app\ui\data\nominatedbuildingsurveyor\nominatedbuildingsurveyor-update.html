<form name="NominatedBuildingSurveyorForm" 
      class="main-content-wrapper" 
      novalidate 
      data-ng-controller='NominatedBuildingSurveyorUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex">
                    <md-card>
                        <md-card-header>
                            Nominated Building Surveyor
                        </md-card-header>
                        <md-card-content>

                            <fieldset redi-enable-roles="settings__settings__edit">

                                <!-- Description -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Description</label>
                                    <input type="text" name="description"
                                           ng-model="vm.buildingSurveyor.description" md-autofocus
                                           md-maxlength="200"
                                           ng-disabled="!vm.newRecord"
                                           required />
                                    <div ng-messages="NominatedBuildingSurveyorForm.description.$error">
                                        <div ng-message="required">Description is required.</div>
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 200.</div>
                                    </div>
                                </md-input-container>

                                <!-- Contact Name -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Contact Name</label>
                                    <input type="text" name="contactName"
                                           ng-model="vm.buildingSurveyor.contactName"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="NominatedBuildingSurveyorForm.contactName.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Email Address -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Email Address</label>
                                    <input type="text" name="emailAddress"
                                           ng-model="vm.buildingSurveyor.emailAddress"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="NominatedBuildingSurveyorForm.emailAddress.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Physical Address -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Physical Address</label>
                                    <input type="text" name="physicalAddress"
                                           ng-model="vm.buildingSurveyor.physicalAddress"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="NominatedBuildingSurveyorForm.physicalAddress.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                                <!-- Phone Number -->
                                <md-input-container class="md-block" flex-gt-sm>
                                    <label>Phone Number</label>
                                    <input type="text" name="phoneNumber"
                                           ng-model="vm.buildingSurveyor.phoneNumber"
                                           md-maxlength="100"
                                           required />
                                    <div ng-messages="NominatedBuildingSurveyorForm.phoneNumber.$error">
                                        <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                                    </div>
                                </md-input-container>

                            </fieldset>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.buildingSurveyor"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           ng-disabled="NominatedBuildingSurveyorForm.$invalid || vm.editPermission == false" 
                           ng-show="vm.buildingSurveyor.deleted!=true" 
                           ng-click="vm.save()">
                    Save
                </md-button>
                <md-button class="md-raised" 
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.buildingSurveyor.buildingSurveyorId!=null && vm.buildingSurveyor.deleted!=true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                    Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.buildingSurveyor.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                    Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
