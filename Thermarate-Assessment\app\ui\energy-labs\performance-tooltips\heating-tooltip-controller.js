(function () {

  'use strict';
  angular
    .module('app')
    .component('elHeatingTooltip', {
      bindings: {
        source: '<',        // The 'StandardHomeModel' used as the basis of the option data
        assessmentMethod: '<',
        heatingCoolingLoadLimits: '<',
      },
      templateUrl: 'app/ui/energy-labs/performance-tooltips/heating-tooltip.html',
      controller: ElHeatingTooltipController,
      controllerAs: 'vm'
    });

  ElHeatingTooltipController.$inject = ['common'];

  function ElHeatingTooltipController(common) {

    let vm = this;

    vm.determineTarget = function () {
        if (vm.assessmentMethod == "House Energy Rating (HER)" && (vm.heatingCoolingLoadLimits == false || vm.source.energyLoadLimits.heatingLoadLimit?.toFixed(1) == null)) {
            return 'N/A';
        } else {
            return vm.source.energyLoadLimits.heatingLoadLimit?.toFixed(1);
        }
    }

    vm.determineCalcResultColour = function() {
        if (vm.assessmentMethod == "House Energy Rating (HER)" && vm.heatingCoolingLoadLimits == false || vm.source.energyLoadLimits.heatingLoadLimit == null) {
            return 'black';
        } else if (common.lessThanOrEqualish(vm.source.heatingLoad, vm.source.energyLoadLimits.heatingLoadLimit, 1)) {
            return 'var(--thermarate-green)';
        } else {
            return 'var(--warning)';
        }
    }
  }

})();