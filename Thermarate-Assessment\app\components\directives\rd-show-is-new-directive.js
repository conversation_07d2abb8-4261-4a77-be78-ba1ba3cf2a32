﻿(function () {
    'use strict';

    var app = angular.module('app');
    /*!
    * Show row is new
    * Check if row is new (created today or yesterday).  Add label with New text
    * @version v1.0.0
    * @link https://github.com/TheSharpieOne/angular-input-match
    * @license MIT License, http://www.opensource.org/licenses/MIT
    */
    app.directive('rdShowIsNew', match);
    function match() {
        return {
            restrict: 'A',
            scope: {
                rdShowIsNew: '='
            },
            link: function (scope, elem, attrs) {
                if (scope.rdShowIsNew) {
                    if (moment(scope.rdShowIsNew).isAfter(moment().subtract(2, 'day'), 'd')) {
                        elem.append("<span class='label label-success label-extra-small' title='This record was added today'>NEW</span>");
                    }
                }
            }
        };
    }
})();