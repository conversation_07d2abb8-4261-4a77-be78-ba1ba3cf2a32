// Name: wadesigncodeservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'wadesigncodeservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', wadesigncodeservice]);

    function wadesigncodeservice(common, config, $http) {

        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'wadesigncode/';
        
        const cache = {
            serviceTypesForCategory: {},
            heatingSystemTypesForCategory: {},
        };

            
        function handleSuccess(response, popupMessage = null) {
            if (response != null && response.data !== undefined) {
                
                if (popupMessage != null)
                    log.logSuccess(popupMessage);

                return response.data;
            }
            else {
                return null;
            }
        }

        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        
        function getList() {
            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            return $http({
                url: wkUrl,
                method: 'GET',
                isArray: true,
                timeout: canceller.promise,
            }).then(handleSuccess, (error) => {

                if (error.status == 0 || error.status == -1) {
                    return;
                } else
                    handleFail(error, "Error getting WA Design Codes list.");
            });
        }
        
        function waDesignCodesForCategoryCode(code, waDesignCodes) {
            if(waDesignCodes == null || waDesignCodes.length === 0)
                return [];
            
            return  waDesignCodes.filter(x => x.forDesignCodeCategory === code);
        }
        
        const service = {
            getList,
            waDesignCodesForCategoryCode,
        };

        return service;

    }
})();
