/**
* Contains methods required to calculate certain values for the NCC 2022 glazing calculator. This basically
* corresponds to V2 of AB-Sim, with modifications as required to work with the admin portals data structures.
*/
(function () {
    'use strict';
    var serviceId = 'certification2022service';
    angular.module('appservices').factory(serviceId, ['common', 'config', '$http', certification2022service]);

    function certification2022service(common) {

        var service = {
            calculateFloorAreas: calculateFloorAreas,
            calculateDirectAreaV2: calculateDirectAreaV2,
            calculateSuspendedAreaV2: calculateSuspendedAreaV2,
            calculateOverRoomsAreaV2: calculateOverRoomsAreaV2,
        };
            
        return service;
        
        /**
        * Calculates the direct, suspended and over room area totals.
        *
        * @param storeys All storeys within the building.
        * @param zones All zones within the building.
        * @param surfaces All 'surface' constructions, NOT the children
        *
        * @returns {{direct: number, overRooms: number, suspended: number}}
        */
        function calculateFloorAreas(storeys, zones, surfaces) {

            // Loop over each storey. For each zone within that storey, calculate the areas.
            // The storeys total will be the total of the zones within the storey. For NCC 2022 we only care about the
            // building totals anyway (whereas NCC 2019 exports 1 glazing calc per storey).
                
            // "Flatten" surfaces into all child elements
            surfaces = surfaces.map(x => x.elements).reduce((a, b) => [...a, ...b]);

            let directTotal    = 0;
            let suspendedTotal = 0;
            let overRoomsTotal = 0;

            storeys.forEach(storey => {

                // Filter by activity codes we care about (Essentially, habitable ones)
                const zonesInStorey = zones.filter(zone => zone.storey === storey.floor &&
                    (zone.zoneActivity.zoneActivityCode === "ZAKitchenLiving" ||
                        zone.zoneActivity.zoneActivityCode === "ZALiving" ||
                        zone.zoneActivity.zoneActivityCode === "ZADayTime" ||
                        zone.zoneActivity.zoneActivityCode === "ZABedroom" ||
                        zone.zoneActivity.zoneActivityCode === "ZANightTime" ||
                        zone.zoneActivity.zoneActivityCode === "ZAUnconditioned" ||
                        zone.zoneActivity.zoneActivityCode === "ZAGarageConditioned"));

                zonesInStorey.forEach(zone => {

                    const constructionsInZone = surfaces.filter(x => x.parentZoneId === zone.linkId);

                    const direct    = this.calculateDirectAreaV2(zone, constructionsInZone);
                    const suspended = this.calculateSuspendedAreaV2(zone, constructionsInZone);
                    const overRooms = this.calculateOverRoomsAreaV2(zone, constructionsInZone);

                    directTotal    += direct;
                    suspendedTotal += suspended;
                    overRoomsTotal += overRooms;

                });
            });

            return { direct: Number(directTotal), suspended: Number(suspendedTotal), overRooms: Number(overRoomsTotal) };

        }

        /**
        * Direct Contact = sum of area for all Exterior Floor (Connected to Ground)
        */
        function calculateDirectAreaV2(zone, constructionsInZone) {
            const sum = constructionsInZone
                ?.filter(x => x.category.constructionCategoryCode === "GroundFloor")
                ?.map(x => x.netArea)
                ?.reduce((a, b) => a + b, 0);

            return sum ?? 0;
        }


        /** Suspended = sum of area for all Exterior Floor (Suspended) */
        function calculateSuspendedAreaV2(zone, constructionsInZone) {

            // a.For every zone with Zone Activity = Kitchen / Living, Living, Day Time, Bedroom, Night Time, Unconditioned, Garage or Garage Conditioned, SUM the area values for ALL of the following:
            //    i.floors with construction code 191 - 240;
            const sum = constructionsInZone
                ?.filter(x => x.category.constructionCategoryCode === "ExteriorFloor" || 
                                x.category.constructionCategoryCode === "ExteriorFloorElevated")
                ?.map(x => x.netArea)
                ?.reduce((a, b) => a + b, 0) ?? 0;

            return sum;
        }

        /**
        * Over rooms in this dwelling =  sum of area for all Intermediate Floor AND Intermediate Floor (Neighbour
        * Below)
        */
        function calculateOverRoomsAreaV2(zone, constructions) {

            const sum = constructions
                ?.filter(x => x.category.constructionCategoryCode === "IntermediateFloor" ||
                    x.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow")
                ?.map(x => x.netArea)
                ?.reduce((a, b) => a + b, 0) ?? 0.0;

            return sum;
        }

    }
})();
