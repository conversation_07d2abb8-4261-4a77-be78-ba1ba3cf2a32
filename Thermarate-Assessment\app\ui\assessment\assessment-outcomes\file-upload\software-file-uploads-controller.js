// At one point in time these were referred to as 'Assessment Artificial Lighting' within
// the UI. Although they are now referred to as 'Building Floor Areas' we have not updated
// our naming within the codebase.
(function () {

    'use strict';
    angular
        .module('app')
        .component('softwareFileUploads', {
            bindings: {
                option: '<',        // Compliance option the building is for.
                assessment: '<',
                disabled: '<',
                jobFiles: '<',
                constructionCategoryList: '<',
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/file-upload/software-file-uploads.html',
            controller: SoftwareFileUploads,
            controllerAs: 'vm'
        });

    SoftwareFileUploads.$inject = ['$http', 'common', 'bootstrap.dialog', 'security', 'assessmentcomplianceoptionservice', 'assessmentsoftwareservice', 'coreLoop', 'uuid4', 'fileservice'];

    function SoftwareFileUploads($http, common, modalDialog, securityservice, assessmentcomplianceoptionservice, assessmentsoftwareservice, coreLoop, uuid4, fileservice) {

        var vm = this;
        let log = common.logger;

        vm.permission_field_assessmentsoftware_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__view']);
        vm.permission_field_assessmentsoftware_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit']);

        vm.dragDropDisabled = function () {
            return !vm.permission_field_assessmentsoftware_view || !vm.permission_field_assessmentsoftware_edit || vm.disabled;
        };

        vm.referenceIsRequired = function () {
            return vm.option?.complianceMethod.complianceMethodCode === 'CMPerfSolution' ||
                   vm.option?.complianceMethod.complianceMethodCode === 'CMPerfSolutionDTS';
        }

        vm.referenceName = function (shortened) {
            return vm.option?.complianceMethod.complianceMethodCode === 'CMPerfSolution' ? 'Reference' : (shortened ? 'DTS' : 'Deemed-to-Satisfy')
        }

        vm.softwareFileIsRequired = function (prop) {
            return (vm.option?.assessmentSoftware != null && vm.option?.assessmentSoftware[prop + 'Required']);
        }

        vm.softwareFileDisplayName = function (prop) {
            return (vm.option?.assessmentSoftware != null && vm.option?.assessmentSoftware[prop + 'Name']);
        }

        vm.glazingIsRequired = function () {
            return !(vm.option.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" ||
                   vm.option.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" ||
                   vm.option.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER" ||
                   vm.option.complianceMethod.complianceMethodCode === "CMPerfELL");
        }

        function requiredSoftwareFileTypes() {
            return vm.softwareFileTypes.filter(t => vm.softwareFileIsRequired(t.prop));
        }

        /**
         * Checks to see if the required files have been uploaded before allowing them
         * to be processed.
         */
        vm.requiredFilesAreUploaded = function (option, building) {

            let canProcess = true;

            for (let i = 0; i < vm.softwareFileTypes.length; i++) {

                if (option?.assessmentSoftware == null) {
                    canProcess = false;
                    break;
                }

                let fileIsRequired = option?.assessmentSoftware[vm.softwareFileTypes[i].prop + "Required"] == true;

                if (fileIsRequired && building[vm.softwareFileTypes[i].prop] == null) {
                    canProcess = false;
                    break;
                }

            }

            return canProcess;
        }

        vm.uploadFiles = function ($files, target) {

            if ($files == null || $files.length == 0 || $files.every(f => f == null))
                return;

            let allRequiredTypes = requiredSoftwareFileTypes();

            $files.forEach(async $file => {

                // Get matching software file type based on extension, if this gives multiple results then match by name
                let name = $file.name.toLowerCase();
                let extension = $file.name.includes('.') ? '.' + $file.name.split('.').pop() : null;
                let matchingType = null;
                let matchingTypesByExtension = allRequiredTypes.filter(t => vm.option.assessmentSoftware[`${t.prop}Extension`] === extension);

                // First check if "Output Summary File" by checking extension is either dt2/txt and contents contains 12 lines
                if ((extension == ".dt2" || extension == ".txt") && (await common.getFileLines($file, true)).length == 12) {
                    matchingType = allRequiredTypes.find(t => t.prop == "fileE");
                }
                // Match by extension
                else if (matchingTypesByExtension.length == 1) {
                    matchingType = matchingTypesByExtension[0];
                // Match by name
                } else {
                    let possibleMatchingNames = ['assessment', 'scratch', 'zone', 'map', 'output', 'natrep'];
                    let matchingTypeByName = allRequiredTypes.find(t => {
                        let fileTypeName = vm.option.assessmentSoftware[`${t.prop}Name`]?.toLowerCase();
                        return fileTypeName != null && possibleMatchingNames.some(n => fileTypeName.includes(n) && name.includes(n));
                    });
                    matchingType = matchingTypeByName;
                }

                // Upload
                if (matchingType != null) {
                    let field = matchingType.prop;
                    let category = 'Simulation Data';
                    let classification = vm.softwareFileDisplayName(matchingType.prop);
                    common.uploadSoftwareFile(
                        vm.assessment,
                        $file,
                        target,
                        field,
                        vm.jobFiles,
                        category,
                        classification,
                        null,
                        null
                    );
                }

            });
        }

        vm.downloadAll = async function (targetName, filesCategory) {
            let fileIds = requiredSoftwareFileTypes().map(t => vm.option[targetName][t.prop]?.fileId).filter(id => id != null);
            let base64String = await fileservice.zipFilesFromIds(fileIds);
            fileservice.downloadFile(
                {url: `data:application/zip;base64,${base64String}`},
                `${vm.assessment.assessmentProjectDetail.clientJobNumber} - ${(vm.option.optionIndex == 0 ? "Baseline" : "Option " + vm.option.optionIndex)} - Software Files - ${filesCategory}`
            );
        }

        vm.deleteAll = function (target, filesCategory) {
            modalDialog.confirmationDialog("Delete Software Files", `Are you sure you want to delete the ${filesCategory} software files?`, "Ok", "Cancel", null).then(function success() {
                vm.softwareFileTypes.forEach(type => {
                    if (target != undefined && target != null && target[type.prop] != undefined) {
                        target[type.prop] = null;
                        if (target[type.prop + "Id"] != undefined) {
                            target[type.prop + "Id"] = null;
                        }
                    }
                });
            });
        }

        /**
         * Calls the server to process the files at the passed in URLs. This way the data that is processed
         * is always what the user sees in the uploads section. Since changes to both software files AND
         * new zone data will only be saved with the full assessment, this means
         *
         * @param {any} option The compliance option to process.
         * @param {any} processReference If true, will process the reference building. Otherwise, proposed.
         */
        vm.processFiles = function (option, processReference = false, processWithMismatch = false) {

            let building = processReference ? option.reference : option.proposed;
            building.processingScratch = true;

            vm.isProcessingScratch = true;
            assessmentcomplianceoptionservice
                .processSimulationFiles(
                    option.complianceOptionsId,
                    building.assessmentComplianceBuildingId,
                    option.assessmentSoftware.assessmentSoftwareCode,
                    building.fileB?.url,
                    building.fileC?.url,
                    building.fileD?.url,
                    building.fileE?.url,
                    Number(vm.assessment?.natHERSClimateZone?.description),
                  processWithMismatch)
                .then(async data => {

                    // If this is the proposed building AND the reference building has NO scratch file attached to
                    // it, then also apply this data to the REFERENCE building.
                    await assessmentcomplianceoptionservice.applyScratchData(building, data);

                    // Run core loop twice (to prepare to the call to computeEnergyUsage).
                    coreLoop.runCoreLoop(building, Number(vm.assessment.nccClimateZone.description), Number(vm.assessment.natHERSClimateZone.description));
                    coreLoop.runCoreLoop(building, Number(vm.assessment.nccClimateZone.description), Number(vm.assessment.natHERSClimateZone.description));

                    if(!processReference || (processReference && vm.referenceIsRequired)) {
                        coreLoop.computeEnergyUsage(building, option);

                        // Store originals for later.
                        building.heatingOriginal         = building.heating;
                        building.coolingOriginal         = building.cooling;
                        building.totalEnergyLoadOriginal = building.totalEnergyLoad;
                        building.houseEnergyRatingOverride = null;
                        building.overrideEnergyLoads = false;
                    }

                    if(processReference === false &&
                        option.isBaselineSimulation === true && option.reference.fileB == null) {
                        console.log("No reference scratch file present, copying data into reference as well.")

                        // Before we apply data we have to re-assign the zone ID's or it will write over on saving to DB.
                        const newZones = angular.copy(data.zones);
                        newZones.forEach(x => x.zoneId = uuid4.generate());
                        data.zones = newZones;

                        await assessmentcomplianceoptionservice.applyScratchData(option.reference, data);

                        coreLoop.runCoreLoop(option.reference, Number(vm.assessment.nccClimateZone.description), Number(vm.assessment.natHERSClimateZone.description));
                        coreLoop.runCoreLoop(option.reference, Number(vm.assessment.nccClimateZone.description), Number(vm.assessment.natHERSClimateZone.description));

                        if(vm.referenceIsRequired()) {
                            coreLoop.computeEnergyUsage(option.reference, option);
                            option.reference.heatingOriginal         = option.reference.heating;
                            option.reference.coolingOriginal         = option.reference.cooling;
                            option.reference.totalEnergyLoadOriginal = option.reference.totalEnergyLoad;
                            option.reference.houseEnergyRatingOverride = null;
                            option.reference.overrideEnergyLoads = false;
                        }
                    }

                    // Refresh this random bit of data so anything watching it knows to update.
                    building.scratchFileProcessedWatchProperty = Math.random();

                }, async error => {

                    try {

                        if(error.data?.message === "OUTPUT_SUMMARY_MISMATCH") {

                            let result = await modalDialog.confirmationDialog(
                              "Warning",
                              "There is a mismatch between the uploaded Output File and the Natrep File. Would you like to cancel and upload a new file or process the files anyway?",
                              "Process",
                              "Cancel");

                            // Note, if cancelled, this will never be hit and the finally block below will be called.
                            vm.processFiles(option, processReference, true);

                        } else
                            building.processingScratch = false;

                    } catch (e) {
                        building.processingScratch = false;
                    }

                });

        }

        // All primary file types
        vm.softwareFileTypes = assessmentsoftwareservice.softwareFileTypes;

        /** Since these values can now differ, we want to take the highest to loop over */
        vm.greatestStoreys = function () {
            return vm.option.proposed.storeys?.length >= vm.option.reference.storeys?.length
                ? vm.option.proposed.storeys
                : vm.option.reference.storeys;
        }

    }
})();