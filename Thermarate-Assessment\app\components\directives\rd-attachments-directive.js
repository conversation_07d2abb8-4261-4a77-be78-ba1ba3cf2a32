﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('rdAttachments', ['attachmentservice', 'speciesservice', '$timeout', function (attachmentservice, speciesservice, $timeout) {
        //Usage:
        //<div rd-attachments rd-key-name='contactId' rd-key='vm.contact.contactId' rd-upload-allowed='true'></div> 
        var directive = {
            link: link,
            restrict: 'AE',
            transclude: true,
            templateUrl: 'app/components/directives/templates/attachments-tpl.html',
            scope: {
                'rdKeyName': '@',
                'rdKey': '=',
                'rdUploadAllowed': '=',
                'rdDataCrop': '=',
                'rdTitle': '@',
                'rdListHeight': '@',
            },
        };
        return directive;

        function link(scope, element, attrs, ctrl) {

            scope.maxHeightStyle = {};
            if (attrs.rdListHeight != undefined && attrs.rdListHeight != null) {
                scope.maxHeightStyle = { 'max-height': attrs.rdListHeight, 'overflow-y': 'auto' };
            }


            scope.refreshList = function () {

                    scope.parentKey = scope.rdKey;
                    var imgArr = speciesservice.getImageArray();

                    var externalIdArr = [];

                    for(var i = 0, len = imgArr.length; i < len; i++){
                        externalIdArr.push(imgArr[i].externalId);
                    }

                    attachmentservice.getAttachmentList(scope.rdKeyName, scope.rdKey, externalIdArr).then(function (data) {
                        scope.attachments = data;
                        scope.uploadModel = null;

                        // This ensures the primary image is selected in the view, and reflects any changes on the client side image array used to perform bulk image saves
                        if (scope.attachments != undefined && scope.attachments != null && imgArr!= undefined && imgArr != null & imgArr.length > 0) {

                            if (scope.attachments.length > 0) {

                                // Get the primary image and ensure it is set on client-side image array
                                // Note: This is only important when an image has been removed, and the server side fetches the first image in 
                                // the database via the species Id and sets it as primary image.
                                // Thus, in order for the change to reflect back on the client-side, the image array must be updated as well
                                if (imgArr[0].speciesId != null && imgArr[0].speciesId > 0) {
                                    var result = $.grep(scope.attachments, function (e) { return e.isPrimary; });
                                    speciesservice.buildImageArray(result[0]);
                                } else {
                                    // The following statements are executed if the end-user is creating a New Species. This ensure that if the primary image is deleted
                                    // the next image is selected as the new primary, and the change is reflected on the client side image array (ie. temporary array)
                                    var result = $.grep(imgArr, function (e) { return e.isPrimary; });

                                    for (var ii = 0; ii < scope.attachments.length; ii++) {
                                        if (result[0].speciesImageId == scope.attachments[ii].speciesImageId) {
                                            scope.attachments[ii].isPrimary = true;
                                            break;
                                        }
                                    }

                                }
                            }
                        }

                    });
            }

            scope.updatePrimaryImage = function (att) {

                for (var i = 0, len = scope.attachments.length; i < len; ++i) {

                    if (att.externalId == scope.attachments[i].externalId) {
                        scope.attachments[i].isPrimary = true;
                    } else {
                        scope.attachments[i].isPrimary = false;
                    }

                    // Update Image array
                    speciesservice.buildImageArray(scope.attachments[i]);

                }           

            };

            $timeout(function () {
                scope.$apply(function () {
                    scope.parentKey = scope.rdKey;
                    scope.refreshList();
                })
            }, 900);

            var lastSavedUuid = null;
            scope.uploadComplete = function (info) {

                if (info.count > 1) {
                    var fileArray = info.files();
                    for (var i = 0; i < fileArray.length; i++) {
                        saveFile(fileArray[i]);
                    }
                }
                else {
                    saveFile(info);              
                }
                return true;
            }

            function saveFile(info) {
                var attachment = {};
                if (info.uuid == lastSavedUuid) {
                    return; // Already saved.
                }
                lastSavedUuid = info.uuid;

                //If only one attachment, then upload the image as the Primary image
                if (scope.attachments != 'undefined') {
                    if (scope.attachments.length == 0) {
                        attachment.isPrimary = true;
                    } else {
                        attachment.isPrimary = info.isPrimary;
                    }
                } else {
                    attachment.isPrimary = info.isPrimary;
                }

                attachment.parentKey = scope.rdKey;
                attachment.speciesImageId = info.speciesImageId;
                attachment.speciesId = info.speciesId;
                attachment.name = info.uuid;
                attachment.externalId = info.uuid;
                attachment.displayName = info.name;
                attachment.notes = "";
                attachment.url = info.cdnUrl;
                attachment.baseUrl = info.originalUrl;
                attachment.size = info.size;
                attachmentservice.saveAttachment(attachment).then(function (data) {

                    speciesservice.buildImageArray(data);

                    var imgArr = speciesservice.getImageArray();

                    var externalIdArr = [];

                    for (var i = 0, len = imgArr.length; i < len; i++) {
                        externalIdArr.push(imgArr[i].externalId);
                    }

                    attachmentservice.getAttachmentList(scope.rdKeyName, scope.rdKey, externalIdArr).then(function (data) {
                        scope.attachments = data;
                        scope.uploadModel = null;
                    });

                    scope.refreshList();
                });
            }

            scope.deleteAttachment = function (attachment) {
                attachmentservice.deleteAttachment(attachment.speciesImageId).then(function () {

                    speciesservice.removeImageArr(attachment.externalId);

                    scope.refreshList();
                });
            }

            scope.$watch('rdKey', function (newValue, oldValue) {

                if (newValue != undefined && newValue != null) {
                    newValue = newValue.toString();
                }

                if (newValue !== oldValue) {
                    scope.refreshList();
                }
            }, false);

            var newwindow = null;
            scope.displayInNewWindow = function (event, url) {
                event.preventDefault();
                newwindow = window.open(url, 'name', 'height=900,width=1000,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=no');
                if (window.focus) { newwindow.focus() }
                return false;
            }
        }
    }]);
})();