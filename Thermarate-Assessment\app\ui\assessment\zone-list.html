<!-- Zones List + Template Selector -->

<!-- Template Selector -->
<md-card ng-if="vm.enableTemplateSelection">
    <md-card-content>

        <!-- Template Selection + Copy to -> from buttons -->
        <div class="layout-column">

            <!-- Template Selection -->
            <md-input-container ng-if="vm.enableTemplateSelection"
                                class="md-block kindly-remove-error-spacer vertically-condensed"
                                flex-gt-sm>

                <label>Design Template</label>
                <div layout="row">
                    <md-select name="template"
                               flex="100"
                               ng-model="vm.source.buildingZonesTemplateId"
                               ng-disabled="vm.disabled">
                        <md-option ng-value="item.buildingDesignTemplateId"
                                   ng-repeat="item in vm.buildingZoneTemplates"
                                   ng-click="vm.applyZonesTemplate(item);">
                            {{item.templateName}}
                        </md-option>
                        <md-option ng-value="'NO_DEFAULT'"
                                   ng-click="vm.nullifyZoneDefaults('NO_DEFAULT')"
                                   ng-if="vm.isTemplate">
                            No Default
                        </md-option>
                        <md-option ng-value="'BLANK_TEMPLATE'"
                                   ng-click="vm.nullifyZoneDefaults(null)"
                                   ng-if="!vm.isTemplate">
                            Blank Building Design Template
                        </md-option>
                        <md-option ng-value="'MODIFIED'"
                                   ng-show="false">
                            Modified Template
                        </md-option>
                    </md-select>

                </div>
            </md-input-container>

            <!-- Copy From -> To Buttons -->
            <div ng-if="!vm.isTemplate"
                 layout="row"
                 class="md-block"
                 style="margin-left: auto">
                <md-button ng-click="vm.copyZonesToFrom(vm.complianceOption, vm.complianceOption[vm.sourceType], vm.baselineOption[vm.sourceType], vm.baselineOption)"
                           class="md-raised"
                           ng-show="!vm.disabledEx() && vm.showCopyBaselineForOption(vm.baselineOption) && vm.complianceOption != vm.baselineOption">
                    Copy Baseline
                </md-button>

                <md-button ng-click="vm.copyZonesToFrom(vm.complianceOption, vm.complianceOption.reference, vm.complianceOption.proposed)"
                           class="md-raised"
                           ng-show="!vm.disabledEx() && vm.sourceType=='reference'">
                    Copy Proposed
                </md-button>

                <!-- Now for every compliance option EXCEPT THIS ONE AND THE BASELINE show a "copy Option X" button -->
                <md-button ng-repeat="opt in vm.optionsNotThisOrBaseline(vm.complianceOption)"
                           ng-if="!vm.disabledEx() && vm.complianceOption.optionIndex != 0 && vm.showCopyBaselineForOption(opt)"
                           ng-click="vm.copyZonesToFrom(vm.complianceOption, vm.complianceOption[vm.sourceType], opt[vm.sourceType], opt)">
                    Copy Option {{opt.optionIndex}}
                </md-button>
            </div>
        </div>

    </md-card-content>
</md-card>

<!-- Building Storeys Card -->
<md-card>
    <md-card-header>
        <span class="md-headline">
            Building Storeys
        </span>
    </md-card-header>

    <md-card-content flex="100" flex-gt-md="50">
        <storey-list source="vm.source"
                     disabled="vm.disabledEx();">
        </storey-list>
    </md-card-content>
</md-card>

<!-- Zones Table -->
<md-card ng-form="ZoneListForm{{vm.complianceOption.optionIndex}}{{vm.sourceType}}">
    <md-card-header>
        <span class="md-headline"
              ng-class="{'card-has-errors' : vm.formHasErrors() }">
            Building Zones
        </span>
        <span flex></span>
    </md-card-header>
    <md-card-content>

        <!-- All Zones in a Table -->
        <div ng-if="vm.source.buildingZonesTemplateId != 'NO_DEFAULT'">

            <!-- Interior Zones-->
            <div class="table-responsive-vertical shadow-z-1"
                 style="margin-bottom: 15px; padding-top: 1px;">

                <!-- Title + "Not Applicable" checkbox -->
                <div style="display: flex; justify-content: space-between;">
                    <h2 style="margin: 15px;">Interior Zones</h2>
                    <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['interior']"
                                 class="not-applicable-checkbox"
                                 ng-change="vm.removeZones(vm.cachedInteriorZones)"
                                 ng-disabled="vm.disabledEx()">
                        Not Applicable
                    </md-checkbox>
                </div>

                <table ng-if="vm.source.zoneTypesNotApplicable['interior'] == false || vm.source.zoneTypesNotApplicable['interior'] == null"
                       class="table table-striped table-hover table-condensed"
                       ng-disabled="vm.source.buildingZonesTemplateId != null">
                    <thead>
                        <tr>
                            <th style="text-align: center;">
                                <md-checkbox ng-model="vm.bulkEditAllInterior"
                                             ng-disabled="vm.disabledEx()"
                                             ng-click="vm.toggleBulkEditAll(!vm.bulkEditAllInterior, 'selectedForInteriorBulkEdit', vm.cachedInteriorZones)"
                                             style="margin: auto;">
                                </md-checkbox>
                            </th>
                            <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneNumber');">
                                Zone Number
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneNumber' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneNumber' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneDescription');">
                                Zone Name
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneDescription' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneDescription' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneActivity.description');">
                                Zone Activity
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneActivity.description' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneActivity.description' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable"  ng-click="vm.sortBy('Interior', 'zoneType.description');">
                                Zone Type
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneType.description' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'zoneType.description' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'conditioned');">
                                Conditioned
                                <i ng-if="vm.InteriorSortInfo.column == 'conditioned' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'conditioned' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'naturallyVentilated');">
                                Naturally Ventilated
                                <i ng-if="vm.InteriorSortInfo.column == 'naturallyVentilated' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'naturallyVentilated' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'nccClassification.nccClassificationCode');">
                                NCC Classification
                                <i ng-if="vm.InteriorSortInfo.column == 'nccClassification.nccClassificationCode' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'nccClassification.nccClassificationCode' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'floorArea');">
                                Floor Area (m<sup>2</sup>)
                                <i ng-if="vm.InteriorSortInfo.column == 'floorArea' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'floorArea' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'ceilingArea');">
                                Ceiling Area (m<sup>2</sup>)
                                <i ng-if="vm.InteriorSortInfo.column == 'ceilingArea' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'ceilingArea' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'volume');">
                                Volume (m<sup>3</sup>)
                                <i ng-if="vm.InteriorSortInfo.column == 'volume' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'volume' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'storey');">
                                Storey
                                <i ng-if="vm.InteriorSortInfo.column == 'storey' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'storey' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'ceilingFan');">
                                Ceiling Fan
                                <i ng-if="vm.InteriorSortInfo.column == 'ceilingFan' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'ceilingFan' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('Interior', 'evaporativeCooler');">
                                Evaporative Cooler
                                <i ng-if="vm.InteriorSortInfo.column == 'evaporativeCooler' && vm.InteriorSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.InteriorSortInfo.column == 'evaporativeCooler' && vm.InteriorSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left" style="width: 32px;"></th>

                        </tr>

                    </thead>
                    <tbody>

                        <tr ng-repeat="item in vm.cachedInteriorZones"
                            lr-drag-src="zones"
                            lr-drop-target="zones"
                            lr-drop-success="vm.renumberZones()"
                            lr-match-property="zoneId"
                            lr-drag-data="vm.source.zones"
                            lr-match-value="{{item.zoneId}}"
                            lr-index="vm.source.zones.indexOf(item)">

                            <td style="text-align: center;">
                                <md-checkbox ng-model="item.selectedForInteriorBulkEdit"
                                             ng-click="vm.bulkEditAllInterior = false;"
                                             ng-disabled="vm.disabledEx()"
                                             style="margin: auto;" />
                            </td>

                            <!-- Zone Number -->
                            <td data-title="Zone Number" ng-class="{ 'draggable': !vm.disabledEx() }">
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx() }">

                                    <input type="text"
                                           name="ZoneNumber{{$index}}"
                                           ng-model="item.zoneNumber"
                                           required
                                           ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                                           ng-blur="vm.updateSource(item, item.zoneNumber, 'Z');" />
                                    <div ng-messages="ZoneListForm['ZoneNumber'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Name -->
                            <td data-title="Zone Name"
                                lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text"
                                           name="ZoneDescription"
                                           ng-model="item.zoneDescription"
                                           ng-model-options="{ updateOn: 'blur' }"
                                           required
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['ZoneDescription'].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Activity -->
                            <td data-title="Zone Activity" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.zoneActivity"
                                               ng-model-options="{trackBy: '$value.zoneActivityCode'}"
                                               required
                                               ng-disabled="vm.disabledEx()">
                                        <md-option ng-repeat="v in vm.cachedInteriorZoneActivityList"
                                                   ng-value="v"
                                                   ng-click="vm.zoneActivityChanged(v, item)">
                                            {{v.description}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Zone Type -->
                            <td data-title="Zone Type" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.zoneType"
                                               ng-model-options="{trackBy: '$value.zoneTypeCode'}"
                                               required
                                               ng-disabled="vm.disabledEx()">
                                        <md-option ng-repeat="v in vm.cachedInteriorZoneTypeList"
                                                   ng-value="v"
                                                   ng-click="vm.zoneTypeChanged(v, item, true);">
                                            {{v.description}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Conditioned -->
                            <td data-title="Conditioned" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                    <md-select name="HabitableRoom"
                                               style="margin: 0;"
                                               ng-model="item.conditioned"
                                               ng-disabled="vm.disabledEx()"
                                               required
                                               ng-change="vm.calculateAllowance(item)">
                                        <md-option ng-value="true"
                                                   ng-click="vm.calculateAllowance(item)">
                                            Yes
                                        </md-option>
                                        <md-option ng-value="false"
                                                   ng-click="vm.calculateAllowance(item)">
                                            No
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Naturally Ventilated -->
                            <td data-title="Naturally Ventilated" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">
                                    <md-select name="NaturallyVentilated"
                                               style="margin: 0;"
                                               ng-model="item.naturallyVentilated"
                                               disabled
                                               required>
                                        <md-option ng-value="true">
                                            Yes
                                        </md-option>
                                        <md-option ng-value="false">
                                            No
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- NCC Classification -->
                            <td data-title="NCC Classification" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.nccClassification"
                                               ng-model-options="{trackBy: '$value.nccClassificationCode'}"
                                               required
                                               ng-disabled="vm.disabledEx()">
                                        <md-option ng-repeat="v in vm.cachedAvailableInteriorNccClassificationList"
                                                   ng-value="v"
                                                   ng-click="vm.onNccClassificationUpdate(item)">
                                            {{v.description}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Floor Area -->
                            <td data-title="Floor Area" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">
                                    <input type="text" name="FloorArea{{$index}}"
                                           ng-model="item.floorArea"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-blur="vm.matchCeilingAreaToFloorArea(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['FloorArea'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Ceiling Area -->
                            <td data-title="Ceiling Area" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">
                                    <input id="CeilingAreaInput{{item.zoneId}}"
                                           type="text"
                                           name="CeilingArea{{$index}}"
                                           ng-model="item.ceilingArea"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                </md-input-container>
                            </td>

                            <!-- Volume -->
                            <td data-title="Volume" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text" name="Volume{{$index}}"
                                           ng-model="item.volume"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['Volume'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Storey -->
                            <td data-title="Storey" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex">

                                    <md-select required
                                               style="margin: 0;"
                                               name="Storey{{$index}}"
                                               ng-disabled="vm.disabledEx()"
                                               ng-model="item.storey"
                                               ng-change="vm.calculateAllowance(item)">
                                        <md-option ng-value="x.floor"
                                                   ng-repeat="x in vm.source.storeys">
                                            {{x.name}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Ceiling Fan -->
                            <td data-title="Ceiling Fan" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    style="text-align: center;">
                                    <md-checkbox name="CeilingFan"
                                                 ng-model="item.ceilingFan"
                                                 style="margin: auto;"
                                                 ng-disabled="vm.disabledEx()">
                                    </md-checkbox>
                                </md-input-container>
                            </td>

                            <!-- Evaporative Cooler -->
                            <td data-title="Evaporative Cooler" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    style="text-align: center;">
                                    <md-checkbox name="EvaporativeCooler"
                                                 ng-model="item.evaporativeCooler"
                                                 style="margin: auto;"
                                                 ng-disabled="vm.disabledEx()">
                                    </md-checkbox>
                                </md-input-container>
                            </td>

                            <!-- Action Buttons -->
                            <td data-title="Clear"
                                lr-no-drag
                                class="text-center">

                                <div ng-include="'zone-more-actions-multi-button'"
                                     style="display: flex; justify-content: center; align-content: center;"/>

                            </td>

                        </tr>

                    </tbody>

                </table>

                <div ng-if="vm.source.zoneTypesNotApplicable['interior'] == false || vm.source.zoneTypesNotApplicable['interior'] == null"
                     layout="row"
                     style="padding: 10px 2px;">

                    <md-button class="md-raised md-primary"
                               ng-click="vm.addZone(vm.cachedInteriorZones, null, 'Z')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx()">
                        Add
                    </md-button>

                    <md-button class="md-raised md-primary"
                               ng-click="vm.showBulkEdit('selectedForInteriorBulkEdit', 'bulkEditAllInterior', vm.cachedInteriorZones, 'Z', 'Interior Zones')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForInteriorBulkEdit')">
                        Bulk Edit
                    </md-button>

                </div>
            </div>

            <!-- Roof Space Zones (Readonly?) -->
            <div class="table-responsive-vertical shadow-z-1"
                 style="margin-bottom: 15px; padding-top: 1px;">

                <!-- Title + "Not Applicable" checkbox -->
                <div style="display: flex; justify-content: space-between;">
                    <h2 style="margin: 15px;">Roof Space</h2>
                    <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['roofspace']"
                                 class="not-applicable-checkbox"
                                 ng-change="vm.removeZones(vm.cachedRoofSpaceZones)"
                                 ng-disabled="vm.disabledEx()">
                        Not Applicable
                    </md-checkbox>
                </div>

                <table ng-if="vm.source.zoneTypesNotApplicable['roofspace'] == false  || vm.source.zoneTypesNotApplicable['roofspace'] == null"
                       class="table table-striped table-hover table-condensed"
                       ng-disabled="vm.source.buildingZonesTemplateId != null">
                    <thead>
                        <tr>

                            <th style="width: 20px; text-align: center;">
                                <md-checkbox ng-model="vm.bulkEditAllRoofSpace"
                                             ng-disabled="vm.disabledEx()"
                                             ng-click="vm.toggleBulkEditAll(!vm.bulkEditAllRoofSpace, 'selectedForRoofSpaceBulkEdit', vm.cachedRoofSpaceZones)"
                                             style="margin: auto;">
                                </md-checkbox>
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('RoofSpace', 'zoneNumber');">
                                Zone Number
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneNumber' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneNumber' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('RoofSpace', 'zoneDescription');">
                                Zone Name
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneDescription' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneDescription' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('RoofSpace', 'zoneActivity.description');">
                                Zone Activity
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneActivity.description' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'zoneActivity.description' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('RoofSpace', 'airCavity.airCavityCode');">
                                Roof Space Ventilation
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'airCavity.airCavityCode' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'airCavity.airCavityCode' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('RoofSpace', 'floorArea');">
                                Floor Area (m<sup>2</sup>)
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'floorArea' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'floorArea' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('RoofSpace', 'volume');">
                                Volume (m<sup>3</sup>)
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'volume' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'volume' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('RoofSpace', 'storeyBelow');">
                                Storey Below
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'storeyBelow' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'storeyBelow' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('RoofSpace', 'isReflective');">
                                Reflective
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'reflective' && vm.RoofSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.RoofSpaceSortInfo.column == 'reflective' && vm.RoofSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-left" style="width: 32px;"></th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr ng-repeat="item in vm.cachedRoofSpaceZones"
                            lr-drag-src="zones"
                            lr-drop-target="zones"
                            lr-drop-success="vm.renumberZones()"
                            lr-match-property="zoneId"
                            lr-drag-data="vm.source.zones"
                            lr-match-value="{{item.zoneId}}"
                            lr-index="vm.source.zones.indexOf(item)">

                            <!-- Bulk Modal Select -->
                            <td style="text-align: center;">
                                <md-checkbox ng-model="item.selectedForRoofSpaceBulkEdit"
                                             ng-click="vm.bulkEditAllRoofSpace = false;"
                                             ng-disabled="vm.disabledEx()"
                                             style="margin: auto;" />
                            </td>

                            <!-- Zone Number -->
                            <td data-title="Zone Number" ng-class="{ 'draggable': !vm.disabledEx() }">
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx() }">

                                    <input type="text"
                                           name="ZoneNumber{{$index}}"
                                           ng-model="item.zoneNumber"
                                           required
                                           ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                                           ng-blur="vm.updateSource(item, item.zoneNumber, 'R');" />
                                    <div ng-messages="ZoneListForm['ZoneNumber'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Name -->
                            <td data-title="Zone Name"
                                lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text"
                                           name="ZoneDescription"
                                           ng-model="item.zoneDescription"
                                           ng-model-options="{ updateOn: 'blur' }"
                                           required
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['ZoneDescription'].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Activity -->
                            <td data-title="Zone Activity" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}"
                                                    ng-disabled>

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.zoneActivity"
                                               ng-model-options="{trackBy: '$value.zoneActivityCode'}"
                                               required
                                               ng-disabled="true">
                                        <md-option ng-repeat="v in vm.cachedRoofZoneActivityList"
                                                   ng-value="v"
                                                   ng-click="vm.zoneActivityChanged(v, item)">
                                            {{v.description}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Roof Space Ventilation -->
                            <td data-title="Roof Space Ventilation" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.airCavity"
                                               ng-model-options="{trackBy: '$value.airCavityCode'}"
                                               required
                                               ng-disabled="vm.disabledEx()">
                                        <md-option ng-repeat="v in vm.cachedRoofAirCavityList"
                                                   ng-value="v">
                                            {{v.title}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Floor Area -->
                            <td data-title="Floor Area" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text" name="FloorArea{{$index}}"
                                           ng-model="item.floorArea"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['FloorArea'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Volume -->
                            <td data-title="Volume" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text" name="Volume{{$index}}"
                                           ng-model="item.volume"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['Volume'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Storey Below -->
                            <td>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select required
                                               style="margin: 0;"
                                               name="Storey{{$index}}"
                                               ng-disabled="vm.disabledEx()"
                                               ng-model="item.storeyBelow">
                                        <md-option ng-value="x.floor"
                                                   ng-repeat="x in vm.source.storeys">
                                            {{x.name}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Reflective -->
                            <td data-title="Reflective"
                                lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    style="text-align: center;">
                                    <md-checkbox ng-model="item.isReflective"
                                                 ng-disabled="vm.disabledEx()"
                                                 style="margin: auto;">
                                    </md-checkbox>
                                </md-input-container>
                            </td>

                            <!-- Action Buttons -->
                            <td data-title="Clear"
                                lr-no-drag
                                class="text-center">

                                <div ng-include="'zone-more-actions-multi-button'"
                                     style="display: flex; justify-content: center; align-content: center;"/>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div ng-if="vm.source.zoneTypesNotApplicable['roofspace'] == false  || vm.source.zoneTypesNotApplicable['roofspace'] == null"
                     layout="row"
                     style="padding: 10px 2px;">

                    <md-button class="md-raised md-primary"
                               ng-click="vm.addZone(vm.cachedRoofSpaceZones, 'ZARoofSpace', 'R')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx()">
                        Add
                    </md-button>

                    <md-button class="md-raised md-primary"
                               ng-click="vm.showBulkEdit('selectedForRoofSpaceBulkEdit', 'bulkEditAllRoofSpace', vm.cachedRoofSpaceZones, 'R', 'Roof Space')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForRoofSpaceBulkEdit')">
                        Bulk Edit
                    </md-button>

                </div>
            </div>

            <!-- Subfloor Space Zones (Readonly?) -->
            <div class="table-responsive-vertical shadow-z-1"
                 style="margin-bottom: 15px; padding-top: 1px;">

                <!-- Title + "Not Applicable" checkbox -->
                <div style="display: flex; justify-content: space-between;">
                    <h2 style="margin: 15px;">Subfloor Space</h2>
                    <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['subfloorspace']"
                                 class="not-applicable-checkbox"
                                 ng-change="vm.removeZones(vm.cachedSubfloorSpaceZones)"
                                 ng-disabled="vm.disabledEx()">
                        Not Applicable
                    </md-checkbox>
                </div>

                <table ng-if="vm.source.zoneTypesNotApplicable['subfloorspace'] == false  || vm.source.zoneTypesNotApplicable['subfloorspace'] == null"
                       class="table table-striped table-hover table-condensed"
                       ng-disabled="vm.source.buildingZonesTemplateId != null">
                    <thead>
                        <tr>
                            <th style="width: 20px; text-align: center;">
                                <md-checkbox ng-model="vm.bulkEditAllSubfloor"
                                             ng-disabled="vm.disabledEx()"
                                             ng-click="vm.toggleBulkEditAll(!vm.bulkEditAllSubfloor, 'selectedForSubfloorBulkEdit', vm.cachedSubfloorSpaceZones)"
                                             style="margin: auto;">
                                </md-checkbox>
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('SubfloorSpace', 'zoneNumber');">
                                Zone Number
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneNumber' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneNumber' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('SubfloorSpace', 'zoneDescription');">
                                Zone Name
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneDescription' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneDescription' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('SubfloorSpace', 'zoneActivity.description');">
                                Zone Activity
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneActivity.description' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'zoneActivity.description' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-left clickable" ng-click="vm.sortBy('SubfloorSpace', 'airCavity.airCavityCode');">
                                Subfloor Space Ventilation
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'airCavity.airCavityCode' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'airCavity.airCavityCode' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('SubfloorSpace', 'floorArea');">
                                Floor Area (m<sup>2</sup>)
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'floorArea' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'floorArea' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>
                            <th class="text-right clickable" ng-click="vm.sortBy('SubfloorSpace', 'volume');">
                                Volume (m<sup>3</sup>)
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'volume' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'volume' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('SubfloorSpace', 'storeyAbove');">
                                Storey Above
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'storeyAbove' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'storeyAbove' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-right clickable" ng-click="vm.sortBy('SubfloorSpace', 'isReflective');">
                                Reflective
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'reflective' && vm.SubfloorSpaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                                <i ng-if="vm.SubfloorSpaceSortInfo.column == 'reflective' && vm.SubfloorSpaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                            </th>

                            <th class="text-left" style="width: 32px;"></th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr ng-repeat="item in vm.cachedSubfloorSpaceZones"
                            lr-drag-src="zones"
                            lr-drop-target="zones"
                            lr-drop-success="vm.renumberZones()"
                            lr-match-property="zoneId"
                            lr-drag-data="vm.source.zones"
                            lr-match-value="{{item.zoneId}}"
                            lr-index="vm.source.zones.indexOf(item)">

                            <!-- Bulk Modal Select -->
                            <td style="text-align: center;">
                                <md-checkbox ng-model="item.selectedForSubfloorBulkEdit"
                                             ng-click="vm.bulkEditAllSubfloor = false;"
                                             ng-disabled="vm.disabledEx()"
                                             style="margin: auto;" />
                            </td>

                            <!-- Zone Number -->
                            <td data-title="Zone Number" ng-class="{ 'draggable': !vm.disabledEx() }">
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx() }">

                                    <input type="text"
                                           name="ZoneNumber{{$index}}"
                                           ng-model="item.zoneNumber"
                                           required
                                           ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                                           ng-blur="vm.updateSource(item, item.zoneNumber, 'S');" />
                                    <div ng-messages="ZoneListForm['ZoneNumber'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Name -->
                            <td data-title="Zone Name"
                                lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text"
                                           name="ZoneDescription"
                                           ng-model="item.zoneDescription"
                                           ng-model-options="{ updateOn: 'blur' }"
                                           required
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['ZoneDescription'].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Zone Activity -->
                            <td data-title="Zone Activity" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}"
                                                    ng-disabled>

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.zoneActivity"
                                               ng-model-options="{trackBy: '$value.zoneActivityCode'}"
                                               required
                                               ng-disabled="true">
                                        <md-option ng-repeat="v in vm.cachedSubfloorZoneActivityList"
                                                   ng-value="v"
                                                   ng-click="vm.zoneActivityChanged(v, item)">
                                            {{v.description}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Subfloor Space Ventilation -->
                            <td data-title="Subfloor Space Ventilation" class="text-left" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select class="kindly-remove-error-spacer vertically-condensed-ex"
                                               style="margin: 0;"
                                               ng-model="item.airCavity"
                                               ng-model-options="{trackBy: '$value.airCavityCode'}"
                                               required
                                               ng-disabled="vm.disabledEx()">
                                        <md-option ng-repeat="v in vm.cachedSubfloorAirCavityList"
                                                   ng-value="v">
                                            {{v.title}}
                                        </md-option>

                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Floor Area -->
                            <td data-title="Floor Area" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text" name="FloorArea{{$index}}"
                                           ng-model="item.floorArea"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['FloorArea'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Volume -->
                            <td data-title="Volume" class="text-right" lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <input type="text" name="Volume{{$index}}"
                                           ng-model="item.volume"
                                           ng-change="vm.calculateAllowance(item);"
                                           ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                           formatted-number
                                           decimals="2"
                                           ng-required="vm.isTemplate == false"
                                           ng-disabled="vm.disabledEx()" />
                                    <div ng-messages="ZoneListForm['Volume'+$index].$error">
                                    </div>
                                </md-input-container>
                            </td>

                            <!-- Storey Above -->
                            <td>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    ng-class="{'input-black' : !vm.disabledEx()}">

                                    <md-select required
                                               style="margin: 0;"
                                               name="Storey{{$index}}"
                                               ng-disabled="vm.disabledEx()"
                                               ng-model="item.storeyAbove">
                                        <md-option ng-value="x.floor"
                                                   ng-repeat="x in vm.source.storeys">
                                            {{x.name}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </td>

                            <!-- Reflective -->
                            <td data-title="Reflective"
                                class="text-center"
                                lr-no-drag>
                                <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                    style="text-align: center;">
                                    <md-checkbox ng-model="item.isReflective"
                                                 ng-disabled="vm.disabledEx()"
                                                 style="margin: auto;">
                                    </md-checkbox>
                                </md-input-container>
                            </td>

                            <!-- Action Buttons -->
                            <td data-title="Clear"
                                lr-no-drag
                                class="text-center">

                                <div ng-include="'zone-more-actions-multi-button'"
                                     style="display: flex; justify-content: center; align-content: center;"/>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div ng-if="vm.source.zoneTypesNotApplicable['subfloorspace'] == false  || vm.source.zoneTypesNotApplicable['subfloorspace'] == null"
                     layout="row"
                     style="padding: 10px 2px;">

                    <md-button class="md-raised md-primary"
                               ng-click="vm.addZone(vm.cachedSubfloorSpaceZones, 'ZASubfloorSpace', 'S')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx()">
                        Add
                    </md-button>

                    <md-button class="md-raised md-primary"
                               ng-click="vm.showBulkEdit('selectedForSubfloorBulkEdit', 'bulkEditAllSubfloor', vm.cachedSubfloorSpaceZones, 'R', 'Subfloor Space')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForSubfloorBulkEdit')">
                        Bulk Edit
                    </md-button>

                </div>
            </div>

            <!-- Ground Surface Zones (Readonly?) -->
            <div class="table-responsive-vertical shadow-z-1"
                 style="margin-bottom: 15px; padding-top: 1px;">

                <!-- Title + "Not Applicable" checkbox -->
                <div style="display: flex; justify-content: space-between;">
                    <h2 style="margin: 15px;">Ground Surface</h2>
                    <md-checkbox ng-model="vm.source.zoneTypesNotApplicable['groundsurface']"
                                 class="not-applicable-checkbox"
                                 ng-change="vm.removeZones(vm.cachedGroundSurfaceZones)"
                                 ng-disabled="vm.disabledEx()">
                        Not Applicable
                    </md-checkbox>
                </div>

                <table ng-if="vm.source.zoneTypesNotApplicable['groundsurface'] == false || vm.source.zoneTypesNotApplicable['groundsurface'] == null"
                       class="table table-striped table-hover table-condensed"
                       ng-disabled="vm.source.buildingZonesTemplateId != null">
                    <thead>
                    <tr>
                        <th style="width: 20px; text-align: center;">
                            <md-checkbox ng-model="vm.bulkEditAllGroundZone"
                                         ng-disabled="vm.disabledEx()"
                                         ng-click="vm.toggleBulkEditAll(!vm.bulkEditAllGroundZone, 'selectedForGroundZoneBulkEdit', vm.cachedGroundSurfaceZones)"
                                         style="margin: auto;">
                            </md-checkbox>
                        </th>
                        <th class="text-left clickable" ng-click="vm.sortBy('GroundSurface', 'zoneNumber');">
                            Zone Number
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneNumber' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneNumber' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-left clickable" ng-click="vm.sortBy('GroundSurface', 'zoneDescription');">
                            Zone Name
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneDescription' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneDescription' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-left clickable" ng-click="vm.sortBy('GroundSurface', 'zoneActivity.description');">
                            Zone Activity
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneActivity.description' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'zoneActivity.description' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>
                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'floorArea');">
                            Floor Area (m<sup>2</sup>)
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'floorArea' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'floorArea' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'conditionedFloorArea');">
                            Conditioned Floor Area (m<sup>2</sup>)
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'conditionedFloorArea' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'conditionedFloorArea' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'perimeter');">
                            Perimeter (m)
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'perimeter' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'perimeter' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'wallThickness');">
                            Wall Thickness (m)
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'wallThickness' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'wallThickness' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'conductivity');">
                            Conductivity
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'conductivity' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'conductivity' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'diffusivity');">
                            Diffusivity
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'diffusivity' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'diffusivity' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'groundReflectance');">
                            Ground Reflectance
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'groundReflectance' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'groundReflectance' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-right clickable" ng-click="vm.sortBy('GroundSurface', 'edgeInsulation');">
                            Edge Insulation
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'edgeInsulation' && vm.GroundSurfaceSortInfo.direction == 'ASC'" class="fa fa-caret-up" />
                            <i ng-if="vm.GroundSurfaceSortInfo.column == 'edgeInsulation' && vm.GroundSurfaceSortInfo.direction == 'DESC'" class="fa fa-caret-down" />
                        </th>

                        <th class="text-left" style="width: 32px;"></th>
                    </tr>
                    </thead>
                    <tbody>

                    <tr ng-repeat="item in vm.cachedGroundSurfaceZones"
                        lr-drag-src="zones"
                        lr-drop-target="zones"
                        lr-drop-success="vm.renumberZones()"
                        lr-match-property="zoneId"
                        lr-drag-data="vm.source.zones"
                        lr-match-value="{{item.zoneId}}"
                        lr-index="vm.source.zones.indexOf(item)">

                        <!-- Bulk Modal Select -->
                        <td style="text-align: center;">
                            <md-checkbox ng-model="item.selectedForGroundZoneBulkEdit"
                                         ng-click="vm.bulkEditAllGroundZone = false;"
                                         ng-disabled="vm.disabledEx()"
                                         style="margin: auto;" />
                        </td>

                        <!-- Zone Number -->
                        <td data-title="Zone Number" ng-class="{ 'draggable': !vm.disabledEx() }">
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx() }">

                                <input type="text"
                                       name="ZoneNumber{{$index}}"
                                       ng-model="item.zoneNumber"
                                       required
                                       ng-disabled="vm.disabledEx() || item.zoneNumberSource == 'SCRATCH'"
                                       ng-blur="vm.updateSource(item, item.zoneNumber, 'S');" />
                                <div ng-messages="ZoneListForm['ZoneNumber'+$index].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Zone Name -->
                        <td data-title="Zone Name"
                            lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex" ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       name="ZoneDescription"
                                       ng-model="item.zoneDescription"
                                       ng-model-options="{ updateOn: 'blur' }"
                                       required
                                       ng-disabled="vm.disabledEx()" />
                                <div ng-messages="ZoneListForm['ZoneDescription'].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Zone Activity -->
                        <td data-title="Zone Activity" class="text-left" lr-no-drag>
                            Ground Surface
                        </td>

                        <!-- Floor Area -->
                        <td data-title="Floor Area" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.floorArea"
                                       ng-change="vm.calculateAllowance(item);"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                                <div ng-messages="ZoneListForm['FloorArea'+$index].$error">
                                </div>
                            </md-input-container>
                        </td>

                        <!-- Conditioned Floor Area -->
                        <td data-title="Floor Area" class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.conditionedFloorArea"
                                       ng-change="vm.calculateAllowance(item);"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Perimeter -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.perimeter"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Wall Thickness -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.wallThickness"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Conductivity -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.conductivity"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Diffusivity -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.diffusivity"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Ground Reflectance -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">

                                <input type="text"
                                       ng-model="item.groundReflectance"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Edge Insulation -->
                        <td class="text-right" lr-no-drag>
                            <md-input-container class="md-block kindly-remove-error-spacer vertically-condensed-ex"
                                                ng-class="{'input-black' : !vm.disabledEx()}">
                                <input type="text"
                                       ng-model="item.edgeInsulation"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2"
                                       ng-required="vm.isTemplate == false"
                                       ng-disabled="vm.disabledEx()" />
                            </md-input-container>
                        </td>

                        <!-- Action Buttons -->
                        <td data-title="Clear"
                            lr-no-drag
                            class="text-center">

                            <div ng-include="'zone-more-actions-multi-button'"
                                 style="display: flex; justify-content: center; align-content: center;"/>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div ng-if="vm.source.zoneTypesNotApplicable['groundsurface'] == false || vm.source.zoneTypesNotApplicable['groundsurface'] == null"
                     layout="row"
                     style="padding: 10px 2px;">

                    <md-button class="md-raised md-primary"
                               ng-click="vm.addZone(vm.cachedGroundSurfaceZones, 'ZAGroundSurface', 'G')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx()">
                        Add
                    </md-button>

                    <md-button class="md-raised md-primary"
                               ng-click="vm.showBulkEdit('selectedForGroundZoneBulkEdit', 'bulkEditAllGroundZone', vm.cachedGroundSurfaceZones, 'R', 'Ground Surface')"
                               ng-show="!vm.disabledEx()"
                               ng-disabled="vm.disabledEx() || vm.noneSelected('selectedForGroundZoneBulkEdit')">
                        Bulk Edit
                    </md-button>

                </div>
            </div>

        </div>

    </md-card-content>
</md-card>

<script type="text/ng-template" id="zone-more-actions-multi-button">

    <!-- 'More' button w/ Popup -->
    <md-menu ng-show="!vm.disabledEx()">

        <!-- Initial '...' button, which launches options -->
        <img md-menu-origin
             class="clickable"
             ng-click="$mdOpenMenu()"
             src="/content/feather/more-horizontal.svg"
             ng-disabled="vm.disabled"/>
        <md-menu-content>

            <md-menu-item>

                <!-- Duplicate -->
                <md-button ng-click="vm.cloneZone(item)">
                    Duplicate
                </md-button>
            </md-menu-item>

            <md-menu-divider></md-menu-divider>

            <md-menu-item>
                <!-- Delete -->
                <md-button ng-click="vm.removeZone(item)">
                    <span style="color: orangered;">Delete</span>
                </md-button>
            </md-menu-item>

        </md-menu-content>
    </md-menu>

</script>