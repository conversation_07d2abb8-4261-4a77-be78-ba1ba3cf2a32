﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using TenureExtraction;

namespace ExtractionTests
{
    [TestClass]
    public class LocalitiesExtractionTests
    {
        [TestMethod]
        public async Task TestLocalitiesExtraction()
        {
            string sqlConnectionString = "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True";
            string geopackagePath = Directory.GetCurrentDirectory() + "\\Data\\Localities_LGATE_234_WA_GDA2020_Public.gpkg";

            var x = new LocalitiesExtractor(sqlConnectionString, geopackagePath,
                new Logger((s) => Console.WriteLine(s)));
            
            await x.UpdateLocalitiesDataset();

            ;
        }
    }
}
