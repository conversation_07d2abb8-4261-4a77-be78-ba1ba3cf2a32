// Shows a summary of certain requirements (natural light, ventilation, air movement etc)
// for all spaces within the building, broken down in several ways.
(function () {

    'use strict';
    angular
        .module('app')
        .component('floorPlanAreas', {
            bindings: {
                source: '<',                // Actual source object which CONTAINS spaces
                storeys: '<',               // Array of Storeys within the Building/Source
                nccClimateSpace: '<',
                nathersClimateSpace: '<', 
            },
            templateUrl: 'app/ui/assessment/floor-plan-data/floor-plan-areas.html',
            controller: Controller,
            controllerAs: 'vm'
        });

    Controller.$inject = ['$scope', 'common', 'coreLoop'];

    function Controller($scope, common, coreLoop) {

        var vm = this;
        vm.zoneTypeList = [];

        vm.buildingSummaryGroups = {};
        vm.lightAndVentilationStoreyRows = [];

        // This is just an array we can loop over to key in on the buildingSummaryGroups below...
        // Unfortunately the logic for each "Group" is custom so we can't generically
        // loop over it...
        vm.knownBuildingSummaryGroups = [
            'allSpaces',
        ];

        vm.sectionExpansions = {}; // Used to keep track of which sections are expanded.

        /** Retrieve and initialize any required data here, not just floating around the controller. */
        function initialize() {

            vm.calculationTimerPromise = setInterval(() => {

                constructFloorPlanAreasSection(vm.source.spaces, vm.storeys);
            },
            1000);
        }

        $scope.$on("$destroy", function () {
            if (vm.calculationTimerPromise) {
                clearInterval(vm.calculationTimerPromise);
            }
        });

        /**
         * Loops over the supplied spaces and breaks them up into building summary groups with calculations
         * pre-baked into our groups to allow for easy looping over within html side.
         * 
         * @param {any} spaces
         * @param {any} storeys
         */
        function constructFloorPlanAreasSection(spaces, storeys) {

            if (spaces == null)
                return;

            const buildingSummaryGroups = {
                allSpaces: { groupName: 'All Spaces', rows: [], descriptionHeading: "Floor Plan Areas" },
            };

            createSpaceFloorPlanAreasSummary(
                storeys, 
                spaces, 
                buildingSummaryGroups.allSpaces?.rows,
                'All Spaces');

            // Merge data.
            vm.knownBuildingSummaryGroups.forEach(summaryGroup => {

                let sameOrSmaller = true; // By default we assume the requirements array is the same size or smaller (i.e. OK to angular.merge)

                if (vm.buildingSummaryGroups[summaryGroup] == null || 
                    (vm.buildingSummaryGroups[summaryGroup].rows?.length > buildingSummaryGroups[summaryGroup].rows?.length))
                    sameOrSmaller = false; // Ok, need to blow away old value...

                if (vm.buildingSummaryGroups[summaryGroup] != null) {
                    let oldSpaceCount = vm.buildingSummaryGroups[summaryGroup].rows?.map(x => x.rows.length)?.reduce((a, b) => a + b, 0);
                    let newSpaceCount = buildingSummaryGroups[summaryGroup].rows?.map(x => x.rows.length)?.reduce((a, b) => a + b, 0);

                    if (oldSpaceCount > newSpaceCount)
                        sameOrSmaller = false;
                }

                if (sameOrSmaller) {

                    // Note: You cannot just go old = new as it causes UI problems. We have to selectively 'merge' 
                    // things. Also cannot use angular.merge as it was causing weird merging of some objects, affecting
                    // other parts of the UI.
                    vm.buildingSummaryGroups[summaryGroup].groupName = buildingSummaryGroups[summaryGroup].groupName;

                    // Delete any rows in the existing table beyond what is in the new one.
                    vm.buildingSummaryGroups[summaryGroup].rows.length = buildingSummaryGroups[summaryGroup]?.rows.length || 0;

                    for (let i = 0; i < buildingSummaryGroups[summaryGroup].rows.length; i++){

                        const oldGroup = vm.buildingSummaryGroups[summaryGroup].rows[i];
                        const newGroup = buildingSummaryGroups[summaryGroup].rows[i];

                        if (oldGroup == null || newGroup == null)
                            continue;

                        oldGroup.name = newGroup.name;
                        oldGroup.rows = newGroup.rows;
                    }

                }
                else {
                    vm.buildingSummaryGroups[summaryGroup] = buildingSummaryGroups[summaryGroup]; // Blow entire thing away - will cause UI problem (only noticeable if an element is open)
                }
            });
        }

        /**
         * Create all individual space rows as well as final "Building Total" row.
         */
        function createSpaceFloorPlanAreasSummary(storeys, spaces, addTo, title) {

            let spacesTotalFloorArea = 0;
            let spacesTotalPerimeter = 0;
            let spacesTotalPerimeterWallArea = 0;

            for (let i = 0; i < spaces.length; i++) {
                const space = spaces[i];
                spacesTotalFloorArea += space.floorArea;
                spacesTotalPerimeter += space.perimeter;
                spacesTotalPerimeterWallArea += (space.perimeter*space.ceilingHeight);
            }

            // Loop over each storey and create a row for each space type in 
            // that storey that actually exists.
            storeys?.forEach(storey => {

                let storeyGroup = { name: storey.name, rows: [] }
                const spacesInStorey = getSpacesInStorey(storey.floor, spaces);

                const zoneTypesInStorey = [...new Set(spacesInStorey.map(x => x.zoneType))];

                let storeyFloorArea = 0;
                let storeyPerimeter = 0;
                let storeyPerimeterWallArea = 0;

                for (let i = 0; i < spacesInStorey.length; i++) {
                    const space = spacesInStorey[i];
                    storeyFloorArea += space.floorArea;
                    storeyPerimeter += space.perimeter;
                    storeyPerimeterWallArea += (space.perimeter*space.ceilingHeight);
                }

                // Create row for each space type within current storey.
                zoneTypesInStorey.forEach(type => {

                    const spacesInStoreyWithType = spacesInStorey.filter(x => x.zoneType === type);

                    storeyGroup.rows.push(createSumRowForSpaces(
                        type,
                        spacesInStoreyWithType,
                        storeyFloorArea,
                        storeyPerimeter,
                        storeyPerimeter));
                });

                storeyGroup.rows.push(createSumRowForSpaces(
                    storeyGroup.name + ' Total',
                    spacesInStorey,
                    storeyFloorArea,
                    storeyPerimeter,
                    storeyPerimeterWallArea,
                ));

                if (storeyGroup.rows.length > 0)
                    addTo.push(storeyGroup);
            });

            // Now create the 'whole building' section which also includes every
            // single space-row again.
            let wholeBuildingGroup = { name: 'Whole Building', rows: [] }

            const zoneTypesInBuilding = [...new Set(spaces.map(x => x.zoneType))];

            // Create row for each space type within current storey.
            zoneTypesInBuilding.forEach(type => {

                const spacesInBuildingWithType = spaces.filter(x => x.zoneType === type);

                wholeBuildingGroup.rows.push(createSumRowForSpaces(
                    type,
                    spacesInBuildingWithType,
                    spacesTotalFloorArea,
                    spacesTotalPerimeter,
                    spacesTotalPerimeter));
            });

            wholeBuildingGroup.rows.push(createSumRowForSpaces(
                'Whole Building Total',
                spaces,
                spacesTotalFloorArea || 0,
                spacesTotalPerimeter || 0,
                spacesTotalPerimeterWallArea || 0,
            ));

            addTo.push(wholeBuildingGroup);

        }

        /**
         * Creates a row with data being the SUMMED value of all spaces passed in (i.e. pre-filtered)
         */
        function createSumRowForSpaces(
                description, 
                spaces, 
                totalFloorArea, 
                totalPerimeter,
                totalPerimeterWallArea) {

            if (spaces == null || spaces.length === 0)
                return null;

            let row = {};

            const floorArea = spaces.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            const perimeter = spaces.map(x => x.perimeter).reduce((a, b) => a + b, 0);
            const perimeterWallArea = spaces.map(x => x.perimeter * x.ceilingHeight).reduce((a, b) => a + b, 0);

            const zoneTypesPresent = [...new Set(spaces.map(x => x.zoneType))]

            row.zoneNumber = ""; // Not an actual space, so blank.
            row.description = description;
            row.zoneType = zoneTypesPresent.length > 1 ? { description: 'All Spaces' } : spaces[0].zoneType;
            row.floorArea = floorArea;
            row.floorAreaPercent = totalFloorArea === 0 ? 0 : ((row.floorArea / totalFloorArea) * 100);
            row.perimeter = perimeter;
            row.perimeterPercent = totalPerimeter === 0 ? 0 : ((row.perimeter / totalPerimeter) * 100);
            row.perimeterWallArea = perimeterWallArea;
            row.perimeterWallAreaPercent = totalPerimeterWallArea === 0 ? 0 : ((row.perimeterWallArea / totalPerimeterWallArea) * 100);

            if (Number.isNaN(row.floorArea))
                row.floorArea = 0;

            if (Number.isNaN(row.floorAreaPercent))
                row.floorAreaPercent = 0;

            if (Number.isNaN(row.perimeter))
                row.perimeter = 0;

            if (Number.isNaN(row.perimeterPercent))
                row.perimeterPercent = 0;

            if(Number.isNaN(row.perimeterWallArea))
                row.perimeterWallArea = 0;

            if(Number.isNaN(row.perimeterWallAreaPercent))
                row.perimeterWallAreaPercent = 0;

            return row;
        }

        function getSpacesInStorey(index, spaces) {
            return spaces.filter(s => s.storey === index);
        }

        vm.expand = function (section) {
            if (vm.source == null || vm.source.spaces.length === 0)
                return;

            vm.sectionExpansions[section] = !vm.sectionExpansions[section];
        }

        // Finally, initialize our component.
        initialize();

    }
})();