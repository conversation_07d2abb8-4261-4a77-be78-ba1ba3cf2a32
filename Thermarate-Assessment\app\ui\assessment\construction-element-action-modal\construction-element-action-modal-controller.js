(function () {

    'use strict';

    let controllerId = 'constructionElementActionModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'constructionservice',
            'servicetemplateservice',
            'common',
            constructionElementActionModalController]);

    function constructionElementActionModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      constructionservice,
                                      servicetemplateservice,
                                      common) {

        // The model for this form 
        const vm = this;

        vm.type = $scope.type;

        vm.parent = $scope.parent;
        vm.elements = $scope.elements;

        vm.availableDestinations = $scope.availableDestinations;

        vm.building = $scope.building;
        vm.option = $scope.option;
        vm.disabled = $scope.disabled;

        function initialize() {
            vm.destination = null;
            vm.title = "Move / Copy " + generateTitle(vm.elements);
        }

        initialize();

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = async function (action) {
            $mdDialog.hide({action, destination: vm.destination });
        }

        function generateTitle(elements) {

            if(elements.length === 1) {
                return elements[0].elementNumber;
            }

            let title = "";

            for(let i = 0; i < elements.length; i++) {
                const element = elements[i];

                title += element.elementNumber;

                if(i + 1 === elements.length)
                    return title;

                // If the next one is the last...
                if(i + 2 === elements.length || i === 2)
                    title += " & ";
                else
                    title += ", ";

                // if there are too many to fit in the header, return " & N more...";
                if(i + 2 === 4) {
                    title += `${elements.length - (i + 1)} more...`;
                    return title;
                }
            }

            return title;

        }

    }
})();