(function () {
    // The BushfireattacklevelUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'BushfireattacklevelUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'bushfireattacklevelservice',
      'security', bushfireattacklevelUpdateController]);
function bushfireattacklevelUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  bushfireattacklevelservice,
                                             securityservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Bushfire Attack Level';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.bushfireAttackLevelCode = null;
        vm.bushfireattacklevel = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        if (vm.newRecord) {
            vm.title = "New Bushfire Attack Level";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.bushfireAttackLevelCode = $scope.bushfireAttackLevelCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.bushfireAttackLevelCode = $stateParams.bushfireAttackLevelCode;
        }

        // Get data for object to display on page
        var bushfireAttackLevelCodePromise = null;
        if (vm.bushfireAttackLevelCode != null) {
            bushfireAttackLevelCodePromise = bushfireattacklevelservice.getBushfireAttackLevel(vm.bushfireAttackLevelCode)
            .then(function (data) {
                if (data != null) {
                    vm.bushfireattacklevel = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("bushfireattacklevel-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                bushfireattacklevelservice.createBushfireAttackLevel(vm.bushfireattacklevel).then(function(data){
                    vm.bushfireattacklevel = data;
                    vm.bushfireAttackLevelCode = vm.bushfireattacklevel.bushfireAttackLevelCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                bushfireattacklevelservice.updateBushfireAttackLevel(vm.bushfireattacklevel).then(function(data){
                    if (data != null) {
                        vm.bushfireattacklevel = data;
                        vm.bushfireAttackLevelCode = vm.bushfireattacklevel.bushfireAttackLevelCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            bushfireattacklevelservice.deleteBushfireAttackLevel(vm.bushfireAttackLevelCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            bushfireattacklevelservice.undoDeleteBushfireAttackLevel(vm.bushfireAttackLevelCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();