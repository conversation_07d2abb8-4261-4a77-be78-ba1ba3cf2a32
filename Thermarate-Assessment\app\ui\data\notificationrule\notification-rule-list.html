<section id="construction-list-view" 
         class="main-content-wrapper" 
         data-ng-controller="notificationRuleListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList(value)'
                data-spinner-busy='vm.isBusy'
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="NotificationRule"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges"
                data-custom-filter="vm.addCustomFilter()">
        </div>
        <div ng-if="vm.initialised" class="table-responsive-vertical shadow-z-1">
            <md-toolbar class="customFiltersBar" layout="row" flex="100" ng-if="vm.customFilter">
                <div>Filter has been applied.&nbsp;</div>
                <div ng-repeat="item in vm.customFilter"
                     class="customFilter"
                     ng-click="vm.removeCustomFilter(item)">
                    <span ng-if="$index!=0">&nbsp;and&nbsp;</span><span class="blueCustomFilter">{{vm.createCustomFilterLabel(item.field)}}</span> is <span class="blackCustomFilter">{{vm.toSplitTitleCase(item.value)}}</span>
                </div>
            </md-toolbar>
            <table class="table table-striped table-hover table-condensed"
                   st-table="vm.notificationRuleList"
                   st-table-filtered-list="exportList"
                   st-global-search="vm.listFilter"
                   st-persist="notificationRuleList"
                   st-pipe="vm.callServer"
                   st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="title" class="can-sort text-left">Title</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="initialStatus" class="can-sort text-center">Initial Status</th>
                        <th st-sort="newStatus" class="can-sort text-center">New Status</th>
                        <th st-sort="newStatus" class="can-sort text-center">Clients</th>
                        <th st-sort="enabled" class="can-sort text-left">Enabled</th>
                        <th style="width: 50px;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.notificationRuleList track by row.notificationRuleId">
                        <td data-title="Action"
                            class="action-col">
                            <md-button class="md-primary list-select"
                                       ui-sref="notificationrule-updateform({ notificationRuleId: row.notificationRuleId, type: row.type})">
                                Select
                            </md-button>
                        </td>
                        <td data-title="Title"
                            class="text-left">
                            {{::row.title }}
                        </td>
                        <td data-title="Description"
                            class="text-left">
                            {{::row.description }}
                        </td>
                        <td data-title="Initial Status"
                            class="text-center">
                            {{vm.statusCodeToDesc(row.initialStatusCode)}}
                        </td>
                        <td data-title="New Status"
                            class="text-center">
                            {{vm.statusCodeToDesc(row.newStatusCode)}}
                        </td>
                        <td data-title="New Status"
                            style="max-width:120px;"
                            class="text-center">
                            <select-all-multi-select
                                all-options="vm.clientList"
                                return-selections="row.clientSelectionsList"
                                primary-key="'clientId'"
                                display-key="'clientName'"
                                is-disabled="true">
                            </select-all-multi-select>
                        </td>
                        <td data-title="Enabled"
                            class="text-left">

                            <div style="display: grid; justify-items: center;">
                                <md-switch ng-model="row.enabled"
                                           ng-click="vm.immediateUpdateEnabled(row);">
                                </md-switch>
                            </div>
                        </td>
                        <!--  -->
                        <td class="text-center">
                            <div layout="row"
                                 layout-align="center center">
                                <md-button class="md-raised md-icon-button"
                                           title="Delete"
                                           ng-click="vm.delete(row)">
                                    <i class="fa fa-eraser fa-lg"></i>
                                </md-button>

                                <md-button style="margin-right: 5px;"
                                           class="md-raised md-icon-button"
                                           title="Copy"
                                           ng-click="vm.clone(row)">
                                    <i class="fa fa-clone fa-lg"></i>
                                </md-button>
                            </div>
                        </td>
<!--                        <td>-->
<!--                            <div style="display: grid; justify-content: center;">-->
<!--                                <md-checkbox style="margin: auto; text-align: center; width: 0;"-->
<!--                                             ng-model="row.isBulkSelected">-->

<!--                                </md-checkbox>-->
<!--                            </div>-->
<!--                        </td>-->

                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="9999" class="text-center">
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; background-color: rgb(250,250,250);">

                                <!-- Just empty. -->
                                <div></div>

                                <!-- Pagination Display -->
                                <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                                <!-- Bulk Delete -->
                                <md-button ng-if="vm.bulkSelectionsExist()"
                                           ng-click="vm.bulkDelete()"
                                           class="md-raised md-warn"
                                           style="align-self: center; justify-self: end;">
                                    BULK DELETE
                                </md-button>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager"
                 style="display: flex; justify-content: space-between;" >

                <span style="flex: 30%; ">
                    Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}
                </span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
