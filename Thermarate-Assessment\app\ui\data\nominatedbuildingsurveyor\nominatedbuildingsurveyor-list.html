<section id="NominatedBuildingSurveyor-list-view" class="main-content-wrapper" data-ng-controller="NominatedBuildingSurveyorListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-model="vm.queryModel"
                data-query-builder-name="NominatedBuildingSurveyor"
                data-query-builder-current="vm.currentQuery"
                data-default-start="vm.rptDateRange"
                data-date-range-label="Created"
                data-date-ranges="vm.ranges">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.nominatedBuildingSurveyorList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="nominatedBuildingSurveyorList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th align="left" class="action-col">Action</th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="description" class="can-sort text-left">Contact Name</th>
                        <th st-sort="description" class="can-sort text-left">Email Address</th>
                        <th st-sort="description" class="can-sort text-left">Physical Address</th>
                        <th st-sort="description" class="can-sort text-left">Phone Number</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.nominatedBuildingSurveyorList">
                        <td data-title="Action" class="action-col">
                            <md-button class="md-primary list-select"
                                       ui-sref="nominatedbuildingsurveyor-updateform({ buildingSurveyorId: row.buildingSurveyorId})"
                                       ng-if="row.buildingSurveyorId != '2502eb9c-cc6d-4711-b13c-a57bec57e9ab'">
                                Select
                            </md-button>
                        </td>

                        <td data-title="Description" class="text-left">{{::row.description}}</td>
                        <td data-title="Description" class="text-left">{{::row.contactName}}</td>
                        <td data-title="Description" class="text-left">{{::row.emailAddress}}</td>
                        <td data-title="Description" class="text-left">{{::row.physicalAddress}}</td>
                        <td data-title="Description" class="text-left">{{::row.phoneNumber}}</td>

                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
