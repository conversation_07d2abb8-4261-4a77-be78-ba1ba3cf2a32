<form id="account-form" name="accountform" novalidate>
    <md-dialog aria-label="My Account" flex-gt-sm="100" style="min-width:680px; width: 680px;">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>My Account</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="icon_close" aria-label="Close dialog">close</md-icon>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content layout-padding>
            <div style="height:100%; max-height:458px; overflow:auto;">
                <div>
                    <md-toolbar class="md-error" ng-show="authReason || authError">
                        <div ng-show="authReason">
                            {{authReason}}
                        </div>
                        <div ng-show="authError">
                            {{authError}}
                        </div>
                    </md-toolbar>
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Email Address</label>
                        <input name="emailAddress" ng-model="user.userName" ng-disabled="true">
                    </md-input-container>
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>Current Password</label>
                        <input name="currentPassword" type="password" ng-model="user.currentPassword" required md-autofocus>
                        <div ng-messages="accountform.currentPassword.$error" md-auto-hide="true">
                            <div ng-message="required">Current password is required.</div>
                        </div>
                    </md-input-container>
                    <md-input-container class="md-block" flex-gt-sm>
                        <label>New Password</label>
                        <input name="newPassword" type="password" ng-model="user.newPassword" md-minlength="6" required >
                        <span ng-if="user.newPassword>''" data-target="newPassword" data-ng-model="user.newPassword"
                                data-min-length="6" data-complexity="6" data-charsets="4" data-sys-strength></span>
                        <div ng-messages="accountform.newPassword.$error" md-auto-hide="true">

                            <div ng-message="required">New password is required.</div>
                            <div ng-message="md-minlength">Password has to be at least 6 characters long.</div>
                        </div>
                    </md-input-container>

                    <md-input-container class="md-block" flex-gt-sm ng-if="!user.userId || user.resetPassword">
                        <label>Confirm Password</label>
                        <input name="newPasswordConfirm" type="password" ng-model="user.newPasswordConfirm"
                               md-minlength="6" required
                               password-confirm match-target="user.newPassword">
                        <div ng-messages="accountform.newPasswordConfirm.$error" md-auto-hide="true">
                            <div ng-message="match">Passwords must match.</div>
                        </div>
                    </md-input-container>

                    <!-- Is an Internal User -->
                    <div style="width:418px;" ng-if="!user.isExternal">

                        <md-button class="md-raised md-primary"
                                   ng-if="!signatureUpdate"
                                   ng-click="updateSignature()">
                            Update Signature
                        </md-button>

                        <!-- ******** Signature Base64 Image ******** -->
                        <md-input-container class="md-block" 
                                            flex-gt-sm 
                                            ng-if="!signatureUpdate">
                            <div ng-bind-html="signaturePicture"
                                 ng-show="user.signatureSVGImage || vm.user.signatureBase64Image"
                                 ng-if="user.signatureSVGImage || vm.user.signatureBase64Image"
                                 ng-init="sanitizeSignature()"></div>
                        </md-input-container>

                        <md-button ng-if="signatureUpdate"
                                   ng-click="saveSignature()"
                                   class="md-raised md-primary">
                            Save Signature
                        </md-button>

                        <br /><br />
                        <!-- ******** JSignature Canvas ******** -->
                        <j-signature ng-if="signatureUpdate" 
                                     signature-svg="vm.user.signatureSVGImage"
                                     signature-base64="vm.user.signatureBase64Image"
                                     preview-height="70"></j-signature>
                    </div>
                </div>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">
            <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
            <md-button class="md-raised" ng-click="cancel()">Cancel</md-button>
            <md-button class="md-raised" ng-click="show2faSetup()" ng-if="!twoFacAuthEnabled">Setup 2FA</md-button>
            <md-button class="md-raised" ng-click="disable2fa()" ng-if="twoFacAuthEnabled">Disable 2FA</md-button>
            <md-button class="md-primary md-raised" ng-click="save()" ng-disabled='accountform.$invalid'>Update My Account</md-button>
        </md-dialog-actions>
    </md-dialog>
</form>
