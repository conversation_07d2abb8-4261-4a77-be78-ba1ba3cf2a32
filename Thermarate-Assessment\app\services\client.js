// Name: clientservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'clientservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', clientservice]);

    function clientservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'client/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getUsersForClient: getUsersForClient,
            getListCancel: getListCancel,
            //gets the clients where their names contain the given string
            getMatching: getMatching,
            currentFilter: function () { return currentFilter },
            getClient: getClient,
            createClient: createClient,
            updateClient: updateClient,
            deleteClient:deleteClient,
            undoDeleteClient: undoDeleteClient,
            setIsFavourite: setIsFavourite,
            purchaseOrderSettingsList: [
                { purchaseOrderCode: 'NotRequired', description: 'Not Required' },
                { purchaseOrderCode: 'Number', description: 'Number Only' },
                { purchaseOrderCode: 'File', description: 'File Only' },
                { purchaseOrderCode: 'NumberAndFile', description: 'Number and File' }
            ]
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Client list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getUsersForClient(clientId) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetUsersForClient';
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: { clientId },
                method: 'GET',
                isArray: true,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting Client list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getClient(clientId) {
            return $http({
                url: baseUrl + 'Get',
                params: {clientId: clientId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createClient(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Client Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateClient(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Client Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getMatching(searchTerm) {
            var url = baseUrl + 'GetMatching';
            if (searchTerm) {
                url = url + "?searchTerm=" + searchTerm;
            }
            return $http({
                url: url,
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error searching clients: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteClient(clientId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { clientId: clientId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteClient(clientId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { clientId: clientId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function setIsFavourite(clientId, isFavourite) {
            return $http({
                url: baseUrl + 'SetIsFavourite',
                params: { clientId, isFavourite },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error setting IsFavourite status for Client: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
    }
})();
