<form name="VariationScratchImportModal" data-ng-controller='ZoneSummaryVariableSelectModalCtrl as vm' style="overflow:visible;">

    <!-- Title -->
    <div data-cc-widget-header
         data-title="Select Variable"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <!-- Body -->
    <div class="content-container" style="width: 1400px;">

        <!-- Drawing Areas -->
        <span ng-if="vm.drawingAreasData != null" style="margin-right:15px; font-size:18px;">
            Drawing Areas
        </span>
        <md-card ng-if="vm.drawingAreasData != null" class="SECTION-drawingAreas" style="margin:0 0 30px 0; box-shadow:none;">
            <md-card-content style="padding:0;">

                <table class="table table-striped table-condensed table-data-centered drawingAreasTable">
                    <colgroup>
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 65px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                        <col span="1" style="width: 50px;">
                    </colgroup>
                    <thead>
                        <tr>
                            <th style="height:30px;">Storey</th>
                            <th style="height:30px;">House (m<sup>2</sup>)</th>
                            <th style="height:30px;">Garage (m<sup>2</sup>)</th>
                            <th style="height:30px;">House & Garage (m<sup>2</sup>)</th>
                            <th style="height:30px;">Alfresco (m<sup>2</sup>)</th>
                            <th style="height:30px;">Porch (m<sup>2</sup>)</th>
                            <th style="height:30px;">House Perimiter (m)</th>
                            <th style="height:30px;">House Facade (m<sup>2</sup>)</th>
                            <th style="height:30px;">Roof - Horizontal (m<sup>2</sup>)</th>
                            <th style="height:30px;">Roof - Pitched (m<sup>2</sup>)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- $$ FLOOR $$ -->
                        <tr ng-repeat="row in vm.drawingAreasData.drawingAreas | filter: { willDelete: false } track by $index" class="FLOOR-{{$index}}">
                            <td> {{row.storeyName}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-houseArea      variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'houseArea')"     > {{row.houseArea}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-garageArea     variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'garageArea')"    > {{row.garageArea}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-houseGarage    variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'houseGarage')"   > {{row.houseGarage}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-alfrescoArea   variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'alfrescoArea')"  > {{row.alfrescoArea}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-porchArea      variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'porchArea')"     > {{row.porchArea}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-housePerimeter variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'housePerimeter')"> {{row.housePerimeter}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-houseFacade    variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'houseFacade')"   > {{row.houseFacade}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-roofHorizontal variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'roofHorizontal')"> {{row.roofHorizontal}} </td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-roofPitched    variable-selectable" ng-click="vm.selectDrawingAreasVariable($event, $index, 'roofPitched')"   > {{row.roofPitched}} </td>
                        </tr>
                        <tr class="SECTION-drawingAreasTotals">
                            <td>Whole Building</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-house          variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'house')"          >{{vm.drawingAreasData.drawingAreasTotals.house}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-garage         variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'garage')"         >{{vm.drawingAreasData.drawingAreasTotals.garage}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-houseGarage    variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'houseGarage')"    >{{vm.drawingAreasData.drawingAreasTotals.houseGarage}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-alfresco       variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'alfresco')"       >{{vm.drawingAreasData.drawingAreasTotals.alfresco}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-porch          variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'porch')"          >{{vm.drawingAreasData.drawingAreasTotals.porch}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-housePerimeter variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'housePerimeter')" >{{vm.drawingAreasData.drawingAreasTotals.housePerimeter}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-houseFacade    variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'houseFacade')"    >{{vm.drawingAreasData.drawingAreasTotals.houseFacade}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-roofHorizontal variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'roofHorizontal')" >{{vm.drawingAreasData.drawingAreasTotals.roofHorizontal}}</td>
                            <!-- $$ VARIABLE $$ -->
                            <td class="VARIABLE-roofPitched    variable-selectable" ng-click="vm.selectDrawingAreasTotalsVariable($event, 'roofPitched')"    >{{vm.drawingAreasData.drawingAreasTotals.roofPitched}}</td>
                        </tr>
                    </tbody>
                </table>

            </md-card-content>
        </md-card>

        <!-- Zone Summary -->
        <span ng-if="vm.zoneSummary != null" style="margin-right:15px;font-size:18px;">
            Zone Summary
        </span>
        <!-- $$ SECTION $$ -->
        <md-tabs ng-if="vm.zoneSummary != null" md-dynamic-height style="margin-top:10px; margin-bottom:30px;" md-selected="vm.selectedTab" class="SECTION-zoneSummary">
            <md-tab ng-repeat="summaryGroup in vm.knownBuildingSummaryGroups" ng-disabled="vm.zoneSummary[summaryGroup].rows.length == 0">
                <md-tab-label>
                    <span>{{vm.zoneSummary[summaryGroup].groupName}}</span>
                </md-tab-label>
                <md-tab-body>
                    <!-- $$ TAB $$ -->
                    <table class="TAB-{{summaryGroup}} table-striped table-hover table-condensed">

                        <!-- Header-->
                        <thead>
                            <tr style="font-weight: bold;">
                                <td class="text-center" style="min-width: 120px; width: 110px;">Storey</td>

                                <td ng-if="summaryGroup == 'interiorZones'"
                                    style="min-width: 100px;"
                                    class="text-center">Zone Number</td>

                                <td style="min-width: 110px;" class="text-center">
                                    {{vm.zoneSummary[summaryGroup].descriptionHeading}}
                                </td>

                                <td style="min-width: 90px; text-align: center;">Zone Count</td>

                                <td style="min-width: 100px; text-align: center;">Floor Area (m<sup>2</sup>)</td>

                                <!-- Volume m3 -->
                                <td style="min-width: 100px; text-align: center;">Volume (m<sup>3</sup>)</td>

                                <!-- Exterior wall area m2 -->
                                <td style="min-width: 130px; text-align: center;">Exterior Wall Area (m<sup>2</sup>)</td>

                                <!-- Exterior glazing area m2 -->
                                <td style="min-width: 150px; text-align: center;">Exterior Glazing Area (m<sup>2</sup>)</td>

                            </tr>

                        </thead>

                        <!-- $$ STOREY $$ -->
                        <tbody class="STOREY-{{storeyIndex}} border-last"
                               ng-repeat="(storeyIndex, storeyGroup) in vm.zoneSummary[summaryGroup].rows track by $index">

                            <!-- $$ ZONE $$ -->
                            <tr class="ZONE-{{zoneIndex}} border-last"
                                ng-repeat="(zoneIndex, row) in storeyGroup.zones track by $index">

                                <!-- Storey -->
                                <td ng-if="$index == 0"
                                    class="text-center border-cell"
                                    data-title="Description"
                                    rowspan="{{storeyGroup.zones.length}}">
                                    <span>{{storeyGroup.name}}</span>
                                </td>

                                <!-- Zone Number -->
                                <td ng-if="summaryGroup == 'interiorZones'">
                                    {{row.zoneNumber}}
                                </td>

                                <!-- Description (Varies) -->
                                <td>
                                    {{row.description}}
                                </td>

                                <!-- Zone Count -->
                                <td class="VARIABLE-zoneCount variable-selectable"
                                    style="text-align: center;"
                                    ng-click="vm.selectZoneSummaryVariable($event, summaryGroup, storeyIndex, row.description, 'zoneCount')">
                                    {{row.zoneCount}}
                                </td>

                                <!-- $$ VARIABLE $$ -->
                                <!-- Floor Area -->
                                <td class="VARIABLE-floorArea variable-selectable"
                                    style="text-align: center;"
                                    data-title="Floor Area"
                                    ng-click="vm.selectZoneSummaryVariable($event, summaryGroup, storeyIndex, row.description, 'floorArea')">
                                    {{row.floorArea.toFixed(2);}}
                                </td>

                                <!-- $$ VARIABLE $$ -->
                                <!-- Volume m3 -->
                                <td class="VARIABLE-volume variable-selectable"
                                    ng-click="vm.selectZoneSummaryVariable($event, summaryGroup, storeyIndex, row.description, 'volume')"
                                    style="text-align: center;">
                                    {{row.volume.toFixed(2);}}
                                </td>

                                <!-- $$ VARIABLE $$ -->
                                <!-- Exterior wall area m2 -->
                                <td class="VARIABLE-exteriorWallArea variable-selectable"
                                    ng-click="vm.selectZoneSummaryVariable($event, summaryGroup, storeyIndex, row.description, 'exteriorWallArea')"
                                    style="text-align: center;">
                                    {{row.exteriorWallArea.toFixed(2);}}
                                </td>

                                <!-- $$ VARIABLE $$ -->
                                <!-- Exterior Glazing area m2 -->
                                <td class="VARIABLE-exteriorGlazingArea variable-selectable"
                                    style="text-align: center;"
                                    ng-click="vm.selectZoneSummaryVariable($event, summaryGroup, storeyIndex, row.description, 'exteriorGlazingArea')">
                                    {{row.exteriorGlazingArea.toFixed(2);}}
                                </td>

                            </tr>

                        </tbody>

                    </table>
                </md-tab-body>
            </md-tab>
        </md-tabs>

        <!-- Envelope Summary -->
        <span ng-if="vm.zoneSummary != null" style="margin-right:15px; font-size:18px;">
                Envelope Summary
        </span>
        <!-- $$ SECTION $$ -->
        <div ng-if="vm.envelopeSummary != null" class="SECTION-envelopeSummary" style="margin-bottom: 35px;">

            <!-- Table & Filters -->
            <div>
                <div>
                    <!-- Table -->
                    <table class="table table-striped table-hover table-condensed" style="margin-bottom: 30px; width:60%;">
                        <thead>
                            <tr style="font-weight: bold; text-align: center;">
                                <!-- Title -->
                                <th></th>
                                <!-- Only show each Sector if at least 1 row exists for column -->
                                <th ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummary)"
                                    ng-repeat="sector in vm.envelopeSummary.sectorKeys"
                                    style="text-transform: capitalize;  text-align: center;">
                                    {{ vm.sectorFromLabel(sector, vm.sectors).description || 'Total'}}
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            <!-- $$ VARIABLE $$ -->
                            <tr class="VARIABLE-exteriorWallAreaTotalsPerSector"
                                style="text-align: center;">
                                <td style="min-width: 130px;">Exterior Wall Area (m<sup>2</sup>)</td>
                                <!-- $$ SECTOR $$ -->
                                <td class="SECTOR-{{sector}} variable-selectable"
                                    style="min-width: 80px; text-align: center;"
                                    ng-click="vm.selectEnvelopeSummaryVariable($event, 'exteriorWallAreaTotalsPerSector', sector)"
                                    ng-repeat="sector in vm.envelopeSummary.sectorKeys"
                                    ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummary)">
                                    {{vm.envelopeSummary.exteriorWallAreaTotalsPerSector[sector].area.toFixed(2)}}
                                </td>
                            </tr>
                            <tr class="VARIABLE-exteriorGlazingAreaTotalsPerSector">
                                <td style="min-width: 130px;">Exterior Glazing Area (m<sup>2</sup>)</td>
                                <!-- $$ SECTOR $$ -->
                                <td class="SECTOR-{{sector}} variable-selectable"
                                    style="min-width: 80px; text-align: center;"
                                    ng-click="vm.selectEnvelopeSummaryVariable($event, 'exteriorGlazingAreaTotalsPerSector', sector)"
                                    ng-repeat="sector in vm.envelopeSummary.sectorKeys"
                                    ng-if="vm.isSectorNotEmpty(sector, vm.envelopeSummary)">
                                    {{vm.envelopeSummary.exteriorGlazingAreaTotalsPerSector[sector].area.toFixed(2)}}
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Filters -->
                    <envelope-summary-filters building="vm.zoneSummaryBuildingData"
                                              envelope-summary-object="vm.envelopeSummary"
                                              filters-object="vm.filters">
                    </envelope-summary-filters>

                </div>
            </div>

        </div>

        <!-- Interior Summary -->
        <span ng-if="vm.zoneSummary != null" style="margin-right:15px; font-size:18px;">
                Interior Summary
        </span>
        <!-- $$ SECTION $$ -->
        <table class="SECTION-interiorSummary table-striped table-hover table-condensed">

            <colgroup>
                <col span="1" style="min-width: 120px;">
                <col span="1" style="min-width: 150px;">
                <col span="1" style="min-width: 220px;">
                <col span="1" style="min-width: 220px;">
            </colgroup>

            <thead>
                <tr>
                    <th style="text-align:center"></th>
                    <th style="text-align:center">Adjacency</th>
                    <th style="text-align:center">Gross Internal Wall Area (m<sup>2</sup>)</th>
                    <th style="text-align:center">Net Internal Wall Area (m<sup>2</sup>)</th>
                </tr>
            </thead>

            <!-- $$ STOREY $$ -->
            <tbody class="STOREY-{{storeyIndex}} border-last" ng-repeat="(storeyIndex, storey) in vm.interiorSummary.storeys track by $index">

                <!-- $$ ROW $$ -->
                <tr class="ROW-{{rowIndex}} border-last" ng-repeat="(rowIndex, row) in storey.rows track by $index">

                    <!-- Group -->
                    <td data-title="Description"
                        ng-if="$index == 0"
                        rowspan="{{storey.rows.length}}"
                        class="text-center border-cell">
                        <span>{{storey.name}}</span>
                    </td>

                    <!-- Adjacency -->
                    <td>
                        {{row.adjacencyName}}
                    </td>

                    <!-- $$ VARIABLE $$ -->
                    <!-- Gross Internal Wall Area (m2) -->
                    <td class="VARIABLE-grossInternalWallArea variable-selectable"
                        style="text-align: center;"
                        ng-click="vm.selectInteriorSummaryVariable($event, storeyIndex, rowIndex, 'grossInternalWallArea', sector)">
                        {{row.grossInternalWallArea.toFixed(2);}}
                    </td>

                    <!-- $$ VARIABLE $$ -->
                    <!-- Net Internal Wall Area (m2) -->
                    <td class="VARIABLE-netInternalWallArea variable-selectable"
                        ng-click="vm.selectInteriorSummaryVariable($event, storeyIndex, rowIndex, 'netInternalWallArea', sector)"
                        style="text-align: center;">
                        {{row.netInternalWallArea.toFixed(2);}}
                    </td>

                </tr>

            </tbody>

        </table>

    </div>

    <!-- Buttons -->
    <div data-cc-widget-button-bar>
        <md-button class="md-raised" ng-click="vm.cancel()" style="float:right; margin:20px 30px;">
            Cancel
        </md-button>
    </div>

</form>

<style>

    /* Body */
    .content-container {
        padding: 25px 20px 10px 20px;
        box-sizing: border-box;
    }

    /*  */
    .export-processing-overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: grid;
        align-items: center;
        justify-items: center;
        z-index: 9999;
        background-color: rgba(255, 255, 255, 0.75);
    }

    .glazing-calc-modal-header {
        display: grid;
        grid-template-columns: 1fr 200px 30px;
        align-items: center;
        background-color: var(--thermarate-grey);
        padding: 0 24px 0 16px;
        height: 64px;
        max-height: 64px;
    }

        .glazing-calc-modal-header > h4 {
            margin: 0;
        }

    /* Selectable */
    .variable-selectable {
        cursor: pointer;
        user-select: none;
    }
    .variable-selectable:not(.variable-selected):hover {
        background-color: #adc43b !important;
    }

    /* Selected */
    .variable-selected {
        background-color: #7cb342 !important;
    }
    .variable-selectable.variable-selected:hover {
        cursor: default !important;
    }

    /* Envelope Summary Filters */
    .filter-grid {
        display: grid;
        /* 1fr causes child multi-select to stretch column when it becomes bigger */
        grid-template-columns: repeat(3, 33%);
        gap: 12px;
        justify-content: space-around;
        justify-items: center;
    }
        .filter-grid > div {
            width: 100%;
        }
    .filter-grid-item {
        padding: 16px;
    }
    .chart-view-container {
        width: 33%;
        margin: 0 auto;
    }

</style>