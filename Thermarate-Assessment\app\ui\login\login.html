
<section id="home-view" id="main-content-wrapper" data-ng-controller="LoginCtrl as vm">
    <section>
        <div>

            <div class="login-form-container" ng-if="vm.loginForm != null && vm.loginForm.stage == null">

                <!-- Welcome back -->
                <form id="welcomeBackForm">

                    <!-- Logo -->
                    <img class="logo" src="content/images/logo.png"/>

                    <!-- Text -->
                    <div class="form-title">Welcome back</div>

                    <!-- SSO - Google -->
                    <div class="sso-tile" ng-click="vm.googleLogin()">
                        <img src="../../../content/images/google-logo.svg" />
                        Continue with Google
                    </div>

                    <!-- SSO - Microsoft -->
                    <div class="sso-tile" ng-click="vm.microsoftLogin()">
                        <img src="../../../content/images/microsoft-logo.svg" />
                        Continue with Microsoft Account
                    </div>

                    <!-- Divider -->
                    <div id="or-divider">
                        <hr/>
                        <div>OR</div>
                    </div>

                    <!-- Email -->
                    <md-input-container class="login-input" flex-gt-sm>
                        <label>Email Address</label>
                        <input name="emailAddress"
                               id="emailAddressInput"
                               type="text"
                               ng-model="vm.loginForm.username"
                               required
                               ng-keydown="$event.keyCode == 13 && vm.welcomBackContinue()" />
                        <div ng-messages="welcomeBackForm.username.$error">
                            <div ng-message="required">Username is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Button -->
                    <button class="continue-button" 
                            type="submit"
                            ng-disabled="welcomeBackForm.$invalid"
                            ng-click="vm.welcomBackContinue()">Continue</button>

                </form>
            </div>

            <!-- Enter your password -->
            <div class="login-form-container" ng-if="vm.loginForm.stage == 'enterYourPassword'">
                <form id="passwordForm">

                    <!-- Logo -->
                    <img class="logo" src="content/images/logo.png"/>

                    <!-- Text -->
                    <div class="form-title">Enter your password</div>

                    <!-- Email -->
                    <md-input-container class="login-input extra-padding" flex-gt-sm>
                        <img class="field-user-icon" src="../../../content/images/user.png" />
                        <input name="username"
                               type="text"
                               ng-model="vm.loginForm.username"
                               ng-disabled="true"
                               required
                               ng-keydown="$event.keyCode == 13 && vm.loginAttempt()" />
                        <img class="field-edit-button" src="../../../content/images/close.png" ng-click="vm.goToWelcomeBack()" />
                    </md-input-container>

                    <!-- Password -->
                    <md-input-container class="login-input" flex-gt-sm style="height: 44px;">
                        <label>Password</label>
                        <input name="password"
                               id="passwordInput"
                               type="password"
                               ng-model="vm.loginForm.password"
                               required />
                        <div class="password-show-hide-button" ng-click="vm.toggleShowHidePassword(!vm.showPassword)">
                            <img src="../../../content/images/{{vm.passwordShowHideIcon}}.png" />
                        </div>
                    </md-input-container>

                    <!-- Forgot Password -->
                    <div class="forgot-password-button" ng-click="vm.forgotPassword()">Forgot password?</div>

                    <!-- Error -->
                    <div class="form-error" ng-if="vm.authError">{{vm.authError}}</div>

                    <!-- Button -->
                    <button class="continue-button"
                            type="submit"
                            ng-click="vm.loginAttempt()">Continue</button>

                </form>
            </div>

            <!-- 2-Factor Authentication -->
            <div class="login-form-container" ng-if="vm.loginForm.stage == 'twoFacConfirm'">
                <form id="twoFacForm">

                    <!-- Logo -->
                    <img class="logo" src="content/images/logo.png"/>

                    <!-- Text -->
                    <div class="form-title">2-Factor Authentication</div>

                    <!-- Text -->
                    <div class="form-subtitle">Enter the 2FA code from your<br/>authenticator app.</div>

                    <!-- Code -->
                    <md-input-container class="login-input" flex-gt-sm>
                        <label>6-Digit Code</label>
                        <input name="twoFacCode"
                               id="twoFacCodeInput"
                               type="text"
                               number-only
                               ng-model="vm.loginForm.twoFacCode"
                               required
                               md-autofocus
                               ng-keydown="$event.keyCode == 13 && vm.enterLogin()">
                        <div ng-messages="twoFacForm.twoFacCode.$error">
                            <div ng-message="required">6-Digit Code is required.</div>
                        </div>
                    </md-input-container>

                    <!-- Error -->
                    <div class="form-error" ng-if="vm.authError">{{vm.authError}}</div>

                    <!-- Button -->
                    <button class="continue-button"
                            type="submit"
                            ng-click="vm.login()">Continue</button>

                    <!-- Back To Login -->
                    <div class="form-bottom-action" ng-click="vm.goToWelcomeBack()">Back to login</div>
                </form>
            </div>

        </div>
    </section>
</section>

<style>

    .login-form-container {
        position: fixed;
        top: 50vh;
        left: 50vw;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 300px;
    }

        .logo {
            width: 220px;
            height: auto;
        }

        .form-title {
            margin-top: 35px;
            margin-bottom: 40px;
            font-size: 24px;
            color: #2e333a;
        }

        .form-subtitle {
            font-size: 14px;
            margin-top: -16px;
            margin-bottom: 16px;
            color: #2e333a;
        }

        .sso-tile, .login-input > input {
            border: 1px solid #c5c9d0 !important;
            border-radius: 5px;
            padding-left: 16px;
            height: 46px;
        }

        .sso-tile {
            width: 100%;
            height: 46px;
            box-sizing: border-box;
            margin-top: 8px;
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #40454b;
            align-items: center;
            user-select: none;
            cursor: pointer;
            transition: background-color 100ms linear;
        }

        .sso-tile:hover {
            background-color: #e5e5e5;
        }

            .sso-tile > img {
                width: 20px;
                height: auto;
            }

        #or-divider {
            color: #696c71;
            margin: 30px 0 20px 0;
            position: relative;
            width: 100%;
        }

            #or-divider > div {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -51%);
                background-color: #fafafa;
                padding: 10px;
                font-size: 11px;
            }

        .login-input {
            width: 100%;
            height: 46px;
        }

            .login-input > label {
                margin-left: 15px !important;
                top: -24% !important;
                height: max-content;
                width: max-content !important;
                background-color: #fafafa;
                text-align: left;
                padding: 0 6px 0 6px !important;
            }

            .login-input > input {
                font-size: 14px;
                padding-right: 8px;
            }

            .login-input > input[disabled] {
                color: black;
            }

            .login-input > .field-user-icon {
                width: 23px;
                height: auto;
                position: absolute;
                left: 17px;
                top: 50%;
                transform: translateY(-44%);
            }

            .login-input.extra-padding > input {
                padding-left: 50px;
            }

            .login-input > .field-edit-button {
                width: 17px;
                height: auto;
                position: absolute;
                right: 18px;
                top: 50%;
                transform: translateY(-40%);
                cursor: pointer;
                opacity: 0.6;
            }
            .login-input > .password-show-hide-button {
                position: absolute;
                top: 3px;
                right: 3px;
                width: 48px;
                height: 44px;
                border-radius: 5px;
                cursor: pointer;
                user-select: none;
            }
            .login-input > .password-show-hide-button:hover {
                background-color: #e5e5e5;
            }
            .login-input > .password-show-hide-button > img {
                width: 20px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
            /* Hide default show/hide button */
            .login-input > input[type=password]::-ms-reveal,
            .login-input > input[type=password]::-ms-clear {
                display: none;
            }

            .forgot-password-button {
                padding: 7px;
                color: #bdd73c;
                text-align: left;
                cursor: pointer;
            }

        .form-error {
            margin-top: 5px;
            color: red;
        }

        .continue-button {
            width: 100%;
            height: 46px;
            background-color: rgb(139, 195, 74);
            border-radius: 5px;
            margin: 12px 0 0 0;
            border: none;
            color: #fafafa;
            letter-spacing: 1px;
            font-size: 14px;
            transition: background-color 100ms linear;
        }
        .continue-button:hover {
            background-color: rgb(121 173 61);
        }

        .form-bottom-action {
            padding: 7px;
            margin-top: 10px;
            color: #2e333a;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
        }
        .form-bottom-action:hover {
            color: #bdd73c;
        }

</style>