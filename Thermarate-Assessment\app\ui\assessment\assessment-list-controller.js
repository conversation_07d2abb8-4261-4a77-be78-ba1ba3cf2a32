// OBSOLETE - NO LONGER IN USE
(function () {
    // The AssessmentListCtrl supports a list page.
    'use strict';
    var controllerId = 'AssessmentListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$state', 'assessmentservice', 'daterangehelper', assessmentListController]);
function assessmentListController($rootScope, $scope, $mdDialog, $state, assessmentservice, daterangehelper) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Assessments';
        vm.assessmentList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'jobId',
                    description: 'Job',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'statusCode',
                    description: 'Status',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'assessmentSoftwareCode',
                    description: 'Assessment Software',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'nccClimateZoneCode',
                    description: 'N C C Climate Zone',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'natHERSClimateZoneCode',
                    description: 'Nat H E R S Climate Zone',
                    dataType: 'string',
                    operators: []
                },
                // {
                //     name: 'buildingOrientation',
                //     description: 'Building Orientation',
                //     dataType: 'decimal',
                //     operators: []
                // },
                {
                    name: 'buildingExposureCode',
                    description: 'Building Exposure',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'proposedConditionedFloorArea',
                    description: 'Conditioned Floor Area',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedUnconditionedFloorArea',
                    description: 'Unconditioned Floor Area',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedGarageFloorArea',
                    description: 'Attached Garage Floor Area',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedHouseEnergyRating',
                    description: 'Proposed House Energy Rating',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'referenceHeating',
                    description: 'Reference Heating',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'referenceCooling',
                    description: 'Reference Cooling',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'referenceHouseEnergyRating',
                    description: 'Reference House Energy Rating',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedHeating',
                    description: 'Proposed Heating',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedCooling',
                    description: 'Proposed Cooling',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'proposedHouseEnergyRating',
                    description: 'Proposed House Energy Rating',
                    dataType: 'decimal',
                    operators: []
                },
                {
                    name: 'fileAId',
                    description: 'Primary Assessment File',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'performanceRequirementP261Code',
                    description: 'Performance Requirement P261',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'performanceRequirementP262Code',
                    description: 'Performance Requirement P262',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'complianceStatusCode',
                    description: 'Compliance Status',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'sealedExhaustFansSpecified',
                    description: 'Sealed Exhaust Fans Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'sealedExhaustFansDetails',
                    description: 'Sealed Exhaust Fans Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'unsealedExhaustFansSpecified',
                    description: 'Unsealed Exhaust Fans Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'unsealedExhaustFansDetails',
                    description: 'Unsealed Exhaust Fans Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'ceilingVentsSpecified',
                    description: 'Ceiling Vents Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'ceilingVentsDetails',
                    description: 'Ceiling Vents Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'wallVentsSpecified',
                    description: 'Wall Vents Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'wallVentsDetails',
                    description: 'Wall Vents Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'gasBayonetPointSpecified',
                    description: 'Gas Bayonet Point Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'gasBayonetPointDetails',
                    description: 'Gas Bayonet Point Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'cappedGasPointSpecified',
                    description: 'Capped/Crimped Gas Point Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'cappedGasPointDetails',
                    description: 'Capped/Crimped Gas Point Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'unfluedGasHeatersSpecified',
                    description: 'Unflued Gas Heaters Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'unfluedGasHeatersDetails',
                    description: 'Unflued Gas Heaters Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'fluedGasHeatersSpecified',
                    description: 'Flued Gas Heaters Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'fluedGasHeatersDetails',
                    description: 'Flued Gas Heaters Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'unsealedChimneySpecified',
                    description: 'Unsealed Chimney Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'unsealedChimneyDetails',
                    description: 'Unsealed Chimney Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'sealedChimneySpecified',
                    description: 'Sealed Chimney Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'sealedChimneyDetails',
                    description: 'Sealed Chimney Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'sealedRecessedLightFittingsSpecified',
                    description: 'Sealed Recessed Lights Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'sealedRecessedLightFittingsDetails',
                    description: 'Sealed Recessed Lights Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'unsealedRecessedLightFittingsSpecified',
                    description: 'Unsealed Recessed Lights Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'unsealedRecessedLightFittingsDetails',
                    description: 'Unsealed Recessed Lights Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'ceilingFansSpecified',
                    description: 'Ceiling Fans Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'ceilingFansDetails',
                    description: 'Ceiling Fans Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'evaporativeCoolingSystemSpecified',
                    description: 'Evaporative Cooling System Specified',
                    dataType: 'boolean',
                    operators: []
                },
                {
                    name: 'evaporativeCoolingSystemDetails',
                    description: 'Evaporative Cooling System Details',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'certificateNumber',
                    description: 'Certificate Number',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'cerficateDate',
                    description: 'Cerficate Date',
                    dataType: 'date',
                    operators: []
                },
                {
                    name: 'assessorUserId',
                    description: 'Assessor',
                    dataType: 'integer',
                    operators: []
                },
                {
                    name: 'assessorSignatureFile',
                    description: 'Assessor Signature File',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        var persistRangeName = "assessmentList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createAssessment = function () {
            $state.go("assessment-updateform", {assessmentId: -1});
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [{ field: "sealedExhaustFansDetails", operator: "startswith", value: val, logic: "or" },
                { field: "createdByName", operator: "startswith", value: val }];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            assessmentservice.getListCancel();
            assessmentservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = assessmentservice.currentFilter();
                    vm.assessmentList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.assessmentList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createAssessment,
                name: 'Add Assessment',
                desc: 'Add Assessment',
                roles: ['assessment_actions__editassessment'],
            });
        }

        setActionButtons();
    }
})();