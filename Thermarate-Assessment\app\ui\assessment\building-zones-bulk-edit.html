<form name="BuildingZonesBulkEdit"
      data-ng-controller='BuildingZonesBulkEditCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="{{vm.title}}"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="margin:auto; padding: 20px;">
        <md-radio-group layout="row"
                        ng-model="vm.data.bulkEditAction"
                        ng-change="vm.clearAddress()"
                        ng-disabled="vm.isLocked">
            <md-radio-button ng-value="'EDIT'">
                Edit
            </md-radio-button>
            <md-radio-button ng-value="'COPY'"
                             ng-click="vm.clearEditValues()">
                Copy
            </md-radio-button>
            <md-radio-button ng-value="'CLEAR'" 
                             ng-click="vm.clearEditValues()">
                Clear
            </md-radio-button>
            <md-radio-button ng-value="'DELETE'" 
                             ng-click="vm.clearEditValues()">
                Delete
            </md-radio-button>
        </md-radio-group>
    </div>

    <div style="min-width:600px; padding: 10px 20px;">

        <fieldset id="edit-inputs"
                  ng-if="vm.data.bulkEditAction == 'EDIT'">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Zone Name -->
                    <tr>
                        <td>
                            Zone Name
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.zoneDescription" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Zone Activity -->
                    <tr ng-if="vm.showForInterior()">
                        <td>
                            Zone Activity
                        </td>
                        <td>
                            <md-select ng-model="vm.data.zoneActivity"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="x"
                                           ng-repeat="x in vm.availableZoneActivityList()">
                                    {{x.description}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Roof Space Ventilation -->
                    <tr ng-if="vm.showForRoofSpace()">
                        <td>
                            Roof Space Ventilation
                        </td>
                        <td>
                            <md-select  ng-model="vm.data.airCavity"
                                        ng-model-options="{trackBy: '$value.airCavityCode'}"
                                        class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="v"
                                           ng-repeat="v in vm.roofVentilationList()">
                                    {{v.title}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>                    

                    <!-- Subfloor Space Ventilation -->
                    <tr ng-if="vm.showForSubfloorSpace()">
                        <td>
                            Subfloor Space Ventilation
                        </td>
                        <td>
                            <md-select  ng-model="vm.data.airCavity"
                                        ng-model-options="{trackBy: '$value.airCavityCode'}"
                                        class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="v"
                                           ng-repeat="v in vm.subfloorVentilationList()">
                                    {{v.title}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Zone Type -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Zone Type
                        </td>
                        <td>
                            <md-select ng-model="vm.data.zoneType"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="x"
                                           ng-repeat="x in vm.availableZoneTypeList()">
                                    {{x.description}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Conditioned -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Conditioned
                        </td>
                        <td>
                            <md-select ng-model="vm.data.conditioned"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Naturally Ventilated (THR-296: removed) --
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Naturally Ventilated
                        </td>
                        <td>
                            <md-select ng-model="vm.data.naturallyVentilated"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr> -->

                    <!-- NCC Classification -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            NCC Classification
                        </td>
                        <td>
                            <md-select ng-model="vm.data.nccClassification"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="x"
                                           ng-repeat="x in vm.availableNccClassificationList()">
                                    {{x.description}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Floor Area (m2) -->
                    <tr>
                        <td>
                            Floor Area (m<sup>2</sup>)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed  kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.floorArea"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Ceiling Area (m2) --> 
                    <tr ng-if="vm.showForInterior()">
                        <td>
                            Ceiling Area (m<sup>2</sup>)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed  kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.ceilingArea"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Volume (m3) -->
                    <tr ng-if="!vm.showForGroundSurface()">
                        <td>
                            Volume (m<sup>3</sup>)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.volume"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Storey -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Storey
                        </td>
                        <td>
                            <md-select ng-model="vm.data.storey"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="storey.floor"
                                           ng-repeat="storey in vm.storeys">
                                    {{storey.name}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Ceiling Fan -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Ceiling Fan
                        </td>
                        <td>
                            <md-select ng-model="vm.data.ceilingFan"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">Yes</md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Evaporative Cooler -->
                    <tr ng-if="vm.showForInteriorAndExterior()">
                        <td>
                            Evaporative Cooler
                        </td>
                        <td>
                            <md-select class="md-block vertically-condensed kindly-remove-error-spacer"
                                       ng-model="vm.data.evaporativeCooler">
                                <md-option ng-value="true">Yes</md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Storey Below -->
                    <tr ng-if="vm.showForRoofSpace()">
                        <td>
                            Storey Below
                        </td>
                        <td>
                            <md-select ng-model="vm.data.storeyBelow"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="storey.floor"
                                           ng-repeat="storey in vm.storeys">
                                    {{storey.name}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Storey Above -->
                    <tr ng-if="vm.showForSubfloorSpace()">
                        <td>
                            Storey Above
                        </td>
                        <td>
                            <md-select ng-model="vm.data.storeyAbove"
                                       class="vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="storey.floor"
                                           ng-repeat="storey in vm.storeys">
                                    {{storey.name}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <tr ng-if="vm.showForRoofSpace() || vm.showForSubfloorSpace()">
                        <td>
                            Reflective
                        </td>
                        <td>
                            <md-select ng-model="vm.data.isReflective"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">Yes</md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Below are for Ground Surface only -->

                    <!-- Conditioned Floor Area (m2) -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Conditioned Floor Area (m<sup>2</sup>)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.conditionedFloorArea"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Perimeter (m) -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Perimeter (m)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.perimeter"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Wall Thickness (m) -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Wall Thickness (m)
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.wallThickness"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Conductivity -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Conductivity
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.conductivity"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Diffusivity -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Diffusivity
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.diffusivity"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Ground Reflectance -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Ground Reflectance
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.groundReflectance"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Edge Insulation -->
                    <tr ng-if="vm.showForGroundSurface()">
                        <td>
                            Edge Insulation
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.data.edgeInsulation"
                                       ng-pattern-restrict="^[0-9\,\.]{0,9}$"
                                       formatted-number
                                       decimals="2" />
                            </md-input-container>
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <div ng-if="vm.data.bulkEditAction == 'COPY'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Zones will be copied. Copied zones will be inserted below their source Zone.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'CLEAR'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Zones will have their data cleared.</span>
        </div>

        <div ng-if="vm.data.bulkEditAction == 'DELETE'"
             style="text-align: center;">
            <span style="font-weight: bold;">The selected Zones will be deleted. </span>
        </div>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

            <div class="clearfix"></div>
        </div>

    </div>

</form>