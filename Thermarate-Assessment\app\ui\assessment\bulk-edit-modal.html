<form name="BulkEditModal"
      data-ng-controller='BulkEditModalCtrl as vm'
      class="main-content-wrapper">

    <div data-cc-widget-header
         data-title="Bulk Edit"
         data-is-modal="true"
         data-cancel="vm.cancel()">
    </div>

    <div style="min-width: 600px; padding: 10px 20px;">

        <!--  -->
        <md-dialog-content layout layout-wrap layout-padding>
            <span flex="100">By filling in any of these fields and clicking "OK" you will edit all of the selected Drawings.</span>
            <span flex="100" ng-if="!vm.isArchiveTable">Warning! By choosing archive or delete, the action will apply when you click "OK"</span>
            <span flex="100" ng-if="vm.isArchiveTable">Warning! By choosing reinstate or delete, the action will apply when you click "OK"</span>
        </md-dialog-content>
        <!--  -->

        <fieldset id="edit-inputs">
            <table class="table table-striped table-hover table-condensed">
                <thead>
                    <tr>
                        <th class="text-left">Option</th>
                        <th class="text-left">Value</th>
                    </tr>
                </thead>
                <tbody>

                    <!-- Size -->
                    <tr>
                        <td>
                            Size
                        </td>
                        <td>
                            <md-select ng-model="vm.pageSize"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="pageSizeListItem.id" ng-repeat="pageSizeListItem in vm.pageSizeList track by pageSizeListItem.id">
                                    {{pageSizeListItem.description}}
                                </md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Stamp -->
                    <tr ng-if="!vm.isArchiveTable">
                        <td>
                            Stamp
                        </td>
                        <td>
                            <md-select ng-model="vm.stampAction"
                                       ng-disabled="vm.isIncludedInReport == false"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="null">Don't Change</md-option>
                                <md-option ng-value="'YESDONTPLACESTAMP'">Yes (But Don't Place Stamp)</md-option>
                                <md-option ng-value="'YESPLACESTAMPDEFAULT'">Yes (Place Using Default Location)</md-option>
                                <md-option ng-value="'NO'">No</md-option>
                            </md-select>
                            <md-tooltip md-direction="top">
                                Select this option to place stamps on drawings at the default location.
                            </md-tooltip>
                        </td>
                    </tr>

                    <!-- Revision -->
                    <tr>
                        <td>
                            Revision
                        </td>
                        <td>
                            <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer">
                                <input type="text"
                                       ng-model="vm.revision" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Revision Date -->
                    <tr>
                        <td>
                            Revision Date
                        </td>
                        <td>
                            <md-input-container flex="100" class="md-block vertically-condensed kindly-remove-error-spacer modal-datepicker">
                                <md-datepicker
                                    class="hiddenCalendar" 
                                    ng-model="vm.revisionDate"
                                    md-placeholder="Select a Date" />
                            </md-input-container>
                        </td>
                    </tr>

                    <!-- Include in Report -->
                    <tr>
                        <td>
                            Include in Report
                        </td>
                        <td>
                            <md-select ng-model="vm.isIncludedInReport"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">Yes</md-option>
                                <md-option ng-value="false" ng-click="vm.stampAction = 'NO'">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Show on Client Portal -->
                    <tr>
                        <td>
                            Show on Client Portal
                        </td>
                        <td>
                            <md-select ng-model="vm.isShownToClient"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">No</md-option>
                                <md-option ng-value="null">Don't Change</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Archive/Reinstate -->
                    <tr>
                        <td ng-if="!vm.isArchiveTable">Archive</td>
                        <td ng-if="vm.isArchiveTable">Reinstate</td>
                        <td>
                            <md-select ng-model="vm.archive"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">No</md-option>
                            </md-select>
                        </td>
                    </tr>

                    <!-- Delete -->
                    <tr>
                        <td>
                            Delete
                        </td>
                        <td>
                            <md-select ng-model="vm.delete"
                                       class="md-block vertically-condensed kindly-remove-error-spacer">
                                <md-option ng-value="true">
                                    Yes
                                </md-option>
                                <md-option ng-value="false">No</md-option>
                            </md-select>
                        </td>
                    </tr>

                </tbody>
            </table>

        </fieldset>

        <!-- Confirm / Cancel Buttons -->
        <div data-cc-widget-button-bar
             layout="row"
             style="margin-top: 50px;">

            <md-button class="md-raised md-primary"
                       style="margin-left: auto;"
                       ng-click="vm.confirm()">
                Confirm
            </md-button>

            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>

            <div class="clearfix"></div>
        </div>

    </div>

</form>

<style>
    /* Date Picker adjustments */

    .modal-datepicker .md-datepicker-input {
        font-size: 12px !important;
    }

    /* Placeholder color */

    .modal-datepicker .md-datepicker-input::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: rgba(0, 0, 0, 0.87);
        opacity: 1; /* Firefox */
    }

    .modal-datepicker .md-datepicker-input:-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: rgba(0, 0, 0, 0.87);
    }

    .modal-datepicker .md-datepicker-input::-ms-input-placeholder { /* Microsoft Edge */
        color: rgba(0, 0, 0, 0.87);
    }

    /* Triangle icon color */

    .modal-datepicker .md-datepicker-triangle-button .md-datepicker-expand-triangle,
    .modal-datepicker .md-datepicker-triangle-button:hover .md-datepicker-expand-triangle {
        border-top-color: rgba(0, 0, 0, 0.87);
    }

    /* Date Picker full width */

    md-datepicker.hiddenCalendar .md-datepicker-input-container {
        width: 100%;
    }
    .md-datepicker-input {
        max-width: 100%;
    }
</style>