(function () {
    // The SuburbUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'SuburbUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'stateservice', 'suburbservice', suburbUpdateController]);
function suburbUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  stateservice, suburbservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Suburb';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.suburbCode = null;
        vm.latRegex = "[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?)";
        vm.lonRegex = "(^[-+]?(180|([1][0-7][0-9]|[0-9]{1,2})(\\.[0-9]+)?)$)";
        vm.suburb = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Suburb";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.suburbCode = $scope.suburbCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.suburbCode = $stateParams.suburbCode;
        }

        // Get data for object to display on page
        var suburbCodePromise = null;
        if (vm.suburbCode != null) {
            suburbCodePromise = suburbservice.getSuburb(vm.suburbCode)
            .then(function (data) {
                if (data != null) {
                    vm.suburb = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists
        vm.stateList = [];
        var statePromise = stateservice.getList()
            .then(function(data){
                vm.stateList = data.data;
            });

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("suburb-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                suburbservice.createSuburb(vm.suburb).then(function(data){
                    vm.suburb = data;
                    vm.suburbCode = vm.suburb.suburbCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                suburbservice.updateSuburb(vm.suburb).then(function(data){
                    if (data != null) {
                        vm.suburb = data;
                        vm.suburbCode = vm.suburb.suburbCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            suburbservice.deleteSuburb(vm.suburbCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            suburbservice.undoDeleteSuburb(vm.suburbCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();