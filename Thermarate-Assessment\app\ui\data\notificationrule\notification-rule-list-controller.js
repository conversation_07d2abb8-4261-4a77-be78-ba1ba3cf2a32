(function () {
    // The ProjectdescriptionListCtrl supports a list page.
    'use strict';
    var controllerId = 'notificationRuleListCtrl';
    angular.module('app').controller(
        controllerId,
        ['$rootScope', '$scope', '$mdDialog', 'notificationruleservice', 'daterangehelper', 'clientservice', 'statusservice', projectdescriptionListController]
    );
    function projectdescriptionListController(
        $rootScope, $scope, $mdDialog, notificationruleservice, daterangehelper, clientservice, statusservice
    ) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        // The model for this form 
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Notification Rules';
        vm.notificationRuleList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = [{ code: 'All', name: 'All' }];
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.statusList = [];

        vm.actionButtons = [{
            onclick: vm.createProjectdescription,
            name: 'Add New',
            desc: 'Add New',
            roles: ['settings__settings__create'],
        }];

        var persistRangeName = "projectdescriptionList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
          'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
          'Last Year', '12 Months', 'All Time');

        vm.queryModel = {
            canSave: false,
            fields: [{
                name: 'projectDescriptionCode',
                description: 'Code',
                dataType: 'string',
                operators: []
            },{
                name: 'description',
                description: 'Description',
                dataType: 'string',
                operators: []
            }],
        };

        var saveTableState = null;

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        vm.initialised = false;
        function initialise() {
            Promise.all([
                clientservice.getList(),
                statusservice.getList(),
            ]).then(results => {
                let r = 0;
                vm.clientList = results[r++].data;
                vm.statusList = results[r++].data.filter(x => x.statusCode.startsWith("A"));
                vm.initialised = true;
            });
        }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null);
            // localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.callServer = function callServer(tableState) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;
            var start = pagination.start || 0; // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100; // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }
            var filter = null;
            if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                var val = saveTableState.search.predicateObject.$;
                // Adjust here for the columns quick search will search.
                filter = [
                    { field: "description", operator: "startswith", value: val, logic: "or" },
                    { field: "createdByName", operator: "startswith", value: val }
                ];
            }
            if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                filter = vm.currentQuery.filter;
            }
            daterangehelper.correctRangeDates(vm.rptDateRange);
            notificationruleservice.getFilteredCancel();
            notificationruleservice.getFiltered(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter).then(
                result => {
                    if (result == undefined || result == null) {
                        return; // Its been cancelled so get out of here.
                    }
                    vm.currentFilter = notificationruleservice.currentFilter();
                    vm.notificationRuleList = result.data;
                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.notificationRuleList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                (error) => {
                    vm.isBusy = false;
                }
            );
        };

        vm.createProjectdescription = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/notificationrule/notification-rule-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: () => 'New'
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(
                () => vm.refreshList(null),
                () => vm.refreshList(null)
            ).finally(() => modalScope.modalInstance = undefined);
        }

        vm.statusCodeToDesc = function (code) {
            if (code == null || code == "")
                return "Any";
            return vm.statusList.find(x => x.statusCode === code)?.description;
        }

        vm.delete = function(row) {
            let index = vm.notificationRuleList.indexOf(row);
            notificationruleservice.deleteNotificationRule(row.notificationRuleId).then(() => {
                row.deleted = true;
                vm.notificationRuleList.splice(index, 1);
            });
        }

        vm.clone = function(row) {
            notificationruleservice.copyNotificationRule(row.notificationRuleId).then(notificationRule => {
                vm.notificationRuleList.push(notificationRule);
                vm.notificationRuleList.sort((a,b) => (a.title > b.title) ? 1 : ((b.title > a.title) ? -1 : 0));
            });
        }

        vm.immediateUpdateEnabled = function(row, value = null) {
            notificationruleservice.updateEnabled(row.notificationRuleId, value !== null ? value : !row.enabled);
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialise();
    }
})();