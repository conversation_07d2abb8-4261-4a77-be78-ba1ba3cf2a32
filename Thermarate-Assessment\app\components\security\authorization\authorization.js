/*
* Security Authorisation Module
* -----------------------------
* Ensures the user is authorised for the requested route.
*
*/

angular.module('security.authorization', ['security.service', 'common'])

// Ensure the user is authorised for the requested route (ie. page)
.run(['$rootScope', '$window', '$location', '$state', 'security', 'common', function ($rootScope, $window, $location, $state,  security, common) {

    var logError = common.logger.getLogFn('security', 'error');

    // Everytime a route changes make sure the user has the correct access rights
    $rootScope.$on("$stateChangeStart", function (event, next, nextParams, current, currentParams) {
        
        if (next.name == "unauthorised" || next.name == "login" || next.name == "external-login-verify")
            return;
        
        //If navigation is heading to login page then all sweet, otherwise stop the navigation to wait for authorisation
        if (next.name != "login" && $rootScope.authenticated != true) {
            event.preventDefault();
        }

        let urlArgs = new URLSearchParams(window.location.href.substring(window.location.href.indexOf('?')+1, window.location.href.length));
        let sso_type = (urlArgs.get('scope'))?.includes("googleapis") ? "google" : "microsoft";
        let ssoCode = urlArgs.get('code');

        // IF have sso auth code, login with sso
        if (ssoCode != null && ssoCode != '') {
            window.location.href = `${(new $window.URL($location.absUrl())).origin}/#/external-login-verify?type=${sso_type}&code=${ssoCode}`;
        // ELSE login like normal
        } else {
            security
                .authorizeRoles(next.data.roles)
                .then(function (hasAccess) {
              
                if (hasAccess != true && !window.location.hash.includes("external-login-verify")) {
                    $rootScope.authenticated = false;
                    if (security.isAuthenticated()) {
                        logError('You are not authorised to access the requested page.');
                        $location.path('/unauthorised');
                    }
                    else {
                        // Not authenticated so show the login page.
                        $location.path('/login');
                    }
                } else {
                    //if authenticated is already true then the routing was not stopped above with event.preventdefault so no need to do anything
                    //If it wasn't already true then set to true and re-route to same page
                    if ($rootScope.authenticated != true) {
                        $rootScope.authenticated = true;
                        $state.go(next, nextParams);
                    }
                }
            });
        }
        
    });

    $rootScope.previousState;
    $rootScope.currentState;
    $rootScope.previousStateParams;
    $rootScope.currentStateParams;

    $rootScope.$on('$stateChangeSuccess', function (ev, to, toParams, from, fromParams) {
        if (to.sticky != true) {
            $rootScope.previousState = from.name;
            $rootScope.previousStateParams = fromParams;
        }
        $rootScope.currentState = to.name;
        $rootScope.currentStateParams = toParams;
    });
}]);

