<!-- Climate Information -->
<md-card ng-form="climateForm">
    <md-card-header>
        <span class="md-title">Climate Information</span>
    </md-card-header>
    <md-card-content>

        <!-- ******** Nat HERS Climate Zone ******** -->
        <md-input-container flex="100" class="md-block">
            <label>NatHERS Climate Zone</label>
            <md-select name="natHERSClimateZoneCode"
                       ng-required="true"
                       ng-change="vm.updateNatHERSData(vm.assessment.natHERSClimateZone);"
                       ng-model="vm.assessment.natHERSClimateZone"
                       ng-model-options="{trackBy: '$value.natHERSClimateZoneCode'}"
                       ng-disabled="vm.disabled">
                <md-option ng-value="item"
                           ng-repeat="item in vm.natHERSClimateZoneList track by item.natHERSClimateZoneCode">
                    {{item.description}}
                </md-option>
            </md-select>
            <div ng-messages="climateForm.natHERSClimateZoneCode.$error">
                <div ng-message="required">NatHERS Climate Zone is required.</div>
            </div>
        </md-input-container>

        <!-- ******** NCC Climate Zone ******** -->
        <md-input-container flex="100" class="md-block">
            <label>NCC Climate Zone</label>
            <md-select name="nccClimateZoneCode"
                       ng-required="true"
                       ng-model="vm.assessment.nccClimateZoneCode"
                       ng-disabled="vm.disabled">
                <md-option ng-value="item.nccClimateZoneCode"
                           ng-repeat="item in vm.nccClimateZoneCodeList track by item.nccClimateZoneCode">
                    {{item.description}}
                </md-option>
            </md-select>
            <div ng-messages="climateForm.nccClimateZoneCode.$error">
                <div ng-message="required">NCC Climate Zone is required.</div>
            </div>
        </md-input-container>

        <!-- ******** NCC Climate Zone Description ******** -->
        <md-input-container flex="100" class="md-block">
            <label>NCC Climate Zone Description</label>
            <md-select name="NCCClimateZoneCodeDesc"
                       ng-model="vm.assessment.nccClimateZoneCode"
                       ng-disabled="true">
                <md-option ng-value="'NCC1'">High humidity summer, warm winter</md-option>
                <md-option ng-value="'NCC2'">Warm humid summer, mild winter</md-option>
                <md-option ng-value="'NCC3'">Hot dry summer, warm winter</md-option>
                <md-option ng-value="'NCC4'">Hot dry summer, cool winter</md-option>
                <md-option ng-value="'NCC5'">Warm temperate</md-option>
                <md-option ng-value="'NCC6'">Mild temperate</md-option>
                <md-option ng-value="'NCC7'">Cool temperate</md-option>
                <md-option ng-value="'NCC8'">Alpine</md-option>
            </md-select>

        </md-input-container>
    </md-card-content>
</md-card>

<!-- Comfort Metrics -->
<md-card>
    <md-card-header>
        <span class="md-title">Comfort Metrics</span>
    </md-card-header>
    <md-card-content>

        <md-input-container flex="100" class="md-block">
            <label>Mean January Outdoor Air Temperature ({{vm.symbol("degrees")}}C)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.meanJanuaryTemp.toFixed(1)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Assumed Heating Thermostat Set Point ({{vm.symbol("degrees")}}C)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.heatingSetPoint.toFixed(1)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Assumed Cooling Thermostat Set Point  ({{vm.symbol("degrees")}}C)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.coolingSetPoint.toFixed(1)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Annual Average Daily Temperature Range ({{vm.symbol("degrees")}}C)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.annualAvgDailyTempRange.toFixed(2)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Heating Hours (HH)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.heatingHours.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Heating Degree Hours (HDH)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.heatingDegreeHours.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Heating Degree Days (HDD)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.heatingDegreeDays.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Cooling Hours (CH)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.coolingHours.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Cooling Degree Hours (CDH)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.coolingDegreeHours.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Cooling Degree Days (CDD)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.coolingDegreeDays.toFixed(0)"/>
        </md-input-container>

        <md-input-container flex="100" class="md-block">
            <label>Dehumidification Gram Hours (DGH)</label>
            <input type="text"
                   ng-disabled="true"
                   ng-value="vm.comfortMetrics.dehumidificationGramHours.toFixed(0)"/>
        </md-input-container>

    </md-card-content>
</md-card>

<!-- Climate Graphs -->
<div class="graph-grid">

    <!-- Temperature -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="monthly-temperature-chart"></div>
        </md-card-content>
    </md-card>

    <!-- Daily Temperature Heatmap -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="temperature-heatmap"></div>
        </md-card-content>
    </md-card>

    <!-- Solar Radiation -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="solar-radiation"></div>
        </md-card-content>
    </md-card>

    <!-- Solar Radiation Variation -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="solar-radiation-variation"></div>
        </md-card-content>
    </md-card>

    <!-- Windrose Chart -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="windrose-chart"></div>
        </md-card-content>
    </md-card>

    <!-- Average Moisture -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="monthly-average-moisture-chart"></div>
        </md-card-content>
    </md-card>

    <!-- Heating and Cooling Degree Days -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="heating-and-cooling-degree-days-chart"></div>
        </md-card-content>
    </md-card>

    <!-- Thermal Comfort -->
    <md-card class="graph-card">
        <md-card-content>
            <div id="thermal-comfort-chart"></div>
        </md-card-content>
    </md-card>

</div>

<style>

    /* By default, show 2 cards per row. */
    .graph-grid {
        display: grid;
        grid-template-columns: 50% 50%;
    }

    .graph-card {

    }

    /* On smaller screens, only have 1 graph per row. */
    @media screen and (max-width: 1140px) {
        .graph-grid {
            grid-template-columns: 100%;
        }
    }

</style>
