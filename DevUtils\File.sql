USE [thermarate];

SELECT TOP (100)
       [FileId]
      ,[DisplayName]
      ,[FileName]
      ,[FolderName]
      ,[URL]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[Deleted]
      ,[VersionNo]
      ,[JobId]
      ,[AssessmentId]
      ,[PageCount]
      ,[OriginalFileUrl]
      ,[SizeInBytes]
      ,[SortOrder]
      ,[ShowOnClientPortal]
      ,[AllowDownloadOnClientPortal]
      ,[ComplianceOptionId]
      ,[Category]
      ,[Classification]
  FROM [dbo].[RSS_File]
  WHERE 1=1
    AND [FileName] LIKE '%.pro%'
  ORDER BY [CreatedOn] DESC