﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;

namespace ExtractionTests
{
    [TestClass]
    public class EnergyPlusWeatherExtractionTests
    {


        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public void TestFullProcess()
        {
            var directory = Directory.GetCurrentDirectory() + "\\EPW Files/";

            // Process the Dataset into our DB.
            TenureExtraction.EnergyPlusWeatherExtractor ex = new TenureExtraction.EnergyPlusWeatherExtractor(
                directory,
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            //ex.Extract();
            ex.RunFullProcess();
            ex.Dispose();
        }


        [TestMethod]
        [Timeout(TestTimeout.Infinite)]
        public async Task TestExcelOutput()
        {
            var directory = Directory.GetCurrentDirectory() + "\\EPW Files/";

            // Process the Dataset into our DB.
            TenureExtraction.EnergyPlusWeatherExtractor ex = new TenureExtraction.EnergyPlusWeatherExtractor(
                directory,
                "Data Source=localhost;Initial Catalog=thermarate;Integrated Security=True"
            );

            //ex.Extract();
            var fileLocation = await ex.CreateZipArchive();
            ;

            
        }
    }
}
