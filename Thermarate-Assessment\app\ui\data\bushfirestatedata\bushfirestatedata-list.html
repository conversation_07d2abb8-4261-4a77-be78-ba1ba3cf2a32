<section id="bushfirestatedata-list-view" class="main-content-wrapper" data-ng-controller="BushfirestatedataListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
             data-quick-find-model='vm.listFilter'
             data-quick-find-holder="Search"
             data-action-buttons='vm.actionButtons'
             data-refresh-list='vm.refreshList()'
             data-spinner-busy='vm.isBusy'
             data-filter-options="vm.filterOptions"
             data-filter-changed="vm.refreshList(value)"
             data-current-filter="vm.currentFilter"
             data-query-builder-model="vm.queryModel"
             data-query-builder-name="Bushfirestatedata"
             data-query-builder-current="vm.currentQuery">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.bushfireStateDataList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="bushfireStateDataList"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="stateCode" class="can-sort text-center">State</th>
                        <th st-sort="hasBushfireData" class="can-sort text-center" style="width: 140px;">Has Bushfire Data</th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.bushfireStateDataList">
                        <td data-title="Code" class="text-center">{{::row.stateCode }}</td>
                        <td data-title="Has Bushfire Data" class="text-center">
                            <div style="display: grid; justify-items: center;">
                                <md-switch style="margin: 0"
                                           ng-model="row.hasBushfireData"
                                           ng-click="vm.immediateUpdateHasBushfireData(row);">
                                </md-switch>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>
