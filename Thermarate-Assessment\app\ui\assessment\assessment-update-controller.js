(function () {
    // The AssessmentUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'AssessmentUpdateCtrl';
    angular.module('app')
        .factory(controllerId, ['nemLogging', 'leaflet', 'leafletDrawEvents'])
        .controller(controllerId, ['$rootScope', 'common', '$interval', '$scope', '$mdDialog', '$stateParams', '$state', '$q', 'bootstrap.dialog',
            'security', 'jobservice', 'statusservice', 'assessmentsoftwareservice', 'nathersclimatezoneservice', 'buildingexposureservice',
            'fileservice','compliancestatusservice', 'userservice', 'assessmentservice', 'zonesummaryservice',
            'assessmentdrawingservice', 'Upload', 'sequentialguidservice', 'compliancemethodservice',
            'bushfireattacklevelservice', 'bushfireproneservice', 'nccclimatezonedataservice', 'clientservice',
            'projectdescriptionservice', 'lottypeservice', 'streettypeservice', 'suburbservice', 'stateservice', 'priorityservice',
            'mapdataservice', 'constants', 'uuid4', 'addressservice', 'metromapservice', 'slipaddress', 'worksdescriptionservice',
            'nominatedbuildingsurveyorservice', 'assessmentcomplianceoptionservice', 'constructionservice', 'certificationservice',
            'servicetemplateservice', 'nccclimatezoneservice', 'bushfirestatedataservice', 'zoneservice', 'coreLoop', 'designchangeservice', 'wholeofhomeexportservice', assessmentUpdateController]);
    function assessmentUpdateController($rootScope, common, $interval, $scope, $mdDialog, $stateParams, $state, $q, modalDialog,
        securityservice, jobservice, statusservice, assessmentsoftwareservice, nathersclimatezoneservice, buildingexposureservice,
        fileservice, compliancestatusservice, userservice, assessmentservice, zonesummaryservice,
        assessmentdrawingservice, Upload, sequentialguidservice, compliancemethodservice,
        bushfireattacklevelservice, bushfireproneservice, nccclimatezonedataservice, clientservice,
         projectdescriptionservice, lottypeservice, streettypeservice, suburbservice, stateservice, priorityservice,
        mapdataservice, constants, uuid4, addressservice, metromapservice, slipaddress, worksdescriptionservice,
        nominatedbuildingsurveyorservice, assessmentcomplianceoptionservice, constructionservice, certificationservice,
        servicetemplateservice, nccclimatezoneservice, bushfirestatedataservice, zoneservice, coreLoop, designchangeservice, wholeofhomeexportservice) {


        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        // The model for this form
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.selectedAssessment = {};
        vm.isArchiveTable = false;

        vm.assessmentList = [];
        var eventListenerList = [];
        vm.title = 'Edit Assessment';
        vm.showdropdown = false;
        //name of current template.
        vm.templateName = null;
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.assessmentId = null;
        vm.jobId = null;
        vm.assessment = { };
        vm.showprojectDescriptions = false;
        vm.newRecord = $stateParams.assessmentId == -1;
        vm.isCopy = $stateParams.isCopy;
        vm.isLocked = false;
        // Check if 'Add Assessment' is clicked from the Job form
        vm.jobId = $stateParams.jobId;
        //load nthe client to get client options and defaults if editing a template
        vm.client = null;
        //Must evaluate whether the template is new or not (e.g. if the template has been touched)
        //Only way to  evaluate a template is new, as a record is already created before pressing save on
        //the asessment- update page, is by the 'modifiedOn' date field being null
        vm.isNewTemplate = false;
        vm.template = {};
        //regexes for data
        vm.assessmentVersionRegex = "\\d{1,3}(\\.\\d{1,3})?";
        vm.latRegex = "[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?)";
        vm.lonRegex = "(^[-+]?(180|([1][0-7][0-9]|[0-9]{1,2})(\\.[0-9]+)?)$)";
        vm.degreesDMSRegex = "[0-9]{1,3}";
        vm.minutesDMSRegex = "[0-9]{1,2}";
        vm.secondsDMSRegex = "[0-9]{1,2}(?:\\.[0-9]{1,2})?";
        vm.directionLatRegex = "[N|S|n|s]{1}";
        vm.directionLonRegex = "[E|W|e|w]{1}";
        vm.singleDigitRegex = "^\d{0,2}(?:\.\d)?$";
        //flag to use dms or lat/long
        vm.useDMS = false;
        vm.dms = {};

        const MAP_INITIALIZATION_TIMEOUT = 500;

        vm.tempCustomAddressHolder = {}; // Temporarily holds non-primary address details for use with search-address.
        vm.buildingToShow = 'proposed'; // Used to switch between showing proposed and reference buildings in building elements tab

        // For whatever reason, our security directive (redi-show-by-roles or redi-allow-roles) does not work nicely with
        // md-tab elements. So for tabs, we have to rely on our security service and then chuck a simple ng-disable on them.
        // (note: ng-show still doesn't work, and ng-if will screw other behind the scenes logic up)



        // - ----------- - //
        // - PERMISSIONS - //
        // - ----------- - //

        vm.setPermissions = function () {
            vm.permission_action_glazingcalculator = securityservice.immediateCheckRoles(['assessment_actions__glazingcalculator']);
            vm.permission_action_exportwoh = securityservice.immediateCheckRoles(['assessment_actions__exportwoh']);
            vm.permission_action_addmultisim = securityservice.immediateCheckRoles(['assessment_actions__addmultisim']);
            vm.permission_action_deletemultisim = securityservice.immediateCheckRoles(['assessment_actions__deletemultisim']);
            vm.permission_action_addmultisim = securityservice.immediateCheckRoles(['assessment_actions__addmultisim']);

            vm.permission_tab_assessment_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessment__view']);
            vm.permission_tab_invoicing_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__invoicing__view']);
            vm.permission_tab_site_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__site__view']);
            vm.permission_tab_address_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__address__view']);
            vm.permission_tab_map_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__map__view'])
            vm.permission_tab_aerialImage_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__aerialimage__view']);
            vm.permission_tab_climate_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__climate__view'])
            vm.permission_tab_lot_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__lot__view'])
            vm.permission_tab_design_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__design__view']);
            vm.permission_tab_construction_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__construction__view']);
            vm.permission_tab_general_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__general__view']);
            vm.permission_tab_openings_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__openings__view']);
            vm.permission_tab_services_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__services__view']);
            vm.permission_tab_drawings_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__drawings__view']);
            vm.permission_tab_simulation_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__simulation__view']);
            vm.permission_tab_multisim_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__multisim__view']);
            vm.permission_tab_analytics_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__analytics__view']);
            vm.permission_tab_results_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__results__view']);
            vm.permission_tab_reports_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__reports__view']);

            vm.permission_tab_assessment_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessment__edit']);
            vm.permission_tab_invoicing_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__invoicing__edit']);
            vm.permission_tab_site_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__site__edit']);
            vm.permission_tab_address_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__address__edit']);
            vm.permission_tab_map_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__map__edit'])
            vm.permission_tab_aerialImage_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__aerialimage__edit']);
            vm.permission_tab_climate_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__climate__edit'])
            vm.permission_tab_lot_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__lot__edit'])
            vm.permission_tab_design_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__design__edit']);
            vm.permission_tab_construction_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__construction__edit']);
            vm.permission_tab_general_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__general__edit']);
            vm.permission_tab_openings_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__openings__edit']);
            vm.permission_tab_services_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__services__edit']);
            vm.permission_tab_drawings_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__drawings__edit']);
            vm.permission_tab_simulation_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__simulation__edit']);
            vm.permission_tab_analytics_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__analytics__edit']);
            vm.permission_tab_multisim_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__multisim__edit']);
            vm.permission_tab_results_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__results__edit']);
            vm.permission_tab_reports_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__reports__edit']);

            vm.permission_field_creator_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__creator__view']);
            vm.permission_field_creator_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__creator__edit']);
            vm.permission_field_assignee_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assignee__view']);
            vm.permission_field_assignee_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assignee__edit']);
            vm.permission_field_assignedassessor_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assignedassessor__view']);
            vm.permission_field_assignedassessor_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assignedassessor__edit']);
            vm.permission_field_eventhistory_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__eventhistory__view']);
            vm.permission_field_address_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__address__view']);
            vm.permission_field_address_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__address__edit']);
            vm.permission_field_searchaddress_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__searchaddress__view']);
            vm.permission_field_searchaddress_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__searchaddress__edit']);
            vm.permission_field_customaddress_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__customaddress__view']);
            vm.permission_field_customaddress_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__customaddress__edit']);
            vm.permission_field_projectowner_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__projectowner__view']);
            vm.permission_field_projectowner_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__projectowner__edit']);
            vm.permission_field_certification_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__view']);
            vm.permission_field_certification_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__edit']);
            vm.permission_field_assessmentmethod_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__view']);
            vm.permission_field_assessmentmethod_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__edit']);
            vm.permission_field_assessmentsoftware_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__view']);
            vm.permission_field_assessmentsoftware_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit']);
            vm.permission_field_herrequired_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__view']);
            vm.permission_field_herrequired_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__edit']);
        }

        //whether compliance option rows with performance solaution and/or house energy rating are required.
        //Rules to determine if these values are true are run when after assessment is loaded.
        vm.requirePerformanceSolutionComplianceMethod = false;
        vm.requireHouseEnergyRatingComplianceMethod = false;
        //default string to use as a description when compliance method is performance solution
        vm.complianceOptionDefaultDescription = "No changes or upgrades required";
        //there are some very specific rules regarding when to clear address fields. This stop address from clearing opn load when state gets added i
        var skipStateClear = false;

        // -------------------------------------------------------------------------------------------------------------------------------------------
        // ASSESSMENT BRRRRRR STUFF
        let intervalTimer = null;

        function initialize() {

            // Brrrrrrr.
            intervalTimer = setInterval(
                () => {
                    // Brrrrrrrr.
                    // This loop is for Assessment level brrr instead of building level brrr.
                    // Site Tab
                    coreLoop.computeRuralLot(vm.assessment);
                    coreLoop.computeNarrowLot(vm.assessment);
                },
                1000);

            // Initialize cached map image values and error messages
            setTimeout(() => {
                vm.updateMapImageCachedValues();
                vm.updateCachedErrorMessages();
                vm.updateCachedGeneralTabErrors();
            }, MAP_INITIALIZATION_TIMEOUT + 100);
        }

        initialize();

        $scope.$on("$destroy", function () {
            if (intervalTimer)
                clearInterval(intervalTimer);
        });

        // -------------------------------------------------------------------------------------------------------------------------------------------
        // METRO MAP STUFF

        // DOM Elements we will need to interact with
        const CROP_RECT_KEY = "Crop Rectangle";
        const CROP_POLY_KEY = "Crop Polygon";
        const GPS_MARKER_KEY = "Set Latitude & Longitude";

        // Completely random coordinates to start the map on (should never be seen here)!
        const initialLat = -32;
        const initialLng = 151;
        const siteInfoZoomLevel = 18;
        const aerialMapZoomLevel = 21;

        /// Actual Map Objects
        var aerialMap = {
            map: null,
            geoOverlay: null,
            markerLayer: null,
            bigImage: null,
            elem: 'map_element',
            div: 'map-div',
            imageBuffer: null,
            isBusy: false
        }
        vm.aerialMap = aerialMap;

        var siteInfoMap = {
            map: null,
            geoOverlay: null,
            markerLayer: null,
            bigImage: null,
            elem: 'siteinfo_map',
            div: 'siteinfo_div',
            imageBuffer: null,
            isBusy: false
        }
        vm.siteInfoMap = siteInfoMap;

        let marker; // Marker that is re-used to indicate the lat/lng coords

        // ! Actual initialization of services.
        function initializeMapAndRelated(mapObj, mapType) {

            const { success, rMap, rOverlay, rMarkerLayer } = metromapservice
                .initializeMap(mapObj.elem, { lat: initialLat, lng: initialLng }, mapType);

            if (!success) {
                // The VERY FIRST time the user open the assessment page (per-session) there
                // can be problems initializing the map. In case of failure, retry.
                setTimeout(() => { initializeMapAndRelated(mapObj, mapType); }, MAP_INITIALIZATION_TIMEOUT);
                return;
            }

            mapObj.map = rMap;
            mapObj.geoOverlay = rOverlay;
            mapObj.markerLayer = rMarkerLayer;

            metromapservice.initializeGeoman(mapObj);

            mapObj.bigImage = metromapservice.initializeBigImage(mapObj.map);

            metromapservice.addOnCreateCallback(mapObj.map, async function (e) {

                if (e.shape == CROP_RECT_KEY || e.shape == CROP_POLY_KEY) {

                    // We assign an ID for the crop function which can be used later
                    // to ignore this layer during render and so on.
                    e.layer.id = "CROP_BOUNDARY";

                    metromapservice.takePartialSnapshot(mapObj.bigImage, 1, e.layer).then((data) => {

                        mapObj.imageBuffer = data;

                        // We reset to includeInPdf = FALSE every time we take a new image if we have entered the address manually
                        // or if the coordinates aren't confirmed. User can always re-check if desired.
                        if (vm.assessment.assessmentProjectDetail.isManual && !vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed) {
                            vm.assessment.includeMapInExportedPDF = false;

                        }

                        // Remove crop layers from the map so they don't show up again if we
                        // discard the image and crop again.
                        mapObj.map.removeLayer(e.layer);
                    });
                }
                else if (e.shape == GPS_MARKER_KEY) {

                    vm.siteInfoMap.isBusy = true;
                    vm.aerialMap.isBusy = true;

                    mapObj.map.pm.disableDraw('Marker');

                    e.layer.id = metromapservice.MARKER_KEYS.GPS;
                    metromapservice.clearExistingMarkers(vm.siteInfoMap);
                    metromapservice.clearExistingMarkers(vm.aerialMap);

                    let clickedLatLng = { lat: e.marker._latlng.lat, lng: e.marker._latlng.lng };
                    var addr = await addressservice.reverseGeocode(e.marker._latlng.lat, e.marker._latlng.lng);
                    e.marker.remove(); // Remove the manually placed marker from the map.

                    if (addr != null) {

                        try {

                            const lotDesc = vm.assessment.assessmentProjectDetail?.lotType?.description || 'Lot';
                            const addrStart = addr.lotNumber
                                ? `${lotDesc} ${addr.lotNumber} (#${addr.streetNumber})`
                                : `${addr.streetNumber}`;

                            let modalScope = $rootScope.$new();

                            modalScope.newAddress = `${addrStart}, ${addr.street} ${addr.streetType}, ${addr.suburb}`;

                            let action = await $mdDialog.show({
                                templateUrl: 'app/ui/assessment/reverse-geocode-modal/reverse-geocode-modal.html',
                                scope: modalScope
                            });

                            // console.log("Returned action was: ", action);

                            // Retreive additional info via our custom SLIP dataset.
                            let remainder = await slipaddress.getRemainder(addr);

                            // Combine our data and insert
                            let fullPayload = common.nullAwareMerge(addr, remainder);

                            if(action === 'all') {
                                vm.assessment.assessmentProjectDetail.isManual = false;
                                addressservice.insertAddressDataIntoObject(fullPayload, vm.assessment, "ASSESSMENT");
                                vm.addressChanged();
                            } else if(action === 'gps') {
                                fullPayload.latitude = clickedLatLng.lat;
                                fullPayload.longitude = clickedLatLng.lng;
                                addressservice.insertAddressDataIntoObject(fullPayload, vm.assessment, "GPS ONLY");
                            }

                            // Note we don't capture the map image just yet.
                            vm.confirmCoordinates(false, false);

                            checkIfBushFireProne();
                            getClimateZoneCode();

                            vm.resetMapToDefault(vm.aerialMap);
                            metromapservice.clearPolygonLayers(vm.siteInfoMap.map);
                            metromapservice.drawBoundary(vm.siteInfoMap.map, vm.assessment.assessmentProjectDetail.boundaryGeometry);

                            vm.resetMapToDefault(vm.siteInfoMap);
                            metromapservice.clearPolygonLayers(vm.aerialMap.map);
                            metromapservice.drawBoundary(vm.aerialMap.map, vm.assessment.assessmentProjectDetail.boundaryGeometry);

                            await captureMapImagesAtCurrentAddress();

                            metromapservice.clearExistingMarkers(vm.siteInfoMap);
                            metromapservice.clearExistingMarkers(vm.aerialMap);

                            updateMarker(vm.siteInfoMap, fullPayload.latitude, fullPayload.longitude); // NEW

                            vm.siteInfoMap.isBusy = false;
                            vm.aerialMap.isBusy = false;

                        } catch (e) {
                            // User cancelled modal so 'ok' should still be null.
                            vm.siteInfoMap.isBusy = false;
                            vm.aerialMap.isBusy = false;
                        }
                    }
                }
            })
        }

        // We set a small delay otherwise the DOM may not be constructed by the
        // point at which the map initializes.
        setTimeout(() => {
            initializeMapAndRelated(aerialMap, 'metro');
            initializeMapAndRelated(siteInfoMap, 'street');
        }, MAP_INITIALIZATION_TIMEOUT);

        /** Moves the map so that the center of the map is on the given coordinates */
        function moveMapTo(mapObj, lat, lng, zoom) {

            if(lat == null || lng == null)
                return;

            mapObj.map.setView([lat, lng], zoom);
            updateMarker(mapObj, lat, lng);
        }

        function updateMarker(mapObj, lat, lng) {

            if (marker != null) {
                mapObj.map.removeLayer(marker);
            }

            if(lat == null || lng == null)
                return;

            marker = L.marker([lat, lng], { fillColor: 'green' }).addTo(mapObj.markerLayer);
            marker.bindPopup(`${lat}, ${lng}`);
        }

        vm.assessmentStateBackup = null;
        vm.assessmentProjectDetailBackup = null;
        vm.siteMapImageFileBackup = null;
        vm.mapImageFileBackup = null;
        vm.siteMapBufferBackup = null;
        vm.aerialMapBufferBackup = null;

        /** Used in combination with 'resetToPrePanState' */
        vm.saveCurrentAddressAndMapState = function () {

            // This might be overkill...
            vm.assessmentStateBackup = angular.copy(vm.assessment);
            vm.assessmentProjectDetailBackup = angular.copy(vm.assessment.assessmentProjectDetail);

            vm.siteMapImageFileBackup = angular.copy(vm.assessment.assessmentProjectDetail.siteMapImageFile);
            vm.mapImageFileBackup = angular.copy(vm.assessment.assessmentProjectDetail.mapImageFile);

            vm.siteMapBufferBackup = angular.copy(vm.siteInfoMap.imageBuffer);
            vm.aerialMapBufferBackup = angular.copy(vm.aerialMap.imageBuffer);
        }

        /** Reset data and both maps to the state they were in BEFORE the user discarded the map and started panning around etc */
        vm.resetToPrePanState = function () {

            // Probably safer to only select what we need here (ffs);
            vm.assessment.natHERSClimateZoneCode = vm.assessmentStateBackup.natHERSClimateZoneCode;
            vm.assessment.assessmentProjectDetail.latitude = vm.assessmentProjectDetailBackup.latitude;
            vm.assessment.assessmentProjectDetail.longitude = vm.assessmentProjectDetailBackup.longitude;
            vm.assessment.assessmentProjectDetail.localGovernmentAuthority = vm.assessmentProjectDetailBackup.localGovernmentAuthority;
            vm.assessment.assessmentProjectDetail.lotNumber = vm.assessmentProjectDetailBackup.lotNumber;

            vm.assessment.assessmentProjectDetail.suburb = vm.assessmentProjectDetailBackup.suburb;
            vm.assessment.assessmentProjectDetail.stateCode = vm.assessmentProjectDetailBackup.state;
            vm.assessment.assessmentProjectDetail.postcode = vm.assessmentProjectDetailBackup.postCode;
            vm.assessment.assessmentProjectDetail.streetName = vm.assessmentProjectDetailBackup.streetName;
            vm.assessment.assessmentProjectDetail.houseNumber = vm.assessmentProjectDetailBackup.houseNumber;

            vm.assessment.assessmentProjectDetail.coordinatesEditable = vm.assessmentProjectDetailBackup.coordinatesEditable;
            vm.assessment.assessmentProjectDetail.coordinatesState = vm.assessmentProjectDetailBackup.coordinatesState;

            vm.assessment.assessmentProjectDetail.planType = vm.assessmentProjectDetailBackup.planType;
            vm.assessment.assessmentProjectDetail.depositedPlanNumber = vm.assessmentProjectDetailBackup.depositedPlanNumber;
            vm.assessment.assessmentProjectDetail.certificateOfTitle = vm.assessmentProjectDetailBackup.certificateOfTitle;
            vm.assessment.assessmentProjectDetail.parcelArea = vm.assessmentProjectDetailBackup.parcelArea;

            vm.assessment.assessmentProjectDetail.lotNumber = vm.assessmentProjectDetailBackup.lotNumber;
            vm.assessment.assessmentProjectDetail.boundaryGeometry = vm.assessmentProjectDetailBackup.boundaryGeometry;

            vm.addressChanged();

            // Reset/recapture maps..
            vm.releaseMapImage(vm.aerialMap, "mapImageFile", 19);
            vm.releaseMapImage(vm.siteInfoMap, "siteMapImageFile", 19);

            setTimeout(function () {

                vm.resetMapToDefault(vm.aerialMap);
                vm.resetMapToDefault(vm.siteInfoMap);

                // These may be null so we still attempt to re-capture afterwards (redundant but w/e)
                if (vm.mapImageFileBackup)
                    vm.assessment.assessmentProjectDetail.mapImageFile = vm.mapImageFileBackup

                if (vm.siteMapImageFileBackup)
                    vm.assessment.assessmentProjectDetail.siteMapImageFile = vm.siteMapImageFileBackup;

                vm.siteInfoMap.imageBuffer = vm.siteMapBufferBackup;
                vm.aerialMap.imageBuffer = vm.aerialMapBufferBackup;

                vm.assessmentStateBackup = null;
                vm.assessmentProjectDetailBackup = null;
            }, 200);

        }

        /** Releases (i.e. deletes) any stored Map imagery. and resets back to given zoom level */
        vm.releaseMapImage = function (mapObj, property, zoom) {

            mapObj.imageBuffer = null;
            document.getElementById(mapObj.elem).hidden = false;
            document.getElementById(mapObj.div).hidden = false;

            vm.assessment.assessmentProjectDetail[property + "Id"] = null;
            vm.assessment.assessmentProjectDetail[property] = null;

            // Update cached values after releasing image
            vm.updateMapImageCachedValues();

            if (zoom != null) {
                setTimeout(function () {
                    mapObj.map.invalidateSize(); // Always invalidate size mate always
                    moveMapTo(mapObj, vm.assessment.assessmentProjectDetail.latitude,
                        vm.assessment.assessmentProjectDetail.longitude,
                        zoom);
                }, 100);
            }

        }

        // Cache variables to store results of map image functions
        vm.cachedHasAerialMapImageData = false;
        vm.cachedHasSiteMapImageData = false;
        vm.cachedMapImagesNotCaptured = "";

        // Cache variables to store error messages
        vm.cachedBuildingSiteFormErrors = "";
        vm.cachedAddressFormErrors = "";

        // Cache variables for general tab errors
        vm.cachedBuildingGeneralTabHasErrors = "";
        vm.cachedBuildingGeneralOptionErrors = {};

        vm.hasMapImageData = function (mapObj, dtoImgFile) {
            if (vm.assessment != null && vm.assessment.assessmentProjectDetail != null) {

                const gotData = !(mapObj.imageBuffer == null &&
                             vm.assessment?.assessmentProjectDetail[dtoImgFile] == null);
                return gotData;

            } else {
                return null;
            }
        }

        // Function to update cached map image values
        vm.updateMapImageCachedValues = function() {
            if (vm.assessment != null && vm.assessment.assessmentProjectDetail != null) {
                vm.cachedHasAerialMapImageData = vm.hasMapImageData(vm.aerialMap, 'mapImageFile');
                vm.cachedHasSiteMapImageData = vm.hasMapImageData(vm.siteInfoMap, 'siteMapImageFile');
                vm.cachedMapImagesNotCaptured = vm.mapImagesNotCaptured();
            }
        }

        // Function to update cached error messages
        vm.updateCachedErrorMessages = function() {
            if ($scope.assessmentform && $scope.assessmentform.buildingSiteForm) {
                vm.cachedBuildingSiteFormErrors = vm.userFriendlyErrorMessage($scope.assessmentform.buildingSiteForm.$error);

                if ($scope.assessmentform.buildingSiteForm.addressForm) {
                    vm.cachedAddressFormErrors = vm.userFriendlyErrorMessage($scope.assessmentform.buildingSiteForm.addressForm.$error);
                }
            }

            // Update general tab error caches
            vm.updateCachedGeneralTabErrors();
        }

        // Function to update cached general tab errors
        vm.updateCachedGeneralTabErrors = function() {
            // Update the general tab errors
            if (vm.assessment?.allComplianceOptions) {
                // Update the main tab error message
                vm.cachedBuildingGeneralTabHasErrors = vm.buildingGeneralTabHasErrors();

                // Update the option-specific error messages
                vm.cachedBuildingGeneralOptionErrors = {};

                for (let i = 0; i < vm.assessment.allComplianceOptions.length; i++) {
                    const opt = vm.assessment.allComplianceOptions[i];
                    const optionKey = opt.optionIndex;

                    if (!vm.cachedBuildingGeneralOptionErrors[optionKey]) {
                        vm.cachedBuildingGeneralOptionErrors[optionKey] = {};
                    }

                    // Cache proposed building errors
                    vm.cachedBuildingGeneralOptionErrors[optionKey]['proposed'] =
                        vm.buildingGeneralOptionErrors(opt, 'proposed');

                    // Cache reference building errors if needed
                    if (referenceIsRequired(opt)) {
                        vm.cachedBuildingGeneralOptionErrors[optionKey]['reference'] =
                            vm.buildingGeneralOptionErrors(opt, 'reference');
                    } else {
                        vm.cachedBuildingGeneralOptionErrors[optionKey]['reference'] = "";
                    }
                }
            }
        }

        /**
         * Erases all drawn geometry and adds the default boundary only (if any) and then
         * zooms to the correct spot.
         */
        vm.resetMapToDefault = function (mapObj) {

            metromapservice.clearExistingMarkers(mapObj);

            metromapservice.clearPolygonLayers(mapObj.map);
            metromapservice.drawBoundary(mapObj.map, vm.assessment.assessmentProjectDetail.boundaryGeometry);

            if(mapObj.map === vm.aerialMap.map) {

                // // Get super close
                var boundary = vm.assessment.assessmentProjectDetail.boundaryGeometry;

                if(boundary != null)
                    metromapservice.zoomToBoundary(mapObj.map, boundary);

                updateMarker(mapObj,
                    vm.assessment.assessmentProjectDetail.latitude,
                    vm.assessment.assessmentProjectDetail.longitude);

            } else if(mapObj.map === vm.siteInfoMap.map) {

                // And now move to a default high-level zoom on the site info map
                moveMapTo(mapObj,
                    vm.assessment.assessmentProjectDetail.latitude,
                    vm.assessment.assessmentProjectDetail.longitude,
                    siteInfoZoomLevel);

            }
        }

        /** Captures the current view AS IS visible from the UI including any drawn polygons. */
        vm.captureCurrentView = function (mapObj) {

            vm.assessmentStateBackup = null;
            vm.assessmentProjectDetailBackup = null;

            mapObj.isBusy = true;

            metromapservice.takePartialSnapshot(mapObj.bigImage, 1, null).then((data) => {

                mapObj.imageBuffer = data;

                // We reset to includeInPdf = FALSE every time we take a new image if we have entered the address manually
                // or if the coordinates aren't confirmed. User can always re-check if desired.
                if (vm.assessment.assessmentProjectDetail.isManual && !vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed) {
                    vm.assessment.includeMapInExportedPDF = false;

                }

                mapObj.isBusy = false;

                // Update cached values after capturing image
                vm.updateMapImageCachedValues();
            });

        }

        // END METRO MAP STUFF
        // -------------------------------------------------------------------------------------------------------------------------------------------

        vm.getMainNcc2022Tables = function (option) {
            return option.ncc2022?.filter(t => t.flippedFromId == null);
        }

        vm.getOriginalAndFlippedTable = function (option, table) {
            let list = [table];
            let flipped = option.ncc2022.find(t => t.flippedFromId == table.id);
            if (flipped != null) { list.push(flipped); }
            return list;
        }

        function getDefaultNcc2022() {
            return {
                id: uuid4.generate(),
                isActive: true,
                natHersClimateZone: null,
                sortByAlpha: false,
                ncc2022OffsetList: Array(8).fill().map((_, index) => { return {
                    northOffset: index*45,
                    her: null,
                    heating: null,
                    cooling: null,
                    total: null
                }})
            }
        }

        vm.addNcc2022 = function (option) {
            if (option.ncc2022 == null) {
                option.ncc2022 = [];
            }
            let newTable = getDefaultNcc2022();
            let certs = vm.assessmentMethodOptionsForThisTable(option, newTable);
            newTable.certification = vm.certificationOptionsForThisTable(option, newTable).find(c => c.certificationId == vm.assessment.job.client.clientDefault.certificationId)?.title;
            newTable.assessmentMethod = vm.assessmentMethodOptionsForThisTable(option, newTable).find(c => c.complianceMethodCode == vm.assessment.job.client.clientDefault.preliminaryComplianceMethodCode)?.description;
            option.ncc2022.push(newTable);
            ncc2022Initialise();
        }

        vm.removeNcc2022 = function (option, table) {
            // Remove this table and any "flipped" attached to it
            option.ncc2022 = option.ncc2022.filter(t => t.id != table.id && t.flippedFromId != table.id);
        }

        vm.toggleNcc2022Active = function (option, table) {
            // IF this is an 'original', set it's 'flipped' to the same value
            if (table.flippedFromId == null) {
                let flipped = option.ncc2022.find(t => t.flippedFromId == table.id);
                if (flipped) {
                    flipped.isActive = table.isActive;
                }
            }
        }

        vm.ncc2022OriginalIsFlipped = function (option, table) {
            // IF this is a 'flipped', return whether it's 'original' has "Active" toggled
            if (table.flippedFromId != null) {
                return option.ncc2022.find(t => t.id == table.flippedFromId).isActive;
            } else {
                return true;
            }
        }

        vm.toggleNcc2022Flip = function (option, table) {
            // IF has flip, remove
            if (option.ncc2022.some(t => t.flippedFromId == table.id)) {
                option.ncc2022 = option.ncc2022.filter(t => t.flippedFromId != table.id);
            // ELSE add flip
            } else {
                option.ncc2022.push({
                    ...getDefaultNcc2022(),
                    sortByAlpha: table.sortByAlpha,
                    flippedFromId: table.id,
                    certification: angular.copy(table.certification),
                    assessmentMethod: angular.copy(table.assessmentMethod),
                    natHersClimateZone: angular.copy(table.natHersClimateZone)
                });
                ncc2022Initialise(); // Make sure "onpaste" sets on new 'flipped' div
            }
        }

        vm.ncc2022FieldChanged = function (option, table, field) {
            // Make sure this table is not the flipped one
            if (table.flippedFromId == null) {
                let flipped = option.ncc2022.find(t => t.flippedFromId == table.id);
                if (flipped != null) {
                    flipped[field] = table[field];
                }
            }
        }

        vm.toggleNcc2022Sort = function(option, table) {
            if (table.flippedFromId == null) {
                table.sortByAlpha = !table.sortByAlpha;
                table.ncc2022OffsetList.sort((a,b) => {
                    if (table.sortByAlpha) {
                        return a.northOffset.toString() > b.northOffset.toString() ? 1 : -1;
                    } else {
                        return a.northOffset > b.northOffset ? 1 : -1;
                    }
                });
                let flipped = option.ncc2022.find(t => t.flippedFromId == table.id);
                if (flipped != null) {
                    flipped.sortByAlpha = table.sortByAlpha;
                    flipped.ncc2022OffsetList.sort((a,b) => {
                        if (flipped.sortByAlpha) {
                            return a.northOffset.toString() > b.northOffset.toString() ? 1 : -1;
                        } else {
                            return a.northOffset > b.northOffset ? 1 : -1;
                        }
                    });
                }
            }
        }

        if (vm.newRecord) {

            vm.title = "New Assessment";
            if (vm.jobId != undefined && vm.jobId != null && vm.jobId != "") {
                vm.assessment.jobId = vm.jobId;
            }
        } else {
            vm.assessmentId = $stateParams.assessmentId;
        }

        if (vm.isModal) {
            vm.hideActionBar = true;
        }
        // Get data for object to display on page
        var assessmentIdPromise = null;
        var clientDeferred = null;
        var clientPromise = null;
        var jobDeferred = $q.defer();

        //resved when either assessment is loaded or when sequential guid is loaded for new assessment
        var assessmentDeferred = $q.defer();

        function ncc2022Initialise() {
            setTimeout(() => {
                vm.assessment.allComplianceOptions.forEach(option => {
                    if (option.ncc2022 == null) { option.ncc2022 = []; }
                    option.ncc2022.forEach(o => {
                        if (option.ncc2022.some(x => x.flippedFromId == o.id)) {
                            o.hasFlip = true;
                        }
                    });
                });
                vm.assessment.allComplianceOptions.forEach(option => {
                    option.ncc2022?.forEach(climateZoneTable => {
                        if (climateZoneTable.ncc2022OffsetList.some(t => t.northOffset == null) || climateZoneTable.ncc2022OffsetList.every(t => t.northOffset == 0)) {
                            climateZoneTable.ncc2022OffsetList.forEach((row, index) => {
                                row.northOffset = index*45;
                            });
                        }
                        document.getElementById(`ncc2022Table-${option.optionIndex}-${climateZoneTable.id}`).onpaste = (event) => {
                            if (vm.permission_tab_multisim_edit) {
                                for (let i = 0; i < event.clipboardData.items.length; i++) {
                                    let clipboardItem = event.clipboardData.items[i];
                                    if (clipboardItem.type == 'text/plain') {
                                        clipboardItem.getAsString(text => {
                                            let rows = text.split('\r\n').map(row => row.split('\t').filter(x => x.length > 0)).filter(x => x.length > 0);
                                            // Ignore if not in right format
                                            if (rows.length != 8 || rows.some(row => row.length != 4))
                                                return;

                                            // Store original values before paste
                                            const originalValues = climateZoneTable.ncc2022OffsetList.map(row => ({
                                                northOffset: row.northOffset,
                                                her: row.her,
                                                heating: row.heating,
                                                cooling: row.cooling,
                                                total: row.total
                                            }));

                                            // Apply pasted values
                                            rows.forEach((pasteRow, rowIndex) => {
                                                let nccRow = climateZoneTable.ncc2022OffsetList.find(r => r.northOffset == rowIndex*45);
                                                pasteRow.forEach((pasteVal, colIndex) => {
                                                    switch (colIndex) {
                                                        case 0: nccRow.her     = pasteVal; break;
                                                        case 1: nccRow.heating = pasteVal; break;
                                                        case 2: nccRow.cooling = pasteVal; break;
                                                        case 3: nccRow.total   = pasteVal; break;
                                                    }
                                                });
                                            });

                                            // Validate House Energy Rating values
                                            let invalidHerValues = [];
                                            climateZoneTable.ncc2022OffsetList.forEach((row, index) => {
                                                if (row.her !== null && row.her !== undefined) {
                                                    const numValue = parseFloat(row.her);
                                                    if (isNaN(numValue) || numValue < 0 || numValue > 10) {
                                                        invalidHerValues.push(index);
                                                        // Restore original value
                                                        const originalRow = originalValues.find(r => r.northOffset === row.northOffset);
                                                        row.her = originalRow.her;
                                                    }
                                                }
                                            });

                                            // Show warning if any invalid values were found
                                            if (invalidHerValues.length > 0) {
                                                modalDialog.infoDialog(
                                                    "Invalid House Energy Rating",
                                                    "House Energy Rating must be between 0.0 and 10.0 inclusive. Invalid values have been reverted to their previous values.",
                                                    "",
                                                    "OK"
                                                );
                                            }
                                        });
                                    }
                                }
                            }
                        }
                    });
                });
            }, 50);
        }

        vm.certificationOptionsForThisTable = function (option, thisTable) {
            if (thisTable.flippedFromId != null) {
                return vm.certificationList;
            } else {
                let otherTables = option.ncc2022.filter(table => table.id != thisTable.id);
                return vm.certificationList.filter(certification => !otherTables.filter(t => t.flippedFromId == null).some(table =>
                    table.certification != null && table.certification == certification.title
                    && table.assessmentMethod != null && table.assessmentMethod == thisTable.assessmentMethod
                    && table.natHersClimateZone != null && table.natHersClimateZone == thisTable.natHersClimateZone
                ));
            }
        }

        vm.assessmentMethodOptionsForThisTable = function (option, thisTable) {
            if (thisTable.flippedFromId != null) {
                return vm.availableComplianceMethods();
            } else {
                let otherTables = option.ncc2022.filter(table => table.id != thisTable.id);
                return vm.availableComplianceMethods().filter(method => !otherTables.filter(t => t.flippedFromId == null).some(table =>
                    table.certification != null && table.certification == thisTable.certification
                    && table.assessmentMethod != null && table.assessmentMethod == method.description
                    && table.natHersClimateZone != null && table.natHersClimateZone == thisTable.natHersClimateZone
                ));
            }
        }

        vm.natHersOptionsForThisTable = function (option, thisTable) {
            if (thisTable.flippedFromId != null) {
                return vm.natHERSClimateZoneList;
            } else {
                let otherTables = option.ncc2022.filter(table => table.id != thisTable.id);
                return vm.natHERSClimateZoneList.filter(zone => !otherTables.filter(t => t.flippedFromId == null).some(table =>
                    table.certification == thisTable.certification
                    && table.assessmentMethod == thisTable.assessmentMethod
                    && table.natHersClimateZone == zone.description
                ));
            }
        }

        if (vm.assessmentId != null) {
            assessmentIdPromise = assessmentservice.getAssessment(vm.assessmentId)
            .then(function (data) {
                if (data != null) {
                    vm.assessment = data;

                    Globals.currentAssessment = data;
                    Globals.currentServicesOption = null;

                    if (vm.assessment && vm.assessment.assessmentProjectDetail && vm.assessment.assessmentProjectDetail.suburb) {
                        skipStateClear = true;
                    }
                    vm.jobId = vm.assessment.jobId;
                    if (vm.assessment.job != undefined && vm.assessment.job != null) {
                        vm.title = "Edit Assessment";

                    }

                    vm.minHouseEnergyRating = vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating?.toFixed(1);

                    // This will not run for templates.
                    if (vm.assessment && vm.assessment.assessmentProjectDetail) {

                        // If Alistair ever wants per-compliance option # of floors,
                        // this will have to change.
                        if (vm.assessment.allComplianceOptions[0].proposed?.storeys != null)
                            vm.floorsWithGlazing = vm.assessment.allComplianceOptions[0].proposed.storeys.length;
                        else
                            vm.floorsWithGlazing = 0;

                        vm.addressChanged();
                        vm.switchToDecimalInput(); // Default to Decimal input.

                        // Small delay to ensure DOM and Map has been initialized.
                        setTimeout(function () {

                            // Draw any boundary geometry data and zoom if it exists, otherwise set default zoom.
                            if(vm.assessment.assessmentProjectDetail.mapImageFileId == null)
                                metromapservice.drawBoundaryAndZoomIfExists(aerialMap.map,
                                    {
                                        lat: vm.assessment.assessmentProjectDetail.latitude,
                                        lng: vm.assessment.assessmentProjectDetail.longitude
                                    },
                                    vm.assessment.assessmentProjectDetail.boundaryGeometry,
                                    true, aerialMapZoomLevel);

                            // Draw any boundary geometry data and zoom if it exists, otherwise set default zoom.
                            if(vm.assessment.assessmentProjectDetail.siteMapImageFileId == null)
                                metromapservice.drawBoundaryAndZoomIfExists(siteInfoMap.map,
                                    {
                                        lat: vm.assessment.assessmentProjectDetail.latitude,
                                        lng: vm.assessment.assessmentProjectDetail.longitude
                                    },
                                    vm.assessment.assessmentProjectDetail.boundaryGeometry,
                                    false, siteInfoZoomLevel);

                            // We only care about updating the marker for the more zoomed-out
                            // siteinfo map as it's hard to see just the boundary.
                            updateMarker(
                                siteInfoMap,
                                vm.assessment.assessmentProjectDetail.latitude,
                                vm.assessment.assessmentProjectDetail.longitude
                            );

                        }, MAP_INITIALIZATION_TIMEOUT + 50);
                    }

                    vm.natHERSClimateZoneList = [];
                    nathersclimatezoneservice.getList().then(data => vm.natHERSClimateZoneList = data.data);

                    ncc2022Initialise();

                    vm.setPermissions();

                    vm.isBusy = false;
                    setIsLocked();

                    // Update cached map image values after assessment is loaded
                    vm.updateMapImageCachedValues();

                    jobDeferred.resolve();
                }
                else {
                    vm.isBusy = false;
                }
                assessmentDeferred.resolve();
            });
        }
        else {

            sequentialguidservice.getGuid().then(data => {
                vm.assessment.assessmentId = data;
                vm.assessmentId = data;
                //set default p2.6.2
                vm.assessment.performanceRequirementP262Code = "P262Satisfied";
                //set default compliance status
                vm.assessment.complianceStatusCode = "CSNotAchieved";

                if (vm.jobId != null) {
                    jobservice.getJob(vm.jobId).then(data => {
                        vm.assessment.job = data;
                        vm.isBusy = false;
                        setIsLocked();
                        jobDeferred.resolve();
                    });
                } else {
                    vm.isBusy = false;
                }

                assessmentDeferred.resolve();
            });
        }

        vm.disableAllActions = false;
        function setIsLocked() {

            const assignedToUser = vm.assessment.assessorUserId === securityservice.currentUser.managedUserId;

            if (vm.assessment.statusCode === 'ASuperseded' ||
                vm.assessment.statusCode === 'AIssued' ||
                vm.assessment.statusCode === 'APreliminaryReview' ||
                vm.assessment.statusCode === 'AComplete' ||
                vm.assessment.statusCode === 'ACompliance' ||
                vm.assessment.statusCode === 'AOptionSelected' ||
                (vm.assessment.job && vm.assessment.job.statusCode === 'JCancelled')) {
                vm.isLocked = true;
            }
            else if(!securityservice.immediateCheckRoles('assessment_actions__editassessment'))
                vm.isLocked = true;
            else {
                vm.isLocked = false;
            }

            disableActionButtons();
        }

        vm.jobFiles = [];
        jobDeferred.promise.then(function () {
            getJobFileList();
        });

        function getJobFileList() {
            if (vm.assessment.jobId != null && vm.assessment.jobId != undefined && vm.assessment.jobId != "") {
                var filter = [{ field: "JobId", operator: "eq", value: vm.assessment.jobId, valueType: "guid?" }]
                fileservice.getList(null, null, null, null, null, null, filter).then(function (data) {
                    if (data.data != null) {
                        vm.jobFiles = data.data;
                    }
                });
            }
        }

        // Get data for any dropdown lists
        vm.statusList = [];
        var statusPromise = statusservice.getList()
            .then(function(data){
                vm.statusList = data.data;
            });
        vm.assessmentSoftwareList = [];
        var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
            .then(function(data){
                vm.assessmentSoftwareList = data;
                vm.allAssessmentSoftwareList = data;
            });

        vm.buildingExposureList = [];
        var buildingExposurePromise = buildingexposureservice.getAll()
            .then(function(data){
                vm.buildingExposureList = data;
            });
        vm.complianceStatusList = [];
        var complianceStatusPromise = compliancestatusservice.getList()
            .then(function(data){
                vm.complianceStatusList = data.data;
            });
        vm.complianceMethodList = [];
        var complianceMethodPromise = compliancemethodservice.getList()
            .then(function (data) {
                vm.complianceMethodList = data.data;
                vm.allComplianceMethodList = data.data;
                vm.complianceOptionsComplianceMethodList = angular.copy(data.data);
            });

        vm.nccClimateZoneList = [];
        let nccClimateZonePromise = nccclimatezoneservice.getList()
            .then(function(data){
                vm.nccClimateZoneList = data.data;
            });

        vm.bushfireAttackLevelList = [];
        var bushfireAttackLevelPromise = bushfireattacklevelservice.getList()
            .then(function (data) {
                vm.bushfireAttackLevelList = data.data;
            });

        vm.lotTypeList = [];
        var lotTypePromise = lottypeservice.getList()
            .then(function (data) {
                vm.lotTypeList = data.data;
            });
        vm.stateList = [];
        var statePromise = stateservice.getList()
            .then(function (data) {
                vm.stateList = data.data;
            });
        vm.priorityList = [];
        var priorityPromise = priorityservice.getList()
            .then(function (data) {
                vm.priorityList = data.data;
            });

        vm.buildingSurveyorList = [];
        var buildingSurveyorPromise = nominatedbuildingsurveyorservice.getList()
            .then(function (data) {
                vm.allBuildingSurveyorList = data;
                vm.buildingSurveyorList = vm.allBuildingSurveyorList;
            });

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        vm.constructionCategoryList = []
        var constructionCategoryPromise = constructionservice.getConstructionCategoryList()
            .then((data) => {
                vm.constructionCategoryList = data;
                vm.constructionTabCategories = constructionservice.constructionCategories();
                vm.openingTabCategories = constructionservice.openingCategories();
        });

        vm.serviceCategoryList = [];
        servicetemplateservice.getServiceCategories()
            .then((data) => {
                vm.serviceCategoryList = data;

            });

        vm.designChangeList = [];
        var designChangePromise = designchangeservice.getList()
            .then(function (data) {
                vm.designChangeList = data.data;
            });

        vm.allWorksDescriptionList = [];
        vm.worksDescriptionList = [];
        let worksDescriptionPromise = worksdescriptionservice.getList()
            .then(data => { vm.worksDescriptionList = vm.allWorksDescriptionList = data; })

        // Wait for the required dropdown lists to set so the client options can filter them
        $q.all([
            assessmentDeferred.promise,
            complianceMethodPromise,
            assessmentSoftwarePromise,
            clientPromise,
            buildingSurveyorPromise,
            certificationPromise,
            worksDescriptionPromise])
            .then(function () {

                if (vm.assessment.job.client.clientOptions)
                    restrictAvailableOptions(vm.assessment.job.client.clientOptions);
        });

        //set min house energy rating when loading into page
        assessmentDeferred.promise.then(function () {
            if (vm.assessment.assessmentProjectDetail && vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating) {
                vm.minHouseEnergyRating = vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating.toFixed(1);
            }
            if (vm.assessment.proposedHouseEnergyRating != undefined || vm.assessment.proposedHouseEnergyRating != null) {
                var description = null;
                if (Number(vm.assessment.proposedHouseEnergyRating) >= 0) {
                    for (var ii = 6; ii < 10; ii+=0.5) {
                        if (Number(vm.assessment.proposedHouseEnergyRating) == ii) {
                            description = Number(vm.assessment.proposedHouseEnergyRating).toFixed(1).toString();
                            break;
                        }
                    }
                    description = "Other";
                }
                if (description != null) {
                    vm.minHouseEnergyRatingObject = { val: Number(vm.assessment.proposedHouseEnergyRating), description: description };
                }
                else {
                    vm.minHouseEnergyRatingObject = null;
                }
            }
            if (vm.assessment.referenceHouseEnergyRating != undefined && vm.assessment.referenceHouseEnergyRating != null) {
                var description = null;
                if (Number(vm.assessment.referenceHouseEnergyRating) >= 0) {
                    for (var ii = 6.0; ii < 10; ii += 0.5) {
                        if (Number(vm.assessment.referenceHouseEnergyRating) == ii) {
                            description = Number(vm.assessment.referenceHouseEnergyRating).toFixed(1).toString();
                        }
                    }
                    description = "Other";
                }
                vm.referenceHouseEnergyRatingObject = {val: Number(vm.assessment.referenceHouseEnergyRating), description: description};
            }
            vm.validatePreliminaryComplianceMethodChange();
        });

        vm.updateAvailableSoftware = function () {
            var currentSoftwareCode = vm.assessment.assessmentSoftwareCode;
            var currentComplianceCode = vm.assessment.assessmentProjectDetail != null
                ? vm.assessment.allComplianceOptions[0].complianceMethodCode
                : vm.template.preliminaryComplianceMethodCode;

            for (var ii = 0; ii < vm.assessmentSoftwareList.length; ii++) {
                var softwareFound = false;
                for (var jj = 0; jj < vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods.length; jj++) {
                    if (vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].complianceMethodCode == currentComplianceCode && vm.assessmentSoftwareList[ii].assessmentSoftwareComplianceMethods[jj].isAvailable) {
                        vm.assessmentSoftwareList[ii].isAvailable = true;
                        softwareFound = true;
                        break;
                    }
                }
                if (!softwareFound) {
                    vm.assessmentSoftwareList[ii].isAvailable = false;
                }

                if (currentSoftwareCode == vm.assessmentSoftwareList[ii].assessmentSoftwareCode && !vm.assessmentSoftwareList[ii].isAvailable) {
                    vm.assessment.assessmentSoftwareCode = null; // clear selected option because software is invalid.
                }
            }
        }

        $q.all([assessmentDeferred.promise, complianceMethodPromise, assessmentSoftwarePromise]).then(function () {
            vm.updateAvailableSoftware();
        });

        function showInvalidClientOptionsModal() {
            modalDialog.infoDialog("Invalid Assessment", null, "Values entered in the Assessment Information do not match the Client options selected.", "Ok", null);
        }

        assessmentDeferred.promise.then(function () {
            getAssessmentVersionDropdownList();
        });

        function getAssessmentVersionDropdownList() {
            if (vm.assessment.job) {
                if (securityservice.immediateCheckRoles(['assessment_actions__viewpreviousversions'])) {
                    assessmentservice.getNavigationAssessments(vm.assessment.jobId).then(function (data) {
                        vm.assessmentList = [];
                        for (var ii = 0; ii < data.length; ii++) {
                            var navAssessment = {
                                assessmentId: data[ii].assessmentId,
                                displayString: "Assessment " + data[ii].version + " (" + data[ii].statusDescription + ")"
                            };
                            vm.assessmentList.push(navAssessment);
                            if (vm.assessment.assessmentId == data[ii].assessmentId) {
                                vm.selectedAssessment = navAssessment;
                            }
                        }
                    });
                } else {
                    var navAssessment = {
                        assessmentId: vm.assessment.assessmentId,
                        displayString: "Assessment " + vm.assessment.assessmentProjectDetail.assessmentVersion + " (" + vm.assessment.status.description + ")"
                    };
                    vm.assessmentList = [navAssessment];
                    vm.selectedAssessment = navAssessment;
                }
            }
        }

        vm.minHouseEnergyRatingChanged = function (item) {

            if (item == null)
                return;

            // Handles our conversion between string/decimal (UGH)
            vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating = parseFloat(item);

            // Re-evaluate all compliance options that are based on HERS.
            vm.updateNatHERSData();
        }

        vm.clearMinHouseEnergyRating = function () {
            //Modify the value on either the Assessment or the Template
            var assessmentDetail = vm.assessment.assessmentProjectDetail;

            vm.assessment.allComplianceOptions[0].requiredHouseEnergyRating = null;
            vm.minHouseEnergyRating = null;
            vm.minHouseEnergyRatingSearchText = '';
        }

        vm.clientChanged = function () {
            vm.showprojectDescriptions = false;
            var clientId = vm.assessment.job?.client != null
                ? vm.assessment.job?.client.clientId
                : vm.clientId;

            if ((clientId == undefined || clientId == null) && (vm.assessment.assessmentProjectDetail && vm.assessment.assessmentProjectDetail.contact)) {
                clientId = vm.assessment.assessmentProjectDetail.creator.clientId
            }
            if ((clientId == undefined || clientId == null) && (vm.assessment.job && vm.assessment.job.client)){
                clientId = vm.assessment.job.client.clientId;
            }
            if (vm.assessment.job && vm.assessment.job.client && vm.assessment.job.client.clientOptions) {
                vm.showprojectDescriptions = true;

                restrictAvailableOptions(vm.assessment.job.client.clientOptions);
            } else if (_.isEmpty(vm.template)) {
                //Don't bother resetting anything if it's a template (client would not have changed)
                resetOptions();
            }
        }

        vm.getclients = function (searchTerm) {
            var filter = [{ field: "clientName", operator: "startswith", value: searchTerm }];
            return clientservice.getList(null, null, null, null, null, null, filter)
            .then(function (data) {
                return data.data;
            });
        }

        //Initial set up for options modifying the field display on the UI
        vm.complianceOptions = [];
        vm.qRCodeOption = [];

        /**
         * Restricts the available inputs based upon the clientOptions argument.
         * @param {any} options
         */
        function restrictAvailableOptions(options) {

            // Restrict building surveyors.
            vm.buildingSurveyorList = vm.allBuildingSurveyorList
                ?.filter(x => options.availableBuildingSurveyorIds?.includes(x.buildingSurveyorId));

            // Don't allow a state where we can't pick ANYTHING - will mean we
            // couldn't continue.
            if (vm.buildingSurveyorList == null || vm.buildingSurveyorList.length == 0)
                vm.buildingSurveyorList = vm.allBuildingSurveyorList;

            // Restrict Compliance Methods
            vm.complianceMethodList = vm.allComplianceMethodList
                ?.filter(x => options.availableComplianceMethodCodes?.includes(x.complianceMethodCode));

            // Restrict Compliance Methods
            vm.certificationList = vm.allCertificationList
                ?.filter(x => options.availableCertifications?.includes(x.certificationId));

            vm.assessmentSoftwareList = vm.allAssessmentSoftwareList;

            vm.worksDescriptionList = vm.allWorksDescriptionList
                ?.filter(x => options.availableWorksDescriptions?.includes(x.worksDescriptionCode));

            // Default selection and disable on UI
            vm.allowQRCodeChoice = options.isQRCodeAvailable;

            // Min house energy rating above doesn't work.
            vm.availableHouseEnergyRatings = options.availableHouseEnergyRatings;
            vm.availableHouseEnergyRatings?.sort((a, b) => a - b);

            // Convert to and from set to remove duplicates if present.
            vm.availableHouseEnergyRatings = [...new Set(vm.availableHouseEnergyRatings)];

            vm.clientOptionsConfigured = true;
        }

        //Note: Technically do not have to do, as selecting a client is mandatory and will set the below values.
        //However, it looks better on the eyes when all fields are shown and dropdown lists are reset when a client is removed
        function resetOptions() {
            //Reset options modifying the field display on the UI
            vm.complianceOptions = [];
            vm.qRCodeOption = [];

            //Reset dropdown lists
            vm.complianceMethodList = vm.allComplianceMethodList;
            vm.assessmentSoftwareList = vm.allAssessmentSoftwareList;
        }

        // Not currently in use
        vm.getSuburbsFromDb = function (searchTerm) {
            var filter = [{ field: "name", operator: "startswith", value: searchTerm }];
            return suburbservice.getList(null, null, null, null, null, null, filter)
            .then(function (data) {
                return data.data;
            });
        }

        var lastSuburbsResults = [];
        var lastStateCode = "";
        var lastSearchTerm = "";
        vm.getSuburbsFromService = function (searchTerm) {
            if (searchTerm == null || searchTerm == "" || searchTerm.length < 3) {
                lastSearchTerm = "";
                lastSuburbsResults = [];
                return [];
            }
            vm.assessment.assessmentProjectDetail.suburb = "";
            if (((lastStateCode == vm.assessment.assessmentProjectDetail.stateCode && lastSearchTerm != null && lastSearchTerm.length > 2 && lastSearchTerm.length <= searchTerm.length && lastSearchTerm == searchTerm.substring(0, lastSearchTerm.length) && lastSuburbsResults != null && lastSuburbsResults.length < 50))) {

                return _.filter(lastSuburbsResults, function (item) { return item.CustomFields.locality.toLowerCase().startsWith(searchTerm.toLowerCase(), 0) });
            }
            return mapdataservice.searchSuburbs(searchTerm, vm.assessment.assessmentProjectDetail.stateCode)
            .then(function (data) {
                lastSearchTerm = searchTerm;
                lastStateCode = vm.assessment.assessmentProjectDetail.stateCode;
                lastSuburbsResults = data.Results;
                return data.Results;
            });
        }

        vm.suburbBlur = function () {
            if (vm.assessment.assessmentProjectDetail.suburb == '') {
                vm.assessment.assessmentProjectDetail.suburb = vm.suburbSearchText;
            }
        }

        vm.streetBlur = function() {
            if (vm.assessment.assessmentProjectDetail.streetName == '') {
                vm.assessment.assessmentProjectDetail.streetName = vm.streetSearchText;
            }
        }

        var lastStreetResults = [];
        var lastStreetSearchTerm = "";
        var lastHouseNoSearchTerm = "";
        vm.getStreetsFromService = function (searchTerm) {
            if (searchTerm == null || searchTerm == "" || searchTerm.length < 3) {
                lastStreetSearchTerm = "";
                lastHouseNoSearchTerm = "";
                lastStreetResults = [];
                return [];
            }
            vm.assessment.assessmentProjectDetail.streetName = "";
            if (((lastStreetSearchTerm != null && lastStreetSearchTerm.length > 2 &&
                lastStreetSearchTerm.length <= searchTerm.length &&
                lastStreetSearchTerm == searchTerm.substring(0, lastStreetSearchTerm.length) &&
                lastHouseNoSearchTerm == vm.assessment.assessmentProjectDetail.houseNumber &&
                lastStreetResults != null && lastStreetResults.length < 50) &&
                lastStreetResults.length > 0)) {
                //return lastStreetResults;
                return _.filter(lastStreetResults, function (item) { return item.CustomFields.route.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0 });
            }

            var combinedSearchTerm = searchTerm;
            if (vm.assessment.assessmentProjectDetail.houseNumber != "") {
                combinedSearchTerm = vm.assessment.assessmentProjectDetail.houseNumber + " " + searchTerm;
            }
            return mapdataservice.searchStreets(combinedSearchTerm, vm.assessment.assessmentProjectDetail.suburb, vm.assessment.assessmentProjectDetail.stateCode, vm.assessment.assessmentProjectDetail.postcode)
            .then(function (data) {
                lastStreetResults = data.Results;
                lastStreetSearchTerm = searchTerm;
                lastHouseNoSearchTerm = vm.assessment.assessmentProjectDetail.houseNumber;
                return data.Results;
            });
        }

        vm.stateChanged = function (stateItem) {
            vm.assessment.natHERSClimateZoneCode = null;
            vm.assessment.nccClimateZoneCode = null;

            vm.assessment.assessmentProjectDetail.suburb = null;
            clearAddressChildFields();
        }

        vm.clearAddress = function () {
            vm.assessment.assessmentProjectDetail.postcode = null;
            vm.assessment.assessmentProjectDetail.suburb = null;
            vm.assessment.assessmentProjectDetail.streetName = null;
            vm.assessment.assessmentProjectDetail.prefix = null;
            vm.assessment.assessmentProjectDetail.houseNumber = null;
            vm.assessment.assessmentProjectDetail.lotTypeCode = null;
            vm.assessment.assessmentProjectDetail.lotType = null;
            vm.assessment.assessmentProjectDetail.stateCode = null;
            vm.assessment.assessmentProjectDetail.streetType = null;
            vm.assessment.assessmentProjectDetail.projectOwner = null;
            vm.assessment.assessmentProjectDetail.planType = null;
            vm.assessment.assessmentProjectDetail.depositedPlanNumber = null;
            vm.assessment.assessmentProjectDetail.originalDepositedPlanNumber = null;
            vm.assessment.assessmentProjectDetail.strataLotNumber = null;
            vm.assessment.assessmentProjectDetail.surveyStrataLotNumber = null;
            vm.assessment.assessmentProjectDetail.lotNumber = null;
            vm.assessment.assessmentProjectDetail.originalLotNumber = null;
            vm.assessment.assessmentProjectDetail.volume = null;
            vm.assessment.assessmentProjectDetail.folio = null;
            vm.assessment.natHERSClimateZoneCode = null;
            vm.assessment.natHERSClimateZone = null;
            vm.assessment.nccClimateZoneCode = null;
            vm.assessment.nccClimateZone = null;
            vm.assessment.buildingExposureCode = null;
            vm.assessment.bushfireAttackLevelCode = null;
            vm.assessment.assessmentProjectDetail.localGovernmentAuthority = null;
            vm.assessment.assessmentProjectDetail.latitude = null;
            vm.assessment.assessmentProjectDetail.longitude = null;
            vm.assessment.isBushFireProne = null;
            vm.assessment.bushFireProneUnknown = null;
            vm.assessment.bushfireAttackLevelCode = null;
            vm.assessment.nccClimateZoneCode = null;
            vm.assessment.assessmentProjectDetail.nccClimateZoneCode = null;
            vm.assessment.assessmentProjectDetail.boundaryGeometry = null;
            vm.assessment.assessmentProjectDetail.boundarySides = null;
            vm.assessment.assessmentProjectDetail.useCustomAddress = false;

            vm.assessment.assessmentProjectDetail.certificateOfTitle = null;
            vm.assessment.assessmentProjectDetail.parcelArea = null;

            vm.assessment.assessmentProjectDetail.lotShape = null;
            vm.assessment.assessmentProjectDetail.northSideFacing = null;
            vm.assessment.assessmentProjectDetail.facingDescription = null;
            vm.assessment.assessmentProjectDetail.facingWithinXMetres = null;
            vm.assessment.assessmentProjectDetail.cornerBlock = null;

            vm.releaseMapImage(aerialMap, "mapImageFile", 19);
            vm.releaseMapImage(siteInfoMap, "siteMapImageFile", siteInfoZoomLevel);

            updateMarker(aerialMap, null, null);
            updateMarker(siteInfoMap, null, null);

            metromapservice.clearPolygonLayers(aerialMap.map);
            metromapservice.clearPolygonLayers(siteInfoMap.map);

            vm.addressChanged();
        }

        function clearAddressChildFields() {
            vm.assessment.assessmentProjectDetail.postcode = null;
            vm.assessment.assessmentProjectDetail.streetName = null;
            vm.assessment.assessmentProjectDetail.prefix = null;
            vm.assessment.assessmentProjectDetail.houseNumber = null;
            vm.assessment.assessmentProjectDetail.lotTypeCode = null;
            vm.streetSearchText = null;
            vm.assessment.assessmentProjectDetail.depositedPlanNumber = null;
            vm.assessment.assessmentProjectDetail.originalDepositedPlanNumber = null;
            vm.assessment.assessmentProjectDetail.strataLotNumber = null;
            vm.assessment.assessmentProjectDetail.surveyStrataLotNumber = null;
            vm.assessment.assessmentProjectDetail.lotNumber = null;
            vm.assessment.assessmentProjectDetail.originalLotNumber = null;
            vm.assessment.assessmentProjectDetail.volume = null;
            vm.assessment.assessmentProjectDetail.folio = null;
            vm.assessment.assessmentProjectDetail.natHERSClimateZoneCode = null;
            vm.assessment.assessmentProjectDetail.nccClimateZoneCode = null;
            vm.assessment.assessmentProjectDetail.buildingExposureCode = null;
            vm.assessment.assessmentProjectDetail.bushfireAttackLevelCode = null;
            vm.assessment.assessmentProjectDetail.localGovernmentAuthority = null;
            vm.assessment.assessmentProjectDetail.certificateOfTitle = null;
            vm.assessment.assessmentProjectDetail.parcelArea = null;
            vm.assessment.assessmentProjectDetail.lotShape = null;
            vm.assessment.assessmentProjectDetail.northSideFacing = null;
            vm.assessment.assessmentProjectDetail.facingDescription = null;
            vm.assessment.assessmentProjectDetail.facingWithinXMetres = null;
            vm.assessment.assessmentProjectDetail.cornerBlock = null;
            vm.assessment.assessmentProjectDetail.boundarySides = null;

            vm.addressChanged();
        }

        vm.suburbItemChanged = function (suburbItem) {
            if (skipStateClear) {
                skipStateClear = false;
                return;
            }
            if (!suburbItem) {
                vm.assessment.natHERSClimateZoneCode = null;
                clearAddressChildFields();
                return;
            }
            if (suburbItem && typeof suburbItem === 'string' && vm.assessment.assessmentProjectDetail.suburb == suburbItem) { return; }
            if (suburbItem && suburbItem.CustomFields
                && common.toTitleCase(suburbItem.CustomFields.locality.toLowerCase()) == vm.assessment.assessmentProjectDetail.suburb
                && vm.assessment.assessmentProjectDetail.postcode == suburbItem.CustomFields.postcode) {
                return;
            }
            vm.assessment.natHERSClimateZoneCode = null;
            clearAddressChildFields();
            if (suburbItem != undefined && typeof suburbItem == 'object') {
                vm.assessment.assessmentProjectDetail.suburb = common.toTitleCase(suburbItem.CustomFields.locality.toLowerCase());
                vm.assessment.assessmentProjectDetail.stateCode = suburbItem.CustomFields.region;
                vm.assessment.assessmentProjectDetail.postcode = suburbItem.CustomFields.postcode;
                vm.assessment.assessmentProjectDetail.localGovernmentAuthority = common.toTitleCase(suburbItem.CustomFields.lga_name);
            }
        }

        vm.streetItemChanged = function (streetItem) {

            if (streetItem != undefined && typeof streetItem == 'object') {
                vm.assessment.natHERSClimateZoneCode = null;
                //vm.assessment.assessmentProjectDetail.streetName = streetItem.Value;
                vm.assessment.assessmentProjectDetail.streetName = common.toTitleCase(streetItem.CustomFields.route);
                if (streetItem.CustomFields.lot_number != "" && streetItem.CustomFields.lot_number != " ") {
                    vm.assessment.assessmentProjectDetail.lotNumber = streetItem.CustomFields.lot_number;
                }
                vm.assessment.assessmentProjectDetail.houseNumber = streetItem.CustomFields.number;
                vm.assessment.assessmentProjectDetail.localGovernmentAuthority = common.toTitleCase(streetItem.CustomFields.lga_name);
                vm.assessment.assessmentProjectDetail.suburb = common.toTitleCase(streetItem.CustomFields.locality);
                vm.assessment.assessmentProjectDetail.stateCode = streetItem.CustomFields.region;
                vm.assessment.assessmentProjectDetail.postcode = streetItem.CustomFields.postcode;
                if (streetItem.CustomFields.legal_parcel_id != null && streetItem.CustomFields.legal_parcel_id.indexOf("/") >= 0) {
                    var splitId = streetItem.CustomFields.legal_parcel_id.split("/");
                    if (splitId != null && splitId.length >= 3) {
                        vm.assessment.assessmentProjectDetail.depositedPlanNumber = splitId[2];
                        vm.assessment.assessmentProjectDetail.originalDepositedPlanNumber = splitId[2];
                    }
                }
                // streetItem.route = vm.assessment.assessmentProjectDetail.streetName;
                // Now we need to go get the NatHERS
                mapdataservice.getNatHers(streetItem.Coordinate)
                    .then(function (data) {
                        if (data.Results != null && data.Results.length > 0) {
                            vm.assessment.natHERSClimateZoneCode = "Nat" + data.Results[0].cimate_zone_id;
                        }
                    });

            }
            vm.addressChanged();
        }

        // Launch Plan Number search modal.
        vm.searchPlanNumber = function (planNumber, fieldName) {
            var newScope = $rootScope.$new(true);
            newScope.planNumber = planNumber;
            $mdDialog.show({
                templateUrl: 'app/ui/assessment/plannumber-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                scope: newScope,
            })
            .then(function (selectedAddress) {
                if (selectedAddress != null) {
                    // populate selected address
                    vm.suburbItemChanged(selectedAddress);
                    vm.streetItemChanged(selectedAddress);
                    var ret = mapdataservice.getPlanNumberFromField(selectedAddress.Value, vm.assessment.assessmentProjectDetail.stateCode);
                    vm.assessment.assessmentProjectDetail[fieldName] = ret.planNumber;
                    if (vm.assessment.assessmentProjectDetail.lotNumber == "" || vm.assessment.assessmentProjectDetail.lotNumber == null && ret.lot != "") {
                        vm.assessment.assessmentProjectDetail.lotNumber == ret.lot;
                    }

                    vm.addressChanged();
                }
            }, function () {
                //cancelled
            });
        }

        // Get data for any dropdown lists
        vm.getstreettypes = function (searchTerm) {
            var filter = [{ field: "description", operator: "startswith", value: searchTerm }];
            return streettypeservice.getList(null, null, null, null, null, null, filter)
            .then(function (data) {
                data.data.push({ description: "Other" });
                return data.data;
            });
        }

        // Functions to get data for Typeahead
        vm.getjobs = function(searchTerm) {
            var filter = [{ field: "clientJobNumber", operator: "startswith", value: searchTerm }];
            return jobservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateJob', function(event){
            event.stopPropagation();
            vm.createJob() // function to launch add modal;
        }));

        vm.getJobListIntervalPromise = $interval(function () {
                getJobFileList();
            }, 30000);

        vm.createJob = function() {
            // Add logic to display create modal form.
        }

        vm.getfiles = function(searchTerm) {
            var filter = [{ field: "displayName", operator: "startswith", value: searchTerm }];
            return fileservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateFile', function(event){
            event.stopPropagation();
            vm.createFile() // function to launch add modal;
        }));

        vm.createFile = function() {
            // Add logic to display create modal form.
        }

        // Functions to get data for Typeahead
        vm.getcontacts = async function (searchTerm) {

            var filter = [{ field: "fullName", operator: "startswith", value: searchTerm }];

            if (vm.assessment.job.client && vm.assessment.job.client.clientId != null) {
                filter[0].logic = "and";
                filter.push({ field: "clientId", operator: "eq", value: vm.assessment.job.client.clientId, valueType: "guid?" });
            }

            return userservice
                .getList(null, null, null, null, null, null, filter, null)
                .then(function (data) {
                    return data.data;
                });

        }

        vm.getemployees = async function(searchTerm) {

            var filter = [
                { field: "fullName", operator: "startswith", value: searchTerm, logic: "and" },
                { field: "isExternal", operator: "eq", value: false, valueType: "boolean" }
            ];

            return userservice
                .getList(null, null, null, null, null, null, filter)
                .then(function(data){
                    return data.data;
                });
        }

        vm.getEmployeesAndClientUsers = async function(searchTerm) {

            let contacts = await vm.getcontacts(searchTerm);
            let employees = await vm.getemployees(searchTerm);

            const combined = [...employees, ...contacts];
            return combined;
        }

        eventListenerList.push($scope.$on('CreateEmployee', function(event){
            event.stopPropagation();
            vm.createEmployee() // function to launch add modal;
        }));

        vm.createEmployee = function() {
            // Add logic to display create modal form.
        }

        vm.getfiles = function(searchTerm) {
            var filter = [{ field: "displayName", operator: "startswith", value: searchTerm }];
            return fileservice.getList(null, null, null, null, null, null, filter)
            .then(function(data){
                return data.data;
            });
        }

        eventListenerList.push($scope.$on('CreateFile', function(event){
            event.stopPropagation();
            vm.createFile() // function to launch add modal;
        }));

        vm.createFile = function() {
            // Add logic to display create modal form.
        }

        //gets the filtered compliance emthod list
        vm.getComplianceMethodList = function () {
            return vm.complianceMethodList;
        }

        // - ----- - //
        // - WATCH - //
        // - ----- - //

        // Watch for selected assessment changes
        let unwatchSelectedAssessment = $scope.$watch('vm.selectedAssessment', function (newValue, oldValue) {
            if (newValue != undefined && newValue != null && newValue.assessmentId != vm.assessment.assessmentId) {
                var params = { assessmentId: newValue.assessmentId };
                if (vm.jobId != undefined && vm.jobId != null) {
                    params.jobId = vm.jobId;
                }
                $state.go("assessment-updateform", params);
            }
        }, true);

        // Watch for status changes
        let unwatchC = $scope.$watch('vm.assessment.status', getAssessmentVersionDropdownList);
        let unwatchB = $scope.$watch('vm.assessment.statusDescription', getAssessmentVersionDropdownList);

        // Watch for form changes to update cached error messages
        let unwatchFormErrors = $scope.$watch('assessmentform.buildingSiteForm.$error', function() {
            vm.updateCachedErrorMessages();
        }, true);

        let unwatchAddressFormErrors = $scope.$watch('assessmentform.buildingSiteForm.addressForm.$error', function() {
            vm.updateCachedErrorMessages();
        }, true);

        // Watch for changes to assessment data that would affect general tab errors
        let unwatchAssessmentOptions = $scope.$watch('vm.assessment.allComplianceOptions', function() {
            vm.updateCachedGeneralTabErrors();
        }, true);

        // Watch for changes to building type (proposed/reference)
        let unwatchBuildingToShow = $scope.$watch('vm.buildingToShow', function(newValue, oldValue) {
            if (newValue !== oldValue) {
                // Update cached error values when building type changes
                vm.updateCachedGeneralTabErrors();
            }
        });


        // - -------------------------------- - //
        // - SAVE / CANCEL / DELETE / DESTROY - //
        // - -------------------------------- - //

        vm.save = async function () {
            try {
                var saveDeferred = $q.defer();
                vm.isBusy = true;
                let isInvalid = false;

                // Remove all construction types that are not required, from all options
                vm.assessment.allComplianceOptions.forEach(option => {
                    option.tempNotSavedInDb = false;

                    // Nullify proposed sealing and ventilation details if they are
                    // not required.
                    if (option.servicesRequired !== undefined &&
                        option.servicesRequired === true) {

                        option.proposed.services = [];
                        option.reference.services = [];
                    }

                    // Add 0's to any missing table cells
                    option.ncc2022?.forEach(table => {
                        table.ncc2022OffsetList.forEach(row => {
                            if (row.her == null)     { row.her = 0; }
                            if (row.heating == null) { row.heating = 0; }
                            if (row.cooling == null) { row.cooling = 0; }
                            if (row.total == null)   { row.total = 0; }
                        })
                    });

                    // Calculate envelope summary data for Proposed building
                    option.proposed.envelopeSummary            = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFilters, 'proposed');
                    option.proposed.envelopeSummaryConditioned = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFiltersConditioned, 'proposed');
                    option.proposed.envelopeSummaryHabitable   = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFiltersHabitable, 'proposed');

                    // Calculate envelope summary data for Reference building
                    option.reference.envelopeSummary            = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFilters, 'reference');
                    option.reference.envelopeSummaryConditioned = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFiltersConditioned, 'reference');
                    option.reference.envelopeSummaryHabitable   = vm.calcEnvelopeSummaryData(option, zonesummaryservice.defaultFiltersHabitable, 'reference');
                });

                // Assign current assessor to job as well
                if (vm.assessment.assessorUser)
                    vm.assessment.job.assessorUser = vm.assessment.assessorUser;

                // Convert from DMS input back to decimal if require
                if (vm.useDMS && vm.dms.lat && vm.dms.lng) {
                    vm.assessment.assessmentProjectDetail.latitude = convertToDecimal(vm.dms.lat);
                    vm.assessment.assessmentProjectDetail.longitude = convertToDecimal(vm.dms.lng);
                }

                // Finally, upload our map image to Amazon (if applicable) and once that is complete, upload the entire
                // assessment.
                await metromapservice.uploadMapImageAndStore(aerialMap.imageBuffer, vm.assessment,
                    "mapImageFile", vm.assessment.jobId);

                await metromapservice.uploadMapImageAndStore(siteInfoMap.imageBuffer, vm.assessment,
                    "siteMapImageFile", vm.assessment.jobId);

                // If our form does not have a compliance option selected (AND THEY EXIST) but is otherwise completely valid (sans the drawing tab)
                // then we wish to enable our form to progress to the 'Compliance Options Provided' state so our standard for considering
                // the form 'valid' is less strict (i.e. we don't care about the working drawings tab, only whether we/the client
                // could select a compliance option or not)
                let selectedOption = vm.getSelectedComplianceOption();

                // Baseline was compliant OR an option was compliant, so include check for valid drawings.
                if (selectedOption)
                    isInvalid = isFormInvalid();
                else if (vm.assessment.allComplianceOptions.length > 1) {
                    // Nothing was compliant, but options exist. If we are able to select them, it means our form is valid
                    // for this stage
                    isInvalid = !allowOptionSelect(); // This does NOT include checks against valid drawings.
                }
                else {
                    // Forms invalid.
                    isInvalid = true;
                }

                // Show 'non stamped drawing model' if the next review stage will be 'Final Review'
                const nextStageIsFinalReview = selectedOption != null && isInvalid === false &&
                    ((selectedOption.isBaselineSimulation && (vm.assessment.statusCode === "AInProgress" || vm.assessment.statusCode === "ADraft"))
                    || (vm.assessment.statusCode === "AOptionSelected"));

                if(nextStageIsFinalReview && vm.hasNonStampedDrawings(selectedOption)) {
                    let result = await modalDialog.yesNoDialog(
                        "Unstamped Drawings...",
                        "You have some drawings without stamp locations assigned, please select an option below to proceed.",
                        "Setup Stamps",
                        "Use Defaults",
                        "Cancel");

                    if (result === true)
                        await vm.stampDrawings(selectedOption);
                }

                const data = await assessmentservice.updateAssessment(vm.assessment, isInvalid);

                vm.mergeAssessment(data);

                vm.resetDefaults();
                setIsLocked();
                saveDeferred.resolve();

                setFormPristine();

                // Theres a whacky bug where values S or W in DMS get converted to negative upon save.
                // This fixes it. Note we don't switch to DMS if currently in decimal mode.
                if (vm.assessment && vm.assessment.assessmentProjectDetail) { //
                    vm.dms.lat = convertToDMS(vm.assessment.assessmentProjectDetail.latitude, 'lat');
                    vm.dms.lng = convertToDMS(vm.assessment.assessmentProjectDetail.longitude, 'lng');
                }
            } finally {
                console.log("Finally block called...");
                vm.isBusy = false;
            }
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("job-listform");
                }
            }
        }

        vm.delete = function (deleteEntireAssessment) {
            vm.isBusy = true;

            // Delete only the assessment record
            assessmentservice.deleteAssessment(vm.assessmentId, deleteEntireAssessment).then(function (response) {
                vm.isBusy = false;

                var data = response.data;

                // If no data is returned, the entire assessment + job were deleted, so cancel.
                // Otherwise, we have a current assessment version, so refresh the page to that.
                if (deleteEntireAssessment || data == null || data.deleted) {
                    // Just cancelling might not be enough when we have been re-directed to this page after
                    // deleting an assessment version, so cancel and go to job-lisform.
                    vm.cancel();
                    $state.go("job-listform");
                }
                else {
                    var params = {
                        assessmentId: data.assessmentId,
                        jobId: vm.jobId
                    }

                    // Refresh this page with new parameters.
                    $state.go("assessment-updateform", params);
                }
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;

            //Undo delete on the assessment record
            assessmentservice.undoDeleteAssessment(vm.assessmentId).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];

            $interval.cancel(vm.getJobListIntervalPromise);

            // Nullify the global assessment
            Globals.currentAssessment = null;
            Globals.currentServicesOption = null;

            // Unwatch all watchers
            if(unwatchC)
                unwatchC();

            if(unwatchB)
                unwatchB();

            if(unwatchFormErrors)
                unwatchFormErrors();

            if(unwatchAddressFormErrors)
                unwatchAddressFormErrors();

            if(unwatchAssessmentOptions)
                unwatchAssessmentOptions();

            if(unwatchBuildingToShow)
                unwatchBuildingToShow();

            if(unwatchSelectedAssessment)
                unwatchSelectedAssessment();
        });

        vm.cancelJob = function () {

            modalDialog.confirmationDialog("Confirm", "Please confirm you want to Cancel this job.", null, null, null, null, null, true).then(function () {
                vm.isBusy = true;
                jobservice.cancelJob(vm.assessment.jobId).then(function (result) {
                    vm.isBusy = false;

                    if (result != null && result.data == "JCancelled") {
                        vm.assessment.job.statusCode = result.data;
                        vm.cancel();
                    }
                });
            });
        }

        // Reinstates a job
        vm.reinstateJob = function () {

            modalDialog.confirmationDialog("Confirm", "Please confirm you want to reinstate this job.", null, null, null, null, null, true).then(function () {
                vm.isBusy = true;
                jobservice.reinstateJob(vm.assessment.jobId).then(function (result) {

                    vm.isBusy = false;
                    if (result != null && result.data == "JInProgress") {

                        vm.assessment.job.statusCode = result.data;
                        vm.assessment.statusCode = "AInProgress";
                    }
                });
            });
        }

        function getSectorAngle(sectors, sector) {
            let angle = null;
            const found = sectors.find(s => sector === s.label.toLowerCase());
            if (found) {
                if (found.min === 0 || found.max === 360) // N
                    angle = 0;
                else
                    angle = (found.max - found.min) / 2 + found.min; // Middle of min and max
            }
            else {
                throw ('Error - Cannot match sector - sector:' + sector + ' sectorDeterminations:' + sectors);
            }

            return angle;
        }

        vm.calcEnvelopeSummaryData = function(option, filters, buildingToShow = null) {
            // Use the passed buildingToShow parameter, or fall back to vm.buildingToShow if not provided
            const targetBuilding = buildingToShow || vm.buildingToShow;

            let sectorKeys = [... new Set(option.sectorDetermination?.sectors?.map(sd => sd.label.toLowerCase())), 'total'];
            let exteriorWallAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(option[targetBuilding], 'surface', 'ExteriorWall', option.sectorDetermination, filters);
            let exteriorGlazingAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(option[targetBuilding], 'opening', 'ExteriorGlazing', option.sectorDetermination, filters);
            let glassExteriorWallRatioPerSector = zoneservice.calcGlassExteriorWallRatioPerSector(exteriorWallAreaTotalsPerSector, exteriorGlazingAreaTotalsPerSector, option.sectorDetermination);
            let parents = option[targetBuilding].openings.filter(o => o.category.constructionCategoryCode == "ExteriorGlazing");
            let rows = zoneservice.generateConstructionRowsFromElements(option[targetBuilding], parents, filters);
            let averageGlazingUValuePerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingUValuePerSector(rows, option.sectorDetermination);
            let averageGlazingSHGCPerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingSHGCPerSector(rows, option.sectorDetermination);
            let percentageData = [];
            let glazingAreaDataLookup = {};
            for (const sector in exteriorGlazingAreaTotalsPerSector) {
                if (sector === 'total')
                    continue;
                if (exteriorGlazingAreaTotalsPerSector.hasOwnProperty(sector)) {
                    percentageData.push( [getSectorAngle(option.sectorDetermination.sectors, sector), parseFloat(exteriorGlazingAreaTotalsPerSector[sector].percentage.toFixed(2))] );
                    glazingAreaDataLookup[getSectorAngle(option.sectorDetermination.sectors, sector)] = parseFloat(exteriorGlazingAreaTotalsPerSector[sector].area.toFixed(2));
                }
            }
            vm.windRosePercentageData = percentageData;
            const sectorLabels = { n: 'North', nne: 'North-North-East', ne: 'North-East', ene: 'East-North-East', e: 'East', ese: 'East-South-East', se: 'South-East', sse: 'South-South-East', s: 'South', ssw: 'South-South-West', sw: 'South-West', wsw: 'West-South-West', w: 'West', wnw: 'West-North-West', nw: 'North-West', nnw: 'North-North-West' };
            const categories = sectorKeys.filter(key => key !== 'total').map(key => sectorLabels[key]);
            const tickSize = 360 / categories.length; // ie. 360 / 8 = 45 degrees
            let windRoseChart = Highcharts.chart('es-windrose-chart-' + option.optionIndex, {
                        series: [{
                            name: 'Exterior Glazing (%)',
                            data: vm.windRosePercentageData
                        }],
                        chart: {
                            polar: true,
                            type: 'column',
                            height: 500
                        },
                        title: { text: null },
                        pane: { size: '95%' },
                        legend: {
                            align: 'center',
                            verticalAlign: 'bottom',
                            layout: 'vertical'
                        },
                        credits: { enabled: false },
                        xAxis: {
                            min: 0,
                            max: 360,
                            type: "",
                            tickInterval: tickSize,
                            tickmarkPlacement: 'on',
                            labels: {
                                enabled: true,
                                formatter: function () {
                                    return categories[this.value / tickSize]; // + '�'; // Index of categories array
                                }
                            }
                        },
                        yAxis: {
                            min: 0,
                            endOnTick: false,
                            showLastLabel: true,
                            title: {
                                text: null
                            },
                            labels: {
                                enabled: false,
                                formatter: function () {
                                    return this.value + '%';
                                }
                            },
                            reversedStacks: false
                        },
                        tooltip: {
                            formatter: function () {
                                let percentage = `<span style="font-weight: bold">${this.y.toFixed(2)}%</span>`;
                                let area = `(<span>${glazingAreaDataLookup[this.x].toFixed(2)}m&sup2;</span>)`;
                                return `<span style="font-size: 11px">${categories[this.x / 45]}</span><br/><span style="color: ${this.color};">\u25CF&nbsp;</span>${this.series.name}: ${percentage} ${area}`;
                            }
                        },
                        plotOptions: {
                            series: {
                                stacking: 'normal',
                                shadow: false,
                                groupPadding: 0,
                                pointPlacement: 'on'
                            }
                        }
                    });
            return {
                sectorKeys: sectorKeys,
                exteriorWallAreaTotalsPerSector: exteriorWallAreaTotalsPerSector,
                exteriorGlazingAreaTotalsPerSector: exteriorGlazingAreaTotalsPerSector,
                glassExteriorWallRatioPerSector: glassExteriorWallRatioPerSector,
                averageGlazingUValuePerSector: averageGlazingUValuePerSector,
                averageGlazingSHGCPerSector: averageGlazingSHGCPerSector,
                windRoseChartSvg: windRoseChart.getSVG({ exporting: { sourceWidth: 878, sourceHeight: 600 } })
            }
        }

        vm.regenerateReports = function () {
            modalDialog.confirmationDialog("Regenerate Reports", "Confirm you would like to trigger the PDF generation process.", "Confirm", "Cancel", null).then(() => {
                assessmentservice.triggerGenerateReports(vm.assessment.assessmentId, vm.assessment.statusCode);
            });
        }



        function setFormPristine() {
            if ($scope.assessmentform) $scope.assessmentform.$setPristine();
            if ($scope.assessmentform.assessmentinfoform) $scope.assessmentform.assessmentinfoform.$setPristine();
            if ($scope.assessmentform.AssessmentDrawingsOptionForm) $scope.assessmentform.AssessmentDrawingsOptionForm.$setPristine();
            if ($scope.assessmentform.buildingSiteForm) $scope.assessmentform.buildingSiteForm.$setPristine();
            if ($scope.assessmentform.assessmentbuildingelementsform) $scope.assessmentform.assessmentbuildingelementsform.$setPristine();
            if ($scope.assessmentform.assessmentcalcfloorform) $scope.assessmentform.assessmentcalcfloorform.$setPristine();
            if ($scope.assessmentform.assessmentcalcannualform) $scope.assessmentform.assessmentcalcannualform.$setPristine();
            if ($scope.assessmentform.assessmentcompoptionform) $scope.assessmentform.assessmentcompoptionform.$setPristine();
          if ($scope.assessmentform.servicesForm) $scope.assessmentform.servicesForm.$setPristine();
            if ($scope.assessmentform.buildingZonesForm) $scope.assessmentform.buildingZonesForm.$setPristine();
        }

        function isFormInvalid() {

            // Ok I don't actually think we use this now, as it would prevent going to "Awaiting Compliance"
            //if (vm.assessmentOutcomesHasErrors())
            //    return true;

            const assessmentInfoInvalid = ($scope.assessmentform.assessmentinfoform && $scope.assessmentform.assessmentinfoform.$invalid);
            const buildingSiteFormInvalid = ($scope.assessmentform.buildingSiteForm && $scope.assessmentform.buildingSiteForm.$invalid);
            const workingDrawingsInvalid = vm.workingDrawingsHasErrors() != "" || $scope.assessmentform?.WorkingDrawingsForm?.$invalid;
            const buildingZonesCustomCalcErrors = vm.buildingZonesHasErrors() != "";
            const buildingZonesInvalid = $scope.assessmentform?.buildingZonesForm?.$invalid;
            const buildingElementsInvalid =
                vm.buildingElementsHasErrors() ||
                vm.buildingElementsFormHasErrors('construction') ||
                vm.buildingElementsFormHasErrors('opening') ||
                vm.buildingServicesFormHasErrors();
            const invoicingFormInvalid = ($scope.assessmentform.invoicingForm && $scope.assessmentform.invoicingForm.$invalid);
            const generalTabInvalid = vm.buildingGeneralTabHasErrors();

            const mapsInvalid = vm.cachedMapImagesNotCaptured !== "";

            return assessmentInfoInvalid || workingDrawingsInvalid ||
                generalTabInvalid ||
                buildingZonesCustomCalcErrors || buildingZonesInvalid ||
                buildingElementsInvalid || mapsInvalid || buildingSiteFormInvalid ||
                invoicingFormInvalid;

        }

        /** Similiar to the above 'isFormInvalid' but avoids any chicken/egg problems in determining whether we can select or not. */
        function allowOptionSelect() {

            // For debugging
            var result1  = $scope.assessmentform.assessmentinfoform && $scope.assessmentform.assessmentinfoform.$invalid;
            var result2  = $scope.assessmentform.buildingSiteForm && $scope.assessmentform.buildingSiteForm.$invalid;
            var result3  = $scope.assessmentform.designForm && $scope.assessmentform.designForm.$invalid;
            var result4  = $scope.assessmentform.assessmentOutcomesRootForm.$invalid;
            var result5  = vm.assessmentOutcomesHasErrors(true);
            var result6  = vm.buildingZonesHasErrors();
            var result7  = vm.buildingGeneralTabHasErrors();
            var result8  = vm.buildingElementsHasErrors() ;
            var result9  = vm.buildingElementsFormHasErrors('construction') ;
            var result10 = vm.buildingElementsFormHasErrors('opening');
            var result11 = vm.buildingServicesFormHasErrors();
            var result12 = $scope.assessmentform.invoicingForm && $scope.assessmentform.invoicingForm.$invalid;
            var result13 = $scope.assessmentform.assessmentcalcfloorform && $scope.assessmentform.assessmentcalcfloorform.$invalid;
            var result14 = $scope.assessmentform.assessmentcalcannualform && $scope.assessmentform.assessmentcalcannualform.$invalid;
            var result15 = $scope.assessmentform.assessmentcompoptionform && $scope.assessmentform.assessmentcompoptionform.$invalid;
            var result16 = $scope.assessmentform.buildingZonesForm && $scope.assessmentform.buildingZonesForm.$invalid;

            if (
                ($scope.assessmentform.assessmentinfoform && $scope.assessmentform.assessmentinfoform.$invalid)
                || ($scope.assessmentform.buildingSiteForm && $scope.assessmentform.buildingSiteForm.$invalid)
                || ($scope.assessmentform.designForm && $scope.assessmentform.designForm.$invalid)
                || $scope.assessmentform.assessmentOutcomesRootForm.$invalid
                || vm.assessmentOutcomesHasErrors(true)
                || vm.buildingZonesHasErrors()
                || vm.buildingGeneralTabHasErrors()
                || vm.buildingElementsHasErrors()
                || vm.buildingElementsFormHasErrors('construction')
                || vm.buildingElementsFormHasErrors('opening')
                || vm.buildingServicesFormHasErrors()
                || ($scope.assessmentform.invoicingForm && $scope.assessmentform.invoicingForm.$invalid)
                || ($scope.assessmentform.assessmentcalcfloorform && $scope.assessmentform.assessmentcalcfloorform.$invalid)
                || ($scope.assessmentform.assessmentcalcannualform && $scope.assessmentform.assessmentcalcannualform.$invalid)
                || ($scope.assessmentform.assessmentcompoptionform && $scope.assessmentform.assessmentcompoptionform.$invalid)
                || ($scope.assessmentform.buildingZonesForm && $scope.assessmentform.buildingZonesForm.$invalid)) {

                return false;
            }

            return true;
        }
        vm.allowOptionSelect = allowOptionSelect;

        vm.copyGeneralToFrom = function(toOption, toBuilding, fromBuilding, fromOption) {

            // USE TIMEOUTS TO AVOID WEIRD UI DISPLAY BUGS.
            setTimeout(() => {

                const spaces = angular.copy(fromBuilding.spaces);
                spaces.forEach(z => {
                    z.zoneId = uuid4.generate();
                    z.createdOn = new Date().toUTCString();
                    z.assessmentComplianceBuildingId = toBuilding.assessmentComplianceBuildingId
                });

                const roofs = angular.copy(fromBuilding.roofs);
                roofs.forEach(z => {
                    z.zoneId = uuid4.generate();
                    z.createdOn = new Date().toUTCString();
                    z.assessmentComplianceBuildingId = toBuilding.assessmentComplianceBuildingId
                });

                toBuilding.spaces = spaces;
                toBuilding.roofs = roofs;

                toBuilding.zoneTypesNotApplicable.floorPlanSpaces = fromBuilding.zoneTypesNotApplicable.floorPlanSpaces;
                toBuilding.zoneTypesNotApplicable.generalroofs = fromBuilding.zoneTypesNotApplicable.generalroofs;

                toBuilding.projectDescription = fromBuilding.projectDescription;
                toBuilding.projectDescriptionOther = fromBuilding.projectDescriptionOther;
                toBuilding.projectClassification = fromBuilding.projectClassification;
                toBuilding.design = fromBuilding.design;
                toBuilding.buildingOrientation = fromBuilding.buildingOrientation;
                toBuilding.lowestLivingAreaFloorType = fromBuilding.lowestLivingAreaFloorType;
                toBuilding.masonryWalls = fromBuilding.masonryWalls;
                toBuilding.buildingWidth = fromBuilding.buildingWidth;
                toBuilding.buildingLength = fromBuilding.buildingLength;

            }, 100);

        }

        vm.optionsNotThisOrBaseline = function (option) {
            return vm.assessment.allComplianceOptions
              ?.filter(x => x.optionIndex !== 0 && x.optionIndex !== option.optionIndex);
        }

        vm.showCopyBaselineForOption = function (option) {
            return vm.buildingToShow === 'proposed' ||
              (vm.buildingToShow === 'reference' && referenceIsRequired(option));
        }

        function isFormDirty() {
            if (($scope.assessmentform && $scope.assessmentform.$dirty)
                || ($scope.assessmentform.assessmentinfoform && $scope.assessmentform.assessmentinfoform.$dirty)
                || ($scope.assessmentform.AssessmentDrawingsOptionForm && $scope.assessmentform.AssessmentDrawingsOptionForm.$dirty)
                || ($scope.assessmentform.AssessmentDrawingsOptionForm && vm.assessment.allComplianceOptions[0].assessmentDrawings && _.find(vm.assessment.allComplianceOptions[0].assessmentDrawings, function (val) { val.deleted == true })!=null)
                || ($scope.assessmentform.buildingSiteForm && $scope.assessmentform.buildingSiteForm.$dirty)
                || ($scope.assessmentform.assessmentbuildingelementsform && $scope.assessmentform.assessmentbuildingelementsform.$dirty)
                || ($scope.assessmentform.assessmentcalcfloorform && $scope.assessmentform.assessmentcalcfloorform.$dirty)
                || ($scope.assessmentform.assessmentcalcannualform && $scope.assessmentform.assessmentcalcannualform.$dirty)
                || ($scope.assessmentform.assessmentcompoptionform && $scope.assessmentform.assessmentcompoptionform.$dirty)
                || ($scope.assessmentform.servicesForm && $scope.assessmentform.servicesForm.$dirty)
                || ($scope.assessmentform.buildingZonesForm && $scope.assessmentform.buildingZonesForm.$dirty)) {
                return true;
            }

            return false;
        }

        //merges current object with incoming object
        vm.mergeAssessment = function (newAssessment) {

            // Note: We can't just ignore any drawings coming in due to the fact new drawings
            // (coming in from an queue process) COULD actually be present... .

            // Loop over all compliance options, we check if there are any NEW (i.e. unique) drawings in the INCOMING
            // assessment, and if there are, we insert them into our CURRENT drawing list for the given option.
            // Finally, we set the INCOMING drawings to BE our CURRENT drawings (which now includes any unique INCOMING drawings)
            // so that when we call angular.merge below it doesn't mess everything up and create whacky duplicates.
            for (let i = 0; i < vm.assessment.allComplianceOptions.length; i++) {

                let currentOption = vm.assessment.allComplianceOptions[i];
                let newOption = newAssessment.allComplianceOptions.find(opt => opt.complianceOptionsId === currentOption.complianceOptionsId);

                // Construct a list of NEW incoming drawings
                let newUniqueDrawings = newOption.assessmentDrawings
                    .filter(x => currentOption.assessmentDrawings
                        .filter(y => y.assessmentDrawingId === x.assessmentDrawingId)
                        .length === 0);

                // Now we want to add all the new unique drawings onto the end of our current drawings...
                newUniqueDrawings.forEach(drawing => currentOption.assessmentDrawings.push(drawing));
                newUniqueDrawings = newUniqueDrawings.sort((a,b) => a.drawingNumber > b.drawingNumber ? 1 : -1);

                common.setDrawingReferences(currentOption.assessmentDrawings);

                // Pre-merging will stop the angular.merge from messing our drawings up.
                newOption.assessmentDrawings = currentOption.assessmentDrawings;

            }

            angular.merge(vm.assessment, newAssessment);
        }

        //called after assessment has been saved or updated. Resets Certain default fields such as compliance option fields, that get removed on save.
        vm.resetDefaults = function () {

            vm.title = "Edit Assessment";

            if(vm.assessment.job != undefined && vm.assessment.job != null){
                vm.subtitle = vm.assessment.job.jobReference + " | " + vm.assessment.job.clientName + " | " + vm.assessment.assessmentProjectDetail.projectOwner;
            }

            // Refresh the Assessment Version dropdown list.
            getAssessmentVersionDropdownList();
        }



        vm.assessmentSoftwareOtherChanged = function () {
            if (vm.assessment.assessmentSoftwareCode == 'Other' && vm.assessment.assessmentSoftwareOther == null) {
                vm.assessment.assessmentSoftwareCode = '';
            }
        }

        vm.clearAssessmentSoftwareDescription = function (item) {
            item.assessmentSoftwareCode = null;
            item.assessmentSoftwareOther = null;
        }

        //strreet search changed - copied from job
        vm.streetSearchChanged = function (streetTypeItem, searchText) {
            vm.tempStreetTypeSearchQuery = searchText;
        }

        vm.streetTypeItemChanged = function (streetTypeItem, newStreetType, searchText) {
            if (newStreetType == "Other") {
                streetTypeItem["streetType"] = vm.tempStreetTypeSearchQuery;
            } else if (newStreetType != undefined) {
                streetTypeItem["streetType"] = newStreetType;
            }
        }

        // Simply updates the address fields.
        vm.addressChanged = function () {
            if (vm.assessment && vm.assessment.assessmentProjectDetail) {
                vm.assessment.assessmentProjectDetail.fullAddress = assessmentservice.getFullAddress(vm.assessment.assessmentProjectDetail);
                vm.assessment.assessmentProjectDetail.originalDisplayAddress = vm.assessment.assessmentProjectDetail.fullAddress.replace(', ', '\n');
                // Update even if we aren't in this mode.
                updateDMSValues();
            }
        }

        // Address has been changed using the search address feature, so we are generally satisfied with its
        // GPS location results and thus wish to take a new screenshot, move the map and so on.
        vm.addressChangedViaSearchAddress = async function () {
            vm.addressChanged();
            checkIfBushFireProne();
            getClimateZoneCode();
            await vm.confirmCoordinates(); // This also takes a snapshot and invalidates map etc.
        }

        // Select suburb
        vm.selectedSuburb = null;
        vm.suburbChanged = function () {
            addressservice.setSuburbToAssessment(
                vm.assessment,
                vm.selectedSuburb,
                () => vm.addressChangedViaSuburbSearch()
            );
        }

        // Changes via suburb search can't be relied on to be accurate, therefore we
        // don't include the pdf by default and unlock coordinates.
        vm.addressChangedViaSuburbSearch = async function () {
            vm.addressChanged();
            vm.assessment.includeMapInExportedPDF = true;

            checkIfBushFireProne();
            getClimateZoneCode();

            await vm.confirmCoordinates(true, true); // This also takes a snapshot and invalidates map etc.

        }

        function checkIfBushFireProne() {

            if(vm.assessment.assessmentProjectDetail.latitude == null ||
               vm.assessment.assessmentProjectDetail.longitude == null)
               return;

            bushfireproneservice.checkIfBushFireProne(vm.assessment.assessmentProjectDetail.latitude, vm.assessment.assessmentProjectDetail.longitude)
                .then(async isProne => {
                    let stateHasData = await bushfirestatedataservice.stateHasBushfireData(vm.assessment.assessmentProjectDetail.stateCode);
                    // IF is prone, means we do have data and 'IsProne' was found
                    if (isProne) {
                        vm.assessment.bushFireProneUnknown = false;
                        vm.assessment.isBushFireProne = true;
                        vm.assessment.bushfireAttackLevelCode = null;
                    // ELSE if have data, means 'IsProne' is false
                    } else if (stateHasData) {
                        vm.assessment.bushFireProneUnknown = false;
                        vm.assessment.isBushFireProne = false;
                        vm.assessment.bushfireAttackLevelCode = "BAL-NotApp";
                    // ELSE we don't have data for this state, so trigger 'unknown' state
                    } else {
                        vm.assessment.bushFireProneUnknown = true;
                        vm.assessment.isBushFireProne = null;
                        vm.assessment.bushfireAttackLevelCode = null;
                    }
                });
        }

        vm.isBushFireProneChange = function () {
            if (!vm.assessment.isBushFireProne) {
                vm.assessment.bushfireAttackLevelCode = "BAL-NotApp";
            }
        }

        function getClimateZoneCode() {

            if(vm.assessment.assessmentProjectDetail.latitude == null ||
                vm.assessment.assessmentProjectDetail.longitude == null)
                return;

            nccclimatezonedataservice.getClimateZone(vm.assessment.assessmentProjectDetail.latitude, vm.assessment.assessmentProjectDetail.longitude)
                .then((code) => {
                    vm.assessment.nccClimateZoneCode = `NCC${code}`
                    vm.assessment.nccClimateZone = vm.nccClimateZoneList.find(x => x.nccClimateZoneCode === vm.assessment.nccClimateZoneCode);
                });
        }

        /** Confirms our coordinates and generates a snapshot of the map at the given GPS location. */
        vm.confirmCoordinates = async function (doCapture = true, isSuburbSearch = false) {

            vm.siteInfoMap.isBusy = true;
            vm.aerialMap.isBusy = true;

            vm.assessment.assessmentProjectDetail.coordinatesAreConfirmed = true;
            vm.assessment.includeMapInExportedPDF = true;

            vm.releaseMapImage(aerialMap, "mapImageFile", doCapture ? 19 : null);
            aerialMap.map.invalidateSize();

            vm.releaseMapImage(siteInfoMap, "siteMapImageFile", doCapture ? siteInfoZoomLevel : null );
            siteInfoMap.map.invalidateSize();

            // Take a full-sized snapshot for maximum fidelity booyah
            if (doCapture) {
                await captureMapImagesAtCurrentAddress(isSuburbSearch);
            }
        }

        async function captureMapImagesAtCurrentAddress(isSuburbSearch = false) {

            let hasMetroMapData = await metromapservice.isDataAvailableAtCoordinates({
                lat: vm.assessment.assessmentProjectDetail.latitude,
                lng: vm.assessment.assessmentProjectDetail.longitude
            });

            let captureMap = hasMetroMapData
                ? "metro"
                : "satellite";

            let maxZoom = hasMetroMapData && vm.assessment.assessmentProjectDetail.boundaryGeometry && vm.assessment.assessmentProjectDetail.boundaryGeometry.length > 0
                ? null
                : 18;

            try {

                aerialMap.imageBuffer = await metromapservice.takeFullscreenSnapshot(
                    document.getElementById("remote_map"),
                    {
                        lat: vm.assessment.assessmentProjectDetail.latitude,
                        lng: vm.assessment.assessmentProjectDetail.longitude
                    },
                    vm.assessment.assessmentProjectDetail.boundaryGeometry,
                    captureMap, true, maxZoom
                );
                aerialMap.isBusy = false;

                // Also take a picture for the site map thing...
                siteInfoMap.imageBuffer = await metromapservice.takeFullscreenSnapshot(
                    document.getElementById("remote_site_map"),
                    {
                        lat: vm.assessment.assessmentProjectDetail.latitude,
                        lng: vm.assessment.assessmentProjectDetail.longitude
                    },
                    vm.assessment.assessmentProjectDetail.boundaryGeometry,
                    'street', isSuburbSearch, siteInfoZoomLevel
                );
                siteInfoMap.isBusy = false;

                // Update cached values after capturing images
                vm.updateMapImageCachedValues();
            } catch (e) {
                console.warn(e);
                aerialMap.isBusy = false;
                siteInfoMap.isBusy = false;
            }
        }

        vm.useCustomClientNameChanged = function (useCustomClientName) {
            if (useCustomClientName === false) {
                vm.assessment.assessmentProjectDetail.customClientName = null;
                vm.clientJobNumberChanged();
            }
        }

        vm.useCustomAddressChanged = function (useCustomAddress, getNewData = true) {
            vm.assessment.assessmentProjectDetail.useCustomAddress = useCustomAddress;
            if (useCustomAddress === true) {
                vm.assessment.assessmentProjectDetail.customDisplayAddress = vm.assessment.assessmentProjectDetail.originalDisplayAddress;
            } else if (useCustomAddress === false && getNewData === true) {
                vm.assessment.assessmentProjectDetail.customDisplayAddress = null;
            }
        }

        //returns the current selected compliance option, or null if none are selected
        vm.getSelectedComplianceOption = function () {
            if (vm.assessment && vm.assessment.allComplianceOptions)
                return vm.assessment.allComplianceOptions.find(x => x.isSelected && x.isCompliant) || null;
            else
                return null;
        }

        //called when compliance method on baseline compliance option gets changed.
        vm.validatePreliminaryComplianceMethodChange = function () {
            vm.updateAvailableSoftware();
        }

        //returns true if save should be disabled
        vm.saveDisabled = function () {
            if (vm.assessment == undefined || vm.assessment == null) {
                return true;
            }
            if ($scope.assessmentform && $scope.assessmentform.templateinfoform && $scope.assessmentform.templateinfoform.$invalid) {
                return true;
            }
            return false;
        }

        vm.openOnlineAssessmentPage = function () {
            var pageUrl = window.location.origin + "/thermarate/certificate/information.aspx?certificateNumber=" + vm.assessment.certificateNumber;
            window.open(pageUrl);
        }

        async function issue() {

            let selectedComplianceOption = vm.getSelectedComplianceOption();

            if (selectedComplianceOption == null) {
                return;
            }

            // Not issueable in current  state
            if (vm.assessment.statusCode !== "AComplete") {

                await modalDialog.infoDialog(
                  "Invalid Assessment",
                  null,
                  "Assessment can only be issued if it is the current assessment and is complete.",
                  "Ok",
                  null);

                return;
            }

            // Invalid version number
            if (vm.assessment.assessmentProjectDetail.assessmentVersion <= 0) {

                await modalDialog.infoDialog(
                  "Assessment Version Number",
                  null,
                  "The version number is invalid. Please choose a version number greater than zero.",
                  "Ok",
                  null);

                return;
            }

            // Issue Certificate
            await modalDialog.confirmationDialog(
              "Please confirm you want to sign the Assessment Certificate",
              "This will provide a certified document of the current Assessment.",
              "Ok",
              "Cancel",
              null);

            await vm.finalizeIssuingAssessment(
              vm.assessment.assessmentId,
              vm.assessment.assessmentProjectDetail.assessmentVersion);

        }

        /**
         * State between 'in progress' and 'compliance options provided' requires approved compliance options before
         * they are released to the client.
         */
        async function approvePreliminaryReview() {

            // Issue Certificate
            var yep = await modalDialog.confirmationDialog(
              "Please confirm you want to approve the Compliance Options",
              "This will release the compliance options to the client.",
              "Ok",
              "Cancel",
              null);

            var approved = await assessmentservice.approvePreliminaryReview(vm.assessment.assessmentId);

            if(approved) {

                // Make sure Status shows as issued - its all updated on server we really should reload.
                vm.assessment.statusCode = "ACompliance";
                vm.assessment.statusDescription = "Compliance Options Provided";
                vm.assessment.job.statusCode = "JCompliance";
                vm.assessment.job.statusDescription = "Compliance Options Provided";

                getAssessmentVersionDropdownList();
                setIsLocked();
            }

        }

        vm.finalizeIssuingAssessment = function (assessmentId) {

            var newScope = $rootScope.$new(true);

            newScope.printPromise = assessmentservice.issueAssessment(assessmentId);

            $mdDialog.show({
                templateUrl: 'app/ui/job/print-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                scope: newScope,
            })
            .then(function (result) {

            }, function () {
                //cancelled
            });

            newScope.printPromise.then(function (wasIssued) {

                if(wasIssued !== true)
                    return;

                getAssessmentVersionDropdownList();
                getJobFileList();

                // Make sure Status shows as issued - its all updated on server we really should reload.
                vm.assessment.statusCode = "AIssued";
                vm.assessment.statusDescription = "Complete";
                vm.assessment.job.statusCode = "JIssued";
                vm.assessment.job.statusDescription = "Complete";

                getAssessmentVersionDropdownList();
                setIsLocked();
            });
        }

        vm.hasNonStampedDrawings = function (option) {
            if (vm.assessment == undefined || vm.assessment == null || vm.assessment == "" || option.assessmentDrawings == null) {
                return false;
            }

            var result = false;
            for (var i = 0; i < option.assessmentDrawings.length; i++) {
                var drawing = option.assessmentDrawings[i];
                if ((drawing.isStamped != true || drawing.stampWidth == null || drawing.stampX == null || drawing.stampY == null) && drawing.isIncludedInReport == true && drawing.toStamp == true) {
                    result = true;
                    break;
                }
            }

            return result;
        }

        vm.stampDrawings = function (option) {
            var deferred = $q.defer();

            var numOfExpectedDrawings = 0;

            for (var i = 0; i < option.assessmentDrawings.length; i++) {
                if (option.assessmentDrawings[i].isIncludedInReport && option.assessmentDrawings[i].deleted == false) {
                    numOfExpectedDrawings++;
                }
            }

            vm.showStampDrawings(numOfExpectedDrawings, null, option).then(function () {
                deferred.resolve();
            });

            return deferred.promise;
        }

        // TODO: Ideally this should live elsewhere as it is (mostly) shared between here and the
        //  building-drawings-controller, however since client wishes to go live TODAY, it can wait...
        vm.showStampDrawings = function (expectedNumOfDrawings, drawing, option) {

            let deferred = $q.defer();

            let modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.assessmentId = vm.assessment.assessmentId;
            modalScope.expectedNumOfDrawings = option.assessmentDrawings.length;
            modalScope.assessmentDrawingId = drawing?.assessmentDrawingId;
            modalScope.assessmentDrawings = option.assessmentDrawings;
            let modalOptions = {
                templateUrl: 'app/ui/assessmentdrawing/assessmentdrawing-stamp.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions).then(function (updatedDrawings) {
                if (updatedDrawings != null) {
                    // Drawing stamp positions where modified and saved, so update list.
                    for (let i = 0, len = updatedDrawings.length; i < len; i++) {
                        let rec = _.findWhere(option.assessmentDrawings, { assessmentDrawingId: updatedDrawings[i].assessmentDrawingId });
                        if (rec != undefined) {
                            rec.stampX = updatedDrawings[i].stampX;
                            rec.stampY = updatedDrawings[i].stampY;
                            rec.toStamp = updatedDrawings[i].toStamp;
                            rec.isStamped = true;
                            rec.stampWidth = updatedDrawings[i].stampWidth;
                            rec.drawingDescription = updatedDrawings[i].drawingDescription;
                            rec.sheetNumber = updatedDrawings[i].sheetNumber;
                            rec.revision = updatedDrawings[i].revision;
                            rec.revisionDate = updatedDrawings[i].revisionDate;
                            rec.isIncludedInReport = updatedDrawings[i].isIncludedInReport;
                            rec.deleted = updatedDrawings[i].deleted;
                            rec.rotation = updatedDrawings[i].rotation;
                            rec.pageSize = updatedDrawings[i].pageSize;
                        }
                    }
                }

                common.setDrawingReferences(option.assessmentDrawings);

                deferred.resolve();
            });

            return deferred.promise;
        }

        vm.copyAssessment = function () {

            var newScope = $rootScope.$new();
            newScope.currentAssessorId = vm.assessment.assessorUserId;
            newScope.baseAssessment = angular.copy(vm.assessment);
            newScope.clientOptions = vm.assessment.job.client.clientOptions;
            newScope.newRecord = false; // Ummm
            newScope.type = "COPY_ASSESSMENT"
            $mdDialog.show({
                // UI of New Job and Copy Assessment are now IDENTICAL (within reason).
                templateUrl: 'app/ui/job/job-update.html',
                scope: newScope,
            })
            .then(function (response) {
                //jobservice.copyAssessment(vm.assessment.job.jobId, vm.assessment.assessmentId, response.copyDrawings, response.purchaseOrder, response.assessorId).then(function (data) {
                //    $state.go("assessment-updateform", { assessmentId: data.data });
                //});
            }, function () {
                //cancelled
            });
        }

        vm.editAssessment = function () {
            var message = "";

            if (vm.assessment.statusCode === "AIssued") {
                message = "The assessment for " + vm.assessment.assessmentProjectDetail.clientJobNumber + " has been completed. Editing the assessment will affect the draft compliance report. Are you sure you want to proceed?";
            }

            // Final Review OR "prcessing option selected"
            if (vm.assessment.statusCode === "AComplete" || vm.assessment.statusCode === "AOptionSelected") {
                message = `A Compliance Option for ${vm.assessment.assessmentProjectDetail.clientJobNumber} has already been selected. Editing the assessment will deselect the Compliance Option. Are you sure you want to proceed?`;
            }

            if (vm.assessment.statusCode === "ACompliance") {
                message = "The energy efficiency compliance report for " + vm.assessment.assessmentProjectDetail.clientJobNumber +
                    " (Client Job Number) is awaiting the client to select a compliance option." +
                    " Editing will remove this assessment from the clients 'compliance options provided' list." +
                    " Are you sure you want to proceed?";
            }

            if(vm.assessment.statusCode === "APreliminaryReview") {
                message = "The assessment will be brought back into an editable state so that you can modify the available compliance options for preliminary approval."
            }

            modalDialog.confirmationDialog("Edit Assessment", message, "Edit", "Cancel", null)
            .then(function success() {
                assessmentservice.makeEditable(vm.assessment.assessmentId)
                .then(function (result) {

                    // Climate Charts Data is generated on page load so don't want to override it.
                    const climateChartData = {...vm.assessment.assessmentProjectDetail.climateChartData};

                    angular.merge(vm.assessment, result.data);

                    // Add Climate Charts Data back in (and also regenerate the JSON).
                    vm.assessment.assessmentProjectDetail.climateChartData = climateChartData;
                    vm.assessment.assessmentProjectDetail.climateChartDataJson = JSON.stringify(climateChartData);

                    // Set on server but didn't seem to be updating with the merge...
                    vm.assessment.statusCode = "AInProgress";
                    vm.assessment.statusDescription = "In Progress";
                    vm.assessment.job.statusCode = "JInProgress";
                    vm.assessment.job.statusDescription = "In Progress";

                    getAssessmentVersionDropdownList();
                    setIsLocked();
                    getJobFileList();
                    setFormPristine();
                    ncc2022Initialise();
                    vm.isBusy = false;
                });
            });
        }

        vm.renumberComplianceOptions = function () {
            common.setItemReferences(vm.assessment.allComplianceOptions, 'itemReference', '', false);
        }

        vm.recertificationAssessment = function () {
            var modalScope = $rootScope.$new();
            modalScope.baseAssessment = vm.assessment;
            modalScope.clientOptions = vm.assessment.job.client.clientOptions;
            modalScope.newRecord = false; // Ummm
            modalScope.type = "RECERTIFY";
            var modalOptions = {
                // Now New Job + Recertify UI/func is EXACTLY identical
                templateUrl: 'app/ui/job/job-update.html',
                scope: modalScope,
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
            }, function () {
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.recertificationAssessment,
                name: 'Recertification',
                desc: 'Recertification',
                icon: 'fa fa-pencil',
                roles: ['assessment_actions__recertify'],
                condition: function() {
                    return !vm.disableAllActions && vm.assessment && (vm.assessment.statusCode === "AIssued"
                        // For instances where a deleted version has no preceding un-superseded versions.
                        || vm.assessment.statusCode === "ASuperseded");
                },
            });
            vm.actionButtons.push({
                onclick: vm.copyAssessment,
                name: 'Copy',
                desc: 'Copy',
                icon: 'fa fa-files-o',
                roles: ['assessment_actions__copyassessment'],
                condition: function(){
                    return !vm.disableAllActions && vm.assessment && vm.assessment.statusCode !== "ASuperseded";
                },
            });
            vm.actionButtons.push({
                onclick: vm.editAssessment,
                name: 'Edit',
                desc: 'Edit',
                icon: 'fa fa-edit',
                roles: ['assessment_actions__editassessment'],
                condition: function() {
                    return !vm.disableAllActions && vm.assessment && (
                        vm.assessment.statusCode === "AIssued" ||
                        vm.assessment.statusCode === "AComplete" ||
                        vm.assessment.statusCode === "APreliminaryReview" ||
                        vm.assessment.statusCode === "ACompliance");
                },
            });
            vm.actionButtons.push({
                onclick: vm.cancelJob,
                name: 'Cancel',
                desc: 'Cancel',
                icon: 'fa fa-ban',
                roles: ['assessment_actions__cancelassessment'],
                condition: function () {
                    return !vm.disableAllActions && vm.assessment && vm.assessment.statusCode != "AIssued" &&
                        vm.assessment.statusCode != "ASuperseded" &&
                        vm.assessment.job != null &&
                        vm.assessment.job.statusCode != "JCancelled";
                },
            });
            vm.actionButtons.push({
                onclick: vm.reinstateJob,
                name: 'Reinstate',
                desc: 'Reinstate',
                icon: 'fa fa-refresh',
                roles: ['assessment_actions__cancelassessment'],
                condition: function () {

                    return !vm.disableAllActions && vm.assessment.job != null && vm.assessment.job.statusCode == "JCancelled";
                },
            });

            vm.actionButtons.push({
                onclick: approvePreliminaryReview,
                name: 'Approve Compliance Options',
                desc: 'Approve Compliance Options',
                tooltip: "The assigned assessor has no recorded signature!",
                icon: 'fa fa-check',
                // TODO: Maybe require 'actions__assessment_approve', i.e. new role?
                roles: ['assessment_actions__approvecompliance'],
                disabled: () => vm.assessment.statusCode !== "APreliminaryReview",
                condition: function () {
                    return !vm.disableAllActions && (vm.assessment && vm.assessment.job &&
                      vm.assessment.statusCode === "APreliminaryReview");
                },
            });

            vm.actionButtons.push({
                onclick: issue,
                name: 'Finalise Report',
                desc: 'Finalise Report',
                tooltip: "The assigned assessor has no recorded signature!",
                icon: 'fa fa-print',
                roles: ['assessment_actions__finalisereport'],
                disabled: () => _.isEmpty(vm.assessment?.assessorUser?.signatureSVGImage) && _.isEmpty(vm.assessment?.assessorUser?.signatureBase64Image),
                condition: function () {

                    // Uncomment this to see why the 'finalise report' button won't appear...
                    // Would be good if there was a popup that maybe indicated why it didn't appear...
                    //console.log("Reqs:");
                    //console.log(vm.assessment);
                    //console.log(vm.assessment.job);
                    //console.log(vm.assessment.job.statusCode);
                    //console.log(vm.assessment.statusCode);
                    //console.log(vm.assessment.assessorUser);
                    //console.log(vm.assessment.assessorUser.signatureSVGImage);

                    return !vm.disableAllActions && (vm.assessment && vm.assessment.job &&
                        vm.assessment.job.statusCode === "JComplete" &&
                        vm.assessment.statusCode !== "ASuperseded" &&
                        vm.assessment.assessorUser);
                },
            });

            vm.actionButtons.push({
                onclick: vm.openOnlineAssessmentPage,
                name: 'Online View',
                desc: 'Online View',
                icon: 'fa fa-globe',
                class: 'action-button-right',
                roles: ['assessment_actions__editassessment'],
                condition: function () {
                    return !vm.disableAllActions && (vm.assessment && vm.assessment.job &&
                        vm.assessment.statusCode == "AIssued" && vm.assessment.certificateNumber != null);
                },
            });
        }

        /** Disables the action buttons if the current user does not have the appropriate permissions) */
        function disableActionButtons() {
            vm.disableAllActions = false;

            const assignedToUser = vm.assessment.assessorUserId === securityservice.currentUser.managedUserId;

            if (!securityservice.immediateCheckRoles('assessment_actions__editassessment'))
                vm.disableAllActions = true;

        }

        setActionButtons();

        vm.safeApply = function () {
            var phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        vm.buildingGeneralTabHasErrors = function() {

            if(vm.assessment?.allComplianceOptions == null)
                return null;

            for(let i = 0; i < vm.assessment.allComplianceOptions.length; i++) {
                const opt = vm.assessment.allComplianceOptions[i];

                const proposedSubTabErrors = vm.buildingGeneralOptionErrors(opt, "proposed");

                if(proposedSubTabErrors != null && proposedSubTabErrors !== "")
                    return proposedSubTabErrors;

                if(referenceIsRequired(opt)) {

                    const referenceSubTabErrors = vm.buildingGeneralOptionErrors(opt, "reference");

                    if(referenceSubTabErrors != null && referenceSubTabErrors !== "")
                        return referenceSubTabErrors;

                }

            }
        }

        vm.buildingGeneralOptionErrors = function(option, buildingString) {

            if(buildingString === 'reference' && !referenceIsRequired(option))
                return "";

            let errorString = "";

            const building = option[buildingString];
            const generalOk = building.projectDescription &&
              building.projectClassification &&
              (building.buildingOrientation != null || building.buildingOrientation === 0) &&
              building.lowestLivingAreaFloorType &&
              (building.masonryWalls !== undefined);

            if(!generalOk)
                errorString += "Data missing from General section. ";

            let spacesFormInvalid = $scope.buildingGeneralForm != null && $scope.buildingGeneralForm["FloorPlanDataForm" + option.optionIndex + buildingString]?.$invalid;
            let roofsFormInvalid = $scope.buildingGeneralForm != null && $scope.buildingGeneralForm["GeneralRoofsDataForm" + option.optionIndex + buildingString]?.$invalid;

            if(spacesFormInvalid === true)
                errorString += "Data missing from Spaces.";

            if(roofsFormInvalid === true)
                errorString += "Data missing from Roofs";

            return errorString;
        }

        function referenceIsRequired(option) {
            return option?.complianceMethod.complianceMethodCode === 'CMPerfSolution' ||
              option?.complianceMethod.complianceMethodCode === 'CMPerfSolutionDTS';
        }

        vm.buildingElementsOptionHasErrors = function (option, buildingString, type) {

            let building = option[buildingString];

            // We have to manually check to make sure there are no construction sections with 0 items
            // in them (because the 'form' doesn't automatically treat this as an 'invalid' state)
            let invalid = false;

            let categoriesToCheck = [];
            if (type == 'construction')
                categoriesToCheck = constructionservice.constructionCategories();
            else if (type == 'opening')
                categoriesToCheck = constructionservice.openingCategories();
            else
                throw "Incorrect type specified!";

            for (let i = 0; i < categoriesToCheck.length; i++) {
                let category = categoriesToCheck[i];

                // If the category has specified to use external data, we don't need to do checks for constructions.
                if (category.allowExternalData == true && building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == true)
                    continue;

                let req = building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()];
                if (req == null || req == false) {
                    // Required == true
                    let found = constructionservice.checkBuildingContainsConstructionOfCategory(category, building);

                    if (!found) {
                        invalid = true;
                        break;
                    }
                }
            }

            if (type == 'construction' && option.servicesNotRequired == false && (building.services == null || building.services.length == 0))
                return "Building is missing required Services.";

            if(type == 'construction') {
                const val = $scope.buildingElementsForm != null && $scope.buildingElementsForm["BuildingElementsOptionForm" + option.optionIndex + buildingString]?.$invalid;
                return val || invalid;
            }else if (type == 'opening') {
                const val = $scope.openingsForm != null && $scope.openingsForm["BuildingElementsOptionForm" + option.optionIndex + buildingString]?.$invalid;
                return val || invalid;
            }

        }

        vm.buildingServicesOptionHasErrors = function (option, building, isReference = false) {

            const buildingString = isReference ? 'reference' : 'proposed';

            if(isReference &&
               option.complianceMethod.complianceMethodCode !== 'CMPerfSolution' &&
               option.complianceMethod.complianceMethodCode !=='CMPerfSolutionDTS') {
                building.servicesFormErrors = false;
                return building.servicesFormErrors;
            }

            // We have to manually check to make sure there are no construction sections with 0 items
            // in them (because the 'form' doesn't automatically treat this as an 'invalid' state)
            let invalid = false;

            let categoriesToCheck = vm.serviceCategoryList;

            for (let i = 0; i < categoriesToCheck.length; i++) {
                let category = categoriesToCheck[i];

                let notRequired = building.categoriesNotRequired[category.serviceCategoryCode.toLowerCase()];

                if (notRequired == null || notRequired === false) {

                    // Therefore required === true
                    let found = building.services?.some(x => x.serviceCategory.serviceCategoryCode === category.serviceCategoryCode);

                    if (!found) {
                        invalid = true;
                        break;
                    }
                }
            }

            // Assign here so we don't have to re-check on the  front end.
            building.servicesFormErrors = $scope.servicesForm != null && $scope.servicesForm["BuildingServicesOptionForm" + option.optionIndex + buildingString]?.$invalid
                                          || invalid;

            return building.servicesFormErrors;

        }

        vm.buildingServicesFormHasErrors = function() {
            // Simply returns true if any of the buildings within contain errors on the service form.
            return vm.assessment?.allComplianceOptions
                ?.some(x => x.proposed.servicesFormErrors === true || x.reference.servicesFormErrors === true);
        }

        // Checks against ALL current building elements subtabs and returns true if any of them
        // are invalid (Alistair did say only allow selection if ALL form data is correct...).
        vm.buildingElementsFormHasErrors = function (type) {

            var invalid = false;

            for (let i = 0; i < vm.assessment?.allComplianceOptions?.length; i++) {

                let option = vm.assessment.allComplianceOptions[i];

                // Loop over all construction categories and see if it is required.
                let proposedIsInvalid = vm.buildingElementsOptionHasErrors(option, 'proposed', type);
                let referenceIsInvalid = false;

                if (option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS')
                    referenceIsInvalid = vm.buildingElementsOptionHasErrors(option, 'reference', type);

                if (proposedIsInvalid || referenceIsInvalid) {
                    invalid = true;
                    break;
                }

                if(invalid)
                    break;

            }
            if (type == 'construction')
                return $scope.buildingElementsForm?.$invalid || invalid;
            else
                return invalid;
        }

        // Checked the selected simulation (or baseline) and return as soon as we hit something which indicates an error.
        // Otherwise we only return false if everything runs through.
        // Note: We only check against the proposed building, not the reference.
        vm.buildingElementsHasErrors = function () {

            if (!vm.assessment || vm.assessment.allComplianceOptions != null && vm.assessment.allComplianceOptions.length === 0) {
                return "Assessment does not exist or all compliance options have been deleted (Critical error).";
            }

            let option = vm.getSelectedComplianceOption();

            if (option == null) {
                // return "A Simulation must be selected before building elements can be verified."
                option = vm.assessment.allComplianceOptions[0];
            }

            // No options at all (Can happen on load in template page)
            if (option == null)
                return true;

            let i = option.optionIndex;

            let formInvalid = ($scope.assessmentform != null && $scope.assessmentform["servicesForm" + i] && $scope.assessmentform["servicesForm" + i].$invalid);

            if (formInvalid) {
                return "Required inputs are missing from the selected option.";
            }

            return "";
        }

        vm.assessmentOutcomesHasErrors = function (skipChecksForSelection) {

            if (!vm.assessment) {
                return "Assessment does not exist (Critical error).";
            }
            if (vm.assessment.performanceRequirementP262Code == null) {
                return "P262 Requirement has not been met.";
            }

            if(skipChecksForSelection)
                return "";

            let found = vm.getSelectedComplianceOption();

            if (found == null && vm.assessment.allComplianceOptions.length > 1)
                return "No compliance option is selected & the baseline is not compliant.";
            else if (found == null)
                return "Baseline is not compliant.";

            return "";
        }

        vm.workingDrawingsHasErrors = function () {

            if (!vm.assessment) {
                return "Assessment does not exist (Critical Error!).";
            }

            if (!vm.assessment.allComplianceOptions) {
                return "There are no compliance options (Critical Error!).";
            }

            // Baseline MUST have drawings uploaded at all times.
            if(vm.assessment.allComplianceOptions[0].assessmentDrawings.length === 0)
                return "Baseline compliance is missing drawings.";

            let selectedComplianceOption = vm.getSelectedComplianceOption();

            // If a compliance option is not selected we still wish to show the generic form errors but nothing specific
            if (selectedComplianceOption == null)
                return false;

            let drawings = selectedComplianceOption.updatedDrawingsRequired
                ? selectedComplianceOption.assessmentDrawings
                : vm.assessment.allComplianceOptions[0].assessmentDrawings;

            let notDeleted = drawings?.filter(d => !d.deleted);

            if (!notDeleted || notDeleted.length == 0) {
                return "There are no drawings uploaded for the required option.";
            }

            return false;
        }

        vm.workingDrawingsOptionHasErrors = function (option) {

            let drawings = option.updatedDrawingsRequired
                ? option.assessmentDrawings
                : vm.assessment.allComplianceOptions[0].assessmentDrawings;

            let notDeletedAndNotHiddenFromClient = drawings?.filter(d => !d.deleted && d.isShownToClient);

            if (!notDeletedAndNotHiddenFromClient || notDeletedAndNotHiddenFromClient.length == 0) {
                return "There are no drawings uploaded for the required option.";
            }

            if($scope?.assessmentform?.WorkingDrawingsForm != null && $scope?.assessmentform?.WorkingDrawingsForm["AssessmentDrawingsOptionForm" + option.optionIndex]?.$invalid)
                return `Option ${option.optionIndex} is missing required data`;

            return false;
        }

        vm.buildingZonesHasErrors = function () {

            if (!vm.assessment) {
                return "Assessment does not exist (Critical error).";
            }

            for (let i = 0; i < vm.assessment.allComplianceOptions?.length; i++) {
                let opt = vm.assessment.allComplianceOptions[i];

                if(opt.proposed.zonesFormErrors === true)
                    return vm.buildingZonesOptionHasErrors(opt, opt.proposed, 'proposed');

                if(opt.reference.zonesFormErrors === true)
                    return vm.buildingZonesOptionHasErrors(opt, opt.reference, 'reference');
            }

            return "";
        }

        /**
        * Checks the given option for building zones (does not check individual data) and returns
        * information about any required data entry conditions.
        *
        * @param {any} option The AssessmentComplianceOption to check
        */
        vm.buildingZonesOptionHasErrors = function (option, building, buildingString) {

            if(buildingString === "reference" &&
                option.complianceMethod.complianceMethodCode !== 'CMPerfSolution' &&
                option.complianceMethod.complianceMethodCode !=='CMPerfSolutionDTS') {
                building.zonesFormErrors = false;
                return building.zonesFormErrors;
            }

            if (building.zones == null || building.zones?.length === 0) {
                building.zonesFormErrors = true;
                return "Option " + option.optionIndex + " " + buildingString + " building zone information is required, but none is present!";
            }

            const form = $scope?.assessmentform?.buildingZonesForm != null ? $scope?.assessmentform?.buildingZonesForm["ZoneListForm" + option.optionIndex + buildingString] : null;

            if (form?.$invalid) {
                building.zonesFormErrors = true;
                return `Option ${option.optionIndex} ${buildingString} building is missing required data`;
            }

            const generalForm = $scope?.assessmentform?.buildingZonesForm != null ? $scope?.assessmentform?.buildingZonesForm["designGeneralForm" + option.optionIndex + buildingString] : null;

            if (generalForm?.$invalid) {
                building.zonesFormErrors = true;
                return `Option ${option.optionIndex} ${buildingString} building is missing required data`;
            }

            const floorPlanForm = $scope?.assessmentform?.buildingZonesForm != null ? $scope?.assessmentform?.buildingZonesForm["FloorPlanDataForm" + option.optionIndex + buildingString] : null;
            if(floorPlanForm?.$invalid) {
                building.zonesFormErrors = true;
                return `Option ${option.optionIndex} ${buildingString} building is missing required data`;
            }

            // if($scope?.assessmentform?.buildingZonesForm.$invalid) {
            //     building.zonesFormErrors = true;
            //     return "There are one or more errors in the general section of the design tab for "
            //     + (option.optionIndex == 0 ? 'Baseline' : 'Option ' + option.optionIndex) + " Simulation";
            // }

            building.zonesFormErrors = false;
            return building.zonesFormErrors;
        }

        vm.clientJobNumberChanged = async function () {
            // Ensure client job number is unique.
            var available = await assessmentservice.checkJobNumberIsAvailable(
                vm.assessment.assessmentProjectDetail.clientJobNumber,
                vm.assessment.assessmentProjectDetail.customClientName,
                vm.assessment.job.client.clientId,
                vm.assessment.job.jobId
            );

            if(!available) {
                modalDialog.infoDialog(
                    "Client Job Number already exists. ",
                    "If you require recertification of an existing assessment, please order the recertification via the Jobs menu. If you are ordering a new assessment, please enter a unique Client Job Number.",
                    "",
                    "Ok",
                    null);

                vm.assessment.assessmentProjectDetail.clientJobNumber = null;
            }
        }

        vm.switchToDMSInput = function() {
            vm.useDMS = true;

            updateDMSValues();
        }

        vm.switchToDecimalInput = function() {
            vm.useDMS = false;

            if (vm.dms.lat && vm.dms.lng) {
                vm.assessment.assessmentProjectDetail.latitude = convertToDecimal(vm.dms.lat);
                vm.assessment.assessmentProjectDetail.longitude = convertToDecimal(vm.dms.lng);
            }
        }

        // When we're already using DMS input, we still wish to update our values
        function updateDMSValues() {
            var lat = vm.assessment.assessmentProjectDetail.latitude;
            var lng = vm.assessment.assessmentProjectDetail.longitude;
            if (lat && lng) {
                vm.dms.lat = convertToDMS(lat, 'lat');
                vm.dms.lng = convertToDMS(lng, 'lng');
            }
        }

        // * ------------------------------------------------------------------------------------------------------------------
        // DMS to Decimal Conversion (and back again)
        // Formula taken and modified from https://www.rapidtables.com/convert/number/degrees-minutes-seconds-to-degrees.html
        // Convert from decimal degrees
        function convertToDMS(dd, type) {
            var sign = "";
            if (dd < 0) { sign = "-"; dd = -dd; }
            var d = Math.floor(dd);
            var m = Math.floor((dd - d) * 60);
            var s = (dd - d - m / 60) * 3600;
            s = Math.round(s * 100) / 100;

            if (s == 60) { s = 0; m++; }
            if (m == 60) { m = 0; d++; }

            var final = sign + d + "\u00B0 " + m + "' " + s + "\"";

            // Determine which direction we're in depending on our sign.
            var direction;
            if (sign == '-') {
                direction = type == "lat"
                    ? "S"
                    : "W"
            } else {
                direction = type == "lat"
                    ? "N"
                    : "E"
            }

            var dms = {
                d: d,
                m: m,
                s: s,
                direction: direction
            }

            return dms;
        }

        // Convert from DMS to decimal.
        function convertToDecimal(dms) {

            // Negate direction if needed.
            if (dms.direction == "S" || dms.direction == "W") {
                dms.d = -dms.d;
            }

            var d = dms.d;
            var m = dms.m;
            var s = dms.s;

            if (d == '') d = '0';
            if (m == '') m = '0';
            if (s == '') s = '0';
            if (m < 0) m = -m;
            if (s < 0) s = -s;
            if (d < 0 || d == '-0') { m = -m; s = -s; }
            var y = parseFloat(d) + parseFloat(m) / 60 + parseFloat(s) / 3600;
            y = roundnum(y, 9);

            return y;

        }

        // Rounds a given number 'x' to 'p' decimal places
        function roundnum(x, p) {
            var i;
            var n = parseFloat(x);
            var m = n.toPrecision(p + 1);
            var y = String(m);
            i = y.indexOf('e');
            if (i == -1) i = y.length;
            var j = y.indexOf('.');
            if (i > j && j != -1) {
                while (i > 0) {
                    if (y.charAt(--i) == '0')
                        y = removeAt(y, i);
                    else
                        break;
                }
                if (y.charAt(i) == '.')
                    y = removeAt(y, i);
            }
            return y;
        }

        function removeAt(s, i) {
            s = s.substring(0, i) + s.substring(i + 1, s.length);
            return s;
        }

        // End DMS to Decimal Conversion
        // ----------------------------------------------------------------------------------------------------------------------

        // Calculates the selected compliance option (or the baseline run) and determines
        // whether we are compliant (Note: P262 must also be compliant). If both are compliant,
        // automatically sets our compliance to true.
        vm.setFinalComplianceMethod = function (saveObject = null) {

            let selectedOption = vm.getSelectedComplianceOption();

            if (selectedOption != null) {
                vm.assessment.complianceStatusCode = (selectedOption.isCompliant === true)
                    ? 'CSAchieved'
                    : 'CSNotAchieved';
            } else {
                vm.assessment.complianceStatusCode = 'CSNotAchieved';
            }

            setP261(vm.assessment.complianceStatusCode);

            // Finally, default back to 'not achieved' if our P262 code is not also satisfied.
            if (vm.assessment.performanceRequirementP262Code !== "P262Satisfied") {
                vm.assessment.complianceStatusCode = 'CSNotAchieved';
            }

            // Mad hack as I want to initiate this from the compliance-options controller and we already had access to
            // this and the watchers were not fired? (they have given up)
            if(saveObject?.saveAssessment === true) {
                console.log("saving assessment)")
                vm.save();
            }
        }

        // Sets P261 requirements description, not whether we _are_ compliant or not.
        function setP261(complianceStatusCode) {

            if (complianceStatusCode === 'CSAchieved') {
                let selectedComplianceOption = vm.getSelectedComplianceOption();

                if (selectedComplianceOption) {
                    switch (selectedComplianceOption.complianceMethod.complianceMethodCode) {
                        case "CMHouseEnergyRating":
                            vm.assessment.p261Description = "Satisfied in accordance with Deemed-to-Satisfy Solution 3.12.0(a)(i)";
                            vm.assessment.performanceRequirementP261Code = "P261Satisfied";
                            break;
                        case "CMPerfSolutionHER":
                            vm.assessment.p261Description = "Satisfied in accordance with Performance Solution A2.2(2)(d)";
                            vm.assessment.performanceRequirementP261Code = "P261Satisfied";
                            break;
                        case "CMPerfSolution":
                            vm.assessment.p261Description = "Satisfied in accordance with Performance Solution V2.6.2.2";
                            vm.assessment.performanceRequirementP261Code = "P261SatisfiedA1";
                            break;
                        case "CMPerfSolutionDTS":
                            vm.assessment.p261Description = "Satisfied in accordance with Performance Solution A2.2(2)(d)";
                            vm.assessment.performanceRequirementP261Code = "P261SatisfiedA1";
                            break;
                        case "CMElementalProv":
                            vm.assessment.p261Description = "Satisfied in accordance with Deemed-to-Satisfy Solution 3.12.0(a)(ii)";
                            vm.assessment.performanceRequirementP261Code = "P261SatisfiedA2";
                            break;
                        case "CMPerfWAProtocolEP":
                        case "CMPerfWAProtocolHER":
                        case "CMPerfELL":
                            vm.assessment.p261Description = "Satisfied in accordance with Performance Solution A2.2(2)(b)(ii)";
                            vm.assessment.performanceRequirementP261Code = "P261SatisfiedB2";
                            break;
                        default:
                            vm.assessment.performanceRequirementP261Code = "P261NotSatisfied";
                            vm.assessment.p261Description = "Not Satisfied";
                            break;
                    }
                }
            } else {
                vm.assessment.performanceRequirementP261Code = "P261NotSatisfied";
                vm.assessment.p261Description = "Not Satisfied";
            }

        }

        // Runs whenever we change the nathers climate zone.
        vm.updateNatHERSData = () => nathersclimatezoneservice.updateNatHERSData(vm.assessment);

        // Variable to track the current tab
        vm.currentMainTab = null;

        // Function to collapse sections when navigating between tabs
        vm.collapseTabSections = function() {
            // Only collapse sections if we're leaving a tab that has expandable sections
            if (!vm.assessment || !vm.assessment.allComplianceOptions) {
                return;
            }

            // Check which tab we're leaving
            if (vm.currentMainTab === 'construction' || vm.currentMainTab === 'openings') {
                // Collapse surfaces and openings when leaving Construction or Openings tab
                vm.assessment.allComplianceOptions.forEach(option => {
                    // Collapse surfaces
                    if (option.proposed && option.proposed.surfaces) {
                        option.proposed.surfaces.forEach(surface => {
                            surface.isExpanded = false;
                        });
                    }

                    // Collapse openings
                    if (option.proposed && option.proposed.openings) {
                        option.proposed.openings.forEach(opening => {
                            opening.isExpanded = false;
                        });
                    }
                });
            } else if (vm.currentMainTab === 'services') {
                // Collapse services when leaving Services tab
                vm.assessment.allComplianceOptions.forEach(option => {
                    if (option.proposed && option.proposed.services) {
                        option.proposed.services.forEach(service => {
                            service.isExpanded = false;
                        });
                    }
                });
            } else if (vm.currentMainTab === 'analytics') {
                // Collapse zones and zone-summary sections when leaving Analytics tab
                vm.assessment.allComplianceOptions.forEach(option => {
                    // Collapse zones
                    if (option.proposed && option.proposed.zones) {
                        option.proposed.zones.forEach(zone => {
                            zone.isExpanded = false;
                        });
                    }

                    // Reset section expansions in zone-summary component
                    const zoneSummaryComponents = document.querySelectorAll('zone-summary');
                    if (zoneSummaryComponents && zoneSummaryComponents.length > 0) {
                        for (let i = 0; i < zoneSummaryComponents.length; i++) {
                            const component = zoneSummaryComponents[i];
                            const componentController = angular.element(component).controller('zoneSummary');
                            if (componentController && componentController.sectionExpansions) {
                                // Reset all section expansions to false
                                Object.keys(componentController.sectionExpansions).forEach(key => {
                                    componentController.sectionExpansions[key] = false;
                                });
                            }
                        }
                    }
                });
            }
        };

        // When we change subtab within the building elements tab, we wish to keep
        // track of what the currently selected tab is, so that if needed we can
        // change whether we show the proposed or reference tab.
        vm.buildingSubtabChanged = function (option) {

            // NOTE: This will not work atm due to the refactor
            // of the building elements into its own component.
            if (option.complianceMethod.requiresReferenceBuilding === false)
                vm.buildingToShow = 'proposed';

            Globals.currentServicesOption = option;

            // Update cached error values when tab changes
            vm.updateCachedGeneralTabErrors();
        }

        // Attempts to convert a form.$error object into a readable list of errors
        // which can be displayed to the user. Requires proper bindings to
        // the input form fields to make sense.
        vm.userFriendlyErrorMessage = function(errors) {

            if (errors == null)
                return "";

            if (Object.keys(errors).length === 0)
                return "";

            let msg = "";

            try {
                msg = unravelError(errors.required, null);

                // Remove final comma and replace with fullstop.
                msg = msg.substring(0, msg.length - 2); // Remove final comma.
                msg += ". ";

                if(msg.length > 40)
                    msg = msg.substring(0, 40) + " [...]. "

                return "There is missing data in the following forms/fields: " + msg;
            }
            catch (error) {
                return "There is missing data within this tab.";
            }

        }

        // Recursively travels through the error tree and tries to display the form
        // errors as nicely as possible. Doesn't do a perfect job as it's just
        // about impossible sometimes to determine whether you should be taking
        // error.$name or unraveling the error when only 1 error is present on a form.
        function unravelError(errors, preceeding) {

            // If there is an array present, unravel the array
            if (Array.isArray(errors) == true) {

                let msg = "";

                errors.forEach(error => {
                    if (error.$error.required.length == 1) {

                        let info = error.$name;

                        if (info == null)
                            info = preceeding.$name;

                        msg += `'${camelCaseToTitleCase(info)}', `;
                    } else {
                        msg += unravelError(error.$error.required, error);
                    }
                });

                return msg;
            }
            else {
                let info = errors.$name;

                if (info == null)
                    info = preceeding.$name;

                return `'${camelCaseToTitleCase(info)}', `;
            }
        }

        // Stolen from https://stackoverflow.com/questions/7225407/convert-camelcasetext-to-sentence-case-text
        function camelCaseToTitleCase(inputString) {
            var result = inputString                                // "__ToGetYourGEDInTimeASongAboutThe26ABCsIsOfTheEssenceButAPersonalIDCardForUser_456InRoom26AContainingABC26TimesIsNotAsEasyAs123ForC3POOrR2D2Or2R2D"
                .replace(/(_)+/g, ' ')                              // " ToGetYourGEDInTimeASongAboutThe26ABCsIsOfTheEssenceButAPersonalIDCardForUser 456InRoom26AContainingABC26TimesIsNotAsEasyAs123ForC3POOrR2D2Or2R2D"
                .replace(/([a-z])([A-Z][a-z])/g, "$1 $2")           // " To Get YourGEDIn TimeASong About The26ABCs IsOf The Essence ButAPersonalIDCard For User456In Room26AContainingABC26Times IsNot AsEasy As123ForC3POOrR2D2Or2R2D"
                .replace(/([A-Z][a-z])([A-Z])/g, "$1 $2")           // " To Get YourGEDIn TimeASong About The26ABCs Is Of The Essence ButAPersonalIDCard For User456In Room26AContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([a-z])([A-Z]+[a-z])/g, "$1 $2")          // " To Get Your GEDIn Time ASong About The26ABCs Is Of The Essence But APersonal IDCard For User456In Room26AContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([A-Z]+)([A-Z][a-z][a-z])/g, "$1 $2")     // " To Get Your GEDIn Time A Song About The26ABCs Is Of The Essence But A Personal ID Card For User456In Room26A ContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([a-z]+)([A-Z0-9]+)/g, "$1 $2")           // " To Get Your GEDIn Time A Song About The 26ABCs Is Of The Essence But A Personal ID Card For User 456In Room 26A Containing ABC26Times Is Not As Easy As 123For C3POOr R2D2Or 2R2D"

            return result;
        }

        // ---------------------
        // Additional "Site Features" list. These are static, so not pulling from the server.
        vm.lotShapeOptions = [
            "Single Residential",
            "Narrow",
            "Battle-axe",
            "Irregular",
            "Rural"
        ];

        vm.northFacingOptions = [
            "Street front",
            "Side boundary",
            "Rear boundary",
            "Ancillary building"
        ];

        vm.facingDescriptionOptions = function(lotShape, facing) {

            if(facing == "Street front")
                return ["Front Yard"];
            else if(facing == "Side boundary" ||
                    facing == "Rear boundary") {

                // "Secondary street (corner block)" not
                // allowed if block shape is battleaxe.
                if(lotShape == "Battle-axe") {
                    return [
                        "Single storey neighbour",
                        "Two storey neighbour",
                        "Three storey neighbour",
                        "High retaining wall (0.5m or higher)",
                        "Vacant block",
                        "Parkland/open space"
                    ];
                } else {
                    return [
                        "Single storey neighbour",
                        "Two storey neighbour",
                        "Three storey neighbour",
                        "High retaining wall (0.5m or higher)",
                        "Vacant block",
                        "Parkland/open space",
                        "Secondary street (corner block)"
                    ];
                }
            }
            else if(facing == "Ancillary building")
                return [
                    "Detached garage/carport",
                    "Ancillary accommodationg (granny flat)",
                    "Shed/workshop"
                ];
            else
                return ["N/A"];

        }

        vm.facingMetresWithinOptions = function(facing) {

            if(facing == "Street front")
                return ["Yes"];
            else
                return ["Yes", "No"];

        }

        const cbt = { bit: true, desc: "Yes" };
        const cbf = { bit: false, desc: "No" };
        vm.cornerBlockOptions = function(lotShape, facingDesc) {

            if(facingDesc && facingDesc == "Secondary street (corner block)")
                return [cbt];
            else if(lotShape && lotShape == "Battle-axe")
                return [cbf];
            else
                return [cbt, cbf];

        }

        /** Clears any of the "Site Features" below the given position. Position 0 being "Block Shape" */
        vm.clearBelow = function(deleteFrom) {

            if(deleteFrom <= 3)
                vm.assessment.assessmentProjectDetail.cornerBlock = null;

            if(deleteFrom <= 2)
                vm.assessment.assessmentProjectDetail.facingWithinXMetres = null;

            if(deleteFrom <= 1)
                vm.assessment.assessmentProjectDetail.facingDescription = null;

            if(deleteFrom <= 0)
                vm.assessment.assessmentProjectDetail.northSideFacing = null;

        }

        /**
         * Returns the 'final' list of drawings for the given option, taking into account
         * where the given option requires updated drawings or not.
         */
        vm.drawingsForOption = function(option) {

            if(option.isBaselineSimulation)
                return option.assessmentDrawings;
            else if(option.updatedDrawingsRequired)
                return option.assessmentDrawings;
            else
                return vm.assessment.allComplianceOptions[0].assessmentDrawings;

        }

        vm.mapImagesNotCaptured = function() {
            // This function is now only used to calculate the cached value
            // It should not be called directly from HTML
            let msg = "";

            if(!vm.hasMapImageData(vm.aerialMap, 'mapImageFile'))
                msg += " Aerial map image not captured.";

            if(!vm.hasMapImageData(vm.siteInfoMap, 'siteMapImageFile'))
                msg += " Map image not captured. ";

            return msg;
        }

        vm.launchGlazingCalcExportModal = function(assessment, option, building) {

            var exportScope = $rootScope.$new();

            exportScope.source = "assessment";
            exportScope.assessment = assessment;
            exportScope.option = option;
            exportScope.building = option[building];

            exportScope.assessmentSoftwareList = vm.allAssessmentSoftwareList;
            exportScope.certificationList = vm.certificationList;
            exportScope.nccClimateZoneList = vm.nccClimateZoneList;
            exportScope.sectorDeterminationList = vm.sectorDeterminationList;

            $mdDialog.show({
                templateUrl: 'app/ui/assessment/glazing-export/export-glazing-calc-modal.html',
                scope: exportScope
            }).then(function (response) {

                // TODO: Do stuff.
            });

        }

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.assessment.worksDescription.worksDescriptionCode);
        }

        // Changing Lot Width disables the manual override on narrowLot
        vm.narrowLotAbortOverride = function() {
            vm.assessment.assessmentProjectDetail.narrowLotIsManuallyOverridden = false;
        }

        // Changing narrowLot enables a manual override that stops it from being auto calculated
        vm.narrowLotManualOverride = function() {
            vm.assessment.assessmentProjectDetail.narrowLotIsManuallyOverridden = true;
        }

        // - NCC 2022 - //
        vm.keyPressedOnInput = function (tableId, event, row, col) {
            // If "Enter" or "Down Arrow"
            if (event.which == 13 || event.which == 40) {
                row++;
            }
            // ELSE IF "Up Arrow"
            else if (event.which == 38) {
                row--;
            }
            if (row >= 0 && row <= 7) {
                document.getElementById(`table_${tableId}_row_${row}_${col}`).focus();
            }
        }

        // Store previous values for House Energy Rating fields
        const previousHerValues = {};

        // Validate House Energy Rating value (must be between 0.0 and 10.0 inclusive)
        vm.validateHouseEnergyRating = function (row) {
            // Find the row identifier
            const tableId = Object.keys(vm.assessment.allComplianceOptions
                .flatMap(option => option.ncc2022 || [])
                .filter(table => table.ncc2022OffsetList.includes(row))
                .map(table => table.id))[0];

            // Find the index of the row
            const rowIndex = vm.assessment.allComplianceOptions
                .flatMap(option => option.ncc2022 || [])
                .find(table => table.id === tableId)
                ?.ncc2022OffsetList.indexOf(row);

            // Create a unique key for this cell
            const cellKey = `${tableId}_${rowIndex}`;

            // Skip validation if value is null or undefined
            if (row.her === null || row.her === undefined) {
                // Store null as the previous value
                previousHerValues[cellKey] = null;
                return;
            }

            // Store the current value before validation
            const currentValue = row.her;

            // Convert to number for validation
            const numValue = parseFloat(currentValue);

            // Check if value is a valid number and within range
            if (isNaN(numValue) || numValue < 0 || numValue > 10) {
                // Show warning popup
                modalDialog.infoDialog(
                    "Invalid House Energy Rating",
                    "House Energy Rating must be between 0.0 and 10.0 inclusive.",
                    "",
                    "OK"
                );

                // We need to set a timeout to allow the digest cycle to complete
                setTimeout(() => {
                    // Restore the previous value (or null if no previous value exists)
                    row.her = previousHerValues[cellKey] !== undefined ? previousHerValues[cellKey] : null;

                    // Force a redraw
                    if (tableId && rowIndex !== undefined) {
                        common.forceBlurInputWithId(`table_${tableId}_row_${rowIndex}_0`);
                    }

                    // Apply changes
                    $scope.$apply();
                }, 0);
            } else {
                // Valid value, store it as the previous value
                previousHerValues[cellKey] = currentValue;
            }
        }

        vm.exportWoh = function(option) {
            // Show "Whole-of-Home Calculator Export"
            var exportScope = $rootScope.$new();
            exportScope.source = "assessment";
            exportScope.mode = "advanced";
            exportScope.assessment = vm.assessment;
            exportScope.option = option;
            exportScope.buildingType = vm.buildingToShow; // Pass the current building type (proposed/reference)
            exportScope.building = option[vm.buildingToShow]; // Use the current building type instead of always using proposed
            $mdDialog.show({
                templateUrl: 'app/ui/whole-of-home/whole-of-home-modal.html',
                scope: exportScope
            });
        }

    }
})();
