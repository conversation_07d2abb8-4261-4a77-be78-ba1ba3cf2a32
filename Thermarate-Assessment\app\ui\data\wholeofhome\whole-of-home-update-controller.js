(function () {
    'use strict';

    const CONTROLLER_ID = 'wohUpdateController';

    angular.module('app')
        .controller(CONTROLLER_ID,
            ['wholeofhomedataservice', wohUpdateController]);

    function wohUpdateController(wholeofhomedataservice) {

        var vm = this;

        vm.processing = false;

        vm.processNewDataset = function (file) {

            if (file == null)
                return;

            vm.processing = true;
            console.log("Processing? ", vm.processing);

            try {
                wholeofhomedataservice.processExcel(file).then(data => {
                    vm.processing = false;
                });

            } catch (e) {
                console.log(e);
                vm.processing = false;
            }

        }

    }
})();