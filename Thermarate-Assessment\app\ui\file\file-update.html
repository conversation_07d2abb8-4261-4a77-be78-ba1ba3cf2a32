<!--TODO: Also not sure if this file is even utilized anymore... -->
<form name="fileform" class="main-content-wrapper" novalidate data-ng-controller='FileUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-action-bar
             data-quick-find-model=''
             data-action-buttons='vm.actionButtons'
             data-refresh-list=''
             data-spinner-busy='vm.isBusy'
             data-new-record=""
             data-new-record-text=""
             data-is-modal="vm.isModal"
             data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
             data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <!--Left Side-->
                <div ng-class="{'flex-100':vm.newRecord==true, 'flex-50':vm.newRecord==false}">
                    <md-card>
                        <md-card-header>
                            File
                        </md-card-header>
                        <md-card-content>

                            <!-- ******** Display Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Display Name</label>
                                <input type="text" name="displayName"
                                       ng-model="vm.file.displayName" md-autofocus
                                       md-maxlength="400"
                                       required />
                                <div ng-messages="fileform.displayName.$error">
                                    <div ng-message="required">Display Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 400.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** File Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>File Name</label>
                                <input type="text" name="fileName"
                                       ng-model="vm.file.fileName"
                                       md-maxlength="400"
                                       required />
                                <div ng-messages="fileform.fileName.$error">
                                    <div ng-message="required">File Name is required.</div>
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 400.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Folder Name ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Folder Name</label>
                                <input type="text" name="folderName"
                                       ng-model="vm.file.folderName"
                                       md-maxlength="400" />
                                <div ng-messages="fileform.folderName.$error">
                                    <div ng-message="md-maxlength">Too many characters entered, max length is 400.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** U R L ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>U R L</label>
                                <input type="text" name="uRL"
                                       ng-model="vm.file.uRL" />
                                <div ng-messages="fileform.uRL.$error">
                                </div>
                            </md-input-container>

                            <!-- ******** Version No ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                                <label>Version No</label>
                                <input type="text" name="versionNo"
                                       ng-model="vm.file.versionNo"
                                       only-numeric />
                                <div ng-messages="fileform.versionNo.$error">
                                </div>
                            </md-input-container>

                            <div class="col-md-12" ng-if="vm.newRecord==false">
                                <div rd-display-created-modified ng-model="vm.file"></div>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>

                <!-- ******** Right Side ******** -->
                <div ng-if="vm.newRecord==false" flex-gt-sm="50">
                    <md-card>
                        <md-card-header>
                            Assessment
                        </md-card-header>
                        <md-card-content>
                            <div class="table-responsive-vertical shadow-z-1">
                                <table class="table table-striped table-hover table-condensed">
                                    <thead>
                                        <tr>
                                            <th class="text-left">Job</th>
                                            <th class="text-left">Status</th>
                                            <th class="text-center">Template</th>
                                            <th class="text-left">Assessment Software</th>
                                            <th class="text-left">N C C Climate Zone</th>
                                            <th class="text-left">Nat H E R S Climate Zone</th>
                                            <th class="text-right">North Offset</th>
                                            <th class="text-left">Building Exposure</th>
                                            <th class="text-right">Conditioned Floor Area</th>
                                            <th class="text-right">Unconditioned Floor Area</th>
                                            <th class="text-right">Attached Garage Floor Area</th>
                                            <th class="text-right">Baseline Heating</th>
                                            <th class="text-right">Baseline Cooling</th>
                                            <th class="text-right">Baseline House Energy Rating</th>
                                            <th class="text-right">Reference Heating</th>
                                            <th class="text-right">Reference Cooling</th>
                                            <th class="text-right">Reference House Energy Rating</th>
                                            <th class="text-right">Proposed Heating</th>
                                            <th class="text-right">Proposed Cooling</th>
                                            <th class="text-right">Proposed House Energy Rating</th>
                                            <th class="text-left">Software File</th>
                                            <th class="text-left">Performance Requirement P261 Code</th>
                                            <th class="text-left">Performance Requirement P262 Code</th>
                                            <th class="text-left">Compliance Status</th>
                                            <th class="text-center">Sealed Exhaust Fans Specified</th>
                                            <th class="text-left">Sealed Exhaust Fans Details</th>
                                            <th class="text-center">Unsealed Exhaust Fans Specified</th>
                                            <th class="text-left">Unsealed Exhaust Fans Details</th>
                                            <th class="text-center">Ceiling Vents Specified</th>
                                            <th class="text-left">Ceiling Vents Details</th>
                                            <th class="text-center">Wall Vents Specified</th>
                                            <th class="text-left">Wall Vents Details</th>
                                            <th class="text-left">Gas Bayonet Point Specified</th>
                                            <th class="text-left">Gas Bayonet Point Details</th>
                                            <th class="text-left">Capped/Crimped Gas Point Specified</th>
                                            <th class="text-left">Capped/Crimped Gas Point Details</th>
                                            <th class="text-center">Unflued Gas Heaters Specified</th>
                                            <th class="text-left">Unflued Gas Heaters Details</th>
                                            <th class="text-center">Flued Gas Heaters Specified</th>
                                            <th class="text-left">Flued Gas Heaters Details</th>
                                            <th class="text-center">Sealed Chimney Specified</th>
                                            <th class="text-center">Sealed Chimney Details</th>
                                            <th class="text-center">Unsealed Chimney Specified</th>
                                            <th class="text-center">Unsealed Chimney Details</th>
                                            <th class="text-center">Sealed Recessed Lights Specified</th>
                                            <th class="text-left">Sealed Recessed Lights Details</th>
                                            <th class="text-center">Unsealed Recessed Lights Specified</th>
                                            <th class="text-left">Unsealed Recessed Lights Details</th>
                                            <th class="text-center">Ceiling Fans Specified</th>
                                            <th class="text-left">Ceiling Fans Details</th>
                                            <th class="text-center">Evaporative Cooling System Specified</th>
                                            <th class="text-left">Evaporative Cooling System Details</th>
                                            <th class="text-left">Certificate Number</th>
                                            <th class="text-left">Cerficate Date</th>
                                            <th class="text-right">Assessor</th>
                                            <th class="text-left">Assessor Signature File</th>
                                            <th class="text-right">M L P D Class1 Building</th>
                                            <th class="text-right">M L P D Class10a Building</th>
                                            <th class="text-right">M L P D Outdoor Living Area</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="item in vm.file.assessments track by $index">
                                            <td data-title="Job" class="text-left">{{::row.clientJobNumber }}</td>
                                            <td data-title="Status" class="text-left">{{::row.description }}</td>
                                            <td data-title="Template" class="text-center">{{item.isTemplate }}</td>
                                            <td data-title="Assessment Software" class="text-left">{{::row.description }}</td>
                                            <td data-title="N C C Climate Zone" class="text-left">{{::row.description }}</td>
                                            <td data-title="Nat H E R S Climate Zone" class="text-left">{{::row.description }}</td>
                                            <td data-title="North Offset" class="text-right">{{item.buildingOrientation }}</td>
                                            <td data-title="Building Exposure" class="text-left">{{::row.description }}</td>
                                            <td data-title="Software File" class="text-left">{{::row.displayName }}</td>
                                            <td data-title="Performance Requirement P261 Code" class="text-left">{{item.performanceRequirementP261Code }}</td>
                                            <td data-title="Performance Requirement P262 Code" class="text-left">{{item.performanceRequirementP262Code }}</td>
                                            <td data-title="Compliance Status" class="text-left">{{::row.description }}</td>
                                            <td data-title="Sealed Exhaust Fans Specified" class="text-center">{{item.sealedExhaustFansSpecified }}</td>
                                            <td data-title="Sealed Exhaust Fans Details" class="text-left">{{item.sealedExhaustFansDetails }}</td>
                                            <td data-title="Unsealed Exhaust Fans Specified" class="text-center">{{item.unsealedExhaustFansSpecified }}</td>
                                            <td data-title="Unsealed Exhaust Fans Details" class="text-left">{{item.unsealedExhaustFansDetails }}</td>
                                            <td data-title="Ceiling Vents Specified" class="text-center">{{item.ceilingVentsSpecified }}</td>
                                            <td data-title="Ceiling Vents Details" class="text-left">{{item.ceilingVentsDetails }}</td>
                                            <td data-title="Wall Vents Specified" class="text-center">{{item.wallVentsSpecified }}</td>
                                            <td data-title="Wall Vents Details" class="text-left">{{item.wallVentsDetails }}</td>
                                            <td data-title="Gas Bayonet Point Specified" class="text-center">{{item.gasBayonetPointSpecified }}</td>
                                            <td data-title="Gas Bayonet Point Details" class="text-left">{{item.gasBayonetPointDetails }}</td>
                                            <td data-title="Capped/Crimped Gas Point Specified" class="text-center">{{item.cappedGasPointSpecified }}</td>
                                            <td data-title="Capped/Crimped Gas Point Details" class="text-left">{{item.cappedGasPointDetails }}</td>
                                            <td data-title="Unflued Gas Heaters Specified" class="text-center">{{item.unfluedGasHeatersSpecified }}</td>
                                            <td data-title="Unflued Gas Heaters Details" class="text-left">{{item.unfluedGasHeatersDetails }}</td>
                                            <td data-title="Flued Gas Heaters Specified" class="text-center">{{item.fluedGasHeatersSpecified }}</td>
                                            <td data-title="Flued Gas Heaters Details" class="text-left">{{item.fluedGasHeatersDetails }}</td>
                                            <td data-title="Sealed Chimney Specified" class="text-center">{{item.sealedChimneySpecified }}</td>
                                            <td data-title="Sealed Chimney Details" class="text-center">{{item.sealedChimneyDetails }}</td>
                                            <td data-title="Unsealed Chimney Specified" class="text-center">{{item.unsealedChimneySpecified }}</td>
                                            <td data-title="Unsealed Chimney Details" class="text-center">{{item.unsealedChimneyDetails }}</td>
                                            <td data-title="Sealed Recessed Lights Specified" class="text-center">{{item.sealedRecessedLightFittingsSpecified }}</td>
                                            <td data-title="Sealed Recessed Lights Details" class="text-left">{{item.sealedRecessedLightFittingsDetails }}</td>
                                            <td data-title="Unsealed Recessed Lights Specified" class="text-center">{{item.unsealedRecessedLightFittingsSpecified }}</td>
                                            <td data-title="Unsealed Recessed Lights Details" class="text-left">{{item.unsealedRecessedLightFittingsDetails }}</td>
                                            <td data-title="Ceiling Fans Specified" class="text-center">{{item.ceilingFansSpecified }}</td>
                                            <td data-title="Ceiling Fans Details" class="text-left">{{item.ceilingFansDetails }}</td>
                                            <td data-title="Evaporative Cooling System Specified" class="text-center">{{item.evaporativeCoolingSystemSpecified }}</td>
                                            <td data-title="Evaporative Cooling System Details" class="text-left">{{item.evaporativeCoolingSystemDetails }}</td>
                                            <td data-title="Certificate Number" class="text-left">{{item.certificateNumber }}</td>
                                            <td data-title="Cerficate Date" class="text-left">{{item.cerficateDate }}</td>
                                            <td data-title="Assessor" class="text-left">{{::row.fullName }}</td>
                                            <td data-title="Assessor Signature File" class="text-left">{{::row.displayName }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </md-card-content>
                    </md-card>
                    <md-card>
                        <md-card-header>
                            Assessment Compliance Option
                        </md-card-header>
                        <md-card-content>
                            <div class="table-responsive-vertical shadow-z-1">
                                <table class="table table-striped table-hover table-condensed">
                                    <thead>
                                        <tr>
                                            <th class="text-left">Assessment</th>
                                            <th class="text-left">Baseline Assessment Method</th>
                                            <th class="text-left">Description</th>
                                            <th class="text-right">Heating</th>
                                            <th class="text-right">Cooling</th>
                                            <th class="text-right">Total</th>
                                            <th class="text-right">House Energy Rating</th>
                                            <th class="text-left">Markup File</th>
                                            <th class="text-left">Software File</th>
                                            <th class="text-center">Selected</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="item in vm.file.assessmentComplianceOptions track by $index">
                                            <td data-title="Assessment" class="text-left">{{::row.sealedExhaustFansDetails }}</td>
                                            <td data-title="Baseline Assessment Method" class="text-left">{{::row.description }}</td>
                                            <td data-title="Description" class="text-left">{{item.description }}</td>
                                            <!-- TODO: Hook these up properly when we split into SimulationBuilding table. -->
                                            <td data-title="Proposed Heating" class="text-right">{{item.proposed.heating }}</td>
                                            <td data-title="Proposed Cooling" class="text-right">{{item.proposed.cooling }}</td>
                                            <td data-title="Proposed Total" class="text-right">{{item.proposed.total }}</td>
                                            <td data-title="House Energy Rating" class="text-right">{{item.houseEnergyRating }}</td>
                                            <td data-title="Markup File" class="text-left">{{::row.displayName }}</td>
                                            <td data-title="Software File" class="text-left">{{::row.displayName }}</td>
                                            <td data-title="Selected" class="text-center">{{item.isSelected }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </md-card-content>
                    </md-card>
                    <md-card>
                        <md-card-header>
                            Assessment Drawing
                        </md-card-header>
                        <md-card-content>
                            <div class="table-responsive-vertical shadow-z-1">
                                <table class="table table-striped table-hover table-condensed">
                                    <thead>
                                        <tr>
                                            <th class="text-left">Assessment</th>
                                            <th class="text-right">Drawing Number</th>
                                            <th class="text-left">Drawing Description</th>
                                            <th class="text-left">Attachment</th>
                                            <th class="text-right">Sheet Number</th>
                                            <th class="text-left">Revision</th>
                                            <th class="text-left">Revision Date</th>
                                            <th class="text-center">Included In Report</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="item in vm.file.assessmentDrawings track by $index">
                                            <td data-title="Assessment" class="text-left">{{::row.sealedExhaustFansDetails }}</td>
                                            <td data-title="Drawing Number" class="text-right">{{item.drawingNumber }}</td>
                                            <td data-title="Drawing Description" class="text-left">{{item.drawingDescription }}</td>
                                            <td data-title="Attachment" class="text-left">{{::row.displayName }}</td>
                                            <td data-title="Sheet Number" class="text-right">{{item.sheetNumber }}</td>
                                            <td data-title="Revision" class="text-left">{{item.revision }}</td>
                                            <td data-title="Revision Date" class="text-left">{{item.revisionDate }}</td>
                                            <td data-title="Included In Report" class="text-center">{{item.isIncludedInReport }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </md-card-content>
                    </md-card>
                    <md-card>
                        <md-card-header>
                            File Version
                        </md-card-header>
                        <md-card-content>
                            <div class="table-responsive-vertical shadow-z-1">
                                <table class="table table-striped table-hover table-condensed">
                                    <thead>
                                        <tr>
                                            <th class="text-left">U R L</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="item in vm.file.fileVersions track by $index">
                                            <td data-title="U R L" class="text-left">{{item.uRL }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </md-card-content>
                    </md-card>
                </div>
            </div>
            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" ng-disabled="fileform.$invalid" ng-show="vm.file.deleted!=true" ng-click="vm.save()">Save</md-button>
                <md-button class="md-raised" ng-show="vm.file.fileId!=null && vm.file.deleted!=true" ng-confirm-click="vm.delete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to delete this record.">Delete</md-button>
                <md-button class="md-raised" ng-show="vm.file.deleted==true" ng-confirm-click="vm.undoDelete()" ng-confirm-condition="true" ng-confirm-message="Please confirm you want to RESTORE this record.">Restore</md-button>
                <md-button class="md-raised" ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>
