﻿
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;

using Microsoft.SqlServer.Types;
using System.Data.SqlTypes;
using System.Text;
using System.Text.RegularExpressions;
using System.IO.Compression;
using System.Threading.Tasks;
using OfficeOpenXml;

namespace TenureExtraction
{
    /// <summary>
    /// Extracts data from the .EPW files and stores in the given SQL table.
    /// The EPW datasets are NOT updated frequently (As in, at this point, it has not been updated
    /// in nearly 2 years). Therefore we do not go to the effort of keeping 2 tables or running this as a 
    /// background process like we do with some of the other datasets (e.g. Tenure).
    /// </summary>
    public class EnergyPlusWeatherExtractor

    {
        private SqlConnection connection;
        private string directory;
        private Logger logger;

        private const string ENERGY_PLUS_WEATHER_TABLE = "RSS_EnergyPlusWeatherData";
        private const string ENERGY_PLUS_WEATHER_HEADER_TABLE = "RSS_EnergyPlusWeatherHeaderData";

        public EnergyPlusWeatherExtractor
            (string directory, string connectionString, LogCallback logCallback = null)
        {
            this.directory = directory;

            connection = new SqlConnection(connectionString);
            connection.Open();

            logger = new Logger(logCallback);
        }

        ~EnergyPlusWeatherExtractor()
        {
            // ? https://social.msdn.microsoft.com/Forums/en-US/b23d8492-1696-4ccb-b4d9-3e7fbacab846/internal-net-framework-data-provider-error-1?forum=adodotnetdataproviders
            //if (connection != null)
            //{
            //    if(connection.State == ConnectionState.Open)
            //        connection.Close();

            //    connection.Dispose();
            //}
        }

        public void Dispose()
        {
            if(connection != null)
            {
                connection.Close();
                connection.Dispose();
            }
        }

        /// <summary>
        /// Run the entire EPW -> SQL extraction process. Handles dropping and creating tables.
        /// 
        /// If stream is NOT provided (the default) we assume that the directory given in the constructor
        /// already contains extracted .epw files.
        /// 
        /// If a stream IS provided, we assume it is a zip file with new data, and extract the contents to
        /// the directory given in the constructor.
        /// </summary>
        public string RunFullProcess(Stream stream = null)
        {
            try
            {
                if(stream != null || this.NewDatasetIsAvailable())
                {
                    this.DropExtractionTable();
                    this.CreateExtractionTable();

                    if(stream != null && Directory.Exists(this.directory))
                        Downloader.CleanDirectory(this.directory);
                    
                    this.Extract(stream);
                    logger.Log("Full extraction of new EnergyPlus Weather dataset completed without errors.");
                    return "Dataset Processed Successfully.";
                }
                else
                {
                    logger.Log("Failed to Extract Dataset! Reason: No new Dataset available!");
                    return "Failed to Extract Dataset! Reason: No new Dataset available!";
                }
                
            }
            catch (Exception e)
            {
                throw e;
            }      
        }

        public bool NewDatasetIsAvailable()
        {
            string[] files = Directory.GetFiles(directory);
            var epwFiles = new List<string>();

            foreach (string f in files)
            {
                if (f.EndsWith(".epw"))
                    epwFiles.Add(f);
            }

            return epwFiles.Count > 0;
        }

        /// <summary>
        /// Drops the extraction table if it already exists. Otherwise does nothing.
        /// </summary>
        public void DropExtractionTable()
        {
            // Note: For now I am just dropping the entire table - this makes it easier in future if Alistair asks for more 
            // info to be extracted or w/e from the DB we don't have to worry about schema updates and so on - we simply modify
            // the creation method and voila.
            try
            {
                SqlCommand cmd = new SqlCommand($"DROP TABLE dbo.{ENERGY_PLUS_WEATHER_TABLE};", connection);
                cmd.ExecuteNonQuery();
                
                cmd = new SqlCommand($"DROP TABLE dbo.{ENERGY_PLUS_WEATHER_HEADER_TABLE};", connection);
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                logger.Log($"DROP TABLE dbo.{ENERGY_PLUS_WEATHER_TABLE} failed with ");
            }
        }

        /// <summary>
        /// Creates the initial table within the database.
        /// </summary>
        public void CreateExtractionTable()
        {
            // Create address table (Derived from .dbf file)
            SqlCommand cmd = new SqlCommand($@"
                CREATE TABLE dbo.{ENERGY_PLUS_WEATHER_TABLE}
                (
                  -- Climate zone is currently used to group rows and link to site address.
                  NatHERSClimateZone                        smallint      NOT NULL,

                  -- Time Step Data (Stuff we actually care about)
                  Year                                      smallint      NOT NULL,
                  Month                                     smallint      NOT NULL,
                  Day                                       smallint      NOT NULL,
                  Hour                                      smallint      NOT NULL,
                  Minute                                    smallint      NOT NULL,

                  DataSourceAndUncertaintyFlags             nvarchar(100) NOT NULL,
                  DryBulbTempC                              decimal(4, 1) NOT NULL,
                  DewPointTempC                             decimal(4, 1) NOT NULL,
                  RelativeHumidity                          decimal(4, 1) NOT NULL, -- File spec says this value should be between 0 and 1, but the actual files seem to be 0-100...
                  AtmosphericStationPressure                int           NOT NULL,
                  ExtraterrestrialHorizontalRadiationWhM2   int           NOT NULL,
                  ExtraterrestrialDirectNormalRadiationWhM2 int           NOT NULL,
                  HorizontalInfraredRadiationFromSkyWhM2    int           NULL,
                  GlobalHorizontalRadiationWhM2             int           NOT NULL,
                  DirectNormalRadiationWhM2                 int           NOT NULL,
                  DiffuseHorizontalRadiationWhM2            int           NOT NULL,
                  GlobalHorizontalIlluminanceLux            int           NOT NULL,
                  DirectNormalIlluminanceLux                int           NOT NULL,
                  DiffuseHorizontalIlluminanceLux           int           NOT NULL,
                  ZenithLuminanceCdM2                       int           NOT NULL,
                  WindDirectionDegrees                      int           NOT NULL,
                  WindSpeedMS                               decimal(6, 2) NOT NULL,
                  TotalSkyCover                             int           NOT NULL,
                  OpaqueSkyCover                            int           NULL,
                  VisibilityKM                              int           NULL,
                  CeilingHeightM                            int           NULL,
                  PresentWeatherObservation                 int           NULL,
                  PresentWeatherCodes                       int           NULL,
                  PrecipitableWaterMM                       int           NOT NULL,
                  AerosolOpticalDepth                       decimal(9, 5) NULL,
                  SnowDepthCM                               int           NULL,
                  DaysSinceLastSnowfall                     int           NULL
                
              ); ", connection);

            cmd.ExecuteNonQuery();
            
            cmd = new SqlCommand($@"
                CREATE TABLE dbo.{ENERGY_PLUS_WEATHER_HEADER_TABLE}
                (
                  -- Climate zone is currently used to group rows and link to site address.
                  NatHERSClimateZone                        smallint      NOT NULL PRIMARY KEY,

                  -- Rows 1-8 of each distinct file contain 'Header Data' as follows.
                  -- Location Data:
                  CityA1                varchar(50)     NOT NULL,
                  StateProvinceRegionA2 varchar(50)     NOT NULL,
                  CountryA3             varchar(30)     NOT NULL,
                  DataSourceA4          varchar(50)     NOT NULL,
                  WmoNumberN1           varchar(30)     NOT NULL,
                  LatitudeN2            decimal(9, 4)   NOT NULL,
                  LongitudeN3           decimal(9, 4)   NOT NULL,
                  TimeZoneN4            decimal(9, 4)   NOT NULL,
                  ElevationN5           decimal(9, 4)   NOT NULL

                  -- Currently we are not worrying about recording this header data but at some point we might.
                  -- Design Conditions
                  -- Typical / Extreme Periods
                  -- Ground Temperatures
                  -- Holidays / Daylight Savings
                  -- Comments 1
                  -- Comments 2
                  -- Data Periods
              ); ", connection);

            cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Extract and parse the given dbfFiles. Can optionally only parse a given number (Used for testing only);
        /// </summary>
        /// <param name="totalCount">OPTIONAL: Limit total rows to count for testing. Mainly for debugging.</param>
        public void Extract(Stream stream = null)
        {

            logger.Log($"EnergyPlus Weather Extraction started to table: {ENERGY_PLUS_WEATHER_TABLE}");

            // Define our bulk insert data
            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("NatHERSClimateZone", typeof(short)); ;
            dataTable.Columns.Add("Year", typeof(short));
            dataTable.Columns.Add("Month", typeof(short));
            dataTable.Columns.Add("Day", typeof(short));
            dataTable.Columns.Add("Hour", typeof(short));
            dataTable.Columns.Add("Minute", typeof(short));
            dataTable.Columns.Add("DataSourceAndUncertaintyFlags", typeof(string));
            dataTable.Columns.Add("DryBulbTempC", typeof(decimal));
            dataTable.Columns.Add("DewPointTempC", typeof(decimal));
            dataTable.Columns.Add("RelativeHumidity", typeof(decimal));
            dataTable.Columns.Add("AtmosphericStationPressure", typeof(int));
            dataTable.Columns.Add("ExtraterrestrialHorizontalRadiationWhM2", typeof(int));
            dataTable.Columns.Add("ExtraterrestrialDirectNormalRadiationWhM2", typeof(int));
            dataTable.Columns.Add("HorizontalInfraredRadiationFromSkyWhM2", typeof(int));
            dataTable.Columns.Add("GlobalHorizontalRadiationWhM2", typeof(int));
            dataTable.Columns.Add("DirectNormalRadiationWhM2", typeof(int));
            dataTable.Columns.Add("DiffuseHorizontalRadiationWhM2", typeof(int));
            dataTable.Columns.Add("GlobalHorizontalIlluminanceLux", typeof(int));
            dataTable.Columns.Add("DirectNormalIlluminanceLux", typeof(int));
            dataTable.Columns.Add("DiffuseHorizontalIlluminanceLux", typeof(int));
            dataTable.Columns.Add("ZenithLuminanceCdM2", typeof(int));
            dataTable.Columns.Add("WindDirectionDegrees", typeof(int));
            dataTable.Columns.Add("WindSpeedMS", typeof(decimal));
            dataTable.Columns.Add("TotalSkyCover", typeof(int));
            dataTable.Columns.Add("OpaqueSkyCover", typeof(int));
            dataTable.Columns.Add("VisibilityKM", typeof(int));
            dataTable.Columns.Add("CeilingHeightM", typeof(int));
            dataTable.Columns.Add("PresentWeatherObservation", typeof(int));
            dataTable.Columns.Add("PresentWeatherCodes", typeof(int));
            dataTable.Columns.Add("PrecipitableWaterMM", typeof(int));
            dataTable.Columns.Add("AerosolOpticalDepth", typeof(decimal));
            dataTable.Columns.Add("SnowDepthCM", typeof(int));
            dataTable.Columns.Add("DaysSinceLastSnowfall", typeof(int));

            // Create object of SqlBulkCopy which help to insert  
            SqlBulkCopy bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.BulkCopyTimeout = 180; // 3 Minutes

            // Map our data table to our DB table
            bulkCopy.DestinationTableName = $"dbo.{ENERGY_PLUS_WEATHER_TABLE}";

            if (stream != null)
                ExtractStreamContents(stream);

            var epwFiles = new List<string>();

            // Find all related .dbf and .shp files within the given folder.
            string[] files = Directory.GetFiles(directory);

            foreach (string f in files)
            {
                if (f.EndsWith(".epw"))
                    epwFiles.Add(f);
            }

            // Loop through each shapefile and retreive the data from them and the corresponding .dbf as needed.
            foreach (string file in epwFiles)
            {

                var ff = File.OpenText(file);

                dataTable.Clear();

                string startOfFileName = file.Remove(0, directory.Length - 0).Replace("\\", "").Substring(0, 2);
                int climateZone = Convert.ToInt32(startOfFileName);

                // The first 8 rows contain header data. We store some of it.
                for (int i = 0; i < 8; i++)
                {
                    var line = ff.ReadLine();

                    if (i == 0)
                    {
                        // Extract 'Location' data.
                        var split = line.Split(',');
                        var headerSql =
                            $@"INSERT INTO dbo.{ENERGY_PLUS_WEATHER_HEADER_TABLE} 
                                    (NatHERSClimateZone, CityA1, StateProvinceRegionA2, CountryA3, DataSourceA4, 
                                     WmoNumberN1, LatitudeN2, LongitudeN3, TimeZoneN4, ElevationN5)
                                VALUES(@NatHERSClimateZone, @CityA1, @StateProvinceRegionA2, @CountryA3, @DataSourceA4, 
                                       @WmoNumberN1, @LatitudeN2, @LongitudeN3, @TimeZoneN4, @ElevationN5)";

                        var insertHeaderCmd = new SqlCommand(headerSql, connection);
                        insertHeaderCmd.Parameters.AddWithValue("@NatHERSClimateZone", climateZone);
                        insertHeaderCmd.Parameters.AddWithValue("@CityA1", split[1]);
                        insertHeaderCmd.Parameters.AddWithValue("@StateProvinceRegionA2", split[2]);
                        insertHeaderCmd.Parameters.AddWithValue("@CountryA3", split[3]);
                        insertHeaderCmd.Parameters.AddWithValue("@DataSourceA4", split[4]);
                        insertHeaderCmd.Parameters.AddWithValue("@WmoNumberN1", split[5]);
                        insertHeaderCmd.Parameters.AddWithValue("@LatitudeN2", Convert.ToDecimal(string.Format("{0:F1}", split[6])));
                        insertHeaderCmd.Parameters.AddWithValue("@LongitudeN3", Convert.ToDecimal(string.Format("{0:F1}", split[7])));
                        insertHeaderCmd.Parameters.AddWithValue("@TimeZoneN4", Convert.ToDecimal(string.Format("{0:F2}", split[8])));
                        insertHeaderCmd.Parameters.AddWithValue("@ElevationN5", Convert.ToDecimal(string.Format("{0:F1}", split[9])));
                        
                        insertHeaderCmd.ExecuteNonQuery();
                    }
                }

                do
                {
                    // This will now be the start of actual data rows.
                    string dataString = ff.ReadLine();
                    var data = dataString.Split(',');

                    DataRow dr = dataTable.NewRow();
                    dr["NatHERSClimateZone"] = climateZone;
                    dr["Year"] = data[Columns.Year];
                    dr["Month"] = data[Columns.Month];
                    dr["Day"] = data[Columns.Day];
                    dr["Hour"] = data[Columns.Hour];
                    dr["Minute"] = data[Columns.Minute];
                    dr["DataSourceAndUncertaintyFlags"] = data[Columns.DataSourceAndUncertaintyFlags];
                    dr["DryBulbTempC"] = data[Columns.DryBulbTempC];
                    dr["DewPointTempC"] = data[Columns.DewPointTempC];
                    dr["RelativeHumidity"] = data[Columns.RelativeHumidity];
                    dr["AtmosphericStationPressure"] = data[Columns.AtmosphericStationPressure];
                    dr["ExtraterrestrialHorizontalRadiationWhM2"] = data[Columns.ExtraterrestrialHorizontalRadiationWhM2];
                    dr["ExtraterrestrialDirectNormalRadiationWhM2"] = data[Columns.ExtraterrestrialDirectNormalRadiationWhM2];
                    dr["HorizontalInfraredRadiationFromSkyWhM2"] = ValueOrNull(data[Columns.HorizontalInfraredRadiationFromSkyWhM2]);
                    dr["GlobalHorizontalRadiationWhM2"] = data[Columns.GlobalHorizontalRadiationWhM2];
                    dr["DirectNormalRadiationWhM2"] = data[Columns.DirectNormalRadiationWhM2];
                    dr["DiffuseHorizontalRadiationWhM2"] = data[Columns.DiffuseHorizontalRadiationWhM2];
                    dr["GlobalHorizontalIlluminanceLux"] = data[Columns.GlobalHorizontalIlluminanceLux];
                    dr["DirectNormalIlluminanceLux"] = data[Columns.DirectNormalIlluminanceLux];
                    dr["DiffuseHorizontalIlluminanceLux"] = data[Columns.DiffuseHorizontalIlluminanceLux];
                    dr["ZenithLuminanceCdM2"] = data[Columns.ZenithLuminanceCdM2];
                    dr["WindDirectionDegrees"] = data[Columns.WindDirectionDegrees];
                    dr["WindSpeedMS"] = data[Columns.WindSpeedMS];
                    dr["TotalSkyCover"] = data[Columns.TotalSkyCover];


                    // From what I've seen so far, these are the only columns that can
                    // sometimes be "null" (Refer to ValueIsNull function).
                    dr["OpaqueSkyCover"] = ValueOrNull(data[Columns.OpaqueSkyCover]);
                    dr["VisibilityKM"] = ValueOrNull(data[Columns.VisibilityKM]);
                    dr["CeilingHeightM"] = ValueOrNull(data[Columns.CeilingHeightM]);
                    dr["PresentWeatherObservation"] = ValueOrNull(data[Columns.PresentWeatherObservation]);
                    dr["PresentWeatherCodes"] = ValueOrNull(data[Columns.PresentWeatherCodes]);
                    dr["PrecipitableWaterMM"] = data[Columns.PrecipitableWaterMM];
                    dr["AerosolOpticalDepth"] = ValueOrNull(data[Columns.AerosolOpticalDepth]);
                    dr["SnowDepthCM"] = ValueOrNull(data[Columns.SnowDepthCM]);
                    dr["DaysSinceLastSnowfall"] = ValueOrNull(data[Columns.DaysSinceLastSnowfall]);

                    dataTable.Rows.Add(dr);

                } while (!ff.EndOfStream);


                ff.Close();
                ff.Dispose();

                // Bulk write data to sql server.
                try
                {
                    bulkCopy.WriteToServer(dataTable);
                } 
                catch (Exception e)
                {
                    logger.Log($"Exception occured while writing to SQL table. Exception: {e.Message}. Stack trace: {e.StackTrace}");                 
                }
            }

            logger.Log($"Finished row extraction and insertion. Creating indexes.");

            // Add index
            var createIndexCmd = new SqlCommand(
                $@" CREATE INDEX si_RSS_EnergyPlusWeatherData_NatHERSClimateZone
                    ON dbo.{ENERGY_PLUS_WEATHER_TABLE}(NatHERSClimateZone);",
                connection);

            createIndexCmd.CommandTimeout = 120; // 2 Minute timeout, should take ~20 seconds.
            createIndexCmd.ExecuteNonQuery();
        }

        /// <summary>
        /// Converts database info into a zip file (as a MemoryStream) where each
        /// climate zone is an individual excel file within the archive.
        /// </summary>
        /// <returns>A memory stream which you must dispose of.</returns>
        public async Task<MemoryStream> CreateZipArchive()
        {   
            // Generate an excel package for each climate zone.
            var excelPackages = new Dictionary<short, ExcelPackage>();

            // We just keep looping until we hit a zone with no response.
            // This way if new zones are added in the future they will be included.
            for (short zone = 1; zone < 1000; zone++)
            {
                var cmd = new SqlCommand($@"
                SELECT *
				FROM dbo.{ENERGY_PLUS_WEATHER_TABLE} 
				WHERE NatHERSClimateZone = @climateZone", this.connection);

                cmd.Parameters.AddWithValue("climateZone", zone);
  
                // Convert the current query into useable data.
                List<EnergyPlusWeatherTimeStepRow> rows = new List<EnergyPlusWeatherTimeStepRow>();

                using (var sqlReader = await cmd.ExecuteReaderAsync())
                {
                    if (sqlReader.HasRows == false)
                        break;

                    rows = this.ConvertQueryToData(zone, sqlReader);
                    sqlReader.Close();
                }

                // Insert our response into an (in-memory) excel package.
                OfficeOpenXml.ExcelPackage package = new OfficeOpenXml.ExcelPackage();
                var sheet = package.Workbook.Worksheets.Add("Sheet 1");

                // Insert header data
                #region Insert Header Data
                sheet.Cells[1, 1 + Columns.Year].Value = "Year"; 
                sheet.Cells[1, 1 + Columns.Month].Value = "Month";
                sheet.Cells[1, 1 + Columns.Day].Value = "Day";
                sheet.Cells[1, 1 + Columns.Hour].Value = "Hour";
                sheet.Cells[1, 1 + Columns.Minute].Value = "Minute";
                sheet.Cells[1, 1 + Columns.DataSourceAndUncertaintyFlags].Value = "Data Source";
                sheet.Cells[1, 1 + Columns.DryBulbTempC].Value = "Dry Bulb Temp C";
                sheet.Cells[1, 1 + Columns.DewPointTempC].Value = "Dew Point Temp C";
                sheet.Cells[1, 1 + Columns.RelativeHumidity].Value = "Relative Humidity";
                sheet.Cells[1, 1 + Columns.AtmosphericStationPressure].Value = "Atmospheric Station Pressure";
                sheet.Cells[1, 1 + Columns.ExtraterrestrialHorizontalRadiationWhM2].Value = "Extraterrestrial Horizontal Radiation WhM2";
                sheet.Cells[1, 1 + Columns.ExtraterrestrialDirectNormalRadiationWhM2].Value = "Extraterrestrial Direct Normal Radiation WhM2";
                sheet.Cells[1, 1 + Columns.HorizontalInfraredRadiationFromSkyWhM2].Value = "Horizontal Infrared Radiation From Sky WhM2";
                sheet.Cells[1, 1 + Columns.GlobalHorizontalRadiationWhM2].Value = "Global Horizontal Radiation WhM2";
                sheet.Cells[1, 1 + Columns.DirectNormalRadiationWhM2].Value = "Direct Normal Radiation WhM2";
                sheet.Cells[1, 1 + Columns.DiffuseHorizontalRadiationWhM2].Value = "Diffuse Horizontal Radiation WhM2";
                sheet.Cells[1, 1 + Columns.GlobalHorizontalIlluminanceLux].Value = "Global Horizontal Illuminance Lux";
                sheet.Cells[1, 1 + Columns.DirectNormalIlluminanceLux].Value = "Direct Normal Illuminance Lux";
                sheet.Cells[1, 1 + Columns.DiffuseHorizontalIlluminanceLux].Value = "Diffuse Horizontal Illuminance Lux";
                sheet.Cells[1, 1 + Columns.ZenithLuminanceCdM2].Value = "Zenith Luminance Cd/M2";
                sheet.Cells[1, 1 + Columns.WindDirectionDegrees].Value = "Wind Direction Degrees";
                sheet.Cells[1, 1 + Columns.WindSpeedMS].Value = "Wind Speed MS";
                sheet.Cells[1, 1 + Columns.TotalSkyCover].Value = "Total Sky Cover";
                sheet.Cells[1, 1 + Columns.OpaqueSkyCover].Value = "Opaque Sky Cover";
                sheet.Cells[1, 1 + Columns.VisibilityKM].Value = "Visibility KM";
                sheet.Cells[1, 1 + Columns.CeilingHeightM].Value = "Ceiling Height M";
                sheet.Cells[1, 1 + Columns.PresentWeatherObservation].Value = "Present Weather Observation";
                sheet.Cells[1, 1 + Columns.PresentWeatherCodes].Value = "Present Weather Codes";
                sheet.Cells[1, 1 + Columns.PrecipitableWaterMM].Value = "Precipitable Water MM";
                sheet.Cells[1, 1 + Columns.AerosolOpticalDepth].Value = "Aerosol Optical Depth";
                sheet.Cells[1, 1 + Columns.SnowDepthCM].Value = "Snow Depth CM";
                sheet.Cells[1, 1 + Columns.DaysSinceLastSnowfall].Value = "Days Since Last Snowfall";
                #endregion 

                // Insert body of data.
                int index = 2;
                foreach(var row in rows)
                {
                    sheet.Cells[index, 1 + Columns.Year].Value = row.Year;
                    sheet.Cells[index, 1 + Columns.Month].Value = row.Month;
                    sheet.Cells[index, 1 + Columns.Day].Value = row.Day;
                    sheet.Cells[index, 1 + Columns.Hour].Value = row.Hour;
                    sheet.Cells[index, 1 + Columns.Minute].Value = row.Minute;
                    sheet.Cells[index, 1 + Columns.DataSourceAndUncertaintyFlags].Value = row.DataSourceAndUncertaintyFlags;
                    sheet.Cells[index, 1 + Columns.DryBulbTempC].Value = row.DryBulbTempC;
                    sheet.Cells[index, 1 + Columns.DewPointTempC].Value = row.DewPointTempC;
                    sheet.Cells[index, 1 + Columns.RelativeHumidity].Value = row.RelativeHumidity;
                    sheet.Cells[index, 1 + Columns.AtmosphericStationPressure].Value = row.AtmosphericStationPressure;
                    sheet.Cells[index, 1 + Columns.ExtraterrestrialHorizontalRadiationWhM2].Value = row.ExtraterrestrialHorizontalRadiationWhM2;
                    sheet.Cells[index, 1 + Columns.ExtraterrestrialDirectNormalRadiationWhM2].Value = row.ExtraterrestrialDirectNormalRadiationWhM2;
                    sheet.Cells[index, 1 + Columns.HorizontalInfraredRadiationFromSkyWhM2].Value = row.HorizontalInfraredRadiationFromSkyWhM2;
                    sheet.Cells[index, 1 + Columns.GlobalHorizontalRadiationWhM2].Value = row.GlobalHorizontalRadiationWhM2;
                    sheet.Cells[index, 1 + Columns.DirectNormalRadiationWhM2].Value = row.DirectNormalRadiationWhM2;
                    sheet.Cells[index, 1 + Columns.DiffuseHorizontalRadiationWhM2].Value = row.DiffuseHorizontalRadiationWhM2;
                    sheet.Cells[index, 1 + Columns.GlobalHorizontalIlluminanceLux].Value = row.GlobalHorizontalIlluminanceLux;
                    sheet.Cells[index, 1 + Columns.DirectNormalIlluminanceLux].Value = row.DirectNormalIlluminanceLux;
                    sheet.Cells[index, 1 + Columns.DiffuseHorizontalIlluminanceLux].Value = row.DiffuseHorizontalIlluminanceLux;
                    sheet.Cells[index, 1 + Columns.ZenithLuminanceCdM2].Value = row.ZenithLuminanceCdM2;
                    sheet.Cells[index, 1 + Columns.WindDirectionDegrees].Value = row.WindDirectionDegrees;
                    sheet.Cells[index, 1 + Columns.WindSpeedMS].Value = row.WindSpeedMS;
                    sheet.Cells[index, 1 + Columns.TotalSkyCover].Value = row.TotalSkyCover;
                    sheet.Cells[index, 1 + Columns.OpaqueSkyCover].Value = row.OpaqueSkyCover;
                    sheet.Cells[index, 1 + Columns.VisibilityKM].Value = row.VisibilityKM;
                    sheet.Cells[index, 1 + Columns.CeilingHeightM].Value = row.CeilingHeightM;
                    sheet.Cells[index, 1 + Columns.PresentWeatherObservation].Value = row.PresentWeatherObservation;
                    sheet.Cells[index, 1 + Columns.PresentWeatherCodes].Value = row.PresentWeatherCodes;
                    sheet.Cells[index, 1 + Columns.PrecipitableWaterMM].Value = row.PrecipitableWaterMM;
                    sheet.Cells[index, 1 + Columns.AerosolOpticalDepth].Value = row.AerosolOpticalDepth;
                    sheet.Cells[index, 1 + Columns.SnowDepthCM].Value = row.SnowDepthCM;
                    sheet.Cells[index, 1 + Columns.DaysSinceLastSnowfall].Value = row.DaysSinceLastSnowfall;
                    index++;
                }

                // Very important to save the package (even tho it's only a stream)
                package.Save();
                excelPackages.Add(zone, package);
                
            }

            
            // This MemoryStream is ultimately where all our data will be stored.
            MemoryStream outputStream = new MemoryStream();

            using (ZipArchive archive = new ZipArchive(outputStream, ZipArchiveMode.Create, true))
            {
                foreach (var kvp in excelPackages)
                {
                    // Add our excel package as an entry within the archive.
                    ExcelPackage package = kvp.Value;
                    var entry = archive.CreateEntry($"{kvp.Key}_WeatherData.xlsx", System.IO.Compression.CompressionLevel.Optimal);

                    using (var entryStream = entry.Open())
                    {
                        package.Stream.Position = 0;
                        package.Stream.CopyTo(entryStream);
                    }
                        
                }
            }

            // Reset stream position to 0 before returning as by defaul it will be at the end.
            outputStream.Position = 0;
            return outputStream;
        }

        private void ExtractStreamContents(Stream stream)
        {
            try
            {
                if (Directory.Exists(this.directory) == false)
                    Directory.CreateDirectory(this.directory);

                const string zipFileName = "/epw.zip";

                // Save contents of stream to a zip file
                var fileStream = File.Create(this.directory + zipFileName);
                stream.Position = 0;
                stream.CopyTo(fileStream);
                fileStream.Close();

                // Open our archive, and check against all files to make sure their isn't an existing
                // file with the same name (causes an exception) before extracting.
                ZipArchive archive = new ZipArchive(stream, ZipArchiveMode.Read, true);
                foreach (ZipArchiveEntry zippedFile in archive.Entries)
                {
                    string completeFileName = Path.Combine(this.directory, zippedFile.Name);
                    string directory = Path.GetDirectoryName(completeFileName);

                    if (zippedFile.Name != "")
                        zippedFile.ExtractToFile(completeFileName, true);
                }

                archive.Dispose();
            }
            catch (Exception e)
            {
                throw new Exception("Error while trying to Unzip downloaded data: " + e.Message);
            }
        }

        /// <summary>
        /// Checks for the presence of cells where ALL data is 9's (or a '.' or '0' ???).
        /// 
        /// From the EPW Spec:
        /// The format is based on TMY2 which is a strict, position-specific format, with missing data filled
        /// with nines.
        /// </summary>
        private object ValueOrNull(string value)
        {
            List<char> aList = new List<char>(value.ToCharArray());

            if (aList.TrueForAll(x => x == '9' || x == '.' || x == '0'))
                return DBNull.Value;
         
            return value;
        }

        private List<EnergyPlusWeatherTimeStepRow> ConvertQueryToData(short zone, SqlDataReader sqlReader)
        {
            List<EnergyPlusWeatherTimeStepRow> rows = new List<EnergyPlusWeatherTimeStepRow>();

            while (sqlReader.Read())
            {

                var r = new EnergyPlusWeatherTimeStepRow();

                r.NatHERSClimateZone = zone;

                r.Year = (short)sqlReader["Year"];
                r.Month = (short)sqlReader["Month"];
                r.Day = (short)sqlReader["Day"];
                r.Hour = (short)sqlReader["Hour"];
                r.Minute = (short)sqlReader["Minute"];
                r.DataSourceAndUncertaintyFlags = sqlReader["DataSourceAndUncertaintyFlags"] as string;

                r.DryBulbTempC = (decimal)sqlReader["DryBulbTempC"];
                r.DewPointTempC = (decimal)sqlReader[nameof(r.DewPointTempC)];
                r.RelativeHumidity = (decimal)sqlReader[nameof(r.RelativeHumidity)];
                r.AtmosphericStationPressure = (int)sqlReader[nameof(r.AtmosphericStationPressure)];
                r.ExtraterrestrialHorizontalRadiationWhM2 = (int)sqlReader[nameof(r.ExtraterrestrialHorizontalRadiationWhM2)];
                r.ExtraterrestrialDirectNormalRadiationWhM2 = (int)sqlReader[nameof(r.ExtraterrestrialDirectNormalRadiationWhM2)];

                r.GlobalHorizontalRadiationWhM2 = (int)sqlReader["GlobalHorizontalRadiationWhM2"];
                r.DirectNormalRadiationWhM2 = (int)sqlReader["DirectNormalRadiationWhM2"];
                r.DiffuseHorizontalRadiationWhM2 = (int)sqlReader["DiffuseHorizontalRadiationWhM2"];

                r.GlobalHorizontalIlluminanceLux = (int)sqlReader[nameof(r.GlobalHorizontalIlluminanceLux)];
                r.DirectNormalIlluminanceLux = (int)sqlReader[nameof(r.DirectNormalIlluminanceLux)];
                r.DiffuseHorizontalIlluminanceLux = (int)sqlReader[nameof(r.DiffuseHorizontalIlluminanceLux)];
                r.ZenithLuminanceCdM2 = (int)sqlReader[nameof(r.ZenithLuminanceCdM2)];
                r.WindDirectionDegrees = (int)sqlReader["WindDirectionDegrees"];
                r.WindSpeedMS = (decimal)sqlReader["WindSpeedMS"];
                r.TotalSkyCover = (int)sqlReader[nameof(r.TotalSkyCover)];
                r.PrecipitableWaterMM = (int)sqlReader["PrecipitableWaterMM"];

                rows.Add(r);

            }

            return rows;
        }
        
        
        private static class Columns
        {
            public static byte Year = 0;
            public static byte Month = 1;
            public static byte Day = 2;
            public static byte Hour = 3;
            public static byte Minute = 4;
            public static byte DataSourceAndUncertaintyFlags = 5;
            public static byte DryBulbTempC = 6;
            public static byte DewPointTempC = 7;
            public static byte RelativeHumidity = 8;
            public static byte AtmosphericStationPressure = 9;
            public static byte ExtraterrestrialHorizontalRadiationWhM2 = 10;
            public static byte ExtraterrestrialDirectNormalRadiationWhM2 = 11;
            public static byte HorizontalInfraredRadiationFromSkyWhM2 = 12;
            public static byte GlobalHorizontalRadiationWhM2 = 13;
            public static byte DirectNormalRadiationWhM2 = 14;
            public static byte DiffuseHorizontalRadiationWhM2 = 15;
            public static byte GlobalHorizontalIlluminanceLux = 16;
            public static byte DirectNormalIlluminanceLux = 17;
            public static byte DiffuseHorizontalIlluminanceLux = 18;
            public static byte ZenithLuminanceCdM2 = 19;
            public static byte WindDirectionDegrees = 20;
            public static byte WindSpeedMS = 21;
            public static byte TotalSkyCover = 22;
            public static byte OpaqueSkyCover = 23;
            public static byte VisibilityKM = 24;
            public static byte CeilingHeightM = 25;
            public static byte PresentWeatherObservation = 26;
            public static byte PresentWeatherCodes = 27;
            public static byte PrecipitableWaterMM = 28;
            public static byte AerosolOpticalDepth = 29;
            public static byte SnowDepthCM = 30;
            public static byte DaysSinceLastSnowfall = 31;
        }


    }

    /// <summary>
    /// A note on optional (marked '?') properties: In reality, it looks like NO EPW rows
    /// contain actual information on these properties. I have included them here
    /// for completeness (and future-proofing...?) but yeah, they're all null.
    /// </summary>
    public class EnergyPlusWeatherTimeStepRow
    {
        public short NatHERSClimateZone { get; set; }

        // Time 
        public short Year { get; set; }
        public short Month { get; set; }
        public short Day { get; set; }
        public short Hour { get; set; }
        public short Minute { get; set; }

        // Misc.
        public string DataSourceAndUncertaintyFlags { get; set; }
        public decimal DryBulbTempC { get; set; }
        public decimal DewPointTempC { get; set; }
        public decimal RelativeHumidity { get; set; }

        // Radiation
        public int AtmosphericStationPressure { get; set; }
        public int ExtraterrestrialHorizontalRadiationWhM2 { get; set; }
        public int ExtraterrestrialDirectNormalRadiationWhM2 { get; set; }
        public int? HorizontalInfraredRadiationFromSkyWhM2 { get; set; }
        public int GlobalHorizontalRadiationWhM2 { get; set; }
        public int DirectNormalRadiationWhM2 { get; set; }
        public int DiffuseHorizontalRadiationWhM2 { get; set; }

        // Other
        public int GlobalHorizontalIlluminanceLux { get; set; }
        public int DirectNormalIlluminanceLux { get; set; }
        public int DiffuseHorizontalIlluminanceLux { get; set; }
        public int ZenithLuminanceCdM2 { get; set; }
        public int WindDirectionDegrees { get; set; }
        public decimal WindSpeedMS { get; set; }
        public int TotalSkyCover { get; set; }
        public int? OpaqueSkyCover { get; set; }
        public int? VisibilityKM { get; set; }
        public int? CeilingHeightM { get; set; }
        public int? PresentWeatherObservation { get; set; }
        public int? PresentWeatherCodes { get; set; }
        public int PrecipitableWaterMM { get; set; }
        public decimal? AerosolOpticalDepth { get; set; }
        public int? SnowDepthCM { get; set; }
        public int? DaysSinceLastSnowfall { get; set; }
    }


    /// <summary>
    /// This is a "minified" version of the full format used for transferring data to
    /// the frontend. We were experiencing performance issues when transferring the full 
    /// payload.The full payload came in at approx 8mb per climate zone. The minified 
    /// version comes in closer to 500kb (WIP..). 150ms to 40ms from the server.
    /// 
    /// NOTE: We are essentially adding to this class AS NEEDED because the actual name of the property
    /// (even when null) contributes to the JSON payload size.
    /// </summary>
    public class EnergyPlusWeatherTimeStepRowMinified
    {
        // Time 
        public short Year { get; set; }
        public short Month { get; set; }
        public short Day { get; set; }
        public short Hour { get; set; }

        // Misc.

        /// <summary>
        /// Dry Bulb Temp C
        /// Used for temperature graph.
        /// </summary>
        public decimal DBTC { get; set; }

        ///// <summary>
        ///// Dew Point Temp C
        ///// </summary>
        //public decimal DPTC { get; set; }

        /// <summary>
        /// Relative Humidity
        /// </summary>
        public decimal RH { get; set; }

        //// Radiation
        public int ASP { get; set; }
        //public int ExtraterrestrialHorizontalRadiationWhM2 { get; set; }
        //public int ExtraterrestrialDirectNormalRadiationWhM2 { get; set; }
        //public int? HorizontalInfraredRadiationFromSkyWhM2 { get; set; }

        /// <summary>
        /// Global Horizontal Radiation (Wh/M2)
        /// </summary>
        public int GHR { get; set; }

        /// <summary>
        /// Direct Normal Radiation (Wh/M2)
        /// Used for "Global Solar" graph.
        /// </summary>
        public int DNR { get; set; }

        /// <summary>
        /// Diffuse Horizontal Radiation (Wh/M2)
        /// </summary>
        public int DHR { get; set; }

        //// Other
        //public int GlobalHorizontalIlluminanceLux { get; set; }
        //public int DirectNormalIlluminanceLux { get; set; }
        //public int DiffuseHorizontalIlluminanceLux { get; set; }
        //public int ZenithLuminanceCdM2 { get; set; }

        /// <summary>
        /// Wind Direction Degrees
        /// </summary>
        public int WDD { get; set; }

        /// <summary>
        /// Wind Speed MS
        /// </summary>
        public decimal WSMS { get; set; }

        //public int TotalSkyCover { get; set; }
        //public int? OpaqueSkyCover { get; set; }
        //public int? VisibilityKM { get; set; }
        //public int? CeilingHeightM { get; set; }
        //public int? PresentWeatherObservation { get; set; }
        //public int? PresentWeatherCodes { get; set; }

        /// <summary>
        ///  Precipitable Water MM
        ///  Used for 'Moisture' graph.
        /// </summary>
        public int PWMM { get; set; }

        //public decimal? AerosolOpticalDepth { get; set; }
        //public int? SnowDepthCM { get; set; }
        //public int? DaysSinceLastSnowfall { get; set; }
    }

    public class EnergyPlusWeatherHeaderDataDto
    {
        public short   NatHERSClimateZone    { get; set; }
      
        public string  CityA1                { get; set; }
        public string  StateProvinceRegionA2 { get; set; }
        public string  CountryA3             { get; set; }
        public string  DataSourceA4          { get; set; }
        public string  WmoNumberN1           { get; set; }
        public decimal LatitudeN2            { get; set; }
        public decimal LongitudeN3           { get; set; }
        public decimal TimeZoneN4            { get; set; }
        public decimal ElevationN5           { get; set; }    
    }


}
