(function () {
    angular
        .module('app')
        .component('buildingConstructionTemplateSelector', {
            bindings: {
                type: '<',          // 'construction' OR 'opening'
                building: '<',      // Actual building.
                buildingType: '<',  // proposed or reference
                option: '<',        // The compliance option
                disabled: '<',      // Disable all inputs?
                showCopyPrevious: '<', // boolean,
                newJob: '<',
                required: '<',        // boolean
            },
            templateUrl: 'app/ui/buildingconstructiontemplates/building-construction-template-selector.html',
            controller: buildingConstructionTemplateSelectorCtrl,
            controllerAs: 'vm'
        });

    buildingConstructionTemplateSelectorCtrl.$inject = [
        '$scope', '$anchorScroll',
        'buildingconstructiontemplateservice', 'constructionservice', 'common'];

    function buildingConstructionTemplateSelectorCtrl($scope, $anchorScroll,
        buildingconstructiontemplateservice, constructionservice, common) {

        // The model for this form 
        var vm = this;

        vm.templateList = [];

        vm.categories = []
        var constructionCategoryPromise = constructionservice.getConstructionCategoryList()
            .then((data) => {
                if(vm.type == 'construction')
                    vm.categories = constructionservice.constructionCategories();
                else if(vm.type == 'opening')
                    vm.categories = constructionservice.openingCategories();
            });

        const blankConstructionTemplate = {

            categoriesNotRequired: {},
            servicesNotRequired: false,
            zoneTypesNotApplicable: {}, // NEEDED?

            services: [],
            openings: [],
            surfaces: [],
            categoriesWithExternalData: {},
            classification: null

        };

        buildingconstructiontemplateservice
            .getAll(vm.type)
            .then(function (data) {
                vm.templateList = data;
            });

        /**
         * Applies the given template, "intelligently" merging the required data as
         * required (e.g. only applies Surface data if this is a surface template, leaves
         * openings alone). Due to frequent changes on which data should live where, we 
         * discard any vestigial data from the template which no longer pplies for the 
         * current rules.
         * 
         * @param {any} type 'surface' or 'opening'
         * @param {any} option The compliance option the building belongs to.
         * @param {any} building The building object
         * @param {any} template The template to apply.
         */
        vm.applyTemplate = async function (option, building, template) {

            await constructionservice.applyTemplate(option, building, template, vm.buildingType);

            // Scroll to the top of our page after a slight delay (otherwise, when changing templates,
            // you can end up halfway down the page)
            setTimeout(() => { $anchorScroll(); }, 500);

        }

        /** 
         *  Deletes all construction elements from the given building for the given type of template
         *  (i.e. use type to specify whether you want to nullify the surface or opening constructions.)
         */
        vm.nullifyTemplateDataForType = function(type, option, building, id) {

            setTimeout(async () => {

                await vm.applyTemplate(option, building, {
                    ...blankConstructionTemplate,
                    templateType: type
                });

                // This is to ensure our UI selector displays properly
                // Reset everything for this particular building.
                building[type + "TemplateId"] = id;
                building[type + "TemplateTitle"] = "";

            }, 151);
        }

        vm.label = function () {

            let label = common.toTitleCase(vm.buildingType);

            if (vm.buildingType == 'reference') {
                label = vm.option.complianceMethod.complianceMethodCode == 'CMPerfSolution'
                    ? 'Reference'
                    : 'Deemed-to-Satisfy';
            }

            let s = `${vm.showCopyPrevious || vm.newJob ? label : ''} ${vm.type == 'construction' ? 'Construction' : 'Opening'} Template`;
            return s;
        }

    }
})();