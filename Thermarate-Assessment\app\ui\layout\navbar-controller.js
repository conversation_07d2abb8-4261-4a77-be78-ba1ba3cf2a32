(function () {
    'use strict';
    // The NavBar Controller manages the main menu bar displayed to the user.
    // It includes the menu items the user has access to, as well as icons for shortcuts, settings, and notifications.
    var controllerId = 'NavbarCtrl';
    angular.module('app').controller(controllerId,
        ['$state', 'config', 'routes', '$rootScope', '$scope', '$filter', '$mdDialog', 'usersettingservice',
          /*'systemversionservice',*/ '$timeout', 'security', 'nccclimatezoneservice', 'assessmentsoftwareservice',
            'certificationservice', navbar]);
    function navbar($state, config, routes, $rootScope, $scope, $filter,$mdDialog, usersettingservice,
          /*systemversionservice,*/ $timeout, security, nccclimatezoneservice, assessmentsoftwareservice,
          certificationservice) {

        var vm = this;
        vm.pendingItemsCollection = [];
        vm.notificationsCollection = [];
        vm.shortcutsCollection = [];

        vm.isCurrent = isCurrent;
        vm.userIconType = "";
        vm.helpBarDetailSetting = "";
        activate();

        ///START toolbar-directive.js logic
        vm.isAuthenticated = security.isAuthenticated;
        vm.login = security.showLogin;
        vm.logout = security.logout;
        vm.account = security.showAccount;
        vm.immediateCheckRoles = security.immediateCheckRoles;
        $scope.$watch(function () {
            return security.currentUser;
        }, function (currentUser) {
            vm.currentUser = currentUser;
        });
        //END toolbar-directive.js logic

        //System Versions
        //systemversionservice.initialise().then(function () {
        //    vm.systemVersion = systemversionservice.getSystemVersion();
        //    vm.configVersion = config.version;
        //});

        //open new tab for report url
        vm.openReport = function (url) {
            window.open(url, '_blank');
        }

        // User Settings
        vm.settingUsers = [
           { code: 'FOH Agent', value: 'FOHAgent' },
           { code: 'BOH User', value: 'BOHUser' },
           { code: 'Admin User', value: 'AdminUser' },
           { code: 'FOH Team Leader', value: 'FOHTeamLeader' }
        ];
        vm.settingSales = [
           { code: 'Show All Details', value: 'ShowAllDetails' },
           { code: 'Show Selected Details', value: 'ShowSelectedDetails' }
        ];

        vm.userTypeSetting = '';
        vm.salesDetailSetting = '';

        usersettingservice.initialise().then(function () {
            vm.userName = security.currentUser.displayName;
            vm.userTypeSetting = usersettingservice.userType();
            vm.salesDetailSetting = usersettingservice.salesDetail();
            vm.helpBarDetailSetting = usersettingservice.helpBarDetail();
            vm.adjustRoles(vm.userTypeSetting);
            vm.userIconType = vm.userTypeSetting;
            if (vm.userIconType == null) {
                vm.userIconType = "FOHAgent";
            }
        });

        //Save change to the user type
        vm.userType= function(userType)
        {
            usersettingservice.setUserTypeData(userType);
            vm.adjustRoles(userType);
            vm.userIconType = userType;
        }
        $rootScope.$on('userSettingsChanged', function (event) {
            vm.helpBarDetailSetting = usersettingservice.helpBarDetail();
        });
        //Adjust roles for test/demo
        vm.adjustRoles = function (userType) {
            // For test environment/demo - show different menu items based on roles.
            if (userType == 'BOHUser') {
                security.addRoles([52]);
                security.removeRoles([50, 53]);
            }
            if (userType == 'FOHAgent') {
                security.addRoles([50]);
                security.removeRoles([52, 53]);
            }
            if (userType == 'AdminUser') {
                security.addRoles([53]);
                security.removeRoles([52, 50]);
            }
        }

        //Save change to the sales type
        vm.selectedSalesType = function(selectedSalesType)
        {
            usersettingservice.setSalesType(selectedSalesType);
        }

        vm.helpBarSave = function (helpBarVal) {
            usersettingservice.setHelpBar(helpBarVal);
        };

        // Activate this controller
        // ---------------------------
        function activate() { getNavRoutes(); }

        // Build the list of navigation routes
        // -------------------------------------------
        // navRoutes will contain the main menu items (where menuParent is '')
        // each individual navRoute will also include any sub routes under subMenu.
        function getNavRoutes() {
            vm.navRoutes = routes.filter(function (r) {
                return r.stateConfig.data && r.stateConfig.data.nav && r.stateConfig.data.menuParent == '' && r.stateConfig.data.excludeFromMenu != true;
            }).sort(function (r1, r2) {
                return Number(r1.stateConfig.data.nav) - Number(r2.stateConfig.data.nav);
            });

            for (var i = 0, l = vm.navRoutes.length; i < l; i++) {
                var obj = vm.navRoutes[i];
                obj.subMenu = routes.filter(function (r) {
                    return r.stateConfig.data && r.stateConfig.data.nav && r.stateConfig.data.menuParent == obj.name && r.stateConfig.data.excludeFromMenu != true;
                }).sort(function (r1, r2) {
                    if (r1.stateConfig.data.nav == r2.stateConfig.data.nav) {
                        var nameA = r1.stateConfig.data.title.toLowerCase(), nameB = r2.stateConfig.data.title.toLowerCase();
                        if (nameA < nameB) //sort string ascending
                            return -1;
                        if (nameA > nameB)
                            return 1;
                        return 0; //default return value (no sorting)
                    }
                    else {
                        return Number(r1.stateConfig.data.nav) - Number(r2.stateConfig.data.nav);
                    }
                });
            }

            for (var i = 0, l = vm.navRoutes.length; i < l; i++) {
                var obj = vm.navRoutes[i];
                for (var j = 0, k = obj.subMenu.length; j < k; j++) {
                    var obj1=obj.subMenu[j];
                    obj1.subMenu1 = routes.filter(function (r) {
                        return r.stateConfig.data && r.stateConfig.data.nav && r.stateConfig.data.menuParent == obj1.name && r.stateConfig.data.excludeFromMenu != true;
                    }).sort(function (r1, r2) {
                        if (r1.stateConfig.data.nav == r2.stateConfig.data.nav) {
                            var nameA = r1.stateConfig.data.title.toLowerCase(), nameB = r2.stateConfig.data.title.toLowerCase();
                            if (nameA < nameB) //sort string ascending
                                return -1;
                            if (nameA > nameB)
                                return 1;
                            return 0; //default return value (no sorting)
                        }
                        else {
                            return Number(r1.stateConfig.data.nav) - Number(r2.stateConfig.data.nav);
                        }
                    });
                }
            }
        }

        vm.jumpTo = function (jumpTo) {
            $rootScope.previousClear = true;
            $state.go(jumpTo);
        }

        // Check if route is the current route for the page.
        function isCurrent(route) {
            var isDropdown = !route.stateConfig.data.noChildren;
            var dropdownClass = '';
            if (isDropdown) { dropdownClass = 'dropdown';}
            if (!route.name || !$state.current || !$state.current.name) {
                return ('' + dropdownClass);
            }
            var menuName = route.name;
            return ($state.current.name === menuName || $state.current.data.menuParent == menuName
                || ( $state.current.data.excludeFromMenu == true && ($rootScope.previousState == menuName || $rootScope.previousMenuParent == menuName)))
                ? ('current ' + dropdownClass) : ('' + dropdownClass);
        }

        vm.createJob = function () {

            var newScope = $rootScope.$new();
            newScope.newRecord = true;
            newScope.type = "NEW_JOB";

            $mdDialog.show({
                // UI of New Job and Copy Assessment are now IDENTICAL (within reason).
                templateUrl: 'app/ui/job/job-update.html',
                scope: newScope,
            }).then(function (response) {

            }, function () {

            });
        }

        vm.showGlazingExportModal = function() {

            // Grab all data required by glazing modal.
            vm.assessmentSoftwareList = [];
            var assessmentSoftwarePromise = assessmentsoftwareservice.getAll()
                .then(function(data){
                    vm.assessmentSoftwareList = data;
                    vm.allAssessmentSoftwareList = data;
                });

            vm.certificationList = [];
            var certificationPromise = certificationservice.getList()
                .then(function (data) {
                    vm.allCertificationList = data.data;
                    vm.certificationList = vm.allCertificationList;
                });

            vm.nccClimateZoneCodeList = [];
            let nccClimateZonePromise = nccclimatezoneservice.getList()
                .then(function(data){
                    vm.nccClimateZoneCodeList = data.data;
                });

            vm.sectorDeterminationList = [];
            let sectorsPromise = certificationservice.getSectorDeterminations()
                .then(data => {
                    vm.sectorDeterminationList = data;
                });

            // Once all data obtained, launch modal.
            Promise.all([assessmentSoftwarePromise, certificationPromise, nccClimateZonePromise, sectorsPromise])
                .then(function () {

                    var exportScope = $rootScope.$new();

                    if(Globals.currentAssessment == null) {

                        exportScope.source = "none";
                        exportScope.mode = "advanced";
                        exportScope.assessment = {};
                        exportScope.option = {};
                        exportScope.building = {};

                        exportScope.assessmentSoftwareList = vm.allAssessmentSoftwareList;
                        exportScope.certificationList = vm.certificationList;
                        exportScope.nccClimateZoneList = vm.nccClimateZoneCodeList;
                        exportScope.sectorDeterminationList = vm.sectorDeterminationList;

                    } else {

                        const assessment = Globals.currentAssessment;
                        const option = assessment.allComplianceOptions.find(x => x.isSelected) || assessment.allComplianceOptions[0];

                        exportScope.source = "assessment";
                        exportScope.assessment = assessment;
                        exportScope.option = option;
                        exportScope.building = option.proposed;

                        exportScope.assessmentSoftwareList = vm.allAssessmentSoftwareList;
                        exportScope.certificationList = vm.certificationList;
                        exportScope.nccClimateZoneList = vm.nccClimateZoneCodeList;
                        exportScope.sectorDeterminationList = vm.sectorDeterminationList;

                    }

                    $mdDialog.show({
                        templateUrl: 'app/ui/assessment/glazing-export/export-glazing-calc-modal.html',
                        scope: exportScope
                    }).then(function (response) {

                        // TODO: Do stuff.

                    });

                });
        }

        vm.showWoHModal = function() {

            // Once all data obtained, launch modal.
            Promise.all([])
                .then(function () {

                    var exportScope = $rootScope.$new();

                    if(Globals.currentAssessment == null) {
                        exportScope.source = "none";
                        exportScope.mode = "advanced";
                        exportScope.assessment = {};
                        exportScope.option = {};
                        exportScope.building = {};
                    } else {

                        // TODO: There might be additional logic needed here regarding isSelected vs isCompliant vs
                        // isBaseline etc.
                        const assessment = Globals.currentAssessment;
                        const option = Globals.currentServicesOption ?? (assessment.allComplianceOptions.find(x => x.isSelected) || assessment.allComplianceOptions[0]);

                        exportScope.source = "assessment";
                        exportScope.mode = "advanced";
                        exportScope.assessment = assessment;
                        exportScope.option = option;
                        exportScope.building = option.proposed;

                    }

                    $mdDialog.show({
                        templateUrl: 'app/ui/whole-of-home/whole-of-home-modal.html',
                        scope: exportScope
                    }).then(function (response) {

                        // TODO: Do stuff.

                    });

                });
        }

        vm.isCollapsed = true;
        $scope.$on('$stateChangeSuccess', function () {
            vm.isCollapsed = true;
        });

    };
})();