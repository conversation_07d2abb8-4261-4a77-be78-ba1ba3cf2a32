<md-card layout-margin>

    <md-card-content ng-form="AssessmentDrawingsOptionForm{{vm.option.optionIndex}}">

        <!-- For simulation OPTIONS which do not require updated drawings, we display the baseline drawings
             but in disabled and 'greyed out' fashion. -->
        <div ng-if="!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired"
             style="position: absolute; left: 0; right: 0; top: 0; bottom: 0;
                                                            background-color: rgba(255, 255, 255, 0.8); z-index: 777;
                                                            display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <h1>Option {{vm.option.optionIndex }} does not require updated drawings.</h1>
            <h3>Baseline drawings will be used for all reports.</h3>
        </div>

        <!-- Upper "Toolbar" -->
        <div class="drawings-toolbar">

            <div>
                <md-button class="md-raised md-primary"
                           style="width: 130px; margin: 0;"
                           ng-click="vm.launchBulkEdit(vm.option)"
                           ng-disabled="vm.disabled || !(vm.selectAllCheckboxState || vm.bulkSelectCheckboxIsIndeterminate)">
                    Bulk Edit
                </md-button>
            </div>

            <!-- Current / Archived button -->
            <div style="justify-self: end; display: flex; grid-gap: 0; padding: 0;">
                <md-button enabled
                           style="margin: 0; padding: 0;"
                           ng-click="vm.switchArchiveState(false)"
                           class="new-left md-raised"
                           ng-class="{'is-active' : vm.showArchiveTable == false}"
                           ng-style="{
                                    'background-color': vm.showArchiveTable == true ? 'rgb(247, 247, 247)' : 'rgb(220, 220, 220)',
                                    'z-index': vm.showArchiveTable == true ? '99' : '30'
                                }">
                    Current
                </md-button>
                <md-button enabled
                           style="margin: 0; padding: 0; background-color: rgb(247, 247, 247);"
                           ng-click="vm.switchArchiveState(true)"
                           class="new-right md-raised"
                           ng-class="{'is-active' : vm.showArchiveTable == true}"
                           ng-style="{
                                    'background-color': vm.showArchiveTable == false ? 'rgb(247, 247, 247)' : 'rgb(220, 220, 220)',
                                    'z-index': vm.showArchiveTable == false ? '99' : '30'
                                }">
                    Archived
                </md-button>
            </div>

        </div>

        <!-- Main drawing table -->
        <fieldset id="working-drawings-tab-fieldset"
                  ng-disabled="vm.disabled"
                  class="elements-body">

            <table class="elements-table elements-table-padding-l">
                <thead>

                <tr>
                    <th style="width: 40px;">
                        <md-checkbox id="allCheckbox"
                                     ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                     ng-model="vm.selectAllCheckboxState"
                                     md-indeterminate="vm.bulkSelectCheckboxIsIndeterminate"
                                     ng-click="vm.selectAllCheckboxes(vm.option, vm.selectAllCheckboxState)">
                        </md-checkbox>
                    </th>
                    <th style="width: 25px;">Number</th>
                    <th style="min-width: 240px; text-align: left;">Drawing Description</th>
                    <th style="width: 100px;">Size</th>
                    <th style="width: 70px;">Orientation</th>
                    <th style="min-width: 230px; text-align: left;">Attachment</th>
                    <th style="width: 120px;">Uploaded By</th>
                    <th style="width: 140px;">Upload Date</th>
                    <th style="width: 60px;">Sheet Number</th>
                    <th style="width: 40px;">Revision</th>
                    <th style="width: 80px;">Revision Date</th>
                    <th style="width: 40px;">Stamp</th>
                    <th style="width: 40px;">Report</th>
                    <th style="width: 40px;">Portal</th>

                    <!-- Actions -->
                    <th class="text-left" style="width: 40px;"></th>
                </tr>
                </thead>
                <tbody>

                <tr ng-if="vm.determineCurrentDrawingsToShow(vm.showArchiveTable).length === 0">
                    <td colspan="999" style="text-align: center; color: darkgrey;">
                        <h2 style="margin: 40px 0 20px 0;">
                            {{ 
                            !vm.showArchiveTable
                                ? 'No drawings uploaded. Use the upload area below to upload drawings for this compliance option.'
                                : 'No archived drawings present.' 
                            }}
                        </h2>
                    </td>
                </tr>

                <tr ng-repeat="drawing in vm.determineCurrentDrawingsToShow(vm.showArchiveTable) track by $index "
                    lr-drag-src="assessmentDrawings"
                    lr-drop-target="assessmentDrawings"
                    lr-drag-data="vm.option.assessmentDrawings"
                    lr-drop-success="vm.renumberWorkingDrawings()"
                    lr-match-property="assessmentDrawingId"
                    lr-match-value="{{drawing.assessmentDrawingId}}"
                    lr-index="vm.option.assessmentDrawings.indexOf(drawing)"
                    ng-click="vm.showStampDrawings(drawing, vm.option)"
                    class="clickable">

                    <!-- Checkbox -->
                    <td data-title="Selected" lr-no-drag>
                        <md-checkbox ng-model="drawing.checkboxSelected"
                                     ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                     ng-click="vm.stopClickThrough($event);"
                                     ng-change="vm.updateBulkSelectStatus(vm.option);">
                        </md-checkbox>
                    </td>

                    <!-- Drawing Number -->
                    <td data-title="Drawing Number" class="text-right">
                            <input ng-if="drawing.processingProgress < 0 || drawing.processingProgress === 100"
                                   ng-model="drawing.drawingNumber"
                                   class="lightweight"
                                   style="width: 30px;"
                                   ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                   ng-click="vm.stopClickThrough($event)">
                    </td>

                    <!-- Description -->
                    <td data-title="Drawing Description" lr-no-drag ng-click="vm.stopClickThrough($event)">
                            <input type="text" 
                                   class="lightweight"
                                   style="text-align: left; width: 100%;"
                                   name="WDDrawingDescription{{$index}}"
                                   ng-required="true"
                                   ng-model="drawing.drawingDescription"
                                   ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                   ng-click="vm.stopClickThrough($event)" />
                    </td>

                    <!-- Size -->
                    <td data-title="Size" class="text-right" lr-no-drag>
                        <md-select name="WDSize{{$index}}"
                                   class="center-input"
                                   ng-model="drawing.pageSize"
                                   ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                   ng-click="vm.stopClickThrough($event)">
                            <md-option ng-value="pageSizeListItem.id"
                                       ng-repeat="pageSizeListItem in vm.pageSizeList track by pageSizeListItem.id">
                                {{pageSizeListItem.description}}
                            </md-option>
                        </md-select>
                        <div ng-messages="AssessmentDrawingsOptionForm['WDSize'+$index].$error">
                        </div>
                    </td>

                    <!-- Orientation -->
                    <td>
                        <img ng-if="vm.pageIsHorizontal(drawing) === 'true'"
                             style="transform: rotate(-90deg)"
                             src="/content/feather/file.svg" />
                        <img ng-if="vm.pageIsHorizontal(drawing) === 'false'"
                             src="/content/feather/file.svg" />
                    </td>

                    <!-- Attachment -->
                    <td data-title="Attachment" 
                        lr-no-drag>

                        <!-- Upload + Processing Progress bar -->
                        <div class="loading-bar-background"
                             style="height:max-content; min-height: 40px; display: grid; grid-template-areas: 'cell'; align-items: center;"
                             ng-click="vm.downloadFile(drawing.attachment); vm.stopClickThrough($event);">

                            <!-- Attached File view button -->
                            <button ng-if="drawing.processingProgress === 100" 
                                    class="lightweight-button"
                                    style="grid-area: cell; text-align:left; align-items: center;">
                                {{drawing.attachment.fileName}}
                            </button>

                            <!-- ERROR state -->
                            <div ng-if="drawing.processingProgress === -1" 
                                 class="loading-bar-processed"
                                 style="background-color: orange; grid-area: cell; height: 100%; width: 100%;">
                            </div>
                            <span ng-if="drawing.processingProgress === -1"  
                                  style="grid-area: cell;">
                                Unknown Error: Process Failed
                            </span>

                            <!-- COMPLETE or PROCESSING state -->
                            <div ng-if="drawing.processingProgress != -1"
                                 class="loading-bar-processed"
                                 ng-class="{'loading-bar-fade' : drawing.processingProgress === 100 }"
                                 style="grid-area: cell; height: 100%; width: {{drawing.processingProgress}}%;">
                            </div>
                            <span ng-if="drawing.processingProgress >= 0  && drawing.processingProgress < 100"
                                  style="grid-area: cell;">{{drawing.processingStatus}}</span>

                            <!-- Spinny  circle -->
                            <div ng-if="drawing.processingProgress >= 0  && drawing.processingProgress < 100"
                                 class="drawing-loader"
                                 style="grid-area: cell; justify-self: end; margin-right: 8px;">
                            </div>
                        </div>

                        <!-- FAILED UPLOADING NOTIFICATION -->
                        <div ng-if="drawing.processingProgress < 0"
                             style="display: grid; grid-template-columns: 1fr; align-items: center;">

                        </div>

                    </td>

                    <!-- Uploaded By -->
                    <td data-title="Uploaded By" class="text-left" lr-no-drag>
                        <md-input-container
                                class="md-block readonly-data kindly-remove-error-spacer vertically-condensed-ex center-input"
                                flex="100"
                                ng-click="vm.stopClickThrough($event)">
                            <span class="read-only-field-value">
                                {{drawing.attachment.createdByName}}
                            </span>
                        </md-input-container>
                    </td>

                    <!-- Upload Date-->
                    <td data-title="Upload Date" class="text-left" lr-no-drag>
                        <md-input-container
                                class="md-block readonly-data kindly-remove-error-spacer vertically-condensed-ex center-input"
                                flex="100"
                                ng-click="vm.stopClickThrough($event)">
                            <span class="read-only-field-value">
                                {{drawing.attachment.createdOn | date: 'dd/MM/yyyy hh:mm a'}}
                            </span>
                        </md-input-container>
                    </td>

                    <!-- Sheet Number -->
                    <td lr-no-drag ng-click="vm.stopClickThrough($event)">
                        <input type="text"
                               class="lightweight"
                               name="WDSheetNumber{{$index}}"
                               ng-required="true"
                               ng-model="drawing.sheetNumber" ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)" />
                    </td>

                    <!-- Revision -->
                    <td data-title="Revision" ng-click="vm.stopClickThrough($event)" lr-no-drag>
                        <input type="text"
                               class="lightweight"
                               name="WDRevision{{$index}}"
                               ng-model="drawing.revision"
                               ng-required="true"
                               ng-change="vm.drawingUpdated(drawing, 'revision', vm.option)"
                               ng-model-options="{updateOn:'blur'}" ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)" />
                    </td>

                    <!-- Revision Date -->
                    <td data-title="Revision Date" class="text-left" lr-no-drag>
                        <md-input-container flex="100" 
                                            class="md-block vertically-condensed kindly-remove-error-spacer modal-datepicker"
                                            ng-click="vm.stopClickThrough($event)">
                            <md-datepicker
                                    class="hiddenCalendar"
                                    ng-model="drawing.revisionDate"
                                    ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                    ng-change="vm.drawingUpdated(drawing, 'revisionDate', vm.option)"
                                    ng-required="true"
                                    md-placeholder="Select a Date" />
                        </md-input-container>
                    </td>

                    <!-- Stamp -->
                    <td>
                        <!-- Stamp Placed? (onClick triggers from row click) -->
                        <div class="no-focus"
                             ng-class="{
                                           'stamp-placed' : drawing.isStamped==true,
                                           'stamp-not-placed' : (drawing.isStamped!=true) || (vm.assessment.statusCode=='AIssued' || vm.assessment.statusCode=='ASuperseded'),
                                           'opacity-0' : drawing.isIncludedInReport != true || drawing.attachment == null || drawing.toStamp != true || !vm.clientOptions.stampDrawings
                                       }"
                             title="Click here to place stamp on drawing."
                             ng-disabled="vm.disableStampFunctionality() || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)">
                        </div>
                    </td>

                    <!-- Report -->
                    <td>
                        <img ng-if="drawing.isIncludedInReport && vm.clientOptions.includeDrawingsInReport"
                             src="/content/images/link.png"
                             style="width: 26px; height: auto; margin-top: 6px;"
                             ng-style="{'opacity': vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired) ? 0.45 : 1 }"
                             ng-click="vm.toggleIncludeInReport($event, drawing)" />
                        <img ng-if="!drawing.isIncludedInReport || !vm.clientOptions.includeDrawingsInReport"
                             src="/content/images/link-unlink.png"
                             style="width: 26px; height: auto; margin-top: 6px;"
                             ng-style="{'opacity': vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired) ? 0.45 : 1 }"
                             ng-click="vm.toggleIncludeInReport($event, drawing)" />
                    </td>

                    <!-- Portal -->
                    <td>
                        <img ng-if="drawing.isShownToClient"
                             src="/content/images/eye-open.png"
                             style="width: 26px; height: auto; margin-top: 6px;"
                             ng-style="{'opacity': vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired) ? 0.45 : 1 }"
                             ng-click="vm.toggleShowToClient($event, drawing)" />
                        <img ng-if="!drawing.isShownToClient"
                             src="/content/images/eye-shut.png"
                             style="width: 26px; height: auto; margin-top: 6px; transform: scaleX(-1);"
                             ng-style="{'opacity': vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired) ? 0.45 : 1 }"
                             ng-click="vm.toggleShowToClient($event, drawing)" />
                    </td>

                    <!-- Actions -->
                    <td ng-click="vm.stopClickThrough($event);"
                        data-title="Actions"
                        lr-no-drag>

                        <div style="display: flex; justify-content: center; align-items: center;">
                            <!-- 'More' button w/ Popup -->
                            <md-menu ng-show="!vm.disabled">

                                <!-- Initial '...' button, which launches options -->
                                <img md-menu-origin
                                     class="clickable"
                                     ng-click="$mdOpenMenu()"
                                     src="/content/feather/more-horizontal.svg"
                                     ng-disabled="vm.disabled"/>
                                <md-menu-content>

                                    <md-menu-item>
                                        <!-- Archive / Reinstate Buttons-->
                                        <md-button ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                                   ng-if="!vm.showArchiveTable && drawing.assessmentDrawingId"
                                                   ng-click="vm.archiveAssessmentDrawing($event, drawing)">
                                            Archive
                                        </md-button>
                                        <md-button ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                                                   ng-if="vm.showArchiveTable && drawing.assessmentDrawingId"
                                                   ng-click="vm.reinstateAssessmentDrawing($event, drawing)">
                                            Reinstate
                                        </md-button>
                                    </md-menu-item>

                                    <!-- Stamp On/Off -->
                                    <md-menu-item ng-if="drawing.isIncludedInReport">
                                        <md-button ng-disabled="vm.disabled"
                                                   ng-click="drawing.toStamp = !drawing.toStamp">
                                            Stamp {{drawing.toStamp ? 'Off' : 'On'}}
                                        </md-button>
                                    </md-menu-item>

                                    <md-menu-divider></md-menu-divider>

                                    <md-menu-item>
                                        <!-- Delete button -->
                                        <md-button ng-click="vm.removeAssessmentDrawing($event, drawing, vm.option)"
                                                   ng-show="!vm.disabled || !(!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)">
                                            <span style="color: orangered">Delete</span>
                                        </md-button>
                                    </md-menu-item>

                                </md-menu-content>
                            </md-menu>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </fieldset>

        <!-- Upload button / drag n drop form area -->
        <div ng-if="vm.option.saveLoading"
             class="drawing-loader"
             style="width: min-content; margin: 30px auto">
        </div>
        <div ng-show="!vm.option.saveLoading && vm.showArchiveTable == false"
             redi-show-by-roles="['assessment_page_(tabs/sub-tabs)__drawings__edit']"
             class="drawing-upload-area clickable"
             style="height: 130px;"
             ngf-drop="vm.uploadFile($files)"
             ngf-select="vm.uploadFile($files)"
             ngf-pattern="'.pdf'"
             ngf-drag-over-class="'drawing-upload-file-over'"
             ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
             accept=".pdf"
             ngf-multiple="true"
        >

            <!-- Notification of upload in progress -->
            <div ng-if="vm.allUploadingFiles.length > 0"
                 style="display: grid; grid-template-columns: 1fr; align-items: center; justify-items: center;">

                <!-- Upload + Processing Progress bar -->
                <h3 style="margin-top: 0;">Uploading...</h3>
                <div class="loading-bar-background"
                     style="width: 400px;"
                     ng-click="vm.stopClickThrough($event)">
                    <div class="loading-bar-processed"
                         style="height: 100%; width: {{vm.drawingsUploadProgress()}}%;">
                    </div>
                </div>
            </div>

            <!-- Actual drawing upload section -->
            <div ng-show="vm.allUploadingFiles.length == 0 && (!vm.disabled || !(!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired))">

                <div style="text-align: center;">
                    <h3 style="margin-top: 0;">Upload Drawings</h3>
                    <button class="md-block feather-icon-button"
                            style="transform: scale(1.2);"
                            ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)"
                            title="Click to upload file.">
                        <img src="/content/feather/upload.svg" />
                    </button>
                    <div style="margin-top: 16px; font-size: 11px;">Click or Drag 'n' Drop</div>
                </div>
            </div>

        </div>

        <!-- Additional action buttons -->
        <div ng-if="!vm.showArchiveTable"
             style="display: flex; justify-content: space-between; margin-top: 20px;">
            <div>
                <md-button class="md-raised"
                           ng-show="vm.assessment.deleted != true"
                           ng-click="vm.importDrawings(vm.option)"
                           ng-disabled="vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)">
                    Import
                </md-button>
                <md-button class="md-raised"
                           ng-show="vm.assessment.assessmentId != null && !vm.disableStampFunctionality()"
                           ng-click="vm.stampDrawings(vm.option)"
                           ng-disabled="vm.disableStampFunctionality() || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)">
                    Stamp Drawings
                </md-button>
            </div>

            <div>
                <md-button class="md-raised"
                           ng-if="vm.option.assessmentDrawings.length"
                           ng-click="vm.openAllCurrentDrawings(vm.option)"
                           ng-disabled="false">
                    Open All
                </md-button>

                <md-button class="md-raised"
                           ng-if="vm.option.assessmentDrawings.length"
                           ng-click="vm.downloadAllCurrentDrawings(vm.option)"
                           ng-disabled="false">
                    Download All
                </md-button>
            </div>

        </div>

    </md-card-content>
</md-card>

<style>
    .drawings-toolbar {
        display: grid;
        grid-template-columns: 1fr 1fr;
        justify-content: space-between;

        padding: 10px 20px;
        background-color: rgba(0, 0, 0, 3%);
        border-radius: 10px;
        margin: 10px 0;

        grid-gap: 0;
    }

    .pending-drawings-container {

        padding: 10px 20px;
        background-color: rgba(0, 0, 0, 3%);
        border-radius: 10px;
        margin: 10px 0;
    }

    .pending-drawings-array {
        display: grid;
        grid-template-columns: 1fr;
        grid-row-gap: 5px;
        align-items: center;

        font-size: 16px;
        margin-bottom: 16px;
    }

    .pending-drawings-row {
        display: grid;
        grid-template-columns: 1fr 3fr;
        align-items: center;
        width: 70%;
    }

    .loading-bar-background {
        height: 12px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 3%);

    }

    .loading-bar-processed {
        height: 12px;
        width: 0;
        border-radius: 6px;
        background-color: rgba(139, 195, 74, 80%);
        transition: 1000ms;
    }

    .loading-bar-fade {
        background-color: transparent !important;
    }

    .drawing-upload-area {
        padding: 30px 20px;
        background-color: rgba(0, 0, 0, 3%);
        border: 1px solid transparent;
        border-radius: 10px;
        margin: 10px 0;
        display: grid;
        align-items: center;
        justify-items: center;

        transition: 1s;
    }

    .drawing-upload-file-over {
        border: 1px solid rgb(139,195,74);
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.6);
    }

    .drawing-upload-area:hover {
        border: 1px solid rgb(139,195,74);
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.6);
    }

    .drawing-loader {

    }
    .drawing-loader:after {
        content: " ";
        display: block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        border: 2px solid var(--thermarate-green);
        border-color: var(--thermarate-green) transparent var(--thermarate-green) transparent;
        animation: drawing-loader 2s linear infinite;
    }
    @keyframes drawing-loader {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

</style>