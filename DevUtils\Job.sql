USE [thermarate];

SELECT [job].[JobId]
      ,[job].[ClientId]
      ,[client].[ClientName] [__Client]
      ,[job].[JobReference]
      ,[job].[StatusCode]
      ,[job].[Notes]
      ,[job].[CreatedOn]
      ,[job].[CreatedByName]
      ,[job].[ModifiedOn]
      ,[job].[ModifiedByName]
      ,[job].[Deleted]
      ,[job].[CurrentAssessmentId]
      ,[job].[AssessorUserId]
  FROM [dbo].[RSS_Job] [job]
  INNER JOIN [dbo].[RSS_Client] [client] ON [job].[ClientId] = [client].[ClientId]
  WHERE 1=1
	--AND [job].[Deleted] = 0
    AND [job].[JobId] = '0d06d0cf-40f0-9e37-3d2f-3a0a94cf6762'
    --AND [client].[ClientId] = '7D76BC43-8273-AD85-FF3D-3A07209DA1F0'
    -- AND [CurrentAssessmentId] = '8e3e7771-6bb7-4e94-a84e-a74ca577f43c'


    UPDATE [dbo].[RSS_Job] SET [Deleted] = 0 WHERE [JobId] = '0d06d0cf-40f0-9e37-3d2f-3a0a94cf6762'