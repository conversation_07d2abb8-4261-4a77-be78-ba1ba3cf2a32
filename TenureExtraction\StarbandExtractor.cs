﻿using System;
using System.Collections.Generic;
using System.IO;

using OfficeOpenXml;

namespace TenureExtraction
{
    public class StarbandExtractor
    {

        // NON-0 Indexed values
        private const int CLIMATE_ZONE_COLUMN = 1;
        private const int STATE_CODE_COLUMN = 2;
        private const int STARBAND_START_ROW = 3;
        private const int STARBAND_END_ROW = 72;
        private const int STARBAND_START_COLUMN = 3;
        private const int STARBAND_END_COLUMN = 98;
        

        /// <summary>
        /// Returns a dictionary where
        ///     Key = Climate Zone
        ///     Value: Starband 
        /// </summary>
        public static Dictionary<int, Starband> Extract(string pathToExcelFile, string sheetName)
        {
            var matrix = new Dictionary<int, Starband>();

            FileInfo existingFile = new FileInfo(pathToExcelFile);
            using (ExcelPackage package = new ExcelPackage(existingFile))
            {
                //get the first worksheet in the workbook
                ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];

                for (int row = STARBAND_START_ROW; row <= STARBAND_END_ROW; row++)
                {

                    int climateZone = Convert.ToInt32(worksheet.Cells[row, CLIMATE_ZONE_COLUMN].Value);
                    int stateCode = Convert.ToInt32(worksheet.Cells[row, STATE_CODE_COLUMN].Value);

                    var rowRatings = new Dictionary<decimal, decimal>();
                    decimal starRating = 0.5M;
                    
                    for (int col = STARBAND_START_COLUMN; col <= STARBAND_END_COLUMN; col++)
                    {
                        rowRatings.Add(starRating, Math.Round(Convert.ToDecimal(worksheet.Cells[row, col].Value), 1));
                        starRating += 0.1M;
                    }

                    Starband band = new Starband(climateZone, stateCode, rowRatings);

                    matrix.Add(climateZone, band);

                }
            }

            return matrix;
        }
    }



    public class Starband
    {
        /// <summary>
        /// The Corresponding NatHERS climate zone.
        /// </summary>
        public readonly int ClimateZone;

        /// <summary>
        /// Not used.
        /// </summary>
        public readonly int StateCode;

        /// <summary>
        /// Key is the Stars Rating.
        /// Value is the Max Energy Rating.
        /// </summary>
        public readonly Dictionary<decimal, decimal> EnergyRatingsByStars;

        public Starband(int climateZone, int stateCode, Dictionary<decimal, decimal> energyRatings)
        {
            this.ClimateZone = climateZone;
            this.StateCode = stateCode;
            this.EnergyRatingsByStars = energyRatings;
        }
    }

}
