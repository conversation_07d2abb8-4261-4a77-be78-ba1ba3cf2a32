<div class="sort-container">
    <!-- Sort -->
    <div class="sort-label">Sort By:</div>
    <md-select class="sort-dropdown"
               name="SortSelection"
               ng-model="vm.currentSort"
               ng-change="vm.triggerApplySort()">
        <md-option ng-value="item"
                    ng-repeat="item in vm.sortByOptions">
            {{item.description}}
        </md-option>
    </md-select>
    <!-- Asc/Desc -->
    <div class="sort-asc-button"  ng-show="vm.sortByDir=='asc'"  ng-click="vm.setDescending()">
        <img src="../../../content/images/sort-ascending.png" />
    </div>
    <div class="sort-desc-button" ng-show="vm.sortByDir=='desc'" ng-click="vm.setAscending()">
        <img src="../../../content/images/sort-descending.png" />
    </div>
</div>

<style>

    .sort-label {}

    .sort-container {
        display: flex;
    }

    .sort-dropdown {
        margin: -12px 0px 0px 20px;
        width: 200px;
        height: 40px;
        border: solid #b8b9b9 1px;
        padding: 7px 5px;
        color: black;
        box-sizing: border-box;
    }

        .sort-dropdown .md-select-value {
            border-bottom: none !important;
        }

        .sort-dropdown > .md-select-value > span {
            margin-top: -5px;
        }

        .sort-dropdown > .md-select-value > .md-select-icon {
            margin-top: -2px;
        }

    .sort-asc-button, .sort-desc-button {
        position: relative;
        margin-left: 10px;
        margin-top: -12px;
        width: 40px;
        height: 40px;
        border: 1px solid grey;
        border-radius: 3px;
        text-align: center;
        cursor: pointer;
        box-sizing: border-box;
        font-size: 19px;
        color: black;
        user-select: none;
    }
    .sort-asc-button > img, .sort-desc-button > img {
        position: absolute;
        top: 50%; left: 50%; transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
    }
    .sort-desc-button > img {
        top: 52%;
    }

</style>