﻿(function () {
    'use strict';

    var app = angular.module('app');
    app.directive('rdShowIsModified', match);
    function match() {
        return {
            restrict: 'A',
            scope: {
                rdShowIsNew: '='
            },
            link: function (scope, elem, attrs) {
                if (scope.rdShowIsNew) {
                    if (moment(scope.rdShowIsNew).isAfter(moment().subtract(1, 'day'), 'd')) {
                        elem.append("<span class='label label-success label-extra-small' title='This record was modified today'>Upd</span>");
                    }
                }
            }
        };
    }
})();