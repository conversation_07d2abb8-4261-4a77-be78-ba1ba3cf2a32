<section id="menu-parent-view" class="mainbar" data-ng-controller="MenuParentCtrl as vm">
    <section class="matter">
        <div class="container-fluid">
            <div class="row-fluid">
                <div class="widget wblue tab-parent">
                    <div data-cc-widget-header title="{{vm.title}}"></div>
                    <ul class="parent-menu-item">
                        <li class="rfwblue fade-selection-animation pull-left"
                            data-ng-repeat="r in vm.navRoutes" redi-allow-roles="{{r.stateConfig.data.roles}}">
                            <div>
                                <a href="#{{r.stateConfig.url}}" class="datas-text" data-ng-bind-html="r.stateConfig.data.content"></a>
                                <div class="clearfix"></div>
                            </div>
                        </li>
                    </ul>
                    <div class="">
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</section>