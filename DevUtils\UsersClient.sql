USE [thermarate];

SELECT [usersClient].[UserClientId]
      ,[usersClient].[UserId]
	  ,[user].[UserName] [__AspNetUser]
      ,[usersClient].[ClientId]
	  ,[client].[ClientName] [__Client]
      ,[usersClient].[CreatedOn]
      ,[usersClient].[CreatedByName]
      ,[usersClient].[ModifiedOn]
      ,[usersClient].[ModifiedByName]
      ,[usersClient].[Deleted]
  FROM [dbo].[RSS_UsersClient] [usersClient]
  INNER JOIN [dbo].[aspnet_Users] [user] ON [usersClient].[UserId] = [user].[UserId]
  INNER JOIN [dbo].[RSS_Client] [client] ON [usersClient].[ClientId] = [client].[ClientId]
  WHERE 1=1
	--AND [usersClient].[ClientId] = 'E124F98F-AEBB-855D-2A05-3A0B11BD626C'