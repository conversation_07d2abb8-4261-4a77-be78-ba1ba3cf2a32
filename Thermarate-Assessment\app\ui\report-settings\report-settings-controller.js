(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "reportSettings";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/report-settings/report-settings.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {

            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                reportSettings: '<',
                disabled: '<'
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: Controller,

            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    Controller.$inject = ['$scope', 'common','uuid4', '$q'];

    function Controller($scope, common, uuid4, $q) {

        let vm = this;

        vm.reportShowSettingChanged = function(reportSetting) {
            if(reportSetting.showOnClientPortal === false)
                reportSetting.allowDownloadOnClientPortal = false;
        }

    }
})();