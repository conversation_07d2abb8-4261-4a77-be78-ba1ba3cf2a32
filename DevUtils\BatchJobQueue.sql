-- USE [thermarate];

SELECT [RecId]
      ,[RequestName]
      ,[RequestData]
    --   ,[RequestedDateTimeUtc]
      ,FORMAT(DATEADD(HOUR, 8, [RequestedDateTimeUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__RequestedDateFormatted]
      ,[Submitted]
    --   ,[DueDateTimeUtc]
      ,FORMAT(DATEADD(HOUR, 8, [DueDateTimeUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__DueDateFormatted]
      ,[RunFrequencyCode]
      ,[RunAtTime]
    --   ,[LastRunDateTimeUtc]
      ,FORMAT(DATEADD(HOUR, 8, [LastRunDateTimeUtc]), 'dd/MM/yyyy hh:mm:ss tt', 'en-US') AS [__LastRunDateFormatted]
  FROM [dbo].[RSS_BatchJobQueue]
  WHERE 1=1
    AND [Submitted] = 0
  ORDER BY [DueDateTimeUtc] DESC
