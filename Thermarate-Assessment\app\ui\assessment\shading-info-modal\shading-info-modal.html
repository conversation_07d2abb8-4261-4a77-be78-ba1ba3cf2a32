<md-dialog ng-controller="ShadingInfoModalCtrl as vm">
  <ng-form name="openingsoverridemodalform" novalidate ng-submit="vm.save()">
    <md-toolbar>
      <div class="md-toolbar-tools">
        <h2 style="text-transform: capitalize">{{vm.title}} ({{vm.item.elementNumber}})</h2>
        <span flex></span>
        <md-button class="md-icon-button" ng-click="vm.cancel()">
          <i class="material-icons">clear</i>
        </md-button>
      </div>
    </md-toolbar>

    <md-dialog-content id="shading-modal" layout="column" layout-margin layout-padding>

      <!-- Horizontal Shading -->
      <table class="table elements-table" id="horizontal-shading-table"
             ng-if="vm.propertyName == 'horizontalShading'">
        <thead>
          <tr>
            <th></th>
            <th>
              Horizontal Projection 1
            </th>
            <th>
              Horizontal Projection 2
            </th>
          </tr>
        </thead>
        <tbody>
          <!-- Eave -->
          <tr>
            <td>Eave Projection (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.projection"
                ng-change="vm.calculateAllEaveFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Eave Vertical Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.verticalOffset"
                ng-change="vm.calculateAllEaveFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Eave RH Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.rhOffset"
                ng-change="vm.calculateAllEaveFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Eave LH Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.lhOffset"
                ng-change="vm.calculateAllEaveFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Eave Length (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.length"
                ng-change="vm.calculateAllEaveFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Eave Shade Angle ({{vm.symbol("degrees")}})</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                disabled
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.eave.shadeAngle"/>
            </td>
          </tr>
          <tr class="seperator">
            <td>Eave Horizontal Shading</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="item.eave.shading"/>
            </td>
          </tr>
          <!-- Pergola -->
          <tr>
            <td>Pergola Projection (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.pergola.projection"
                ng-change="vm.calculateAllPergolaFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Pergola Vertical Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.pergola.verticalOffset"
                ng-change="vm.calculateAllPergolaFields()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Pergola RH Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.pergola.rhOffset"
                ng-change="vm.calculateAllPergolaFields()"
                ng-required
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Pergola LH Offset (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.pergola.lhOffset"
                ng-change="vm.calculateAllPergolaFields()"
                ng-required
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Pergola Length (m)</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.pergola.length"
                ng-change="vm.calculateAllPergolaFields()"
                ng-required
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Pergola Shade Angle ({{vm.symbol("degrees")}})</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input formatted-number
                     disabled
                     class="lightweight"
                     decimals="2"
                     name="units"
                     ng-model="item.pergola.shadeAngle"
                     ng-required />
            </td>
          </tr>
          <tr class="seperator">
            <td>Pergola Horizontal Shading</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="item.pergola.shading"
                ng-required />
            </td>
          </tr>
          <!-- Horizontal Shading -->
          <tr>
            <td>Horizontal Shading</td>
            <td ng-repeat="item in vm.horizontalShading track by $index">
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="item.shading"/>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Vertical Shading -->
      <table class="table elements-table" id="vertical-shading-table"
             ng-if="vm.propertyName == 'verticalShading'">
        <thead>
          <tr>
            <th></th>
            <th>Vertical Screen 1</th>
            <th>Vertical Screen 2</th>
            <th>Vertical Screen 3</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Screen Height (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.screenHeight"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Screen Width (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.screenWidth"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Perpendicular Distance (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.perpendicularDistance"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>RH Offset (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.rhOffset"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
              </td>
          </tr>
          <tr>
            <td>LH Offset (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.lhOffset"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Vertical Offset (m)</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.verticalOffset"
                ng-change="vm.calculateAllVerticalShading()"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Vertical Shade Angle ({{vm.symbol("degrees")}})</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input formatted-number
                disabled
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="item.verticalShadeAngle"
                ng-required />
            </td>
          </tr>
          <tr>
            <td>Vertical Shading</td>
            <td ng-repeat="item in vm.verticalShading track by $index">
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="item.shading"
                ng-required />
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Left Wing Wall -->
      <table class="table elements-table" id="left-wing-wall-table"
             ng-if="vm.propertyName == 'leftWingWall'">
        <!-- No headers -->
        <tbody>
          <tr>
            <td>Projection (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.leftWingWall.projection"
                ng-change="vm.calculateWingWallShadingFields(vm.item.leftWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Horizontal Offset (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.leftWingWall.horizontalOffset"
                ng-change="vm.calculateWingWallShadingFields(vm.item.leftWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
              </td>
          </tr>
          <tr>
            <td>Vertical Offset (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.leftWingWall.verticalOffset"
                ng-change="vm.calculateWingWallShadingFields(vm.item.leftWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Shade Angle ({{vm.symbol("degrees")}})</td>
            <td>
              <input formatted-number
                disabled
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.leftWingWall.shadeAngle"/>
            </td>
          </tr>
          <tr>
            <td>Left Wing Shading</td>
            <td>
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="vm.item.leftWingWall.shading"
                ng-required />
              </td>
          </tr>
        </tbody>
      </table>

      <!-- Right Wing Wall -->
      <table class="table elements-table" id="right-wing-wall-table"
             ng-if="vm.propertyName == 'rightWingWall'">
        <!-- No headers -->
        <tbody>
          <tr>
            <td>Projection (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.rightWingWall.projection"
                ng-change="vm.calculateWingWallShadingFields(vm.item.rightWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
            </td>
          </tr>
          <tr>
            <td>Horizontal Offset (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.rightWingWall.horizontalOffset"
                ng-change="vm.calculateWingWallShadingFields(vm.item.rightWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled"/>
              </td>
          </tr>
          <tr>
            <td>Vertical Offset (m)</td>
            <td>
              <input formatted-number
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.rightWingWall.verticalOffset"
                ng-change="vm.calculateWingWallShadingFields(vm.item.rightWingWall, vm.item.width)"
                ng-disabled="vm.item.source === 'external' || vm.disabled" />
            </td>
          </tr>
          <tr>
            <td>Shade Angle ({{vm.symbol("degrees")}})</td>
            <td>
              <input formatted-number
                disabled
                class="lightweight"
                decimals="2"
                name="units"
                ng-model="vm.item.rightWingWall.shadeAngle"
                ng-required />
            </td>
          </tr>
          <tr>
            <td>Right Wing Shading</td>
            <td>
              <input
                disabled
                class="lightweight"
                name="units"
                ng-model="vm.item.rightWingWall.shading"
                ng-required />
              </td>
          </tr>
        </tbody>
      </table>

    </md-dialog-content>

    <md-dialog-actions layout="row">
      <md-button class="md-raised md-primary"
                 ng-click="vm.save()"
                 ng-disabled="vm.disabled">
        Save
      </md-button>
      <md-button class="md-raised" ng-click="vm.cancel()"> Cancel </md-button>
    </md-dialog-actions>

  </ng-form>

</md-dialog>

<style>
  md-dialog-content * {
    font-size: 14px;
  }
  md-input-container label:not(.md-container-ignore) {
    left: auto;
  }
  /* Tables */
  #horizontal-shading-table > tbody > tr> td:first-child,
  #vertical-shading-table > tbody > tr> td:first-child,
  #left-wing-wall-table > tbody > tr> td:first-child,
  #right-wing-wall-table > tbody > tr> td:first-child {
    text-align: left;
    font-size: 12px;
    color: #757575;
  }
  #horizontal-shading-table {
    width: 550px;
  }
  #vertical-shading-table {
    width: 700px;
  }
  #left-wing-wall-table, #right-wing-wall-table {
    width: 400px;
  }
  .seperator > td {
    border-bottom: 1px solid lightgrey !important;
  }
</style>
