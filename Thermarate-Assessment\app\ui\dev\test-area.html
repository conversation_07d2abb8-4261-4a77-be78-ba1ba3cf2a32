<form name="assessmentform" class="main-content-wrapper" 
      novalidate data-ng-controller='DevTestCtrl as vm' form-error-check>

    <div class="widget" ng-cloak layout="column">

        <h1>DEVELOPMENT PURPOSES ONLY</h1>
        <h2>DO NOT USE IN PRODUCTION</h2>
<!--        -->
<!--            <building-drawings ng-if="vm.assessment.allComplianceOptions[0] != null"-->
<!--                               assessment="vm.assessment"-->
<!--                               option="vm.assessment.allComplianceOptions[0]"-->
<!--                               disabled="false"-->
<!--                               job-files="vm.jobFiles">-->
<!--            </building-drawings>-->

        <!-- Results -->
<!--        <results-overview ng-if="vm.assessment.allComplianceOptions[0] != null"-->
<!--                          assessment="vm.assessment"-->
<!--                          option="vm.assessment.allComplianceOptions[0]"-->
<!--                          building="vm.assessment.allComplianceOptions[0].proposed">-->
<!--        </results-overview>-->

<!--        <floor-plan-data ng-if="vm.assessment.allComplianceOptions[0] != null"-->
<!--                         source="vm.assessment.allComplianceOptions[0].proposed">-->

<!--        </floor-plan-data>-->

<!--        <zone-summary ng-if="vm.assessment.allComplianceOptions[0].proposed != null"-->
<!--                      source="vm.assessment.allComplianceOptions[0].proposed"-->
<!--                      storeys="vm.assessment.allComplianceOptions[0].proposed.storeys"-->
<!--                      ncc-climate-zone="vm.assessment.nccClimateZone.description"-->
<!--                      nathers-climate-zone="vm.assessment.natHERSClimateZone.description">-->
<!--        </zone-summary>-->

<!--        <climate-overview ng-if="vm.assessment != null"-->
<!--                          assessment="vm.assessment">-->

<!--        </climate-overview>-->

        <fieldset ng-if="vm.assessment.assessmentProjectDetail != null"
                  ng-form="assessmentOutcomesRootForm">

            <assessment-outcomes is-locked="false"
                                 allow-option-select="true"
                                 assessment="vm.assessment"
                                 client-id="vm.assessment.job.clientId"
                                 compliance-status-list="vm.complianceStatusList"
                                 compliance-method-list="vm.complianceMethodList"
                                 compliance-method-code="vm.assessment.allComplianceOptions[0].complianceMethodCode"
                                 assessment-software-list="vm.allAssessmentSoftwareList"
                                 set-final-compliance-method="vm.setFinalComplianceMethod()"
                                 job-files="vm.jobFiles"></assessment-outcomes>

        </fieldset>

    </div>

    <div class="fixed-action-bar">
        <div data-cc-widget-button-bar>

            <md-button class="md-raised md-primary"
                       type="button"
                       ng-disabled="vm.isBusy"
                       ng-click="vm.save()">
                Save
            </md-button>
        </div>
    </div>
</form>
