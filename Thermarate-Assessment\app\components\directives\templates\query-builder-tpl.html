﻿<style>
    .query-table tr td {
        padding: 4px;
    }
    md-select.operator {
        background-color: #c1c1c1;
    }
    div.value-col {
        display:inline-block;
        max-width:200px;
    }
    div.value-col md-input-container {
        padding-top: 2px;
    }
    div.logic-select {
        padding-top:13px;
    }
    .logic-select md-select .md-select-value {
        background-color:#c1c1c1;
    }
    p {
        margin:0;
    }
    .md-datepicker-button {
        float: left;
        margin-top: -2.5px;
    }
    .logic-select md-select {
        margin:0px;
    }
    .query-table md-input-container  {
        height:30px;
        margin:0px;
        margin-top:15px;
    }
    .md-button.close {
        min-width: 28px;
    }
</style>
<form name="queryBuilderForm" class="form-horizontal" novalidate >
    <section id="create-sale-view" class="mainbar matter">
        <div class="container-fluid">
            <div class="row-fluid">
                <div class="widget wblue">
                    <div data-cc-widget-header
                         data-title="{{vm.title}}"
                         data-is-modal="vm.isModal"
                         data-cancel="vm.cancel()">
                    </div>
                    <div data-cc-widget-content
                         data-is-modal="true">
                        <table class="query-table">
                            <tr ng-repeat="line in vm.currentQuery.lines track by $index">
                                <td valign="top">
                                    <md-input-container>
                                        <md-select name="field{{$index}}"
                                                   style="min-width:200px;"
                                                   ng-change="vm.fieldChanged(line)"
                                                   ng-model="line.field"
                                                   ng-model-options="{trackBy: '$value.name'}"
                                                   placeholder="select field...">
                                            <md-option ng-value="srow"
                                                       ng-repeat="srow in vm.queryModel.fields  | filter : {description: $select.search }">
                                                {{srow.description}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>
                                    <div ng-if="!$last" class="logic-select">
                                        <md-select name="logic{{$index}}"
                                                   style="min-width:100px; max-width: 100px;"
                                                   ng-model="line.logic"
                                                   ng-model-options="{trackBy: '$value.name'}"
                                                   placeholder="select or/and">
                                            <md-option ng-value="srow"
                                                       ng-repeat="srow in vm.logics | filter : {description: $select.search }">
                                                {{srow.description}}
                                            </md-option>
                                        </md-select>
                                    </div>
                                </td>
                                <td valign="top" >
                                    <md-input-container>
                                        <md-select name="operator{{$index}}"
                                                   style="min-width:200px;"
                                                   class="operator"
                                                   ng-model="line.operator"
                                                   placeholder="select operator..."
                                                   ng-model-options="{trackBy: '$value.name'}"
                                                   required>
                                            <md-option ng-value="srow"
                                                       ng-repeat="srow in vm.operators  | filter : { dataTypes : line.field.dataType }  | filter : {description: $select.search }">
                                                {{srow.description}}
                                            </md-option>
                                        </md-select>
                                    </md-input-container>
                                </td>
                                <td valign="top">
                                    <div  class="value-col" ng-if="line.operator.valueRequired==true">
                                        <md-input-container ng-if="line.field.dataType=='string'">
                                            <input
                                                   type="text" name="value{{$index}}"
                                                   ng-model="line.value"
                                                   class="form-control"
                                                   placeholder="enter value"
                                                   required />
                                        </md-input-container>
                                        <md-input-container  ng-if="line.field.dataType=='decimal'">
                                            <input
                                                   type="text" name="value{{$index}}"
                                                   ng-model="line.value"
                                                   class="form-control"
                                                   placeholder="enter value"
                                                   number-only
                                                   required />
                                        </md-input-container>
                                        <md-input-container ng-if="line.field.dataType=='integer'">
                                            <input
                                                   type="text" name="value{{$index}}"
                                                   ng-model="line.value"
                                                   class="form-control"
                                                   placeholder="enter value"
                                                   only-numeric
                                                   required />
                                        </md-input-container>
                                        <md-input-container  ng-if="line.field.dataType=='boolean'">
                                            <select
                                                    ng-model="line.value">
                                                <option value=true>True</option>
                                                <option value=false>False</option>
                                            </select>
                                        </md-input-container>
                                        <md-input-container  ng-if="line.field.dataType=='date' ">
                                            <p class="input-group">
                                                <md-datepicker ng-model="line.value"
                                                               name="value{{$index}}"
                                                               required
                                                               md-placeholder="Enter date">
                                                </md-datepicker>
                                            </p>
                                        </md-input-container>
                                        <md-input-container class="second-date" ng-if="line.operator.name=='between' && line.field.dataType=='date'">
                                            <p class="input-group">
                                                <md-datepicker ng-model="line.value2"
                                                               name="value2{{$index}}"
                                                               md-min-date="line.value"
                                                               required
                                                               md-placeholder="to date">
                                                </md-datepicker>
                                            </p>
                                        </md-input-container>
                                        <md-input-container ng-if="line.operator.name=='between' && (line.field.dataType=='integer'|| line.field.dataType=='decimal')">
                                            <input 
                                                   type="text" name="value2{{$index}}"
                                                   ng-model="line.value2"
                                                   class="form-control"
                                                   placeholder="enter to value"
                                                   number-only
                                                   required />
                                        </md-input-container>
                                    </div>
                                </td>
                                <td valign="top">
                                    
                                </td>
                                <td valign="top">
                                    <md-button
                                            tabindex="-1" 
                                            class="close" 
                                            title="remove line"
                                            ng-click="vm.removeLine($index)">
                                        ×
                                    </md-button>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" style="padding-top: 13px;">
                                    <md-button class="md-raised" ng-click="vm.addQueryLine()">Add Another Line</md-button>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div data-cc-widget-button-bar
                         data-is-modal="vm.isModal">
                        <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                        <md-button class="md-raised md-primary" ng-disabled="queryBuilderForm.$invalid" ng-click="vm.close()">Run and Close</md-button>
                        <md-button class="md-raised" ng-disabled="queryBuilderForm.$invalid" ng-click="vm.run()">Run Query</md-button>
                        <md-button class="md-raised" ng-click="vm.clear()">Clear</md-button>
                        <md-button class="md-raised" ng-click="vm.cancel()">Close</md-button>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>
