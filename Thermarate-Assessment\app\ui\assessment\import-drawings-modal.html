<md-dialog ng-controller="ImportDrawingsModalCtrl as vm" class="smallModal">
    <md-toolbar>
        <div class="md-toolbar-tools">
            <h2>Import Previous Drawings</h2>
            <span flex></span>
            <md-button class="md-icon-button" ng-click="vm.cancel()">
                <i class="material-icons">clear</i>
            </md-button>
        </div>
    </md-toolbar>

    <md-dialog-content layout layout-wrap layout-margin class="import-top">
        <div layout="row" flex="100">
            <span>Find and select the drawings you wish to import from previous Jobs that involve the Client ({{vm.clientName}}).</span>
        </div>
    </md-dialog-content>

    <md-dialog-content layout layout-wrap layout-margin layout-padding class="import-dialog">
        <div flex="100" class="shadow-z-1 import-bord">
            <md-toolbar layout class="import-toolbar">
                <form ng-submit="vm.filterBySearch()" flex="66" layout="row">
                    <input flex="80" type="text" name="search"
                           ng-model="vm.searchText"
                           placeholder="Search" class="quick-search" />
                    <md-button flex="20" class="extra-small-button" style="padding: 0; margin: 0; min-height: 30px; min-width: 30px;" ng-click="vm.filterBySearch()" ng-if="!vm.isFiltered"><i class="fa fa-search"></i></md-button>
                    <md-button flex="20" class="extra-small-button" style="padding: 0; margin: 0; color: red; min-height: 30px; min-width: 30px;" ng-click="vm.resetFilter()" ng-if="vm.isFiltered"><i class="fa fa-times"></i></md-button>
                </form>
            </md-toolbar>
            <div class="import-wrapper">
                <!-- Current Job -->
                <div ng-click="vm.open(vm.currentJob); $event.stopPropagation();" width="100%" class="import-indent-1">
                    <div class="import-hov">
                        <span class="import-span"><i class="import-plus-minus" ng-class="{'fa fa-plus': !vm.currentJob.isOpen, 'fa fa-minus': vm.currentJob.isOpen}"></i>&nbsp;{{vm.currentJob.jobReference}} (Current Job)</span>
                    </div>
                    <div ng-click="vm.open(assessment); $event.stopPropagation();" class="import-indent-2" ng-repeat="assessment in vm.currentJob.assessments" ng-show="(!vm.isFiltered&&vm.currentJob.isOpen)||(vm.isFiltered&&vm.currentJob.filterFound&&vm.currentJob.isOpen)">
                        <div class="import-hov">
                            <span class="import-span"><i class="import-plus-minus" ng-class="{'fa fa-plus': !assessment.isOpen, 'fa fa-minus': assessment.isOpen}"></i>&nbsp;Assessment {{assessment.versionNumber}}</span>
                        </div>
                        <div class="import-indent-3" ng-repeat="drawing in assessment.assessmentDrawings" ng-show="(!vm.isFiltered&&assessment.isOpen)||(vm.isFiltered&&assessment.filterFound&&assessment.isOpen)">
                            <div ng-show="(!vm.isFiltered||(vm.isFiltered&&drawing.filterFound))&&(drawing.archived==false||(drawing.archived && vm.allowArchiveDrawings))" ng-click="vm.select(drawing); $event.stopPropagation();" class="import-hov">
                                <span class="import-span"><md-checkbox ng-model="drawing.isSelected"></md-checkbox>&nbsp;<a href="{{drawing.attachment.url }}" target="_blank">{{drawing.attachmentDisplayName}}</a></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End of Current Job -->
                <!-- Other Jobs -->
                <div ng-click="vm.open(job); $event.stopPropagation();" class="import-indent-1" ng-repeat="job in vm.jobList" width="100%">
                    <div class="import-hov">
                        <span class="import-span"><i class="import-plus-minus" ng-class="{'fa fa-plus': !job.isOpen, 'fa fa-minus': job.isOpen}"></i>&nbsp;{{job.jobReference}}</span>
                    </div>
                    <div ng-click="vm.open(assessment); $event.stopPropagation();" class="import-indent-2" ng-repeat="assessment in job.assessments" ng-show="(!vm.isFiltered&&job.isOpen)||(vm.isFiltered&&job.filterFound&&job.isOpen)">
                        <div class="import-hov">
                            <span class="import-span"><i class="import-plus-minus" ng-class="{'fa fa-plus': !assessment.isOpen, 'fa fa-minus': assessment.isOpen}"></i>&nbsp;Assessment {{assessment.versionNumber}}</span>
                        </div>
                        <div class="import-indent-3" ng-repeat="drawing in assessment.assessmentDrawings" ng-show="(!vm.isFiltered&&assessment.isOpen)||(vm.isFiltered&&assessment.filterFound&&assessment.isOpen)">
                            <div class="import-hov" ng-show="(!vm.isFiltered||(vm.isFiltered&&drawing.filterFound))&&(drawing.archived==false||(drawing.archived && vm.allowArchiveDrawings))" ng-click="vm.select(drawing); $event.stopPropagation();">
                                <span class="import-span"><md-checkbox ng-model="drawing.isSelected"></md-checkbox>&nbsp;<a href="{{drawing.attachment.url }}" target="_blank">{{drawing.attachmentDisplayName}}</a>&nbsp;&nbsp;&nbsp;<span style="color: red;" ng-show="drawing.archived">[Archived]</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </md-dialog-content>

    <md-dialog-content layout layout-wrap layout-margin class="import-bottom">
        <!-- End of Other Jobs -->
        <div layout="row" flex="100">
            <md-checkbox ng-model="vm.allowArchiveDrawings" ng-click="vm.toggleArchive()">&nbsp;Allow Archive Drawings</md-checkbox>
            <span flex></span>
            <span>{{vm.selectedCount}} Selected</span>
        </div>
    </md-dialog-content>

    <md-dialog-actions layout="row">
        <md-button class="md-raised" ng-click="vm.cancel()">
            Cancel
        </md-button>
        <md-button class="md-raised md-primary" ng-click="vm.submitSelection()">
            Ok
        </md-button>
    </md-dialog-actions>
</md-dialog>