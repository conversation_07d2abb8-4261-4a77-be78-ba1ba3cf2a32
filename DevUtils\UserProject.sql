USE [thermarate];

SELECT [userProject].[UserProjectId]
	  ,[userProject].[UserId]
      ,[user].[FullName] [__User]
      ,[userProject].[ProjectId]
	  ,[project].[ProjectName] [__Project]
      ,[userProject].[CreatedOn]
      ,[userProject].[CreatedByName]
      ,[userProject].[ModifiedOn]
      ,[userProject].[ModifiedByName]
      ,[userProject].[Deleted]
  FROM [dbo].[RSS_UserProject] [userProject]
  INNER JOIN [dbo].[RSS_User] [user] ON [userProject].[UserId] = [user].[UserId]
  INNER JOIN [dbo].[RSS_Project] [project] ON [userProject].[ProjectId] = [project].[ProjectId]
  WHERE 1=1
	--AND [userProject].[Deleted] = 0
  ORDER BY [CreatedOn] DESC