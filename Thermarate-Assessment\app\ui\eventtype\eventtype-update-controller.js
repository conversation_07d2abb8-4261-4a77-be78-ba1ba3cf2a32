(function () {
    // The EventtypeUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'EventtypeUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'eventtypeservice', eventtypeUpdateController]);
function eventtypeUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  eventtypeservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Event Type';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.eventTypeCode = null;
        vm.eventtype = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Event Type";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.eventTypeCode = $scope.eventTypeCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.eventTypeCode = $stateParams.eventTypeCode;
        }

        // Get data for object to display on page
        var eventTypeCodePromise = null;
        if (vm.eventTypeCode != null) {
            eventTypeCodePromise = eventtypeservice.getEventType(vm.eventTypeCode)
            .then(function (data) {
                if (data != null) {
                    vm.eventtype = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("eventtype-list");
                }
            }
        }

        vm.save = function () {
            vm.isBusy = true;
            if(vm.newRecord == true){
                eventtypeservice.createEventType(vm.eventtype).then(function(data){
                    vm.eventtype = data;
                    vm.eventTypeCode = vm.eventtype.eventTypeCode;
                    vm.isBusy = false;
                    vm.cancel();
                });
            }else{
                eventtypeservice.updateEventType(vm.eventtype).then(function(data){
                    if (data != null) {
                        vm.eventtype = data;
                        vm.eventTypeCode = vm.eventtype.eventTypeCode;
                    }
                    vm.isBusy = false;
                });
            }
        }

        vm.delete = function () {
            vm.isBusy = true;
            eventtypeservice.deleteEventType(vm.eventTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            vm.isBusy = true;
            eventtypeservice.undoDeleteEventType(vm.eventTypeCode).then(function () {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();